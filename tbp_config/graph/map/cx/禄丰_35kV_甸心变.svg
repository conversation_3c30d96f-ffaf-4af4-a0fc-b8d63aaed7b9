<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-98" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3136 -1323 2485 1276">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <rect height="29" stroke-width="2" width="15" x="1" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="8" x2="11" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="9" x2="6" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.8" x1="8" x2="8" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="11" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="1" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="51" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape135">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.386149" x1="10" x2="10" y1="23" y2="57"/>
    <rect height="18" stroke-width="0.304622" width="10" x="5" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="77" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.386149" x1="76" x2="76" y1="23" y2="57"/>
    <rect height="18" stroke-width="0.304622" width="10" x="71" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.386149" x1="44" x2="44" y1="23" y2="57"/>
    <rect height="18" stroke-width="0.304622" width="10" x="39" y="33"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer:shape1_0">
    <circle cx="26" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="24" y2="31"/>
   </symbol>
   <symbol id="transformer:shape1_1">
    <circle cx="26" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape1-2">
    <circle cx="56" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="57" x2="57" y1="54" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="72" x2="57" y1="45" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="72" x2="57" y1="45" y2="54"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape2">
    <circle cx="7" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23e5520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21d6490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_221e2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc0690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2170a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2171470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_251a180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_251aa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_21557a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_21557a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fd2970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fd2970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d8b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d8b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1f9e770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22c04e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_21d2070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_21d2c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_222b930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20f6680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20f5650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24b9c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24ba570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_212f2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_212fb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fa2b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fa3240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fa3de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24bb5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24bc770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20f7350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_212a150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_212adf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ffbcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ffd0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1286" width="2495" x="3131" y="-1328"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2490400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 1313.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a04a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 1298.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a1230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 1283.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a52b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4702.000000 1319.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a5480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4691.000000 1304.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a5690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4716.000000 1289.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a6140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5009.000000 1323.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a63a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4998.000000 1308.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a65e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5023.000000 1293.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243b630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 802.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243b890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 787.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243bad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 772.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243be00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4046.000000 827.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2235d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4046.000000 798.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22362e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 782.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243c2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4046.000000 813.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243ea20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4967.000000 807.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243ec80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4956.000000 792.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243eec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 777.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244f740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.000000 1314.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244f9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 1299.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244fbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 1284.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="446" x="4482" y="-640"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5314.000000 -889.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.715424 -463.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4891.293379 -461.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.000000 -821.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.955059 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.955059 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.515539 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -264.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.000000 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.000000 -804.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.000000 -901.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44998" ObjectName="SW-LF_DX.LF_DX_3871SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="44998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.000000 -1013.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44999" ObjectName="SW-LF_DX.LF_DX_3872SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="44999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.000000 -1098.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45000" ObjectName="SW-LF_DX.LF_DX_3873SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="45000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -1112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -903.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45011" ObjectName="SW-LF_DX.LF_DX_3881SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="45011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -1015.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45012" ObjectName="SW-LF_DX.LF_DX_3882SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="45012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -1100.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45013" ObjectName="SW-LF_DX.LF_DX_3883SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="45013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 -1114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -903.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45001" ObjectName="SW-LF_DX.LF_DX_3891SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="45001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -1015.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45002" ObjectName="SW-LF_DX.LF_DX_3892SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="45002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -1100.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45003" ObjectName="SW-LF_DX.LF_DX_3893SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="45003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4794.000000 -1114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.000000 -902.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45004" ObjectName="SW-LF_DX.LF_DX_3861SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="45004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.000000 -1014.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45005" ObjectName="SW-LF_DX.LF_DX_3862SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="45005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -1113.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.000000 -1011.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.293379 -560.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.715424 -564.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4146.236434 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4146.000000 -261.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -188.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4545.000000 -179.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.630021 -387.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.630021 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.630021 -228.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5015.630021 -219.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -253.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -249.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -1070.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -1070.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3617.000000 -1017.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3640.000000 -965.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3618.000000 -921.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3641.000000 -869.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3520.000000 -825.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3590.000000 -784.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3618.000000 -713.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3641.000000 -661.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3621.000000 -601.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3644.000000 -549.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3621.000000 -489.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3644.000000 -437.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3623.000000 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -325.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3621.000000 -264.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3644.000000 -212.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5323.984796 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5323.984796 -616.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5244.908446 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.239765 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -195.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 -186.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.239765 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5515.256699 -356.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5478.908446 -376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21519f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4136.000000 -1106.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1981c00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -1108.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23674e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -933.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23a1da0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 -1108.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2607250">
    <use class="BV-0KV" transform="matrix(-1.500000 -0.000000 0.000000 1.360000 5325.984796 -663.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_XZ" endPointId="0" endStationName="LF_DX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_XinDian" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4200,-1195 4200,-1257 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="45007" ObjectName="AC-35kV.LN_XinDian"/>
    <cge:TPSR_Ref TObjectID="45007_SS-98"/></metadata>
   <polyline fill="none" opacity="0" points="4200,-1195 4200,-1257 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XQ" endPointId="0" endStationName="LF_DX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_Dianxin" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4458,-1197 4458,-1259 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="45010" ObjectName="AC-35kV.LN_Dianxin"/>
    <cge:TPSR_Ref TObjectID="45010_SS-98"/></metadata>
   <polyline fill="none" opacity="0" points="4458,-1197 4458,-1259 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_HJ" endPointId="0" endStationName="LF_DX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_Dianheia" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4744,-1197 4744,-1259 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="45009" ObjectName="AC-35kV.LN_Dianheia"/>
    <cge:TPSR_Ref TObjectID="45009_SS-98"/></metadata>
   <polyline fill="none" opacity="0" points="4744,-1197 4744,-1259 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_GF" endPointId="0" endStationName="LF_DX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_Gaofeng" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5063,-1196 5063,-1258 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="45008" ObjectName="AC-35kV.LN_Gaofeng"/>
    <cge:TPSR_Ref TObjectID="45008_SS-98"/></metadata>
   <polyline fill="none" opacity="0" points="5063,-1196 5063,-1258 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_DX.LF_DX_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="17873"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.065147 -664.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.065147 -664.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="42319" ObjectName="TF-LF_DX.LF_DX_2T"/>
    <cge:TPSR_Ref TObjectID="42319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 -0.868778 0.689303 -0.000000 3647.578125 -804.705882)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 -0.868778 0.689303 -0.000000 3647.578125 -804.705882)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 0.898990 0.869565 0.000000 3635.000000 -152.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 0.898990 0.869565 0.000000 3635.000000 -152.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_DX.LF_DX_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="17889"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -651.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -651.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="42325" ObjectName="TF-LF_DX.LF_DX_1T"/>
    <cge:TPSR_Ref TObjectID="42325"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2377f10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5272.000000 -980.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21da9e0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5337.000000 -1039.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d18d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4628.000000 -102.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f6210">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4403.000000 -172.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ad200">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -916.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fa0990">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5009.000000 -1088.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b4890">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4179.000000 -169.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c9c9b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5098.630021 -142.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27dd0b0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3715.000000 -1029.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2185c50">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3715.500000 -998.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218cb50">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3716.000000 -933.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2191420">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3716.500000 -902.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23cf360">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3587.000000 -827.851792)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24d3460">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3715.166667 -725.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24d7e70">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3715.666667 -694.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25cdac0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3718.166667 -613.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d21d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3718.666667 -582.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d9d50">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3718.166667 -501.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25de620">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3718.666667 -470.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e63e0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3720.166667 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25eacb0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3720.666667 -358.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f25e0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3718.166667 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f6eb0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3718.666667 -245.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2611a50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4857.000000 -109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261dea0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5470.404908 -357.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261f0d0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5495.404908 -266.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24406f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4405.000000 -904.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_244a5b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5210.000000 -227.000000)" xlink:href="#lightningRod:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-263419" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -987.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263419" ObjectName="LF_DX:LF_DX_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -943.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-256797" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4466.000000 -1311.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42309"/>
     <cge:Term_Ref ObjectID="17860"/>
    <cge:TPSR_Ref TObjectID="42309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-256798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4466.000000 -1311.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42309"/>
     <cge:Term_Ref ObjectID="17860"/>
    <cge:TPSR_Ref TObjectID="42309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-256795" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4466.000000 -1311.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42309"/>
     <cge:Term_Ref ObjectID="17860"/>
    <cge:TPSR_Ref TObjectID="42309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-256888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4762.000000 -1316.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42314"/>
     <cge:Term_Ref ObjectID="17865"/>
    <cge:TPSR_Ref TObjectID="42314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-256885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4762.000000 -1316.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42314"/>
     <cge:Term_Ref ObjectID="17865"/>
    <cge:TPSR_Ref TObjectID="42314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-256873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4762.000000 -1316.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42314"/>
     <cge:Term_Ref ObjectID="17865"/>
    <cge:TPSR_Ref TObjectID="42314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-256915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 -1323.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42316"/>
     <cge:Term_Ref ObjectID="17867"/>
    <cge:TPSR_Ref TObjectID="42316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-256916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 -1323.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42316"/>
     <cge:Term_Ref ObjectID="17867"/>
    <cge:TPSR_Ref TObjectID="42316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-256913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 -1323.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42316"/>
     <cge:Term_Ref ObjectID="17867"/>
    <cge:TPSR_Ref TObjectID="42316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-257131" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4693.000000 -801.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42318"/>
     <cge:Term_Ref ObjectID="17869"/>
    <cge:TPSR_Ref TObjectID="42318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-257132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4693.000000 -801.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42318"/>
     <cge:Term_Ref ObjectID="17869"/>
    <cge:TPSR_Ref TObjectID="42318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-257128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4693.000000 -801.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42318"/>
     <cge:Term_Ref ObjectID="17869"/>
    <cge:TPSR_Ref TObjectID="42318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-257135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -825.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42312"/>
     <cge:Term_Ref ObjectID="17864"/>
    <cge:TPSR_Ref TObjectID="42312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-257136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -825.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42312"/>
     <cge:Term_Ref ObjectID="17864"/>
    <cge:TPSR_Ref TObjectID="42312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-257137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -825.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42312"/>
     <cge:Term_Ref ObjectID="17864"/>
    <cge:TPSR_Ref TObjectID="42312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-257140" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -825.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42312"/>
     <cge:Term_Ref ObjectID="17864"/>
    <cge:TPSR_Ref TObjectID="42312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-257200" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5026.000000 -805.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257200" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42326"/>
     <cge:Term_Ref ObjectID="17891"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-257201" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5026.000000 -805.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42326"/>
     <cge:Term_Ref ObjectID="17891"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-257197" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5026.000000 -805.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257197" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42326"/>
     <cge:Term_Ref ObjectID="17891"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-260527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -1312.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="260527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42701"/>
     <cge:Term_Ref ObjectID="18501"/>
    <cge:TPSR_Ref TObjectID="42701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-260530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -1312.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="260530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42701"/>
     <cge:Term_Ref ObjectID="18501"/>
    <cge:TPSR_Ref TObjectID="42701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-260525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -1312.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="260525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42701"/>
     <cge:Term_Ref ObjectID="18501"/>
    <cge:TPSR_Ref TObjectID="42701"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3427" y="-1173"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3427" y="-1173"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3427" y="-1208"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3427" y="-1208"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="3574" y="-1183"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="3574" y="-1183"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="70" x="4946" y="-726"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="70" x="4946" y="-726"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="69" x="4547" y="-741"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="69" x="4547" y="-741"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4480" y="-988"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4480" y="-988"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4763" y="-989"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4763" y="-989"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="5087" y="-986"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="5087" y="-986"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4217" y="-986"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4217" y="-986"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3427" y="-1173"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3427" y="-1208"/></g>
   <g href="35kV甸心变GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="3574" y="-1183"/></g>
   <g href="35kV甸心变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="70" x="4946" y="-726"/></g>
   <g href="35kV甸心变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="69" x="4547" y="-741"/></g>
   <g href="35kV甸心变LF_DX_388间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4480" y="-988"/></g>
   <g href="35kV甸心变LF_DX_389间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4763" y="-989"/></g>
   <g href="35kV甸心变35kV高峰线386断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="5087" y="-986"/></g>
   <g href="35kV甸心变LF_DX_387间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4217" y="-986"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4490" x2="4522" y1="-633" y2="-600"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4523" x2="4491" y1="-633" y2="-601"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4142" x2="4163" y1="-340" y2="-316"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4164" x2="4143" y1="-340" y2="-317"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4370" x2="4391" y1="-338" y2="-314"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4392" x2="4371" y1="-338" y2="-315"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4595" x2="4616" y1="-283" y2="-259"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4616" x2="4595" y1="-284" y2="-261"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4825" x2="4846" y1="-281" y2="-257"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4844" x2="4823" y1="-281" y2="-258"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4882" x2="4914" y1="-636" y2="-603"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4915" x2="4883" y1="-636" y2="-604"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.717647 -0.000000 0.000000 0.700000 3516.000000 -147.000000)" xlink:href="#transformer:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.717647 -0.000000 0.000000 0.700000 3516.000000 -147.000000)" xlink:href="#transformer:shape1_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.717647 -0.000000 0.000000 0.700000 3516.000000 -147.000000)" xlink:href="#transformer:shape1-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -172.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -135.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4146.000000 -191.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.630021 -175.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -142.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_212ceb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5323,-878 5323,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="42312@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219ee80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5323,-878 5323,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21c78a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5323,-973 5279,-973 5279,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_21da9e0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2377f10@0" Pin0InfoVect0LinkObjId="g_2377f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21da9e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5323,-973 5279,-973 5279,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_223ae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5323,-973 5323,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2377f10@0" ObjectIDND1="0@x" ObjectIDZND0="g_21da9e0@0" Pin0InfoVect0LinkObjId="g_21da9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2377f10_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5323,-973 5323,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2201ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4507,-771 4507,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="42318@0" ObjectIDZND0="42319@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-257114_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4507,-771 4507,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_222e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-522 4505,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-522 4505,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2174fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-770 4899,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="42326@0" ObjectIDZND0="42325@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-257168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-770 4899,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e9e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-520 4900,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-520 4900,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_219ee80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-862 4899,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="42312@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-862 4899,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c440a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-393 4605,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-393 4605,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20fdec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-393 4379,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-393 4379,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fa9f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-245 4379,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_21f6210@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_21f6210_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-245 4379,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ae070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4507,-878 4507,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="42312@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219ee80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4507,-878 4507,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_234eb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5323,-973 5323,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2377f10@0" ObjectIDND1="g_21da9e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2377f10_0" Pin1InfoVect1LinkObjId="g_21da9e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5323,-973 5323,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20095b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-809 4578,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_21037c0@0" Pin0InfoVect0LinkObjId="g_21037c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-809 4578,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22034f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-878 4200,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="42312@0" ObjectIDZND0="44998@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219ee80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-878 4200,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2203750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-942 4200,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44998@1" ObjectIDZND0="42701@0" Pin0InfoVect0LinkObjId="SW-260488_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-942 4200,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21dd2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-992 4200,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42701@1" ObjectIDZND0="44999@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260488_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-992 4200,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b8270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-1139 4200,-1174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="45000@1" ObjectIDZND0="45007@1" ObjectIDZND1="g_21519f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_23b92c0_1" Pin0InfoVect1LinkObjId="g_21519f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-1139 4200,-1174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2108a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-1183 4143,-1183 4143,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="voltageTransformer" ObjectIDND0="45000@x" ObjectIDND1="0@x" ObjectIDND2="45007@1" ObjectIDZND0="g_21519f0@0" Pin0InfoVect0LinkObjId="g_21519f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_24b8270_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-1183 4143,-1183 4143,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23b92c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-1174 4200,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="voltageTransformer" ObjectIDND0="45000@x" ObjectIDND1="0@x" ObjectIDZND0="45007@1" ObjectIDZND1="g_21519f0@0" Pin0InfoVect0LinkObjId="g_24b8270_1" Pin0InfoVect1LinkObjId="g_21519f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-1174 4200,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2151790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-1183 4200,-1196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="45000@x" ObjectIDND1="0@x" ObjectIDND2="g_21519f0@0" ObjectIDZND0="45007@1" Pin0InfoVect0LinkObjId="g_24b8270_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_21519f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-1183 4200,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2156390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-1094 4259,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24a52c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24a52c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-1094 4259,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21565d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-1153 4259,-1174 4200,-1174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="45000@x" ObjectIDZND1="45007@1" ObjectIDZND2="g_21519f0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24b8270_1" Pin0InfoVect2LinkObjId="g_21519f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-1153 4259,-1174 4200,-1174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2129000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4458,-878 4458,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="42312@0" ObjectIDZND0="45011@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219ee80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4458,-878 4458,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2129230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4458,-944 4458,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45011@1" ObjectIDZND0="42309@0" Pin0InfoVect0LinkObjId="SW-256774_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4458,-944 4458,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_217b2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4458,-1141 4458,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="45013@1" ObjectIDZND0="45010@1" ObjectIDZND1="g_23a1da0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_21543c0_1" Pin0InfoVect1LinkObjId="g_23a1da0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4458,-1141 4458,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2154160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4458,-1185 4401,-1185 4401,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="voltageTransformer" ObjectIDND0="45013@x" ObjectIDND1="0@x" ObjectIDND2="45010@1" ObjectIDZND0="g_23a1da0@0" Pin0InfoVect0LinkObjId="g_23a1da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_217b2f0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4458,-1185 4401,-1185 4401,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21543c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4458,-1176 4458,-1185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="voltageTransformer" ObjectIDND0="45013@x" ObjectIDND1="0@x" ObjectIDZND0="45010@1" ObjectIDZND1="g_23a1da0@0" Pin0InfoVect0LinkObjId="g_217b2f0_1" Pin0InfoVect1LinkObjId="g_23a1da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4458,-1176 4458,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2223910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4458,-1185 4458,-1198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="45013@x" ObjectIDND1="0@x" ObjectIDND2="g_23a1da0@0" ObjectIDZND0="45010@1" Pin0InfoVect0LinkObjId="g_217b2f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_23a1da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4458,-1185 4458,-1198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fa1d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4517,-1096 4517,-1119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2223b70@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2223b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4517,-1096 4517,-1119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fa1fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4517,-1155 4517,-1176 4458,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="45010@1" ObjectIDZND1="g_23a1da0@0" ObjectIDZND2="45013@x" Pin0InfoVect0LinkObjId="g_217b2f0_1" Pin0InfoVect1LinkObjId="g_23a1da0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4517,-1155 4517,-1176 4458,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21cd0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-878 4744,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="42312@0" ObjectIDZND0="45001@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219ee80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-878 4744,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21cd310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-944 4744,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45001@1" ObjectIDZND0="42314@0" Pin0InfoVect0LinkObjId="SW-256849_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-944 4744,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23b30a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-994 4744,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42314@1" ObjectIDZND0="45002@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-256849_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-994 4744,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24a4100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-1141 4744,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="45003@1" ObjectIDZND0="g_1981c00@0" ObjectIDZND1="45009@1" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1981c00_0" Pin0InfoVect1LinkObjId="g_2124530_1" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-1141 4744,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2124070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-1185 4687,-1185 4687,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="voltageTransformer" ObjectIDND0="45003@x" ObjectIDND1="0@x" ObjectIDND2="45009@1" ObjectIDZND0="g_1981c00@0" Pin0InfoVect0LinkObjId="g_1981c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2124530_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-1185 4687,-1185 4687,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21242d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-1176 4744,-1185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" ObjectIDND0="45003@x" ObjectIDND1="0@x" ObjectIDZND0="g_1981c00@0" ObjectIDZND1="45009@1" Pin0InfoVect0LinkObjId="g_1981c00_0" Pin0InfoVect1LinkObjId="g_2124530_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-1176 4744,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2124530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-1185 4744,-1198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="45003@x" ObjectIDND1="0@x" ObjectIDND2="g_1981c00@0" ObjectIDZND0="45009@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1981c00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-1185 4744,-1198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_221cca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4803,-1096 4803,-1119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19820e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19820e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4803,-1096 4803,-1119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221cf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4803,-1155 4803,-1176 4744,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_1981c00@0" ObjectIDZND1="45009@1" ObjectIDZND2="45003@x" Pin0InfoVect0LinkObjId="g_1981c00_0" Pin0InfoVect1LinkObjId="g_2124530_1" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4803,-1155 4803,-1176 4744,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_222b150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-878 5063,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="42312@0" ObjectIDZND0="45004@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219ee80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-878 5063,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21f7ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-943 5063,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45004@1" ObjectIDZND0="42316@0" Pin0InfoVect0LinkObjId="SW-256890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-943 5063,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22099f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-993 5063,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42316@1" ObjectIDZND0="45005@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-256890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-993 5063,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2209c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-1058 5063,-1175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="45005@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1fa0990@0" ObjectIDZND2="45008@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1fa0990_0" Pin0InfoVect2LinkObjId="g_2244d90_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-1058 5063,-1175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20f43d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-1095 5122,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21f5920@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f5920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-1095 5122,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f4630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-1154 5122,-1175 5063,-1175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@1" ObjectIDZND0="45005@x" ObjectIDZND1="g_1fa0990@0" ObjectIDZND2="45008@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1fa0990_0" Pin0InfoVect2LinkObjId="g_2244d90_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-1154 5122,-1175 5063,-1175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_216fb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-1056 4744,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="45002@1" ObjectIDZND0="45003@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-1056 4744,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_216fde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-1075 4744,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="45002@x" ObjectIDND1="0@x" ObjectIDZND0="45003@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-1075 4744,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2170040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1003 4021,-1003 4021,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_23674e0@0" ObjectIDND1="0@x" ObjectIDZND0="g_23ad200@0" Pin0InfoVect0LinkObjId="g_23ad200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23674e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1003 4021,-1003 4021,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2367280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1003 4107,-1003 4107,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="g_23ad200@0" ObjectIDZND0="g_23674e0@0" Pin0InfoVect0LinkObjId="g_23674e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_23ad200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1003 4107,-1003 4107,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fa04d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1016 4067,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_23674e0@0" ObjectIDZND1="g_23ad200@0" Pin0InfoVect0LinkObjId="g_23674e0_0" Pin0InfoVect1LinkObjId="g_23ad200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1016 4067,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fa0730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-1189 5017,-1189 5017,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="45005@x" ObjectIDND2="45008@1" ObjectIDZND0="g_1fa0990@0" Pin0InfoVect0LinkObjId="g_1fa0990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2244d90_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-1189 5017,-1189 5017,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2244b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-1175 5063,-1189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="45005@x" ObjectIDZND0="g_1fa0990@0" ObjectIDZND1="45008@1" Pin0InfoVect0LinkObjId="g_1fa0990_0" Pin0InfoVect1LinkObjId="g_2244d90_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-1175 5063,-1189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2244d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-1189 5063,-1197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1fa0990@0" ObjectIDND1="0@x" ObjectIDND2="45005@x" ObjectIDZND0="45008@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fa0990_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-1189 5063,-1197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be7ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4507,-563 4507,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4507,-563 4507,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be8460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-797 4899,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42326@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-257168_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-797 4899,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2236e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-390 4155,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-390 4155,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24b43d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-242 4186,-242 4186,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_24b4890@0" Pin0InfoVect0LinkObjId="g_24b4890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-242 4186,-242 4186,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24b4630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-242 4155,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_24b4890@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24b4890_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-242 4155,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_234aec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-175 4604,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="0@0" ObjectIDND2="g_21d18d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_21d18d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-175 4604,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24c31c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-184 4586,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_21d18d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_21d18d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-184 4586,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24c3420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4550,-184 4537,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_24c3680@0" Pin0InfoVect0LinkObjId="g_24c3680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4550,-184 4537,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24c4110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-193 4604,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_21d18d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_21d18d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-193 4604,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24c4370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-184 4604,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_21d18d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_21d18d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-184 4604,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c99ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-392 5075,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-392 5075,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c9c4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-347 5075,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-347 5075,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c9c750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-215 5106,-215 5106,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2c9c9b0@0" Pin0InfoVect0LinkObjId="g_2c9c9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-215 5106,-215 5106,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c9e610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-215 5075,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="0@0" ObjectIDND1="0@x" ObjectIDND2="g_2c9c9b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2c9c9b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-215 5075,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21610f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-292 5075,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-292 5075,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21636f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-224 5057,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDND1="0@x" ObjectIDND2="g_2c9c9b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2c9c9b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-224 5057,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2163950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5021,-224 5008,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2163bb0@0" Pin0InfoVect0LinkObjId="g_2163bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5021,-224 5008,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2164640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-233 5075,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2c9c9b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2c9c9b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-233 5075,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21648a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-224 5075,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2c9c9b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2c9c9b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_1" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-224 5075,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2165230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-175 4635,-175 4635,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@0" ObjectIDZND0="g_21d18d0@0" Pin0InfoVect0LinkObjId="g_21d18d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-175 4635,-175 4635,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21676b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-258 4313,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2167910@0" Pin0InfoVect0LinkObjId="g_2167910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-258 4313,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2168890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-258 4362,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_21f6210@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_21f6210_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-258 4362,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2168a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-269 4379,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_21f6210@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_21f6210_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-269 4379,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2367eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-258 4379,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_21f6210@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_21f6210_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-258 4379,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23680a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4101,-254 4088,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_23682d0@0" Pin0InfoVect0LinkObjId="g_23682d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4101,-254 4088,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2369090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-254 4137,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_24b4890@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24b4890_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-254 4137,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_236b510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-254 4155,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_24b4890@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_24b4890_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-254 4155,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_236b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-245 4410,-245 4410,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_21f6210@0" Pin0InfoVect0LinkObjId="g_21f6210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-245 4410,-245 4410,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24884c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-1075 4635,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="45002@x" ObjectIDND1="45003@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-1075 4635,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2488720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-1075 4363,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4599,-1075 4363,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2488980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-1075 4066,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-1075 4066,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248fa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4523,-809 4507,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="42318@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-257114_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4523,-809 4507,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248ff30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4507,-809 4507,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="42318@1" Pin0InfoVect0LinkObjId="SW-257114_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4507,-809 4507,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2490120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4507,-824 4507,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="42318@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-257114_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4507,-824 4507,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23a22a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4458,-1056 4458,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="45012@1" ObjectIDZND0="45013@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4458,-1056 4458,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23a24d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-1054 4200,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="44999@1" ObjectIDZND0="45000@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-1054 4200,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27da410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3506,-1008 3531,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3506,-1008 3531,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27dce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-1008 3576,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-1008 3576,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27dde60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-1008 3671,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_27dd0b0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2185c50@1" Pin0InfoVect0LinkObjId="g_2185c50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27dd0b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-1008 3671,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27de0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3612,-1008 3632,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2185c50@0" ObjectIDZND1="g_27dd0b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2185c50_0" Pin0InfoVect1LinkObjId="g_27dd0b0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3612,-1008 3632,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27dedb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-1008 3641,-1036 3656,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2185c50@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_27dd0b0@0" Pin0InfoVect0LinkObjId="g_27dd0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2185c50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-1008 3641,-1036 3656,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21859f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-970 3690,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_27de320@0" Pin0InfoVect0LinkObjId="g_27de320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-970 3690,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2187920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-1008 3632,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2185c50@0" ObjectIDND1="g_27dd0b0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2185c50_0" Pin1InfoVect1LinkObjId="g_27dd0b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-1008 3632,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2187b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3632,-1008 3632,-970 3645,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2185c50@0" ObjectIDND2="g_27dd0b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2185c50_0" Pin1InfoVect2LinkObjId="g_27dd0b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3632,-1008 3632,-970 3645,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2189ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3506,-912 3532,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3506,-912 3532,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_218c8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3559,-912 3577,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3559,-912 3577,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_218d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3642,-912 3672,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_218cb50@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2191420@1" Pin0InfoVect0LinkObjId="g_2191420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_218cb50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3642,-912 3672,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_218db60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-912 3633,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_218cb50@0" ObjectIDZND1="g_2191420@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_218cb50_0" Pin0InfoVect1LinkObjId="g_2191420_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-912 3633,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_218e850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3642,-912 3642,-940 3657,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2191420@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_218cb50@0" Pin0InfoVect0LinkObjId="g_218cb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2191420_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3642,-912 3642,-940 3657,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21911c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-874 3691,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_218ddc0@0" Pin0InfoVect0LinkObjId="g_218ddc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-874 3691,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23cae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3642,-912 3633,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_218cb50@0" ObjectIDND1="g_2191420@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_218cb50_0" Pin1InfoVect1LinkObjId="g_2191420_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3642,-912 3633,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23cb040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-912 3633,-874 3646,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_218cb50@0" ObjectIDND1="g_2191420@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_218cb50_0" Pin1InfoVect1LinkObjId="g_2191420_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-912 3633,-874 3646,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23cf100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3506,-816 3525,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3506,-816 3525,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23d0110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3592,-816 3592,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_23cf360@0" Pin0InfoVect0LinkObjId="g_23cf360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3592,-816 3592,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23d0c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3651,-816 3592,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_23cf360@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_23cf360_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3651,-816 3592,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23d4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3561,-816 3576,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_23cf360@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_23cf360_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3561,-816 3576,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23d48b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-816 3592,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_23cf360@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_23cf360_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-816 3592,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23d4b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-789 3576,-789 3576,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_23cf360@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_23cf360_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-789 3576,-789 3576,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23d4d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3631,-789 3639,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_23d0e60@0" Pin0InfoVect0LinkObjId="g_23d0e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3631,-789 3639,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d0800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3506,-704 3532,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3506,-704 3532,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d3200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3559,-704 3577,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3559,-704 3577,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d4210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3642,-704 3672,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_24d3460@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_24d7e70@1" Pin0InfoVect0LinkObjId="g_24d7e70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24d3460_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3642,-704 3672,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d4470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-704 3633,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_24d3460@0" ObjectIDZND1="g_24d7e70@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_24d3460_0" Pin0InfoVect1LinkObjId="g_24d7e70_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-704 3633,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d5160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3642,-704 3642,-732 3657,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_24d7e70@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_24d3460@0" Pin0InfoVect0LinkObjId="g_24d3460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24d7e70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3642,-704 3642,-732 3657,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d7c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-666 3691,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_24d46d0@0" Pin0InfoVect0LinkObjId="g_24d46d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-666 3691,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d8f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3642,-704 3633,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_24d3460@0" ObjectIDND1="g_24d7e70@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24d3460_0" Pin1InfoVect1LinkObjId="g_24d7e70_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3642,-704 3633,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d9170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-704 3633,-666 3646,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_24d3460@0" ObjectIDND1="g_24d7e70@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24d3460_0" Pin1InfoVect1LinkObjId="g_24d7e70_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-704 3633,-666 3646,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24dd4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3506,-592 3535,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3506,-592 3535,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25cd860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-592 3580,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-592 3580,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25ce750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-592 3675,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25cdac0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_25d21d0@1" Pin0InfoVect0LinkObjId="g_25d21d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25cdac0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-592 3675,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25ce9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-592 3636,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_25d21d0@0" ObjectIDZND1="g_25cdac0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_25d21d0_0" Pin0InfoVect1LinkObjId="g_25cdac0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-592 3636,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25cf640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-592 3645,-620 3660,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25d21d0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_25cdac0@0" Pin0InfoVect0LinkObjId="g_25cdac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25d21d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-592 3645,-620 3660,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25d1f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3684,-554 3694,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_25cec10@0" Pin0InfoVect0LinkObjId="g_25cec10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3684,-554 3694,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25d3420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-592 3636,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_25d21d0@0" ObjectIDND1="g_25cdac0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25d21d0_0" Pin1InfoVect1LinkObjId="g_25cdac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-592 3636,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25d3610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-592 3636,-554 3649,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_25d21d0@0" ObjectIDND2="g_25cdac0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_25d21d0_0" Pin1InfoVect2LinkObjId="g_25cdac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-592 3636,-554 3649,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25d70f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3506,-480 3535,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3506,-480 3535,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25d9af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-480 3580,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-480 3580,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25dab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-480 3675,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25d9d50@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_25de620@1" Pin0InfoVect0LinkObjId="g_25de620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25d9d50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-480 3675,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25dad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-480 3636,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_25d9d50@0" ObjectIDZND1="g_25de620@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_25d9d50_0" Pin0InfoVect1LinkObjId="g_25de620_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-480 3636,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25dba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-480 3645,-508 3660,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25de620@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_25d9d50@0" Pin0InfoVect0LinkObjId="g_25d9d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25de620_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-480 3645,-508 3660,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25de3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3684,-442 3694,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_25dafc0@0" Pin0InfoVect0LinkObjId="g_25dafc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3684,-442 3694,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25df870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-480 3636,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_25d9d50@0" ObjectIDND1="g_25de620@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25d9d50_0" Pin1InfoVect1LinkObjId="g_25de620_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-480 3636,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25dfa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-480 3636,-442 3649,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_25d9d50@0" ObjectIDND1="g_25de620@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25d9d50_0" Pin1InfoVect1LinkObjId="g_25de620_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-480 3636,-442 3649,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25e3780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3506,-368 3537,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3506,-368 3537,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25e6180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3564,-368 3582,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3564,-368 3582,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25e7190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-368 3677,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25e63e0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_25eacb0@1" Pin0InfoVect0LinkObjId="g_25eacb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25e63e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-368 3677,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25e73f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3618,-368 3638,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_25e63e0@0" ObjectIDZND1="g_25eacb0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_25e63e0_0" Pin0InfoVect1LinkObjId="g_25eacb0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3618,-368 3638,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25e80e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-368 3647,-396 3662,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25eacb0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_25e63e0@0" Pin0InfoVect0LinkObjId="g_25e63e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25eacb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-368 3647,-396 3662,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25eaa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-330 3696,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_25e7650@0" Pin0InfoVect0LinkObjId="g_25e7650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-330 3696,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25ebf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-368 3638,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_25e63e0@0" ObjectIDND1="g_25eacb0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25e63e0_0" Pin1InfoVect1LinkObjId="g_25eacb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-368 3638,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25ec0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-368 3638,-330 3651,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_25e63e0@0" ObjectIDND1="g_25eacb0@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25e63e0_0" Pin1InfoVect1LinkObjId="g_25eacb0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-368 3638,-330 3651,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25ef980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3506,-256 3535,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3506,-256 3535,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f2380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3562,-255 3580,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3562,-255 3580,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f3390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-256 3675,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25f25e0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_25f6eb0@1" Pin0InfoVect0LinkObjId="g_25f6eb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25f25e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-256 3675,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f35f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-255 3636,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_25f25e0@0" ObjectIDZND1="g_25f6eb0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_25f25e0_0" Pin0InfoVect1LinkObjId="g_25f6eb0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-255 3636,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f42e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-255 3645,-283 3660,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25f6eb0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_25f25e0@0" Pin0InfoVect0LinkObjId="g_25f25e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25f6eb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-255 3645,-283 3660,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f6c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3684,-217 3694,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_25f3850@0" Pin0InfoVect0LinkObjId="g_25f3850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3684,-217 3694,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f8100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-255 3636,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_25f25e0@0" ObjectIDND1="g_25f6eb0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25f25e0_0" Pin1InfoVect1LinkObjId="g_25f6eb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-255 3636,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f82f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-255 3636,-217 3649,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_25f25e0@0" ObjectIDND1="g_25f6eb0@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25f25e0_0" Pin1InfoVect1LinkObjId="g_25f6eb0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-255 3636,-217 3649,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25fd260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-176 3648,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="transformer2" ObjectIDND0="g_25f6eb0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25f6eb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-176 3648,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25fdd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3534,-143 3534,-176 3648,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="g_25f6eb0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_25f6eb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3534,-143 3534,-176 3648,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25fdfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-176 3744,-176 3744,-255 3714,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_25f6eb0@0" Pin0InfoVect0LinkObjId="g_25f6eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-176 3744,-176 3744,-255 3714,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26038a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-511 5315,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-511 5315,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26062e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-556 5315,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-556 5315,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2606540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-611 5315,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2607250@0" Pin0InfoVect0LinkObjId="g_2607250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-611 5315,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_260be00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-390 5254,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-390 5254,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_260c060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-345 5254,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_244a5b0@1" Pin0InfoVect0LinkObjId="g_244a5b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-345 5254,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_260edb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-393 4834,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-393 4834,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26117f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-182 4864,-182 4864,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2611a50@0" Pin0InfoVect0LinkObjId="g_2611a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-182 4864,-182 4864,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26137f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-182 4834,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2611a50@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2611a50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-182 4834,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26185d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-191 4815,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_2611a50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2611a50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-191 4815,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2618830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-191 4766,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2618a90@0" Pin0InfoVect0LinkObjId="g_2618a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-191 4766,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2619520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-200 4834,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2611a50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2611a50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-200 4834,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2619780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-191 4834,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_2611a50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2611a50_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-191 4834,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_261da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5487,-362 5463,-362 5463,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_261dea0@0" Pin0InfoVect0LinkObjId="g_261dea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5487,-362 5463,-362 5463,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_261dc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5488,-362 5488,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_261dea0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_261dea0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5488,-362 5488,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_261ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5509,-290 5509,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_261f0d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_261f0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5509,-290 5509,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_261ee70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5510,-351 5510,-362 5488,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_261dea0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_261dea0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5510,-351 5510,-362 5488,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2430f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4507,-616 4507,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4507,-616 4507,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24311a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-550 4899,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-550 4899,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2431400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-601 4899,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-601 4899,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2431660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-254 4155,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_24b4890@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24b4890_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-254 4155,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24318c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-320 3749,-320 3749,-368 3718,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_25eacb0@0" Pin0InfoVect0LinkObjId="g_25eacb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-320 3749,-320 3749,-368 3718,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24323c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-302 4155,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_25eacb0@0" Pin0InfoVect0LinkObjId="g_25eacb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-302 4155,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2432620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-320 4155,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_25eacb0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_25eacb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-320 4155,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2432c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-331 3809,-478 3808,-480 3714,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_25de620@0" Pin0InfoVect0LinkObjId="g_25de620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-331 3809,-478 3808,-480 3714,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2433d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-311 3840,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-311 3840,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2433f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-330 3840,-592 3714,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_25d21d0@0" Pin0InfoVect0LinkObjId="g_25d21d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-330 3840,-592 3714,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2434bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-311 3872,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-311 3872,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2434e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-305 4379,-324 4380,-320 4266,-320 4266,-142 4265,-144 3808,-143 3808,-145 3809,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-305 4379,-324 4380,-320 4266,-320 4266,-142 4265,-144 3808,-143 3808,-145 3809,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24350a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-134 3871,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-134 3871,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2435620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-90 3870,-48 3873,-48 4722,-48 4722,-249 4834,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-90 3870,-48 3873,-48 4722,-48 4722,-249 4834,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24358a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-330 3872,-704 3711,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_24d7e70@0" Pin0InfoVect0LinkObjId="g_24d7e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-330 3872,-704 3711,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2437f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-669 4506,-656 3969,-656 3969,-1008 3713,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="42319@0" ObjectIDZND0="g_2185c50@0" Pin0InfoVect0LinkObjId="g_2185c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2201ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-669 4506,-656 3969,-656 3969,-1008 3713,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_243ca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-296 4605,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-296 4605,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_243cc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-348 4605,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-348 4605,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_243ce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-130 3840,-96 4510,-96 4511,-97 4512,-238 4512,-256 4604,-256 4604,-261 4604,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-130 3840,-96 4510,-96 4511,-97 4512,-238 4512,-256 4604,-256 4604,-261 4604,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_243d010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-351 4835,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-351 4835,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_243d240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-294 4835,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-294 4835,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_243dc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-261 4834,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-261 4834,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_243ded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-249 4834,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-249 4834,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2440260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-668 4899,-644 3916,-644 3914,-912 3711,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="42325@x" ObjectIDZND0="g_2191420@0" Pin0InfoVect0LinkObjId="g_2191420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2174fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-668 4899,-644 3916,-644 3914,-912 3711,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24404b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4459,-1005 4413,-1005 4413,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="42309@x" ObjectIDND1="45012@x" ObjectIDZND0="g_24406f0@0" Pin0InfoVect0LinkObjId="g_24406f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-256774_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4459,-1005 4413,-1005 4413,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2441f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4458,-994 4458,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42309@1" ObjectIDZND0="g_24406f0@0" ObjectIDZND1="45012@x" Pin0InfoVect0LinkObjId="g_24406f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-256774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4458,-994 4458,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24421e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4458,-1005 4458,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_24406f0@0" ObjectIDND1="42309@x" ObjectIDZND0="45012@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24406f0_0" Pin1InfoVect1LinkObjId="SW-256774_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4458,-1005 4458,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2442440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5488,-445 5488,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5488,-445 5488,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2445630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-475 5315,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-475 5315,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2445890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-426 5254,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-426 5254,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2445af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-428 5075,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-428 5075,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2445d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-466 4900,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-466 4900,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2447cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-426 4155,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-426 4155,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24484f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-429 4379,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-429 4379,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2448d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-468 4505,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-468 4505,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2449550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-429 4605,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-429 4605,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2449d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-429 4834,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-429 4834,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24505a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1061 4067,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1061 4067,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2450790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1075 4016,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1075 4016,-1075 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52547" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3446.000000 -1092.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9257" ObjectName="DYN-LF_DX"/>
     <cge:Meas_Ref ObjectId="52547"/>
    </metadata>
   </g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4181,-293 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-311 3808,-311 3807,-311 3805,-312 3804,-312 3803,-313 3802,-314 3801,-315 3800,-316 3800,-317 3799,-318 3799,-319 3799,-321 3799,-322 3799,-323 3800,-324 3800,-326 3801,-327 3802,-328 3803,-329 3804,-329 3805,-330 3806,-330 3807,-331 3809,-331 3810,-331 " stroke="rgb(60,120,255)" stroke-width="0.978788"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4409,-291 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-311 3840,-311 3839,-311 3837,-312 3836,-312 3835,-313 3834,-314 3833,-315 3832,-316 3832,-317 3831,-318 3831,-319 3831,-321 3831,-322 3831,-323 3832,-324 3832,-326 3833,-327 3834,-328 3835,-329 3836,-329 3837,-330 3838,-330 3839,-331 3841,-331 3842,-331 " stroke="rgb(60,120,255)" stroke-width="0.978788"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-134 3839,-134 3838,-134 3836,-135 3835,-135 3834,-136 3833,-137 3832,-138 3831,-139 3831,-140 3830,-141 3830,-142 3830,-144 3830,-145 3830,-146 3831,-147 3831,-149 3832,-150 3833,-151 3834,-152 3835,-152 3836,-153 3837,-153 3838,-154 3840,-154 3841,-154 " stroke="rgb(60,120,255)" stroke-width="0.978788"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-311 3871,-311 3870,-311 3868,-312 3867,-312 3866,-313 3865,-314 3864,-315 3863,-316 3863,-317 3862,-318 3862,-319 3862,-321 3862,-322 3862,-323 3863,-324 3863,-326 3864,-327 3865,-328 3866,-329 3867,-329 3868,-330 3869,-330 3870,-331 3872,-331 3873,-331 " stroke="rgb(60,120,255)" stroke-width="0.978788"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-134 3870,-134 3869,-134 3867,-135 3866,-135 3865,-136 3864,-137 3863,-138 3862,-139 3862,-140 3861,-141 3861,-142 3861,-144 3861,-145 3861,-146 3862,-147 3862,-149 3863,-150 3864,-151 3865,-152 3866,-152 3867,-153 3868,-153 3869,-154 3871,-154 3872,-154 " stroke="rgb(60,120,255)" stroke-width="0.978788"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-89 3870,-89 3869,-89 3867,-90 3866,-90 3865,-91 3864,-92 3863,-93 3862,-94 3862,-95 3861,-96 3861,-97 3861,-99 3861,-100 3861,-101 3862,-102 3862,-104 3863,-105 3864,-106 3865,-107 3866,-107 3867,-108 3868,-108 3869,-109 3871,-109 3872,-109 " stroke="rgb(60,120,255)" stroke-width="0.978788"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_DX.LF_GF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4056,-878 5360,-878 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42312" ObjectName="BS-LF_DX.LF_GF_3IM"/>
    <cge:TPSR_Ref TObjectID="42312"/></metadata>
   <polyline fill="none" opacity="0" points="4056,-878 5360,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4061,-445 5621,-445 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4061,-445 5621,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3506,-193 3506,-1064 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3506,-193 3506,-1064 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="5323" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="4200" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="4458" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="4744" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="5063" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42312" cx="4507" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3506" cy="-1008" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3506" cy="-912" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3506" cy="-816" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3506" cy="-704" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3506" cy="-592" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3506" cy="-480" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3506" cy="-368" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3506" cy="-255" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5488" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5315" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5254" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5075" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4900" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4155" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4379" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4505" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4605" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4834" cy="-445" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-257114">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.065147 -763.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42318" ObjectName="SW-LF_DX.LF_DX_312BK"/>
     <cge:Meas_Ref ObjectId="257114"/>
    <cge:TPSR_Ref TObjectID="42318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.780570 -514.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257168">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.057818 -762.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42326" ObjectName="SW-LF_DX.LF_DX_311BK"/>
     <cge:Meas_Ref ObjectId="257168"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.351196 -515.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.955059 -340.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.515539 -340.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260488">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.000000 -957.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42701" ObjectName="SW-LF_DX.LF_DX_387BK"/>
     <cge:Meas_Ref ObjectId="260488"/>
    <cge:TPSR_Ref TObjectID="42701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-256774">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -959.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42309" ObjectName="SW-LF_DX.LF_DX_388BK"/>
     <cge:Meas_Ref ObjectId="256774"/>
    <cge:TPSR_Ref TObjectID="42309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-256890">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.000000 -958.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42316" ObjectName="SW-LF_DX.LF_DX_386BK"/>
     <cge:Meas_Ref ObjectId="256890"/>
    <cge:TPSR_Ref TObjectID="42316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4146.236434 -337.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.630021 -339.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-256849">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -959.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42314" ObjectName="SW-LF_DX.LF_DX_389BK"/>
     <cge:Meas_Ref ObjectId="256849"/>
    <cge:TPSR_Ref TObjectID="42314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3567.000000 -999.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3568.000000 -903.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3567.166667 -695.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3570.166667 -583.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3570.166667 -471.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3572.166667 -359.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3570.166667 -246.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5324.984796 -565.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5244.908446 -337.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.239765 -340.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2224c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2224c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2224c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2224c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2224c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2224c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2224c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2205570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_216d140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">甸心变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_21f6820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5285.000000 -864.000000) translate(0,18)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2198600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4065.000000 -492.000000) translate(0,18)">10kV 母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_24b8ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4562.000000 -97.000000) translate(0,18)">羊毛岭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_21ff340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5032.500000 -138.000000) translate(0,18)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1fac880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5265.000000 -693.000000) translate(0,18)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2318970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4300.000000 -182.000000) translate(0,18)">安乐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2199390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5334.000000 -923.000000) translate(0,15)">3600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2000160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 -791.000000) translate(0,15)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23bb460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.515539 -371.000000) translate(0,15)">488</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21ed540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4385.515539 -420.000000) translate(0,15)">4881</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21ed7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4386.000000 -296.000000) translate(0,15)">4886</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24f59d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.715424 -545.000000) translate(0,15)">422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24fae40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4910.293379 -543.000000) translate(0,15)">421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24fb0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4909.293379 -493.000000) translate(0,15)">4212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24fa900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4513.715424 -495.000000) translate(0,15)">4222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24fab40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.955059 -371.000000) translate(0,15)">484</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24f01d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.955059 -321.000000) translate(0,15)">4842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23b2370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.955059 -420.000000) translate(0,15)">4841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b25b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4850.000000 -853.000000) translate(0,12)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b5110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4534.000000 -849.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2205070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -232.000000) translate(0,16)">15758580348</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_23d9130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4935.000000 -684.000000) translate(0,15)">S7-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1fa1900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5271.000000 -1080.000000) translate(0,18)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fabe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4531.000000 -831.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2156830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4225.000000 -926.000000) translate(0,15)">3871</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19db600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -1036.000000) translate(0,15)">3872</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2200bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4206.000000 -1128.000000) translate(0,15)">3873</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2200df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -1134.000000) translate(0,15)">3873D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_222a1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4194.000000 -1263.000000) translate(0,18)">辛甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2114130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 -1209.000000) translate(0,16)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fa2220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4483.000000 -928.000000) translate(0,15)">3881</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_224a210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4480.000000 -1038.000000) translate(0,15)">3882</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_224a450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -1110.000000) translate(0,15)">3883</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21ef670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4532.000000 -1136.000000) translate(0,15)">3883D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_21ef8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 -1265.000000) translate(0,18)">甸心线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24f7030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4318.000000 -1211.000000) translate(0,16)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_221d160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -928.000000) translate(0,15)">3891</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_242ec80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1038.000000) translate(0,15)">3892</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_242ef50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.000000 -1110.000000) translate(0,15)">3893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21f21c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4818.000000 -1136.000000) translate(0,15)">3893D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_21f2400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -1265.000000) translate(0,18)">甸黑阿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2192f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4604.000000 -1211.000000) translate(0,16)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f4890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5088.000000 -927.000000) translate(0,15)">3861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21481b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5085.000000 -1037.000000) translate(0,15)">3862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21483f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5137.000000 -1135.000000) translate(0,15)">3862D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2148630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5057.000000 -1264.000000) translate(0,18)">高峰线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_20060f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4547.000000 -695.000000) translate(0,15)">S9-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1be7d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4913.000000 -593.000000) translate(0,15)">4211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1be8220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -588.000000) translate(0,15)">4221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1be8650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4014.000000 -927.000000) translate(0,18)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_24b55a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4073.000000 -221.000000) translate(0,18)">琅井线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24b6090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4164.236434 -368.000000) translate(0,15)">489</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24b6310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4162.236434 -417.000000) translate(0,15)">4891</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24b6550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -314.000000) translate(0,15)">4896</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24c45d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -226.000000) translate(0,15)">4846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24c4ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4536.000000 -214.000000) translate(0,15)">48467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c9d760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5083.630021 -370.000000) translate(0,15)">483</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c9dc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5081.630021 -319.000000) translate(0,15)">4832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c9de90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5081.630021 -419.000000) translate(0,15)">4831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2164b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5092.000000 -266.000000) translate(0,15)">4836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2164ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5002.000000 -254.000000) translate(0,15)">48367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21683a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -288.000000) translate(0,15)">48867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2368bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4074.000000 -284.000000) translate(0,15)">48967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2488be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4592.000000 -1059.000000) translate(0,15)">3800</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24890d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -1061.000000) translate(0,15)">3805</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_248afd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3438.000000 -1165.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_248c260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3438.000000 -1200.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_248c910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -195.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_248c910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -195.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_248ecb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -205.500000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_248ecb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -205.500000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_248ecb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -205.500000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4480.000000 -988.000000) translate(0,12)">388</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a4710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4763.000000 -989.000000) translate(0,12)">389</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23a58d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5087.000000 -986.000000) translate(0,15)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27df8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -999.000000) translate(0,15)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2186cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3566.000000 -1040.000000) translate(0,15)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2187d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -986.000000) translate(0,15)">00267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_218eab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3531.000000 -903.000000) translate(0,15)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2192040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -944.000000) translate(0,15)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23cb230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3583.000000 -890.000000) translate(0,15)">00167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_23d6760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -827.000000) translate(0,13)">10kV环网柜母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_23d6760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -827.000000) translate(0,29)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24d53c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3531.000000 -695.000000) translate(0,15)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24d8a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -736.000000) translate(0,15)">0126</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24d9360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3583.000000 -674.000000) translate(0,15)">01267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24d9680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3518.000000 -808.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24d98c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3551.000000 -781.000000) translate(0,15)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_24dafe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3721.000000 -734.000000) translate(0,18)">妥安线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25cf8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3534.000000 -583.000000) translate(0,15)">013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d2df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -624.000000) translate(0,15)">0136</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d3800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3586.000000 -562.000000) translate(0,15)">01367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_25d5000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -623.000000) translate(0,18)">羊毛岭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dbcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3534.000000 -471.000000) translate(0,15)">014</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25df240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -512.000000) translate(0,15)">0146</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3586.000000 -450.000000) translate(0,15)">01467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_25e1450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3724.000000 -398.000000) translate(0,18)">琅井线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_25e18a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3734.000000 -508.000000) translate(0,18)">安乐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e8340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3536.000000 -359.000000) translate(0,15)">015</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25eb8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -400.000000) translate(0,15)">0156</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25ec2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3588.000000 -338.000000) translate(0,15)">01567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25f4540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3534.000000 -246.000000) translate(0,15)">016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25f7ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -287.000000) translate(0,15)">0166</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25f84e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3586.000000 -225.000000) translate(0,15)">01667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_25fe220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3484.000000 -80.000000) translate(0,18)">10kV保护TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_25ff240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3660.000000 -76.000000) translate(0,18)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26067a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5326.984796 -550.000000) translate(0,15)">482</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2606dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5329.984796 -602.000000) translate(0,15)">4822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2607010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5332.984796 -497.000000) translate(0,15)">4821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_260c2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5207.000000 -203.000000) translate(0,18)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_260cc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5262.908446 -368.000000) translate(0,15)">487</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_260cf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5260.908446 -417.000000) translate(0,15)">4871</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2612800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.239765 -371.000000) translate(0,15)">481</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2612e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4844.239765 -322.000000) translate(0,15)">4812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2613070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4841.239765 -420.000000) translate(0,15)">4811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26199e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4842.000000 -233.000000) translate(0,15)">4816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2619ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4752.000000 -221.000000) translate(0,15)">48167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_261c640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -127.000000) translate(0,18)">妥安线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_26209a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5404.000000 -256.000000) translate(0,18)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2620ea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5425.000000 -409.000000) translate(0,15)">4600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2435b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.500000 -101.000000) translate(0,18)">站外1号杆引流已解脱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_24376e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4938.500000 -635.000000) translate(0,18)">解脱断面</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2438130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -1091.000000) translate(0,18)">10kV环网柜母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2439040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4547.000000 -741.000000) translate(0,16)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2439830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4516.000000 -792.000000) translate(0,16)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2439c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4065.000000 -854.000000) translate(0,16)">35kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243f100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3574.000000 -1183.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_244dbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4946.000000 -726.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -986.000000) translate(0,12)">387</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21037c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 -803.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a52c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 -1076.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2223b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 -1078.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19820e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 -1078.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f5920" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5116.000000 -1077.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24c3680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 -178.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2163bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4989.630021 -218.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2167910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -252.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23682d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 -248.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27de320" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3708.500000 -975.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218ddc0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3709.500000 -879.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d0e60" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3657.500000 -796.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24d46d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3708.500000 -671.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25cec10" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3711.500000 -559.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25dafc0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3711.500000 -447.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e7650" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3713.500000 -335.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f3850" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3711.500000 -222.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2618a90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4748.000000 -185.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_DX"/>
</svg>