<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-148" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1157 2754 1299">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape131">
    <ellipse cx="32" cy="19" rx="7.5" ry="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="8" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="32" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="16" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="14" y2="16"/>
    <ellipse cx="21" cy="15" rx="7.5" ry="6.5" stroke-width="1"/>
    <ellipse cx="43" cy="19" rx="7.5" ry="6.5" stroke-width="1"/>
    <ellipse cx="32" cy="9" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="43" cy="9" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="2" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="3" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="32" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="32" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="43" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="43" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="41" y1="17" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="46" y1="17" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="41" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="32" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="32" y1="18" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape187">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="56" y2="47"/>
    <polyline arcFlag="1" points="6,36 5,36 5,36 4,36 3,36 3,37 2,37 1,38 1,38 1,39 0,40 0,40 0,41 0,42 0,43 0,43 1,44 1,45 1,45 2,46 3,46 3,47 4,47 5,47 5,47 6,47 " stroke-width="0.171589"/>
    <polyline arcFlag="1" points="6,25 5,25 5,25 4,25 3,25 3,26 2,26 1,27 1,27 1,28 0,29 0,29 0,30 0,31 0,32 0,32 1,33 1,34 1,34 2,35 3,35 3,36 4,36 5,36 5,36 6,36 " stroke-width="0.171589"/>
    <polyline arcFlag="1" points="6,14 5,14 5,14 4,14 3,14 3,15 2,15 1,16 1,16 1,17 0,18 0,18 0,19 0,20 0,21 0,21 1,22 1,23 1,23 2,24 3,24 3,25 4,25 5,25 5,25 6,25 " stroke-width="0.171589"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_2b44660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape29">
    <polyline DF8003:Layer="PUBLIC" points="16,35 22,32 22,38 16,35 16,35 16,35 "/>
    <polyline DF8003:Layer="PUBLIC" points="14,35 8,32 8,38 14,35 14,35 14,35 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="2" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="53" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="78" x2="2" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="38" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="22" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="79" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="76" x2="80" y1="64" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="75" x2="81" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="78" x2="78" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="78" x2="78" y1="63" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="78,25 75,19 81,19 78,25 78,25 78,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="78" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="78" y1="16" y2="16"/>
    <polyline DF8003:Layer="PUBLIC" points="78,27 75,33 81,33 78,27 78,27 78,27 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="40" y1="38" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="40" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="38" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="38" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="56" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="56" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="56" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="56" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="56" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="56" y1="36" y2="40"/>
    <circle cx="39" cy="17" r="11" stroke-width="1"/>
    <circle cx="56" cy="35" r="11" stroke-width="1"/>
    <ellipse cx="38" cy="35" rx="11.5" ry="11" stroke-width="1"/>
    <circle cx="56" cy="17" r="11" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape113">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="9" y1="5" y2="36"/>
    <rect height="31" stroke-width="1" width="15" x="1" y="5"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="reactance:shape1">
    <polyline points="13,13 11,13 9,14 8,14 6,15 5,16 3,17 2,19 1,21 1,22 0,24 0,26 0,28 1,30 1,31 2,33 3,34 5,36 6,37 8,38 9,38 11,39 13,39 15,39 17,38 18,38 20,37 21,36 23,34 24,33 25,31 25,30 26,28 26,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="13" x2="25" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="47" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="13" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape42_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape60_0">
    <circle cx="15" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="36" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="14" y1="63" y2="65"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="20,63 22,61 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,54 13,54 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="49" x2="17" y1="16" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="20" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="59" y2="54"/>
    <polyline DF8003:Layer="PUBLIC" points="16,86 10,99 23,99 16,86 16,87 16,86 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="114" y2="72"/>
   </symbol>
   <symbol id="transformer2:shape60_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,35 49,35 49,16 " stroke-width="1"/>
    <circle cx="15" cy="35" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="35" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="35" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="35" y2="30"/>
   </symbol>
   <symbol id="transformer2:shape61_0">
    <circle cx="15" cy="35" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,35 49,35 49,16 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="49" x2="17" y1="16" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="35" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="35" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="35" y2="40"/>
   </symbol>
   <symbol id="transformer2:shape61_1">
    <circle cx="15" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="16" x2="11" y1="65" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="16" x2="22" y1="65" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="11" x2="22" y1="55" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape59_0">
    <circle cx="24" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="26,10 20,23 33,23 26,10 26,11 26,10 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="30" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="20" y1="57" y2="52"/>
    <circle cx="24" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="42" y2="0"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="25,57 58,57 58,27 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="25" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="58" y1="23" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="63" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="61" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="59" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="29" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="29" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="21" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="21" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="76" y2="76"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="25,76 22,76 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="25,76 22,76 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="29,85 31,83 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="29,85 31,83 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="23" y1="85" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="23" y1="85" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="81" y2="81"/>
   </symbol>
   <symbol id="transformer2:shape59_1">
    <circle cx="24" cy="79" fillStyle="0" r="15" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="64" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="57" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="9" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="45" y1="9" y2="9"/>
    <circle cx="31" cy="46" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="40" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="31" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="23" x2="23" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="39" x2="22" y1="10" y2="10"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1309" width="2764" x="3112" y="-1162"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33157b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 883.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3276f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.000000 868.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22dbc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 853.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d1c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3658.000000 -3.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24310d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f9cd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3672.000000 -33.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2345820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 385.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2346690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 401.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2346bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 357.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cba8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 371.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cbab60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 417.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fdaa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.000000 406.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fdac50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 391.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fdae20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 376.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41c7b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4815.000000 832.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41c7d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4815.000000 848.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41c7f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 804.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41c8120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 818.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41c8330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4815.000000 864.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_41c8e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3883.000000 520.000000) translate(0,15)">档位：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea2580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3891.000000 726.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea2840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 711.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea2a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3905.000000 696.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4002440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5713.000000 399.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40028d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5713.000000 415.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4002b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5719.000000 371.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4002d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5705.000000 385.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4002f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5713.000000 431.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bd9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.000000 438.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bdcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 423.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bdef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 408.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35be310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 724.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35be5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.000000 709.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.000000 694.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="3744,-533 3739,-544 3750,-544 3744,-533 3744,-534 3744,-533 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="3744,-519 3739,-508 3750,-508 3744,-519 3744,-518 3744,-519 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="4581,-530 4576,-541 4587,-541 4581,-530 4581,-531 4581,-530 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="4581,-516 4576,-505 4587,-505 4581,-516 4581,-515 4581,-516 " stroke="rgb(170,85,127)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-124384">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 -840.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22847" ObjectName="SW-CX_DZS.CX_DZS_151BK"/>
     <cge:Meas_Ref ObjectId="124384"/>
    <cge:TPSR_Ref TObjectID="22847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5745.000000 -787.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5762.000000 -831.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124391">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 -681.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22853" ObjectName="SW-CX_DZS.CX_DZS_101BK"/>
     <cge:Meas_Ref ObjectId="124391"/>
    <cge:TPSR_Ref TObjectID="22853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -388.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22860" ObjectName="SW-CX_DZS.CX_DZS_301BK"/>
     <cge:Meas_Ref ObjectId="124398"/>
    <cge:TPSR_Ref TObjectID="22860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124408">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22867" ObjectName="SW-CX_DZS.CX_DZS_351BK"/>
     <cge:Meas_Ref ObjectId="124408"/>
    <cge:TPSR_Ref TObjectID="22867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124413">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22871" ObjectName="SW-CX_DZS.CX_DZS_352BK"/>
     <cge:Meas_Ref ObjectId="124413"/>
    <cge:TPSR_Ref TObjectID="22871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124418">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22875" ObjectName="SW-CX_DZS.CX_DZS_353BK"/>
     <cge:Meas_Ref ObjectId="124418"/>
    <cge:TPSR_Ref TObjectID="22875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -238.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22879" ObjectName="SW-CX_DZS.CX_DZS_354BK"/>
     <cge:Meas_Ref ObjectId="124423"/>
    <cge:TPSR_Ref TObjectID="22879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124430">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 -228.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22885" ObjectName="SW-CX_DZS.CX_DZS_312BK"/>
     <cge:Meas_Ref ObjectId="124430"/>
    <cge:TPSR_Ref TObjectID="22885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297663">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.000000 -678.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46141" ObjectName="SW-CX_DZS.CX_DZS_102BK"/>
     <cge:Meas_Ref ObjectId="297663"/>
    <cge:TPSR_Ref TObjectID="46141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297670">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -412.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46148" ObjectName="SW-CX_DZS.CX_DZS_302BK"/>
     <cge:Meas_Ref ObjectId="297670"/>
    <cge:TPSR_Ref TObjectID="46148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297684">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4893.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46160" ObjectName="SW-CX_DZS.CX_DZS_356BK"/>
     <cge:Meas_Ref ObjectId="297684"/>
    <cge:TPSR_Ref TObjectID="46160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297689">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5154.000000 -218.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46164" ObjectName="SW-CX_DZS.CX_DZS_357BK"/>
     <cge:Meas_Ref ObjectId="297689"/>
    <cge:TPSR_Ref TObjectID="46164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297693">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5334.000000 -223.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46167" ObjectName="SW-CX_DZS.CX_DZS_358BK"/>
     <cge:Meas_Ref ObjectId="297693"/>
    <cge:TPSR_Ref TObjectID="46167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5514.000000 -218.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5703.000000 -222.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297677">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 -226.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46154" ObjectName="SW-CX_DZS.CX_DZS_355BK"/>
     <cge:Meas_Ref ObjectId="297677"/>
    <cge:TPSR_Ref TObjectID="46154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297682">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 -15.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46159" ObjectName="SW-CX_DZS.CX_DZS_350BK"/>
     <cge:Meas_Ref ObjectId="297682"/>
    <cge:TPSR_Ref TObjectID="46159"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3308190">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4057.000000 -1029.000000)" xlink:href="#voltageTransformer:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DZS.CX_DZS_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3693,-782 4882,-782 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22844" ObjectName="BS-CX_DZS.CX_DZS_1IM"/>
    <cge:TPSR_Ref TObjectID="22844"/></metadata>
   <polyline fill="none" opacity="0" points="3693,-782 4882,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5213,-751 5359,-751 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5213,-751 5359,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5450,-753 5840,-753 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5450,-753 5840,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DZS.CX_DZS_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-336 5837,-336 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46140" ObjectName="BS-CX_DZS.CX_DZS_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="46140"/></metadata>
   <polyline fill="none" opacity="0" points="4582,-336 5837,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DZS.CX_DZS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3530,-334 4514,-334 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22845" ObjectName="BS-CX_DZS.CX_DZS_3IM"/>
    <cge:TPSR_Ref TObjectID="22845"/></metadata>
   <polyline fill="none" opacity="0" points="3530,-334 4514,-334 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4693.000000 54.000000)" xlink:href="#reactance:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_4017230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 -939.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3670410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -881.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3545c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4010.000000 -833.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_404e210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 -883.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d9690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 -807.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2353ad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3721.000000 -458.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e1f0d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -183.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3347020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5348.000000 -897.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e36e10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5607.000000 -898.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_362f440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 -724.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_367e380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 -674.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_361b230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 -616.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3454140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3847.000000 -182.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_46b66e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4048.000000 -183.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35e1160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 50.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41ccb50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -180.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2382c70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4558.000000 -455.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fddac0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -720.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fde550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 -671.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d4490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4581.000000 -613.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_239a450" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4565.000000 -393.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e0d7a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 54.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fc9e70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 -264.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41b49b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5261.000000 -267.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22cbae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5441.000000 -262.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fafe60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5630.000000 -265.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fd55c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4624.000000 -83.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2603fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -261.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e9fca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -261.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4006390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 -406.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4007080" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 -408.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2b30710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-334 3729,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22845@0" ObjectIDZND0="22868@1" Pin0InfoVect0LinkObjId="SW-124409_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e1de40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-334 3729,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ee6440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-189 3711,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3ee3280@0" ObjectIDND1="g_3582930@0" ObjectIDND2="22869@x" ObjectIDZND0="22870@1" Pin0InfoVect0LinkObjId="SW-124411_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ee3280_0" Pin1InfoVect1LinkObjId="g_3582930_0" Pin1InfoVect2LinkObjId="SW-124410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-189 3711,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed7f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3675,-189 3665,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22870@0" ObjectIDZND0="g_3e1f0d0@0" Pin0InfoVect0LinkObjId="g_3e1f0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3675,-189 3665,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ded620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-782 4091,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22844@0" ObjectIDZND0="22848@0" Pin0InfoVect0LinkObjId="SW-124385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_267f770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-782 4091,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3f350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4037,-945 4027,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22852@1" ObjectIDZND0="g_4017230@0" Pin0InfoVect0LinkObjId="g_4017230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124389_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4037,-945 4027,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22daa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-839 4028,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22849@0" ObjectIDZND0="g_3545c30@0" Pin0InfoVect0LinkObjId="g_3545c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-839 4028,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2642970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-565 3769,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="22859@x" ObjectIDZND0="g_2a0b020@0" Pin0InfoVect0LinkObjId="g_2a0b020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-565 3769,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_400d9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-565 3824,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_2a0b020@0" ObjectIDND1="22859@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a0b020_0" Pin1InfoVect1LinkObjId="SW-124397_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-565 3824,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aaca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4671,-813 4661,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22865@1" ObjectIDZND0="g_22d9690@0" Pin0InfoVect0LinkObjId="g_22d9690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4671,-813 4661,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f4d9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4671,-889 4661,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22864@0" ObjectIDZND0="g_404e210@0" Pin0InfoVect0LinkObjId="g_404e210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4671,-889 4661,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f39930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-889 4707,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3362660@0" ObjectIDND1="g_2485b70@0" ObjectIDND2="22863@x" ObjectIDZND0="22864@1" Pin0InfoVect0LinkObjId="SW-124403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3362660_0" Pin1InfoVect1LinkObjId="g_2485b70_0" Pin1InfoVect2LinkObjId="SW-124402_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-889 4707,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3ee60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-889 4725,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="22864@x" ObjectIDND1="g_3362660@0" ObjectIDND2="g_2485b70@0" ObjectIDZND0="22863@1" Pin0InfoVect0LinkObjId="SW-124402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-124403_0" Pin1InfoVect1LinkObjId="g_3362660_0" Pin1InfoVect2LinkObjId="g_2485b70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-889 4725,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23bcf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-909 4764,-909 4764,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3362660@0" ObjectIDND1="22864@x" ObjectIDND2="22863@x" ObjectIDZND0="g_2485b70@0" Pin0InfoVect0LinkObjId="g_2485b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3362660_0" Pin1InfoVect1LinkObjId="SW-124403_0" Pin1InfoVect2LinkObjId="SW-124402_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-909 4764,-909 4764,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_403dd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-930 4725,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3362660@0" ObjectIDZND0="22864@x" ObjectIDZND1="22863@x" ObjectIDZND2="g_2485b70@0" Pin0InfoVect0LinkObjId="SW-124403_0" Pin0InfoVect1LinkObjId="SW-124402_0" Pin0InfoVect2LinkObjId="g_2485b70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3362660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-930 4725,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4046390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-909 4725,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3362660@0" ObjectIDND1="g_2485b70@0" ObjectIDZND0="22864@x" ObjectIDZND1="22863@x" Pin0InfoVect0LinkObjId="SW-124403_0" Pin0InfoVect1LinkObjId="SW-124402_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3362660_0" Pin1InfoVect1LinkObjId="g_2485b70_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-909 4725,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32772b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4039,-887 4029,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22851@0" ObjectIDZND0="g_3670410@0" Pin0InfoVect0LinkObjId="g_3670410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4039,-887 4029,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3574390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-1029 4123,-1036 4091,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3fbdb40@0" ObjectIDZND0="g_3308190@0" ObjectIDZND1="22852@x" ObjectIDZND2="22850@x" Pin0InfoVect0LinkObjId="g_3308190_0" Pin0InfoVect1LinkObjId="SW-124389_0" Pin0InfoVect2LinkObjId="SW-124387_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fbdb40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-1029 4123,-1036 4091,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24956e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-476 3727,-486 3712,-486 3712,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2353ad0@0" ObjectIDZND0="22859@0" Pin0InfoVect0LinkObjId="SW-124397_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2353ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-476 3727,-486 3712,-486 3712,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33a7070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-565 3712,-565 3712,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a0b020@0" ObjectIDZND0="22859@1" Pin0InfoVect0LinkObjId="SW-124397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a0b020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-565 3712,-565 3712,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34b7000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5263,-929 5263,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3f409f0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f409f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5263,-929 5263,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_404ec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5276,-778 5276,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5276,-778 5276,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34c02e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="5522,-930 5522,-838 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3e1adb0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e1adb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5522,-930 5522,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a95700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5263,-982 5263,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3f409f0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f409f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5263,-982 5263,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32aff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-924 5354,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_357e640@1" ObjectIDZND0="g_3347020@0" Pin0InfoVect0LinkObjId="g_3347020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_357e640_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-924 5354,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1dba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5281,-1029 5354,-1029 5354,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5281,-1029 5354,-1029 5354,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_342f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-981 5367,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_357e640@0" ObjectIDZND0="g_3ffb5c0@0" Pin0InfoVect0LinkObjId="g_3ffb5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_357e640_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-981 5367,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32de6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-988 5354,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3ffb5c0@0" ObjectIDZND1="g_357e640@0" Pin0InfoVect0LinkObjId="g_3ffb5c0_0" Pin0InfoVect1LinkObjId="g_357e640_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-988 5354,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_367eb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-973 5354,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_357e640@0" ObjectIDZND0="g_3ffb5c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3ffb5c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_357e640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-973 5354,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f51c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5509,-782 5509,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5509,-782 5509,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae8c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5522,-983 5522,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3e1adb0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e1adb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5522,-983 5522,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3550c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5613,-925 5613,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_3295530@1" ObjectIDZND0="g_3e36e10@0" Pin0InfoVect0LinkObjId="g_3e36e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3295530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5613,-925 5613,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_234ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5540,-1030 5613,-1030 5613,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5540,-1030 5613,-1030 5613,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2642420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5613,-982 5626,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_3295530@0" ObjectIDZND0="g_32ad440@0" Pin0InfoVect0LinkObjId="g_32ad440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3295530_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5613,-982 5626,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f52130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5613,-989 5613,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_32ad440@0" ObjectIDZND1="g_3295530@0" Pin0InfoVect0LinkObjId="g_32ad440_0" Pin0InfoVect1LinkObjId="g_3295530_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5613,-989 5613,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3554a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5613,-974 5613,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3295530@0" ObjectIDZND0="g_32ad440@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_32ad440_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3295530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5613,-974 5613,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32e3b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5754,-753 5754,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5754,-753 5754,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24826f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5754,-822 5754,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_403d070@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_403d070_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5754,-822 5754,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_241d880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5754,-841 5771,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_403d070@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_403d070_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5754,-841 5771,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3362cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5798,-841 5811,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3362f20@0" Pin0InfoVect0LinkObjId="g_3362f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5798,-841 5811,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22c6c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5754,-841 5754,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_403d070@0" Pin0InfoVect0LinkObjId="g_403d070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5754,-841 5754,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33e2090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5754,-919 5754,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_403d070@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_403d070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5754,-919 5754,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fe6ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5754,-979 5754,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5754,-979 5754,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32eb350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5754,-1078 5718,-1078 5718,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_3455b10@0" Pin0InfoVect0LinkObjId="g_3455b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5754,-1078 5718,-1078 5718,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32916e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5754,-1057 5754,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3455b10@0" Pin0InfoVect0LinkObjId="g_3455b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5754,-1057 5754,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34558b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5754,-1078 5754,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_3455b10@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3455b10_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5754,-1078 5754,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_267f770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-773 3825,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22854@1" ObjectIDZND0="22844@0" Pin0InfoVect0LinkObjId="g_2380c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-773 3825,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23144f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-730 3825,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22853@x" ObjectIDND1="22855@x" ObjectIDZND0="22854@0" Pin0InfoVect0LinkObjId="SW-124392_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-124391_0" Pin1InfoVect1LinkObjId="SW-124393_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-730 3825,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3346960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-716 3825,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22853@1" ObjectIDZND0="22855@x" ObjectIDZND1="22854@x" Pin0InfoVect0LinkObjId="SW-124393_0" Pin0InfoVect1LinkObjId="SW-124392_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-716 3825,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e35720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-680 3825,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22857@x" ObjectIDND1="22856@x" ObjectIDZND0="22853@0" Pin0InfoVect0LinkObjId="SW-124391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-124395_0" Pin1InfoVect1LinkObjId="SW-124394_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-680 3825,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3347350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-670 3825,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22856@1" ObjectIDZND0="22853@x" ObjectIDZND1="22857@x" Pin0InfoVect0LinkObjId="SW-124391_0" Pin0InfoVect1LinkObjId="SW-124395_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124394_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-670 3825,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ae9440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-584 3825,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22888@1" ObjectIDZND0="22858@x" ObjectIDZND1="22856@x" Pin0InfoVect0LinkObjId="SW-124396_0" Pin0InfoVect1LinkObjId="SW-124394_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-584 3825,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_367f190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-622 3825,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22888@x" ObjectIDND1="22858@x" ObjectIDZND0="22856@0" Pin0InfoVect0LinkObjId="SW-124394_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-124396_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-622 3825,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ee3c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-730 3812,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22853@x" ObjectIDND1="22854@x" ObjectIDZND0="22855@1" Pin0InfoVect0LinkObjId="SW-124393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-124391_0" Pin1InfoVect1LinkObjId="SW-124392_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-730 3812,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ee3e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-730 3761,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22855@0" ObjectIDZND0="g_362f440@0" Pin0InfoVect0LinkObjId="g_362f440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-730 3761,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ee40f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-680 3811,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22853@x" ObjectIDND1="22856@x" ObjectIDZND0="22857@1" Pin0InfoVect0LinkObjId="SW-124395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-124391_0" Pin1InfoVect1LinkObjId="SW-124394_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-680 3811,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_362f1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-680 3760,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22857@0" ObjectIDZND0="g_367e380@0" Pin0InfoVect0LinkObjId="g_367e380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-680 3760,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_361ad70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-622 3811,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22888@x" ObjectIDND1="22856@x" ObjectIDZND0="22858@0" Pin0InfoVect0LinkObjId="SW-124396_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-124394_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-622 3811,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_361afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-622 3762,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22858@1" ObjectIDZND0="g_361b230@0" Pin0InfoVect0LinkObjId="g_361b230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-622 3762,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e1d980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-435 3823,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22862@0" ObjectIDZND0="22860@1" Pin0InfoVect0LinkObjId="SW-124398_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-435 3823,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e1dbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-396 3823,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22860@0" ObjectIDZND0="22861@1" Pin0InfoVect0LinkObjId="SW-124399_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-396 3823,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e1de40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-349 3823,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22861@0" ObjectIDZND0="22845@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124399_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-349 3823,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e1e0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-490 3863,-490 3863,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22888@x" ObjectIDND1="22862@x" ObjectIDZND0="g_2afb090@0" Pin0InfoVect0LinkObjId="g_2afb090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-124400_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-490 3863,-490 3863,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afabd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-503 3823,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22888@0" ObjectIDZND0="g_2afb090@0" ObjectIDZND1="22862@x" Pin0InfoVect0LinkObjId="g_2afb090_0" Pin0InfoVect1LinkObjId="SW-124400_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-503 3823,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-490 3823,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22888@x" ObjectIDND1="g_2afb090@0" ObjectIDZND0="22862@1" Pin0InfoVect0LinkObjId="SW-124400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="g_2afb090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-490 3823,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_349dd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-288 3729,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22868@0" ObjectIDZND0="22867@1" Pin0InfoVect0LinkObjId="SW-124408_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-288 3729,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e97330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-249 3729,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22867@0" ObjectIDZND0="22869@1" Pin0InfoVect0LinkObjId="SW-124410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-249 3729,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ee2dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-202 3729,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="22869@0" ObjectIDZND0="22870@x" ObjectIDZND1="g_3ee3280@0" ObjectIDZND2="g_3582930@0" Pin0InfoVect0LinkObjId="SW-124411_0" Pin0InfoVect1LinkObjId="g_3ee3280_0" Pin0InfoVect2LinkObjId="g_3582930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-202 3729,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ee3020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-189 3759,-189 3759,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22870@x" ObjectIDND1="g_3582930@0" ObjectIDND2="22869@x" ObjectIDZND0="g_3ee3280@0" Pin0InfoVect0LinkObjId="g_3ee3280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-124411_0" Pin1InfoVect1LinkObjId="g_3582930_0" Pin1InfoVect2LinkObjId="SW-124410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-189 3759,-189 3759,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3583350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-173 3729,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3582930@1" ObjectIDZND0="22870@x" ObjectIDZND1="g_3ee3280@0" ObjectIDZND2="22869@x" Pin0InfoVect0LinkObjId="SW-124411_0" Pin0InfoVect1LinkObjId="g_3ee3280_0" Pin0InfoVect2LinkObjId="SW-124410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3582930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-173 3729,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35835b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-97 3708,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_3582930@0" ObjectIDND1="43281@x" ObjectIDZND0="g_2606d50@0" Pin0InfoVect0LinkObjId="g_2606d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3582930_0" Pin1InfoVect1LinkObjId="SM-CX_DZS.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-97 3708,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2606890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-81 3729,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43281@0" ObjectIDZND0="g_2606d50@0" ObjectIDZND1="g_3582930@0" Pin0InfoVect0LinkObjId="g_2606d50_0" Pin0InfoVect1LinkObjId="g_3582930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_DZS.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-81 3729,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2606af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-97 3729,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_2606d50@0" ObjectIDND1="43281@x" ObjectIDZND0="g_3582930@0" Pin0InfoVect0LinkObjId="g_3582930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2606d50_0" Pin1InfoVect1LinkObjId="SM-CX_DZS.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-97 3729,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fb4af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-188 3911,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_35ca5c0@0" ObjectIDND1="g_3ee1be0@0" ObjectIDND2="22873@x" ObjectIDZND0="22874@1" Pin0InfoVect0LinkObjId="SW-124416_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35ca5c0_0" Pin1InfoVect1LinkObjId="g_3ee1be0_0" Pin1InfoVect2LinkObjId="SW-124415_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-188 3911,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fb4ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-188 3865,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22874@0" ObjectIDZND0="g_3454140@0" Pin0InfoVect0LinkObjId="g_3454140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3875,-188 3865,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d7680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-287 3929,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22872@0" ObjectIDZND0="22871@1" Pin0InfoVect0LinkObjId="SW-124413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-287 3929,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ee14c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-248 3929,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22871@0" ObjectIDZND0="22873@1" Pin0InfoVect0LinkObjId="SW-124415_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124413_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-248 3929,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ee1720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-201 3929,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="22873@0" ObjectIDZND0="22874@x" ObjectIDZND1="g_35ca5c0@0" ObjectIDZND2="g_3ee1be0@0" Pin0InfoVect0LinkObjId="SW-124416_0" Pin0InfoVect1LinkObjId="g_35ca5c0_0" Pin0InfoVect2LinkObjId="g_3ee1be0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-201 3929,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ee1980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-188 3959,-188 3959,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22874@x" ObjectIDND1="g_35ca5c0@0" ObjectIDND2="22873@x" ObjectIDZND0="g_3ee1be0@0" Pin0InfoVect0LinkObjId="g_3ee1be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-124416_0" Pin1InfoVect1LinkObjId="g_35ca5c0_0" Pin1InfoVect2LinkObjId="SW-124415_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-188 3959,-188 3959,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35cafe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-172 3929,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_35ca5c0@1" ObjectIDZND0="22874@x" ObjectIDZND1="g_3ee1be0@0" ObjectIDZND2="22873@x" Pin0InfoVect0LinkObjId="SW-124416_0" Pin0InfoVect1LinkObjId="g_3ee1be0_0" Pin0InfoVect2LinkObjId="SW-124415_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35ca5c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-172 3929,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35cb240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-96 3908,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_35ca5c0@0" ObjectIDND1="43282@x" ObjectIDZND0="g_35cb960@0" Pin0InfoVect0LinkObjId="g_35cb960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35ca5c0_0" Pin1InfoVect1LinkObjId="SM-CX_DZS.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-96 3908,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35cb4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-80 3929,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43282@0" ObjectIDZND0="g_35cb960@0" ObjectIDZND1="g_35ca5c0@0" Pin0InfoVect0LinkObjId="g_35cb960_0" Pin0InfoVect1LinkObjId="g_35ca5c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_DZS.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-80 3929,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35cb700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-96 3929,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_35cb960@0" ObjectIDND1="43282@x" ObjectIDZND0="g_35ca5c0@0" Pin0InfoVect0LinkObjId="g_35ca5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35cb960_0" Pin1InfoVect1LinkObjId="SM-CX_DZS.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-96 3929,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_346bd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-334 3929,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22845@0" ObjectIDZND0="22872@1" Pin0InfoVect0LinkObjId="SW-124414_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e1de40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-334 3929,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a1dfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-189 4112,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3d12340@0" ObjectIDND1="g_3433f00@0" ObjectIDND2="22877@x" ObjectIDZND0="22878@1" Pin0InfoVect0LinkObjId="SW-124421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d12340_0" Pin1InfoVect1LinkObjId="g_3433f00_0" Pin1InfoVect2LinkObjId="SW-124420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-189 4112,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a1e200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-189 4066,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22878@0" ObjectIDZND0="g_46b66e0@0" Pin0InfoVect0LinkObjId="g_46b66e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-189 4066,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f5f8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-288 4130,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22876@0" ObjectIDZND0="22875@1" Pin0InfoVect0LinkObjId="SW-124418_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-288 4130,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34337e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-249 4130,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22875@0" ObjectIDZND0="22877@1" Pin0InfoVect0LinkObjId="SW-124420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-249 4130,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3433a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-202 4130,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="22877@0" ObjectIDZND0="22878@x" ObjectIDZND1="g_3d12340@0" ObjectIDZND2="g_3433f00@0" Pin0InfoVect0LinkObjId="SW-124421_0" Pin0InfoVect1LinkObjId="g_3d12340_0" Pin0InfoVect2LinkObjId="g_3433f00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-202 4130,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3433ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-189 4160,-189 4160,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22878@x" ObjectIDND1="g_3d12340@0" ObjectIDND2="22877@x" ObjectIDZND0="g_3433f00@0" Pin0InfoVect0LinkObjId="g_3433f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-124421_0" Pin1InfoVect1LinkObjId="g_3d12340_0" Pin1InfoVect2LinkObjId="SW-124420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-189 4160,-189 4160,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d12d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-173 4130,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3d12340@1" ObjectIDZND0="22878@x" ObjectIDZND1="g_3433f00@0" ObjectIDZND2="22877@x" Pin0InfoVect0LinkObjId="SW-124421_0" Pin0InfoVect1LinkObjId="g_3433f00_0" Pin0InfoVect2LinkObjId="SW-124420_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d12340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-173 4130,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d12fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-334 4130,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22845@0" ObjectIDZND0="22876@1" Pin0InfoVect0LinkObjId="SW-124419_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e1de40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-334 4130,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b43c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-101 4130,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b43eb0@0" ObjectIDZND0="g_3d12340@0" Pin0InfoVect0LinkObjId="g_3d12340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b43eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-101 4130,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2341d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,23 4300,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_35e1bf0@1" ObjectIDZND0="g_35e1160@0" Pin0InfoVect0LinkObjId="g_35e1160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e1bf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,23 4300,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2341fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-186 4318,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3cbe4e0@0" ObjectIDND1="g_23449d0@0" ObjectIDND2="22881@x" ObjectIDZND0="22882@1" Pin0InfoVect0LinkObjId="SW-124426_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3cbe4e0_0" Pin1InfoVect1LinkObjId="g_23449d0_0" Pin1InfoVect2LinkObjId="SW-124425_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-186 4318,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2342210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4282,-186 4272,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22882@0" ObjectIDZND0="g_41ccb50@0" Pin0InfoVect0LinkObjId="g_41ccb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4282,-186 4272,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41c49f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-285 4336,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22880@0" ObjectIDZND0="22879@1" Pin0InfoVect0LinkObjId="SW-124423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124424_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-285 4336,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbddc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-246 4336,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22879@0" ObjectIDZND0="22881@1" Pin0InfoVect0LinkObjId="SW-124425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-246 4336,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbe020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-199 4336,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="22881@0" ObjectIDZND0="22882@x" ObjectIDZND1="g_3cbe4e0@0" ObjectIDZND2="g_23449d0@0" Pin0InfoVect0LinkObjId="SW-124426_0" Pin0InfoVect1LinkObjId="g_3cbe4e0_0" Pin0InfoVect2LinkObjId="g_23449d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-199 4336,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbe280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-186 4366,-186 4366,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22882@x" ObjectIDND1="g_3cbe4e0@0" ObjectIDND2="22881@x" ObjectIDZND0="g_23449d0@0" Pin0InfoVect0LinkObjId="g_23449d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-124426_0" Pin1InfoVect1LinkObjId="g_3cbe4e0_0" Pin1InfoVect2LinkObjId="SW-124425_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-186 4366,-186 4366,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbef00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-170 4336,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3cbe4e0@1" ObjectIDZND0="22882@x" ObjectIDZND1="g_23449d0@0" ObjectIDZND2="22881@x" Pin0InfoVect0LinkObjId="SW-124426_0" Pin0InfoVect1LinkObjId="g_23449d0_0" Pin0InfoVect2LinkObjId="SW-124425_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cbe4e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-170 4336,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbf160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-334 4336,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22845@0" ObjectIDZND0="22880@1" Pin0InfoVect0LinkObjId="SW-124424_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e1de40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-334 4336,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbf3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-98 4336,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3cbe4e0@0" Pin0InfoVect0LinkObjId="g_3cbe4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-98 4336,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e10900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-38 4289,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_35e1bf0@0" ObjectIDND1="22883@x" ObjectIDZND0="g_3e118b0@0" Pin0InfoVect0LinkObjId="g_3e118b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35e1bf0_0" Pin1InfoVect1LinkObjId="SW-124427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-38 4289,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e113f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-50 4300,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22883@0" ObjectIDZND0="g_35e1bf0@0" ObjectIDZND1="g_3e118b0@0" Pin0InfoVect0LinkObjId="g_35e1bf0_0" Pin0InfoVect1LinkObjId="g_3e118b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-50 4300,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e11650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-38 4300,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3e118b0@0" ObjectIDND1="22883@x" ObjectIDZND0="g_35e1bf0@0" Pin0InfoVect0LinkObjId="g_35e1bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e118b0_0" Pin1InfoVect1LinkObjId="SW-124427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-38 4300,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2344770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4316,-86 4300,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="22883@1" Pin0InfoVect0LinkObjId="SW-124427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4316,-86 4300,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbd2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-334 3616,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22845@0" ObjectIDZND0="22866@0" Pin0InfoVect0LinkObjId="SW-124406_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e1de40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-334 3616,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348c9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-400 3653,-400 3653,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_35c99b0@0" ObjectIDND1="22866@x" ObjectIDZND0="g_348d6e0@0" Pin0InfoVect0LinkObjId="g_348d6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35c99b0_0" Pin1InfoVect1LinkObjId="SW-124406_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-400 3653,-400 3653,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348d480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-386 3616,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22866@1" ObjectIDZND0="g_348d6e0@0" ObjectIDZND1="g_35c99b0@0" Pin0InfoVect0LinkObjId="g_348d6e0_0" Pin0InfoVect1LinkObjId="g_35c99b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-386 3616,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a19290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4466,-334 4466,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22845@0" ObjectIDZND0="22884@1" Pin0InfoVect0LinkObjId="SW-124429_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e1de40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4466,-334 4466,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ca2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-400 3616,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_348d6e0@0" ObjectIDND1="22866@x" ObjectIDZND0="g_35c99b0@0" Pin0InfoVect0LinkObjId="g_35c99b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_348d6e0_0" Pin1InfoVect1LinkObjId="SW-124406_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-400 3616,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fbc640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-445 3616,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_35c99b0@1" ObjectIDZND0="g_35c66c0@0" Pin0InfoVect0LinkObjId="g_35c66c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35c99b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-445 3616,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fbe5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4075,-887 4092,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22851@1" ObjectIDZND0="22847@x" ObjectIDZND1="22850@x" Pin0InfoVect0LinkObjId="SW-124384_0" Pin0InfoVect1LinkObjId="SW-124387_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4075,-887 4092,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fbe810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4074,-839 4091,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22849@1" ObjectIDZND0="22847@x" ObjectIDZND1="22848@x" Pin0InfoVect0LinkObjId="SW-124384_0" Pin0InfoVect1LinkObjId="SW-124385_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4074,-839 4091,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fbf300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-839 4091,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22849@x" ObjectIDND1="22847@x" ObjectIDZND0="22848@1" Pin0InfoVect0LinkObjId="SW-124385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-124386_0" Pin1InfoVect1LinkObjId="SW-124384_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-839 4091,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fbf560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-1036 4091,-1019 4052,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3fbdb40@0" ObjectIDND1="22852@x" ObjectIDND2="22850@x" ObjectIDZND0="g_3308190@0" Pin0InfoVect0LinkObjId="g_3308190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3fbdb40_0" Pin1InfoVect1LinkObjId="SW-124389_0" Pin1InfoVect2LinkObjId="SW-124387_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-1036 4091,-1019 4052,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fbf7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-848 4091,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22847@0" ObjectIDZND0="22849@x" ObjectIDZND1="22848@x" Pin0InfoVect0LinkObjId="SW-124386_0" Pin0InfoVect1LinkObjId="SW-124385_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-848 4091,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fc02b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-887 4091,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22851@x" ObjectIDND1="22850@x" ObjectIDZND0="22847@1" Pin0InfoVect0LinkObjId="SW-124384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-124388_0" Pin1InfoVect1LinkObjId="SW-124387_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-887 4091,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fc0510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-899 4091,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22850@0" ObjectIDZND0="22851@x" ObjectIDZND1="22847@x" Pin0InfoVect0LinkObjId="SW-124388_0" Pin0InfoVect1LinkObjId="SW-124384_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-899 4091,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22c1bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4073,-945 4091,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="22852@0" ObjectIDZND0="22850@x" ObjectIDZND1="g_3fbdb40@0" ObjectIDZND2="g_3308190@0" Pin0InfoVect0LinkObjId="SW-124387_0" Pin0InfoVect1LinkObjId="g_3fbdb40_0" Pin0InfoVect2LinkObjId="g_3308190_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4073,-945 4091,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22c1e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-945 4091,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="22852@x" ObjectIDND1="g_3fbdb40@0" ObjectIDND2="g_3308190@0" ObjectIDZND0="22850@1" Pin0InfoVect0LinkObjId="SW-124387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-124389_0" Pin1InfoVect1LinkObjId="g_3fbdb40_0" Pin1InfoVect2LinkObjId="g_3308190_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-945 4091,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_237ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4707,-813 4725,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="22865@0" ObjectIDZND0="22863@x" ObjectIDZND1="22844@0" Pin0InfoVect0LinkObjId="SW-124402_0" Pin0InfoVect1LinkObjId="g_267f770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4707,-813 4725,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2380a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-836 4725,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="22863@0" ObjectIDZND0="22865@x" ObjectIDZND1="22844@0" Pin0InfoVect0LinkObjId="SW-124404_0" Pin0InfoVect1LinkObjId="g_267f770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-836 4725,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2380c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-813 4725,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="22865@x" ObjectIDND1="22863@x" ObjectIDZND0="22844@0" Pin0InfoVect0LinkObjId="g_267f770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-124404_0" Pin1InfoVect1LinkObjId="SW-124402_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-813 4725,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2380ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-562 4606,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="46170@x" ObjectIDND1="46147@x" ObjectIDZND0="g_2381390@0" Pin0InfoVect0LinkObjId="g_2381390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2381130_0" Pin1InfoVect1LinkObjId="SW-297669_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-562 4606,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2381130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-562 4661,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2381390@0" ObjectIDND1="46147@x" ObjectIDZND0="46170@x" Pin0InfoVect0LinkObjId="g_24fe2c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2381390_0" Pin1InfoVect1LinkObjId="SW-297669_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-562 4661,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23827a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-473 4564,-483 4549,-483 4549,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2382c70@0" ObjectIDZND0="46147@0" Pin0InfoVect0LinkObjId="SW-297669_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2382c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-473 4564,-483 4549,-483 4549,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2382a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-562 4549,-562 4549,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="46170@x" ObjectIDND1="g_2381390@0" ObjectIDZND0="46147@1" Pin0InfoVect0LinkObjId="SW-297669_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2381130_0" Pin1InfoVect1LinkObjId="g_2381390_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-562 4549,-562 4549,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fdd600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-726 4598,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46143@0" ObjectIDZND0="g_3fddac0@0" Pin0InfoVect0LinkObjId="g_3fddac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-726 4598,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fdd860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-677 4597,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46145@0" ObjectIDZND0="g_3fde550@0" Pin0InfoVect0LinkObjId="g_3fde550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-677 4597,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23d4230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-619 4599,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46146@1" ObjectIDZND0="g_23d4490@0" Pin0InfoVect0LinkObjId="g_23d4490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297668_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-619 4599,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22eb5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-313 4604,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22886@1" ObjectIDZND0="46140@0" Pin0InfoVect0LinkObjId="g_3fce620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-313 4604,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22eb850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4466,-276 4466,-238 4525,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22884@0" ObjectIDZND0="22885@1" Pin0InfoVect0LinkObjId="SW-124430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124429_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4466,-276 4466,-238 4525,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ebab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-277 4604,-238 4549,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22886@0" ObjectIDZND0="22885@0" Pin0InfoVect0LinkObjId="SW-124430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-277 4604,-238 4549,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_239d6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-430 5084,-430 5084,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3e091c0@0" ObjectIDND1="46153@x" ObjectIDND2="46151@x" ObjectIDZND0="g_239d940@0" Pin0InfoVect0LinkObjId="g_239d940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e091c0_0" Pin1InfoVect1LinkObjId="SW-297675_0" Pin1InfoVect2LinkObjId="SW-297674_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-430 5084,-430 5084,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e09ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-430 5047,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_239d940@0" ObjectIDND1="46153@x" ObjectIDND2="46151@x" ObjectIDZND0="g_3e091c0@0" Pin0InfoVect0LinkObjId="g_3e091c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_239d940_0" Pin1InfoVect1LinkObjId="SW-297675_0" Pin1InfoVect2LinkObjId="SW-297674_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-430 5047,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e09d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-475 5047,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3e091c0@1" ObjectIDZND0="g_239e6f0@0" Pin0InfoVect0LinkObjId="g_239e6f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e091c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-475 5047,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4022650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4866,27 4866,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_3e0e230@1" ObjectIDZND0="g_3e0d7a0@0" Pin0InfoVect0LinkObjId="g_3e0d7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e0e230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4866,27 4866,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4027ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-94 4902,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_4027180@0" Pin0InfoVect0LinkObjId="g_4027180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-94 4902,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fc17d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4866,-34 4855,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="46163@x" ObjectIDND1="g_3e0e230@0" ObjectIDZND0="g_3fc1ef0@0" Pin0InfoVect0LinkObjId="g_3fc1ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297687_0" Pin1InfoVect1LinkObjId="g_3e0e230_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4866,-34 4855,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fc1a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4866,-46 4866,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46163@0" ObjectIDZND0="g_3fc1ef0@0" ObjectIDZND1="g_3e0e230@0" Pin0InfoVect0LinkObjId="g_3fc1ef0_0" Pin0InfoVect1LinkObjId="g_3e0e230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297687_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4866,-46 4866,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fc1c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4866,-34 4866,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3fc1ef0@0" ObjectIDND1="46163@x" ObjectIDZND0="g_3e0e230@0" Pin0InfoVect0LinkObjId="g_3e0e230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3fc1ef0_0" Pin1InfoVect1LinkObjId="SW-297687_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4866,-34 4866,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fc2ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-82 4866,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="46163@1" Pin0InfoVect0LinkObjId="SW-297687_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-82 4866,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fc4bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5109,-270 5099,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46166@0" ObjectIDZND0="g_3fc9e70@0" Pin0InfoVect0LinkObjId="g_3fc9e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297691_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5109,-270 5099,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d01a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-90 5142,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_35cf780@0" ObjectIDND1="47294@x" ObjectIDZND0="g_35d08c0@0" Pin0InfoVect0LinkObjId="g_35d08c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35cf780_0" Pin1InfoVect1LinkObjId="SM-CX_DZS.CX_DZS_P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-90 5142,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d0400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-74 5163,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47294@0" ObjectIDZND0="g_35cf780@0" ObjectIDZND1="g_35d08c0@0" Pin0InfoVect0LinkObjId="g_35cf780_0" Pin0InfoVect1LinkObjId="g_35d08c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_DZS.CX_DZS_P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-74 5163,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d0660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-90 5163,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="47294@x" ObjectIDND1="g_35d08c0@0" ObjectIDZND0="g_35cf780@0" Pin0InfoVect0LinkObjId="g_35cf780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_DZS.CX_DZS_P1_0" Pin1InfoVect1LinkObjId="g_35d08c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-90 5163,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d1670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-336 5163,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46140@0" ObjectIDZND0="46165@1" Pin0InfoVect0LinkObjId="SW-297690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22eb5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-336 5163,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d3400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5289,-273 5279,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46169@0" ObjectIDZND0="g_41b49b0@0" Pin0InfoVect0LinkObjId="g_41b49b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5289,-273 5279,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41b8c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5343,-91 5322,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_41b8240@0" ObjectIDND1="47295@x" ObjectIDZND0="g_41b9380@0" Pin0InfoVect0LinkObjId="g_41b9380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41b8240_0" Pin1InfoVect1LinkObjId="SM-CX_DZS.CX_DZS_P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5343,-91 5322,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41b8ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5343,-75 5343,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47295@0" ObjectIDZND0="g_41b8240@0" ObjectIDZND1="g_41b9380@0" Pin0InfoVect0LinkObjId="g_41b8240_0" Pin0InfoVect1LinkObjId="g_41b9380_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_DZS.CX_DZS_P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5343,-75 5343,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41b9120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5343,-91 5343,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="47295@x" ObjectIDND1="g_41b9380@0" ObjectIDZND0="g_41b8240@0" Pin0InfoVect0LinkObjId="g_41b8240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_DZS.CX_DZS_P2_0" Pin1InfoVect1LinkObjId="g_41b9380_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5343,-91 5343,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41ba130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5343,-336 5343,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46140@0" ObjectIDZND0="46168@1" Pin0InfoVect0LinkObjId="SW-297694_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22eb5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5343,-336 5343,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_41bb980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5469,-268 5459,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_22cbae0@0" Pin0InfoVect0LinkObjId="g_22cbae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5469,-268 5459,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22cfd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-87 5502,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_22cf350@0" ObjectIDND1="0@x" ObjectIDZND0="g_22d0490@0" Pin0InfoVect0LinkObjId="g_22d0490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22cf350_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-87 5502,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22cffd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-71 5523,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_22cf350@0" ObjectIDZND1="g_22d0490@0" Pin0InfoVect0LinkObjId="g_22cf350_0" Pin0InfoVect1LinkObjId="g_22d0490_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-71 5523,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22d0230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-87 5523,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_22d0490@0" ObjectIDZND0="g_22cf350@0" Pin0InfoVect0LinkObjId="g_22cf350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_22d0490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-87 5523,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3faa990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5658,-271 5648,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3fafe60@0" Pin0InfoVect0LinkObjId="g_3fafe60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5658,-271 5648,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fcb920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5712,-91 5691,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_3fb36d0@0" ObjectIDND1="0@x" ObjectIDZND0="g_3fcc040@0" Pin0InfoVect0LinkObjId="g_3fcc040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3fb36d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5712,-91 5691,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fcbb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5712,-75 5712,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3fb36d0@0" ObjectIDZND1="g_3fcc040@0" Pin0InfoVect0LinkObjId="g_3fb36d0_0" Pin0InfoVect1LinkObjId="g_3fcc040_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5712,-75 5712,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fcbde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5712,-91 5712,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_3fcc040@0" ObjectIDZND0="g_3fb36d0@0" Pin0InfoVect0LinkObjId="g_3fb36d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3fcc040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5712,-91 5712,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fccdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5712,-336 5712,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46140@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22eb5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5712,-336 5712,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fce620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-314 5523,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="46140@0" Pin0InfoVect0LinkObjId="g_22eb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-314 5523,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fcf640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4598,-399 4583,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46150@0" ObjectIDZND0="g_239a450@0" Pin0InfoVect0LinkObjId="g_239a450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4598,-399 4583,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fcfd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-317 4902,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46161@1" ObjectIDZND0="46140@0" Pin0InfoVect0LinkObjId="g_22eb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297685_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-317 4902,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fd0380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-89 4642,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46158@0" ObjectIDZND0="g_3fd55c0@0" Pin0InfoVect0LinkObjId="g_3fd55c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-89 4642,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ff8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-309 4706,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46155@1" ObjectIDZND0="46140@0" Pin0InfoVect0LinkObjId="g_22eb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-309 4706,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2600de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4688,-89 4706,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46158@1" ObjectIDZND0="46156@x" ObjectIDZND1="g_26004e0@0" ObjectIDZND2="g_25fded0@0" Pin0InfoVect0LinkObjId="SW-297680_0" Pin0InfoVect1LinkObjId="g_26004e0_0" Pin0InfoVect2LinkObjId="g_25fded0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4688,-89 4706,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2601040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-102 4706,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46156@0" ObjectIDZND0="46158@x" ObjectIDZND1="g_26004e0@0" ObjectIDZND2="g_25fded0@0" Pin0InfoVect0LinkObjId="SW-297681_0" Pin0InfoVect1LinkObjId="g_26004e0_0" Pin0InfoVect2LinkObjId="g_25fded0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-102 4706,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26012a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,49 4706,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_25fec00@0" Pin0InfoVect0LinkObjId="g_25fec00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,49 4706,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2604a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-267 4640,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46157@0" ObjectIDZND0="g_2603fb0@0" Pin0InfoVect0LinkObjId="g_2603fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-267 4640,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2604ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4686,-267 4706,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46157@1" ObjectIDZND0="46154@x" ObjectIDZND1="46155@x" Pin0InfoVect0LinkObjId="SW-297677_0" Pin0InfoVect1LinkObjId="SW-297678_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297679_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4686,-267 4706,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2604f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-273 4706,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46155@0" ObjectIDZND0="46154@x" ObjectIDZND1="46157@x" Pin0InfoVect0LinkObjId="SW-297677_0" Pin0InfoVect1LinkObjId="SW-297679_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297678_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-273 4706,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2605160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-267 4706,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46157@x" ObjectIDND1="46155@x" ObjectIDZND0="46154@1" Pin0InfoVect0LinkObjId="SW-297677_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297679_0" Pin1InfoVect1LinkObjId="SW-297678_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-267 4706,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea0730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-267 4839,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46162@0" ObjectIDZND0="g_3e9fca0@0" Pin0InfoVect0LinkObjId="g_3e9fca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297686_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-267 4839,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea0990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-62 4670,-71 4706,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26004e0@1" ObjectIDZND0="46158@x" ObjectIDZND1="46156@x" ObjectIDZND2="g_25fded0@0" Pin0InfoVect0LinkObjId="SW-297681_0" Pin0InfoVect1LinkObjId="SW-297680_0" Pin0InfoVect2LinkObjId="g_25fded0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26004e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-62 4670,-71 4706,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea0bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-89 4706,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="46158@x" ObjectIDND1="46156@x" ObjectIDZND0="g_26004e0@0" ObjectIDZND1="g_25fded0@0" ObjectIDZND2="46159@x" Pin0InfoVect0LinkObjId="g_26004e0_0" Pin0InfoVect1LinkObjId="g_25fded0_0" Pin0InfoVect2LinkObjId="SW-297682_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297681_0" Pin1InfoVect1LinkObjId="SW-297680_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-89 4706,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea0e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-71 4706,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_26004e0@0" ObjectIDND1="46158@x" ObjectIDND2="46156@x" ObjectIDZND0="46159@1" Pin0InfoVect0LinkObjId="SW-297682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26004e0_0" Pin1InfoVect1LinkObjId="SW-297681_0" Pin1InfoVect2LinkObjId="SW-297680_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-71 4706,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea10b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-18 4670,-2 4706,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="reactance" EndDevType1="breaker" ObjectIDND0="g_26004e0@0" ObjectIDZND0="0@x" ObjectIDZND1="46159@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-297682_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26004e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-18 4670,-2 4706,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea1310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-23 4706,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="reactance" ObjectIDND0="46159@0" ObjectIDZND0="g_26004e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_26004e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-23 4706,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea1570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-2 4706,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="reactance" ObjectIDND0="g_26004e0@0" ObjectIDND1="46159@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26004e0_0" Pin1InfoVect1LinkObjId="SW-297682_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-2 4706,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea17d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-71 4739,-71 4739,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_26004e0@0" ObjectIDND1="46158@x" ObjectIDND2="46156@x" ObjectIDZND0="g_25fded0@0" Pin0InfoVect0LinkObjId="g_25fded0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26004e0_0" Pin1InfoVect1LinkObjId="SW-297681_0" Pin1InfoVect2LinkObjId="SW-297680_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-71 4739,-71 4739,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea4160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-149 4706,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3ea3740@0" ObjectIDZND0="46156@1" Pin0InfoVect0LinkObjId="SW-297680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ea3740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-149 4706,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea43c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4746,-206 4746,-213 4706,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_3ea2cc0@0" ObjectIDZND0="46154@x" ObjectIDZND1="g_3ea3740@0" Pin0InfoVect0LinkObjId="SW-297677_0" Pin0InfoVect1LinkObjId="g_3ea3740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ea2cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4746,-206 4746,-213 4706,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea4620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-234 4706,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46154@0" ObjectIDZND0="g_3ea2cc0@0" ObjectIDZND1="g_3ea3740@0" Pin0InfoVect0LinkObjId="g_3ea2cc0_0" Pin0InfoVect1LinkObjId="g_3ea3740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-234 4706,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea4880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-213 4706,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_3ea2cc0@0" ObjectIDND1="46154@x" ObjectIDZND0="g_3ea3740@1" Pin0InfoVect0LinkObjId="g_3ea3740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ea2cc0_0" Pin1InfoVect1LinkObjId="SW-297677_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-213 4706,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb06c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5374,-196 5374,-205 5343,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_41b7510@0" ObjectIDZND0="g_41b8240@0" ObjectIDZND1="46167@x" Pin0InfoVect0LinkObjId="g_41b8240_0" Pin0InfoVect1LinkObjId="SW-297693_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41b7510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5374,-196 5374,-205 5343,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb1030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5343,-231 5343,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46167@0" ObjectIDZND0="g_41b7510@0" ObjectIDZND1="g_41b8240@0" Pin0InfoVect0LinkObjId="g_41b7510_0" Pin0InfoVect1LinkObjId="g_41b8240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297693_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5343,-231 5343,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb1220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5343,-205 5343,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_41b7510@0" ObjectIDND1="46167@x" ObjectIDZND0="g_41b8240@1" Pin0InfoVect0LinkObjId="g_41b8240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41b7510_0" Pin1InfoVect1LinkObjId="SW-297693_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5343,-205 5343,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb1430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5554,-196 5554,-206 5523,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_22ce620@0" ObjectIDZND0="g_22cf350@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_22cf350_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ce620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5554,-196 5554,-206 5523,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb1ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-226 5523,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_22ce620@0" ObjectIDZND1="g_22cf350@0" Pin0InfoVect0LinkObjId="g_22ce620_0" Pin0InfoVect1LinkObjId="g_22cf350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-226 5523,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb2150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-206 5523,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_22ce620@0" ObjectIDND1="0@x" ObjectIDZND0="g_22cf350@1" Pin0InfoVect0LinkObjId="g_22cf350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22ce620_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-206 5523,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb23b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5744,-199 5744,-210 5712,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_3fb29a0@0" ObjectIDZND0="g_3fb36d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3fb36d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fb29a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5744,-199 5744,-210 5712,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb2ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5712,-230 5712,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3fb29a0@0" ObjectIDZND1="g_3fb36d0@0" Pin0InfoVect0LinkObjId="g_3fb29a0_0" Pin0InfoVect1LinkObjId="g_3fb36d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5712,-230 5712,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb3100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5712,-210 5712,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_3fb29a0@0" ObjectIDND1="0@x" ObjectIDZND0="g_3fb36d0@1" Pin0InfoVect0LinkObjId="g_3fb36d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3fb29a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5712,-210 5712,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb3360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5193,-198 5193,-208 5163,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_35cea50@0" ObjectIDZND0="g_35cf780@0" ObjectIDZND1="46164@x" Pin0InfoVect0LinkObjId="g_35cf780_0" Pin0InfoVect1LinkObjId="SW-297689_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35cea50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5193,-198 5193,-208 5163,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb3e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-226 5163,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46164@0" ObjectIDZND0="g_35cea50@0" ObjectIDZND1="g_35cf780@0" Pin0InfoVect0LinkObjId="g_35cea50_0" Pin0InfoVect1LinkObjId="g_35cf780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297689_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-226 5163,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb40b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-208 5163,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_35cea50@0" ObjectIDND1="46164@x" ObjectIDZND0="g_35cf780@1" Pin0InfoVect0LinkObjId="g_35cf780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35cea50_0" Pin1InfoVect1LinkObjId="SW-297689_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-208 5163,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb4c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-273 5343,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46169@1" ObjectIDZND0="46168@x" ObjectIDZND1="46167@x" Pin0InfoVect0LinkObjId="SW-297694_0" Pin0InfoVect1LinkObjId="SW-297693_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297695_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-273 5343,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb55b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5343,-282 5343,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46168@0" ObjectIDZND0="46169@x" ObjectIDZND1="46167@x" Pin0InfoVect0LinkObjId="SW-297695_0" Pin0InfoVect1LinkObjId="SW-297693_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5343,-282 5343,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb57a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5343,-273 5343,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46169@x" ObjectIDND1="46168@x" ObjectIDZND0="46167@1" Pin0InfoVect0LinkObjId="SW-297693_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297695_0" Pin1InfoVect1LinkObjId="SW-297694_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5343,-273 5343,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb59b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5505,-268 5523,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5505,-268 5523,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb6470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-278 5523,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-278 5523,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb66d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5523,-268 5523,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5523,-268 5523,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb6930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5694,-271 5712,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5694,-271 5712,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4000070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5712,-282 5712,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5712,-282 5712,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40002d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5712,-271 5712,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5712,-271 5712,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4000530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5145,-270 5163,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46166@1" ObjectIDZND0="46165@x" ObjectIDZND1="46164@x" Pin0InfoVect0LinkObjId="SW-297690_0" Pin0InfoVect1LinkObjId="SW-297689_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297691_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5145,-270 5163,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4001000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-281 5163,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46165@0" ObjectIDZND0="46166@x" ObjectIDZND1="46164@x" Pin0InfoVect0LinkObjId="SW-297691_0" Pin0InfoVect1LinkObjId="SW-297689_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-281 5163,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4001260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-270 5163,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46166@x" ObjectIDND1="46165@x" ObjectIDZND0="46164@1" Pin0InfoVect0LinkObjId="SW-297689_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297691_0" Pin1InfoVect1LinkObjId="SW-297690_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-270 5163,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4006e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-412 4981,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46153@0" ObjectIDZND0="g_4006390@0" Pin0InfoVect0LinkObjId="g_4006390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-412 4981,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_400a310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5098,-413 5098,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4007080@0" ObjectIDZND0="46152@1" Pin0InfoVect0LinkObjId="SW-297676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4007080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5098,-413 5098,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_400a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5098,-361 5098,-351 5047,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="46152@0" ObjectIDZND0="46151@x" ObjectIDZND1="46140@0" Pin0InfoVect0LinkObjId="SW-297674_0" Pin0InfoVect1LinkObjId="g_22eb5f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5098,-361 5098,-351 5047,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_400b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-363 5047,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="46151@0" ObjectIDZND0="46152@x" ObjectIDZND1="46140@0" Pin0InfoVect0LinkObjId="SW-297676_0" Pin0InfoVect1LinkObjId="g_22eb5f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297674_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-363 5047,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_400b2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-351 5047,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="46152@x" ObjectIDND1="46151@x" ObjectIDZND0="46140@0" Pin0InfoVect0LinkObjId="g_22eb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297676_0" Pin1InfoVect1LinkObjId="SW-297674_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-351 5047,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_400be90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5032,-412 5047,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46153@1" ObjectIDZND0="46151@x" ObjectIDZND1="g_239d940@0" ObjectIDZND2="g_3e091c0@0" Pin0InfoVect0LinkObjId="SW-297674_0" Pin0InfoVect1LinkObjId="g_239d940_0" Pin0InfoVect2LinkObjId="g_3e091c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297675_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5032,-412 5047,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_400c800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-399 5047,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46151@1" ObjectIDZND0="46153@x" ObjectIDZND1="g_239d940@0" ObjectIDZND2="g_3e091c0@0" Pin0InfoVect0LinkObjId="SW-297675_0" Pin0InfoVect1LinkObjId="g_239d940_0" Pin0InfoVect2LinkObjId="g_3e091c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297674_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-399 5047,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_400c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-412 5047,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46153@x" ObjectIDND1="46151@x" ObjectIDZND0="g_239d940@0" ObjectIDZND1="g_3e091c0@0" Pin0InfoVect0LinkObjId="g_239d940_0" Pin0InfoVect1LinkObjId="g_3e091c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297675_0" Pin1InfoVect1LinkObjId="SW-297674_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-412 5047,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b92a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4885,-267 4902,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46162@1" ObjectIDZND0="46161@x" ObjectIDZND1="46160@x" Pin0InfoVect0LinkObjId="SW-297685_0" Pin0InfoVect1LinkObjId="SW-297684_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297686_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4885,-267 4902,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b9d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-281 4902,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46161@0" ObjectIDZND0="46162@x" ObjectIDZND1="46160@x" Pin0InfoVect0LinkObjId="SW-297686_0" Pin0InfoVect1LinkObjId="SW-297684_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297685_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-281 4902,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b9f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-267 4902,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46162@x" ObjectIDND1="46161@x" ObjectIDZND0="46160@1" Pin0InfoVect0LinkObjId="SW-297684_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297686_0" Pin1InfoVect1LinkObjId="SW-297685_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-267 4902,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ba1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-198 4933,-206 4902,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_3fc2f00@0" ObjectIDZND0="46160@x" ObjectIDZND1="g_4027180@0" Pin0InfoVect0LinkObjId="SW-297684_0" Pin0InfoVect1LinkObjId="g_4027180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fc2f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-198 4933,-206 4902,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bace0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-219 4902,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="46160@0" ObjectIDZND0="g_3fc2f00@0" ObjectIDZND1="g_4027180@0" Pin0InfoVect0LinkObjId="g_3fc2f00_0" Pin0InfoVect1LinkObjId="g_4027180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-219 4902,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35baf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-206 4902,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_3fc2f00@0" ObjectIDND1="46160@x" ObjectIDZND0="g_4027180@1" Pin0InfoVect0LinkObjId="g_4027180_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3fc2f00_0" Pin1InfoVect1LinkObjId="SW-297684_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-206 4902,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35c3c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-944 4091,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="22852@x" ObjectIDND1="22850@x" ObjectIDZND0="g_3fbdb40@0" ObjectIDZND1="g_3308190@0" ObjectIDZND2="34024@1" Pin0InfoVect0LinkObjId="g_3fbdb40_0" Pin0InfoVect1LinkObjId="g_3308190_0" Pin0InfoVect2LinkObjId="g_35c3e80_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-124389_0" Pin1InfoVect1LinkObjId="SW-124387_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-944 4091,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35c3e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4092,-1037 4092,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3fbdb40@0" ObjectIDND1="g_3308190@0" ObjectIDND2="22852@x" ObjectIDZND0="34024@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3fbdb40_0" Pin1InfoVect1LinkObjId="g_3308190_0" Pin1InfoVect2LinkObjId="SW-124389_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4092,-1037 4092,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35c45a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-782 4662,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22844@0" ObjectIDZND0="46142@1" Pin0InfoVect0LinkObjId="SW-297664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_267f770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-782 4662,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35c4d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-726 4662,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46143@1" ObjectIDZND0="46142@x" ObjectIDZND1="46141@x" Pin0InfoVect0LinkObjId="SW-297664_0" Pin0InfoVect1LinkObjId="SW-297663_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-726 4662,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35c5810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-734 4662,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46142@0" ObjectIDZND0="46143@x" ObjectIDZND1="46141@x" Pin0InfoVect0LinkObjId="SW-297665_0" Pin0InfoVect1LinkObjId="SW-297663_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-734 4662,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35c5a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-726 4662,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46143@x" ObjectIDND1="46142@x" ObjectIDZND0="46141@1" Pin0InfoVect0LinkObjId="SW-297663_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297665_0" Pin1InfoVect1LinkObjId="SW-297664_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-726 4662,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35c5cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-677 4662,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46145@1" ObjectIDZND0="46141@x" ObjectIDZND1="46144@x" Pin0InfoVect0LinkObjId="SW-297663_0" Pin0InfoVect1LinkObjId="SW-297666_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297667_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-677 4662,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24fd0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-686 4662,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46141@0" ObjectIDZND0="46145@x" ObjectIDZND1="46144@x" Pin0InfoVect0LinkObjId="SW-297667_0" Pin0InfoVect1LinkObjId="SW-297666_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297663_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-686 4662,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24fd310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-677 4662,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="46145@x" ObjectIDND1="46141@x" ObjectIDZND0="46144@1" Pin0InfoVect0LinkObjId="SW-297666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297667_0" Pin1InfoVect1LinkObjId="SW-297663_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-677 4662,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24fd570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-619 4662,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="46146@0" ObjectIDZND0="46144@x" ObjectIDZND1="46170@x" Pin0InfoVect0LinkObjId="SW-297666_0" Pin0InfoVect1LinkObjId="g_2381130_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297668_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-619 4662,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24fe060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-631 4662,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="46144@0" ObjectIDZND0="46146@x" ObjectIDZND1="46170@x" Pin0InfoVect0LinkObjId="SW-297668_0" Pin0InfoVect1LinkObjId="g_2381130_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-631 4662,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24fe2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-619 4662,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="46146@x" ObjectIDND1="46144@x" ObjectIDZND0="46170@1" Pin0InfoVect0LinkObjId="g_2381130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297668_0" Pin1InfoVect1LinkObjId="SW-297666_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-619 4662,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24feaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-349 4660,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46149@0" ObjectIDZND0="46140@0" Pin0InfoVect0LinkObjId="g_22eb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-349 4660,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24fed50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4634,-399 4660,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46150@1" ObjectIDZND0="46148@x" ObjectIDZND1="46149@x" Pin0InfoVect0LinkObjId="SW-297670_0" Pin0InfoVect1LinkObjId="SW-297671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297672_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4634,-399 4660,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ff840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-420 4660,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46148@0" ObjectIDZND0="46150@x" ObjectIDZND1="46149@x" Pin0InfoVect0LinkObjId="SW-297672_0" Pin0InfoVect1LinkObjId="SW-297671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-420 4660,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ffaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-399 4660,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="46150@x" ObjectIDND1="46148@x" ObjectIDZND0="46149@1" Pin0InfoVect0LinkObjId="SW-297671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297672_0" Pin1InfoVect1LinkObjId="SW-297670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-399 4660,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ffd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-476 4735,-485 4660,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="g_22e9ee0@0" ObjectIDZND0="46170@x" ObjectIDZND1="46148@x" Pin0InfoVect0LinkObjId="g_2381130_0" Pin0InfoVect1LinkObjId="SW-297670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e9ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-476 4735,-485 4660,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25007f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-500 4660,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="46170@0" ObjectIDZND0="g_22e9ee0@0" ObjectIDZND1="46148@x" Pin0InfoVect0LinkObjId="g_22e9ee0_0" Pin0InfoVect1LinkObjId="SW-297670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2381130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-500 4660,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2500a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-485 4660,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="g_22e9ee0@0" ObjectIDND1="46170@x" ObjectIDZND0="46148@1" Pin0InfoVect0LinkObjId="SW-297670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22e9ee0_0" Pin1InfoVect1LinkObjId="g_2381130_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-485 4660,-447 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22845" cx="3729" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22845" cx="3823" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22845" cx="3929" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22845" cx="4130" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22845" cx="4336" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22845" cx="3616" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22844" cx="4091" cy="-782" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22844" cx="3825" cy="-782" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22844" cx="4725" cy="-782" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5754" cy="-753" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22845" cx="4466" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46140" cx="4604" cy="-336" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46140" cx="5163" cy="-336" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46140" cx="5343" cy="-336" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46140" cx="5712" cy="-336" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46140" cx="5523" cy="-336" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46140" cx="4902" cy="-336" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46140" cx="4706" cy="-336" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46140" cx="5047" cy="-336" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22844" cx="4662" cy="-782" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46140" cx="4660" cy="-336" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5276" cy="-751" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5509" cy="-753" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-93794" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3435.000000 -999.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19742" ObjectName="DYN-CX_DZS"/>
     <cge:Meas_Ref ObjectId="93794"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35a1fb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4042.000000 -51.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35a1fb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4042.000000 -51.000000) translate(0,33)">      ±12MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f9f710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -808.000000) translate(0,15)">110kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -964.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -964.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -964.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -964.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -964.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -964.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -964.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_35b3930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -1073.500000) translate(0,16)">大中山升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4065.500000 -1077.000000) translate(0,15)">保</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4065.500000 -1077.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4065.500000 -1077.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -645.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -645.000000) translate(0,33)">SZ11-50000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -645.000000) translate(0,51)">115±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -645.000000) translate(0,69)">50000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -645.000000) translate(0,87)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -645.000000) translate(0,105)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3de9ef0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3865.000000 -51.000000) translate(0,15)"> 大中山Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3de9ef0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3865.000000 -51.000000) translate(0,33)">(12-20号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f3da10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5211.000000 -1130.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3a350" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5224.500000 -1108.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ee51e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5724.000000 -1157.000000) translate(0,15)">10kV仓连线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ee51e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5724.000000 -1157.000000) translate(0,33)"> 涧水支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a4d670" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5226.000000 -742.000000) translate(0,15)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24440e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5580.000000 -743.000000) translate(0,15)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3de96d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5162.000000 -1037.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2485e40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5724.500000 -1117.000000) translate(0,15)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fb45b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3663.000000 -51.000000) translate(0,15)">大中山Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fb45b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3663.000000 -51.000000) translate(0,33)">(1-11号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a1b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -485.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fbcad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -452.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fbcad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -452.000000) translate(0,33)"> 电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22c2070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4324.000000 11.000000) translate(0,15)">35kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22c2070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4324.000000 11.000000) translate(0,33)"> 及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22c3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -104.000000) translate(0,15)">1号接地变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22c3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -104.000000) translate(0,33)">DKSC-630/35-315/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22c49f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3270.000000 -126.000000) translate(0,15)">6221900</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e040a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4034.000000 -861.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e04620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -817.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e04a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4034.000000 -913.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e04cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -923.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e04f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -869.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e05140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -939.000000) translate(0,12)">K1517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e05660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -591.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e058e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3779.000000 -371.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e05b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3774.000000 -458.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e05d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3785.000000 -414.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e05fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3769.000000 -756.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e061e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 -762.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e06420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3767.000000 -706.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e06660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 -659.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e068a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 -710.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e06ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -653.000000) translate(0,12)">K1017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e06d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3664.000000 -528.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e06f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -313.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e071a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 -215.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd6740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -227.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd6980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -270.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd6bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -310.000000) translate(0,12)">3541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd6e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -212.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd7040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -224.000000) translate(0,12)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd7280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4345.000000 -267.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd74c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -74.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd7700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -312.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd7b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 -214.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd7dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -226.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd8000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -269.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd8240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -313.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd8480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -215.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd86c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -227.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -270.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd8b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -301.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd8d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -302.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd8fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 -262.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd9200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -319.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd9440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4671.000000 -915.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd9680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4732.000000 -861.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd98c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -844.000000) translate(0,12)">K1900</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd9b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3623.000000 -375.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_41c9b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -719.000000) translate(0,15)">全站检修停电前应挂“全站检修”牌，“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_41c9b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -719.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_41c9b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -719.000000) translate(0,51)">全站检修完工后仅可摘除“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_41c9b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -719.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_41c9b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -719.000000) translate(0,87)">全站检修复电后才可以摘除“全站检修”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e09f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4924.000000 -482.000000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e09f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4924.000000 -482.000000) translate(0,33)"> 电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e0a490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -616.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e0a490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -616.000000) translate(0,33)">SZ18-55000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e0a490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -616.000000) translate(0,51)">115±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e0a490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -616.000000) translate(0,69)">55000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e0a490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -616.000000) translate(0,87)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e0a490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -616.000000) translate(0,105)">Ud%=12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e0a690" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4422.000000 -730.000000) translate(0,15)">110kVGIS组合电器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fc3c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4890.000000 15.000000) translate(0,15)">35kV2号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fc3c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4890.000000 15.000000) translate(0,33)"> 及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fc4170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4945.000000 -98.000000) translate(0,15)">2号接地变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fc4170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4945.000000 -98.000000) translate(0,33)">DKSC-1800/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fc4170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4945.000000 -98.000000) translate(0,51)">-315/0.4GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fc43c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5085.000000 -43.000000) translate(0,15)">   涧水塘Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fc43c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5085.000000 -43.000000) translate(0,33)">(1-4号、9号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35d2ec0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5287.000000 -44.000000) translate(0,15)"> 涧水塘Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35d2ec0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5287.000000 -44.000000) translate(0,33)">(5-8号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fce880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4703.000000 -422.000000) translate(0,15)">全绝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fce880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4703.000000 -422.000000) translate(0,33)">缘管</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fce880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4703.000000 -422.000000) translate(0,51)">母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fcff10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4619.000000 105.000000) translate(0,15)">2号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fcff10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4619.000000 105.000000) translate(0,33)">      ±14MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea1a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4679.000000 -707.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea1f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -436.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea2160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4500.000000 -529.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ea4ae0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5474.000000 -39.000000) translate(0,15)">预留储能备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ea5f30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5661.000000 -40.000000) translate(0,15)">预留储能备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eafa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5356.000000 -252.000000) translate(0,12)">358</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eb0060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5350.000000 -307.000000) translate(0,12)">3582</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eb0480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5287.000000 -299.000000) translate(0,12)">35827</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eb4310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5174.000000 -248.000000) translate(0,12)">357</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eb47c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5170.000000 -306.000000) translate(0,12)">3572</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eb4a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5107.000000 -296.000000) translate(0,12)">35727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40014c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5574.000000 -377.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_400b520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -390.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_400ba10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5105.000000 -386.000000) translate(0,12)">39020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_400bc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -438.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bb1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4911.000000 -240.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bb650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4909.000000 -306.000000) translate(0,12)">3562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bb890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4847.000000 -293.000000) translate(0,12)">35627</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bbad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4823.000000 -71.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bbd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -255.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bbf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 -298.000000) translate(0,12)">3552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bc190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -258.000000) translate(0,12)">35527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bc3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 -127.000000) translate(0,12)">3556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bc610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4650.000000 -115.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bc850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4667.000000 -374.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bca90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4595.000000 -425.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bccd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -758.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bcf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4595.000000 -755.000000) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bd150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -655.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bd390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4594.000000 -702.000000) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bd5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -653.000000) translate(0,12)">K1027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c0c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5540.000000 -245.000000) translate(0,12)">359</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c0e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5533.000000 -300.000000) translate(0,12)">3592</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c1080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5467.000000 -293.000000) translate(0,12)">35927</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c12c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5728.000000 -249.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c1500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5724.000000 -305.000000) translate(0,12)">3612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c1740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5655.000000 -300.000000) translate(0,12)">36127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c37a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4710.000000 -18.000000) translate(0,12)">350</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2500eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3481.000000 -938.000000) translate(0,12)">AGC/AVC</text>
  </g><g id="Line_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-486 3744,-486 3744,-508 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="3744" x2="3744" y1="-565" y2="-544"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5263" x2="5285" y1="-881" y2="-881"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5309" x2="5341" y1="-879" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5348" x2="5368" y1="-879" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5368" x2="5368" y1="-884" y2="-875"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5370" x2="5370" y1="-883" y2="-877"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5372" x2="5372" y1="-882" y2="-877"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5255" x2="5298" y1="-778" y2="-778"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5255" x2="5244" y1="-778" y2="-796"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5249" x2="5305" y1="-789" y2="-789"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5289" x2="5289" y1="-814" y2="-801"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5522" x2="5500" y1="-872" y2="-872"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5476" x2="5444" y1="-870" y2="-870"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5437" x2="5417" y1="-870" y2="-870"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5417" x2="5417" y1="-875" y2="-866"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5415" x2="5415" y1="-874" y2="-868"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5413" x2="5413" y1="-873" y2="-868"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5263" x2="5495" y1="-852" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5495" x2="5495" y1="-852" y2="-850"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5522" x2="5289" y1="-846" y2="-846"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5289" x2="5289" y1="-846" y2="-834"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5262" x2="5262" y1="-814" y2="-801"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5497" x2="5497" y1="-815" y2="-802"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5523" x2="5523" y1="-815" y2="-802"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5495" x2="5495" y1="-842" y2="-835"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5298" x2="5309" y1="-778" y2="-796"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5488" x2="5531" y1="-782" y2="-782"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5488" x2="5477" y1="-782" y2="-800"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5482" x2="5538" y1="-793" y2="-793"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5531" x2="5542" y1="-782" y2="-800"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="3744" x2="3769" y1="-486" y2="-486"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-483 4581,-483 4581,-505 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="4581" x2="4581" y1="-562" y2="-541"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="4581" x2="4606" y1="-483" y2="-483"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-124411">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.000000 -163.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22870" ObjectName="SW-CX_DZS.CX_DZS_35167SW"/>
     <cge:Meas_Ref ObjectId="124411"/>
    <cge:TPSR_Ref TObjectID="22870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124409">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22868" ObjectName="SW-CX_DZS.CX_DZS_3511SW"/>
     <cge:Meas_Ref ObjectId="124409"/>
    <cge:TPSR_Ref TObjectID="22868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124385">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22848" ObjectName="SW-CX_DZS.CX_DZS_1511SW"/>
     <cge:Meas_Ref ObjectId="124385"/>
    <cge:TPSR_Ref TObjectID="22848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124387">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22850" ObjectName="SW-CX_DZS.CX_DZS_1516SW"/>
     <cge:Meas_Ref ObjectId="124387"/>
    <cge:TPSR_Ref TObjectID="22850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124386">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 -813.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22849" ObjectName="SW-CX_DZS.CX_DZS_15117SW"/>
     <cge:Meas_Ref ObjectId="124386"/>
    <cge:TPSR_Ref TObjectID="22849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124403">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -863.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22864" ObjectName="SW-CX_DZS.CX_DZS_19017SW"/>
     <cge:Meas_Ref ObjectId="124403"/>
    <cge:TPSR_Ref TObjectID="22864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124402">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4716.000000 -831.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22863" ObjectName="SW-CX_DZS.CX_DZS_1901SW"/>
     <cge:Meas_Ref ObjectId="124402"/>
    <cge:TPSR_Ref TObjectID="22863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124388">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4048.000000 -861.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22851" ObjectName="SW-CX_DZS.CX_DZS_15160SW"/>
     <cge:Meas_Ref ObjectId="124388"/>
    <cge:TPSR_Ref TObjectID="22851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124397">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 -500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22859" ObjectName="SW-CX_DZS.CX_DZS_1010SW"/>
     <cge:Meas_Ref ObjectId="124397"/>
    <cge:TPSR_Ref TObjectID="22859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124389">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 -940.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22852" ObjectName="SW-CX_DZS.CX_DZS_K1517SW"/>
     <cge:Meas_Ref ObjectId="124389"/>
    <cge:TPSR_Ref TObjectID="22852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124404">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 -808.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22865" ObjectName="SW-CX_DZS.CX_DZS_K1900SW"/>
     <cge:Meas_Ref ObjectId="124404"/>
    <cge:TPSR_Ref TObjectID="22865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5345.000000 -983.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5604.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5749.000000 -1007.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124392">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 -732.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22854" ObjectName="SW-CX_DZS.CX_DZS_1011SW"/>
     <cge:Meas_Ref ObjectId="124392"/>
    <cge:TPSR_Ref TObjectID="22854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124394">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 -629.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22856" ObjectName="SW-CX_DZS.CX_DZS_1016SW"/>
     <cge:Meas_Ref ObjectId="124394"/>
    <cge:TPSR_Ref TObjectID="22856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124395">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -654.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22857" ObjectName="SW-CX_DZS.CX_DZS_10160SW"/>
     <cge:Meas_Ref ObjectId="124395"/>
    <cge:TPSR_Ref TObjectID="22857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124393">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3785.000000 -704.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22855" ObjectName="SW-CX_DZS.CX_DZS_10117SW"/>
     <cge:Meas_Ref ObjectId="124393"/>
    <cge:TPSR_Ref TObjectID="22855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124396">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -617.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22858" ObjectName="SW-CX_DZS.CX_DZS_K1017SW"/>
     <cge:Meas_Ref ObjectId="124396"/>
    <cge:TPSR_Ref TObjectID="22858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124400">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -430.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22862" ObjectName="SW-CX_DZS.CX_DZS_3016SW"/>
     <cge:Meas_Ref ObjectId="124400"/>
    <cge:TPSR_Ref TObjectID="22862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124399">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -344.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22861" ObjectName="SW-CX_DZS.CX_DZS_3011SW"/>
     <cge:Meas_Ref ObjectId="124399"/>
    <cge:TPSR_Ref TObjectID="22861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124410">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -197.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22869" ObjectName="SW-CX_DZS.CX_DZS_3516SW"/>
     <cge:Meas_Ref ObjectId="124410"/>
    <cge:TPSR_Ref TObjectID="22869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124416">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3884.000000 -162.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22874" ObjectName="SW-CX_DZS.CX_DZS_35267SW"/>
     <cge:Meas_Ref ObjectId="124416"/>
    <cge:TPSR_Ref TObjectID="22874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124414">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -282.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22872" ObjectName="SW-CX_DZS.CX_DZS_3521SW"/>
     <cge:Meas_Ref ObjectId="124414"/>
    <cge:TPSR_Ref TObjectID="22872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124415">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -196.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22873" ObjectName="SW-CX_DZS.CX_DZS_3526SW"/>
     <cge:Meas_Ref ObjectId="124415"/>
    <cge:TPSR_Ref TObjectID="22873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124421">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 -163.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22878" ObjectName="SW-CX_DZS.CX_DZS_35367SW"/>
     <cge:Meas_Ref ObjectId="124421"/>
    <cge:TPSR_Ref TObjectID="22878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124419">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22876" ObjectName="SW-CX_DZS.CX_DZS_3531SW"/>
     <cge:Meas_Ref ObjectId="124419"/>
    <cge:TPSR_Ref TObjectID="22876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124420">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -197.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22877" ObjectName="SW-CX_DZS.CX_DZS_3536SW"/>
     <cge:Meas_Ref ObjectId="124420"/>
    <cge:TPSR_Ref TObjectID="22877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124427">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -45.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22883" ObjectName="SW-CX_DZS.CX_DZS_3010SW"/>
     <cge:Meas_Ref ObjectId="124427"/>
    <cge:TPSR_Ref TObjectID="22883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124426">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22882" ObjectName="SW-CX_DZS.CX_DZS_35467SW"/>
     <cge:Meas_Ref ObjectId="124426"/>
    <cge:TPSR_Ref TObjectID="22882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124424">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -280.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22880" ObjectName="SW-CX_DZS.CX_DZS_3541SW"/>
     <cge:Meas_Ref ObjectId="124424"/>
    <cge:TPSR_Ref TObjectID="22880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124425">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -194.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22881" ObjectName="SW-CX_DZS.CX_DZS_3546SW"/>
     <cge:Meas_Ref ObjectId="124425"/>
    <cge:TPSR_Ref TObjectID="22881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124406">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22866" ObjectName="SW-CX_DZS.CX_DZS_3901SW"/>
     <cge:Meas_Ref ObjectId="124406"/>
    <cge:TPSR_Ref TObjectID="22866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124429">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4457.000000 -271.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22884" ObjectName="SW-CX_DZS.CX_DZS_3121SW"/>
     <cge:Meas_Ref ObjectId="124429"/>
    <cge:TPSR_Ref TObjectID="22884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124431">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -272.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22886" ObjectName="SW-CX_DZS.CX_DZS_3122SW"/>
     <cge:Meas_Ref ObjectId="124431"/>
    <cge:TPSR_Ref TObjectID="22886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297664">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.000000 -729.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46142" ObjectName="SW-CX_DZS.CX_DZS_1021SW"/>
     <cge:Meas_Ref ObjectId="297664"/>
    <cge:TPSR_Ref TObjectID="46142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297666">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.000000 -626.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46144" ObjectName="SW-CX_DZS.CX_DZS_1026SW"/>
     <cge:Meas_Ref ObjectId="297666"/>
    <cge:TPSR_Ref TObjectID="46144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297667">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 -651.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46145" ObjectName="SW-CX_DZS.CX_DZS_10260SW"/>
     <cge:Meas_Ref ObjectId="297667"/>
    <cge:TPSR_Ref TObjectID="46145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297665">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -700.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46143" ObjectName="SW-CX_DZS.CX_DZS_10217SW"/>
     <cge:Meas_Ref ObjectId="297665"/>
    <cge:TPSR_Ref TObjectID="46143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297668">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.000000 -614.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46146" ObjectName="SW-CX_DZS.CX_DZS_K1027SW"/>
     <cge:Meas_Ref ObjectId="297668"/>
    <cge:TPSR_Ref TObjectID="46146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297671">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -344.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46149" ObjectName="SW-CX_DZS.CX_DZS_3022SW"/>
     <cge:Meas_Ref ObjectId="297671"/>
    <cge:TPSR_Ref TObjectID="46149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297669">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4540.000000 -497.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46147" ObjectName="SW-CX_DZS.CX_DZS_1020SW"/>
     <cge:Meas_Ref ObjectId="297669"/>
    <cge:TPSR_Ref TObjectID="46147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297672">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.000000 -373.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46150" ObjectName="SW-CX_DZS.CX_DZS_30227SW"/>
     <cge:Meas_Ref ObjectId="297672"/>
    <cge:TPSR_Ref TObjectID="46150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297674">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5038.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46151" ObjectName="SW-CX_DZS.CX_DZS_3902SW"/>
     <cge:Meas_Ref ObjectId="297674"/>
    <cge:TPSR_Ref TObjectID="46151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297687">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4857.000000 -41.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46163" ObjectName="SW-CX_DZS.CX_DZS_3020SW"/>
     <cge:Meas_Ref ObjectId="297687"/>
    <cge:TPSR_Ref TObjectID="46163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297685">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4893.000000 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46161" ObjectName="SW-CX_DZS.CX_DZS_3562SW"/>
     <cge:Meas_Ref ObjectId="297685"/>
    <cge:TPSR_Ref TObjectID="46161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297691">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5118.000000 -244.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46166" ObjectName="SW-CX_DZS.CX_DZS_35727SW"/>
     <cge:Meas_Ref ObjectId="297691"/>
    <cge:TPSR_Ref TObjectID="46166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297690">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5154.000000 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46165" ObjectName="SW-CX_DZS.CX_DZS_3572SW"/>
     <cge:Meas_Ref ObjectId="297690"/>
    <cge:TPSR_Ref TObjectID="46165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297695">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5298.000000 -247.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46169" ObjectName="SW-CX_DZS.CX_DZS_35827SW"/>
     <cge:Meas_Ref ObjectId="297695"/>
    <cge:TPSR_Ref TObjectID="46169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297694">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5334.000000 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46168" ObjectName="SW-CX_DZS.CX_DZS_3582SW"/>
     <cge:Meas_Ref ObjectId="297694"/>
    <cge:TPSR_Ref TObjectID="46168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5478.000000 -242.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5514.000000 -273.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5667.000000 -245.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5703.000000 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297681">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4661.000000 -63.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46158" ObjectName="SW-CX_DZS.CX_DZS_35567SW"/>
     <cge:Meas_Ref ObjectId="297681"/>
    <cge:TPSR_Ref TObjectID="46158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297678">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 -268.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46155" ObjectName="SW-CX_DZS.CX_DZS_3552SW"/>
     <cge:Meas_Ref ObjectId="297678"/>
    <cge:TPSR_Ref TObjectID="46155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297680">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 -97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46156" ObjectName="SW-CX_DZS.CX_DZS_3556SW"/>
     <cge:Meas_Ref ObjectId="297680"/>
    <cge:TPSR_Ref TObjectID="46156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297679">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4659.000000 -241.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46157" ObjectName="SW-CX_DZS.CX_DZS_35527SW"/>
     <cge:Meas_Ref ObjectId="297679"/>
    <cge:TPSR_Ref TObjectID="46157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297686">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4858.000000 -241.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46162" ObjectName="SW-CX_DZS.CX_DZS_35627SW"/>
     <cge:Meas_Ref ObjectId="297686"/>
    <cge:TPSR_Ref TObjectID="46162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297675">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 -386.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46153" ObjectName="SW-CX_DZS.CX_DZS_39027SW"/>
     <cge:Meas_Ref ObjectId="297675"/>
    <cge:TPSR_Ref TObjectID="46153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5089.000000 -356.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46152" ObjectName="SW-CX_DZS.CX_DZS_39020SW"/>
     <cge:Meas_Ref ObjectId="297676"/>
    <cge:TPSR_Ref TObjectID="46152"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_BDS" endPointId="0" endStationName="CX_DZS" flowDrawDirect="1" flowShape="0" id="AC-110kV.baoda_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4092,-1064 4092,-1107 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34024" ObjectName="AC-110kV.baoda_line"/>
    <cge:TPSR_Ref TObjectID="34024_SS-148"/></metadata>
   <polyline fill="none" opacity="0" points="4092,-1064 4092,-1107 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3362660">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4693.000000 -928.000000)" xlink:href="#lightningRod:shape131"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2485b70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4757.000000 -910.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0b020">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -473.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f409f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5258.000000 -924.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_357e640">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.967213 5348.000000 -919.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ffb5c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5363.000000 -973.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3295530">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.967213 5607.000000 -920.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32ad440">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5622.000000 -974.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3362f20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5807.000000 -833.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e1adb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5517.000000 -925.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_403d070">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5749.000000 -861.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3455b10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5711.000000 -1009.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afb090">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3856.000000 -422.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ee3280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -130.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3582930">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 -115.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2606d50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3651.000000 -90.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ee1be0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35ca5c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -114.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35cb960">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 -89.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3433f00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 -130.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d12340">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -115.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b43eb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -71.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35e1bf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 28.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cbe4e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4331.000000 -112.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e118b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -31.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23449d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4358.000000 -125.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_348d6e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -413.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35c66c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3560.000000 -451.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35c99b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -408.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fbdb40">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4116.000000 -975.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2381390">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -470.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e9ee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4728.000000 -422.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_239d940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -443.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_239e6f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4991.000000 -481.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e091c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5039.000000 -440.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e0e230">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 32.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4027180">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4897.000000 -108.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fc1ef0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -27.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fc2f00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4926.000000 -144.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35cea50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 -144.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35cf780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5158.000000 -108.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35d08c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5085.000000 -83.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41b7510">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5367.000000 -142.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41b8240">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5338.000000 -109.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41b9380">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5265.000000 -84.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ce620">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -142.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22cf350">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5518.000000 -105.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d0490">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5445.000000 -80.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb29a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5737.000000 -145.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb36d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5707.000000 -109.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fcc040">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5634.000000 -84.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25fded0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4732.000000 -10.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25fec00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 95.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26004e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 -13.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ea2cc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 -152.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ea3740">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4701.000000 -144.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-124162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -884.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22847"/>
     <cge:Term_Ref ObjectID="32131"/>
    <cge:TPSR_Ref TObjectID="22847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-124163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -884.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22847"/>
     <cge:Term_Ref ObjectID="32131"/>
    <cge:TPSR_Ref TObjectID="22847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-124161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -884.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22847"/>
     <cge:Term_Ref ObjectID="32131"/>
    <cge:TPSR_Ref TObjectID="22847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-124166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -725.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22853"/>
     <cge:Term_Ref ObjectID="32143"/>
    <cge:TPSR_Ref TObjectID="22853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-124167" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -725.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124167" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22853"/>
     <cge:Term_Ref ObjectID="32143"/>
    <cge:TPSR_Ref TObjectID="22853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-124165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -725.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22853"/>
     <cge:Term_Ref ObjectID="32143"/>
    <cge:TPSR_Ref TObjectID="22853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-124175" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -407.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22860"/>
     <cge:Term_Ref ObjectID="32157"/>
    <cge:TPSR_Ref TObjectID="22860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-124176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -407.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22860"/>
     <cge:Term_Ref ObjectID="32157"/>
    <cge:TPSR_Ref TObjectID="22860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-124174" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -407.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22860"/>
     <cge:Term_Ref ObjectID="32157"/>
    <cge:TPSR_Ref TObjectID="22860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-124201" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3715.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22867"/>
     <cge:Term_Ref ObjectID="32171"/>
    <cge:TPSR_Ref TObjectID="22867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-124202" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3715.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124202" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22867"/>
     <cge:Term_Ref ObjectID="32171"/>
    <cge:TPSR_Ref TObjectID="22867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-124200" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3715.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124200" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22867"/>
     <cge:Term_Ref ObjectID="32171"/>
    <cge:TPSR_Ref TObjectID="22867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-124205" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22871"/>
     <cge:Term_Ref ObjectID="32179"/>
    <cge:TPSR_Ref TObjectID="22871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-124206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22871"/>
     <cge:Term_Ref ObjectID="32179"/>
    <cge:TPSR_Ref TObjectID="22871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-124204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22871"/>
     <cge:Term_Ref ObjectID="32179"/>
    <cge:TPSR_Ref TObjectID="22871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-124193" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124193" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22875"/>
     <cge:Term_Ref ObjectID="32187"/>
    <cge:TPSR_Ref TObjectID="22875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-124194" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22875"/>
     <cge:Term_Ref ObjectID="32187"/>
    <cge:TPSR_Ref TObjectID="22875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-124192" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124192" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22875"/>
     <cge:Term_Ref ObjectID="32187"/>
    <cge:TPSR_Ref TObjectID="22875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-124188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 -418.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22845"/>
     <cge:Term_Ref ObjectID="32128"/>
    <cge:TPSR_Ref TObjectID="22845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-124189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 -418.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22845"/>
     <cge:Term_Ref ObjectID="32128"/>
    <cge:TPSR_Ref TObjectID="22845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-124190" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 -418.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22845"/>
     <cge:Term_Ref ObjectID="32128"/>
    <cge:TPSR_Ref TObjectID="22845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-124191" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 -418.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124191" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22845"/>
     <cge:Term_Ref ObjectID="32128"/>
    <cge:TPSR_Ref TObjectID="22845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-124187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 -418.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22845"/>
     <cge:Term_Ref ObjectID="32128"/>
    <cge:TPSR_Ref TObjectID="22845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-124183" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -865.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22844"/>
     <cge:Term_Ref ObjectID="32127"/>
    <cge:TPSR_Ref TObjectID="22844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-124184" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -865.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124184" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22844"/>
     <cge:Term_Ref ObjectID="32127"/>
    <cge:TPSR_Ref TObjectID="22844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-124185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -865.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22844"/>
     <cge:Term_Ref ObjectID="32127"/>
    <cge:TPSR_Ref TObjectID="22844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-124186" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -865.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124186" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22844"/>
     <cge:Term_Ref ObjectID="32127"/>
    <cge:TPSR_Ref TObjectID="22844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-124182" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -865.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22844"/>
     <cge:Term_Ref ObjectID="32127"/>
    <cge:TPSR_Ref TObjectID="22844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-124173" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -520.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124173" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22888"/>
     <cge:Term_Ref ObjectID="32216"/>
    <cge:TPSR_Ref TObjectID="22888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-124197" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4286.000000 -281.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124197" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22879"/>
     <cge:Term_Ref ObjectID="32195"/>
    <cge:TPSR_Ref TObjectID="22879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-124198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4286.000000 -281.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22879"/>
     <cge:Term_Ref ObjectID="32195"/>
    <cge:TPSR_Ref TObjectID="22879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-124196" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4286.000000 -281.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124196" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22879"/>
     <cge:Term_Ref ObjectID="32195"/>
    <cge:TPSR_Ref TObjectID="22879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-297735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5781.000000 -432.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46140"/>
     <cge:Term_Ref ObjectID="30427"/>
    <cge:TPSR_Ref TObjectID="46140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-297737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5781.000000 -432.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46140"/>
     <cge:Term_Ref ObjectID="30427"/>
    <cge:TPSR_Ref TObjectID="46140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-297739" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5781.000000 -432.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46140"/>
     <cge:Term_Ref ObjectID="30427"/>
    <cge:TPSR_Ref TObjectID="46140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-297720" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5781.000000 -432.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297720" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46140"/>
     <cge:Term_Ref ObjectID="30427"/>
    <cge:TPSR_Ref TObjectID="46140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-297740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5781.000000 -432.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46140"/>
     <cge:Term_Ref ObjectID="30427"/>
    <cge:TPSR_Ref TObjectID="46140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4829.000000 -438.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46148"/>
     <cge:Term_Ref ObjectID="30446"/>
    <cge:TPSR_Ref TObjectID="46148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297714" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4829.000000 -438.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46148"/>
     <cge:Term_Ref ObjectID="30446"/>
    <cge:TPSR_Ref TObjectID="46148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4829.000000 -438.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46148"/>
     <cge:Term_Ref ObjectID="30446"/>
    <cge:TPSR_Ref TObjectID="46148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297707" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -722.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297707" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46141"/>
     <cge:Term_Ref ObjectID="30432"/>
    <cge:TPSR_Ref TObjectID="46141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297713" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -722.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46141"/>
     <cge:Term_Ref ObjectID="30432"/>
    <cge:TPSR_Ref TObjectID="46141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -722.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46141"/>
     <cge:Term_Ref ObjectID="30432"/>
    <cge:TPSR_Ref TObjectID="46141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 15.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46154"/>
     <cge:Term_Ref ObjectID="30476"/>
    <cge:TPSR_Ref TObjectID="46154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297715" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 15.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297715" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46154"/>
     <cge:Term_Ref ObjectID="30476"/>
    <cge:TPSR_Ref TObjectID="46154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297703" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 15.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297703" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46154"/>
     <cge:Term_Ref ObjectID="30476"/>
    <cge:TPSR_Ref TObjectID="46154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4942.000000 58.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46160"/>
     <cge:Term_Ref ObjectID="30488"/>
    <cge:TPSR_Ref TObjectID="46160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297716" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4942.000000 58.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297716" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46160"/>
     <cge:Term_Ref ObjectID="30488"/>
    <cge:TPSR_Ref TObjectID="46160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297704" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4942.000000 58.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297704" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46160"/>
     <cge:Term_Ref ObjectID="30488"/>
    <cge:TPSR_Ref TObjectID="46160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5140.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46164"/>
     <cge:Term_Ref ObjectID="30496"/>
    <cge:TPSR_Ref TObjectID="46164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5140.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46164"/>
     <cge:Term_Ref ObjectID="30496"/>
    <cge:TPSR_Ref TObjectID="46164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297705" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5140.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46164"/>
     <cge:Term_Ref ObjectID="30496"/>
    <cge:TPSR_Ref TObjectID="46164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297712" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5327.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297712" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46167"/>
     <cge:Term_Ref ObjectID="30838"/>
    <cge:TPSR_Ref TObjectID="46167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297718" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5327.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46167"/>
     <cge:Term_Ref ObjectID="30838"/>
    <cge:TPSR_Ref TObjectID="46167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297706" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5327.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46167"/>
     <cge:Term_Ref ObjectID="30838"/>
    <cge:TPSR_Ref TObjectID="46167"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="42" qtmmishow="hidden" width="164" x="3246" y="-1083"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1101"/></g>
   <g href="AVC大中山.svg" style="fill-opacity:0"><rect height="31" qtmmishow="hidden" width="86" x="3474" y="-945"/></g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5495,-842 5494,-842 5494,-842 5493,-842 5493,-842 5492,-843 5492,-843 5491,-843 5491,-844 5491,-844 5490,-845 5490,-845 5490,-846 5490,-846 5490,-847 5490,-847 5491,-848 5491,-848 5491,-849 5492,-849 5492,-849 5493,-850 5493,-850 5494,-850 5494,-850 5495,-850 " stroke="rgb(60,120,255)" stroke-width="0.191496"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_DZS.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 -60.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43281" ObjectName="SM-CX_DZS.P1"/>
    <cge:TPSR_Ref TObjectID="43281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DZS.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -59.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43282" ObjectName="SM-CX_DZS.P2"/>
    <cge:TPSR_Ref TObjectID="43282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DZS.CX_DZS_P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5158.000000 -53.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47294" ObjectName="SM-CX_DZS.CX_DZS_P1"/>
    <cge:TPSR_Ref TObjectID="47294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DZS.CX_DZS_P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5338.000000 -54.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47295" ObjectName="SM-CX_DZS.CX_DZS_P2"/>
    <cge:TPSR_Ref TObjectID="47295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5518.000000 -50.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5707.000000 -54.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1025.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-124162" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3250.538462 -922.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124162" ObjectName="CX_DZS:CX_DZS_151BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-124163" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3248.538462 -880.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124163" ObjectName="CX_DZS:CX_DZS_151BK_Q"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-986"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1106"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="5285" y="-887"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="5324" y="-886"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="5256" y="-834"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="5282" y="-834"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="5490" y="-835"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="5516" y="-835"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="5476" y="-878"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="5437" y="-877"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-506"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="206" lineStyle="1" stroke="rgb(255,255,255)" stroke-dasharray="10 5 " stroke-width="1" width="220" x="5483" y="-1093"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="42" qtmmishow="hidden" width="164" x="3246" y="-1083"/>
    </a>
   <metadata/><rect fill="white" height="42" opacity="0" stroke="white" transform="" width="164" x="3246" y="-1083"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1101"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1101"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="31" qtmmishow="hidden" width="86" x="3474" y="-945"/>
    </a>
   <metadata/><rect fill="white" height="31" opacity="0" stroke="white" transform="" width="86" x="3474" y="-945"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5245.000000 -970.000000)" xlink:href="#transformer2:shape60_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5245.000000 -970.000000)" xlink:href="#transformer2:shape60_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5504.000000 -971.000000)" xlink:href="#transformer2:shape60_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5504.000000 -971.000000)" xlink:href="#transformer2:shape60_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5738.000000 -907.000000)" xlink:href="#transformer2:shape61_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5738.000000 -907.000000)" xlink:href="#transformer2:shape61_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DZS.CX_DZS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="32215"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.000000 -498.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.000000 -498.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22888" ObjectName="TF-CX_DZS.CX_DZS_1T"/>
    <cge:TPSR_Ref TObjectID="22888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4311.000000 -5.000000)" xlink:href="#transformer2:shape59_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4311.000000 -5.000000)" xlink:href="#transformer2:shape59_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DZS.CX_DZS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30846"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4624.000000 -495.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4624.000000 -495.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="46170" ObjectName="TF-CX_DZS.CX_DZS_2T"/>
    <cge:TPSR_Ref TObjectID="46170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -1.000000)" xlink:href="#transformer2:shape59_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -1.000000)" xlink:href="#transformer2:shape59_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DZS"/>
</svg>