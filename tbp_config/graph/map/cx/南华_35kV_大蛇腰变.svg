<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-197" aopId="3965442" id="thSvg" product="E8000V2" version="1.0" viewBox="1339 -1421 2145 1267">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="capacitor:shape32">
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="2" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="21" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.271035" x1="11" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
   </symbol>
   <symbol id="capacitor:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="52" x2="52" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="43" x2="43" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="8" x2="8" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="20" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="26" x2="26" y1="88" y2="21"/>
    <polyline arcFlag="1" points="43,36 44,36 45,36 45,37 46,37 46,37 47,38 47,38 48,39 48,39 48,40 48,40 49,41 49,42 49,42 48,43 48,44 48,44 48,45 47,45 47,46 46,46 46,47 45,47 45,47 44,47 43,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,25 44,25 45,25 45,25 46,26 46,26 47,26 47,27 48,27 48,28 48,29 48,29 49,30 49,31 49,31 48,32 48,33 48,33 48,34 47,34 47,35 46,35 46,35 45,36 45,36 44,36 43,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,15 44,15 45,15 45,15 46,15 46,16 47,16 47,17 48,17 48,18 48,18 48,19 49,20 49,20 49,21 48,22 48,22 48,23 48,23 47,24 47,24 46,25 46,25 45,25 45,26 44,26 43,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="38" x2="26" y1="88" y2="88"/>
    <polyline arcFlag="1" points="25,101 23,101 21,100 20,100 18,99 17,98 15,97 14,95 13,93 13,92 12,90 12,88 12,86 13,84 13,83 14,81 15,80 17,78 18,77 20,76 21,76 23,75 25,75 27,75 29,76 30,76 32,77 33,78 35,80 36,81 37,83 37,84 38,86 38,88 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="7" y1="5" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape74">
    <circle cx="39" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <circle cx="30" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="71" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="31" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="24" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="8" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,17 39,15 45,15 43,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="4" y2="4"/>
    <ellipse cx="14" cy="18" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="22" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="17" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="24" x2="24" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="24" y1="8" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="24" y1="10" y2="12"/>
    <ellipse cx="8" cy="10" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape39_0">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline points="64,96 64,89 " stroke-width="1.1"/>
    <polyline points="58,96 64,96 " stroke-width="1.1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape39_1">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,96 1,33 " stroke-width="1.1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2cca1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cca680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ccadf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ccbe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ccd140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ccdde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cce980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ccf380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2674c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2674c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd23e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd23e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd4210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd4210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2cd5220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd6eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2cd7b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2cd89e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cd92c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cdaa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cdb780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cdc040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cdc800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cdd8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cde260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cded50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2cdf710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ce0b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ce1730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ce2760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ce33a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cf1b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ce4c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2ce6280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2ce77b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1277" width="2155" x="1334" y="-1426"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,127)" stroke-width="1" x1="2332" x2="2332" y1="-589" y2="-589"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,127)" stroke-width="1" x1="2332" x2="2332" y1="-589" y2="-589"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,127)" stroke-width="1" x1="2331" x2="2331" y1="-593" y2="-593"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(173,255,47)" stroke-width="1.8" x1="2430" x2="2421" y1="-561" y2="-570"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(173,255,47)" stroke-width="1.8" x1="2439" x2="2430" y1="-562" y2="-553"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(173,255,47)" stroke-width="1.8" x1="2430" x2="2421" y1="-553" y2="-562"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(173,255,47)" stroke-width="1.8" x1="2439" x2="2430" y1="-570" y2="-561"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(173,255,47)" stroke-width="2" x1="2430" x2="2430" y1="-636" y2="-561"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(173,255,47)" stroke-width="1.8" x1="2439" x2="2430" y1="-627" y2="-636"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(173,255,47)" stroke-width="1.8" x1="2430" x2="2421" y1="-644" y2="-635"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(173,255,47)" stroke-width="1.8" x1="2439" x2="2430" y1="-635" y2="-644"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(173,255,47)" stroke-width="1.8" x1="2430" x2="2421" y1="-636" y2="-627"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="1745" x2="1745" y1="-297" y2="-315"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="1340" y="-1404"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="1364" y="-833"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-131270">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2550.000000 -961.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24061" ObjectName="SW-NH_DSY.NH_DSY_3901SW"/>
     <cge:Meas_Ref ObjectId="131270"/>
    <cge:TPSR_Ref TObjectID="24061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131292">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2299.536635 -1025.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24064" ObjectName="SW-NH_DSY.NH_DSY_3511SW"/>
     <cge:Meas_Ref ObjectId="131292"/>
    <cge:TPSR_Ref TObjectID="24064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193633">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2276.925893 -471.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29451" ObjectName="SW-NH_DSY.NH_DSY_05467SW"/>
     <cge:Meas_Ref ObjectId="193633"/>
    <cge:TPSR_Ref TObjectID="29451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131332">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.340002 -1029.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24068" ObjectName="SW-NH_DSY.NH_DSY_3521SW"/>
     <cge:Meas_Ref ObjectId="131332"/>
    <cge:TPSR_Ref TObjectID="24068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131236">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2300.000000 -1099.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24052" ObjectName="SW-NH_DSY.NH_DSY_3631SW"/>
     <cge:Meas_Ref ObjectId="131236"/>
    <cge:TPSR_Ref TObjectID="24052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131237">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2300.000000 -1227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24053" ObjectName="SW-NH_DSY.NH_DSY_3636SW"/>
     <cge:Meas_Ref ObjectId="131237"/>
    <cge:TPSR_Ref TObjectID="24053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131238">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2338.000000 -1129.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24054" ObjectName="SW-NH_DSY.NH_DSY_36317SW"/>
     <cge:Meas_Ref ObjectId="131238"/>
    <cge:TPSR_Ref TObjectID="24054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.000000 -1192.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24055" ObjectName="SW-NH_DSY.NH_DSY_36360SW"/>
     <cge:Meas_Ref ObjectId="131239"/>
    <cge:TPSR_Ref TObjectID="24055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.078125 -1257.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24056" ObjectName="SW-NH_DSY.NH_DSY_36367SW"/>
     <cge:Meas_Ref ObjectId="131240"/>
    <cge:TPSR_Ref TObjectID="24056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131254">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2953.000000 -1098.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24058" ObjectName="SW-NH_DSY.NH_DSY_3611SW"/>
     <cge:Meas_Ref ObjectId="131254"/>
    <cge:TPSR_Ref TObjectID="24058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131255">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2953.000000 -1227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24059" ObjectName="SW-NH_DSY.NH_DSY_3616SW"/>
     <cge:Meas_Ref ObjectId="131255"/>
    <cge:TPSR_Ref TObjectID="24059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216188">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2992.078125 -1257.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24060" ObjectName="SW-NH_DSY.NH_DSY_36167SW"/>
     <cge:Meas_Ref ObjectId="216188"/>
    <cge:TPSR_Ref TObjectID="24060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131203">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2679.000000 -1098.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24046" ObjectName="SW-NH_DSY.NH_DSY_3621SW"/>
     <cge:Meas_Ref ObjectId="131203"/>
    <cge:TPSR_Ref TObjectID="24046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131204">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2679.000000 -1226.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24047" ObjectName="SW-NH_DSY.NH_DSY_3626SW"/>
     <cge:Meas_Ref ObjectId="131204"/>
    <cge:TPSR_Ref TObjectID="24047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131205">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2717.000000 -1128.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24048" ObjectName="SW-NH_DSY.NH_DSY_36217SW"/>
     <cge:Meas_Ref ObjectId="131205"/>
    <cge:TPSR_Ref TObjectID="24048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131206">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2718.000000 -1191.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24049" ObjectName="SW-NH_DSY.NH_DSY_36260SW"/>
     <cge:Meas_Ref ObjectId="131206"/>
    <cge:TPSR_Ref TObjectID="24049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131207">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2718.078125 -1256.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24050" ObjectName="SW-NH_DSY.NH_DSY_36267SW"/>
     <cge:Meas_Ref ObjectId="131207"/>
    <cge:TPSR_Ref TObjectID="24050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2589.000000 -1005.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24062" ObjectName="SW-NH_DSY.NH_DSY_39017SW"/>
     <cge:Meas_Ref ObjectId="131271"/>
    <cge:TPSR_Ref TObjectID="24062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131385">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2100.257512 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24077" ObjectName="SW-NH_DSY.NH_DSY_0526SW"/>
     <cge:Meas_Ref ObjectId="131385"/>
    <cge:TPSR_Ref TObjectID="24077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2161.257512 -471.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29449" ObjectName="SW-NH_DSY.NH_DSY_05267SW"/>
     <cge:Meas_Ref ObjectId="193627"/>
    <cge:TPSR_Ref TObjectID="29449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131413">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2210.925893 -367.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24085" ObjectName="SW-NH_DSY.NH_DSY_0546SW"/>
     <cge:Meas_Ref ObjectId="131413"/>
    <cge:TPSR_Ref TObjectID="24085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131399">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2944.618659 -339.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24081" ObjectName="SW-NH_DSY.NH_DSY_0536SW"/>
     <cge:Meas_Ref ObjectId="131399"/>
    <cge:TPSR_Ref TObjectID="24081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193630">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3009.618659 -463.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29450" ObjectName="SW-NH_DSY.NH_DSY_05367SW"/>
     <cge:Meas_Ref ObjectId="193630"/>
    <cge:TPSR_Ref TObjectID="29450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216366">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2873.055341 -462.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29462" ObjectName="SW-NH_DSY.NH_DSY_05967SW"/>
     <cge:Meas_Ref ObjectId="216366"/>
    <cge:TPSR_Ref TObjectID="29462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1736.243447 -247.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29461" ObjectName="SW-NH_DSY.NH_DSY_05800SW"/>
     <cge:Meas_Ref ObjectId="216245"/>
    <cge:TPSR_Ref TObjectID="29461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1800.243447 -474.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29474" ObjectName="SW-NH_DSY.NH_DSY_05867SW"/>
     <cge:Meas_Ref ObjectId="193665"/>
    <cge:TPSR_Ref TObjectID="29474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131469">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1734.767876 -432.052632)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24101" ObjectName="SW-NH_DSY.NH_DSY_0586SW"/>
     <cge:Meas_Ref ObjectId="131469"/>
    <cge:TPSR_Ref TObjectID="24101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131427">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3101.676067 -335.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24089" ObjectName="SW-NH_DSY.NH_DSY_0556SW"/>
     <cge:Meas_Ref ObjectId="131427"/>
    <cge:TPSR_Ref TObjectID="24089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193636">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3165.676067 -461.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29452" ObjectName="SW-NH_DSY.NH_DSY_05567SW"/>
     <cge:Meas_Ref ObjectId="193636"/>
    <cge:TPSR_Ref TObjectID="29452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131455">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3383.972877 -336.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24097" ObjectName="SW-NH_DSY.NH_DSY_0576SW"/>
     <cge:Meas_Ref ObjectId="131455"/>
    <cge:TPSR_Ref TObjectID="24097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3447.972877 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29454" ObjectName="SW-NH_DSY.NH_DSY_05767SW"/>
     <cge:Meas_Ref ObjectId="193642"/>
    <cge:TPSR_Ref TObjectID="29454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1926.676067 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29448" ObjectName="SW-NH_DSY.NH_DSY_05167SW"/>
     <cge:Meas_Ref ObjectId="193624"/>
    <cge:TPSR_Ref TObjectID="29448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131371">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1862.676067 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24073" ObjectName="SW-NH_DSY.NH_DSY_0516SW"/>
     <cge:Meas_Ref ObjectId="131371"/>
    <cge:TPSR_Ref TObjectID="24073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216223">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1990.676067 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29460" ObjectName="SW-NH_DSY.NH_DSY_0506SW"/>
     <cge:Meas_Ref ObjectId="216223"/>
    <cge:TPSR_Ref TObjectID="29460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2054.676067 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29475" ObjectName="SW-NH_DSY.NH_DSY_05067SW"/>
     <cge:Meas_Ref ObjectId="193654"/>
    <cge:TPSR_Ref TObjectID="29475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131441">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3239.972877 -336.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24093" ObjectName="SW-NH_DSY.NH_DSY_0566SW"/>
     <cge:Meas_Ref ObjectId="131441"/>
    <cge:TPSR_Ref TObjectID="24093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193639">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3303.972877 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29453" ObjectName="SW-NH_DSY.NH_DSY_05667SW"/>
     <cge:Meas_Ref ObjectId="193639"/>
    <cge:TPSR_Ref TObjectID="29453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216257">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2599.000000 -604.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32186" ObjectName="SW-NH_DSY.NH_DSY_012XC"/>
     <cge:Meas_Ref ObjectId="216257"/>
    <cge:TPSR_Ref TObjectID="32186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216257">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2599.000000 -535.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32187" ObjectName="SW-NH_DSY.NH_DSY_012XC1"/>
     <cge:Meas_Ref ObjectId="216257"/>
    <cge:TPSR_Ref TObjectID="32187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2657.000000 -551.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29469" ObjectName="SW-NH_DSY.NH_DSY_0902SW"/>
     <cge:Meas_Ref ObjectId="216307"/>
    <cge:TPSR_Ref TObjectID="29469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216288">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2346.000000 -587.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29467" ObjectName="SW-NH_DSY.NH_DSY_0901SW"/>
     <cge:Meas_Ref ObjectId="216288"/>
    <cge:TPSR_Ref TObjectID="29467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216256">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2470.000000 -548.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32185" ObjectName="SW-NH_DSY.NH_DSY_0121XC"/>
     <cge:Meas_Ref ObjectId="216256"/>
    <cge:TPSR_Ref TObjectID="32185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2099.000000 -633.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32152" ObjectName="SW-NH_DSY.NH_DSY_052XC"/>
     <cge:Meas_Ref ObjectId="216193"/>
    <cge:TPSR_Ref TObjectID="32152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2099.000000 -558.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32153" ObjectName="SW-NH_DSY.NH_DSY_052XC1"/>
     <cge:Meas_Ref ObjectId="216193"/>
    <cge:TPSR_Ref TObjectID="32153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2945.000000 -634.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32154" ObjectName="SW-NH_DSY.NH_DSY_053XC"/>
     <cge:Meas_Ref ObjectId="216194"/>
    <cge:TPSR_Ref TObjectID="32154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2945.000000 -559.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32155" ObjectName="SW-NH_DSY.NH_DSY_053XC1"/>
     <cge:Meas_Ref ObjectId="216194"/>
    <cge:TPSR_Ref TObjectID="32155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1862.000000 -632.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32150" ObjectName="SW-NH_DSY.NH_DSY_051XC"/>
     <cge:Meas_Ref ObjectId="216192"/>
    <cge:TPSR_Ref TObjectID="32150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1862.000000 -557.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32151" ObjectName="SW-NH_DSY.NH_DSY_051XC1"/>
     <cge:Meas_Ref ObjectId="216192"/>
    <cge:TPSR_Ref TObjectID="32151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216204">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1990.000000 -635.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32166" ObjectName="SW-NH_DSY.NH_DSY_050XC"/>
     <cge:Meas_Ref ObjectId="216204"/>
    <cge:TPSR_Ref TObjectID="32166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216204">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1990.000000 -560.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32164" ObjectName="SW-NH_DSY.NH_DSY_050XC1"/>
     <cge:Meas_Ref ObjectId="216204"/>
    <cge:TPSR_Ref TObjectID="32164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216195">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2210.000000 -635.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32156" ObjectName="SW-NH_DSY.NH_DSY_054XC"/>
     <cge:Meas_Ref ObjectId="216195"/>
    <cge:TPSR_Ref TObjectID="32156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216195">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2210.000000 -560.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32157" ObjectName="SW-NH_DSY.NH_DSY_054XC1"/>
     <cge:Meas_Ref ObjectId="216195"/>
    <cge:TPSR_Ref TObjectID="32157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216247">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2804.000000 -627.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32182" ObjectName="SW-NH_DSY.NH_DSY_059XC"/>
     <cge:Meas_Ref ObjectId="216247"/>
    <cge:TPSR_Ref TObjectID="32182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216247">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2804.000000 -552.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32180" ObjectName="SW-NH_DSY.NH_DSY_059XC1"/>
     <cge:Meas_Ref ObjectId="216247"/>
    <cge:TPSR_Ref TObjectID="32180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216196">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3101.000000 -630.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32158" ObjectName="SW-NH_DSY.NH_DSY_055XC"/>
     <cge:Meas_Ref ObjectId="216196"/>
    <cge:TPSR_Ref TObjectID="32158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216196">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3101.000000 -555.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32159" ObjectName="SW-NH_DSY.NH_DSY_055XC1"/>
     <cge:Meas_Ref ObjectId="216196"/>
    <cge:TPSR_Ref TObjectID="32159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216197">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3239.000000 -638.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32160" ObjectName="SW-NH_DSY.NH_DSY_056XC"/>
     <cge:Meas_Ref ObjectId="216197"/>
    <cge:TPSR_Ref TObjectID="32160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216197">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3239.000000 -563.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32161" ObjectName="SW-NH_DSY.NH_DSY_056XC1"/>
     <cge:Meas_Ref ObjectId="216197"/>
    <cge:TPSR_Ref TObjectID="32161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216198">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3383.000000 -637.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32162" ObjectName="SW-NH_DSY.NH_DSY_057XC"/>
     <cge:Meas_Ref ObjectId="216198"/>
    <cge:TPSR_Ref TObjectID="32162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216198">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3383.000000 -562.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32163" ObjectName="SW-NH_DSY.NH_DSY_057XC1"/>
     <cge:Meas_Ref ObjectId="216198"/>
    <cge:TPSR_Ref TObjectID="32163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216206">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1734.000000 -633.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32169" ObjectName="SW-NH_DSY.NH_DSY_058XC"/>
     <cge:Meas_Ref ObjectId="216206"/>
    <cge:TPSR_Ref TObjectID="32169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216206">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1734.000000 -558.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32168" ObjectName="SW-NH_DSY.NH_DSY_058XC1"/>
     <cge:Meas_Ref ObjectId="216206"/>
    <cge:TPSR_Ref TObjectID="32168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216208">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2298.000000 -783.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32172" ObjectName="SW-NH_DSY.NH_DSY_001XC"/>
     <cge:Meas_Ref ObjectId="216208"/>
    <cge:TPSR_Ref TObjectID="32172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216208">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2298.000000 -708.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32170" ObjectName="SW-NH_DSY.NH_DSY_001XC1"/>
     <cge:Meas_Ref ObjectId="216208"/>
    <cge:TPSR_Ref TObjectID="32170"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_DSY" endPointId="0" endStationName="NH_HTP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_damahongTdsy" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2962,-1419 2962,-1388 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34576" ObjectName="AC-35kV.LN_damahongTdsy"/>
    <cge:TPSR_Ref TObjectID="34576_SS-197"/></metadata>
   <polyline fill="none" opacity="0" points="2962,-1419 2962,-1388 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_DSY" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_nanhuangdaDSY" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2309,-1419 2309,-1388 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38072" ObjectName="AC-35kV.LN_nanhuangdaDSY"/>
    <cge:TPSR_Ref TObjectID="38072_SS-197"/></metadata>
   <polyline fill="none" opacity="0" points="2309,-1419 2309,-1388 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_NH" endPointId="0" endStationName="NH_DSY" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dasheyao" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2688,-1419 2688,-1389 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38076" ObjectName="AC-35kV.LN_dasheyao"/>
    <cge:TPSR_Ref TObjectID="38076_SS-197"/></metadata>
   <polyline fill="none" opacity="0" points="2688,-1419 2688,-1389 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-NH_DSY.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.125000 2100.257512 -255.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33949" ObjectName="EC-NH_DSY.052Ld"/>
    <cge:TPSR_Ref TObjectID="33949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_DSY.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2210.925893 -256.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33951" ObjectName="EC-NH_DSY.054Ld"/>
    <cge:TPSR_Ref TObjectID="33951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_DSY.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2943.618659 -255.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33950" ObjectName="EC-NH_DSY.053Ld"/>
    <cge:TPSR_Ref TObjectID="33950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_DSY.055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3101.676067 -257.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33952" ObjectName="EC-NH_DSY.055Ld"/>
    <cge:TPSR_Ref TObjectID="33952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_DSY.057Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3383.972877 -256.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33955" ObjectName="EC-NH_DSY.057Ld"/>
    <cge:TPSR_Ref TObjectID="33955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_DSY.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1862.676067 -260.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33948" ObjectName="EC-NH_DSY.051Ld"/>
    <cge:TPSR_Ref TObjectID="33948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_DSY.050Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1990.676067 -260.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33954" ObjectName="EC-NH_DSY.050Ld"/>
    <cge:TPSR_Ref TObjectID="33954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_DSY.056Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3239.972877 -257.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33953" ObjectName="EC-NH_DSY.056Ld"/>
    <cge:TPSR_Ref TObjectID="33953"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_241c570" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2368.000000 -1149.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ed410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2370.000000 -1212.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24329f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2370.000000 -1277.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23eb120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3022.000000 -1277.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_249dd60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2747.000000 -1148.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23c0010" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2749.000000 -1211.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_241eb40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2749.000000 -1276.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23a4b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2619.000000 -1025.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23cc830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2165.257512 -440.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ccf40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3013.904373 -435.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_245b190" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3169.961781 -434.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_236ee60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3452.258591 -441.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2336690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2281.530235 -443.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23735d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1803.993464 -440.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2376350" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2876.519820 -435.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_236a760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1930.961781 -437.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f80a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2058.961781 -437.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2452d20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3308.258591 -442.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_24d9e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2559,-933 2559,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_21314b0@0" ObjectIDZND0="24061@0" Pin0InfoVect0LinkObjId="SW-131270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21314b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2559,-933 2559,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24678c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1086 2309,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24042@0" ObjectIDZND0="24064@1" Pin0InfoVect0LinkObjId="SW-131292_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23f09a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1086 2309,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2412840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1030 2309,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24064@0" ObjectIDZND0="24063@1" Pin0InfoVect0LinkObjId="SW-131291_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1030 2309,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2412a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-972 2309,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24063@0" ObjectIDZND0="24104@1" Pin0InfoVect0LinkObjId="g_2303b70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-972 2309,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24acd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2286,-455 2286,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2336690@0" ObjectIDZND0="29451@0" Pin0InfoVect0LinkObjId="SW-193633_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2336690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2286,-455 2286,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ad210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2775,-1034 2775,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24068@0" ObjectIDZND0="24067@1" Pin0InfoVect0LinkObjId="SW-131331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2775,-1034 2775,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ad400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2775,-976 2775,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24067@0" ObjectIDZND0="24105@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2775,-976 2775,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_241cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2365,-1155 2373,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24054@1" ObjectIDZND0="g_241c570@0" Pin0InfoVect0LinkObjId="g_241c570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131238_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2365,-1155 2373,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23edbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2366,-1218 2374,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24055@1" ObjectIDZND0="g_23ed410@0" Pin0InfoVect0LinkObjId="g_23ed410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2366,-1218 2374,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23eddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1201 2309,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24051@1" ObjectIDZND0="24055@x" ObjectIDZND1="24053@x" Pin0InfoVect0LinkObjId="SW-131239_0" Pin0InfoVect1LinkObjId="SW-131237_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1201 2309,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23edfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1218 2309,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24051@x" ObjectIDND1="24055@x" ObjectIDZND0="24053@0" Pin0InfoVect0LinkObjId="SW-131237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131235_0" Pin1InfoVect1LinkObjId="SW-131239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1218 2309,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24331a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2330,-1283 2309,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24056@0" ObjectIDZND0="g_2433770@0" ObjectIDZND1="g_246fbc0@0" ObjectIDZND2="38072@1" Pin0InfoVect0LinkObjId="g_2433770_0" Pin0InfoVect1LinkObjId="g_246fbc0_0" Pin0InfoVect2LinkObjId="g_2434520_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2330,-1283 2309,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2433390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2366,-1283 2374,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24056@1" ObjectIDZND0="g_24329f0@0" Pin0InfoVect0LinkObjId="g_24329f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2366,-1283 2374,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2433580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1268 2309,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24053@1" ObjectIDZND0="24056@x" ObjectIDZND1="g_2433770@0" ObjectIDZND2="g_246fbc0@0" Pin0InfoVect0LinkObjId="SW-131240_0" Pin0InfoVect1LinkObjId="g_2433770_0" Pin0InfoVect2LinkObjId="g_246fbc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131237_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1268 2309,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2434140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2339,-1347 2309,-1347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2433770@0" ObjectIDZND0="24056@x" ObjectIDZND1="24053@x" ObjectIDZND2="g_246fbc0@0" Pin0InfoVect0LinkObjId="SW-131240_0" Pin0InfoVect1LinkObjId="SW-131237_0" Pin0InfoVect2LinkObjId="g_246fbc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2433770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2339,-1347 2309,-1347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2434330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1283 2309,-1347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24056@x" ObjectIDND1="24053@x" ObjectIDZND0="g_2433770@0" ObjectIDZND1="g_246fbc0@0" ObjectIDZND2="38072@1" Pin0InfoVect0LinkObjId="g_2433770_0" Pin0InfoVect1LinkObjId="g_246fbc0_0" Pin0InfoVect2LinkObjId="g_2434520_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131240_0" Pin1InfoVect1LinkObjId="SW-131237_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1283 2309,-1347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2434520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1347 2309,-1388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2433770@0" ObjectIDND1="24056@x" ObjectIDND2="24053@x" ObjectIDZND0="38072@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2433770_0" Pin1InfoVect1LinkObjId="SW-131240_0" Pin1InfoVect2LinkObjId="SW-131237_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1347 2309,-1388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2434710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1347 2243,-1347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2433770@0" ObjectIDND1="24056@x" ObjectIDND2="24053@x" ObjectIDZND0="g_246fbc0@0" Pin0InfoVect0LinkObjId="g_246fbc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2433770_0" Pin1InfoVect1LinkObjId="SW-131240_0" Pin1InfoVect2LinkObjId="SW-131237_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1347 2243,-1347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2422a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2330,-1218 2309,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24055@0" ObjectIDZND0="24051@x" ObjectIDZND1="24053@x" Pin0InfoVect0LinkObjId="SW-131235_0" Pin0InfoVect1LinkObjId="SW-131237_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131239_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2330,-1218 2309,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2424070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2329,-1155 2309,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24054@0" ObjectIDZND0="24051@x" ObjectIDZND1="24052@x" Pin0InfoVect0LinkObjId="SW-131235_0" Pin0InfoVect1LinkObjId="SW-131236_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131238_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2329,-1155 2309,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2490c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1174 2309,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24051@0" ObjectIDZND0="24054@x" ObjectIDZND1="24052@x" Pin0InfoVect0LinkObjId="SW-131238_0" Pin0InfoVect1LinkObjId="SW-131236_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1174 2309,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2490e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1155 2309,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="24054@x" ObjectIDND1="24051@x" ObjectIDZND0="24052@1" Pin0InfoVect0LinkObjId="SW-131236_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131238_0" Pin1InfoVect1LinkObjId="SW-131235_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1155 2309,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248a110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2962,-1086 2962,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24042@0" ObjectIDZND0="24058@0" Pin0InfoVect0LinkObjId="SW-131254_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23f09a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2962,-1086 2962,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23eba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2983,-1283 2962,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24060@0" ObjectIDZND0="g_23ec0b0@0" ObjectIDZND1="g_238aeb0@0" ObjectIDZND2="34576@1" Pin0InfoVect0LinkObjId="g_23ec0b0_0" Pin0InfoVect1LinkObjId="g_238aeb0_0" Pin0InfoVect2LinkObjId="g_23ed0a0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2983,-1283 2962,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23ebc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3019,-1283 3027,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24060@1" ObjectIDZND0="g_23eb120@0" Pin0InfoVect0LinkObjId="g_23eb120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216188_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3019,-1283 3027,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23ebe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2962,-1268 2962,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24059@1" ObjectIDZND0="24060@x" ObjectIDZND1="g_23ec0b0@0" ObjectIDZND2="g_238aeb0@0" Pin0InfoVect0LinkObjId="SW-216188_0" Pin0InfoVect1LinkObjId="g_23ec0b0_0" Pin0InfoVect2LinkObjId="g_238aeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131255_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2962,-1268 2962,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23ecc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2992,-1347 2962,-1347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_23ec0b0@0" ObjectIDZND0="24060@x" ObjectIDZND1="24059@x" ObjectIDZND2="g_238aeb0@0" Pin0InfoVect0LinkObjId="SW-216188_0" Pin0InfoVect1LinkObjId="SW-131255_0" Pin0InfoVect2LinkObjId="g_238aeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23ec0b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2992,-1347 2962,-1347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23ece80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2962,-1283 2962,-1347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24060@x" ObjectIDND1="24059@x" ObjectIDZND0="g_23ec0b0@0" ObjectIDZND1="g_238aeb0@0" ObjectIDZND2="34576@1" Pin0InfoVect0LinkObjId="g_23ec0b0_0" Pin0InfoVect1LinkObjId="g_238aeb0_0" Pin0InfoVect2LinkObjId="g_23ed0a0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216188_0" Pin1InfoVect1LinkObjId="SW-131255_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2962,-1283 2962,-1347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23ed0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2962,-1347 2962,-1393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="24060@x" ObjectIDND1="24059@x" ObjectIDND2="g_23ec0b0@0" ObjectIDZND0="34576@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216188_0" Pin1InfoVect1LinkObjId="SW-131255_0" Pin1InfoVect2LinkObjId="g_23ec0b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2962,-1347 2962,-1393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243ed80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2962,-1347 2896,-1347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24060@x" ObjectIDND1="24059@x" ObjectIDND2="g_23ec0b0@0" ObjectIDZND0="g_238aeb0@0" Pin0InfoVect0LinkObjId="g_238aeb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216188_0" Pin1InfoVect1LinkObjId="SW-131255_0" Pin1InfoVect2LinkObjId="g_23ec0b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2962,-1347 2896,-1347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_249e690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2744,-1154 2752,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24048@1" ObjectIDZND0="g_249dd60@0" Pin0InfoVect0LinkObjId="g_249dd60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131205_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2744,-1154 2752,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23c0940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2745,-1217 2753,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24049@1" ObjectIDZND0="g_23c0010@0" Pin0InfoVect0LinkObjId="g_23c0010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2745,-1217 2753,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23c0b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1200 2688,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24045@1" ObjectIDZND0="24047@x" ObjectIDZND1="24049@x" Pin0InfoVect0LinkObjId="SW-131204_0" Pin0InfoVect1LinkObjId="SW-131206_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131202_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1200 2688,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23c0d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1217 2688,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24045@x" ObjectIDND1="24049@x" ObjectIDZND0="24047@0" Pin0InfoVect0LinkObjId="SW-131204_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131202_0" Pin1InfoVect1LinkObjId="SW-131206_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1217 2688,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_241f470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2709,-1282 2688,-1282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24050@0" ObjectIDZND0="24047@x" ObjectIDZND1="g_233af60@0" ObjectIDZND2="g_241fad0@0" Pin0InfoVect0LinkObjId="SW-131204_0" Pin0InfoVect1LinkObjId="g_233af60_0" Pin0InfoVect2LinkObjId="g_241fad0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2709,-1282 2688,-1282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_241f690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2745,-1282 2753,-1282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24050@1" ObjectIDZND0="g_241eb40@0" Pin0InfoVect0LinkObjId="g_241eb40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131207_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2745,-1282 2753,-1282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_241f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1267 2688,-1282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24047@1" ObjectIDZND0="24050@x" ObjectIDZND1="g_233af60@0" ObjectIDZND2="g_241fad0@0" Pin0InfoVect0LinkObjId="SW-131207_0" Pin0InfoVect1LinkObjId="g_233af60_0" Pin0InfoVect2LinkObjId="g_241fad0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131204_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1267 2688,-1282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2420680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2718,-1346 2688,-1346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_241fad0@0" ObjectIDZND0="g_238ba70@0" ObjectIDZND1="g_233af60@0" ObjectIDZND2="24047@x" Pin0InfoVect0LinkObjId="g_238ba70_0" Pin0InfoVect1LinkObjId="g_233af60_0" Pin0InfoVect2LinkObjId="SW-131204_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_241fad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2718,-1346 2688,-1346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24208a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1346 2688,-1389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_241fad0@0" ObjectIDND1="g_238ba70@0" ObjectIDND2="g_233af60@0" ObjectIDZND0="38076@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_241fad0_0" Pin1InfoVect1LinkObjId="g_238ba70_0" Pin1InfoVect2LinkObjId="g_233af60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1346 2688,-1389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2420ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1346 2554,-1346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_241fad0@0" ObjectIDND1="g_233af60@0" ObjectIDND2="24047@x" ObjectIDZND0="g_238ba70@0" Pin0InfoVect0LinkObjId="g_238ba70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_241fad0_0" Pin1InfoVect1LinkObjId="g_233af60_0" Pin1InfoVect2LinkObjId="SW-131204_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1346 2554,-1346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2420ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2708,-1154 2688,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24048@0" ObjectIDZND0="24045@x" ObjectIDZND1="24046@x" Pin0InfoVect0LinkObjId="SW-131202_0" Pin0InfoVect1LinkObjId="SW-131203_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131205_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2708,-1154 2688,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23395e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1173 2688,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24045@0" ObjectIDZND0="24048@x" ObjectIDZND1="24046@x" Pin0InfoVect0LinkObjId="SW-131205_0" Pin0InfoVect1LinkObjId="SW-131203_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1173 2688,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2339800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1154 2688,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24045@x" ObjectIDND1="24048@x" ObjectIDZND0="24046@1" Pin0InfoVect0LinkObjId="SW-131203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131202_0" Pin1InfoVect1LinkObjId="SW-131205_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1154 2688,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2339a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2709,-1217 2688,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24049@0" ObjectIDZND0="24047@x" ObjectIDZND1="24045@x" Pin0InfoVect0LinkObjId="SW-131204_0" Pin0InfoVect1LinkObjId="SW-131202_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2709,-1217 2688,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_233b9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2604,-1268 2604,-1249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_233af60@1" ObjectIDZND0="24106@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_233af60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2604,-1268 2604,-1249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_238a6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2962,-1201 2962,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24057@1" ObjectIDZND0="24059@0" Pin0InfoVect0LinkObjId="SW-131255_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131253_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2962,-1201 2962,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_238a8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2962,-1174 2962,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24057@0" ObjectIDZND0="24058@1" Pin0InfoVect0LinkObjId="SW-131254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131253_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2962,-1174 2962,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f0740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2616,-1031 2624,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24062@1" ObjectIDZND0="g_23a4b70@0" Pin0InfoVect0LinkObjId="g_23a4b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2616,-1031 2624,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f09a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2580,-1031 2560,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="24062@0" ObjectIDZND0="24042@0" ObjectIDZND1="24061@x" Pin0InfoVect0LinkObjId="g_23d7af0_0" Pin0InfoVect1LinkObjId="SW-131270_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2580,-1031 2560,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f1470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2560,-1086 2560,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24042@0" ObjectIDZND0="24062@x" ObjectIDZND1="24061@x" Pin0InfoVect0LinkObjId="SW-131271_0" Pin0InfoVect1LinkObjId="SW-131270_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23f09a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2560,-1086 2560,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f16b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2560,-1031 2560,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="24062@x" ObjectIDND1="24042@0" ObjectIDZND0="24061@1" Pin0InfoVect0LinkObjId="SW-131270_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131271_0" Pin1InfoVect1LinkObjId="g_23f09a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2560,-1031 2560,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f24e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2170,-452 2170,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_23cc830@0" ObjectIDZND0="29449@0" Pin0InfoVect0LinkObjId="SW-193627_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23cc830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2170,-452 2170,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242e7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3019,-447 3019,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_23ccf40@0" ObjectIDZND0="29450@0" Pin0InfoVect0LinkObjId="SW-193630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23ccf40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3019,-447 3019,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23cd8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2109,-378 2109,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="24077@0" ObjectIDZND0="33949@x" ObjectIDZND1="g_23f1910@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.052Ld_0" Pin0InfoVect1LinkObjId="g_23f1910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2109,-378 2109,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23cdb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2109,-285 2109,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33949@0" ObjectIDZND0="24077@x" ObjectIDZND1="g_23f1910@0" Pin0InfoVect0LinkObjId="SW-131385_0" Pin0InfoVect1LinkObjId="g_23f1910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-NH_DSY.052Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2109,-285 2109,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23cdd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2109,-304 2142,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33949@x" ObjectIDND1="24077@x" ObjectIDZND0="g_23f1910@0" Pin0InfoVect0LinkObjId="g_23f1910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_DSY.052Ld_0" Pin1InfoVect1LinkObjId="SW-131385_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2109,-304 2142,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2418180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2356,-478 2356,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_24177b0@1" ObjectIDZND0="g_2405d60@0" Pin0InfoVect0LinkObjId="g_2405d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24177b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2356,-478 2356,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e9240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-475 2667,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_23e8870@1" ObjectIDZND0="g_24183e0@0" Pin0InfoVect0LinkObjId="g_24183e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23e8870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-475 2667,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245a810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3175,-446 3175,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_245b190@0" ObjectIDZND0="29452@0" Pin0InfoVect0LinkObjId="SW-193636_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_245b190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3175,-446 3175,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_236e280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3457,-453 3457,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_236ee60@0" ObjectIDZND0="29454@0" Pin0InfoVect0LinkObjId="SW-193642_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_236ee60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3457,-453 3457,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_236ec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3429,-314 3394,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_245bb20@0" ObjectIDZND0="24097@x" ObjectIDZND1="33955@x" Pin0InfoVect0LinkObjId="SW-131455_0" Pin0InfoVect1LinkObjId="EC-NH_DSY.057Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_245bb20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3429,-314 3394,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23716c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2386,-497 2386,-528 2356,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2416a40@0" ObjectIDZND0="g_24177b0@0" ObjectIDZND1="29467@x" Pin0InfoVect0LinkObjId="g_24177b0_0" Pin0InfoVect1LinkObjId="SW-216288_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2416a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2386,-497 2386,-528 2356,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23364a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2882,-447 2882,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2376350@0" ObjectIDZND0="29462@0" Pin0InfoVect0LinkObjId="SW-216366_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2882,-447 2882,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2338630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1744,-421 1744,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="41913@0" ObjectIDZND0="24101@0" Pin0InfoVect0LinkObjId="SW-131469_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-NH_DSY.NH_DSY_1C_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1744,-421 1744,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2338820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1809,-452 1809,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_23735d0@0" ObjectIDZND0="29474@0" Pin0InfoVect0LinkObjId="SW-193665_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23735d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1809,-452 1809,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2372ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2356,-528 2356,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2416a40@0" ObjectIDND1="29467@x" ObjectIDZND0="g_24177b0@0" Pin0InfoVect0LinkObjId="g_24177b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2416a40_0" Pin1InfoVect1LinkObjId="SW-216288_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2356,-528 2356,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2373f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2719,-511 2719,-528 2667,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_23e7b00@0" ObjectIDZND0="g_23e8870@0" ObjectIDZND1="29469@x" Pin0InfoVect0LinkObjId="g_23e8870_0" Pin0InfoVect1LinkObjId="SW-216307_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23e7b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-511 2719,-528 2667,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2374a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-556 2667,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29469@0" ObjectIDZND0="g_23e7b00@0" ObjectIDZND1="g_23e8870@0" Pin0InfoVect0LinkObjId="g_23e7b00_0" Pin0InfoVect1LinkObjId="g_23e8870_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-556 2667,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2374cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-528 2667,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_23e7b00@0" ObjectIDND1="29469@x" ObjectIDZND0="g_23e8870@0" Pin0InfoVect0LinkObjId="g_23e8870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23e7b00_0" Pin1InfoVect1LinkObjId="SW-216307_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-528 2667,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2375820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2813,-349 2813,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="41914@0" ObjectIDZND0="32180@x" ObjectIDZND1="29462@x" ObjectIDZND2="g_22e2b10@0" Pin0InfoVect0LinkObjId="SW-216247_0" Pin0InfoVect1LinkObjId="SW-216366_0" Pin0InfoVect2LinkObjId="g_22e2b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-NH_DSY.NH_DSY_2C_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2813,-349 2813,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2376ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-307 2220,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_23f2720@0" ObjectIDND1="24085@x" ObjectIDZND0="33951@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.054Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23f2720_0" Pin1InfoVect1LinkObjId="SW-131413_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-307 2220,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c2e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2254,-306 2220,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_23f2720@0" ObjectIDZND0="33951@x" ObjectIDZND1="24085@x" Pin0InfoVect0LinkObjId="EC-NH_DSY.054Ld_0" Pin0InfoVect1LinkObjId="SW-131413_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23f2720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2254,-306 2220,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c3070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-306 2220,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33951@x" ObjectIDND1="g_23f2720@0" ObjectIDZND0="24085@0" Pin0InfoVect0LinkObjId="SW-131413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_DSY.054Ld_0" Pin1InfoVect1LinkObjId="g_23f2720_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-306 2220,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c3ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2984,-314 2953,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_23c9170@0" ObjectIDZND0="33950@x" ObjectIDZND1="24081@x" Pin0InfoVect0LinkObjId="EC-NH_DSY.053Ld_0" Pin0InfoVect1LinkObjId="SW-131399_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23c9170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2984,-314 2953,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c3e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-315 3111,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_238ff60@0" ObjectIDND1="24089@x" ObjectIDZND0="33952@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.055Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_238ff60_0" Pin1InfoVect1LinkObjId="SW-131427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-315 3111,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c4930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3158,-315 3111,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_238ff60@0" ObjectIDZND0="33952@x" ObjectIDZND1="24089@x" Pin0InfoVect0LinkObjId="EC-NH_DSY.055Ld_0" Pin0InfoVect1LinkObjId="SW-131427_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_238ff60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3158,-315 3111,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c4b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-340 3111,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="24089@0" ObjectIDZND0="33952@x" ObjectIDZND1="g_238ff60@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.055Ld_0" Pin0InfoVect1LinkObjId="g_238ff60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-340 3111,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c56c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-341 3393,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="24097@0" ObjectIDZND0="33955@x" ObjectIDZND1="g_245bb20@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.057Ld_0" Pin0InfoVect1LinkObjId="g_245bb20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131455_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-341 3393,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c5920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-314 3393,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24097@x" ObjectIDND1="g_245bb20@0" ObjectIDZND0="33955@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.057Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131455_0" Pin1InfoVect1LinkObjId="g_245bb20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-314 3393,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2393bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2953,-282 2953,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33950@0" ObjectIDZND0="g_23c9170@0" ObjectIDZND1="24081@x" Pin0InfoVect0LinkObjId="g_23c9170_0" Pin0InfoVect1LinkObjId="SW-131399_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-NH_DSY.053Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2953,-282 2953,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2369de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1936,-449 1936,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_236a760@0" ObjectIDZND0="29448@0" Pin0InfoVect0LinkObjId="SW-193624_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_236a760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1936,-449 1936,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_236b0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-373 1872,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="24073@0" ObjectIDZND0="33948@x" ObjectIDZND1="g_23afbc0@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.051Ld_0" Pin0InfoVect1LinkObjId="g_23afbc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131371_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-373 1872,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23af7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-304 1872,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24073@x" ObjectIDND1="g_23afbc0@0" ObjectIDZND0="33948@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.051Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131371_0" Pin1InfoVect1LinkObjId="g_23afbc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-304 1872,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23af9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1910,-304 1872,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_23afbc0@0" ObjectIDZND0="33948@x" ObjectIDZND1="24073@x" Pin0InfoVect0LinkObjId="EC-NH_DSY.051Ld_0" Pin0InfoVect1LinkObjId="SW-131371_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23afbc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1910,-304 1872,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f3d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,-304 2000,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_23f4240@0" ObjectIDND1="29460@x" ObjectIDZND0="33954@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.050Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23f4240_0" Pin1InfoVect1LinkObjId="SW-216223_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-304 2000,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f3fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2038,-304 2000,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_23f4240@0" ObjectIDZND0="33954@x" ObjectIDZND1="29460@x" Pin0InfoVect0LinkObjId="EC-NH_DSY.050Ld_0" Pin0InfoVect1LinkObjId="SW-216223_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23f4240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2038,-304 2000,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f7720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2064,-469 2064,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29475@0" ObjectIDZND0="g_23f80a0@0" Pin0InfoVect0LinkObjId="g_23f80a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2064,-469 2064,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f8a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,-373 2000,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="29460@0" ObjectIDZND0="g_23f4240@0" ObjectIDZND1="33954@x" Pin0InfoVect0LinkObjId="g_23f4240_0" Pin0InfoVect1LinkObjId="EC-NH_DSY.050Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216223_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-373 2000,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2452140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3313,-454 3313,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2452d20@0" ObjectIDZND0="29453@0" Pin0InfoVect0LinkObjId="SW-193639_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2452d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3313,-454 3313,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2452ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3285,-315 3250,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2453b70@0" ObjectIDZND0="24093@x" ObjectIDZND1="33953@x" Pin0InfoVect0LinkObjId="SW-131441_0" Pin0InfoVect1LinkObjId="EC-NH_DSY.056Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2453b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3285,-315 3250,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24536b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3249,-340 3249,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24093@0" ObjectIDZND0="g_2453b70@0" ObjectIDZND1="33953@x" Pin0InfoVect0LinkObjId="g_2453b70_0" Pin0InfoVect1LinkObjId="EC-NH_DSY.056Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131441_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3249,-340 3249,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2453910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3249,-315 3249,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24093@x" ObjectIDND1="g_2453b70@0" ObjectIDZND0="33953@0" Pin0InfoVect0LinkObjId="EC-NH_DSY.056Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131441_0" Pin1InfoVect1LinkObjId="g_2453b70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3249,-315 3249,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23d7af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-1104 2309,-1086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24052@0" ObjectIDZND0="24042@0" Pin0InfoVect0LinkObjId="g_23f09a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-1104 2309,-1086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23da510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1103 2688,-1086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24046@0" ObjectIDZND0="24042@0" Pin0InfoVect0LinkObjId="g_23f09a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1103 2688,-1086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_237c9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2605,-1310 2605,-1321 2688,-1321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_233af60@0" ObjectIDZND0="24047@x" ObjectIDZND1="24050@x" ObjectIDZND2="g_241fad0@0" Pin0InfoVect0LinkObjId="SW-131204_0" Pin0InfoVect1LinkObjId="SW-131207_0" Pin0InfoVect2LinkObjId="g_241fad0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_233af60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2605,-1310 2605,-1321 2688,-1321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_237d320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1282 2688,-1321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24047@x" ObjectIDND1="24050@x" ObjectIDZND0="g_233af60@0" ObjectIDZND1="g_241fad0@0" ObjectIDZND2="g_238ba70@0" Pin0InfoVect0LinkObjId="g_233af60_0" Pin0InfoVect1LinkObjId="g_241fad0_0" Pin0InfoVect2LinkObjId="g_238ba70_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131204_0" Pin1InfoVect1LinkObjId="SW-131207_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1282 2688,-1321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_237d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1321 2688,-1346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_233af60@0" ObjectIDND1="24047@x" ObjectIDND2="24050@x" ObjectIDZND0="g_241fad0@0" ObjectIDZND1="g_238ba70@0" ObjectIDZND2="38076@1" Pin0InfoVect0LinkObjId="g_241fad0_0" Pin0InfoVect1LinkObjId="g_238ba70_0" Pin0InfoVect2LinkObjId="g_24208a0_1" Pin0Num="3" Pin1InfoVect0LinkObjId="g_233af60_0" Pin1InfoVect1LinkObjId="SW-131204_0" Pin1InfoVect2LinkObjId="SW-131207_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1321 2688,-1346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e5e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2609,-611 2609,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32186@1" ObjectIDZND0="29464@1" Pin0InfoVect0LinkObjId="SW-193903_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216257_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2609,-611 2609,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e6060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2609,-573 2609,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29464@0" ObjectIDZND0="32187@1" Pin0InfoVect0LinkObjId="SW-216257_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193903_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2609,-573 2609,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2355600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1744,-528 1744,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_230c590@0" ObjectIDND1="g_230d430@0" ObjectIDND2="29474@x" ObjectIDZND0="32168@0" Pin0InfoVect0LinkObjId="SW-216206_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_230c590_0" Pin1InfoVect1LinkObjId="g_230d430_0" Pin1InfoVect2LinkObjId="SW-193665_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1744,-528 1744,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23557f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-529 1872,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_230f370@0" ObjectIDND1="29448@x" ObjectIDND2="g_2310df0@0" ObjectIDZND0="32151@0" Pin0InfoVect0LinkObjId="SW-216192_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_230f370_0" Pin1InfoVect1LinkObjId="SW-193624_0" Pin1InfoVect2LinkObjId="g_2310df0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-529 1872,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23559e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,-566 2000,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="32164@0" ObjectIDZND0="g_2311ed0@0" ObjectIDZND1="g_2312fb0@0" ObjectIDZND2="29475@x" Pin0InfoVect0LinkObjId="g_2311ed0_0" Pin0InfoVect1LinkObjId="g_2312fb0_0" Pin0InfoVect2LinkObjId="SW-193654_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-566 2000,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2355c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-565 2220,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="32157@0" ObjectIDZND0="g_22d52b0@0" ObjectIDZND1="g_22d6390@0" ObjectIDZND2="29451@x" Pin0InfoVect0LinkObjId="g_22d52b0_0" Pin0InfoVect1LinkObjId="g_22d6390_0" Pin0InfoVect2LinkObjId="SW-193633_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216195_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-565 2220,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2355e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1744,-656 1744,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32169@0" ObjectIDZND0="24043@0" Pin0InfoVect0LinkObjId="g_2356070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1744,-656 1744,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2356070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-656 1872,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32150@0" ObjectIDZND0="24043@0" Pin0InfoVect0LinkObjId="g_2355e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-656 1872,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23562a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,-657 2000,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32166@0" ObjectIDZND0="24043@0" Pin0InfoVect0LinkObjId="g_2355e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-657 2000,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23564d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-659 2220,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32156@0" ObjectIDZND0="24043@0" Pin0InfoVect0LinkObjId="g_2355e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216195_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-659 2220,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2356730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2813,-528 2813,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="41914@x" ObjectIDND1="29462@x" ObjectIDND2="g_22e2b10@0" ObjectIDZND0="32180@0" Pin0InfoVect0LinkObjId="SW-216247_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="CB-NH_DSY.NH_DSY_2C_0" Pin1InfoVect1LinkObjId="SW-216366_0" Pin1InfoVect2LinkObjId="g_22e2b10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2813,-528 2813,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2356990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2813,-651 2813,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32182@0" ObjectIDZND0="29459@0" Pin0InfoVect0LinkObjId="g_2356bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216247_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2813,-651 2813,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2356bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2954,-656 2954,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32154@0" ObjectIDZND0="29459@0" Pin0InfoVect0LinkObjId="g_2356990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2954,-656 2954,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2356e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-654 3111,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32158@0" ObjectIDZND0="29459@0" Pin0InfoVect0LinkObjId="g_2356990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-654 3111,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23570b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3249,-661 3249,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32160@0" ObjectIDZND0="29459@0" Pin0InfoVect0LinkObjId="g_2356990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216197_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3249,-661 3249,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2357310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-659 3393,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32162@0" ObjectIDZND0="29459@0" Pin0InfoVect0LinkObjId="g_2356990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216198_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-659 3393,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2357570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2954,-528 2954,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2323970@0" ObjectIDND1="29450@x" ObjectIDND2="g_232afe0@0" ObjectIDZND0="32155@0" Pin0InfoVect0LinkObjId="SW-216194_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2323970_0" Pin1InfoVect1LinkObjId="SW-193630_0" Pin1InfoVect2LinkObjId="g_232afe0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2954,-528 2954,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23577d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-526 3111,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_23258f0@0" ObjectIDND1="29452@x" ObjectIDND2="g_232bec0@0" ObjectIDZND0="32159@0" Pin0InfoVect0LinkObjId="SW-216196_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23258f0_0" Pin1InfoVect1LinkObjId="SW-193636_0" Pin1InfoVect2LinkObjId="g_232bec0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-526 3111,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2357a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3249,-529 3249,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_23273b0@0" ObjectIDND1="29453@x" ObjectIDND2="g_232cda0@0" ObjectIDZND0="32161@0" Pin0InfoVect0LinkObjId="SW-216197_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23273b0_0" Pin1InfoVect1LinkObjId="SW-193639_0" Pin1InfoVect2LinkObjId="g_232cda0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3249,-529 3249,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2357c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-528 3393,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2322ca0@0" ObjectIDND1="29454@x" ObjectIDND2="g_232a5c0@0" ObjectIDZND0="32163@0" Pin0InfoVect0LinkObjId="SW-216198_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2322ca0_0" Pin1InfoVect1LinkObjId="SW-193642_0" Pin1InfoVect2LinkObjId="g_232a5c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-528 3393,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d26c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-625 2667,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29469@1" ObjectIDZND0="29459@0" Pin0InfoVect0LinkObjId="g_2356990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-625 2667,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d4000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2430,-643 2430,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="24043@0" Pin0InfoVect0LinkObjId="g_2355e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2430,-643 2430,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d4260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2609,-628 2609,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32186@0" ObjectIDZND0="29459@0" Pin0InfoVect0LinkObjId="g_2356990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216257_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2609,-628 2609,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2303b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2308,-858 2308,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_23d4a90@1" ObjectIDZND0="24104@0" Pin0InfoVect0LinkObjId="g_2412a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23d4a90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2308,-858 2308,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2303dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2308,-820 2271,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_23d4a90@0" ObjectIDND1="32172@x" ObjectIDZND0="g_23d56b0@0" Pin0InfoVect0LinkObjId="g_23d56b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23d4a90_0" Pin1InfoVect1LinkObjId="SW-216208_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2308,-820 2271,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23048a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2308,-820 2308,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_23d56b0@0" ObjectIDND1="32172@x" ObjectIDZND0="g_23d4a90@0" Pin0InfoVect0LinkObjId="g_23d4a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23d56b0_0" Pin1InfoVect1LinkObjId="SW-216208_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2308,-820 2308,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230c3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2773,-874 2774,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="24105@0" ObjectIDZND0="g_2304b00@1" Pin0InfoVect0LinkObjId="g_2304b00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24ad400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2773,-874 2774,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230cf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1744,-473 1744,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24101@1" ObjectIDZND0="g_230c590@0" Pin0InfoVect0LinkObjId="g_230c590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1744,-473 1744,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230d1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1744,-520 1744,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_230c590@1" ObjectIDZND0="g_230d430@0" ObjectIDZND1="29474@x" ObjectIDZND2="32168@x" Pin0InfoVect0LinkObjId="g_230d430_0" Pin0InfoVect1LinkObjId="SW-193665_0" Pin0InfoVect2LinkObjId="SW-216206_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230c590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1744,-520 1744,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230e160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,-513 1777,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_230d430@0" ObjectIDZND0="29474@x" ObjectIDZND1="g_230c590@0" ObjectIDZND2="32168@x" Pin0InfoVect0LinkObjId="SW-193665_0" Pin0InfoVect1LinkObjId="g_230c590_0" Pin0InfoVect2LinkObjId="SW-216206_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230d430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1777,-513 1777,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230ec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1809,-515 1809,-528 1777,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29474@1" ObjectIDZND0="g_230d430@0" ObjectIDZND1="g_230c590@0" ObjectIDZND2="32168@x" Pin0InfoVect0LinkObjId="g_230d430_0" Pin0InfoVect1LinkObjId="g_230c590_0" Pin0InfoVect2LinkObjId="SW-216206_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1809,-515 1809,-528 1777,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230eeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,-528 1744,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_230d430@0" ObjectIDND1="29474@x" ObjectIDZND0="g_230c590@0" ObjectIDZND1="32168@x" Pin0InfoVect0LinkObjId="g_230c590_0" Pin0InfoVect1LinkObjId="SW-216206_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_230d430_0" Pin1InfoVect1LinkObjId="SW-193665_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1777,-528 1744,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1905,-514 1905,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_230f370@0" ObjectIDZND0="32151@x" ObjectIDZND1="g_2310df0@0" ObjectIDZND2="29448@x" Pin0InfoVect0LinkObjId="SW-216192_0" Pin0InfoVect1LinkObjId="g_2310df0_0" Pin0InfoVect2LinkObjId="SW-193624_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230f370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1905,-514 1905,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2310930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-528 1905,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="32151@x" ObjectIDND1="g_2310df0@0" ObjectIDZND0="g_230f370@0" ObjectIDZND1="29448@x" Pin0InfoVect0LinkObjId="g_230f370_0" Pin0InfoVect1LinkObjId="SW-193624_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216192_0" Pin1InfoVect1LinkObjId="g_2310df0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-528 1905,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2310b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1905,-528 1936,-528 1936,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_230f370@0" ObjectIDND1="32151@x" ObjectIDND2="g_2310df0@0" ObjectIDZND0="29448@1" Pin0InfoVect0LinkObjId="SW-193624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_230f370_0" Pin1InfoVect1LinkObjId="SW-216192_0" Pin1InfoVect2LinkObjId="g_2310df0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1905,-528 1936,-528 1936,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2311a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-410 1872,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24073@1" ObjectIDZND0="g_2310df0@0" Pin0InfoVect0LinkObjId="g_2310df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131371_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-410 1872,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2311c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-515 1872,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2310df0@1" ObjectIDZND0="32151@x" ObjectIDZND1="g_230f370@0" ObjectIDZND2="29448@x" Pin0InfoVect0LinkObjId="SW-216192_0" Pin0InfoVect1LinkObjId="g_230f370_0" Pin0InfoVect2LinkObjId="SW-193624_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2310df0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-515 1872,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2312af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,-529 2000,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="32164@x" ObjectIDND1="g_2312fb0@0" ObjectIDND2="29475@x" ObjectIDZND0="g_2311ed0@1" Pin0InfoVect0LinkObjId="g_2311ed0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216204_0" Pin1InfoVect1LinkObjId="g_2312fb0_0" Pin1InfoVect2LinkObjId="SW-193654_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-529 2000,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2312d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,-473 2000,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2311ed0@0" ObjectIDZND0="29460@1" Pin0InfoVect0LinkObjId="SW-216223_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2311ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-473 2000,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d16c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2036,-514 2036,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2312fb0@0" ObjectIDZND0="29475@x" ObjectIDZND1="32164@x" ObjectIDZND2="g_2311ed0@0" Pin0InfoVect0LinkObjId="SW-193654_0" Pin0InfoVect1LinkObjId="SW-216204_0" Pin0InfoVect2LinkObjId="g_2311ed0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2312fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2036,-514 2036,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d2160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2064,-505 2064,-529 2036,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29475@1" ObjectIDZND0="g_2312fb0@0" ObjectIDZND1="32164@x" ObjectIDZND2="g_2311ed0@0" Pin0InfoVect0LinkObjId="g_2312fb0_0" Pin0InfoVect1LinkObjId="SW-216204_0" Pin0InfoVect2LinkObjId="g_2311ed0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2064,-505 2064,-529 2036,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d23c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2036,-529 2000,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2312fb0@0" ObjectIDND1="29475@x" ObjectIDZND0="32164@x" ObjectIDZND1="g_2311ed0@0" Pin0InfoVect0LinkObjId="SW-216204_0" Pin0InfoVect1LinkObjId="g_2311ed0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2312fb0_0" Pin1InfoVect1LinkObjId="SW-193654_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2036,-529 2000,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d3110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2109,-529 2109,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_22d3830@0" ObjectIDND1="29449@x" ObjectIDND2="32153@x" ObjectIDZND0="g_22d2620@1" Pin0InfoVect0LinkObjId="g_22d2620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22d3830_0" Pin1InfoVect1LinkObjId="SW-193627_0" Pin1InfoVect2LinkObjId="SW-216193_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2109,-529 2109,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d3370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2109,-472 2109,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_22d2620@0" ObjectIDZND0="24077@1" Pin0InfoVect0LinkObjId="SW-131385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22d2620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2109,-472 2109,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d35d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2140,-513 2140,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_22d3830@0" ObjectIDZND0="29449@x" ObjectIDZND1="g_22d2620@0" ObjectIDZND2="32153@x" Pin0InfoVect0LinkObjId="SW-193627_0" Pin0InfoVect1LinkObjId="g_22d2620_0" Pin0InfoVect2LinkObjId="SW-216193_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22d3830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2140,-513 2140,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d4df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2170,-512 2170,-529 2140,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29449@1" ObjectIDZND0="g_22d3830@0" ObjectIDZND1="g_22d2620@0" ObjectIDZND2="32153@x" Pin0InfoVect0LinkObjId="g_22d3830_0" Pin0InfoVect1LinkObjId="g_22d2620_0" Pin0InfoVect2LinkObjId="SW-216193_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193627_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2170,-512 2170,-529 2140,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d5050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2140,-529 2110,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_22d3830@0" ObjectIDND1="29449@x" ObjectIDZND0="g_22d2620@0" ObjectIDZND1="32153@x" Pin0InfoVect0LinkObjId="g_22d2620_0" Pin0InfoVect1LinkObjId="SW-216193_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22d3830_0" Pin1InfoVect1LinkObjId="SW-193627_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2140,-529 2110,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d5ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-528 2220,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="32157@x" ObjectIDND1="g_22d6390@0" ObjectIDND2="29451@x" ObjectIDZND0="g_22d52b0@1" Pin0InfoVect0LinkObjId="g_22d52b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216195_0" Pin1InfoVect1LinkObjId="g_22d6390_0" Pin1InfoVect2LinkObjId="SW-193633_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-528 2220,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d6130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-472 2220,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_22d52b0@0" ObjectIDZND0="24085@1" Pin0InfoVect0LinkObjId="SW-131413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22d52b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-472 2220,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d70c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2254,-514 2254,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_22d6390@0" ObjectIDZND0="29451@x" ObjectIDZND1="32157@x" ObjectIDZND2="g_22d52b0@0" Pin0InfoVect0LinkObjId="SW-193633_0" Pin0InfoVect1LinkObjId="SW-216195_0" Pin0InfoVect2LinkObjId="g_22d52b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22d6390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2254,-514 2254,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d7bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2286,-512 2286,-528 2254,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29451@1" ObjectIDZND0="g_22d6390@0" ObjectIDZND1="32157@x" ObjectIDZND2="g_22d52b0@0" Pin0InfoVect0LinkObjId="g_22d6390_0" Pin0InfoVect1LinkObjId="SW-216195_0" Pin0InfoVect2LinkObjId="g_22d52b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193633_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2286,-512 2286,-528 2254,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d7e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2254,-528 2220,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_22d6390@0" ObjectIDND1="29451@x" ObjectIDZND0="32157@x" ObjectIDZND1="g_22d52b0@0" Pin0InfoVect0LinkObjId="SW-216195_0" Pin0InfoVect1LinkObjId="g_22d52b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22d6390_0" Pin1InfoVect1LinkObjId="SW-193633_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2254,-528 2220,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22dca60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2356,-528 2356,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2416a40@0" ObjectIDND1="g_24177b0@0" ObjectIDZND0="29467@0" Pin0InfoVect0LinkObjId="SW-216288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2416a40_0" Pin1InfoVect1LinkObjId="g_24177b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2356,-528 2356,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22dccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2356,-661 2356,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29467@1" ObjectIDZND0="24043@0" Pin0InfoVect0LinkObjId="g_2355e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2356,-661 2356,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e2180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2480,-701 2480,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24043@0" ObjectIDZND0="32185@1" Pin0InfoVect0LinkObjId="SW-216256_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2355e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2480,-701 2480,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e2370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2480,-553 2480,-528 2609,-528 2609,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="32185@0" ObjectIDZND0="32187@0" Pin0InfoVect0LinkObjId="SW-216257_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2480,-553 2480,-528 2609,-528 2609,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e35a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2842,-528 2842,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="29462@x" ObjectIDND1="41914@x" ObjectIDND2="32180@x" ObjectIDZND0="g_22e2b10@0" Pin0InfoVect0LinkObjId="g_22e2b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216366_0" Pin1InfoVect1LinkObjId="CB-NH_DSY.NH_DSY_2C_0" Pin1InfoVect2LinkObjId="SW-216247_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2842,-528 2842,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2322580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2882,-503 2882,-528 2842,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29462@1" ObjectIDZND0="41914@x" ObjectIDZND1="32180@x" ObjectIDZND2="g_22e2b10@0" Pin0InfoVect0LinkObjId="CB-NH_DSY.NH_DSY_2C_0" Pin0InfoVect1LinkObjId="SW-216247_0" Pin0InfoVect2LinkObjId="g_22e2b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216366_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2882,-503 2882,-528 2842,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23227e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2842,-528 2813,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="29462@x" ObjectIDND1="g_22e2b10@0" ObjectIDZND0="41914@x" ObjectIDZND1="32180@x" Pin0InfoVect0LinkObjId="CB-NH_DSY.NH_DSY_2C_0" Pin0InfoVect1LinkObjId="SW-216247_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216366_0" Pin1InfoVect1LinkObjId="g_22e2b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2842,-528 2813,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2322a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3425,-500 3425,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2322ca0@0" ObjectIDZND0="29454@x" ObjectIDZND1="32163@x" ObjectIDZND2="g_232a5c0@0" Pin0InfoVect0LinkObjId="SW-193642_0" Pin0InfoVect1LinkObjId="SW-216198_0" Pin0InfoVect2LinkObjId="g_232a5c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2322ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3425,-500 3425,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23246e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2985,-500 2985,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2323970@0" ObjectIDZND0="29450@x" ObjectIDZND1="32155@x" ObjectIDZND2="g_232afe0@0" Pin0InfoVect0LinkObjId="SW-193630_0" Pin0InfoVect1LinkObjId="SW-216194_0" Pin0InfoVect2LinkObjId="g_232afe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2323970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2985,-500 2985,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23251d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3019,-504 3019,-528 2985,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29450@1" ObjectIDZND0="g_2323970@0" ObjectIDZND1="32155@x" ObjectIDZND2="g_232afe0@0" Pin0InfoVect0LinkObjId="g_2323970_0" Pin0InfoVect1LinkObjId="SW-216194_0" Pin0InfoVect2LinkObjId="g_232afe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3019,-504 3019,-528 2985,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2325430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2985,-528 2954,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2323970@0" ObjectIDND1="29450@x" ObjectIDZND0="32155@x" ObjectIDZND1="g_232afe0@0" Pin0InfoVect0LinkObjId="SW-216194_0" Pin0InfoVect1LinkObjId="g_232afe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2323970_0" Pin1InfoVect1LinkObjId="SW-193630_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2985,-528 2954,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2325690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3143,-500 3143,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_23258f0@0" ObjectIDZND0="32159@x" ObjectIDZND1="g_232bec0@0" ObjectIDZND2="29452@x" Pin0InfoVect0LinkObjId="SW-216196_0" Pin0InfoVect1LinkObjId="g_232bec0_0" Pin0InfoVect2LinkObjId="SW-193636_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23258f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3143,-500 3143,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2326ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-528 3143,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="32159@x" ObjectIDND1="g_232bec0@0" ObjectIDZND0="g_23258f0@0" ObjectIDZND1="29452@x" Pin0InfoVect0LinkObjId="g_23258f0_0" Pin0InfoVect1LinkObjId="SW-193636_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216196_0" Pin1InfoVect1LinkObjId="g_232bec0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-528 3143,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2327150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3143,-528 3175,-528 3175,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_23258f0@0" ObjectIDND1="32159@x" ObjectIDND2="g_232bec0@0" ObjectIDZND0="29452@1" Pin0InfoVect0LinkObjId="SW-193636_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23258f0_0" Pin1InfoVect1LinkObjId="SW-216196_0" Pin1InfoVect2LinkObjId="g_232bec0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3143,-528 3175,-528 3175,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2328120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3277,-502 3277,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_23273b0@0" ObjectIDZND0="29453@x" ObjectIDZND1="32161@x" ObjectIDZND2="g_232cda0@0" Pin0InfoVect0LinkObjId="SW-193639_0" Pin0InfoVect1LinkObjId="SW-216197_0" Pin0InfoVect2LinkObjId="g_232cda0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23273b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3277,-502 3277,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2328c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3313,-510 3313,-529 3277,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29453@1" ObjectIDZND0="g_23273b0@0" ObjectIDZND1="32161@x" ObjectIDZND2="g_232cda0@0" Pin0InfoVect0LinkObjId="g_23273b0_0" Pin0InfoVect1LinkObjId="SW-216197_0" Pin0InfoVect2LinkObjId="g_232cda0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193639_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3313,-510 3313,-529 3277,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2328e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3277,-529 3249,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_23273b0@0" ObjectIDND1="29453@x" ObjectIDZND0="32161@x" ObjectIDZND1="g_232cda0@0" Pin0InfoVect0LinkObjId="SW-216197_0" Pin0InfoVect1LinkObjId="g_232cda0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23273b0_0" Pin1InfoVect1LinkObjId="SW-193639_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3277,-529 3249,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2329960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3457,-509 3457,-528 3425,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29454@1" ObjectIDZND0="g_2322ca0@0" ObjectIDZND1="32163@x" ObjectIDZND2="g_232a5c0@0" Pin0InfoVect0LinkObjId="g_2322ca0_0" Pin0InfoVect1LinkObjId="SW-216198_0" Pin0InfoVect2LinkObjId="g_232a5c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193642_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3457,-509 3457,-528 3425,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2329bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3425,-528 3393,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2322ca0@0" ObjectIDND1="29454@x" ObjectIDZND0="32163@x" ObjectIDZND1="g_232a5c0@0" Pin0InfoVect0LinkObjId="SW-216198_0" Pin0InfoVect1LinkObjId="g_232a5c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2322ca0_0" Pin1InfoVect1LinkObjId="SW-193642_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3425,-528 3393,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232a360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2953,-314 2953,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_23c9170@0" ObjectIDND1="33950@x" ObjectIDZND0="24081@0" Pin0InfoVect0LinkObjId="SW-131399_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23c9170_0" Pin1InfoVect1LinkObjId="EC-NH_DSY.053Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2953,-314 2953,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232ba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2954,-380 2954,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24081@1" ObjectIDZND0="g_232afe0@0" Pin0InfoVect0LinkObjId="g_232afe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131399_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2954,-380 2954,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232bc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2954,-452 2954,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_232afe0@1" ObjectIDZND0="32155@x" ObjectIDZND1="g_2323970@0" ObjectIDZND2="29450@x" Pin0InfoVect0LinkObjId="SW-216194_0" Pin0InfoVect1LinkObjId="g_2323970_0" Pin0InfoVect2LinkObjId="SW-193630_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_232afe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2954,-452 2954,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232c8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-376 3111,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24089@1" ObjectIDZND0="g_232bec0@0" Pin0InfoVect0LinkObjId="g_232bec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131427_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-376 3111,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232cb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-456 3111,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_232bec0@1" ObjectIDZND0="32159@x" ObjectIDZND1="g_23258f0@0" ObjectIDZND2="29452@x" Pin0InfoVect0LinkObjId="SW-216196_0" Pin0InfoVect1LinkObjId="g_23258f0_0" Pin0InfoVect2LinkObjId="SW-193636_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_232bec0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-456 3111,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232d7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3249,-377 3249,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24093@1" ObjectIDZND0="g_232cda0@0" Pin0InfoVect0LinkObjId="g_232cda0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3249,-377 3249,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232da20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3249,-460 3249,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_232cda0@1" ObjectIDZND0="32161@x" ObjectIDZND1="g_23273b0@0" ObjectIDZND2="29453@x" Pin0InfoVect0LinkObjId="SW-216197_0" Pin0InfoVect1LinkObjId="g_23273b0_0" Pin0InfoVect2LinkObjId="SW-193639_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_232cda0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3249,-460 3249,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232dc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-378 3393,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24097@1" ObjectIDZND0="g_232a5c0@0" Pin0InfoVect0LinkObjId="g_232a5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131455_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-378 3393,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232dee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-465 3393,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_232a5c0@1" ObjectIDZND0="32163@x" ObjectIDZND1="g_2322ca0@0" ObjectIDZND2="29454@x" Pin0InfoVect0LinkObjId="SW-216198_0" Pin0InfoVect1LinkObjId="g_2322ca0_0" Pin0InfoVect2LinkObjId="SW-193642_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_232a5c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-465 3393,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24fb850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2109,-701 2109,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24043@0" ObjectIDZND0="32152@0" Pin0InfoVect0LinkObjId="SW-216193_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2355e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2109,-701 2109,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24fbab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2109,-640 2109,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32152@1" ObjectIDZND0="24075@1" Pin0InfoVect0LinkObjId="SW-131383_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216193_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2109,-640 2109,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24fbd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2109,-596 2109,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24075@0" ObjectIDZND0="32153@1" Pin0InfoVect0LinkObjId="SW-216193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2109,-596 2109,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24fbf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2109,-565 2109,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="32153@0" ObjectIDZND0="g_22d2620@0" ObjectIDZND1="g_22d3830@0" ObjectIDZND2="29449@x" Pin0InfoVect0LinkObjId="g_22d2620_0" Pin0InfoVect1LinkObjId="g_22d3830_0" Pin0InfoVect2LinkObjId="SW-193627_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2109,-565 2109,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2505280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2955,-641 2955,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32154@1" ObjectIDZND0="24079@1" Pin0InfoVect0LinkObjId="SW-131397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216194_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2955,-641 2955,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25054e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2955,-597 2955,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24079@0" ObjectIDZND0="32155@1" Pin0InfoVect0LinkObjId="SW-216194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2955,-597 2955,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250d690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-639 1872,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32150@1" ObjectIDZND0="24071@1" Pin0InfoVect0LinkObjId="SW-131369_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216192_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-639 1872,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250d8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-595 1872,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24071@0" ObjectIDZND0="32151@1" Pin0InfoVect0LinkObjId="SW-216192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-595 1872,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e9890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,-642 2000,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32166@1" ObjectIDZND0="29456@1" Pin0InfoVect0LinkObjId="SW-216202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216204_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-642 2000,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e9af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,-598 2000,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29456@0" ObjectIDZND0="32164@1" Pin0InfoVect0LinkObjId="SW-216204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-598 2000,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f1ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-642 2220,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32156@1" ObjectIDZND0="24083@1" Pin0InfoVect0LinkObjId="SW-131411_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216195_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-642 2220,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f1f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-598 2220,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24083@0" ObjectIDZND0="32157@1" Pin0InfoVect0LinkObjId="SW-216195_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-598 2220,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22fa0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2814,-634 2814,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32182@1" ObjectIDZND0="24102@1" Pin0InfoVect0LinkObjId="SW-131480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216247_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2814,-634 2814,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22fa350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2814,-590 2814,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24102@0" ObjectIDZND0="32180@1" Pin0InfoVect0LinkObjId="SW-216247_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2814,-590 2814,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2342ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-637 3111,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32158@1" ObjectIDZND0="24087@1" Pin0InfoVect0LinkObjId="SW-131425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216196_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-637 3111,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2343250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-593 3111,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24087@0" ObjectIDZND0="32159@1" Pin0InfoVect0LinkObjId="SW-216196_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-593 3111,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_234b400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3249,-645 3249,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32160@1" ObjectIDZND0="24091@1" Pin0InfoVect0LinkObjId="SW-131439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216197_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3249,-645 3249,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_234b660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3249,-601 3249,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24091@0" ObjectIDZND0="32161@1" Pin0InfoVect0LinkObjId="SW-216197_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3249,-601 3249,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2353810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-644 3393,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32162@1" ObjectIDZND0="24095@1" Pin0InfoVect0LinkObjId="SW-131453_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216198_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-644 3393,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2353a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-600 3393,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24095@0" ObjectIDZND0="32163@1" Pin0InfoVect0LinkObjId="SW-216198_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-600 3393,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229f980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1744,-640 1744,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32169@1" ObjectIDZND0="24099@1" Pin0InfoVect0LinkObjId="SW-131467_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1744,-640 1744,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229fbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1744,-596 1744,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24099@0" ObjectIDZND0="32168@1" Pin0InfoVect0LinkObjId="SW-216206_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131467_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1744,-596 1744,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c9830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2308,-780 2308,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24065@1" ObjectIDZND0="32172@1" Pin0InfoVect0LinkObjId="SW-216208_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131296_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2308,-780 2308,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c9a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2308,-807 2308,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="32172@0" ObjectIDZND0="g_23d4a90@0" ObjectIDZND1="g_23d56b0@0" Pin0InfoVect0LinkObjId="g_23d4a90_0" Pin0InfoVect1LinkObjId="g_23d56b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2308,-807 2308,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c9cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2308,-701 2308,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24043@0" ObjectIDZND0="32170@0" Pin0InfoVect0LinkObjId="SW-216208_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2355e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2308,-701 2308,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2308,-732 2308,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32170@1" ObjectIDZND0="24065@0" Pin0InfoVect0LinkObjId="SW-131296_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2308,-732 2308,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25cb6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2774,-1086 2774,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24042@0" ObjectIDZND0="24068@1" Pin0InfoVect0LinkObjId="SW-131332_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23f09a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2774,-1086 2774,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25cbf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2430,-535 2455,-535 2455,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_2404210@0" Pin0InfoVect0LinkObjId="g_2404210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_241c570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2430,-535 2455,-535 2455,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25cca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2430,-553 2430,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="g_2404210@0" Pin0InfoVect0LinkObjId="g_241c570_0" Pin0InfoVect1LinkObjId="g_2404210_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2430,-553 2430,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25cccb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2430,-535 2430,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2404210@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_241c570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2404210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2430,-535 2430,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a1f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2774,-723 2774,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="24069@1" ObjectIDZND0="29459@0" Pin0InfoVect0LinkObjId="g_2356990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2774,-723 2774,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a21c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2738,-811 2774,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_23056e0@0" ObjectIDZND0="24069@x" ObjectIDZND1="g_2304b00@0" Pin0InfoVect0LinkObjId="SW-131336_0" Pin0InfoVect1LinkObjId="g_2304b00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23056e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2738,-811 2774,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a225f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2774,-785 2774,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="24069@0" ObjectIDZND0="g_23056e0@0" ObjectIDZND1="g_2304b00@0" Pin0InfoVect0LinkObjId="g_23056e0_0" Pin0InfoVect1LinkObjId="g_2304b00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2774,-785 2774,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a227e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2774,-811 2774,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_23056e0@0" ObjectIDND1="24069@x" ObjectIDZND0="g_2304b00@0" Pin0InfoVect0LinkObjId="g_2304b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23056e0_0" Pin1InfoVect1LinkObjId="SW-131336_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2774,-811 2774,-832 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.221894 -208.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24071"/>
     <cge:Term_Ref ObjectID="33935"/>
    <cge:TPSR_Ref TObjectID="24071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.221894 -208.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24071"/>
     <cge:Term_Ref ObjectID="33935"/>
    <cge:TPSR_Ref TObjectID="24071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.221894 -208.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24071"/>
     <cge:Term_Ref ObjectID="33935"/>
    <cge:TPSR_Ref TObjectID="24071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2135.257512 -209.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24075"/>
     <cge:Term_Ref ObjectID="33943"/>
    <cge:TPSR_Ref TObjectID="24075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2135.257512 -209.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24075"/>
     <cge:Term_Ref ObjectID="33943"/>
    <cge:TPSR_Ref TObjectID="24075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2135.257512 -209.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24075"/>
     <cge:Term_Ref ObjectID="33943"/>
    <cge:TPSR_Ref TObjectID="24075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.925893 -211.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24083"/>
     <cge:Term_Ref ObjectID="33959"/>
    <cge:TPSR_Ref TObjectID="24083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131167" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.925893 -211.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131167" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24083"/>
     <cge:Term_Ref ObjectID="33959"/>
    <cge:TPSR_Ref TObjectID="24083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.925893 -211.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24083"/>
     <cge:Term_Ref ObjectID="33959"/>
    <cge:TPSR_Ref TObjectID="24083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2436.536635 -1018.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24063"/>
     <cge:Term_Ref ObjectID="33919"/>
    <cge:TPSR_Ref TObjectID="24063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131116" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2436.536635 -1018.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24063"/>
     <cge:Term_Ref ObjectID="33919"/>
    <cge:TPSR_Ref TObjectID="24063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2436.536635 -1018.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24063"/>
     <cge:Term_Ref ObjectID="33919"/>
    <cge:TPSR_Ref TObjectID="24063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2442.536635 -861.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24065"/>
     <cge:Term_Ref ObjectID="33923"/>
    <cge:TPSR_Ref TObjectID="24065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2442.536635 -861.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24065"/>
     <cge:Term_Ref ObjectID="33923"/>
    <cge:TPSR_Ref TObjectID="24065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131134" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2442.536635 -861.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24065"/>
     <cge:Term_Ref ObjectID="33923"/>
    <cge:TPSR_Ref TObjectID="24065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131126" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2875.340002 -1011.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24067"/>
     <cge:Term_Ref ObjectID="33927"/>
    <cge:TPSR_Ref TObjectID="24067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131127" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2875.340002 -1011.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24067"/>
     <cge:Term_Ref ObjectID="33927"/>
    <cge:TPSR_Ref TObjectID="24067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131123" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2875.340002 -1011.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24067"/>
     <cge:Term_Ref ObjectID="33927"/>
    <cge:TPSR_Ref TObjectID="24067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131090" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2844.000000 -1208.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131090" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24045"/>
     <cge:Term_Ref ObjectID="33883"/>
    <cge:TPSR_Ref TObjectID="24045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2844.000000 -1208.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24045"/>
     <cge:Term_Ref ObjectID="33883"/>
    <cge:TPSR_Ref TObjectID="24045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2844.000000 -1208.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24045"/>
     <cge:Term_Ref ObjectID="33883"/>
    <cge:TPSR_Ref TObjectID="24045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3082.000000 -1211.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24057"/>
     <cge:Term_Ref ObjectID="33907"/>
    <cge:TPSR_Ref TObjectID="24057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3082.000000 -1211.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24057"/>
     <cge:Term_Ref ObjectID="33907"/>
    <cge:TPSR_Ref TObjectID="24057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3082.000000 -1211.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24057"/>
     <cge:Term_Ref ObjectID="33907"/>
    <cge:TPSR_Ref TObjectID="24057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-131129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2885.340002 -906.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24105"/>
     <cge:Term_Ref ObjectID="34008"/>
    <cge:TPSR_Ref TObjectID="24105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-131130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2885.340002 -906.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24105"/>
     <cge:Term_Ref ObjectID="34008"/>
    <cge:TPSR_Ref TObjectID="24105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-131118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2445.536635 -920.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24104"/>
     <cge:Term_Ref ObjectID="34004"/>
    <cge:TPSR_Ref TObjectID="24104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-131119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2445.536635 -920.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24104"/>
     <cge:Term_Ref ObjectID="34004"/>
    <cge:TPSR_Ref TObjectID="24104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2885.340002 -850.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24069"/>
     <cge:Term_Ref ObjectID="33931"/>
    <cge:TPSR_Ref TObjectID="24069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2885.340002 -850.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24069"/>
     <cge:Term_Ref ObjectID="33931"/>
    <cge:TPSR_Ref TObjectID="24069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2885.340002 -850.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24069"/>
     <cge:Term_Ref ObjectID="33931"/>
    <cge:TPSR_Ref TObjectID="24069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3418.000000 -199.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24095"/>
     <cge:Term_Ref ObjectID="33983"/>
    <cge:TPSR_Ref TObjectID="24095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131182" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3418.000000 -199.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24095"/>
     <cge:Term_Ref ObjectID="33983"/>
    <cge:TPSR_Ref TObjectID="24095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3418.000000 -199.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24095"/>
     <cge:Term_Ref ObjectID="33983"/>
    <cge:TPSR_Ref TObjectID="24095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3279.000000 -201.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24091"/>
     <cge:Term_Ref ObjectID="33975"/>
    <cge:TPSR_Ref TObjectID="24091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131177" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3279.000000 -201.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24091"/>
     <cge:Term_Ref ObjectID="33975"/>
    <cge:TPSR_Ref TObjectID="24091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131174" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3279.000000 -201.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24091"/>
     <cge:Term_Ref ObjectID="33975"/>
    <cge:TPSR_Ref TObjectID="24091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-194056" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2536.000000 -614.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29464"/>
     <cge:Term_Ref ObjectID="41975"/>
    <cge:TPSR_Ref TObjectID="29464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-194057" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2536.000000 -614.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29464"/>
     <cge:Term_Ref ObjectID="41975"/>
    <cge:TPSR_Ref TObjectID="29464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-194054" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2536.000000 -614.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29464"/>
     <cge:Term_Ref ObjectID="41975"/>
    <cge:TPSR_Ref TObjectID="29464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -202.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24079"/>
     <cge:Term_Ref ObjectID="33951"/>
    <cge:TPSR_Ref TObjectID="24079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -202.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24079"/>
     <cge:Term_Ref ObjectID="33951"/>
    <cge:TPSR_Ref TObjectID="24079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -202.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24079"/>
     <cge:Term_Ref ObjectID="33951"/>
    <cge:TPSR_Ref TObjectID="24079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131186" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2018.000000 -209.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131186" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29456"/>
     <cge:Term_Ref ObjectID="41962"/>
    <cge:TPSR_Ref TObjectID="29456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2018.000000 -209.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29456"/>
     <cge:Term_Ref ObjectID="41962"/>
    <cge:TPSR_Ref TObjectID="29456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131184" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2018.000000 -209.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131184" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29456"/>
     <cge:Term_Ref ObjectID="41962"/>
    <cge:TPSR_Ref TObjectID="29456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -201.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131171" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24087"/>
     <cge:Term_Ref ObjectID="33967"/>
    <cge:TPSR_Ref TObjectID="24087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131172" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -201.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131172" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24087"/>
     <cge:Term_Ref ObjectID="33967"/>
    <cge:TPSR_Ref TObjectID="24087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -201.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24087"/>
     <cge:Term_Ref ObjectID="33967"/>
    <cge:TPSR_Ref TObjectID="24087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-194040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 -793.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24043"/>
     <cge:Term_Ref ObjectID="33880"/>
    <cge:TPSR_Ref TObjectID="24043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-194041" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 -793.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24043"/>
     <cge:Term_Ref ObjectID="33880"/>
    <cge:TPSR_Ref TObjectID="24043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-194042" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 -793.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194042" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24043"/>
     <cge:Term_Ref ObjectID="33880"/>
    <cge:TPSR_Ref TObjectID="24043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-194046" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 -793.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24043"/>
     <cge:Term_Ref ObjectID="33880"/>
    <cge:TPSR_Ref TObjectID="24043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-194043" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 -793.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24043"/>
     <cge:Term_Ref ObjectID="33880"/>
    <cge:TPSR_Ref TObjectID="24043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-194047" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3370.000000 -779.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194047" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29459"/>
     <cge:Term_Ref ObjectID="41968"/>
    <cge:TPSR_Ref TObjectID="29459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-194048" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3370.000000 -779.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194048" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29459"/>
     <cge:Term_Ref ObjectID="41968"/>
    <cge:TPSR_Ref TObjectID="29459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-194049" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3370.000000 -779.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194049" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29459"/>
     <cge:Term_Ref ObjectID="41968"/>
    <cge:TPSR_Ref TObjectID="29459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-194053" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3370.000000 -779.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194053" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29459"/>
     <cge:Term_Ref ObjectID="41968"/>
    <cge:TPSR_Ref TObjectID="29459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-194050" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3370.000000 -779.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194050" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29459"/>
     <cge:Term_Ref ObjectID="41968"/>
    <cge:TPSR_Ref TObjectID="29459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-131095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2443.000000 -1211.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24051"/>
     <cge:Term_Ref ObjectID="33895"/>
    <cge:TPSR_Ref TObjectID="24051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-131096" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2443.000000 -1211.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24051"/>
     <cge:Term_Ref ObjectID="33895"/>
    <cge:TPSR_Ref TObjectID="24051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-131093" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2443.000000 -1211.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131093" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24051"/>
     <cge:Term_Ref ObjectID="33895"/>
    <cge:TPSR_Ref TObjectID="24051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-131105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1999.000000 -1134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24042"/>
     <cge:Term_Ref ObjectID="33879"/>
    <cge:TPSR_Ref TObjectID="24042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-131103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1999.000000 -1134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24042"/>
     <cge:Term_Ref ObjectID="33879"/>
    <cge:TPSR_Ref TObjectID="24042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-131104" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1999.000000 -1134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24042"/>
     <cge:Term_Ref ObjectID="33879"/>
    <cge:TPSR_Ref TObjectID="24042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131191" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1721.000000 -213.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131191" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24099"/>
     <cge:Term_Ref ObjectID="33991"/>
    <cge:TPSR_Ref TObjectID="24099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131192" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1721.000000 -213.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131192" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24099"/>
     <cge:Term_Ref ObjectID="33991"/>
    <cge:TPSR_Ref TObjectID="24099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1721.000000 -213.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24099"/>
     <cge:Term_Ref ObjectID="33991"/>
    <cge:TPSR_Ref TObjectID="24099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194037" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2805.000000 -231.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24102"/>
     <cge:Term_Ref ObjectID="33997"/>
    <cge:TPSR_Ref TObjectID="24102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194038" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2805.000000 -231.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24102"/>
     <cge:Term_Ref ObjectID="33997"/>
    <cge:TPSR_Ref TObjectID="24102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-194035" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2805.000000 -231.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194035" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24102"/>
     <cge:Term_Ref ObjectID="33997"/>
    <cge:TPSR_Ref TObjectID="24102"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="1709,-1277 1706,-1280 1706,-1227 1709,-1230 1709,-1277" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="1709,-1277 1706,-1280 1757,-1280 1754,-1277 1709,-1277" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="1709,-1230 1706,-1227 1757,-1227 1754,-1230 1709,-1230" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="1754,-1277 1757,-1280 1757,-1227 1754,-1230 1754,-1277" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="1709" y="-1277"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="1709" y="-1277"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="1469" y="-1388"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="1469" y="-1388"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1416" y="-1405"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1416" y="-1405"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3123" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3123" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3402" y="-622"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3402" y="-622"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2120" y="-615"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2120" y="-615"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1882" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1882" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2010" y="-615"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2010" y="-615"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2230" y="-615"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2230" y="-615"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2965" y="-618"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2965" y="-618"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3258" y="-621"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3258" y="-621"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2826" y="-612"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2826" y="-612"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="1756" y="-612"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="1756" y="-612"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="2619" y="-594"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="2619" y="-594"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="62" x="1368" y="-961"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="62" x="1368" y="-961"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2318" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2318" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2700" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2700" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2975" y="-1193"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2975" y="-1193"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1692" y="-1345"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1692" y="-1345"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1692" y="-1384"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1692" y="-1384"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2213" y="-926"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2213" y="-926"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="2684" y="-936"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="2684" y="-936"/></g>
  </g><g id="MotifButton_Layer">
   <g href="AVC大蛇腰站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="1709" y="-1277"/></g>
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="1469" y="-1388"/></g>
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1416" y="-1405"/></g>
   <g href="35kV大蛇腰变NH_DSY_055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3123" y="-614"/></g>
   <g href="35kV大蛇腰变NH_DSY_057间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3402" y="-622"/></g>
   <g href="35kV大蛇腰变NH_DSY_052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2120" y="-615"/></g>
   <g href="35kV大蛇腰变NH_DSY_051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1882" y="-614"/></g>
   <g href="35kV大蛇腰变NH_DSY_050间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2010" y="-615"/></g>
   <g href="35kV大蛇腰变NH_DSY_054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2230" y="-615"/></g>
   <g href="35kV大蛇腰变NH_DSY_053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2965" y="-618"/></g>
   <g href="35kV大蛇腰变NH_DSY_056间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3258" y="-621"/></g>
   <g href="35kV大蛇腰变NH_DSY_059间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2826" y="-612"/></g>
   <g href="35kV大蛇腰变NH_DSY_058间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="1756" y="-612"/></g>
   <g href="35kV大蛇腰变NH_DSY_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="2619" y="-594"/></g>
   <g href="35kV大蛇腰变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="62" x="1368" y="-961"/></g>
   <g href="35kV大蛇腰变NH_DSY_363间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2318" y="-1195"/></g>
   <g href="35kV大蛇腰变NH_DSY_362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2700" y="-1195"/></g>
   <g href="35kV大蛇腰变NH_DSY_361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2975" y="-1193"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1692" y="-1345"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1692" y="-1384"/></g>
   <g href="35kV大蛇腰变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2213" y="-926"/></g>
   <g href="35kV大蛇腰变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="2684" y="-936"/></g>
  </g><g id="Rectangle_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(173,255,47)" stroke-width="1" width="16" x="2422" y="-615"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NH_DSY.NH_DSY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1719,-701 2501,-701 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24043" ObjectName="BS-NH_DSY.NH_DSY_9IM"/>
    <cge:TPSR_Ref TObjectID="24043"/></metadata>
   <polyline fill="none" opacity="0" points="1719,-701 2501,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_DSY.NH_DSY_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2583,-695 3455,-695 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29459" ObjectName="BS-NH_DSY.NH_DSY_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="29459"/></metadata>
   <polyline fill="none" opacity="0" points="2583,-695 3455,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_DSY.NH_DSY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,-1086 3130,-1086 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24042" ObjectName="BS-NH_DSY.NH_DSY_3IM"/>
    <cge:TPSR_Ref TObjectID="24042"/></metadata>
   <polyline fill="none" opacity="0" points="2001,-1086 3130,-1086 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-NH_DSY.NH_DSY_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34011"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 0.602041 -0.891304 -0.000000 2618.000000 -1252.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.000000 0.602041 -0.891304 -0.000000 2618.000000 -1252.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24106" ObjectName="TF-NH_DSY.NH_DSY_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2445.095473 -521.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2445.095473 -521.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_DSY.NH_DSY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34007"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.918367 2739.000000 -870.000000)" xlink:href="#transformer2:shape39_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.918367 2739.000000 -870.000000)" xlink:href="#transformer2:shape39_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24105" ObjectName="TF-NH_DSY.NH_DSY_2T"/>
    <cge:TPSR_Ref TObjectID="24105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_DSY.NH_DSY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34003"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.918367 2274.000000 -866.000000)" xlink:href="#transformer2:shape39_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.918367 2274.000000 -866.000000)" xlink:href="#transformer2:shape39_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24104" ObjectName="TF-NH_DSY.NH_DSY_1T"/>
    <cge:TPSR_Ref TObjectID="24104"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1448.500000 -1327.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1450.000000 -1328.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217892" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1478.000000 -1232.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217892" ObjectName="NH_DSY:NH_DSY_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217892" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1478.000000 -1192.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217892" ObjectName="NH_DSY:NH_DSY_sumP1"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-131034" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1608.000000 -1258.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24022" ObjectName="DYN-NH_DSY"/>
     <cge:Meas_Ref ObjectId="131034"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239a450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1789.000000 734.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239b6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1797.000000 764.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239bc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1797.000000 779.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239bec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1797.000000 795.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239c100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1796.000000 749.250000) translate(0,12)">U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2439860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2384.000000 1016.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243a490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2373.000000 1001.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243ad10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2398.000000 986.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24064c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2384.000000 860.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2406670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2373.000000 845.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2406820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2398.000000 830.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24075e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2389.000000 1212.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24078b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2378.000000 1197.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2407af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2403.000000 1182.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2408910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2373.000000 903.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24094f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2373.000000 918.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23700b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3303.000000 718.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2370360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3311.000000 748.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23705a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3311.000000 763.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23707e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3311.000000 779.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2370a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3310.000000 733.250000) translate(0,12)">U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2337050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1840.000000 210.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2337530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1829.000000 195.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2337770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1854.000000 180.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_237ba10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2811.000000 892.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_237bc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2811.000000 907.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_237c330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1928.000000 1121.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_237c560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1935.000000 1135.250000) translate(0,12)">U0(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_237c770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1928.000000 1105.000000) translate(0,12)">Ubc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.636364 -0.000000 0.000000 -0.500000 1086.545455 -185.500000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2724" x2="2713" y1="366" y2="347"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2713" x2="2702" y1="348" y2="366"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2703" x2="2724" y1="367" y2="367"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2724" x2="2713" y1="366" y2="347"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2353f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2784.000000 1208.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2354560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2773.000000 1193.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23547a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2798.000000 1178.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2354bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3026.000000 1210.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2354e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3015.000000 1195.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23550c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3040.000000 1180.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228bbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1959.000000 208.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228be50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1948.000000 193.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228c090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1973.000000 178.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228c4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2076.000000 210.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228c730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2065.000000 195.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228c970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2090.000000 180.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228cd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2192.000000 213.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228d010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2181.000000 198.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228d250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2206.000000 183.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228d670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2914.000000 200.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228d8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.000000 185.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228db30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2928.000000 170.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228df50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3081.000000 200.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228e1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3070.000000 185.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228e410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3095.000000 170.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228e830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3221.000000 201.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228eab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3210.000000 186.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228ecf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3235.000000 171.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228f110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3358.000000 200.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228f390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3347.000000 185.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228f5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3372.000000 170.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228ff80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 214.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2290200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1656.000000 199.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2290440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 184.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2290e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2751.000000 232.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22910e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2740.000000 217.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2291320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 202.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-NH_DSY.NH_DSY_2C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2793.623216 -286.000000)" xlink:href="#capacitor:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41914" ObjectName="CB-NH_DSY.NH_DSY_2C"/>
    <cge:TPSR_Ref TObjectID="41914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-NH_DSY.NH_DSY_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1719.000000 -312.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41913" ObjectName="CB-NH_DSY.NH_DSY_1C"/>
    <cge:TPSR_Ref TObjectID="41913"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24042" cx="2962" cy="-1086" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24042" cx="2560" cy="-1086" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24042" cx="2309" cy="-1086" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24042" cx="2309" cy="-1086" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24042" cx="2688" cy="-1086" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24043" cx="1872" cy="-700" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24043" cx="2000" cy="-701" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24043" cx="2220" cy="-701" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29459" cx="2813" cy="-695" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29459" cx="2954" cy="-695" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29459" cx="3111" cy="-695" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29459" cx="3249" cy="-695" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29459" cx="3393" cy="-695" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24043" cx="2356" cy="-701" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29459" cx="2667" cy="-695" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29459" cx="2609" cy="-695" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24043" cx="2109" cy="-701" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24043" cx="1744" cy="-701" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24043" cx="2308" cy="-701" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24042" cx="2774" cy="-1086" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29459" cx="2774" cy="-695" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24043" cx="2430" cy="-701" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-131291">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2299.536635 -964.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24063" ObjectName="SW-NH_DSY.NH_DSY_351BK"/>
     <cge:Meas_Ref ObjectId="131291"/>
    <cge:TPSR_Ref TObjectID="24063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.340002 -968.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24067" ObjectName="SW-NH_DSY.NH_DSY_352BK"/>
     <cge:Meas_Ref ObjectId="131331"/>
    <cge:TPSR_Ref TObjectID="24067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131235">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2300.000000 -1166.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24051" ObjectName="SW-NH_DSY.NH_DSY_363BK"/>
     <cge:Meas_Ref ObjectId="131235"/>
    <cge:TPSR_Ref TObjectID="24051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131253">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2953.000000 -1166.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24057" ObjectName="SW-NH_DSY.NH_DSY_361BK"/>
     <cge:Meas_Ref ObjectId="131253"/>
    <cge:TPSR_Ref TObjectID="24057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131202">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2679.000000 -1165.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24045" ObjectName="SW-NH_DSY.NH_DSY_362BK"/>
     <cge:Meas_Ref ObjectId="131202"/>
    <cge:TPSR_Ref TObjectID="24045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193903">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2600.000000 -565.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29464" ObjectName="SW-NH_DSY.NH_DSY_012BK"/>
     <cge:Meas_Ref ObjectId="193903"/>
    <cge:TPSR_Ref TObjectID="29464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131336">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.676471 2764.000000 -718.647059)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24069" ObjectName="SW-NH_DSY.NH_DSY_002BK"/>
     <cge:Meas_Ref ObjectId="131336"/>
    <cge:TPSR_Ref TObjectID="24069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131383">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2100.000000 -588.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24075" ObjectName="SW-NH_DSY.NH_DSY_052BK"/>
     <cge:Meas_Ref ObjectId="131383"/>
    <cge:TPSR_Ref TObjectID="24075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131397">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2946.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24079" ObjectName="SW-NH_DSY.NH_DSY_053BK"/>
     <cge:Meas_Ref ObjectId="131397"/>
    <cge:TPSR_Ref TObjectID="24079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131369">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1863.000000 -587.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24071" ObjectName="SW-NH_DSY.NH_DSY_051BK"/>
     <cge:Meas_Ref ObjectId="131369"/>
    <cge:TPSR_Ref TObjectID="24071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216202">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1991.000000 -590.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29456" ObjectName="SW-NH_DSY.NH_DSY_050BK"/>
     <cge:Meas_Ref ObjectId="216202"/>
    <cge:TPSR_Ref TObjectID="29456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131411">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2211.000000 -590.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24083" ObjectName="SW-NH_DSY.NH_DSY_054BK"/>
     <cge:Meas_Ref ObjectId="131411"/>
    <cge:TPSR_Ref TObjectID="24083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2805.000000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24102" ObjectName="SW-NH_DSY.NH_DSY_059BK"/>
     <cge:Meas_Ref ObjectId="131480"/>
    <cge:TPSR_Ref TObjectID="24102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131425">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3102.000000 -585.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24087" ObjectName="SW-NH_DSY.NH_DSY_055BK"/>
     <cge:Meas_Ref ObjectId="131425"/>
    <cge:TPSR_Ref TObjectID="24087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131439">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3240.000000 -593.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24091" ObjectName="SW-NH_DSY.NH_DSY_056BK"/>
     <cge:Meas_Ref ObjectId="131439"/>
    <cge:TPSR_Ref TObjectID="24091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3384.000000 -592.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24095" ObjectName="SW-NH_DSY.NH_DSY_057BK"/>
     <cge:Meas_Ref ObjectId="131453"/>
    <cge:TPSR_Ref TObjectID="24095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131467">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1735.000000 -588.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24099" ObjectName="SW-NH_DSY.NH_DSY_058BK"/>
     <cge:Meas_Ref ObjectId="131467"/>
    <cge:TPSR_Ref TObjectID="24099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131296">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2299.000000 -745.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24065" ObjectName="SW-NH_DSY.NH_DSY_001BK"/>
     <cge:Meas_Ref ObjectId="131296"/>
    <cge:TPSR_Ref TObjectID="24065"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_227c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24da070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1971.000000 -1073.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_24ae6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -1376.000000) translate(0,16)">大蛇腰变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24acf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1849.000000 -248.000000) translate(0,15)">五街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2402780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1978.000000 -245.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21266b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2088.000000 -240.000000) translate(0,15)">一街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24aff50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3079.000000 -241.000000) translate(0,15)">红土坡线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2491060" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2857.326733 -1269.000000) translate(0,15)">线路电压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2491060" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2857.326733 -1269.000000) translate(0,33)">互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2389940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1665.000000 -735.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2389e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2585.000000 -1380.000000) translate(0,15)">大蛇腰线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238a030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 -1382.000000) translate(0,15)">南黄大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238a240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -1380.000000) translate(0,15)">大红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_238a420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2514.000000 -1190.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238aa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2523.000000 -847.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238aa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2523.000000 -847.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2470a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2700.000000 -1306.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2470f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2700.000000 -1258.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2471130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2700.000000 -1239.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2471310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2700.000000 -1194.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24714f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2700.000000 -1175.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24716d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2700.000000 -1124.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24718b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2320.000000 -1305.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2471a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2318.000000 -1258.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2471c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2320.000000 -1238.000000) translate(0,12)">36360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2471e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2320.000000 -1193.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2472030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2320.000000 -1174.000000) translate(0,12)">36317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2472210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2317.000000 -1129.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24723f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -1306.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a2390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -1258.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a2570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -1194.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a2750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -1124.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23beaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2404.000000 -418.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2404e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2440.000000 -603.000000) translate(0,12)">0500</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24054c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2901.000000 -242.000000) translate(0,15)">一街II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_236f7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -730.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2370c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2184.000000 -239.000000) translate(0,15)">一街煤矿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23718b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2302.000000 -351.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23718b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2302.000000 -351.000000) translate(0,33)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2371bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2604.000000 -375.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2371bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2604.000000 -375.000000) translate(0,33)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2371e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2752.000000 -267.000000) translate(0,15)">10kV2号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2334b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -243.000000) translate(0,15)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2334f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3229.000000 -239.000000) translate(0,15)">大龙潭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2335900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3350.000000 -242.000000) translate(0,15)">野猪塘煤矿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23379b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3072.065176 -362.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2337bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3157.065176 -434.000000) translate(0,12)">05567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2337e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1756.233039 -612.000000) translate(0,12)">058</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2338210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1705.233039 -456.000000) translate(0,12)">0586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c6450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3402.000000 -622.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3356.000000 -363.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c6b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3439.000000 -440.000000) translate(0,12)">05767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c6dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3124.000000 -615.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2393da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2826.000000 -612.000000) translate(0,12)">059</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2394160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 -282.000000) translate(0,12)">05800</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2396290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2618.000000 -594.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2398700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2860.000000 -436.000000) translate(0,12)">05967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2398940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2672.000000 -595.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2398b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 -458.000000) translate(0,12)">05867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23aec90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2120.000000 -615.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23af220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2116.000000 -399.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23af460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2125.000000 -460.000000) translate(0,12)">05267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b2190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 -614.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b26d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1877.000000 -393.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b2910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1893.000000 -462.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a8720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2010.000000 -615.000000) translate(0,12)">050</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a8c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2007.000000 -397.000000) translate(0,12)">0506</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a8ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2020.000000 -462.000000) translate(0,12)">05067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a9220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 -615.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a9710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2227.000000 -397.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2243.000000 -462.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a9ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2965.000000 -618.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23aa200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2915.000000 -368.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23aa440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3001.000000 -436.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2455a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3213.000000 -363.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2455f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3294.000000 -442.000000) translate(0,12)">05667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2456180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -621.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d7210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2318.000000 -993.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d7460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2316.000000 -1055.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d7670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2784.000000 -997.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d78b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2781.000000 -1059.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23dbcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -961.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2377070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1702.000000 -1338.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2379650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1702.000000 -1377.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2379d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2156.000000 -902.000000) translate(0,15)">SZ11-5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2379d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2156.000000 -902.000000) translate(0,33)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_237b690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2624.000000 -909.000000) translate(0,15)">SZ11-3150kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_237b690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2624.000000 -909.000000) translate(0,33)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_23e6560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 -452.000000) translate(0,17)">7332229</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_230bd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2793.000000 -761.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22dcf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2366.000000 -634.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e1c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2488.000000 -594.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22e2560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2509.000000 -546.000000) translate(0,15)">10kV母联</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_232ed90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2567.000000 -989.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_232efd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2578.000000 -1057.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24fc7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.500000 -427.000000) translate(0,17)">4631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2291560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1341.000000 -385.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2291560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1341.000000 -385.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2293a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 -395.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2293a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 -395.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2293a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 -395.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_2296e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2213.000000 -926.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_22979a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2684.000000 -936.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_22a3490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1713.000000 -1263.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2333.000000 -775.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25ccf10" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2477.326733 -1271.000000) translate(0,15)">线路电压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25ccf10" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2477.326733 -1271.000000) translate(0,33)">互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25cd540" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2158.326733 -1279.000000) translate(0,15)">线路电压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25cd540" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2158.326733 -1279.000000) translate(0,33)">互感器</text>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21314b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2590.000000 -852.000000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2433770">
    <use class="BV-35KV" transform="matrix(-0.000000 0.933333 -1.000000 -0.000000 2393.500000 -1353.877778)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ec0b0">
    <use class="BV-35KV" transform="matrix(-0.000000 0.933333 -1.000000 -0.000000 3045.500000 -1353.877778)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_241fad0">
    <use class="BV-35KV" transform="matrix(-0.000000 0.933333 -1.000000 -0.000000 2772.500000 -1352.877778)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_233af60">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -0.945455 2599.000000 -1263.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_238aeb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2886.000000 -1275.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_238ba70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2544.000000 -1274.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_246fbc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2233.000000 -1275.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f1910">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2148.635290 -250.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f2720">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2261.303670 -252.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23c9170">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2990.988668 -260.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2404210">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2460.473251 -447.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2405d60">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2342.807020 -373.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2416a40">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2393.623894 -443.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24177b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2347.550216 -473.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24183e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2653.073369 -385.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e7b00">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2725.461671 -457.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e8870">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2657.745136 -470.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_238ff60">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3165.053845 -261.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_245bb20">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3436.350655 -260.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23afbc0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 1917.053845 -250.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f4240">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2045.053845 -250.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2453b70">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3291.350655 -261.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d4a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.816327 2298.000000 -822.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d56b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2264.000000 -766.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2304b00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.816327 2764.000000 -828.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23056e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2731.000000 -757.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_230c590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1734.000000 -476.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_230d430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1770.000000 -459.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_230f370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1898.000000 -460.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2310df0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1862.000000 -471.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2311ed0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1990.000000 -468.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2312fb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2029.000000 -460.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d2620">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2099.000000 -467.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d3830">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2133.000000 -459.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d52b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2210.000000 -467.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d6390">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2247.000000 -460.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e2b10">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2848.461671 -454.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2322ca0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3431.461671 -446.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2323970">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2991.461671 -446.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23258f0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3149.461671 -446.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23273b0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3283.461671 -448.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232a5c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 -407.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232afe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2949.000000 -394.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232bec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3106.000000 -398.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232cda0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3244.000000 -402.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="NH_DSY"/>
</svg>