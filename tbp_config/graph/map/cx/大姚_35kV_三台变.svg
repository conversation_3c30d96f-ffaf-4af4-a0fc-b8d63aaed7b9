<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-227" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-755 -1283 2147 1251">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape15">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="50" x2="47" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="41" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape163">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <polyline points="34,21 34,9 19,9 " stroke-width="1"/>
    <polyline points="35,33 35,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="29" y1="28" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="44" y1="28" y2="28"/>
    <rect height="9" stroke-width="1" width="5" x="32" y="21"/>
    <ellipse cx="8" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="7" x2="7" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="10" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="4" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="10" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="9" x2="9" y1="12" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="47" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="38" x2="20" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="13" x2="13" y1="48" y2="31"/>
    <circle cx="34" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="24" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="4" x2="13" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape95_0">
    <ellipse cx="14" cy="43" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="13" y1="38" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="13" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="9" y1="0" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="31" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="42" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="46" x2="28" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="40" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="42" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="38" y2="42"/>
   </symbol>
   <symbol id="transformer2:shape95_1">
    <polyline DF8003:Layer="PUBLIC" points="12,70 8,61 18,61 12,70 "/>
    <circle cx="13" cy="62" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.1743"/>
    <polyline points="58,100 64,100 " stroke-width="1.1743"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.1743"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c517c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_11d4330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c53490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c53d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c55040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c55c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c566c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c571e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c52570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c52570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c5a3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c5a3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c5c160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c5c160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1c5ce60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c5ea60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c5f650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c60410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c60d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c62410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c63010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c638b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c64070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c65150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c65ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c665c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c66f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c683d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c68ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c69ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c6ab90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c79390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c6c190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1c6d3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1c6e9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1261" width="2157" x="-760" y="-1288"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1390" x2="1390" y1="-578" y2="-563"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-186967">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.241796 -1011.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28328" ObjectName="SW-DY_ST.DY_ST_361BK"/>
     <cge:Meas_Ref ObjectId="186967"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186995">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -37.758204 -767.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28334" ObjectName="SW-DY_ST.DY_ST_301BK"/>
     <cge:Meas_Ref ObjectId="186995"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187025">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -39.758204 -601.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28337" ObjectName="SW-DY_ST.DY_ST_001BK"/>
     <cge:Meas_Ref ObjectId="187025"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187086">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.787729 -368.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28343" ObjectName="SW-DY_ST.DY_ST_062BK"/>
     <cge:Meas_Ref ObjectId="187086"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 41.212271 -368.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28346" ObjectName="SW-DY_ST.DY_ST_063BK"/>
     <cge:Meas_Ref ObjectId="187109"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.212271 -363.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28349" ObjectName="SW-DY_ST.DY_ST_064BK"/>
     <cge:Meas_Ref ObjectId="187132"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187201">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 445.000000 -405.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28358" ObjectName="SW-DY_ST.DY_ST_012BK"/>
     <cge:Meas_Ref ObjectId="187201"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187178">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.212271 -366.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28355" ObjectName="SW-DY_ST.DY_ST_082BK"/>
     <cge:Meas_Ref ObjectId="187178"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187155">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.212271 -364.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28352" ObjectName="SW-DY_ST.DY_ST_081BK"/>
     <cge:Meas_Ref ObjectId="187155"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.241796 -1009.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44758" ObjectName="SW-DY_ST.DY_ST_362BK"/>
     <cge:Meas_Ref ObjectId="285516"/>
    <cge:TPSR_Ref TObjectID="44758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285375">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.241796 -783.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44749" ObjectName="SW-DY_ST.DY_ST_302BK"/>
     <cge:Meas_Ref ObjectId="285375"/>
    <cge:TPSR_Ref TObjectID="44749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.241796 -594.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44750" ObjectName="SW-DY_ST.DY_ST_002BK"/>
     <cge:Meas_Ref ObjectId="285378"/>
    <cge:TPSR_Ref TObjectID="44750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285710">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1017.212271 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44764" ObjectName="SW-DY_ST.DY_ST_083BK"/>
     <cge:Meas_Ref ObjectId="285710"/>
    <cge:TPSR_Ref TObjectID="44764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1232.212271 -377.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44767" ObjectName="SW-DY_ST.DY_ST_084BK"/>
     <cge:Meas_Ref ObjectId="285760"/>
    <cge:TPSR_Ref TObjectID="44767"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_ST.DY_ST_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-299,-903 1330,-903 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28324" ObjectName="BS-DY_ST.DY_ST_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   <polyline fill="none" opacity="0" points="-299,-903 1330,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_ST.DY_ST_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-304,-512 424,-512 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28325" ObjectName="BS-DY_ST.DY_ST_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   <polyline fill="none" opacity="0" points="-304,-512 424,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_ST.DY_ST_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="493,-510 1318,-510 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28326" ObjectName="BS-DY_ST.DY_ST_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   <polyline fill="none" opacity="0" points="493,-510 1318,-510 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-DY_ST.DY_ST_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 -73.000000)" xlink:href="#capacitor:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44777" ObjectName="CB-DY_ST.DY_ST_Cb2"/>
    <cge:TPSR_Ref TObjectID="44777"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.304348 -0.000000 -0.000000 1.187500 725.000000 -1105.000000)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.304348 -0.000000 -0.000000 1.187500 725.000000 -1105.000000)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.152174 -0.000000 -0.000000 1.135802 1096.000000 -706.864198)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.152174 -0.000000 -0.000000 1.135802 1096.000000 -706.864198)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_ST.DY_ST_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="40278"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.862037 -0.000000 0.000000 -0.851573 -62.000000 -669.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.862037 -0.000000 0.000000 -0.851573 -62.000000 -669.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28361" ObjectName="TF-DY_ST.DY_ST_1T"/>
    <cge:TPSR_Ref TObjectID="28361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_ST.DY_ST_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="22351"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.862037 -0.000000 0.000000 -0.851573 801.000000 -646.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.862037 -0.000000 0.000000 -0.851573 801.000000 -646.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="44776" ObjectName="TF-DY_ST.DY_ST_2T"/>
    <cge:TPSR_Ref TObjectID="44776"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1259d70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -25.000000 -1167.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_117b3d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1001.000000 -1055.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_117c010">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 946.000000 -1065.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11f5080">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -195.323558 -214.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1108ad0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 78.676442 -214.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11dec60">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 334.676442 -209.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_110fef0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 862.676442 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_126bb00">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 683.676442 -210.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1213b80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.000000 -629.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1214480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 323.000000 -624.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ec4d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 -628.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ecdd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 545.000000 -623.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11d8390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 -1118.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11dadc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 570.000000 -691.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11cf700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.000000 -692.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1171280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 395.000000 -1165.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1174730">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -1158.000000)" xlink:href="#lightningRod:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1160dd0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1054.676442 -202.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12062b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 -259.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_121eaf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 -629.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1220780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.000000 -738.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-186879" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -599.000000 -1099.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186879" ObjectName="DY_ST:DY_ST_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-186879" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -598.000000 -1057.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186879" ObjectName="DY_ST:DY_ST_301BK_P"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 227.000000 -1259.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28328"/>
     <cge:Term_Ref ObjectID="40210"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 227.000000 -1259.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28328"/>
     <cge:Term_Ref ObjectID="40210"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 227.000000 -1259.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28328"/>
     <cge:Term_Ref ObjectID="40210"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -253.000000 -164.500000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28343"/>
     <cge:Term_Ref ObjectID="40240"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -253.000000 -164.500000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28343"/>
     <cge:Term_Ref ObjectID="40240"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -253.000000 -164.500000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28343"/>
     <cge:Term_Ref ObjectID="40240"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.000000 -164.500000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28346"/>
     <cge:Term_Ref ObjectID="40246"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.000000 -164.500000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28346"/>
     <cge:Term_Ref ObjectID="40246"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.000000 -164.500000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28346"/>
     <cge:Term_Ref ObjectID="40246"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -164.500000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28349"/>
     <cge:Term_Ref ObjectID="40252"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -164.500000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28349"/>
     <cge:Term_Ref ObjectID="40252"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -164.500000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28349"/>
     <cge:Term_Ref ObjectID="40252"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -164.500000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28352"/>
     <cge:Term_Ref ObjectID="40258"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -164.500000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28352"/>
     <cge:Term_Ref ObjectID="40258"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -164.500000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28352"/>
     <cge:Term_Ref ObjectID="40258"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -164.500000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28355"/>
     <cge:Term_Ref ObjectID="40264"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -164.500000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28355"/>
     <cge:Term_Ref ObjectID="40264"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -164.500000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28355"/>
     <cge:Term_Ref ObjectID="40264"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -398.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28358"/>
     <cge:Term_Ref ObjectID="40270"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-186940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -398.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28358"/>
     <cge:Term_Ref ObjectID="40270"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -398.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28358"/>
     <cge:Term_Ref ObjectID="40270"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -398.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28358"/>
     <cge:Term_Ref ObjectID="40270"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -113.000000 -629.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28337"/>
     <cge:Term_Ref ObjectID="40228"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -113.000000 -629.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28337"/>
     <cge:Term_Ref ObjectID="40228"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -113.000000 -629.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28337"/>
     <cge:Term_Ref ObjectID="40228"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -109.000000 -858.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28334"/>
     <cge:Term_Ref ObjectID="40222"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -109.000000 -858.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28334"/>
     <cge:Term_Ref ObjectID="40222"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -109.000000 -858.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28334"/>
     <cge:Term_Ref ObjectID="40222"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-186898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -666.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-186899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -666.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-186900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -666.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-186904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -666.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-186901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -666.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-186902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -666.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-186903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -666.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-186905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -666.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-186906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -659.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-186907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -659.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-186908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -659.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-186912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -659.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-186909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -659.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-186910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -659.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-186911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -659.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-186913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -659.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-186890" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -1052.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-186891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -1052.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-186892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -1052.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-186896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -1052.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-186893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -1052.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-186894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -1052.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-186895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -1052.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-186897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -1052.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-186889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 83.000000 -725.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28361"/>
     <cge:Term_Ref ObjectID="40279"/>
    <cge:TPSR_Ref TObjectID="28361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-186888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -704.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28361"/>
     <cge:Term_Ref ObjectID="40279"/>
    <cge:TPSR_Ref TObjectID="28361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-285325" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 948.000000 -704.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44776"/>
     <cge:Term_Ref ObjectID="22349"/>
    <cge:TPSR_Ref TObjectID="44776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-285324" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 948.000000 -704.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285324" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44776"/>
     <cge:Term_Ref ObjectID="22349"/>
    <cge:TPSR_Ref TObjectID="44776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-285312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -874.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44749"/>
     <cge:Term_Ref ObjectID="22301"/>
    <cge:TPSR_Ref TObjectID="44749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-285313" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -874.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285313" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44749"/>
     <cge:Term_Ref ObjectID="22301"/>
    <cge:TPSR_Ref TObjectID="44749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-285314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -874.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44749"/>
     <cge:Term_Ref ObjectID="22301"/>
    <cge:TPSR_Ref TObjectID="44749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-285321" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -632.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285321" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44750"/>
     <cge:Term_Ref ObjectID="22303"/>
    <cge:TPSR_Ref TObjectID="44750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-285322" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -632.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44750"/>
     <cge:Term_Ref ObjectID="22303"/>
    <cge:TPSR_Ref TObjectID="44750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-285318" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -632.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285318" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44750"/>
     <cge:Term_Ref ObjectID="22303"/>
    <cge:TPSR_Ref TObjectID="44750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-285363" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -190.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285363" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44767"/>
     <cge:Term_Ref ObjectID="22331"/>
    <cge:TPSR_Ref TObjectID="44767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-285364" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -190.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285364" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44767"/>
     <cge:Term_Ref ObjectID="22331"/>
    <cge:TPSR_Ref TObjectID="44767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-285357" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1007.000000 -160.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285357" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44764"/>
     <cge:Term_Ref ObjectID="22325"/>
    <cge:TPSR_Ref TObjectID="44764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-285358" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1007.000000 -160.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285358" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44764"/>
     <cge:Term_Ref ObjectID="22325"/>
    <cge:TPSR_Ref TObjectID="44764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-285359" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1007.000000 -160.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285359" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44764"/>
     <cge:Term_Ref ObjectID="22325"/>
    <cge:TPSR_Ref TObjectID="44764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-285368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 646.000000 -1235.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44758"/>
     <cge:Term_Ref ObjectID="22313"/>
    <cge:TPSR_Ref TObjectID="44758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-285369" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 646.000000 -1235.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285369" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44758"/>
     <cge:Term_Ref ObjectID="22313"/>
    <cge:TPSR_Ref TObjectID="44758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-285370" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 646.000000 -1235.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285370" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44758"/>
     <cge:Term_Ref ObjectID="22313"/>
    <cge:TPSR_Ref TObjectID="44758"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-606" y="-1252"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="31" y="-1040"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="31" y="-1040"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-215" y="-397"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-215" y="-397"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="60" y="-397"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="60" y="-397"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="316" y="-392"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="316" y="-392"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="844" y="-395"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="844" y="-395"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="455" y="-439"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="455" y="-439"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-457" y="-1232"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-457" y="-1232"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-457" y="-1267"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-457" y="-1267"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="-127" y="-739"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="-127" y="-739"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-323" y="-1247"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-323" y="-1247"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="451" y="-1038"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="451" y="-1038"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1036" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1036" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="717" y="-751"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="717" y="-751"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1251" y="-406"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1251" y="-406"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="665" y="-393"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="665" y="-393"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="23" qtmmishow="hidden" width="76" x="-707" y="-877"/>
    </a>
   <metadata/><rect fill="white" height="23" opacity="0" stroke="white" transform="" width="76" x="-707" y="-877"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV三台变35kV三台T接线361断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="31" y="-1040"/></g>
   <g href="35kV三台变10kV多底河线062断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-215" y="-397"/></g>
   <g href="35kV三台变10kV必期拉线063断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="60" y="-397"/></g>
   <g href="35kV三台变10kV零级电站并网线064断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="316" y="-392"/></g>
   <g href="35kV三台变10kV干河线082断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="844" y="-395"/></g>
   <g href="35kV三台变10kV分段012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="455" y="-439"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-457" y="-1232"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-457" y="-1267"/></g>
   <g href="35kV三台变35kV1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="-127" y="-739"/></g>
   <g href="AVC三台站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-323" y="-1247"/></g>
   <g href="35kV三台变35kV三铁线362断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="451" y="-1038"/></g>
   <g href="35kV三台变10kV过拉线083断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1036" y="-385"/></g>
   <g href="35kV三台变35kV2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="717" y="-751"/></g>
   <g href="35kV三台变10kV2号电容器组084断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1251" y="-406"/></g>
   <g href="35kV三台变10kV菜西拉铜矿线081断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="665" y="-393"/></g>
   <g href="35kV三台变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="23" qtmmishow="hidden" width="76" x="-707" y="-877"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-324" y="-1246"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ST" endPointId="0" endStationName="DY_GH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_tiangui" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="21,-1184 21,-1229 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38089" ObjectName="AC-35kV.LN_tiangui"/>
    <cge:TPSR_Ref TObjectID="38089_SS-227"/></metadata>
   <polyline fill="none" opacity="0" points="21,-1184 21,-1229 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ST" endPointId="0" endStationName="DY_TSB" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_SanTie" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="441,-1209 441,-1266 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48652" ObjectName="AC-35kV.LN_SanTie"/>
    <cge:TPSR_Ref TObjectID="48652_SS-227"/></metadata>
   <polyline fill="none" opacity="0" points="441,-1209 441,-1266 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1260d60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 123.241796 -1062.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12c9c50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 123.241796 -984.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1258990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 125.241796 -1141.085366)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_117a460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.881701 -1024.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12a5e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 74.241796 -817.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1151ec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 543.241796 -1060.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1181d30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 543.241796 -982.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1185bf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 545.241796 -1139.085366)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1198da0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.241796 -828.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1119cc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.166667 938.000000 -401.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11fe420" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 938.000000 -336.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1201e20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1147.000000 -356.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12055c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.166667 1147.000000 -421.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10e2e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1128.000000 -189.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1098680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.241796 -582.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="1008" cy="-903" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="-224" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="50" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="306" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="708" cy="-903" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="21" cy="-903" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="441" cy="-903" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="403" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="366" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="-28" cy="-903" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="-30" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="834" cy="-903" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="1081" cy="-510" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="588" cy="-510" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="834" cy="-510" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="527" cy="-510" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="1241" cy="-510" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="1026" cy="-510" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="655" cy="-510" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="834" cy="-510" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_119f630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">三台变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_f3eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：0878-6148328</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1245820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -21.000000 -1256.000000) translate(0,12)">35kV三台T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e54a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -1133.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117ca20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 -1181.000000) translate(0,12)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_122e170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -250.000000 -210.000000) translate(0,12)">多底河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12356b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.000000 -210.000000) translate(0,12)">必期拉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110c3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 267.000000 -206.000000) translate(0,12)">零级电站并网线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_125f640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.000000 -206.000000) translate(0,12)">菜西拉铜矿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a7410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -758.000000) translate(0,12)">10kVⅠ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11eeb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 499.000000 -758.000000) translate(0,12)">10kVⅡ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11471d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 -734.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11478c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -1040.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1147e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 28.000000 -958.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11480b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 28.000000 -1114.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11482f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 78.000000 -1173.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1148630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 76.000000 -1094.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1148a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 76.000000 -1016.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1148cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1015.000000 -979.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11491f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1053.000000 -1056.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1149470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -18.000000 -796.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11496b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -21.000000 -870.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11498f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 27.000000 -849.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1149b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -20.000000 -630.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1149d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -23.000000 -559.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1149fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -397.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_114a1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -217.000000 -468.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_114a430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -217.000000 -326.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aa690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 60.000000 -397.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aa8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 57.000000 -326.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aab10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 57.000000 -468.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aad50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 316.000000 -392.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aaf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -463.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -321.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -439.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 534.000000 -452.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 410.000000 -457.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11abad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 665.000000 -393.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11abd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -322.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11abf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -464.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ac190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 844.000000 -395.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ac3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -324.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ac610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -466.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ac850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.000000 -535.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aca90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 122.000000 -538.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11accd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 -934.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11acf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 595.000000 -552.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ad150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 373.000000 -553.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11b6440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 823.000000 -200.000000) translate(0,12)">干河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11b9470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -191.000000 -709.000000) translate(0,12)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11b9470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -191.000000 -709.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11b9470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -191.000000 -709.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11b9470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -191.000000 -709.000000) translate(0,57)">7.07%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11bb600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -705.000000 -874.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_11d5910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -446.000000 -1224.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_11d7da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -446.000000 -1259.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_11d3250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -719.000000 -608.000000) translate(0,16)">直流系统信号采集异常，经变电站现场人员检查，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_11d3250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -719.000000 -608.000000) translate(0,35)">直流系统与远动通信异常，需报缺陷处理。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1256480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -755.000000 -261.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_11e8f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -601.000000 -271.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_11e8f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -601.000000 -271.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11c6510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -127.000000 -739.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_11c7000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -304.500000 -1234.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109a320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -723.000000) translate(0,12)">SZ20-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109a320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -723.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109a320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -723.000000) translate(0,42)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109a320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -723.000000) translate(0,57)">7.80%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1217820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -749.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1217a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 847.000000 -811.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1217ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 844.000000 -868.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1217ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 893.000000 -862.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1218120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -623.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1218360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 842.000000 -557.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12185a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 861.000000 -614.000000) translate(0,12)">00227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1219770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 -1038.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1219980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -956.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1219bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -1112.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1219e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 496.000000 -1014.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121a040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 495.000000 -1092.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121a280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 496.000000 -1136.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121a4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1036.000000 -385.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121a700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1033.000000 -456.000000) translate(0,12)">0832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121a940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1033.000000 -314.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121ab80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 966.000000 -434.000000) translate(0,12)">08327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121adc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 966.000000 -368.000000) translate(0,12)">08360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121b000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1251.000000 -406.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121b240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1248.000000 -477.000000) translate(0,12)">0842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121b480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1248.000000 -335.000000) translate(0,12)">0843</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121b6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1248.000000 -238.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121b900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -454.000000) translate(0,12)">08427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121bb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -388.000000) translate(0,12)">08430</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121bd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -221.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121d7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -47.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121e440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 389.000000 -1283.000000) translate(0,12)">35kV三铁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a00ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -194.000000) translate(0,12)">过拉线</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ad480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 41.000000 727.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11adc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.500000 705.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1153fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -290.000000 610.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1154550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -296.000000 663.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1154b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -304.000000 575.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1155390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -304.000000 557.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1155610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -288.000000 540.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1156170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -296.000000 644.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11563f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -304.000000 593.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1156630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -296.000000 627.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1156960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 603.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1156bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 656.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1156e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 568.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1157070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 550.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11572b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 533.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11574f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 637.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1157730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 586.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1157970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 620.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1157ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -285.000000 997.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1157f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -291.000000 1050.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1158170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -299.000000 962.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11583b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -299.000000 944.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11585f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -283.000000 927.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120b6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -291.000000 1031.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120b900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -299.000000 980.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120bb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -291.000000 1014.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11b7aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 426.000000 380.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11b83b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 426.000000 362.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11b8630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 430.000000 345.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11b91f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 426.000000 397.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1158b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 906.000000 725.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11591a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.500000 703.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109aca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1284.500000 191.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109b830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1310.000000 174.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109bba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 153.500000 1241.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109be00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 179.000000 1224.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109c040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 165.000000 1258.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109c970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.500000 1221.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109cc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.000000 1204.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109ce50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.000000 1238.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109d180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 678.500000 843.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109d3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 826.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109d620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.000000 860.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109d950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.500000 615.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109dbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 706.000000 598.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109ddf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 632.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109e120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -175.500000 836.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109e380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -150.000000 819.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109e5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -164.000000 853.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109e8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -180.500000 612.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109eb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -155.000000 595.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109ed90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.000000 629.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109f0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -351.500000 145.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109f320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -326.000000 128.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_109f560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -340.000000 162.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_12217b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 1249.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="518" cy="1242" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1222060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 736.000000 773.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="743" cy="766" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.238710 -213.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34240" ObjectName="EC-DY_ST.062Ld"/>
    <cge:TPSR_Ref TObjectID="34240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.761290 -213.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34241" ObjectName="EC-DY_ST.063Ld"/>
    <cge:TPSR_Ref TObjectID="34241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.761290 -208.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34242" ObjectName="EC-DY_ST.064Ld"/>
    <cge:TPSR_Ref TObjectID="34242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.761290 -211.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34244" ObjectName="EC-DY_ST.082Ld"/>
    <cge:TPSR_Ref TObjectID="34244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.761290 -209.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34243" ObjectName="EC-DY_ST.081Ld"/>
    <cge:TPSR_Ref TObjectID="34243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.761290 -201.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49385" ObjectName="EC-DY_ST.083Ld"/>
    <cge:TPSR_Ref TObjectID="49385"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153544" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -418.000000 -1163.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26034" ObjectName="DYN-DY_ST"/>
     <cge:Meas_Ref ObjectId="153544"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-186971">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.241796 -1084.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28331" ObjectName="SW-DY_ST.DY_ST_3616SW"/>
     <cge:Meas_Ref ObjectId="186971"/>
    <cge:TPSR_Ref TObjectID="28331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186972">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 73.241796 -1063.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28332" ObjectName="SW-DY_ST.DY_ST_36160SW"/>
     <cge:Meas_Ref ObjectId="186972"/>
    <cge:TPSR_Ref TObjectID="28332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186969">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.241796 -928.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28329" ObjectName="SW-DY_ST.DY_ST_3611SW"/>
     <cge:Meas_Ref ObjectId="186969"/>
    <cge:TPSR_Ref TObjectID="28329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186970">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 73.241796 -985.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28330" ObjectName="SW-DY_ST.DY_ST_36117SW"/>
     <cge:Meas_Ref ObjectId="186970"/>
    <cge:TPSR_Ref TObjectID="28330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186973">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 75.241796 -1142.085366)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28333" ObjectName="SW-DY_ST.DY_ST_36167SW"/>
     <cge:Meas_Ref ObjectId="186973"/>
    <cge:TPSR_Ref TObjectID="28333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.203031 -936.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187074">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 998.881701 -949.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28339" ObjectName="SW-DY_ST.DY_ST_3901SW"/>
     <cge:Meas_Ref ObjectId="187074"/>
    <cge:TPSR_Ref TObjectID="28339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1049.881701 -1025.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28340" ObjectName="SW-DY_ST.DY_ST_39017SW"/>
     <cge:Meas_Ref ObjectId="187075"/>
    <cge:TPSR_Ref TObjectID="28340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -36.758204 -840.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28335" ObjectName="SW-DY_ST.DY_ST_3011SW"/>
     <cge:Meas_Ref ObjectId="186997"/>
    <cge:TPSR_Ref TObjectID="28335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187027">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -38.758204 -529.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28338" ObjectName="SW-DY_ST.DY_ST_0011SW"/>
     <cge:Meas_Ref ObjectId="187027"/>
    <cge:TPSR_Ref TObjectID="28338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.238710 -438.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28344" ObjectName="SW-DY_ST.DY_ST_0621SW"/>
     <cge:Meas_Ref ObjectId="187088"/>
    <cge:TPSR_Ref TObjectID="28344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.238710 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28345" ObjectName="SW-DY_ST.DY_ST_0626SW"/>
     <cge:Meas_Ref ObjectId="187089"/>
    <cge:TPSR_Ref TObjectID="28345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 24.241796 -818.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28336" ObjectName="SW-DY_ST.DY_ST_30117SW"/>
     <cge:Meas_Ref ObjectId="186998"/>
    <cge:TPSR_Ref TObjectID="28336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.761290 -438.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28347" ObjectName="SW-DY_ST.DY_ST_0631SW"/>
     <cge:Meas_Ref ObjectId="187111"/>
    <cge:TPSR_Ref TObjectID="28347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.761290 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28348" ObjectName="SW-DY_ST.DY_ST_0636SW"/>
     <cge:Meas_Ref ObjectId="187112"/>
    <cge:TPSR_Ref TObjectID="28348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187134">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.761290 -433.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28350" ObjectName="SW-DY_ST.DY_ST_0641SW"/>
     <cge:Meas_Ref ObjectId="187134"/>
    <cge:TPSR_Ref TObjectID="28350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187135">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.761290 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28351" ObjectName="SW-DY_ST.DY_ST_0646SW"/>
     <cge:Meas_Ref ObjectId="187135"/>
    <cge:TPSR_Ref TObjectID="28351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187203">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 394.226115 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28359" ObjectName="SW-DY_ST.DY_ST_0121SW"/>
     <cge:Meas_Ref ObjectId="187203"/>
    <cge:TPSR_Ref TObjectID="28359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187204">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 518.226115 -422.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28360" ObjectName="SW-DY_ST.DY_ST_0122SW"/>
     <cge:Meas_Ref ObjectId="187204"/>
    <cge:TPSR_Ref TObjectID="28360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187180">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.761290 -436.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28356" ObjectName="SW-DY_ST.DY_ST_0822SW"/>
     <cge:Meas_Ref ObjectId="187180"/>
    <cge:TPSR_Ref TObjectID="28356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187181">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.761290 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28357" ObjectName="SW-DY_ST.DY_ST_0826SW"/>
     <cge:Meas_Ref ObjectId="187181"/>
    <cge:TPSR_Ref TObjectID="28357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187157">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.761290 -434.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28353" ObjectName="SW-DY_ST.DY_ST_0812SW"/>
     <cge:Meas_Ref ObjectId="187157"/>
    <cge:TPSR_Ref TObjectID="28353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.761290 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28354" ObjectName="SW-DY_ST.DY_ST_0816SW"/>
     <cge:Meas_Ref ObjectId="187158"/>
    <cge:TPSR_Ref TObjectID="28354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187078">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 356.881701 -523.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28341" ObjectName="SW-DY_ST.DY_ST_0901SW"/>
     <cge:Meas_Ref ObjectId="187078"/>
    <cge:TPSR_Ref TObjectID="28341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187079">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.881701 -522.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28342" ObjectName="SW-DY_ST.DY_ST_0902SW"/>
     <cge:Meas_Ref ObjectId="187079"/>
    <cge:TPSR_Ref TObjectID="28342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1076.203031 -544.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285518">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.241796 -1082.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44761" ObjectName="SW-DY_ST.DY_ST_3626SW"/>
     <cge:Meas_Ref ObjectId="285518"/>
    <cge:TPSR_Ref TObjectID="44761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285520">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 493.241796 -1061.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44762" ObjectName="SW-DY_ST.DY_ST_36260SW"/>
     <cge:Meas_Ref ObjectId="285520"/>
    <cge:TPSR_Ref TObjectID="44762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285517">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.241796 -926.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44759" ObjectName="SW-DY_ST.DY_ST_3621SW"/>
     <cge:Meas_Ref ObjectId="285517"/>
    <cge:TPSR_Ref TObjectID="44759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285519">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 493.241796 -983.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44760" ObjectName="SW-DY_ST.DY_ST_36217SW"/>
     <cge:Meas_Ref ObjectId="285519"/>
    <cge:TPSR_Ref TObjectID="44760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285521">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 495.241796 -1140.085366)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44763" ObjectName="SW-DY_ST.DY_ST_36267SW"/>
     <cge:Meas_Ref ObjectId="285521"/>
    <cge:TPSR_Ref TObjectID="44763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285376">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.241796 -838.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44751" ObjectName="SW-DY_ST.DY_ST_3021SW"/>
     <cge:Meas_Ref ObjectId="285376"/>
    <cge:TPSR_Ref TObjectID="44751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.241796 -527.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44752" ObjectName="SW-DY_ST.DY_ST_0022SW"/>
     <cge:Meas_Ref ObjectId="285379"/>
    <cge:TPSR_Ref TObjectID="44752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285377">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.241796 -829.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44753" ObjectName="SW-DY_ST.DY_ST_30217SW"/>
     <cge:Meas_Ref ObjectId="285377"/>
    <cge:TPSR_Ref TObjectID="44753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285712">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.761290 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44766" ObjectName="SW-DY_ST.DY_ST_0836SW"/>
     <cge:Meas_Ref ObjectId="285712"/>
    <cge:TPSR_Ref TObjectID="44766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.761290 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44765" ObjectName="SW-DY_ST.DY_ST_0832SW"/>
     <cge:Meas_Ref ObjectId="285711"/>
    <cge:TPSR_Ref TObjectID="44765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.761290 -305.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44769" ObjectName="SW-DY_ST.DY_ST_0843SW"/>
     <cge:Meas_Ref ObjectId="285762"/>
    <cge:TPSR_Ref TObjectID="44769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.761290 -447.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44768" ObjectName="SW-DY_ST.DY_ST_0842SW"/>
     <cge:Meas_Ref ObjectId="285761"/>
    <cge:TPSR_Ref TObjectID="44768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 964.241796 -403.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44772" ObjectName="SW-DY_ST.DY_ST_08327SW"/>
     <cge:Meas_Ref ObjectId="285713"/>
    <cge:TPSR_Ref TObjectID="44772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285714">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 964.241796 -337.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44771" ObjectName="SW-DY_ST.DY_ST_08360SW"/>
     <cge:Meas_Ref ObjectId="285714"/>
    <cge:TPSR_Ref TObjectID="44771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1173.241796 -357.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44774" ObjectName="SW-DY_ST.DY_ST_08460SW"/>
     <cge:Meas_Ref ObjectId="285765"/>
    <cge:TPSR_Ref TObjectID="44774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1173.241796 -423.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44773" ObjectName="SW-DY_ST.DY_ST_08427SW"/>
     <cge:Meas_Ref ObjectId="285764"/>
    <cge:TPSR_Ref TObjectID="44773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285763">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.761290 -208.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44770" ObjectName="SW-DY_ST.DY_ST_0846SW"/>
     <cge:Meas_Ref ObjectId="285763"/>
    <cge:TPSR_Ref TObjectID="44770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285766">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.241796 -190.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44775" ObjectName="SW-DY_ST.DY_ST_08467SW"/>
     <cge:Meas_Ref ObjectId="285766"/>
    <cge:TPSR_Ref TObjectID="44775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285380">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 859.241796 -583.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44754" ObjectName="SW-DY_ST.DY_ST_00227SW"/>
     <cge:Meas_Ref ObjectId="285380"/>
    <cge:TPSR_Ref TObjectID="44754"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_12d3fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="114,-1068 128,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28332@0" ObjectIDZND0="g_1260d60@0" Pin0InfoVect0LinkObjId="g_1260d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186972_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="114,-1068 128,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1261c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-1068 21,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28332@1" ObjectIDZND0="28331@x" ObjectIDZND1="28328@x" Pin0InfoVect0LinkObjId="SW-186971_0" Pin0InfoVect1LinkObjId="SW-186967_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186972_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="78,-1068 21,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12707f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1089 21,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28331@0" ObjectIDZND0="28332@x" ObjectIDZND1="28328@x" Pin0InfoVect0LinkObjId="SW-186972_0" Pin0InfoVect1LinkObjId="SW-186967_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186971_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1089 21,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12709e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1068 21,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28332@x" ObjectIDND1="28331@x" ObjectIDZND0="28328@1" Pin0InfoVect0LinkObjId="SW-186967_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186972_0" Pin1InfoVect1LinkObjId="SW-186971_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1068 21,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12ca280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="114,-990 128,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28330@0" ObjectIDZND0="g_12c9c50@0" Pin0InfoVect0LinkObjId="g_12c9c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="114,-990 128,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12ca470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-990 21,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28330@1" ObjectIDZND0="28328@x" ObjectIDZND1="28329@x" Pin0InfoVect0LinkObjId="SW-186967_0" Pin0InfoVect1LinkObjId="SW-186969_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186970_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="78,-990 21,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a9bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1019 21,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28328@0" ObjectIDZND0="28330@x" ObjectIDZND1="28329@x" Pin0InfoVect0LinkObjId="SW-186970_0" Pin0InfoVect1LinkObjId="SW-186969_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186967_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1019 21,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a9de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-990 21,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28330@x" ObjectIDND1="28328@x" ObjectIDZND0="28329@1" Pin0InfoVect0LinkObjId="SW-186969_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186970_0" Pin1InfoVect1LinkObjId="SW-186967_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-990 21,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1259140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,-1147 130,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28333@0" ObjectIDZND0="g_1258990@0" Pin0InfoVect0LinkObjId="g_1258990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186973_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="116,-1147 130,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1259330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="80,-1147 23,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="28333@1" ObjectIDZND0="28331@x" ObjectIDZND1="g_1259d70@0" ObjectIDZND2="38089@1" Pin0InfoVect0LinkObjId="SW-186971_0" Pin0InfoVect1LinkObjId="g_1259d70_0" Pin0InfoVect2LinkObjId="g_125a780_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186973_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="80,-1147 23,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1259b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1125 21,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="28331@1" ObjectIDZND0="28333@x" ObjectIDZND1="g_1259d70@0" ObjectIDZND2="38089@1" Pin0InfoVect0LinkObjId="SW-186973_0" Pin0InfoVect1LinkObjId="g_1259d70_0" Pin0InfoVect2LinkObjId="g_125a780_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186971_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1125 21,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_125a780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1159 21,-1188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1259d70@0" ObjectIDND1="28333@x" ObjectIDND2="28331@x" ObjectIDZND0="38089@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1259d70_0" Pin1InfoVect1LinkObjId="SW-186973_0" Pin1InfoVect2LinkObjId="SW-186971_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1159 21,-1188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_125afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-18,-1171 -18,-1159 21,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_1259d70@0" ObjectIDZND0="28333@x" ObjectIDZND1="28331@x" ObjectIDZND2="38089@1" Pin0InfoVect0LinkObjId="SW-186973_0" Pin0InfoVect1LinkObjId="SW-186971_0" Pin0InfoVect2LinkObjId="g_125a780_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1259d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-18,-1171 -18,-1159 21,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1245630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-1159 21,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1259d70@0" ObjectIDND1="38089@1" ObjectIDZND0="28333@x" ObjectIDZND1="28331@x" Pin0InfoVect0LinkObjId="SW-186973_0" Pin0InfoVect1LinkObjId="SW-186971_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1259d70_0" Pin1InfoVect1LinkObjId="g_125a780_1" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="21,-1159 21,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1247d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="708,-986 708,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="708,-986 708,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11e57d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="708,-941 708,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_116e4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="708,-941 708,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_117ac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1091,-1030 1105,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28340@0" ObjectIDZND0="g_117a460@0" Pin0InfoVect0LinkObjId="g_117a460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187075_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1091,-1030 1105,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_117ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-1030 1007,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28340@1" ObjectIDZND0="28339@x" ObjectIDZND1="g_117c010@0" ObjectIDZND2="g_117b3d0@0" Pin0InfoVect0LinkObjId="SW-187074_0" Pin0InfoVect1LinkObjId="g_117c010_0" Pin0InfoVect2LinkObjId="g_117b3d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-1030 1007,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_117aff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,-990 1008,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28339@1" ObjectIDZND0="28340@x" ObjectIDZND1="g_117c010@0" ObjectIDZND2="g_117b3d0@0" Pin0InfoVect0LinkObjId="SW-187075_0" Pin0InfoVect1LinkObjId="g_117c010_0" Pin0InfoVect2LinkObjId="g_117b3d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1008,-990 1008,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_117b1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1009,-1120 1009,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_11d8390@0" ObjectIDZND0="g_117b3d0@1" Pin0InfoVect0LinkObjId="g_117b3d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11d8390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1009,-1120 1009,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_117ba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,-1044 953,-1044 953,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28340@x" ObjectIDND1="28339@x" ObjectIDND2="g_117b3d0@0" ObjectIDZND0="g_117c010@0" Pin0InfoVect0LinkObjId="g_117c010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-187075_0" Pin1InfoVect1LinkObjId="SW-187074_0" Pin1InfoVect2LinkObjId="g_117b3d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1008,-1044 953,-1044 953,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_117bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,-1030 1008,-1044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28340@x" ObjectIDND1="28339@x" ObjectIDZND0="g_117c010@0" ObjectIDZND1="g_117b3d0@0" Pin0InfoVect0LinkObjId="g_117c010_0" Pin0InfoVect1LinkObjId="g_117b3d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-187075_0" Pin1InfoVect1LinkObjId="SW-187074_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1008,-1030 1008,-1044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_117be20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,-1044 1008,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_117c010@0" ObjectIDND1="28340@x" ObjectIDND2="28339@x" ObjectIDZND0="g_117b3d0@0" Pin0InfoVect0LinkObjId="g_117b3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_117c010_0" Pin1InfoVect1LinkObjId="SW-187075_0" Pin1InfoVect2LinkObjId="SW-187074_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1008,-1044 1008,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_116e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,-954 1008,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28339@0" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_11e57d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1008,-954 1008,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1209ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-30,-670 -30,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="28361@0" ObjectIDZND0="28337@1" Pin0InfoVect0LinkObjId="SW-187025_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10c04c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-30,-670 -30,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c02d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-30,-609 -30,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28337@0" ObjectIDZND0="28338@1" Pin0InfoVect0LinkObjId="SW-187027_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-30,-609 -30,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10c04c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-28,-775 -28,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28334@0" ObjectIDZND0="28361@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-28,-775 -28,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f5bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-479 -224,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28344@1" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_1109640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-479 -224,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122b3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-443 -224,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28344@0" ObjectIDZND0="28343@1" Pin0InfoVect0LinkObjId="SW-187086_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-443 -224,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122b600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-376 -224,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28343@0" ObjectIDZND0="28345@1" Pin0InfoVect0LinkObjId="SW-187089_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-376 -224,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122db10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-188,-268 -188,-283 -224,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_11f5080@0" ObjectIDZND0="28345@x" ObjectIDZND1="34240@x" Pin0InfoVect0LinkObjId="SW-187089_0" Pin0InfoVect1LinkObjId="EC-DY_ST.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11f5080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-188,-268 -188,-283 -224,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-301 -224,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28345@0" ObjectIDZND0="g_11f5080@0" ObjectIDZND1="34240@x" Pin0InfoVect0LinkObjId="g_11f5080_0" Pin0InfoVect1LinkObjId="EC-DY_ST.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187089_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-301 -224,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122df50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-283 -224,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_11f5080@0" ObjectIDND1="28345@x" ObjectIDZND0="34240@0" Pin0InfoVect0LinkObjId="EC-DY_ST.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11f5080_0" Pin1InfoVect1LinkObjId="SW-187089_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-283 -224,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a6780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="65,-823 79,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28336@0" ObjectIDZND0="g_12a5e50@0" Pin0InfoVect0LinkObjId="g_12a5e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="65,-823 79,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a69a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-823 -28,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28336@1" ObjectIDZND0="28335@x" ObjectIDZND1="28334@x" Pin0InfoVect0LinkObjId="SW-186997_0" Pin0InfoVect1LinkObjId="SW-186995_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="29,-823 -28,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11bca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-28,-845 -28,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28335@0" ObjectIDZND0="28336@x" ObjectIDZND1="28334@x" Pin0InfoVect0LinkObjId="SW-186998_0" Pin0InfoVect1LinkObjId="SW-186995_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-28,-845 -28,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11bcc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-28,-823 -28,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28336@x" ObjectIDND1="28335@x" ObjectIDZND0="28334@1" Pin0InfoVect0LinkObjId="SW-186995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186998_0" Pin1InfoVect1LinkObjId="SW-186997_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-28,-823 -28,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1109640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-479 50,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28347@1" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_11f5bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187111_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-479 50,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1109860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-443 50,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28347@0" ObjectIDZND0="28346@1" Pin0InfoVect0LinkObjId="SW-187109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187111_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-443 50,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1109a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-376 50,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28346@0" ObjectIDZND0="28348@1" Pin0InfoVect0LinkObjId="SW-187112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-376 50,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_110bf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="86,-268 86,-283 50,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1108ad0@0" ObjectIDZND0="28348@x" ObjectIDZND1="34241@x" Pin0InfoVect0LinkObjId="SW-187112_0" Pin0InfoVect1LinkObjId="EC-DY_ST.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1108ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="86,-268 86,-283 50,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_110c1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-301 50,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28348@0" ObjectIDZND0="g_1108ad0@0" ObjectIDZND1="34241@x" Pin0InfoVect0LinkObjId="g_1108ad0_0" Pin0InfoVect1LinkObjId="EC-DY_ST.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="50,-301 50,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12354c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-283 50,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1108ad0@0" ObjectIDND1="28348@x" ObjectIDZND0="34241@0" Pin0InfoVect0LinkObjId="EC-DY_ST.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1108ad0_0" Pin1InfoVect1LinkObjId="SW-187112_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-283 50,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11df7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-474 306,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28350@1" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_11f5bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187134_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-474 306,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11df9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-438 306,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28350@0" ObjectIDZND0="28349@1" Pin0InfoVect0LinkObjId="SW-187132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187134_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-438 306,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11dfc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-371 306,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28349@0" ObjectIDZND0="28351@1" Pin0InfoVect0LinkObjId="SW-187135_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-371 306,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e2090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="342,-263 342,-278 306,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_11dec60@0" ObjectIDZND0="28351@x" ObjectIDZND1="34242@x" Pin0InfoVect0LinkObjId="SW-187135_0" Pin0InfoVect1LinkObjId="EC-DY_ST.064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11dec60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="342,-263 342,-278 306,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e22b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-296 306,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28351@0" ObjectIDZND0="g_11dec60@0" ObjectIDZND1="34242@x" Pin0InfoVect0LinkObjId="g_11dec60_0" Pin0InfoVect1LinkObjId="EC-DY_ST.064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="306,-296 306,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e24d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-278 306,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28351@x" ObjectIDND1="g_11dec60@0" ObjectIDZND0="34242@0" Pin0InfoVect0LinkObjId="EC-DY_ST.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-187135_0" Pin1InfoVect1LinkObjId="g_11dec60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-278 306,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_110f0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="527,-427 527,-415 481,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28360@0" ObjectIDZND0="28358@0" Pin0InfoVect0LinkObjId="SW-187201_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="527,-427 527,-415 481,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_110f340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="403,-432 403,-415 454,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28359@0" ObjectIDZND0="28358@1" Pin0InfoVect0LinkObjId="SW-187201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="403,-432 403,-415 454,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1268a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-441 834,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28356@0" ObjectIDZND0="28355@1" Pin0InfoVect0LinkObjId="SW-187178_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-441 834,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1268c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-374 834,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28355@0" ObjectIDZND0="28357@1" Pin0InfoVect0LinkObjId="SW-187181_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187178_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-374 834,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1269860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-266 870,-281 834,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_110fef0@0" ObjectIDZND0="28357@x" ObjectIDZND1="34244@x" Pin0InfoVect0LinkObjId="SW-187181_0" Pin0InfoVect1LinkObjId="EC-DY_ST.082Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_110fef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="870,-266 870,-281 834,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1269ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-299 834,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28357@0" ObjectIDZND0="g_110fef0@0" ObjectIDZND1="34244@x" Pin0InfoVect0LinkObjId="g_110fef0_0" Pin0InfoVect1LinkObjId="EC-DY_ST.082Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="834,-299 834,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1269d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-281 834,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_110fef0@0" ObjectIDND1="28357@x" ObjectIDZND0="34244@0" Pin0InfoVect0LinkObjId="EC-DY_ST.082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_110fef0_0" Pin1InfoVect1LinkObjId="SW-187181_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-281 834,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125c2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-439 655,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28353@0" ObjectIDZND0="28352@1" Pin0InfoVect0LinkObjId="SW-187155_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187157_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-439 655,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125c510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-372 655,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28352@0" ObjectIDZND0="28354@1" Pin0InfoVect0LinkObjId="SW-187158_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187155_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-372 655,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125ef20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="691,-264 691,-279 655,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_126bb00@0" ObjectIDZND0="28354@x" ObjectIDZND1="34243@x" Pin0InfoVect0LinkObjId="SW-187158_0" Pin0InfoVect1LinkObjId="EC-DY_ST.081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_126bb00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="691,-264 691,-279 655,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125f180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-297 655,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28354@0" ObjectIDZND0="g_126bb00@0" ObjectIDZND1="34243@x" Pin0InfoVect0LinkObjId="g_126bb00_0" Pin0InfoVect1LinkObjId="EC-DY_ST.081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="655,-297 655,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-279 655,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_126bb00@0" ObjectIDND1="28354@x" ObjectIDZND0="34243@0" Pin0InfoVect0LinkObjId="EC-DY_ST.081Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_126bb00_0" Pin1InfoVect1LinkObjId="SW-187158_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-279 655,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1213920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-694 367,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_11cf700@0" ObjectIDZND0="g_1213b80@1" Pin0InfoVect0LinkObjId="g_1213b80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11cf700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-694 367,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a6480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="366,-609 330,-609 330,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1213b80@0" ObjectIDND1="28341@x" ObjectIDZND0="g_1214480@0" Pin0InfoVect0LinkObjId="g_1214480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1213b80_0" Pin1InfoVect1LinkObjId="SW-187078_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="366,-609 330,-609 330,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a6f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="366,-634 366,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1213b80@0" ObjectIDZND0="g_1214480@0" ObjectIDZND1="28341@x" Pin0InfoVect0LinkObjId="g_1214480_0" Pin0InfoVect1LinkObjId="SW-187078_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1213b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="366,-634 366,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a71b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="366,-609 366,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1214480@0" ObjectIDND1="g_1213b80@0" ObjectIDZND0="28341@1" Pin0InfoVect0LinkObjId="SW-187078_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1214480_0" Pin1InfoVect1LinkObjId="g_1213b80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="366,-609 366,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ec270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="589,-693 589,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_11dadc0@0" ObjectIDZND0="g_11ec4d0@1" Pin0InfoVect0LinkObjId="g_11ec4d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11dadc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="589,-693 589,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11edb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="588,-608 552,-608 552,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_11ec4d0@0" ObjectIDND1="28342@x" ObjectIDZND0="g_11ecdd0@0" Pin0InfoVect0LinkObjId="g_11ecdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11ec4d0_0" Pin1InfoVect1LinkObjId="SW-187079_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="588,-608 552,-608 552,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11edde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="588,-633 588,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_11ec4d0@0" ObjectIDZND0="g_11ecdd0@0" ObjectIDZND1="28342@x" Pin0InfoVect0LinkObjId="g_11ecdd0_0" Pin0InfoVect1LinkObjId="SW-187079_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11ec4d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="588,-633 588,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ee040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="588,-608 588,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_11ecdd0@0" ObjectIDND1="g_11ec4d0@0" ObjectIDZND0="28342@1" Pin0InfoVect0LinkObjId="SW-187079_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11ecdd0_0" Pin1InfoVect1LinkObjId="g_11ec4d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="588,-608 588,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1146f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-594 1081,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-594 1081,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11476d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-549 1081,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_115d8e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-549 1081,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1152950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="534,-1066 548,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44762@0" ObjectIDZND0="g_1151ec0@0" Pin0InfoVect0LinkObjId="g_1151ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="534,-1066 548,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1152bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="498,-1066 441,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44762@1" ObjectIDZND0="44761@x" ObjectIDZND1="44758@x" Pin0InfoVect0LinkObjId="SW-285518_0" Pin0InfoVect1LinkObjId="SW-285516_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="498,-1066 441,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1152e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1087 441,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44761@0" ObjectIDZND0="44762@x" ObjectIDZND1="44758@x" Pin0InfoVect0LinkObjId="SW-285520_0" Pin0InfoVect1LinkObjId="SW-285516_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285518_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1087 441,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1153070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1066 441,-1044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44761@x" ObjectIDND1="44762@x" ObjectIDZND0="44758@1" Pin0InfoVect0LinkObjId="SW-285516_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285518_0" Pin1InfoVect1LinkObjId="SW-285520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1066 441,-1044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11827c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="534,-988 548,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44760@0" ObjectIDZND0="g_1181d30@0" Pin0InfoVect0LinkObjId="g_1181d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285519_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="534,-988 548,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1182a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="498,-988 441,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44760@1" ObjectIDZND0="44758@x" ObjectIDZND1="44759@x" Pin0InfoVect0LinkObjId="SW-285516_0" Pin0InfoVect1LinkObjId="SW-285517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285519_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="498,-988 441,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1182c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1017 441,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44758@0" ObjectIDZND0="44760@x" ObjectIDZND1="44759@x" Pin0InfoVect0LinkObjId="SW-285519_0" Pin0InfoVect1LinkObjId="SW-285517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1017 441,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1182ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-988 441,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="44758@x" ObjectIDND1="44760@x" ObjectIDZND0="44759@1" Pin0InfoVect0LinkObjId="SW-285517_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285516_0" Pin1InfoVect1LinkObjId="SW-285519_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-988 441,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1170b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="536,-1145 550,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44763@0" ObjectIDZND0="g_1185bf0@0" Pin0InfoVect0LinkObjId="g_1185bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285521_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="536,-1145 550,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1170dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="500,-1145 443,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44763@1" ObjectIDZND0="44761@x" ObjectIDZND1="g_1171280@0" ObjectIDZND2="g_1174730@0" Pin0InfoVect0LinkObjId="SW-285518_0" Pin0InfoVect1LinkObjId="g_1171280_0" Pin0InfoVect2LinkObjId="g_1174730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285521_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="500,-1145 443,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1171020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1123 441,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44761@1" ObjectIDZND0="44763@x" ObjectIDZND1="g_1171280@0" ObjectIDZND2="g_1174730@0" Pin0InfoVect0LinkObjId="SW-285521_0" Pin0InfoVect1LinkObjId="g_1171280_0" Pin0InfoVect2LinkObjId="g_1174730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285518_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1123 441,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1172030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="402,-1169 402,-1157 441,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1171280@0" ObjectIDZND0="44761@x" ObjectIDZND1="44763@x" ObjectIDZND2="g_1174730@0" Pin0InfoVect0LinkObjId="SW-285518_0" Pin0InfoVect1LinkObjId="SW-285521_0" Pin0InfoVect2LinkObjId="g_1174730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1171280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="402,-1169 402,-1157 441,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1172290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1157 441,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1171280@0" ObjectIDND1="g_1174730@0" ObjectIDND2="48652@1" ObjectIDZND0="44761@x" ObjectIDZND1="44763@x" Pin0InfoVect0LinkObjId="SW-285518_0" Pin0InfoVect1LinkObjId="SW-285521_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1171280_0" Pin1InfoVect1LinkObjId="g_1174730_0" Pin1InfoVect2LinkObjId="g_1176bc0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1157 441,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1175e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="485,-1198 441,-1198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1174730@0" ObjectIDZND0="g_1171280@0" ObjectIDZND1="44761@x" ObjectIDZND2="44763@x" Pin0InfoVect0LinkObjId="g_1171280_0" Pin0InfoVect1LinkObjId="SW-285518_0" Pin0InfoVect2LinkObjId="SW-285521_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1174730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="485,-1198 441,-1198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1176960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1157 441,-1198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1171280@0" ObjectIDND1="44761@x" ObjectIDND2="44763@x" ObjectIDZND0="g_1174730@0" ObjectIDZND1="48652@1" Pin0InfoVect0LinkObjId="g_1174730_0" Pin0InfoVect1LinkObjId="g_1176bc0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1171280_0" Pin1InfoVect1LinkObjId="SW-285518_0" Pin1InfoVect2LinkObjId="SW-285521_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1157 441,-1198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1176bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1198 441,-1214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1174730@0" ObjectIDND1="g_1171280@0" ObjectIDND2="44761@x" ObjectIDZND0="48652@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1174730_0" Pin1InfoVect1LinkObjId="g_1171280_0" Pin1InfoVect2LinkObjId="SW-285518_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1198 441,-1214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11917c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-650 834,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="44776@0" ObjectIDZND0="44750@1" Pin0InfoVect0LinkObjId="SW-285378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_121f5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-650 834,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1199830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="932,-834 946,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44753@0" ObjectIDZND0="g_1198da0@0" Pin0InfoVect0LinkObjId="g_1198da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285377_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="932,-834 946,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1199a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-834 834,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44753@1" ObjectIDZND0="44751@x" ObjectIDZND1="44749@x" Pin0InfoVect0LinkObjId="SW-285376_0" Pin0InfoVect1LinkObjId="SW-285375_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-834 834,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1158800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-834 834,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44753@x" ObjectIDND1="44751@x" ObjectIDZND0="44749@1" Pin0InfoVect0LinkObjId="SW-285375_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285377_0" Pin1InfoVect1LinkObjId="SW-285376_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-834 834,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_115a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-933 21,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28329@0" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_11e57d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186969_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="21,-933 21,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_115b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-931 441,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44759@0" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_11e57d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285517_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-931 441,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_115b870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="403,-468 403,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28359@1" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_11f5bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187203_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="403,-468 403,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_115c050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="366,-528 366,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28341@0" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_11f5bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="366,-528 366,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_115c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-28,-881 -28,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28335@1" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_11e57d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-28,-881 -28,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_115d0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-30,-534 -30,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28338@0" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_11f5bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187027_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-30,-534 -30,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_115d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="588,-527 588,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28342@0" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_11476d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187079_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="588,-527 588,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_115db40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-879 834,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44751@1" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_11e57d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-879 834,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_115e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-532 834,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44752@0" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_11476d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-532 834,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1187fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1062,-256 1062,-271 1026,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1160dd0@0" ObjectIDZND0="44766@x" ObjectIDZND1="49385@x" Pin0InfoVect0LinkObjId="SW-285712_0" Pin0InfoVect1LinkObjId="EC-DY_ST.083Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1160dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1062,-256 1062,-271 1026,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1188220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-289 1026,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="44766@0" ObjectIDZND0="g_1160dd0@0" ObjectIDZND1="49385@x" Pin0InfoVect0LinkObjId="g_1160dd0_0" Pin0InfoVect1LinkObjId="EC-DY_ST.083Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-289 1026,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1188480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-271 1026,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="44766@x" ObjectIDND1="g_1160dd0@0" ObjectIDZND0="49385@0" Pin0InfoVect0LinkObjId="EC-DY_ST.083Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285712_0" Pin1InfoVect1LinkObjId="g_1160dd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-271 1026,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1113f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="527,-463 527,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28360@1" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_11476d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187204_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="527,-463 527,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11feeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="969,-408 956,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44772@1" ObjectIDZND0="g_1119cc0@0" Pin0InfoVect0LinkObjId="g_1119cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285713_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="969,-408 956,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ff110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="956,-342 969,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11fe420@0" ObjectIDZND0="44771@1" Pin0InfoVect0LinkObjId="SW-285714_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11fe420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="956,-342 969,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12028b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-362 1178,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1201e20@0" ObjectIDZND0="44774@1" Pin0InfoVect0LinkObjId="SW-285765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1201e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-362 1178,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1206050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1178,-428 1165,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44773@1" ObjectIDZND0="g_12055c0@0" Pin0InfoVect0LinkObjId="g_12055c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285764_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1178,-428 1165,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1206ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-310 1241,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="44769@0" ObjectIDZND0="g_12062b0@1" Pin0InfoVect0LinkObjId="g_12062b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-310 1241,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1207130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-264 1241,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12062b0@0" ObjectIDZND0="44770@1" Pin0InfoVect0LinkObjId="SW-285763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12062b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-264 1241,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10dce60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-213 1241,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="44770@0" ObjectIDZND0="44777@x" ObjectIDZND1="44775@x" Pin0InfoVect0LinkObjId="CB-DY_ST.DY_ST_Cb2_0" Pin0InfoVect1LinkObjId="SW-285766_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-213 1241,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10dd0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-196 1241,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="44770@x" ObjectIDND1="44775@x" ObjectIDZND0="44777@0" Pin0InfoVect0LinkObjId="CB-DY_ST.DY_ST_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285763_0" Pin1InfoVect1LinkObjId="SW-285766_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-196 1241,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10dd320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-74 1241,-59 1206,-59 1206,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDZND0="44770@x" ObjectIDZND1="44777@x" ObjectIDZND2="44775@x" Pin0InfoVect0LinkObjId="SW-285763_0" Pin0InfoVect1LinkObjId="CB-DY_ST.DY_ST_Cb2_0" Pin0InfoVect2LinkObjId="SW-285766_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-74 1241,-59 1206,-59 1206,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10dde20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1195,-196 1206,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="44775@0" ObjectIDZND0="44770@x" ObjectIDZND1="44777@x" Pin0InfoVect0LinkObjId="SW-285763_0" Pin0InfoVect1LinkObjId="CB-DY_ST.DY_ST_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285766_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1195,-196 1206,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10de080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-196 1241,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="44775@x" ObjectIDZND0="44770@x" ObjectIDZND1="44777@x" Pin0InfoVect0LinkObjId="SW-285763_0" Pin0InfoVect1LinkObjId="CB-DY_ST.DY_ST_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285766_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1206,-196 1241,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10de2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-488 1241,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44768@1" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_11476d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-488 1241,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10deb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-467 1026,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44765@1" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_11476d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285711_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-467 1026,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10df340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-475 655,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28353@1" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_11476d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187157_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-475 655,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10dfb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-477 834,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28356@1" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_11476d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-477 834,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1091ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1146,-195 1159,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_10e2e50@0" ObjectIDZND0="44775@1" Pin0InfoVect0LinkObjId="SW-285766_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10e2e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1146,-195 1159,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1091d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-408 1026,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44772@0" ObjectIDZND0="44765@x" ObjectIDZND1="44764@x" Pin0InfoVect0LinkObjId="SW-285711_0" Pin0InfoVect1LinkObjId="SW-285710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-408 1026,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1092800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-431 1026,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44765@0" ObjectIDZND0="44772@x" ObjectIDZND1="44764@x" Pin0InfoVect0LinkObjId="SW-285713_0" Pin0InfoVect1LinkObjId="SW-285710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-431 1026,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1092a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-408 1026,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44772@x" ObjectIDND1="44765@x" ObjectIDZND0="44764@1" Pin0InfoVect0LinkObjId="SW-285710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285713_0" Pin1InfoVect1LinkObjId="SW-285711_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-408 1026,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1092cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-342 1026,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44771@0" ObjectIDZND0="44764@x" ObjectIDZND1="44766@x" Pin0InfoVect0LinkObjId="SW-285710_0" Pin0InfoVect1LinkObjId="SW-285712_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285714_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-342 1026,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10937b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-364 1026,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44764@0" ObjectIDZND0="44771@x" ObjectIDZND1="44766@x" Pin0InfoVect0LinkObjId="SW-285714_0" Pin0InfoVect1LinkObjId="SW-285712_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-364 1026,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1093a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-342 1026,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="44771@x" ObjectIDND1="44764@x" ObjectIDZND0="44766@1" Pin0InfoVect0LinkObjId="SW-285712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285714_0" Pin1InfoVect1LinkObjId="SW-285710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-342 1026,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1093c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-428 1241,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44773@0" ObjectIDZND0="44768@x" ObjectIDZND1="44767@x" Pin0InfoVect0LinkObjId="SW-285761_0" Pin0InfoVect1LinkObjId="SW-285760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-428 1241,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1094760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-452 1241,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44768@0" ObjectIDZND0="44773@x" ObjectIDZND1="44767@x" Pin0InfoVect0LinkObjId="SW-285764_0" Pin0InfoVect1LinkObjId="SW-285760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-452 1241,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10949c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-428 1241,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44773@x" ObjectIDND1="44768@x" ObjectIDZND0="44767@1" Pin0InfoVect0LinkObjId="SW-285760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285764_0" Pin1InfoVect1LinkObjId="SW-285761_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-428 1241,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1094c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-362 1241,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44774@0" ObjectIDZND0="44767@x" ObjectIDZND1="44769@x" Pin0InfoVect0LinkObjId="SW-285760_0" Pin0InfoVect1LinkObjId="SW-285762_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-362 1241,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1095710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-385 1241,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44767@0" ObjectIDZND0="44774@x" ObjectIDZND1="44769@x" Pin0InfoVect0LinkObjId="SW-285765_0" Pin0InfoVect1LinkObjId="SW-285762_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-385 1241,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1095970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-362 1241,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="44774@x" ObjectIDND1="44767@x" ObjectIDZND0="44769@1" Pin0InfoVect0LinkObjId="SW-285762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285765_0" Pin1InfoVect1LinkObjId="SW-285760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-362 1241,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1099110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="900,-588 914,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44754@0" ObjectIDZND0="g_1098680@0" Pin0InfoVect0LinkObjId="g_1098680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="900,-588 914,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1099370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-588 834,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44754@1" ObjectIDZND0="44752@x" ObjectIDZND1="44750@x" Pin0InfoVect0LinkObjId="SW-285379_0" Pin0InfoVect1LinkObjId="SW-285378_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="864,-588 834,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1099e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-602 834,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44750@0" ObjectIDZND0="44754@x" ObjectIDZND1="44752@x" Pin0InfoVect0LinkObjId="SW-285380_0" Pin0InfoVect1LinkObjId="SW-285379_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="834,-602 834,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_109a0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-588 834,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="44754@x" ObjectIDND1="44750@x" ObjectIDZND0="44752@1" Pin0InfoVect0LinkObjId="SW-285379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285380_0" Pin1InfoVect1LinkObjId="SW-285378_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-588 834,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_121f5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-683 784,-708 831,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_121eaf0@0" ObjectIDZND0="44776@x" Pin0InfoVect0LinkObjId="g_1221460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_121eaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="784,-683 784,-708 831,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12202c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-843 834,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44751@0" ObjectIDZND0="44753@x" ObjectIDZND1="44749@x" Pin0InfoVect0LinkObjId="SW-285377_0" Pin0InfoVect1LinkObjId="SW-285375_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="834,-843 834,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1220520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-834 834,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" ObjectIDND0="44753@x" ObjectIDND1="44751@x" ObjectIDND2="44749@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-285377_0" Pin1InfoVect1LinkObjId="SW-285376_0" Pin1InfoVect2LinkObjId="SW-285375_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="834,-834 834,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1221200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-791 834,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="44749@0" ObjectIDZND0="g_1220780@1" Pin0InfoVect0LinkObjId="g_1220780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285375_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-791 834,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1221460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-743 834,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1220780@0" ObjectIDZND0="44776@1" Pin0InfoVect0LinkObjId="g_121f5b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1220780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-743 834,-728 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_ST"/>
</svg>