<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-169" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3110 -1283 2555 1439">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="122" y2="130"/>
    <polyline arcFlag="1" points="37,122 35,122 33,121 32,121 30,120 29,119 27,118 26,116 25,114 25,113 24,111 24,109 24,107 25,105 25,104 26,102 27,101 29,99 30,98 32,97 33,97 35,96 37,96 39,96 41,97 42,97 44,98 45,99 47,101 48,102 49,104 49,105 50,107 50,109 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="50" x2="38" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.915724" x1="37" x2="37" y1="109" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="23" y2="14"/>
    <polyline arcFlag="1" points="13,23 12,23 12,23 11,23 10,23 10,24 9,24 8,25 8,25 8,26 7,27 7,27 7,28 7,29 7,30 7,30 8,31 8,32 8,32 9,33 10,33 10,34 11,34 12,34 12,34 13,34 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,34 12,34 12,34 11,34 10,34 10,35 9,35 8,36 8,36 8,37 7,38 7,38 7,39 7,40 7,41 7,41 8,42 8,43 8,43 9,44 10,44 10,45 11,45 12,45 12,45 13,45 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,45 12,45 12,45 11,45 10,45 10,46 9,46 8,47 8,47 8,48 7,49 7,49 7,50 7,51 7,52 7,52 8,53 8,54 8,54 9,55 10,55 10,56 11,56 12,56 12,56 13,56 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="65" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="13" y2="13"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="56" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="16" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape51_0">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="97" y1="54" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="100" x2="94" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="98" x2="96" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="91" y1="54" y2="54"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
   </symbol>
   <symbol id="transformer2:shape51_1">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="27" x2="28" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="22" x2="21" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="28" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
    <circle cx="23" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="7" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_278b2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278cdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278de10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278f070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278fc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27906f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27911b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2403810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2403810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27941f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27941f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2795f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2795f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2796fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2798ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2799790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_279a550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_279ae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279c540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279d130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_279e170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279f250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279fbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a06c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a1080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a24e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a3000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a3fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27a4ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27b34a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a62a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27a74d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27a8ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1449" width="2565" x="3105" y="-1288"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.315124" x1="4254" x2="4225" y1="-82" y2="-82"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.315124" x1="4254" x2="4254" y1="-71" y2="-82"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.254902" x1="4230" x2="4225" y1="-87" y2="-82"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.254902" x1="4225" x2="4219" y1="-82" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.26273" x1="4225" x2="4225" y1="-77" y2="-82"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4260" x2="4248" y1="-71" y2="-71"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.228882" x1="4255" x2="4253" y1="-64" y2="-64"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.322998" x1="4257" x2="4251" y1="-67" y2="-67"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.504854" x1="4225" x2="4225" y1="20" y2="-72"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4786" x2="4794" y1="-1052" y2="-1033"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4795" x2="4786" y1="-1056" y2="-1052"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4803" x2="4795" y1="-1037" y2="-1056"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4794" x2="4803" y1="-1033" y2="-1037"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4801" x2="4801" y1="-1067" y2="-1067"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4801" x2="4803" y1="-1067" y2="-1067"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4798" x2="4801" y1="-1067" y2="-1067"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4789" x2="4801" y1="-1058" y2="-1030"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5656" x2="5665" y1="-406" y2="-406"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-526 4109,-511 4124,-511 4116,-526 4116,-526 4116,-526 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-538 4109,-553 4124,-553 4116,-538 4116,-538 4116,-538 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-161 3563,-146 3578,-146 3570,-161 3570,-161 3570,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-173 3563,-188 3578,-188 3570,-173 3570,-173 3570,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-161 3700,-146 3715,-146 3707,-161 3707,-161 3707,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-173 3700,-188 3715,-188 3707,-173 3707,-173 3707,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-161 3838,-146 3853,-146 3845,-161 3845,-161 3845,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-173 3838,-188 3853,-188 3845,-173 3845,-173 3845,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-161 3974,-146 3989,-146 3981,-161 3981,-161 3981,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-173 3974,-188 3989,-188 3981,-173 3981,-173 3981,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-161 4109,-146 4124,-146 4116,-161 4116,-161 4116,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-173 4109,-188 4124,-188 4116,-173 4116,-173 4116,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-155 4218,-140 4233,-140 4225,-155 4225,-155 4225,-155 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-184 4218,-199 4233,-199 4225,-184 4225,-184 4225,-184 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-103 4221,-111 4229,-111 4225,-103 4225,-104 4225,-103 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-37 4218,-52 4233,-52 4225,-37 4225,-37 4225,-37 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-161 4370,-146 4385,-146 4377,-161 4377,-161 4377,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-173 4370,-188 4385,-188 4377,-173 4377,-173 4377,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-161 4988,-146 5003,-146 4995,-161 4995,-161 4995,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-173 4988,-188 5003,-188 4995,-173 4995,-173 4995,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-161 5115,-146 5130,-146 5122,-161 5122,-161 5122,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-173 5115,-188 5130,-188 5122,-173 5122,-173 5122,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-161 5247,-146 5262,-146 5254,-161 5254,-161 5254,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-173 5247,-188 5262,-188 5254,-173 5254,-173 5254,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-161 5392,-146 5407,-146 5399,-161 5399,-161 5399,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-173 5392,-188 5407,-188 5399,-173 5399,-173 5399,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-161 5524,-146 5539,-146 5531,-161 5531,-161 5531,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-173 5524,-188 5539,-188 5531,-173 5531,-173 5531,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-527 4987,-512 5002,-512 4994,-527 4994,-527 4994,-527 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-539 4987,-554 5002,-554 4994,-539 4994,-539 4994,-539 " stroke="rgb(0,255,0)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-119423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.638655 -888.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22272" ObjectName="SW-WD_JC.WD_JC_331BK"/>
     <cge:Meas_Ref ObjectId="119423"/>
    <cge:TPSR_Ref TObjectID="22272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119412">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4391.638655 -888.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22268" ObjectName="SW-WD_JC.WD_JC_332BK"/>
     <cge:Meas_Ref ObjectId="119412"/>
    <cge:TPSR_Ref TObjectID="22268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.638655 -888.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22264" ObjectName="SW-WD_JC.WD_JC_333BK"/>
     <cge:Meas_Ref ObjectId="119401"/>
    <cge:TPSR_Ref TObjectID="22264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119390">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.638655 -888.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22260" ObjectName="SW-WD_JC.WD_JC_334BK"/>
     <cge:Meas_Ref ObjectId="119390"/>
    <cge:TPSR_Ref TObjectID="22260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119440">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -691.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22280" ObjectName="SW-WD_JC.WD_JC_301BK"/>
     <cge:Meas_Ref ObjectId="119440"/>
    <cge:TPSR_Ref TObjectID="22280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119445">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -413.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22282" ObjectName="SW-WD_JC.WD_JC_401BK"/>
     <cge:Meas_Ref ObjectId="119445"/>
    <cge:TPSR_Ref TObjectID="22282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119344">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22228" ObjectName="SW-WD_JC.WD_JC_431BK"/>
     <cge:Meas_Ref ObjectId="119344"/>
    <cge:TPSR_Ref TObjectID="22228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22223" ObjectName="SW-WD_JC.WD_JC_432BK"/>
     <cge:Meas_Ref ObjectId="119337"/>
    <cge:TPSR_Ref TObjectID="22223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119330">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22218" ObjectName="SW-WD_JC.WD_JC_433BK"/>
     <cge:Meas_Ref ObjectId="119330"/>
    <cge:TPSR_Ref TObjectID="22218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22213" ObjectName="SW-WD_JC.WD_JC_434BK"/>
     <cge:Meas_Ref ObjectId="119323"/>
    <cge:TPSR_Ref TObjectID="22213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119316">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22208" ObjectName="SW-WD_JC.WD_JC_435BK"/>
     <cge:Meas_Ref ObjectId="119316"/>
    <cge:TPSR_Ref TObjectID="22208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119434">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22276" ObjectName="SW-WD_JC.WD_JC_436BK"/>
     <cge:Meas_Ref ObjectId="119434"/>
    <cge:TPSR_Ref TObjectID="22276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22252" ObjectName="SW-WD_JC.WD_JC_441BK"/>
     <cge:Meas_Ref ObjectId="119378"/>
    <cge:TPSR_Ref TObjectID="22252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119385">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4676.000000 -274.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22257" ObjectName="SW-WD_JC.WD_JC_412BK"/>
     <cge:Meas_Ref ObjectId="119385"/>
    <cge:TPSR_Ref TObjectID="22257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119372">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22248" ObjectName="SW-WD_JC.WD_JC_442BK"/>
     <cge:Meas_Ref ObjectId="119372"/>
    <cge:TPSR_Ref TObjectID="22248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119365">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5245.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22243" ObjectName="SW-WD_JC.WD_JC_443BK"/>
     <cge:Meas_Ref ObjectId="119365"/>
    <cge:TPSR_Ref TObjectID="22243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119358">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5390.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22238" ObjectName="SW-WD_JC.WD_JC_444BK"/>
     <cge:Meas_Ref ObjectId="119358"/>
    <cge:TPSR_Ref TObjectID="22238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119351">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5522.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22233" ObjectName="SW-WD_JC.WD_JC_445BK"/>
     <cge:Meas_Ref ObjectId="119351"/>
    <cge:TPSR_Ref TObjectID="22233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119461">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4985.000000 -692.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22285" ObjectName="SW-WD_JC.WD_JC_302BK"/>
     <cge:Meas_Ref ObjectId="119461"/>
    <cge:TPSR_Ref TObjectID="22285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119465">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4985.000000 -414.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22287" ObjectName="SW-WD_JC.WD_JC_402BK"/>
     <cge:Meas_Ref ObjectId="119465"/>
    <cge:TPSR_Ref TObjectID="22287"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c1d7e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 -989.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f72f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 -989.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f4810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 -989.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2848b80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5010.000000 -989.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_324ae50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -655.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c53b10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -171.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c469b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4824.000000 -171.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WD" endPointId="0" endStationName="WD_JC" flowDrawDirect="1" flowShape="0" id="AC-35kV.jincheng_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4697,-1151 4697,-1184 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37442" ObjectName="AC-35kV.jincheng_line"/>
    <cge:TPSR_Ref TObjectID="37442_SS-169"/></metadata>
   <polyline fill="none" opacity="0" points="4697,-1151 4697,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_JC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_JinYiChaTjc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4993,-1153 4993,-1186 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37810" ObjectName="AC-35kV.LN_JinYiChaTjc"/>
    <cge:TPSR_Ref TObjectID="37810_SS-169"/></metadata>
   <polyline fill="none" opacity="0" points="4993,-1153 4993,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_DXS" endPointId="0" endStationName="WD_JC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_jinda" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4116,-1150 4116,-1183 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38121" ObjectName="AC-35kV.LN_jinda"/>
    <cge:TPSR_Ref TObjectID="38121_SS-169"/></metadata>
   <polyline fill="none" opacity="0" points="4116,-1150 4116,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_XN" endPointId="0" endStationName="WD_JC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_luwu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4401,-1139 4401,-1185 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39628" ObjectName="AC-35kV.LN_luwu"/>
    <cge:TPSR_Ref TObjectID="39628_SS-169"/></metadata>
   <polyline fill="none" opacity="0" points="4401,-1139 4401,-1185 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.431Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33614" ObjectName="EC-WD_JC.431Ld"/>
    <cge:TPSR_Ref TObjectID="33614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.432Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33615" ObjectName="EC-WD_JC.432Ld"/>
    <cge:TPSR_Ref TObjectID="33615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.433Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33912" ObjectName="EC-WD_JC.433Ld"/>
    <cge:TPSR_Ref TObjectID="33912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.434Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33613" ObjectName="EC-WD_JC.434Ld"/>
    <cge:TPSR_Ref TObjectID="33613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.435Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33616" ObjectName="EC-WD_JC.435Ld"/>
    <cge:TPSR_Ref TObjectID="33616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.441Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33617" ObjectName="EC-WD_JC.441Ld"/>
    <cge:TPSR_Ref TObjectID="33617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.442Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33612" ObjectName="EC-WD_JC.442Ld"/>
    <cge:TPSR_Ref TObjectID="33612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.JC_443Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5245.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33941" ObjectName="EC-WD_JC.JC_443Ld"/>
    <cge:TPSR_Ref TObjectID="33941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.444Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5390.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33611" ObjectName="EC-WD_JC.444Ld"/>
    <cge:TPSR_Ref TObjectID="33611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JC.445Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5522.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33610" ObjectName="EC-WD_JC.445Ld"/>
    <cge:TPSR_Ref TObjectID="33610"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1eeb8d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -977.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2863350" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4322.000000 -977.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29a6200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4618.000000 -977.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32345a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.000000 -977.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_264ed40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -723.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32d3130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3302cc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3229e30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef7db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc09f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c3e240" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2402560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3caae60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5020.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_263bea0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 -239.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2290dd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 -239.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_283e9f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5147.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f23ad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5279.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e9150" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5424.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_275f3c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5556.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_34d1be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-983 4055,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22274@0" ObjectIDZND0="g_1eeb8d0@0" Pin0InfoVect0LinkObjId="g_1eeb8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-983 4055,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_227d590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-851 4116,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22275@0" ObjectIDZND0="22300@0" Pin0InfoVect0LinkObjId="g_29ac670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-851 4116,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eb6450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-897 4116,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22272@0" ObjectIDZND0="22275@1" Pin0InfoVect0LinkObjId="SW-119426_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-897 4116,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cb6f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-924 4116,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22272@1" ObjectIDZND0="22273@0" Pin0InfoVect0LinkObjId="SW-119424_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-924 4116,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0a680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-1088 4116,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3302750@0" ObjectIDZND0="g_1c1d7e0@0" ObjectIDZND1="22274@x" ObjectIDZND2="22273@x" Pin0InfoVect0LinkObjId="g_1c1d7e0_0" Pin0InfoVect1LinkObjId="SW-119425_0" Pin0InfoVect2LinkObjId="SW-119424_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3302750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-1088 4116,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d6200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-1088 4116,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3302750@0" ObjectIDND1="g_1c1d7e0@0" ObjectIDND2="22274@x" ObjectIDZND0="38121@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3302750_0" Pin1InfoVect1LinkObjId="g_1c1d7e0_0" Pin1InfoVect2LinkObjId="SW-119425_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-1088 4116,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bf320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-1059 4155,-1068 4116,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_1c1d7e0@0" ObjectIDZND0="g_3302750@0" ObjectIDZND1="38121@1" ObjectIDZND2="22274@x" Pin0InfoVect0LinkObjId="g_3302750_0" Pin0InfoVect1LinkObjId="g_29d6200_1" Pin0InfoVect2LinkObjId="SW-119425_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c1d7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-1059 4155,-1068 4116,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a5b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-983 4116,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22274@x" ObjectIDND1="22273@x" ObjectIDZND0="g_1c1d7e0@0" ObjectIDZND1="g_3302750@0" ObjectIDZND2="38121@1" Pin0InfoVect0LinkObjId="g_1c1d7e0_0" Pin0InfoVect1LinkObjId="g_3302750_0" Pin0InfoVect2LinkObjId="g_29d6200_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119425_0" Pin1InfoVect1LinkObjId="SW-119424_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-983 4116,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a7ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-1068 4116,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1c1d7e0@0" ObjectIDND1="22274@x" ObjectIDND2="22273@x" ObjectIDZND0="g_3302750@0" ObjectIDZND1="38121@1" Pin0InfoVect0LinkObjId="g_3302750_0" Pin0InfoVect1LinkObjId="g_29d6200_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c1d7e0_0" Pin1InfoVect1LinkObjId="SW-119425_0" Pin1InfoVect2LinkObjId="SW-119424_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-1068 4116,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aa840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4101,-983 4116,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22274@1" ObjectIDZND0="g_1c1d7e0@0" ObjectIDZND1="g_3302750@0" ObjectIDZND2="38121@1" Pin0InfoVect0LinkObjId="g_1c1d7e0_0" Pin0InfoVect1LinkObjId="g_3302750_0" Pin0InfoVect2LinkObjId="g_29d6200_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119425_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4101,-983 4116,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aa3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-983 4116,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_1c1d7e0@0" ObjectIDND1="g_3302750@0" ObjectIDND2="38121@1" ObjectIDZND0="22273@1" Pin0InfoVect0LinkObjId="SW-119424_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c1d7e0_0" Pin1InfoVect1LinkObjId="g_3302750_0" Pin1InfoVect2LinkObjId="g_29d6200_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-983 4116,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ac410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-983 4340,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22270@0" ObjectIDZND0="g_2863350@0" Pin0InfoVect0LinkObjId="g_2863350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-983 4340,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ac670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-851 4401,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22271@0" ObjectIDZND0="22300@0" Pin0InfoVect0LinkObjId="g_227d590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-851 4401,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29acaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-897 4401,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22268@0" ObjectIDZND0="22271@1" Pin0InfoVect0LinkObjId="SW-119415_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-897 4401,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f4a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-924 4401,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22268@1" ObjectIDZND0="22269@0" Pin0InfoVect0LinkObjId="SW-119413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-924 4401,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f6570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-1088 4401,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_26680d0@0" ObjectIDZND0="g_29f72f0@0" ObjectIDZND1="22270@x" ObjectIDZND2="22269@x" Pin0InfoVect0LinkObjId="g_29f72f0_0" Pin0InfoVect1LinkObjId="SW-119414_0" Pin0InfoVect2LinkObjId="SW-119413_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26680d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-1088 4401,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f5c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-1088 4401,-1142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_26680d0@0" ObjectIDND1="g_29f72f0@0" ObjectIDND2="22270@x" ObjectIDZND0="39628@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26680d0_0" Pin1InfoVect1LinkObjId="g_29f72f0_0" Pin1InfoVect2LinkObjId="SW-119414_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-1088 4401,-1142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f79b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4440,-1059 4440,-1068 4401,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_29f72f0@0" ObjectIDZND0="22270@x" ObjectIDZND1="22269@x" ObjectIDZND2="g_26680d0@0" Pin0InfoVect0LinkObjId="SW-119414_0" Pin0InfoVect1LinkObjId="SW-119413_0" Pin0InfoVect2LinkObjId="g_26680d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f72f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4440,-1059 4440,-1068 4401,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f5370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-983 4401,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22270@x" ObjectIDND1="22269@x" ObjectIDZND0="g_29f72f0@0" ObjectIDZND1="g_26680d0@0" ObjectIDZND2="39628@1" Pin0InfoVect0LinkObjId="g_29f72f0_0" Pin0InfoVect1LinkObjId="g_26680d0_0" Pin0InfoVect2LinkObjId="g_29f5c70_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119414_0" Pin1InfoVect1LinkObjId="SW-119413_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-983 4401,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a010d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-1068 4401,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_29f72f0@0" ObjectIDND1="22270@x" ObjectIDND2="22269@x" ObjectIDZND0="g_26680d0@0" ObjectIDZND1="39628@1" Pin0InfoVect0LinkObjId="g_26680d0_0" Pin0InfoVect1LinkObjId="g_29f5c70_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29f72f0_0" Pin1InfoVect1LinkObjId="SW-119414_0" Pin1InfoVect2LinkObjId="SW-119413_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-1068 4401,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f7790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-983 4401,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22270@1" ObjectIDZND0="g_29f72f0@0" ObjectIDZND1="g_26680d0@0" ObjectIDZND2="39628@1" Pin0InfoVect0LinkObjId="g_29f72f0_0" Pin0InfoVect1LinkObjId="g_26680d0_0" Pin0InfoVect2LinkObjId="g_29f5c70_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-983 4401,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32da240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-983 4401,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_29f72f0@0" ObjectIDND1="g_26680d0@0" ObjectIDND2="39628@1" ObjectIDZND0="22269@1" Pin0InfoVect0LinkObjId="SW-119413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29f72f0_0" Pin1InfoVect1LinkObjId="g_26680d0_0" Pin1InfoVect2LinkObjId="g_29f5c70_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-983 4401,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_321cdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-983 4636,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22266@0" ObjectIDZND0="g_29a6200@0" Pin0InfoVect0LinkObjId="g_29a6200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-983 4636,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a6680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-851 4697,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22267@0" ObjectIDZND0="22300@0" Pin0InfoVect0LinkObjId="g_227d590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-851 4697,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a8100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-897 4697,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22264@0" ObjectIDZND0="22267@1" Pin0InfoVect0LinkObjId="SW-119404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-897 4697,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a8560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-924 4697,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22264@1" ObjectIDZND0="22265@0" Pin0InfoVect0LinkObjId="SW-119402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-924 4697,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5c920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4676,-1088 4697,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_29a6f80@0" ObjectIDZND0="22266@x" ObjectIDZND1="22265@x" ObjectIDZND2="g_29f4810@0" Pin0InfoVect0LinkObjId="SW-119403_0" Pin0InfoVect1LinkObjId="SW-119402_0" Pin0InfoVect2LinkObjId="g_29f4810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29a6f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4676,-1088 4697,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_324ee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-1088 4697,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_29a6f80@0" ObjectIDND1="22266@x" ObjectIDND2="22265@x" ObjectIDZND0="37442@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29a6f80_0" Pin1InfoVect1LinkObjId="SW-119403_0" Pin1InfoVect2LinkObjId="SW-119402_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-1088 4697,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aaa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4736,-1059 4736,-1068 4697,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_29f4810@0" ObjectIDZND0="22266@x" ObjectIDZND1="22265@x" ObjectIDZND2="g_29a6f80@0" Pin0InfoVect0LinkObjId="SW-119403_0" Pin0InfoVect1LinkObjId="SW-119402_0" Pin0InfoVect2LinkObjId="g_29a6f80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f4810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4736,-1059 4736,-1068 4697,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f3610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-983 4697,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22266@x" ObjectIDND1="22265@x" ObjectIDZND0="g_29f4810@0" ObjectIDZND1="g_29a6f80@0" ObjectIDZND2="37442@1" Pin0InfoVect0LinkObjId="g_29f4810_0" Pin0InfoVect1LinkObjId="g_29a6f80_0" Pin0InfoVect2LinkObjId="g_324ee20_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119403_0" Pin1InfoVect1LinkObjId="SW-119402_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-983 4697,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b6b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-1068 4697,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="22266@x" ObjectIDND1="22265@x" ObjectIDND2="g_29f4810@0" ObjectIDZND0="g_29a6f80@0" ObjectIDZND1="37442@1" Pin0InfoVect0LinkObjId="g_29a6f80_0" Pin0InfoVect1LinkObjId="g_324ee20_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119403_0" Pin1InfoVect1LinkObjId="SW-119402_0" Pin1InfoVect2LinkObjId="g_29f4810_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-1068 4697,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f84d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-983 4697,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22266@1" ObjectIDZND0="g_29f4810@0" ObjectIDZND1="g_29a6f80@0" ObjectIDZND2="37442@1" Pin0InfoVect0LinkObjId="g_29f4810_0" Pin0InfoVect1LinkObjId="g_29a6f80_0" Pin0InfoVect2LinkObjId="g_324ee20_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119403_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4682,-983 4697,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f7510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-983 4697,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="22266@x" ObjectIDND1="g_29f4810@0" ObjectIDND2="g_29a6f80@0" ObjectIDZND0="22265@1" Pin0InfoVect0LinkObjId="SW-119402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119403_0" Pin1InfoVect1LinkObjId="g_29f4810_0" Pin1InfoVect2LinkObjId="g_29a6f80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-983 4697,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229e100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4942,-983 4932,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22262@0" ObjectIDZND0="g_32345a0@0" Pin0InfoVect0LinkObjId="g_32345a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4942,-983 4932,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_275be60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-851 4993,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22263@0" ObjectIDZND0="22300@0" Pin0InfoVect0LinkObjId="g_227d590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-851 4993,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_323cd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-897 4993,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22260@0" ObjectIDZND0="22263@1" Pin0InfoVect0LinkObjId="SW-119393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-897 4993,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5c400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-924 4993,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22260@1" ObjectIDZND0="22261@0" Pin0InfoVect0LinkObjId="SW-119391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-924 4993,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2f260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4972,-1088 4993,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_266ab90@0" ObjectIDZND0="22262@x" ObjectIDZND1="22261@x" ObjectIDZND2="g_2848b80@0" Pin0InfoVect0LinkObjId="SW-119392_0" Pin0InfoVect1LinkObjId="SW-119391_0" Pin0InfoVect2LinkObjId="g_2848b80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_266ab90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4972,-1088 4993,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32ff020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-1088 4993,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_266ab90@0" ObjectIDND1="22262@x" ObjectIDND2="22261@x" ObjectIDZND0="37810@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_266ab90_0" Pin1InfoVect1LinkObjId="SW-119392_0" Pin1InfoVect2LinkObjId="SW-119391_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-1088 4993,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_270f9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5032,-1059 5032,-1068 4993,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2848b80@0" ObjectIDZND0="22262@x" ObjectIDZND1="22261@x" ObjectIDZND2="g_266ab90@0" Pin0InfoVect0LinkObjId="SW-119392_0" Pin0InfoVect1LinkObjId="SW-119391_0" Pin0InfoVect2LinkObjId="g_266ab90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2848b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5032,-1059 5032,-1068 4993,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f24d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-983 4993,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22262@x" ObjectIDND1="22261@x" ObjectIDZND0="g_2848b80@0" ObjectIDZND1="g_266ab90@0" ObjectIDZND2="37810@1" Pin0InfoVect0LinkObjId="g_2848b80_0" Pin0InfoVect1LinkObjId="g_266ab90_0" Pin0InfoVect2LinkObjId="g_32ff020_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119392_0" Pin1InfoVect1LinkObjId="SW-119391_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-983 4993,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1edb820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-1068 4993,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="22262@x" ObjectIDND1="22261@x" ObjectIDND2="g_2848b80@0" ObjectIDZND0="g_266ab90@0" ObjectIDZND1="37810@1" Pin0InfoVect0LinkObjId="g_266ab90_0" Pin0InfoVect1LinkObjId="g_32ff020_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119392_0" Pin1InfoVect1LinkObjId="SW-119391_0" Pin1InfoVect2LinkObjId="g_2848b80_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-1068 4993,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3297cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4978,-983 4993,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22262@1" ObjectIDZND0="g_2848b80@0" ObjectIDZND1="g_266ab90@0" ObjectIDZND2="37810@1" Pin0InfoVect0LinkObjId="g_2848b80_0" Pin0InfoVect1LinkObjId="g_266ab90_0" Pin0InfoVect2LinkObjId="g_32ff020_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4978,-983 4993,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28263f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-983 4993,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="22262@x" ObjectIDND1="g_2848b80@0" ObjectIDND2="g_266ab90@0" ObjectIDZND0="22261@1" Pin0InfoVect0LinkObjId="SW-119391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119392_0" Pin1InfoVect1LinkObjId="g_2848b80_0" Pin1InfoVect2LinkObjId="g_266ab90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-983 4993,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c488f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-778 4116,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22281@1" ObjectIDZND0="22300@0" Pin0InfoVect0LinkObjId="g_227d590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119442_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-778 4116,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33596d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-726 4116,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22280@1" ObjectIDZND0="22281@0" Pin0InfoVect0LinkObjId="SW-119442_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-726 4116,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32d2350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-460 4116,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22284@0" ObjectIDZND0="22282@1" Pin0InfoVect0LinkObjId="SW-119445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-460 4116,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29e9bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-421 4116,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22282@0" ObjectIDZND0="22283@1" Pin0InfoVect0LinkObjId="SW-119447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119445_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-421 4116,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32d9d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-373 4116,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22283@0" ObjectIDZND0="22301@0" Pin0InfoVect0LinkObjId="g_323a780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-373 4116,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a0dc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-699 4116,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22280@0" ObjectIDZND0="22297@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-699 4116,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27099c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-572 4116,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="22297@1" ObjectIDZND0="22284@1" Pin0InfoVect0LinkObjId="SW-119448_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a0dc80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-572 4116,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272e990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-832 4556,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22300@0" ObjectIDZND0="22290@1" Pin0InfoVect0LinkObjId="SW-119481_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_227d590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-832 4556,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26d9170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4516,-717 4516,-729 4556,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_1ee4280@0" ObjectIDZND0="22290@x" ObjectIDZND1="g_324ae50@0" ObjectIDZND2="22291@x" Pin0InfoVect0LinkObjId="SW-119481_0" Pin0InfoVect1LinkObjId="g_324ae50_0" Pin0InfoVect2LinkObjId="SW-119482_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ee4280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4516,-717 4516,-729 4556,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39c1760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-765 4556,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="22290@0" ObjectIDZND0="g_1ee4280@0" ObjectIDZND1="g_324ae50@0" ObjectIDZND2="22291@x" Pin0InfoVect0LinkObjId="g_1ee4280_0" Pin0InfoVect1LinkObjId="g_324ae50_0" Pin0InfoVect2LinkObjId="SW-119482_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119481_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-765 4556,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ef77d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-729 4556,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1ee4280@0" ObjectIDND1="22290@x" ObjectIDND2="22291@x" ObjectIDZND0="g_324ae50@0" Pin0InfoVect0LinkObjId="g_324ae50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ee4280_0" Pin1InfoVect1LinkObjId="SW-119481_0" Pin1InfoVect2LinkObjId="SW-119482_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-729 4556,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3be9270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4616,-729 4607,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_264ed40@0" ObjectIDZND0="22291@1" Pin0InfoVect0LinkObjId="SW-119482_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_264ed40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4616,-729 4607,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2693c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4571,-729 4556,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="22291@0" ObjectIDZND0="g_1ee4280@0" ObjectIDZND1="22290@x" ObjectIDZND2="g_324ae50@0" Pin0InfoVect0LinkObjId="g_1ee4280_0" Pin0InfoVect1LinkObjId="SW-119481_0" Pin0InfoVect2LinkObjId="g_324ae50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4571,-729 4556,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_323a780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-335 3570,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22230@1" ObjectIDZND0="22301@0" Pin0InfoVect0LinkObjId="g_32d9d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119346_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-335 3570,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3236f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3538,-77 3538,-89 3570,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3338f80@0" ObjectIDZND0="33614@x" ObjectIDZND1="22232@x" Pin0InfoVect0LinkObjId="EC-WD_JC.431Ld_0" Pin0InfoVect1LinkObjId="SW-119348_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3338f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3538,-77 3538,-89 3570,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3237180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3538,-183 3538,-195 3570,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_335e4f0@0" ObjectIDZND0="22232@x" ObjectIDZND1="22229@x" Pin0InfoVect0LinkObjId="SW-119348_0" Pin0InfoVect1LinkObjId="SW-119345_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_335e4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3538,-183 3538,-195 3570,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d5db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-241 3570,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22229@1" ObjectIDZND0="22228@0" Pin0InfoVect0LinkObjId="SW-119344_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119345_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-241 3570,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-195 3570,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_335e4f0@0" ObjectIDND1="22229@x" ObjectIDZND0="22232@1" Pin0InfoVect0LinkObjId="SW-119348_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_335e4f0_0" Pin1InfoVect1LinkObjId="SW-119345_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-195 3570,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335a960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-205 3570,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22229@0" ObjectIDZND0="g_335e4f0@0" ObjectIDZND1="22232@x" Pin0InfoVect0LinkObjId="g_335e4f0_0" Pin0InfoVect1LinkObjId="SW-119348_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119345_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-205 3570,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a10f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-11 3570,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33614@0" ObjectIDZND0="g_3338f80@0" ObjectIDZND1="22232@x" Pin0InfoVect0LinkObjId="g_3338f80_0" Pin0InfoVect1LinkObjId="SW-119348_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.431Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-11 3570,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a0fb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-89 3570,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_3338f80@0" ObjectIDND1="33614@x" ObjectIDZND0="22232@0" Pin0InfoVect0LinkObjId="SW-119348_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3338f80_0" Pin1InfoVect1LinkObjId="EC-WD_JC.431Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-89 3570,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32d4e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-217 3601,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_32d3130@0" ObjectIDZND0="22231@0" Pin0InfoVect0LinkObjId="SW-119347_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32d3130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-217 3601,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32d5ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-290 3601,-290 3601,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22228@x" ObjectIDND1="22230@x" ObjectIDZND0="22231@1" Pin0InfoVect0LinkObjId="SW-119347_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119344_0" Pin1InfoVect1LinkObjId="SW-119346_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-290 3601,-290 3601,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_285add0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-335 3707,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22225@1" ObjectIDZND0="22301@0" Pin0InfoVect0LinkObjId="g_32d9d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119339_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-335 3707,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3beb520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3675,-77 3675,-89 3707,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_324e410@0" ObjectIDZND0="33615@x" ObjectIDZND1="22227@x" Pin0InfoVect0LinkObjId="EC-WD_JC.432Ld_0" Pin0InfoVect1LinkObjId="SW-119341_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_324e410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3675,-77 3675,-89 3707,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26699f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3675,-183 3675,-195 3707,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_285b000@0" ObjectIDZND0="22227@x" ObjectIDZND1="22224@x" Pin0InfoVect0LinkObjId="SW-119341_0" Pin0InfoVect1LinkObjId="SW-119338_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_285b000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3675,-183 3675,-195 3707,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2669c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-241 3707,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22224@1" ObjectIDZND0="22223@0" Pin0InfoVect0LinkObjId="SW-119337_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119338_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-241 3707,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27100b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-195 3707,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_285b000@0" ObjectIDND1="22224@x" ObjectIDZND0="22227@1" Pin0InfoVect0LinkObjId="SW-119341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_285b000_0" Pin1InfoVect1LinkObjId="SW-119338_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-195 3707,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2710310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-205 3707,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22224@0" ObjectIDZND0="22227@x" ObjectIDZND1="g_285b000@0" Pin0InfoVect0LinkObjId="SW-119341_0" Pin0InfoVect1LinkObjId="g_285b000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-205 3707,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3239810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-11 3707,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33615@0" ObjectIDZND0="g_324e410@0" ObjectIDZND1="22227@x" Pin0InfoVect0LinkObjId="g_324e410_0" Pin0InfoVect1LinkObjId="SW-119341_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.432Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-11 3707,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3239a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-89 3707,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33615@x" ObjectIDND1="g_324e410@0" ObjectIDZND0="22227@0" Pin0InfoVect0LinkObjId="SW-119341_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JC.432Ld_0" Pin1InfoVect1LinkObjId="g_324e410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-89 3707,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a02c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-217 3738,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3302cc0@0" ObjectIDZND0="22226@0" Pin0InfoVect0LinkObjId="SW-119340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3302cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-217 3738,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a02eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-290 3738,-290 3738,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22225@x" ObjectIDND1="22223@x" ObjectIDZND0="22226@1" Pin0InfoVect0LinkObjId="SW-119340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119339_0" Pin1InfoVect1LinkObjId="SW-119337_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-290 3738,-290 3738,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a07aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-335 3845,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22220@1" ObjectIDZND0="22301@0" Pin0InfoVect0LinkObjId="g_32d9d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-335 3845,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b4bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-77 3813,-89 3845,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3221d10@0" ObjectIDZND0="33912@x" ObjectIDZND1="22222@x" Pin0InfoVect0LinkObjId="EC-WD_JC.433Ld_0" Pin0InfoVect1LinkObjId="SW-119334_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3221d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-77 3813,-89 3845,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b4e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-183 3813,-195 3845,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2a07d10@0" ObjectIDZND0="22222@x" ObjectIDZND1="22219@x" Pin0InfoVect0LinkObjId="SW-119334_0" Pin0InfoVect1LinkObjId="SW-119331_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a07d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-183 3813,-195 3845,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-195 3845,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2a07d10@0" ObjectIDND1="22219@x" ObjectIDZND0="22222@1" Pin0InfoVect0LinkObjId="SW-119334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a07d10_0" Pin1InfoVect1LinkObjId="SW-119331_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-195 3845,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-205 3845,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22219@0" ObjectIDZND0="22222@x" ObjectIDZND1="g_2a07d10@0" Pin0InfoVect0LinkObjId="SW-119334_0" Pin0InfoVect1LinkObjId="g_2a07d10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-205 3845,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32495d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-11 3845,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33912@0" ObjectIDZND0="g_3221d10@0" ObjectIDZND1="22222@x" Pin0InfoVect0LinkObjId="g_3221d10_0" Pin0InfoVect1LinkObjId="SW-119334_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.433Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-11 3845,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3249830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-89 3845,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33912@x" ObjectIDND1="g_3221d10@0" ObjectIDZND0="22222@0" Pin0InfoVect0LinkObjId="SW-119334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JC.433Ld_0" Pin1InfoVect1LinkObjId="g_3221d10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-89 3845,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b5e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3876,-217 3876,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3229e30@0" ObjectIDZND0="22221@0" Pin0InfoVect0LinkObjId="SW-119333_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3229e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3876,-217 3876,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b60c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-290 3876,-290 3876,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22220@x" ObjectIDND1="22218@x" ObjectIDZND0="22221@1" Pin0InfoVect0LinkObjId="SW-119333_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119332_0" Pin1InfoVect1LinkObjId="SW-119330_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-290 3876,-290 3876,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ef4cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-335 3981,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22215@1" ObjectIDZND0="22301@0" Pin0InfoVect0LinkObjId="g_32d9d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-335 3981,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_324c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-77 3949,-89 3981,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2b02940@0" ObjectIDZND0="33613@x" ObjectIDZND1="22217@x" Pin0InfoVect0LinkObjId="EC-WD_JC.434Ld_0" Pin0InfoVect1LinkObjId="SW-119327_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b02940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-77 3949,-89 3981,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275ae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-183 3949,-195 3981,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ef4f40@0" ObjectIDZND0="22217@x" ObjectIDZND1="22214@x" Pin0InfoVect0LinkObjId="SW-119327_0" Pin0InfoVect1LinkObjId="SW-119324_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ef4f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-183 3949,-195 3981,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275b070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-241 3981,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22214@1" ObjectIDZND0="22213@0" Pin0InfoVect0LinkObjId="SW-119323_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119324_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-241 3981,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb9440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-195 3981,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1ef4f40@0" ObjectIDND1="22214@x" ObjectIDZND0="22217@1" Pin0InfoVect0LinkObjId="SW-119327_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ef4f40_0" Pin1InfoVect1LinkObjId="SW-119324_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-195 3981,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb9680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-205 3981,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22214@0" ObjectIDZND0="22217@x" ObjectIDZND1="g_1ef4f40@0" Pin0InfoVect0LinkObjId="SW-119327_0" Pin0InfoVect1LinkObjId="g_1ef4f40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-205 3981,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb98e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-11 3981,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33613@0" ObjectIDZND0="g_2b02940@0" ObjectIDZND1="22217@x" Pin0InfoVect0LinkObjId="g_2b02940_0" Pin0InfoVect1LinkObjId="SW-119327_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.434Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-11 3981,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ddcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-89 3981,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33613@x" ObjectIDND1="g_2b02940@0" ObjectIDZND0="22217@0" Pin0InfoVect0LinkObjId="SW-119327_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JC.434Ld_0" Pin1InfoVect1LinkObjId="g_2b02940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-89 3981,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eff5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-217 4012,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ef7db0@0" ObjectIDZND0="22216@0" Pin0InfoVect0LinkObjId="SW-119326_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ef7db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-217 4012,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_261c750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-290 4012,-290 4012,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22215@x" ObjectIDND1="22213@x" ObjectIDZND0="22216@1" Pin0InfoVect0LinkObjId="SW-119326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119325_0" Pin1InfoVect1LinkObjId="SW-119323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-290 4012,-290 4012,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3229570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-335 4116,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22210@1" ObjectIDZND0="22301@0" Pin0InfoVect0LinkObjId="g_32d9d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-335 4116,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca3b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-77 4084,-89 4116,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1c0c580@0" ObjectIDZND0="33616@x" ObjectIDZND1="22256@x" Pin0InfoVect0LinkObjId="EC-WD_JC.435Ld_0" Pin0InfoVect1LinkObjId="SW-119382_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c0c580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-77 4084,-89 4116,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca3d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-183 4084,-195 4116,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_23eb910@0" ObjectIDZND0="22256@x" ObjectIDZND1="22209@x" Pin0InfoVect0LinkObjId="SW-119382_0" Pin0InfoVect1LinkObjId="SW-119317_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23eb910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-183 4084,-195 4116,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca3f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-241 4116,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22209@1" ObjectIDZND0="22208@0" Pin0InfoVect0LinkObjId="SW-119316_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119317_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-241 4116,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b9ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-195 4116,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_23eb910@0" ObjectIDND1="22209@x" ObjectIDZND0="22256@1" Pin0InfoVect0LinkObjId="SW-119382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23eb910_0" Pin1InfoVect1LinkObjId="SW-119317_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-195 4116,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27ba250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-205 4116,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22209@0" ObjectIDZND0="22256@x" ObjectIDZND1="g_23eb910@0" Pin0InfoVect0LinkObjId="SW-119382_0" Pin0InfoVect1LinkObjId="g_23eb910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119317_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-205 4116,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242ac90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-11 4116,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33616@0" ObjectIDZND0="g_1c0c580@0" ObjectIDZND1="22256@x" Pin0InfoVect0LinkObjId="g_1c0c580_0" Pin0InfoVect1LinkObjId="SW-119382_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.435Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-11 4116,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242aef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-89 4116,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33616@x" ObjectIDND1="g_1c0c580@0" ObjectIDZND0="22256@0" Pin0InfoVect0LinkObjId="SW-119382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JC.435Ld_0" Pin1InfoVect1LinkObjId="g_1c0c580_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-89 4116,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27aa300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4147,-217 4147,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1cc09f0@0" ObjectIDZND0="22211@0" Pin0InfoVect0LinkObjId="SW-119319_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cc09f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4147,-217 4147,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27aa560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-290 4147,-290 4147,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22210@x" ObjectIDND1="22208@x" ObjectIDZND0="22211@1" Pin0InfoVect0LinkObjId="SW-119319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119318_0" Pin1InfoVect1LinkObjId="SW-119316_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-290 4147,-290 4147,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27515f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-241 3845,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22219@1" ObjectIDZND0="22218@0" Pin0InfoVect0LinkObjId="SW-119330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119331_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-241 3845,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_323b6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-335 4225,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="22301@0" Pin0InfoVect0LinkObjId="g_32d9d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c1d7e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-335 4225,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2825af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4256,-217 4256,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3c3e240@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1c1d7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3e240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4256,-217 4256,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2825d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-290 4256,-290 4256,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_27520f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1c1d7e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c1d7e0_0" Pin1InfoVect1LinkObjId="g_27520f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-290 4256,-290 4256,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ff6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-279 3570,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22228@1" ObjectIDZND0="22231@x" ObjectIDZND1="22230@x" Pin0InfoVect0LinkObjId="SW-119347_0" Pin0InfoVect1LinkObjId="SW-119346_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-279 3570,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d09fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-290 3570,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22231@x" ObjectIDND1="22228@x" ObjectIDZND0="22230@0" Pin0InfoVect0LinkObjId="SW-119346_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119347_0" Pin1InfoVect1LinkObjId="SW-119344_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-290 3570,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2859520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-299 3707,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22225@0" ObjectIDZND0="22226@x" ObjectIDZND1="22223@x" Pin0InfoVect0LinkObjId="SW-119340_0" Pin0InfoVect1LinkObjId="SW-119337_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119339_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-299 3707,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2859760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-290 3707,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22226@x" ObjectIDND1="22225@x" ObjectIDZND0="22223@1" Pin0InfoVect0LinkObjId="SW-119337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119340_0" Pin1InfoVect1LinkObjId="SW-119339_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-290 3707,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2422670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-299 3845,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22220@0" ObjectIDZND0="22221@x" ObjectIDZND1="22218@x" Pin0InfoVect0LinkObjId="SW-119333_0" Pin0InfoVect1LinkObjId="SW-119330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-299 3845,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27570a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-290 3845,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22221@x" ObjectIDND1="22220@x" ObjectIDZND0="22218@1" Pin0InfoVect0LinkObjId="SW-119330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119333_0" Pin1InfoVect1LinkObjId="SW-119332_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-290 3845,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f2bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-299 3981,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22215@0" ObjectIDZND0="22216@x" ObjectIDZND1="22213@x" Pin0InfoVect0LinkObjId="SW-119326_0" Pin0InfoVect1LinkObjId="SW-119323_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-299 3981,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f2c010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-290 3981,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22216@x" ObjectIDND1="22215@x" ObjectIDZND0="22213@1" Pin0InfoVect0LinkObjId="SW-119323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119326_0" Pin1InfoVect1LinkObjId="SW-119325_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-290 3981,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27224d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-299 4116,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22210@0" ObjectIDZND0="22211@x" ObjectIDZND1="22208@x" Pin0InfoVect0LinkObjId="SW-119319_0" Pin0InfoVect1LinkObjId="SW-119316_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-299 4116,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26668b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-290 4116,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22211@x" ObjectIDND1="22210@x" ObjectIDZND0="22208@1" Pin0InfoVect0LinkObjId="SW-119316_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119319_0" Pin1InfoVect1LinkObjId="SW-119318_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-290 4116,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2751e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-299 4225,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_27520f0@0" Pin0InfoVect0LinkObjId="g_1c1d7e0_0" Pin0InfoVect1LinkObjId="g_27520f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c1d7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-299 4225,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26c7de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-290 4225,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_27520f0@0" Pin0InfoVect0LinkObjId="g_27520f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c1d7e0_0" Pin1InfoVect1LinkObjId="g_1c1d7e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-290 4225,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c775f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-335 4377,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22278@1" ObjectIDZND0="22301@0" Pin0InfoVect0LinkObjId="g_32d9d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-335 4377,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c9400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-77 4345,-89 4377,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="g_26fd8c0@0" ObjectIDZND0="37474@x" ObjectIDZND1="41643@x" Pin0InfoVect0LinkObjId="SW-248339_0" Pin0InfoVect1LinkObjId="CB-WD_JC.WD_JC_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26fd8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-77 4345,-89 4377,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c9630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-183 4345,-195 4377,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3c77820@0" ObjectIDZND0="37474@x" ObjectIDZND1="22277@x" Pin0InfoVect0LinkObjId="SW-248339_0" Pin0InfoVect1LinkObjId="SW-119435_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c77820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-183 4345,-195 4377,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336ba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-241 4377,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22277@1" ObjectIDZND0="22276@0" Pin0InfoVect0LinkObjId="SW-119434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-241 4377,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f9960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-195 4377,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3c77820@0" ObjectIDND1="22277@x" ObjectIDZND0="37474@1" Pin0InfoVect0LinkObjId="SW-248339_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c77820_0" Pin1InfoVect1LinkObjId="SW-119435_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-195 4377,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f9bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-205 4377,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22277@0" ObjectIDZND0="g_3c77820@0" ObjectIDZND1="37474@x" Pin0InfoVect0LinkObjId="g_3c77820_0" Pin0InfoVect1LinkObjId="SW-248339_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-205 4377,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287a350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-59 4377,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="41643@0" ObjectIDZND0="g_26fd8c0@0" ObjectIDZND1="37474@x" Pin0InfoVect0LinkObjId="g_26fd8c0_0" Pin0InfoVect1LinkObjId="SW-248339_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-WD_JC.WD_JC_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-59 4377,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-89 4377,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="g_26fd8c0@0" ObjectIDND1="41643@x" ObjectIDZND0="37474@0" Pin0InfoVect0LinkObjId="SW-248339_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26fd8c0_0" Pin1InfoVect1LinkObjId="CB-WD_JC.WD_JC_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-89 4377,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a08040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-217 4408,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2402560@0" ObjectIDZND0="22279@0" Pin0InfoVect0LinkObjId="SW-119437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2402560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-217 4408,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a082a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-290 4408,-290 4408,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22278@x" ObjectIDND1="22276@x" ObjectIDZND0="22279@1" Pin0InfoVect0LinkObjId="SW-119437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119436_0" Pin1InfoVect1LinkObjId="SW-119434_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-290 4408,-290 4408,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a08500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-299 4377,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22278@0" ObjectIDZND0="22279@x" ObjectIDZND1="22276@x" Pin0InfoVect0LinkObjId="SW-119437_0" Pin0InfoVect1LinkObjId="SW-119434_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119436_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-299 4377,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a08760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-290 4377,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22279@x" ObjectIDND1="22278@x" ObjectIDZND0="22276@1" Pin0InfoVect0LinkObjId="SW-119434_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119437_0" Pin1InfoVect1LinkObjId="SW-119436_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-290 4377,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_322ed40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-335 4995,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22254@1" ObjectIDZND0="22302@0" Pin0InfoVect0LinkObjId="g_32c57e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-335 4995,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28efc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4963,-77 4963,-89 4995,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2b00680@0" ObjectIDZND0="33617@x" ObjectIDZND1="22303@x" Pin0InfoVect0LinkObjId="EC-WD_JC.441Ld_0" Pin0InfoVect1LinkObjId="SW-119658_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b00680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4963,-77 4963,-89 4995,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28efef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4963,-183 4963,-195 4995,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_322efa0@0" ObjectIDZND0="22303@x" ObjectIDZND1="22253@x" Pin0InfoVect0LinkObjId="SW-119658_0" Pin0InfoVect1LinkObjId="SW-119379_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_322efa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4963,-183 4963,-195 4995,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f0150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-241 4995,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22253@1" ObjectIDZND0="22252@0" Pin0InfoVect0LinkObjId="SW-119378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119379_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-241 4995,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28bc440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-195 4995,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_322efa0@0" ObjectIDND1="22253@x" ObjectIDZND0="22303@1" Pin0InfoVect0LinkObjId="SW-119658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_322efa0_0" Pin1InfoVect1LinkObjId="SW-119379_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-195 4995,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28bc6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-205 4995,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22253@0" ObjectIDZND0="g_322efa0@0" ObjectIDZND1="22303@x" Pin0InfoVect0LinkObjId="g_322efa0_0" Pin0InfoVect1LinkObjId="SW-119658_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-205 4995,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28bc900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-11 4995,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33617@0" ObjectIDZND0="g_2b00680@0" ObjectIDZND1="22303@x" Pin0InfoVect0LinkObjId="g_2b00680_0" Pin0InfoVect1LinkObjId="SW-119658_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.441Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-11 4995,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28bcb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-89 4995,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2b00680@0" ObjectIDND1="33617@x" ObjectIDZND0="22303@0" Pin0InfoVect0LinkObjId="SW-119658_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b00680_0" Pin1InfoVect1LinkObjId="EC-WD_JC.441Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-89 4995,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2408690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-217 5026,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3caae60@0" ObjectIDZND0="22255@0" Pin0InfoVect0LinkObjId="SW-119381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3caae60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-217 5026,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24088f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-290 5026,-290 5026,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22254@x" ObjectIDND1="22252@x" ObjectIDZND0="22255@1" Pin0InfoVect0LinkObjId="SW-119381_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119380_0" Pin1InfoVect1LinkObjId="SW-119378_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-290 5026,-290 5026,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb9ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-299 4995,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22254@0" ObjectIDZND0="22255@x" ObjectIDZND1="22252@x" Pin0InfoVect0LinkObjId="SW-119381_0" Pin0InfoVect1LinkObjId="SW-119378_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-299 4995,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cba130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-290 4995,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22255@x" ObjectIDND1="22254@x" ObjectIDZND0="22252@1" Pin0InfoVect0LinkObjId="SW-119378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119381_0" Pin1InfoVect1LinkObjId="SW-119380_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-290 4995,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29ccd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-348 4534,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22301@0" ObjectIDZND0="22292@1" Pin0InfoVect0LinkObjId="SW-119483_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32d9d80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-348 4534,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29ccf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-233 4494,-245 4534,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_34a89f0@0" ObjectIDZND0="22292@x" ObjectIDZND1="22293@x" ObjectIDZND2="g_3c53b10@0" Pin0InfoVect0LinkObjId="SW-119483_0" Pin0InfoVect1LinkObjId="SW-119484_0" Pin0InfoVect2LinkObjId="g_3c53b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34a89f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-233 4494,-245 4534,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29cd1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-281 4534,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="22292@0" ObjectIDZND0="g_34a89f0@0" ObjectIDZND1="22293@x" ObjectIDZND2="g_3c53b10@0" Pin0InfoVect0LinkObjId="g_34a89f0_0" Pin0InfoVect1LinkObjId="SW-119484_0" Pin0InfoVect2LinkObjId="g_3c53b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119483_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-281 4534,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29cd450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-245 4534,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="22292@x" ObjectIDND1="g_34a89f0@0" ObjectIDND2="22293@x" ObjectIDZND0="g_3c53b10@0" Pin0InfoVect0LinkObjId="g_3c53b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119483_0" Pin1InfoVect1LinkObjId="g_34a89f0_0" Pin1InfoVect2LinkObjId="SW-119484_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-245 4534,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2836410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-245 4585,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_263bea0@0" ObjectIDZND0="22293@1" Pin0InfoVect0LinkObjId="SW-119484_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_263bea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-245 4585,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2836650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4549,-245 4534,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="22293@0" ObjectIDZND0="22292@x" ObjectIDZND1="g_34a89f0@0" ObjectIDZND2="g_3c53b10@0" Pin0InfoVect0LinkObjId="SW-119483_0" Pin0InfoVect1LinkObjId="g_34a89f0_0" Pin0InfoVect2LinkObjId="g_3c53b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119484_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4549,-245 4534,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275a9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4654,-328 4654,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22258@1" ObjectIDZND0="22301@0" Pin0InfoVect0LinkObjId="g_32d9d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4654,-328 4654,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c57e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-328 4745,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22259@1" ObjectIDZND0="22302@0" Pin0InfoVect0LinkObjId="g_322ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-328 4745,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c5a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4712,-284 4745,-284 4745,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22257@0" ObjectIDZND0="22259@0" Pin0InfoVect0LinkObjId="SW-119387_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4712,-284 4745,-284 4745,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c5ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-284 4654,-284 4654,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22257@1" ObjectIDZND0="22258@0" Pin0InfoVect0LinkObjId="SW-119386_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-284 4654,-284 4654,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea8cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4833,-348 4833,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22302@0" ObjectIDZND0="22294@1" Pin0InfoVect0LinkObjId="SW-119485_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_322ed40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4833,-348 4833,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea8f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4793,-233 4793,-245 4833,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_32c5f00@0" ObjectIDZND0="22294@x" ObjectIDZND1="22295@x" ObjectIDZND2="g_3c469b0@0" Pin0InfoVect0LinkObjId="SW-119485_0" Pin0InfoVect1LinkObjId="SW-119486_0" Pin0InfoVect2LinkObjId="g_3c469b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c5f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4793,-233 4793,-245 4833,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea9190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4833,-281 4833,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="22294@0" ObjectIDZND0="g_32c5f00@0" ObjectIDZND1="22295@x" ObjectIDZND2="g_3c469b0@0" Pin0InfoVect0LinkObjId="g_32c5f00_0" Pin0InfoVect1LinkObjId="SW-119486_0" Pin0InfoVect2LinkObjId="g_3c469b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4833,-281 4833,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea93f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4833,-245 4833,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="22294@x" ObjectIDND1="g_32c5f00@0" ObjectIDND2="22295@x" ObjectIDZND0="g_3c469b0@0" Pin0InfoVect0LinkObjId="g_3c469b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119485_0" Pin1InfoVect1LinkObjId="g_32c5f00_0" Pin1InfoVect2LinkObjId="SW-119486_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4833,-245 4833,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-245 4884,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2290dd0@0" ObjectIDZND0="22295@1" Pin0InfoVect0LinkObjId="SW-119486_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2290dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-245 4884,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273e1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-245 4833,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="22295@0" ObjectIDZND0="22294@x" ObjectIDZND1="g_32c5f00@0" ObjectIDZND2="g_3c469b0@0" Pin0InfoVect0LinkObjId="SW-119485_0" Pin0InfoVect1LinkObjId="g_32c5f00_0" Pin0InfoVect2LinkObjId="g_3c469b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119486_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-245 4833,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e5980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-335 5122,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22250@1" ObjectIDZND0="22302@0" Pin0InfoVect0LinkObjId="g_322ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-335 5122,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2761420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5090,-77 5090,-89 5122,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_24099b0@0" ObjectIDZND0="33612@x" ObjectIDZND1="22212@x" Pin0InfoVect0LinkObjId="EC-WD_JC.442Ld_0" Pin0InfoVect1LinkObjId="SW-119320_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24099b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5090,-77 5090,-89 5122,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2761680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5090,-183 5090,-195 5122,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2408d60@0" ObjectIDZND0="22212@x" ObjectIDZND1="22249@x" Pin0InfoVect0LinkObjId="SW-119320_0" Pin0InfoVect1LinkObjId="SW-119373_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2408d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5090,-183 5090,-195 5122,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27618e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-241 5122,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22249@1" ObjectIDZND0="22248@0" Pin0InfoVect0LinkObjId="SW-119372_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119373_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-241 5122,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26932b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-195 5122,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2408d60@0" ObjectIDND1="22249@x" ObjectIDZND0="22212@1" Pin0InfoVect0LinkObjId="SW-119320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2408d60_0" Pin1InfoVect1LinkObjId="SW-119373_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-195 5122,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2693510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-205 5122,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22249@0" ObjectIDZND0="22212@x" ObjectIDZND1="g_2408d60@0" Pin0InfoVect0LinkObjId="SW-119320_0" Pin0InfoVect1LinkObjId="g_2408d60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-205 5122,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2693770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-11 5122,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33612@0" ObjectIDZND0="g_24099b0@0" ObjectIDZND1="22212@x" Pin0InfoVect0LinkObjId="g_24099b0_0" Pin0InfoVect1LinkObjId="SW-119320_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.442Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-11 5122,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b65670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-89 5122,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33612@x" ObjectIDND1="g_24099b0@0" ObjectIDZND0="22212@0" Pin0InfoVect0LinkObjId="SW-119320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JC.442Ld_0" Pin1InfoVect1LinkObjId="g_24099b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-89 5122,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_283f260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5153,-217 5153,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_283e9f0@0" ObjectIDZND0="22251@0" Pin0InfoVect0LinkObjId="SW-119375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_283e9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5153,-217 5153,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_283f4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-290 5153,-290 5153,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22250@x" ObjectIDND1="22248@x" ObjectIDZND0="22251@1" Pin0InfoVect0LinkObjId="SW-119375_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119374_0" Pin1InfoVect1LinkObjId="SW-119372_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-290 5153,-290 5153,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_283f720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-299 5122,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22250@0" ObjectIDZND0="22251@x" ObjectIDZND1="22248@x" Pin0InfoVect0LinkObjId="SW-119375_0" Pin0InfoVect1LinkObjId="SW-119372_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-299 5122,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_283f980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-290 5122,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22250@x" ObjectIDND1="22251@x" ObjectIDZND0="22248@1" Pin0InfoVect0LinkObjId="SW-119372_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119374_0" Pin1InfoVect1LinkObjId="SW-119375_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-290 5122,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2620a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-335 5254,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22245@1" ObjectIDZND0="22302@0" Pin0InfoVect0LinkObjId="g_322ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119367_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-335 5254,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_226da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5222,-77 5222,-89 5254,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_32c2cd0@0" ObjectIDZND0="33941@x" ObjectIDZND1="22247@x" Pin0InfoVect0LinkObjId="EC-WD_JC.JC_443Ld_0" Pin0InfoVect1LinkObjId="SW-119369_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c2cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5222,-77 5222,-89 5254,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_226dca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5222,-183 5222,-195 5254,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_32c21c0@0" ObjectIDZND0="22247@x" ObjectIDZND1="22244@x" Pin0InfoVect0LinkObjId="SW-119369_0" Pin0InfoVect1LinkObjId="SW-119366_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c21c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5222,-183 5222,-195 5254,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_226df00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-241 5254,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22244@1" ObjectIDZND0="22243@0" Pin0InfoVect0LinkObjId="SW-119365_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119366_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-241 5254,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-195 5254,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_32c21c0@0" ObjectIDND1="22244@x" ObjectIDZND0="22247@1" Pin0InfoVect0LinkObjId="SW-119369_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32c21c0_0" Pin1InfoVect1LinkObjId="SW-119366_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-195 5254,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321f420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-205 5254,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22244@0" ObjectIDZND0="22247@x" ObjectIDZND1="g_32c21c0@0" Pin0InfoVect0LinkObjId="SW-119369_0" Pin0InfoVect1LinkObjId="g_32c21c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119366_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-205 5254,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321f680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-11 5254,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33941@0" ObjectIDZND0="g_32c2cd0@0" ObjectIDZND1="22247@x" Pin0InfoVect0LinkObjId="g_32c2cd0_0" Pin0InfoVect1LinkObjId="SW-119369_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.JC_443Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-11 5254,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321f8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-89 5254,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33941@x" ObjectIDND1="g_32c2cd0@0" ObjectIDZND0="22247@0" Pin0InfoVect0LinkObjId="SW-119369_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JC.JC_443Ld_0" Pin1InfoVect1LinkObjId="g_32c2cd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-89 5254,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f244c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5285,-217 5285,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f23ad0@0" ObjectIDZND0="22246@0" Pin0InfoVect0LinkObjId="SW-119368_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f23ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5285,-217 5285,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f24720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-290 5285,-290 5285,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22245@x" ObjectIDND1="22243@x" ObjectIDZND0="22246@1" Pin0InfoVect0LinkObjId="SW-119368_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119367_0" Pin1InfoVect1LinkObjId="SW-119365_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-290 5285,-290 5285,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24fe350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-335 5399,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22240@1" ObjectIDZND0="22302@0" Pin0InfoVect0LinkObjId="g_322ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5399,-335 5399,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5367,-77 5367,-89 5399,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_24ff200@0" ObjectIDZND0="33611@x" ObjectIDZND1="22242@x" Pin0InfoVect0LinkObjId="EC-WD_JC.444Ld_0" Pin0InfoVect1LinkObjId="SW-119362_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24ff200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5367,-77 5367,-89 5399,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321e440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5367,-183 5367,-195 5399,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_24fe580@0" ObjectIDZND0="22242@x" ObjectIDZND1="22239@x" Pin0InfoVect0LinkObjId="SW-119362_0" Pin0InfoVect1LinkObjId="SW-119359_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24fe580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5367,-183 5367,-195 5399,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321e6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-241 5399,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22239@1" ObjectIDZND0="22238@0" Pin0InfoVect0LinkObjId="SW-119358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119359_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5399,-241 5399,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c48f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-195 5399,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_24fe580@0" ObjectIDND1="22239@x" ObjectIDZND0="22242@1" Pin0InfoVect0LinkObjId="SW-119362_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24fe580_0" Pin1InfoVect1LinkObjId="SW-119359_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5399,-195 5399,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c49180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-205 5399,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22239@0" ObjectIDZND0="g_24fe580@0" ObjectIDZND1="22242@x" Pin0InfoVect0LinkObjId="g_24fe580_0" Pin0InfoVect1LinkObjId="SW-119362_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5399,-205 5399,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c493e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-11 5399,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33611@0" ObjectIDZND0="g_24ff200@0" ObjectIDZND1="22242@x" Pin0InfoVect0LinkObjId="g_24ff200_0" Pin0InfoVect1LinkObjId="SW-119362_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.444Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5399,-11 5399,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c49640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-89 5399,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_24ff200@0" ObjectIDND1="33611@x" ObjectIDZND0="22242@0" Pin0InfoVect0LinkObjId="SW-119362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24ff200_0" Pin1InfoVect1LinkObjId="EC-WD_JC.444Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5399,-89 5399,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e9b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5430,-217 5430,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22e9150@0" ObjectIDZND0="22241@0" Pin0InfoVect0LinkObjId="SW-119361_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e9150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5430,-217 5430,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e9d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-290 5430,-290 5430,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22240@x" ObjectIDND1="22238@x" ObjectIDZND0="22241@1" Pin0InfoVect0LinkObjId="SW-119361_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119360_0" Pin1InfoVect1LinkObjId="SW-119358_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5399,-290 5430,-290 5430,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e9fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-299 5399,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22240@0" ObjectIDZND0="22241@x" ObjectIDZND1="22238@x" Pin0InfoVect0LinkObjId="SW-119361_0" Pin0InfoVect1LinkObjId="SW-119358_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5399,-299 5399,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b3a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5399,-290 5399,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22241@x" ObjectIDND1="22240@x" ObjectIDZND0="22238@1" Pin0InfoVect0LinkObjId="SW-119358_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119361_0" Pin1InfoVect1LinkObjId="SW-119360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5399,-290 5399,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c38590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-335 5531,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22235@1" ObjectIDZND0="22302@0" Pin0InfoVect0LinkObjId="g_322ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119353_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-335 5531,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2901a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5499,-77 5499,-89 5531,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3c39430@0" ObjectIDZND0="33610@x" ObjectIDZND1="22237@x" Pin0InfoVect0LinkObjId="EC-WD_JC.445Ld_0" Pin0InfoVect1LinkObjId="SW-119355_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c39430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5499,-77 5499,-89 5531,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2901c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5499,-183 5499,-195 5531,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3c387e0@0" ObjectIDZND0="22237@x" ObjectIDZND1="22234@x" Pin0InfoVect0LinkObjId="SW-119355_0" Pin0InfoVect1LinkObjId="SW-119352_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c387e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5499,-183 5499,-195 5531,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2901ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-241 5531,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22234@1" ObjectIDZND0="22233@0" Pin0InfoVect0LinkObjId="SW-119351_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119352_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-241 5531,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cd1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-195 5531,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3c387e0@0" ObjectIDND1="22234@x" ObjectIDZND0="22237@1" Pin0InfoVect0LinkObjId="SW-119355_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c387e0_0" Pin1InfoVect1LinkObjId="SW-119352_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-195 5531,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cd400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-205 5531,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22234@0" ObjectIDZND0="g_3c387e0@0" ObjectIDZND1="22237@x" Pin0InfoVect0LinkObjId="g_3c387e0_0" Pin0InfoVect1LinkObjId="SW-119355_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-205 5531,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cd660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-11 5531,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33610@0" ObjectIDZND0="g_3c39430@0" ObjectIDZND1="22237@x" Pin0InfoVect0LinkObjId="g_3c39430_0" Pin0InfoVect1LinkObjId="SW-119355_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JC.445Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-11 5531,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cd8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-89 5531,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_3c39430@0" ObjectIDND1="33610@x" ObjectIDZND0="22237@0" Pin0InfoVect0LinkObjId="SW-119355_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c39430_0" Pin1InfoVect1LinkObjId="EC-WD_JC.445Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-89 5531,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29462e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5562,-217 5562,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_275f3c0@0" ObjectIDZND0="22236@0" Pin0InfoVect0LinkObjId="SW-119354_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_275f3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5562,-217 5562,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2946540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-290 5562,-290 5562,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22235@x" ObjectIDND1="22233@x" ObjectIDZND0="22236@1" Pin0InfoVect0LinkObjId="SW-119354_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119353_0" Pin1InfoVect1LinkObjId="SW-119351_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-290 5562,-290 5562,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29467a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-299 5531,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22235@0" ObjectIDZND0="22236@x" ObjectIDZND1="22233@x" Pin0InfoVect0LinkObjId="SW-119354_0" Pin0InfoVect1LinkObjId="SW-119351_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119353_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-299 5531,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2946a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-290 5531,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22236@x" ObjectIDND1="22235@x" ObjectIDZND0="22233@1" Pin0InfoVect0LinkObjId="SW-119351_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119354_0" Pin1InfoVect1LinkObjId="SW-119353_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-290 5531,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343d710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-779 4994,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22286@1" ObjectIDZND0="22300@0" Pin0InfoVect0LinkObjId="g_227d590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119462_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-779 4994,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343d970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-727 4994,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22285@1" ObjectIDZND0="22286@0" Pin0InfoVect0LinkObjId="SW-119462_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119461_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-727 4994,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc0090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-461 4994,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22289@0" ObjectIDZND0="22287@1" Pin0InfoVect0LinkObjId="SW-119465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-461 4994,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2401c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-422 4994,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22287@0" ObjectIDZND0="22288@1" Pin0InfoVect0LinkObjId="SW-119467_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119465_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-422 4994,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2401ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-374 4994,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22288@0" ObjectIDZND0="22302@0" Pin0InfoVect0LinkObjId="g_322ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119467_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-374 4994,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242fcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-700 4994,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22285@0" ObjectIDZND0="22298@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119461_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-700 4994,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242ff00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-573 4994,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="22298@1" ObjectIDZND0="22289@1" Pin0InfoVect0LinkObjId="SW-119468_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_242fcc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-573 4994,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1baac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-299 5254,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22245@0" ObjectIDZND0="22246@x" ObjectIDZND1="22243@x" Pin0InfoVect0LinkObjId="SW-119368_0" Pin0InfoVect1LinkObjId="SW-119365_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-299 5254,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1baae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-290 5254,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22246@x" ObjectIDND1="22245@x" ObjectIDZND0="22243@1" Pin0InfoVect0LinkObjId="SW-119365_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119368_0" Pin1InfoVect1LinkObjId="SW-119367_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-290 5254,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bea4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-1068 4799,-1087 4798,-1088 4697,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_29a6f80@0" ObjectIDZND1="22266@x" ObjectIDZND2="22265@x" Pin0InfoVect0LinkObjId="g_29a6f80_0" Pin0InfoVect1LinkObjId="SW-119403_0" Pin0InfoVect2LinkObjId="SW-119402_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-1068 4799,-1087 4798,-1088 4697,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2292280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4800,-1030 4800,-1011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1c1d7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4800,-1030 4800,-1011 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22301" cx="3570" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22301" cx="3707" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22301" cx="3845" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22301" cx="3981" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22301" cx="4116" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22301" cx="4225" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22301" cx="4377" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22301" cx="4534" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22301" cx="4654" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22302" cx="4995" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22302" cx="4745" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22302" cx="4833" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22302" cx="5122" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22302" cx="5254" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22302" cx="5399" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22302" cx="5531" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22302" cx="4993" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22300" cx="4116" cy="-832" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22300" cx="4401" cy="-832" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22300" cx="4697" cy="-832" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22300" cx="4993" cy="-832" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22300" cx="4116" cy="-832" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22300" cx="4556" cy="-832" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22300" cx="4994" cy="-832" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-118602" type="2">
    <use transform="matrix(1.851332 -0.000000 0.000000 -1.500000 3496.730014 -1063.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22115" ObjectName="DYN-WD_JC"/>
     <cge:Meas_Ref ObjectId="118602"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_337f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,20)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_337f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,45)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_337f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,70)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_337f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,95)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_337f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,120)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_337f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,145)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_337f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,170)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_337f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,195)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_337f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,220)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,20)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,45)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,70)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,95)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,120)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,145)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,170)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,195)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,220)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,245)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,270)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,295)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,320)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,345)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,370)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,395)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27a6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -589.000000) translate(0,420)">联系方式：8711550</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_3229990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5119.000000 -877.000000) translate(0,24)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26e4e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3683.000000 -386.000000) translate(0,24)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="50" graphid="g_275d5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3285.000000 -1185.500000) translate(0,40)">近城变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_279ff10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4156.142857 -1188.800000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_279ff10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4156.142857 -1188.800000) translate(0,45)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_279ff10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4156.142857 -1188.800000) translate(0,70)">近</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_279ff10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4156.142857 -1188.800000) translate(0,95)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_279ff10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4156.142857 -1188.800000) translate(0,120)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1dc7850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -674.000000) translate(0,20)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1dc7850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -674.000000) translate(0,45)">SFZ9-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1dc7850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -674.000000) translate(0,70)">Y/△-11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1dc7850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -674.000000) translate(0,95)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1dc7850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -674.000000) translate(0,120)">Ud=7.54%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1c0b0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4132.000000 -987.000000) translate(0,20)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3d03870" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4443.142857 -1188.800000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3d03870" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4443.142857 -1188.800000) translate(0,45)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3d03870" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4443.142857 -1188.800000) translate(0,70)">禄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3d03870" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4443.142857 -1188.800000) translate(0,95)">武</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3d03870" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4443.142857 -1188.800000) translate(0,120)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_29ad320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4417.000000 -987.000000) translate(0,20)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2b5ec80" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4731.142857 -1202.800000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2b5ec80" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4731.142857 -1202.800000) translate(0,45)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2b5ec80" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4731.142857 -1202.800000) translate(0,70)">近</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2b5ec80" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4731.142857 -1202.800000) translate(0,95)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2b5ec80" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4731.142857 -1202.800000) translate(0,120)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_29b64b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4709.000000 -988.000000) translate(0,20)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1efe840" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5027.142857 -1208.800000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1efe840" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5027.142857 -1208.800000) translate(0,45)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1efe840" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5027.142857 -1208.800000) translate(0,70)">近</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1efe840" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5027.142857 -1208.800000) translate(0,95)">邑</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1efe840" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5027.142857 -1208.800000) translate(0,120)">插</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1efe840" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5027.142857 -1208.800000) translate(0,145)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1efcdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5009.000000 -987.000000) translate(0,20)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1c68970" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5266.142857 -47.800000) translate(0,20)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1c68970" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5266.142857 -47.800000) translate(0,45)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1c68970" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5266.142857 -47.800000) translate(0,70)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2823960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3513.000000 -277.000000) translate(0,20)">431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2823e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -331.000000) translate(0,20)">4311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_324b7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3498.000000 -229.000000) translate(0,20)">4312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_324ba20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -126.000000) translate(0,20)">4316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_324bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3604.000000 -251.000000) translate(0,16)">43117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_29e3f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3653.000000 -276.000000) translate(0,20)">432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_29e42f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -332.000000) translate(0,20)">4321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_29e4510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3651.000000 -234.000000) translate(0,20)">4322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1cc49e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3716.000000 -125.000000) translate(0,20)">4326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1cc4c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3745.000000 -251.000000) translate(0,16)">43217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1cc4e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.000000 -274.000000) translate(0,20)">433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1cc5040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3777.000000 -335.000000) translate(0,20)">4331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27513b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3778.000000 -226.000000) translate(0,20)">4332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_27517e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 -121.000000) translate(0,20)">4336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2751a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -251.000000) translate(0,16)">43317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_324d480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3926.000000 -277.000000) translate(0,20)">434</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_324d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3913.000000 -333.000000) translate(0,20)">4341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_324d8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3915.000000 -226.000000) translate(0,20)">4342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_324dae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -125.000000) translate(0,20)">4346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_271d180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4019.000000 -251.000000) translate(0,16)">43417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_271d3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -276.000000) translate(0,20)">435</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_271d600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -125.000000) translate(0,20)">4356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_271d840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4048.000000 -225.000000) translate(0,20)">4352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_323b270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4049.000000 -329.000000) translate(0,20)">4351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_323b4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -259.000000) translate(0,16)">43517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_323b8c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4177.142857 29.200000) translate(0,20)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2430160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.000000 -657.000000) translate(0,20)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2430160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.000000 -657.000000) translate(0,45)">SFZ9-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2430160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.000000 -657.000000) translate(0,70)">Y/△-11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2430160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.000000 -657.000000) translate(0,95)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2430160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.000000 -657.000000) translate(0,120)">Ud=7.57%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2621530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -919.000000) translate(0,20)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26217f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4133.000000 -956.000000) translate(0,20)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26219f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4032.000000 -1020.000000) translate(0,20)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2621c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -875.000000) translate(0,20)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2621e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 -919.000000) translate(0,20)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26220b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4417.000000 -956.000000) translate(0,20)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26222f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1021.000000) translate(0,20)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2622530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4417.000000 -876.000000) translate(0,20)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2622770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -915.000000) translate(0,20)">333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26229b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -951.000000) translate(0,20)">3336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2622bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4615.000000 -1017.000000) translate(0,20)">33367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ceaa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -874.000000) translate(0,20)">3331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ceaca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5018.000000 -916.000000) translate(0,20)">334</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ceaee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -955.000000) translate(0,20)">3346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ceb120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4912.000000 -1020.000000) translate(0,20)">33467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ceb360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5010.000000 -874.000000) translate(0,20)">3341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_28ba5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5217.000000 -390.000000) translate(0,24)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28baa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -722.000000) translate(0,20)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28bace0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4130.000000 -772.000000) translate(0,20)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28baf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -443.000000) translate(0,20)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee6c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -491.000000) translate(0,20)">4016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee6e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4133.000000 -399.000000) translate(0,20)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee70d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -800.000000) translate(0,20)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee7310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -764.000000) translate(0,20)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee7550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5022.000000 -724.000000) translate(0,20)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee7790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5018.000000 -774.000000) translate(0,20)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee79d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5020.000000 -443.000000) translate(0,20)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee7c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5019.000000 -490.000000) translate(0,20)">4026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee7e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5015.000000 -397.000000) translate(0,20)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee8090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -274.000000) translate(0,20)">436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee82d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.000000 -234.000000) translate(0,20)">4362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee8510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4308.000000 -331.000000) translate(0,20)">4361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ee8750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4415.000000 -251.000000) translate(0,16)">43617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ee8990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4464.000000 -323.000000) translate(0,20)">4901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33367d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -276.000000) translate(0,16)">49017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3336a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4845.000000 -314.000000) translate(0,20)">4902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3336c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4846.000000 -279.000000) translate(0,16)">49027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3336e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -322.000000) translate(0,20)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33370d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4755.000000 -321.000000) translate(0,20)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3337310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4591.000000 -336.000000) translate(0,20)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3337550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4938.000000 -277.000000) translate(0,20)">441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3337790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5010.000000 -126.000000) translate(0,20)">4416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33379d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4925.000000 -234.000000) translate(0,20)">4412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3337c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4929.000000 -331.000000) translate(0,20)">4411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3337e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5033.000000 -251.000000) translate(0,16)">44117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3338090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 -276.000000) translate(0,20)">442</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33382d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5136.000000 -125.000000) translate(0,20)">4426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3338510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5055.000000 -223.000000) translate(0,20)">4422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1ba9e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5054.000000 -327.000000) translate(0,20)">4421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1baa020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -251.000000) translate(0,16)">44217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1baa260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5196.000000 -279.000000) translate(0,20)">443</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1bab000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5269.000000 -125.000000) translate(0,20)">4436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1bab430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5186.000000 -226.000000) translate(0,20)">4432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1bab670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5186.000000 -334.000000) translate(0,20)">4431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1bab8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5292.000000 -251.000000) translate(0,16)">44317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1babaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5345.000000 -277.000000) translate(0,20)">444</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_22a24f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5415.000000 -123.000000) translate(0,20)">4446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_22a2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5334.000000 -228.000000) translate(0,20)">4442</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_22a2940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5329.000000 -331.000000) translate(0,20)">4441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22a2b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5437.000000 -251.000000) translate(0,16)">44417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_22a2dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5473.000000 -281.000000) translate(0,20)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_22a3000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5549.000000 -122.000000) translate(0,20)">4456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_22a3240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5467.000000 -226.000000) translate(0,20)">4452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_22a3480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5461.000000 -327.000000) translate(0,20)">4451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22a36c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5573.000000 -260.000000) translate(0,16)">44517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" graphid="g_272f690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4189.000000 -672.000000) translate(0,26)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" graphid="g_272ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5062.000000 -671.000000) translate(0,26)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22924e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4778.000000 -924.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2292af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4495.000000 -651.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2293100" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4229.142857 -328.800000) translate(0,20)">4001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22936e0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4257.142857 -257.800000) translate(0,15)">40017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_2293920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -752.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_274cf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -1156.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_274d640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -1191.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="24" graphid="g_274db10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4441.000000 -577.000000) translate(0,20)">1、2号主变档位联调</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_274dec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -137.000000) translate(0,20)">4366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26ac270" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4456.142857 -168.800000) translate(0,20)">Ⅰ母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26ac760" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4755.142857 -164.800000) translate(0,20)">Ⅱ母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26acb80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3589.000000 -73.000000) translate(0,20)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26acb80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3589.000000 -73.000000) translate(0,45)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26acb80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3589.000000 -73.000000) translate(0,70)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_26acb80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3589.000000 -73.000000) translate(0,95)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c220" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -86.000000) translate(0,20)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c220" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -86.000000) translate(0,45)">矣</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c220" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -86.000000) translate(0,70)">波</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c220" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -86.000000) translate(0,95)">矿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c220" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -86.000000) translate(0,120)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c220" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -86.000000) translate(0,145)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c4a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3866.000000 -89.000000) translate(0,20)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c4a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3866.000000 -89.000000) translate(0,45)">土</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c4a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3866.000000 -89.000000) translate(0,70)">田</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c4a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3866.000000 -89.000000) translate(0,95)">冶</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c4a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3866.000000 -89.000000) translate(0,120)">炼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c4a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3866.000000 -89.000000) translate(0,145)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322c4a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3866.000000 -89.000000) translate(0,170)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322d170" transform="matrix(1.000000 0.000000 0.000000 1.000000 4003.000000 -80.000000) translate(0,20)">赵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322d170" transform="matrix(1.000000 0.000000 0.000000 1.000000 4003.000000 -80.000000) translate(0,45)">家</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322d170" transform="matrix(1.000000 0.000000 0.000000 1.000000 4003.000000 -80.000000) translate(0,70)">庄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322d170" transform="matrix(1.000000 0.000000 0.000000 1.000000 4003.000000 -80.000000) translate(0,95)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322dcc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4136.000000 -83.000000) translate(0,20)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322dcc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4136.000000 -83.000000) translate(0,45)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322dcc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4136.000000 -83.000000) translate(0,70)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322dcc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4136.000000 -83.000000) translate(0,95)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_322dcc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4136.000000 -83.000000) translate(0,120)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28aecd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5013.000000 -77.000000) translate(0,20)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28aecd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5013.000000 -77.000000) translate(0,45)">北</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28aecd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5013.000000 -77.000000) translate(0,70)">片</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28aecd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5013.000000 -77.000000) translate(0,95)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28aecd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5013.000000 -77.000000) translate(0,120)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28af550" transform="matrix(1.000000 0.000000 0.000000 1.000000 5142.000000 -51.000000) translate(0,20)">吆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28af550" transform="matrix(1.000000 0.000000 0.000000 1.000000 5142.000000 -51.000000) translate(0,45)">鹰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28af550" transform="matrix(1.000000 0.000000 0.000000 1.000000 5142.000000 -51.000000) translate(0,70)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28b0510" transform="matrix(1.000000 0.000000 0.000000 1.000000 5418.000000 -83.000000) translate(0,20)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28b0510" transform="matrix(1.000000 0.000000 0.000000 1.000000 5418.000000 -83.000000) translate(0,45)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28b0510" transform="matrix(1.000000 0.000000 0.000000 1.000000 5418.000000 -83.000000) translate(0,70)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28b0510" transform="matrix(1.000000 0.000000 0.000000 1.000000 5418.000000 -83.000000) translate(0,95)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28b0510" transform="matrix(1.000000 0.000000 0.000000 1.000000 5418.000000 -83.000000) translate(0,120)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28b0890" transform="matrix(1.000000 0.000000 0.000000 1.000000 5553.000000 -49.000000) translate(0,20)">狮</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28b0890" transform="matrix(1.000000 0.000000 0.000000 1.000000 5553.000000 -49.000000) translate(0,45)">高</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_28b0890" transform="matrix(1.000000 0.000000 0.000000 1.000000 5553.000000 -49.000000) translate(0,70)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2294810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3110.000000 -128.500000) translate(0,20)">武定巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2295860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 -148.500000) translate(0,20)">18787878990</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2295860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 -148.500000) translate(0,45)">18787842893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2295860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 -148.500000) translate(0,70)">13987880311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" graphid="g_2295bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 78.000000) translate(0,19)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2246320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3521.000000 -1171.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_26117c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3234.000000 -683.000000) translate(0,20)">隔刀远控</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4217,8 4225,20 4233,8 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-119426">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -846.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22275" ObjectName="SW-WD_JC.WD_JC_3311SW"/>
     <cge:Meas_Ref ObjectId="119426"/>
    <cge:TPSR_Ref TObjectID="22275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119424">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -929.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22273" ObjectName="SW-WD_JC.WD_JC_3316SW"/>
     <cge:Meas_Ref ObjectId="119424"/>
    <cge:TPSR_Ref TObjectID="22273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119425">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4060.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22274" ObjectName="SW-WD_JC.WD_JC_33167SW"/>
     <cge:Meas_Ref ObjectId="119425"/>
    <cge:TPSR_Ref TObjectID="22274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119415">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 -846.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22271" ObjectName="SW-WD_JC.WD_JC_3321SW"/>
     <cge:Meas_Ref ObjectId="119415"/>
    <cge:TPSR_Ref TObjectID="22271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119413">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 -929.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22269" ObjectName="SW-WD_JC.WD_JC_3326SW"/>
     <cge:Meas_Ref ObjectId="119413"/>
    <cge:TPSR_Ref TObjectID="22269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119414">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4345.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22270" ObjectName="SW-WD_JC.WD_JC_33267SW"/>
     <cge:Meas_Ref ObjectId="119414"/>
    <cge:TPSR_Ref TObjectID="22270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -846.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22267" ObjectName="SW-WD_JC.WD_JC_3331SW"/>
     <cge:Meas_Ref ObjectId="119404"/>
    <cge:TPSR_Ref TObjectID="22267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119402">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -929.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22265" ObjectName="SW-WD_JC.WD_JC_3336SW"/>
     <cge:Meas_Ref ObjectId="119402"/>
    <cge:TPSR_Ref TObjectID="22265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22266" ObjectName="SW-WD_JC.WD_JC_33367SW"/>
     <cge:Meas_Ref ObjectId="119403"/>
    <cge:TPSR_Ref TObjectID="22266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119391">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.000000 -929.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22261" ObjectName="SW-WD_JC.WD_JC_3346SW"/>
     <cge:Meas_Ref ObjectId="119391"/>
    <cge:TPSR_Ref TObjectID="22261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119392">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4937.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22262" ObjectName="SW-WD_JC.WD_JC_33467SW"/>
     <cge:Meas_Ref ObjectId="119392"/>
    <cge:TPSR_Ref TObjectID="22262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119393">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.000000 -846.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22263" ObjectName="SW-WD_JC.WD_JC_3341SW"/>
     <cge:Meas_Ref ObjectId="119393"/>
    <cge:TPSR_Ref TObjectID="22263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119442">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -737.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22281" ObjectName="SW-WD_JC.WD_JC_3011SW"/>
     <cge:Meas_Ref ObjectId="119442"/>
    <cge:TPSR_Ref TObjectID="22281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22284" ObjectName="SW-WD_JC.WD_JC_4016SW"/>
     <cge:Meas_Ref ObjectId="119448"/>
    <cge:TPSR_Ref TObjectID="22284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119447">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22283" ObjectName="SW-WD_JC.WD_JC_4011SW"/>
     <cge:Meas_Ref ObjectId="119447"/>
    <cge:TPSR_Ref TObjectID="22283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119481">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -760.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22290" ObjectName="SW-WD_JC.WD_JC_3901SW"/>
     <cge:Meas_Ref ObjectId="119481"/>
    <cge:TPSR_Ref TObjectID="22290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119482">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.000000 -724.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22291" ObjectName="SW-WD_JC.WD_JC_39017SW"/>
     <cge:Meas_Ref ObjectId="119482"/>
    <cge:TPSR_Ref TObjectID="22291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22229" ObjectName="SW-WD_JC.WD_JC_4312SW"/>
     <cge:Meas_Ref ObjectId="119345"/>
    <cge:TPSR_Ref TObjectID="22229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119348">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22232" ObjectName="SW-WD_JC.WD_JC_4316SW"/>
     <cge:Meas_Ref ObjectId="119348"/>
    <cge:TPSR_Ref TObjectID="22232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119346">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22230" ObjectName="SW-WD_JC.WD_JC_4311SW"/>
     <cge:Meas_Ref ObjectId="119346"/>
    <cge:TPSR_Ref TObjectID="22230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119347">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22231" ObjectName="SW-WD_JC.WD_JC_43117SW"/>
     <cge:Meas_Ref ObjectId="119347"/>
    <cge:TPSR_Ref TObjectID="22231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119338">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22224" ObjectName="SW-WD_JC.WD_JC_4322SW"/>
     <cge:Meas_Ref ObjectId="119338"/>
    <cge:TPSR_Ref TObjectID="22224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119341">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22227" ObjectName="SW-WD_JC.WD_JC_4326SW"/>
     <cge:Meas_Ref ObjectId="119341"/>
    <cge:TPSR_Ref TObjectID="22227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119339">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22225" ObjectName="SW-WD_JC.WD_JC_4321SW"/>
     <cge:Meas_Ref ObjectId="119339"/>
    <cge:TPSR_Ref TObjectID="22225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22226" ObjectName="SW-WD_JC.WD_JC_43217SW"/>
     <cge:Meas_Ref ObjectId="119340"/>
    <cge:TPSR_Ref TObjectID="22226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22219" ObjectName="SW-WD_JC.WD_JC_4332SW"/>
     <cge:Meas_Ref ObjectId="119331"/>
    <cge:TPSR_Ref TObjectID="22219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119334">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22222" ObjectName="SW-WD_JC.WD_JC_4336SW"/>
     <cge:Meas_Ref ObjectId="119334"/>
    <cge:TPSR_Ref TObjectID="22222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119332">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22220" ObjectName="SW-WD_JC.WD_JC_4331SW"/>
     <cge:Meas_Ref ObjectId="119332"/>
    <cge:TPSR_Ref TObjectID="22220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119333">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22221" ObjectName="SW-WD_JC.WD_JC_43317SW"/>
     <cge:Meas_Ref ObjectId="119333"/>
    <cge:TPSR_Ref TObjectID="22221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22214" ObjectName="SW-WD_JC.WD_JC_4342SW"/>
     <cge:Meas_Ref ObjectId="119324"/>
    <cge:TPSR_Ref TObjectID="22214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119327">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22217" ObjectName="SW-WD_JC.WD_JC_4346SW"/>
     <cge:Meas_Ref ObjectId="119327"/>
    <cge:TPSR_Ref TObjectID="22217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22215" ObjectName="SW-WD_JC.WD_JC_4341SW"/>
     <cge:Meas_Ref ObjectId="119325"/>
    <cge:TPSR_Ref TObjectID="22215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119326">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22216" ObjectName="SW-WD_JC.WD_JC_43417SW"/>
     <cge:Meas_Ref ObjectId="119326"/>
    <cge:TPSR_Ref TObjectID="22216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119317">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22209" ObjectName="SW-WD_JC.WD_JC_4352SW"/>
     <cge:Meas_Ref ObjectId="119317"/>
    <cge:TPSR_Ref TObjectID="22209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119382">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22256" ObjectName="SW-WD_JC.WD_JC_4356SW"/>
     <cge:Meas_Ref ObjectId="119382"/>
    <cge:TPSR_Ref TObjectID="22256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22210" ObjectName="SW-WD_JC.WD_JC_4351SW"/>
     <cge:Meas_Ref ObjectId="119318"/>
    <cge:TPSR_Ref TObjectID="22210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119319">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22211" ObjectName="SW-WD_JC.WD_JC_43517SW"/>
     <cge:Meas_Ref ObjectId="119319"/>
    <cge:TPSR_Ref TObjectID="22211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22277" ObjectName="SW-WD_JC.WD_JC_4362SW"/>
     <cge:Meas_Ref ObjectId="119435"/>
    <cge:TPSR_Ref TObjectID="22277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-248339">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37474" ObjectName="SW-WD_JC.WD_JC_4366SW"/>
     <cge:Meas_Ref ObjectId="248339"/>
    <cge:TPSR_Ref TObjectID="37474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119436">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22278" ObjectName="SW-WD_JC.WD_JC_4361SW"/>
     <cge:Meas_Ref ObjectId="119436"/>
    <cge:TPSR_Ref TObjectID="22278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119437">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22279" ObjectName="SW-WD_JC.WD_JC_43617SW"/>
     <cge:Meas_Ref ObjectId="119437"/>
    <cge:TPSR_Ref TObjectID="22279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22253" ObjectName="SW-WD_JC.WD_JC_4412SW"/>
     <cge:Meas_Ref ObjectId="119379"/>
    <cge:TPSR_Ref TObjectID="22253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22303" ObjectName="SW-WD_JC.WD_JC_4416SW"/>
     <cge:Meas_Ref ObjectId="119658"/>
    <cge:TPSR_Ref TObjectID="22303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119380">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22254" ObjectName="SW-WD_JC.WD_JC_4411SW"/>
     <cge:Meas_Ref ObjectId="119380"/>
    <cge:TPSR_Ref TObjectID="22254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119381">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22255" ObjectName="SW-WD_JC.WD_JC_44117SW"/>
     <cge:Meas_Ref ObjectId="119381"/>
    <cge:TPSR_Ref TObjectID="22255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119483">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22292" ObjectName="SW-WD_JC.WD_JC_4901SW"/>
     <cge:Meas_Ref ObjectId="119483"/>
    <cge:TPSR_Ref TObjectID="22292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119484">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 -240.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22293" ObjectName="SW-WD_JC.WD_JC_49017SW"/>
     <cge:Meas_Ref ObjectId="119484"/>
    <cge:TPSR_Ref TObjectID="22293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119386">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4645.000000 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22258" ObjectName="SW-WD_JC.WD_JC_4121SW"/>
     <cge:Meas_Ref ObjectId="119386"/>
    <cge:TPSR_Ref TObjectID="22258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22259" ObjectName="SW-WD_JC.WD_JC_4122SW"/>
     <cge:Meas_Ref ObjectId="119387"/>
    <cge:TPSR_Ref TObjectID="22259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4824.000000 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22294" ObjectName="SW-WD_JC.WD_JC_4902SW"/>
     <cge:Meas_Ref ObjectId="119485"/>
    <cge:TPSR_Ref TObjectID="22294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119486">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -240.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22295" ObjectName="SW-WD_JC.WD_JC_49027SW"/>
     <cge:Meas_Ref ObjectId="119486"/>
    <cge:TPSR_Ref TObjectID="22295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119373">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22249" ObjectName="SW-WD_JC.WD_JC_4422SW"/>
     <cge:Meas_Ref ObjectId="119373"/>
    <cge:TPSR_Ref TObjectID="22249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22212" ObjectName="SW-WD_JC.WD_JC_4426SW"/>
     <cge:Meas_Ref ObjectId="119320"/>
    <cge:TPSR_Ref TObjectID="22212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22250" ObjectName="SW-WD_JC.WD_JC_4421SW"/>
     <cge:Meas_Ref ObjectId="119374"/>
    <cge:TPSR_Ref TObjectID="22250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119375">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5144.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22251" ObjectName="SW-WD_JC.WD_JC_44217SW"/>
     <cge:Meas_Ref ObjectId="119375"/>
    <cge:TPSR_Ref TObjectID="22251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119366">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5245.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22244" ObjectName="SW-WD_JC.WD_JC_4432SW"/>
     <cge:Meas_Ref ObjectId="119366"/>
    <cge:TPSR_Ref TObjectID="22244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119369">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5245.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22247" ObjectName="SW-WD_JC.WD_JC_4436SW"/>
     <cge:Meas_Ref ObjectId="119369"/>
    <cge:TPSR_Ref TObjectID="22247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119367">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5245.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22245" ObjectName="SW-WD_JC.WD_JC_4431SW"/>
     <cge:Meas_Ref ObjectId="119367"/>
    <cge:TPSR_Ref TObjectID="22245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119368">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5276.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22246" ObjectName="SW-WD_JC.WD_JC_44317SW"/>
     <cge:Meas_Ref ObjectId="119368"/>
    <cge:TPSR_Ref TObjectID="22246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5390.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22239" ObjectName="SW-WD_JC.WD_JC_4442SW"/>
     <cge:Meas_Ref ObjectId="119359"/>
    <cge:TPSR_Ref TObjectID="22239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119362">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5390.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22242" ObjectName="SW-WD_JC.WD_JC_4446SW"/>
     <cge:Meas_Ref ObjectId="119362"/>
    <cge:TPSR_Ref TObjectID="22242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5390.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22240" ObjectName="SW-WD_JC.WD_JC_4441SW"/>
     <cge:Meas_Ref ObjectId="119360"/>
    <cge:TPSR_Ref TObjectID="22240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119361">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5421.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22241" ObjectName="SW-WD_JC.WD_JC_44417SW"/>
     <cge:Meas_Ref ObjectId="119361"/>
    <cge:TPSR_Ref TObjectID="22241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119352">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5522.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22234" ObjectName="SW-WD_JC.WD_JC_4452SW"/>
     <cge:Meas_Ref ObjectId="119352"/>
    <cge:TPSR_Ref TObjectID="22234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119355">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5522.000000 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22237" ObjectName="SW-WD_JC.WD_JC_4456SW"/>
     <cge:Meas_Ref ObjectId="119355"/>
    <cge:TPSR_Ref TObjectID="22237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119353">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5522.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22235" ObjectName="SW-WD_JC.WD_JC_4451SW"/>
     <cge:Meas_Ref ObjectId="119353"/>
    <cge:TPSR_Ref TObjectID="22235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119354">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5553.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22236" ObjectName="SW-WD_JC.WD_JC_44517SW"/>
     <cge:Meas_Ref ObjectId="119354"/>
    <cge:TPSR_Ref TObjectID="22236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119462">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4985.000000 -738.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22286" ObjectName="SW-WD_JC.WD_JC_3021SW"/>
     <cge:Meas_Ref ObjectId="119462"/>
    <cge:TPSR_Ref TObjectID="22286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119468">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4985.000000 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22289" ObjectName="SW-WD_JC.WD_JC_4026SW"/>
     <cge:Meas_Ref ObjectId="119468"/>
    <cge:TPSR_Ref TObjectID="22289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119467">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4985.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22288" ObjectName="SW-WD_JC.WD_JC_4021SW"/>
     <cge:Meas_Ref ObjectId="119467"/>
    <cge:TPSR_Ref TObjectID="22288"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-WD_JC.WD_JC_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 72.000000)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41643" ObjectName="CB-WD_JC.WD_JC_Cb1"/>
    <cge:TPSR_Ref TObjectID="41643"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3302750">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -1081.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26680d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -1081.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29a6f80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 -1081.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266ab90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 -1081.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ee4280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4509.000000 -663.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_335e4f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3338f80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_285b000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3668.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_324e410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3668.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a07d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3221d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef4f40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b02940">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23eb910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0c580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27520f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -72.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c77820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26fd8c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_322efa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4956.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b00680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4956.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34a89f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 -179.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32c5f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 -179.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2408d60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5083.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24099b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5083.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32c21c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5215.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32c2cd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5215.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24fe580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5360.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ff200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5360.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c387e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5492.000000 -129.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c39430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5492.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119270" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4120.000000 -1278.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119270" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22272"/>
     <cge:Term_Ref ObjectID="31253"/>
    <cge:TPSR_Ref TObjectID="22272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4120.000000 -1278.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22272"/>
     <cge:Term_Ref ObjectID="31253"/>
    <cge:TPSR_Ref TObjectID="22272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4120.000000 -1278.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22272"/>
     <cge:Term_Ref ObjectID="31253"/>
    <cge:TPSR_Ref TObjectID="22272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -1278.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22268"/>
     <cge:Term_Ref ObjectID="31245"/>
    <cge:TPSR_Ref TObjectID="22268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119266" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -1278.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22268"/>
     <cge:Term_Ref ObjectID="31245"/>
    <cge:TPSR_Ref TObjectID="22268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -1278.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22268"/>
     <cge:Term_Ref ObjectID="31245"/>
    <cge:TPSR_Ref TObjectID="22268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4698.000000 -1283.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22264"/>
     <cge:Term_Ref ObjectID="31237"/>
    <cge:TPSR_Ref TObjectID="22264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4698.000000 -1283.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22264"/>
     <cge:Term_Ref ObjectID="31237"/>
    <cge:TPSR_Ref TObjectID="22264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119258" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4698.000000 -1283.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119258" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22264"/>
     <cge:Term_Ref ObjectID="31237"/>
    <cge:TPSR_Ref TObjectID="22264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -1279.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22260"/>
     <cge:Term_Ref ObjectID="31229"/>
    <cge:TPSR_Ref TObjectID="22260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -1279.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22260"/>
     <cge:Term_Ref ObjectID="31229"/>
    <cge:TPSR_Ref TObjectID="22260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -1279.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22260"/>
     <cge:Term_Ref ObjectID="31229"/>
    <cge:TPSR_Ref TObjectID="22260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119287" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4014.000000 -481.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22282"/>
     <cge:Term_Ref ObjectID="31273"/>
    <cge:TPSR_Ref TObjectID="22282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119288" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4014.000000 -481.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22282"/>
     <cge:Term_Ref ObjectID="31273"/>
    <cge:TPSR_Ref TObjectID="22282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4014.000000 -481.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22282"/>
     <cge:Term_Ref ObjectID="31273"/>
    <cge:TPSR_Ref TObjectID="22282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4901.000000 -473.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22287"/>
     <cge:Term_Ref ObjectID="31283"/>
    <cge:TPSR_Ref TObjectID="22287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119300" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4901.000000 -473.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22287"/>
     <cge:Term_Ref ObjectID="31283"/>
    <cge:TPSR_Ref TObjectID="22287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4901.000000 -473.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22287"/>
     <cge:Term_Ref ObjectID="31283"/>
    <cge:TPSR_Ref TObjectID="22287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119220" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3546.000000 65.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119220" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22228"/>
     <cge:Term_Ref ObjectID="31165"/>
    <cge:TPSR_Ref TObjectID="22228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119221" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3546.000000 65.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119221" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22228"/>
     <cge:Term_Ref ObjectID="31165"/>
    <cge:TPSR_Ref TObjectID="22228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119218" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3546.000000 65.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22228"/>
     <cge:Term_Ref ObjectID="31165"/>
    <cge:TPSR_Ref TObjectID="22228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 66.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22223"/>
     <cge:Term_Ref ObjectID="31155"/>
    <cge:TPSR_Ref TObjectID="22223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119216" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 66.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22223"/>
     <cge:Term_Ref ObjectID="31155"/>
    <cge:TPSR_Ref TObjectID="22223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119213" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 66.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22223"/>
     <cge:Term_Ref ObjectID="31155"/>
    <cge:TPSR_Ref TObjectID="22223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119210" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3815.000000 69.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22218"/>
     <cge:Term_Ref ObjectID="31145"/>
    <cge:TPSR_Ref TObjectID="22218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119211" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3815.000000 69.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119211" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22218"/>
     <cge:Term_Ref ObjectID="31145"/>
    <cge:TPSR_Ref TObjectID="22218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119208" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3815.000000 69.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22218"/>
     <cge:Term_Ref ObjectID="31145"/>
    <cge:TPSR_Ref TObjectID="22218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119205" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 69.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22213"/>
     <cge:Term_Ref ObjectID="31135"/>
    <cge:TPSR_Ref TObjectID="22213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 69.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22213"/>
     <cge:Term_Ref ObjectID="31135"/>
    <cge:TPSR_Ref TObjectID="22213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119203" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 69.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119203" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22213"/>
     <cge:Term_Ref ObjectID="31135"/>
    <cge:TPSR_Ref TObjectID="22213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119200" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 66.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119200" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22208"/>
     <cge:Term_Ref ObjectID="31125"/>
    <cge:TPSR_Ref TObjectID="22208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119201" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 66.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22208"/>
     <cge:Term_Ref ObjectID="31125"/>
    <cge:TPSR_Ref TObjectID="22208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 66.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22208"/>
     <cge:Term_Ref ObjectID="31125"/>
    <cge:TPSR_Ref TObjectID="22208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4365.000000 106.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22276"/>
     <cge:Term_Ref ObjectID="31261"/>
    <cge:TPSR_Ref TObjectID="22276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4365.000000 106.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22276"/>
     <cge:Term_Ref ObjectID="31261"/>
    <cge:TPSR_Ref TObjectID="22276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -264.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22257"/>
     <cge:Term_Ref ObjectID="31223"/>
    <cge:TPSR_Ref TObjectID="22257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -264.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22257"/>
     <cge:Term_Ref ObjectID="31223"/>
    <cge:TPSR_Ref TObjectID="22257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -264.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22257"/>
     <cge:Term_Ref ObjectID="31223"/>
    <cge:TPSR_Ref TObjectID="22257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119245" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4990.000000 68.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22252"/>
     <cge:Term_Ref ObjectID="31213"/>
    <cge:TPSR_Ref TObjectID="22252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4990.000000 68.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22252"/>
     <cge:Term_Ref ObjectID="31213"/>
    <cge:TPSR_Ref TObjectID="22252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4990.000000 68.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22252"/>
     <cge:Term_Ref ObjectID="31213"/>
    <cge:TPSR_Ref TObjectID="22252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 67.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22248"/>
     <cge:Term_Ref ObjectID="31205"/>
    <cge:TPSR_Ref TObjectID="22248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 67.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22248"/>
     <cge:Term_Ref ObjectID="31205"/>
    <cge:TPSR_Ref TObjectID="22248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 67.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22248"/>
     <cge:Term_Ref ObjectID="31205"/>
    <cge:TPSR_Ref TObjectID="22248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5260.000000 67.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22243"/>
     <cge:Term_Ref ObjectID="31195"/>
    <cge:TPSR_Ref TObjectID="22243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5260.000000 67.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22243"/>
     <cge:Term_Ref ObjectID="31195"/>
    <cge:TPSR_Ref TObjectID="22243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119233" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5260.000000 67.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22243"/>
     <cge:Term_Ref ObjectID="31195"/>
    <cge:TPSR_Ref TObjectID="22243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119225" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5522.000000 64.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119225" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22233"/>
     <cge:Term_Ref ObjectID="31175"/>
    <cge:TPSR_Ref TObjectID="22233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119226" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5522.000000 64.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119226" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22233"/>
     <cge:Term_Ref ObjectID="31175"/>
    <cge:TPSR_Ref TObjectID="22233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119223" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5522.000000 64.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119223" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22233"/>
     <cge:Term_Ref ObjectID="31175"/>
    <cge:TPSR_Ref TObjectID="22233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5393.000000 67.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22238"/>
     <cge:Term_Ref ObjectID="31185"/>
    <cge:TPSR_Ref TObjectID="22238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5393.000000 67.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22238"/>
     <cge:Term_Ref ObjectID="31185"/>
    <cge:TPSR_Ref TObjectID="22238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5393.000000 67.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22238"/>
     <cge:Term_Ref ObjectID="31185"/>
    <cge:TPSR_Ref TObjectID="22238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-119638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -485.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22301"/>
     <cge:Term_Ref ObjectID="31312"/>
    <cge:TPSR_Ref TObjectID="22301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-119642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -485.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22301"/>
     <cge:Term_Ref ObjectID="31312"/>
    <cge:TPSR_Ref TObjectID="22301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-119643" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -485.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119643" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22301"/>
     <cge:Term_Ref ObjectID="31312"/>
    <cge:TPSR_Ref TObjectID="22301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-121134" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -485.000000) translate(0,95)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22301"/>
     <cge:Term_Ref ObjectID="31312"/>
    <cge:TPSR_Ref TObjectID="22301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-119646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -485.000000) translate(0,120)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22301"/>
     <cge:Term_Ref ObjectID="31312"/>
    <cge:TPSR_Ref TObjectID="22301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-119635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -989.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22300"/>
     <cge:Term_Ref ObjectID="31311"/>
    <cge:TPSR_Ref TObjectID="22300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-119636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -989.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22300"/>
     <cge:Term_Ref ObjectID="31311"/>
    <cge:TPSR_Ref TObjectID="22300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-119637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -989.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22300"/>
     <cge:Term_Ref ObjectID="31311"/>
    <cge:TPSR_Ref TObjectID="22300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-121133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -989.000000) translate(0,95)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22300"/>
     <cge:Term_Ref ObjectID="31311"/>
    <cge:TPSR_Ref TObjectID="22300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-119644" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -989.000000) translate(0,120)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119644" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22300"/>
     <cge:Term_Ref ObjectID="31311"/>
    <cge:TPSR_Ref TObjectID="22300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-119634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -989.000000) translate(0,145)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22300"/>
     <cge:Term_Ref ObjectID="31311"/>
    <cge:TPSR_Ref TObjectID="22300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-119639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5550.000000 -492.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22302"/>
     <cge:Term_Ref ObjectID="31313"/>
    <cge:TPSR_Ref TObjectID="22302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-119640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5550.000000 -492.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22302"/>
     <cge:Term_Ref ObjectID="31313"/>
    <cge:TPSR_Ref TObjectID="22302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-119641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5550.000000 -492.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22302"/>
     <cge:Term_Ref ObjectID="31313"/>
    <cge:TPSR_Ref TObjectID="22302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-121135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5550.000000 -492.000000) translate(0,95)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22302"/>
     <cge:Term_Ref ObjectID="31313"/>
    <cge:TPSR_Ref TObjectID="22302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-119648" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5550.000000 -492.000000) translate(0,120)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22302"/>
     <cge:Term_Ref ObjectID="31313"/>
    <cge:TPSR_Ref TObjectID="22302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119281" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.000000 -791.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22280"/>
     <cge:Term_Ref ObjectID="31269"/>
    <cge:TPSR_Ref TObjectID="22280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119282" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.000000 -791.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22280"/>
     <cge:Term_Ref ObjectID="31269"/>
    <cge:TPSR_Ref TObjectID="22280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119278" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.000000 -791.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22280"/>
     <cge:Term_Ref ObjectID="31269"/>
    <cge:TPSR_Ref TObjectID="22280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-119283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.000000 -791.000000) translate(0,95)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22280"/>
     <cge:Term_Ref ObjectID="31269"/>
    <cge:TPSR_Ref TObjectID="22280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119293" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -794.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22285"/>
     <cge:Term_Ref ObjectID="31279"/>
    <cge:TPSR_Ref TObjectID="22285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -794.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22285"/>
     <cge:Term_Ref ObjectID="31279"/>
    <cge:TPSR_Ref TObjectID="22285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119290" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -794.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119290" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22285"/>
     <cge:Term_Ref ObjectID="31279"/>
    <cge:TPSR_Ref TObjectID="22285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-119295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -794.000000) translate(0,95)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22285"/>
     <cge:Term_Ref ObjectID="31279"/>
    <cge:TPSR_Ref TObjectID="22285"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="66" qtmmishow="hidden" width="245" x="3235" y="-1193"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="97" qtmmishow="hidden" width="99" x="3176" y="-1215"/></g>
   <g href="35kV近城变WD_JC_433间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="3791" y="-274"/></g>
   <g href="35kV近城变WD_JC_434间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="3926" y="-277"/></g>
   <g href="35kV近城变WD_JC_431间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="3513" y="-277"/></g>
   <g href="35kV近城变WD_JC_432间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="3653" y="-276"/></g>
   <g href="35kV近城变10kV城区Ⅰ回线435间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="36" x="4061" y="-276"/></g>
   <g href="35kV近城变WD_JC_436间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="36" x="4316" y="-274"/></g>
   <g href="35kV近城变WD_JC_412间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="4674" y="-322"/></g>
   <g href="35kV近城变WD_JC_441间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="37" x="4938" y="-277"/></g>
   <g href="35kV近城变WD_JC_442间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="5065" y="-276"/></g>
   <g href="35kV近城变10kV备用线443间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="5196" y="-279"/></g>
   <g href="35kV近城变WD_JC_444间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="5345" y="-277"/></g>
   <g href="35kV近城变WD_JC_445间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="36" x="5473" y="-281"/></g>
   <g href="35kV近城变WD_JC_331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="4137" y="-919"/></g>
   <g href="35kV近城变WD_JC_332间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="4419" y="-919"/></g>
   <g href="35kV近城变WD_JC_333间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="4715" y="-915"/></g>
   <g href="35kV近城变WD_JC_334间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="5018" y="-916"/></g>
   <g href="35kV近城变近城变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="32" qtmmishow="hidden" width="112" x="4189" y="-672"/></g>
   <g href="35kV近城变近城变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="32" qtmmishow="hidden" width="112" x="5062" y="-671"/></g>
   <g href="35kV近城变GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="34" qtmmishow="hidden" width="99" x="3235" y="-756"/></g>
   <g href="cx_配调_配网接线图35_武定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3602" y="-1164"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3602" y="-1199"/></g>
   <g href="AVC近城站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3517" y="-1185"/></g>
   <g href="35kV近城变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="34" qtmmishow="hidden" width="99" x="3229" y="-690"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.819672 -0.000000 0.000000 -1.466667 -2927.196721 -4.933333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d04990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3498.000000 -47.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3417570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3487.000000 -62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322ab50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 -77.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(2.015003 -0.000000 0.000000 -1.434783 -6197.854291 18.891304)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eeb700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287ef90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cf2450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.323258 -0.000000 0.000000 -1.463038 -1406.055780 -25.210391)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_333b7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 -89.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a40b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 -104.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.297136 -0.000000 0.000000 -1.954389 -1577.782343 574.606356)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a42c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.000000 605.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27084e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.000000 590.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.673752 -0.000000 0.000000 -1.695652 -4572.661225 1044.326087)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2708950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2708c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c51e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.531557 -0.000000 0.000000 -1.458238 -2124.928471 316.850203)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c52040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 744.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c522f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 729.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c52530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 713.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c527f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 759.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.297136 -0.000000 0.000000 -1.954389 -2465.782343 578.606356)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c53310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.000000 605.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c53560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.000000 590.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.531557 -0.000000 0.000000 -1.458238 -1250.928471 316.850203)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c53860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 744.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c53ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 729.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef1bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 713.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef1e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 759.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.673752 -0.000000 0.000000 -1.695652 -3686.661225 1054.326087)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef2230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef2530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef2770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27305b0" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3457.631579 436.443182) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27308a0" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3465.105263 410.329545) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2730af0" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3445.000000 384.556818) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3caf580" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3458.631579 458.556818) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3caf7d0" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3460.631579 483.329545) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cafad0" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 5455.631579 441.443182) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cafd40" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 5463.105263 415.329545) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3caff80" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 5443.000000 389.556818) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb01c0" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 5456.631579 463.556818) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb0400" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 5458.631579 488.329545) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb0730" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3786.105263 913.329545) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb09b0" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3766.000000 887.556818) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb0bf0" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3779.631579 961.556818) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb0e30" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3781.631579 986.329545) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb1070" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3790.000000 862.556818) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb12b0" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 3778.631579 939.443182) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.819672 -0.000000 0.000000 -1.466667 -1477.196721 1.066667)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d0bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3498.000000 -47.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2792060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3487.000000 -62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2792240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 -77.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.819672 -0.000000 0.000000 -1.466667 -2057.196721 -1343.933333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2792660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3498.000000 -47.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2792960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3487.000000 -62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2792ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 -77.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.819672 -0.000000 0.000000 -1.466667 -1759.196721 -1350.933333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2792fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3498.000000 -47.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27932c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3487.000000 -62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2793500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 -77.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.819672 -0.000000 0.000000 -1.466667 -1464.196721 -1346.933333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2294090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3498.000000 -47.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2294390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3487.000000 -62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22945d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 -77.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_JC.WD_JC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3512,-349 4671,-349 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22301" ObjectName="BS-WD_JC.WD_JC_9IM"/>
    <cge:TPSR_Ref TObjectID="22301"/></metadata>
   <polyline fill="none" opacity="0" points="3512,-349 4671,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_JC.WD_JC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-833 5169,-833 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22300" ObjectName="BS-WD_JC.WD_JC_3IM"/>
    <cge:TPSR_Ref TObjectID="22300"/></metadata>
   <polyline fill="none" opacity="0" points="3913,-833 5169,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_JC.WD_JC_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4726,-349 5613,-349 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22302" ObjectName="BS-WD_JC.WD_JC_9IIM"/>
    <cge:TPSR_Ref TObjectID="22302"/></metadata>
   <polyline fill="none" opacity="0" points="4726,-349 5613,-349 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="25" transform="matrix(2.291667 -0.000000 -0.000000 1.583142 3261.000000 -1109.170179) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-119312" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4312.000000 -599.000000) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119312" ObjectName="WD_JC:WD_JC_1T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145075" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3300.538462 -929.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145075" ObjectName="WD_JC:WD_JC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145366" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3298.538462 -878.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145366" ObjectName="WD_JC:WD_JC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-119313" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.000000 -569.000000) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119313" ObjectName="WD_JC:WD_JC_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-119314" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5194.000000 -604.000000) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119314" ObjectName="WD_JC:WD_JC_2T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-119315" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -573.000000) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119315" ObjectName="WD_JC:WD_JC_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145075" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3296.538462 -1026.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145075" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145075" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3297.538462 -976.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145075" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-1048"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416609" width="14" x="4218" y="-247"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(255,255,0)" stroke-width="1" width="9" x="4551" y="-715"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(118,238,0)" stroke-width="1" width="9" x="4530" y="-233"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(118,238,0)" stroke-width="1" width="9" x="4829" y="-234"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="66" qtmmishow="hidden" width="245" x="3235" y="-1193"/>
    </a>
   <metadata/><rect fill="white" height="66" opacity="0" stroke="white" transform="" width="245" x="3235" y="-1193"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="97" qtmmishow="hidden" width="99" x="3176" y="-1215"/>
    </a>
   <metadata/><rect fill="white" height="97" opacity="0" stroke="white" transform="" width="99" x="3176" y="-1215"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="3791" y="-274"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="3791" y="-274"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="3926" y="-277"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="3926" y="-277"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="3513" y="-277"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="3513" y="-277"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="3653" y="-276"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="3653" y="-276"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="36" x="4061" y="-276"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="36" x="4061" y="-276"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="36" x="4316" y="-274"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="36" x="4316" y="-274"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="4674" y="-322"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="4674" y="-322"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="37" x="4938" y="-277"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="37" x="4938" y="-277"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="5065" y="-276"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="5065" y="-276"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="5196" y="-279"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="5196" y="-279"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="5345" y="-277"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="5345" y="-277"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="36" x="5473" y="-281"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="36" x="5473" y="-281"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="4137" y="-919"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="4137" y="-919"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="4419" y="-919"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="4419" y="-919"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="4715" y="-915"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="4715" y="-915"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="5018" y="-916"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="5018" y="-916"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="32" qtmmishow="hidden" width="112" x="4189" y="-672"/>
    </a>
   <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="" width="112" x="4189" y="-672"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="32" qtmmishow="hidden" width="112" x="5062" y="-671"/>
    </a>
   <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="" width="112" x="5062" y="-671"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="34" qtmmishow="hidden" width="99" x="3235" y="-756"/>
    </a>
   <metadata/><rect fill="white" height="34" opacity="0" stroke="white" transform="" width="99" x="3235" y="-756"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3602" y="-1164"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3602" y="-1164"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3602" y="-1199"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3602" y="-1199"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3517,-1185 3514,-1188 3514,-1135 3517,-1138 3517,-1185" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3517,-1185 3514,-1188 3565,-1188 3562,-1185 3517,-1185" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3517,-1138 3514,-1135 3565,-1135 3562,-1138 3517,-1138" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3562,-1185 3565,-1188 3565,-1135 3562,-1138 3562,-1185" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="3517" y="-1185"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3517" y="-1185"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="34" qtmmishow="hidden" width="99" x="3229" y="-690"/>
    </a>
   <metadata/><rect fill="white" height="34" opacity="0" stroke="white" transform="" width="99" x="3229" y="-690"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-WD_JC.WD_JC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31305"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -567.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -567.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22297" ObjectName="TF-WD_JC.WD_JC_1T"/>
    <cge:TPSR_Ref TObjectID="22297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_JC.WD_JC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31309"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4955.000000 -568.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4955.000000 -568.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22298" ObjectName="TF-WD_JC.WD_JC_2T"/>
    <cge:TPSR_Ref TObjectID="22298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.869565 -0.000000 0.000000 -0.897959 4787.000000 -928.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.869565 -0.000000 0.000000 -0.897959 4787.000000 -928.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_JC"/>
</svg>