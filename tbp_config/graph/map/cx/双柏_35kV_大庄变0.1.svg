<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-160" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-265 -1300 2306 1450">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="47" y2="76"/>
    <polyline arcFlag="1" points="25,13 27,13 29,14 30,14 32,15 33,16 35,17 36,19 37,21 37,22 38,24 38,26 38,28 37,30 37,31 36,33 35,34 33,36 32,37 30,38 29,38 27,39 25,39 23,39 21,38 20,38 18,37 17,36 15,34 14,33 13,31 13,30 12,28 12,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="85" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="93" y2="101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="91" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="96" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="93" y2="114"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="108" y2="104"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="108" y2="104"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="51"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="101" y2="101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="108" y2="108"/>
    <polyline arcFlag="1" points="44,67 45,67 46,67 46,67 47,66 47,66 48,65 48,65 49,64 49,64 49,63 49,62 50,61 50,60 50,60 49,59 49,58 49,57 49,57 48,56 48,56 47,55 47,55 46,54 46,54 45,54 44,54 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,80 45,80 46,80 46,80 47,79 47,79 48,78 48,78 49,77 49,77 49,76 49,75 50,74 50,73 50,73 49,72 49,71 49,70 49,70 48,69 48,69 47,68 47,68 46,67 46,67 45,67 44,67 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,93 45,93 46,93 46,93 47,92 47,92 48,91 48,91 49,90 49,90 49,89 49,88 50,87 50,87 50,86 49,85 49,84 49,83 49,83 48,82 48,82 47,81 47,81 46,80 46,80 45,80 44,80 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="13" y2="5"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape201">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.485753" x1="3" x2="15" y1="27" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="15" x2="9" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="90" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.532624" x1="5" x2="8" y1="100" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="3" x2="10" y1="98" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="0" x2="13" y1="96" y2="96"/>
    <polyline arcFlag="1" points="6,79 7,79 7,79 8,79 9,79 9,78 10,78 11,77 11,77 11,76 12,75 12,75 12,74 12,73 12,72 12,72 11,71 11,70 11,70 10,69 9,69 9,68 8,68 7,68 7,68 6,68 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,68 7,68 7,68 8,68 9,68 9,67 10,67 11,66 11,66 11,65 12,64 12,64 12,63 12,62 12,61 12,61 11,60 11,59 11,59 10,58 9,58 9,57 8,57 7,57 7,57 6,57 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,57 7,57 7,57 8,57 9,57 9,56 10,56 11,55 11,55 11,54 12,53 12,53 12,52 12,51 12,50 12,50 11,49 11,48 11,48 10,47 9,47 9,46 8,46 7,46 7,46 6,46 " stroke-width="0.815047"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="4" y2="13"/>
    <polyline arcFlag="1" points="6,46 7,46 7,46 8,46 9,46 9,45 10,45 11,44 11,44 11,43 12,42 12,42 12,41 12,40 12,39 12,39 11,38 11,37 11,37 10,36 9,36 9,35 8,35 7,35 7,35 6,35 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,35 7,35 7,35 8,35 9,35 9,34 10,34 11,33 11,33 11,32 12,31 12,31 12,30 12,29 12,28 12,28 11,27 11,26 11,26 10,25 9,25 9,24 8,24 7,24 7,24 6,24 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,90 7,90 7,90 8,90 9,90 9,89 10,89 11,88 11,88 11,87 12,86 12,86 12,85 12,84 12,83 12,83 11,82 11,81 11,81 10,80 9,80 9,79 8,79 7,79 7,79 6,79 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,24 7,24 7,24 8,24 9,24 9,23 10,23 11,22 11,22 11,21 12,20 12,20 12,19 12,18 12,17 12,17 11,16 11,15 11,15 10,14 9,14 9,13 8,13 7,13 7,13 6,13 " stroke-width="0.815047"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.71892" x1="15" x2="15" y1="8" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="14" x2="39" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="5" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="3" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="3" x2="43" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="11" x2="11" y1="3" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="43" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="11" x2="11" y1="3" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="43" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2167600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_210dad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20daf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f5e0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26c7b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d59080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2127a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_17a77b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2013850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2632150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2632150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2559660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2559660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2497550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_239d2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f40f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22aba00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2284af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2166fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2134380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20e7880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_209acb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f32140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d30060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26ba2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_210d480" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21a6b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_152a080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bcf7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc67a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2bc8a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2bc0ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1460" width="2316" x="-270" y="-1305"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3686340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 940.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca72e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 926.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3428c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 912.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3339c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 897.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2deb130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 414.000000 882.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2b970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 1292.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27223f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 635.000000 1277.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e57c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 660.000000 1262.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18be6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 722.000000 695.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c7e360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 680.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a4500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 736.000000 665.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_367c940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 724.000000 555.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d67450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 724.000000 537.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3543990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 720.000000 498.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35515c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 483.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35517d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 468.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1879660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 114.000000 437.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d04770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 114.000000 423.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d049a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 114.000000 409.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d888a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 120.000000 394.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d9ea40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 106.000000 379.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ec9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 192.000000 -103.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ecc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.000000 -118.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_184ac40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 206.000000 -133.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb4750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 676.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2718340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1508.000000 661.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2718550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1533.000000 646.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c52280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.000000 565.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c524e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.000000 547.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c52720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 479.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33adaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1520.000000 464.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33adce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1545.000000 449.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3546f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1946.000000 432.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35471f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1946.000000 418.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3435030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1946.000000 404.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3435270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1952.000000 389.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34354b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1938.000000 374.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d63e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 372.000000 451.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 436.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d642b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 386.000000 421.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="64" stroke="rgb(0,255,0)" stroke-width="1" width="37" x="260" y="-737"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(0,255,0)" stroke-width="1" width="34" x="303" y="-733"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="92" stroke="rgb(0,255,0)" stroke-width="1" width="20" x="208" y="-762"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="216" y="-727"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(50,205,50)" stroke-width="0.424575" width="29" x="203" y="-605"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-108241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.000000 -1002.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21015" ObjectName="SW-SB_DZ.SB_DZ_3616SW"/>
     <cge:Meas_Ref ObjectId="108241"/>
    <cge:TPSR_Ref TObjectID="21015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.000000 -846.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21014" ObjectName="SW-SB_DZ.SB_DZ_3611SW"/>
     <cge:Meas_Ref ObjectId="108240"/>
    <cge:TPSR_Ref TObjectID="21014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108244">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.000000 -1022.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21018" ObjectName="SW-SB_DZ.SB_DZ_36167SW"/>
     <cge:Meas_Ref ObjectId="108244"/>
    <cge:TPSR_Ref TObjectID="21018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 -901.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21016" ObjectName="SW-SB_DZ.SB_DZ_36117SW"/>
     <cge:Meas_Ref ObjectId="108242"/>
    <cge:TPSR_Ref TObjectID="21016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 -992.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21017" ObjectName="SW-SB_DZ.SB_DZ_36160SW"/>
     <cge:Meas_Ref ObjectId="108243"/>
    <cge:TPSR_Ref TObjectID="21017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1189.000000 -858.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20982" ObjectName="SW-SB_DZ.SB_DZ_39010SW"/>
     <cge:Meas_Ref ObjectId="107721"/>
    <cge:TPSR_Ref TObjectID="20982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107716">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1110.000000 -875.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20977" ObjectName="SW-SB_DZ.SB_DZ_3901SW"/>
     <cge:Meas_Ref ObjectId="107716"/>
    <cge:TPSR_Ref TObjectID="20977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1026.000000 -890.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20981" ObjectName="SW-SB_DZ.SB_DZ_39017SW"/>
     <cge:Meas_Ref ObjectId="107720"/>
    <cge:TPSR_Ref TObjectID="20981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107717">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 -742.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20978" ObjectName="SW-SB_DZ.SB_DZ_3011SW"/>
     <cge:Meas_Ref ObjectId="107717"/>
    <cge:TPSR_Ref TObjectID="20978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 557.000000 -674.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20980" ObjectName="SW-SB_DZ.SB_DZ_30117SW"/>
     <cge:Meas_Ref ObjectId="107719"/>
    <cge:TPSR_Ref TObjectID="20980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107718">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20979" ObjectName="SW-SB_DZ.SB_DZ_0011SW"/>
     <cge:Meas_Ref ObjectId="107718"/>
    <cge:TPSR_Ref TObjectID="20979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107715">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 975.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20976" ObjectName="SW-SB_DZ.SB_DZ_0901SW"/>
     <cge:Meas_Ref ObjectId="107715"/>
    <cge:TPSR_Ref TObjectID="20976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 977.000000 -466.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107835">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -87.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20992" ObjectName="SW-SB_DZ.SB_DZ_0626SW"/>
     <cge:Meas_Ref ObjectId="107835"/>
    <cge:TPSR_Ref TObjectID="20992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107836">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -271.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20993" ObjectName="SW-SB_DZ.SB_DZ_0621SW"/>
     <cge:Meas_Ref ObjectId="107836"/>
    <cge:TPSR_Ref TObjectID="20993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -90.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20989" ObjectName="SW-SB_DZ.SB_DZ_0636SW"/>
     <cge:Meas_Ref ObjectId="107804"/>
    <cge:TPSR_Ref TObjectID="20989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107805">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -264.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20990" ObjectName="SW-SB_DZ.SB_DZ_0631SW"/>
     <cge:Meas_Ref ObjectId="107805"/>
    <cge:TPSR_Ref TObjectID="20990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107897">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.000000 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20998" ObjectName="SW-SB_DZ.SB_DZ_0646SW"/>
     <cge:Meas_Ref ObjectId="107897"/>
    <cge:TPSR_Ref TObjectID="20998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107898">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.000000 -266.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20999" ObjectName="SW-SB_DZ.SB_DZ_0641SW"/>
     <cge:Meas_Ref ObjectId="107898"/>
    <cge:TPSR_Ref TObjectID="20999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107866">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1011.000000 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20995" ObjectName="SW-SB_DZ.SB_DZ_0656SW"/>
     <cge:Meas_Ref ObjectId="107866"/>
    <cge:TPSR_Ref TObjectID="20995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107867">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1011.000000 -265.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20996" ObjectName="SW-SB_DZ.SB_DZ_0651SW"/>
     <cge:Meas_Ref ObjectId="107867"/>
    <cge:TPSR_Ref TObjectID="20996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -87.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20986" ObjectName="SW-SB_DZ.SB_DZ_0666SW"/>
     <cge:Meas_Ref ObjectId="107772"/>
    <cge:TPSR_Ref TObjectID="20986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107928">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1464.000000 -85.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21001" ObjectName="SW-SB_DZ.SB_DZ_0676SW"/>
     <cge:Meas_Ref ObjectId="107928"/>
    <cge:TPSR_Ref TObjectID="21001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 -723.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107711">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.000000 -1010.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20972" ObjectName="SW-SB_DZ.SB_DZ_3626SW"/>
     <cge:Meas_Ref ObjectId="107711"/>
    <cge:TPSR_Ref TObjectID="20972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107710">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.000000 -854.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20971" ObjectName="SW-SB_DZ.SB_DZ_3621SW"/>
     <cge:Meas_Ref ObjectId="107710"/>
    <cge:TPSR_Ref TObjectID="20971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107714">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1403.000000 -1030.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20975" ObjectName="SW-SB_DZ.SB_DZ_36267SW"/>
     <cge:Meas_Ref ObjectId="107714"/>
    <cge:TPSR_Ref TObjectID="20975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107712">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1594.000000 -909.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20973" ObjectName="SW-SB_DZ.SB_DZ_36217SW"/>
     <cge:Meas_Ref ObjectId="107712"/>
    <cge:TPSR_Ref TObjectID="20973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107713">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1595.000000 -1000.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20974" ObjectName="SW-SB_DZ.SB_DZ_36260SW"/>
     <cge:Meas_Ref ObjectId="107713"/>
    <cge:TPSR_Ref TObjectID="20974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107960">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21005" ObjectName="SW-SB_DZ.SB_DZ_0616SW"/>
     <cge:Meas_Ref ObjectId="107960"/>
    <cge:TPSR_Ref TObjectID="21005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -271.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21004" ObjectName="SW-SB_DZ.SB_DZ_0611SW"/>
     <cge:Meas_Ref ObjectId="107959"/>
    <cge:TPSR_Ref TObjectID="21004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107981">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 338.000000 3.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21006" ObjectName="SW-SB_DZ.SB_DZ_06100SW"/>
     <cge:Meas_Ref ObjectId="107981"/>
    <cge:TPSR_Ref TObjectID="21006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 345.000000 -132.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21013" ObjectName="SW-SB_DZ.SB_DZ_06167SW"/>
     <cge:Meas_Ref ObjectId="107968"/>
    <cge:TPSR_Ref TObjectID="21013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.000000 -454.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38888" ObjectName="SW-SB_DZ.SB_DZ_0686SW"/>
     <cge:Meas_Ref ObjectId="233132"/>
    <cge:TPSR_Ref TObjectID="38888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233131">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38887" ObjectName="SW-SB_DZ.SB_DZ_0681SW"/>
     <cge:Meas_Ref ObjectId="233131"/>
    <cge:TPSR_Ref TObjectID="38887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 259.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38889" ObjectName="SW-SB_DZ.SB_DZ_0030SW"/>
     <cge:Meas_Ref ObjectId="233133"/>
    <cge:TPSR_Ref TObjectID="38889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244003">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -92.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41026" ObjectName="SW-SB_DZ.SB_DZ_0696SW"/>
     <cge:Meas_Ref ObjectId="244003"/>
    <cge:TPSR_Ref TObjectID="41026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-262184">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -266.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42829" ObjectName="SW-SB_DZ.SB_DZ_0692SW"/>
     <cge:Meas_Ref ObjectId="262184"/>
    <cge:TPSR_Ref TObjectID="42829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244048">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1894.000000 -87.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41029" ObjectName="SW-SB_DZ.SB_DZ_0716SW"/>
     <cge:Meas_Ref ObjectId="244048"/>
    <cge:TPSR_Ref TObjectID="41029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-262221">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1894.000000 -261.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42830" ObjectName="SW-SB_DZ.SB_DZ_0712SW"/>
     <cge:Meas_Ref ObjectId="262221"/>
    <cge:TPSR_Ref TObjectID="42830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261809">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1453.000000 -739.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42820" ObjectName="SW-SB_DZ.SB_DZ_3021SW"/>
     <cge:Meas_Ref ObjectId="261809"/>
    <cge:TPSR_Ref TObjectID="42820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261810">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.000000 -673.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42821" ObjectName="SW-SB_DZ.SB_DZ_30217SW"/>
     <cge:Meas_Ref ObjectId="261810"/>
    <cge:TPSR_Ref TObjectID="42821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261811">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1453.000000 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42822" ObjectName="SW-SB_DZ.SB_DZ_0022SW"/>
     <cge:Meas_Ref ObjectId="261811"/>
    <cge:TPSR_Ref TObjectID="42822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261895">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1765.000000 -372.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42824" ObjectName="SW-SB_DZ.SB_DZ_0902SW"/>
     <cge:Meas_Ref ObjectId="261895"/>
    <cge:TPSR_Ref TObjectID="42824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261901">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.090909 -405.000000)" xlink:href="#switch2:shape4_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42826" ObjectName="SW-SB_DZ.SB_DZ_0121SW"/>
     <cge:Meas_Ref ObjectId="261901"/>
    <cge:TPSR_Ref TObjectID="42826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -261.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42827" ObjectName="SW-SB_DZ.SB_DZ_0662SW"/>
     <cge:Meas_Ref ObjectId="107773"/>
    <cge:TPSR_Ref TObjectID="42827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-262107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1464.000000 -265.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42828" ObjectName="SW-SB_DZ.SB_DZ_0672SW"/>
     <cge:Meas_Ref ObjectId="262107"/>
    <cge:TPSR_Ref TObjectID="42828"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-SB_DZ.SB_DZ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="366,-826 1893,-826 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20966" ObjectName="BS-SB_DZ.SB_DZ_3IM"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   <polyline fill="none" opacity="0" points="366,-826 1893,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_DZ.SB_DZ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,-348 1092,-348 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20967" ObjectName="BS-SB_DZ.SB_DZ_9IM"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   <polyline fill="none" opacity="0" points="183,-348 1092,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_DZ.SB_DZ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1283,-348 2008,-348 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20968" ObjectName="BS-SB_DZ.SB_DZ_9IIM"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   <polyline fill="none" opacity="0" points="1283,-348 2008,-348 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-SB_DZ.SB_DZ_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 281.000000 -123.000000)" xlink:href="#capacitor:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40826" ObjectName="CB-SB_DZ.SB_DZ_Cb1"/>
    <cge:TPSR_Ref TObjectID="40826"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-SB_DZ.SB_DZ_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29269"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1090.000000 -616.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1090.000000 -616.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21011" ObjectName="TF-SB_DZ.SB_DZ_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_DZ.SB_DZ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29265"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.069444 -0.000000 0.000000 -1.088889 625.000000 -519.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.069444 -0.000000 0.000000 -1.088889 625.000000 -519.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21009" ObjectName="TF-SB_DZ.SB_DZ_1T"/>
    <cge:TPSR_Ref TObjectID="21009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_DZ.SB_DZ_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29273"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.720000 -0.000000 0.000000 -0.777778 850.000000 -20.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.720000 -0.000000 0.000000 -0.777778 850.000000 -20.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21012" ObjectName="TF-SB_DZ.SB_DZ_Zyb2"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_DZ.SB_DZ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="18688"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.000000 -515.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.000000 -515.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="42816" ObjectName="TF-SB_DZ.SB_DZ_2T"/>
    <cge:TPSR_Ref TObjectID="42816"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_333c470">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 -1113.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32e9040">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1110.000000 -967.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be7fb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.000000 -947.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef2d20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 -398.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3599160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -15.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2defa90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 590.000000 -8.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d69170">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 -71.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ee6370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 -9.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a41800">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -5.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33200e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1422.000000 -3.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17da130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.000000 -1121.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c52c20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 460.000000 -1.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a3e70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 3.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17de9e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 784.000000 -17.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d98930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.648489 -509.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e694c0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -0.626634 262.000000 -682.000000)" xlink:href="#lightningRod:shape201"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35582e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 288.000000 -616.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b73a80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 344.000000 -617.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e62d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1609.000000 -10.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_187aff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1852.000000 -5.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3555db0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 245.000000 -62.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2def0f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1704.000000 -402.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3426110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1765.000000 -480.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -161.000000 -1001.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-107552" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -129.000000 -820.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107552" ObjectName="SB_DZ:SB_DZ_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-107553" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -129.000000 -780.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107553" ObjectName="SB_DZ:SB_DZ_301BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-107552" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -905.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107552" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-107552" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -130.000000 -865.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107552" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107696" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -1293.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21008"/>
     <cge:Term_Ref ObjectID="29261"/>
    <cge:TPSR_Ref TObjectID="21008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107697" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -1293.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21008"/>
     <cge:Term_Ref ObjectID="29261"/>
    <cge:TPSR_Ref TObjectID="21008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107693" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -1293.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21008"/>
     <cge:Term_Ref ObjectID="29261"/>
    <cge:TPSR_Ref TObjectID="21008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107678" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -1300.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107678" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21007"/>
     <cge:Term_Ref ObjectID="29259"/>
    <cge:TPSR_Ref TObjectID="21007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107679" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -1300.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107679" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21007"/>
     <cge:Term_Ref ObjectID="29259"/>
    <cge:TPSR_Ref TObjectID="21007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107670" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -1300.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21007"/>
     <cge:Term_Ref ObjectID="29259"/>
    <cge:TPSR_Ref TObjectID="21007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 783.000000 -695.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20983"/>
     <cge:Term_Ref ObjectID="29211"/>
    <cge:TPSR_Ref TObjectID="20983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 783.000000 -695.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20983"/>
     <cge:Term_Ref ObjectID="29211"/>
    <cge:TPSR_Ref TObjectID="20983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 783.000000 -695.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20983"/>
     <cge:Term_Ref ObjectID="29211"/>
    <cge:TPSR_Ref TObjectID="20983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 -495.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20984"/>
     <cge:Term_Ref ObjectID="29213"/>
    <cge:TPSR_Ref TObjectID="20984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 -495.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20984"/>
     <cge:Term_Ref ObjectID="29213"/>
    <cge:TPSR_Ref TObjectID="20984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 -495.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20984"/>
     <cge:Term_Ref ObjectID="29213"/>
    <cge:TPSR_Ref TObjectID="20984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107664" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 279.000000 105.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21003"/>
     <cge:Term_Ref ObjectID="29251"/>
    <cge:TPSR_Ref TObjectID="21003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107665" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 279.000000 105.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21003"/>
     <cge:Term_Ref ObjectID="29251"/>
    <cge:TPSR_Ref TObjectID="21003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107656" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 279.000000 105.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21003"/>
     <cge:Term_Ref ObjectID="29251"/>
    <cge:TPSR_Ref TObjectID="21003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 452.000000 105.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20991"/>
     <cge:Term_Ref ObjectID="29227"/>
    <cge:TPSR_Ref TObjectID="20991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 452.000000 105.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20991"/>
     <cge:Term_Ref ObjectID="29227"/>
    <cge:TPSR_Ref TObjectID="20991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 452.000000 105.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20991"/>
     <cge:Term_Ref ObjectID="29227"/>
    <cge:TPSR_Ref TObjectID="20991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 628.000000 105.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20988"/>
     <cge:Term_Ref ObjectID="29221"/>
    <cge:TPSR_Ref TObjectID="20988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 628.000000 105.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20988"/>
     <cge:Term_Ref ObjectID="29221"/>
    <cge:TPSR_Ref TObjectID="20988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 628.000000 105.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20988"/>
     <cge:Term_Ref ObjectID="29221"/>
    <cge:TPSR_Ref TObjectID="20988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 105.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20997"/>
     <cge:Term_Ref ObjectID="29239"/>
    <cge:TPSR_Ref TObjectID="20997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 105.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20997"/>
     <cge:Term_Ref ObjectID="29239"/>
    <cge:TPSR_Ref TObjectID="20997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 105.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20997"/>
     <cge:Term_Ref ObjectID="29239"/>
    <cge:TPSR_Ref TObjectID="20997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 105.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20994"/>
     <cge:Term_Ref ObjectID="29233"/>
    <cge:TPSR_Ref TObjectID="20994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 105.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20994"/>
     <cge:Term_Ref ObjectID="29233"/>
    <cge:TPSR_Ref TObjectID="20994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 105.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20994"/>
     <cge:Term_Ref ObjectID="29233"/>
    <cge:TPSR_Ref TObjectID="20994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 105.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20985"/>
     <cge:Term_Ref ObjectID="29215"/>
    <cge:TPSR_Ref TObjectID="20985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 105.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20985"/>
     <cge:Term_Ref ObjectID="29215"/>
    <cge:TPSR_Ref TObjectID="20985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 105.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20985"/>
     <cge:Term_Ref ObjectID="29215"/>
    <cge:TPSR_Ref TObjectID="20985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107650" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 105.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107650" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21000"/>
     <cge:Term_Ref ObjectID="29245"/>
    <cge:TPSR_Ref TObjectID="21000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107651" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 105.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21000"/>
     <cge:Term_Ref ObjectID="29245"/>
    <cge:TPSR_Ref TObjectID="21000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 105.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21000"/>
     <cge:Term_Ref ObjectID="29245"/>
    <cge:TPSR_Ref TObjectID="21000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-107522" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 484.000000 -943.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-107523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 484.000000 -943.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-107524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 484.000000 -943.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-107543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 484.000000 -943.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-107526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 484.000000 -943.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-107529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 172.000000 -438.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-107530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 172.000000 -438.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-107531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 172.000000 -438.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-108084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 172.000000 -438.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-107533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 172.000000 -438.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-107571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 -551.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21009"/>
     <cge:Term_Ref ObjectID="29266"/>
    <cge:TPSR_Ref TObjectID="21009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-107570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 -551.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21009"/>
     <cge:Term_Ref ObjectID="29266"/>
    <cge:TPSR_Ref TObjectID="21009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-244008" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.000000 105.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244008" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41024"/>
     <cge:Term_Ref ObjectID="62155"/>
    <cge:TPSR_Ref TObjectID="41024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-244009" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.000000 105.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41024"/>
     <cge:Term_Ref ObjectID="62155"/>
    <cge:TPSR_Ref TObjectID="41024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-244004" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.000000 105.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244004" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41024"/>
     <cge:Term_Ref ObjectID="62155"/>
    <cge:TPSR_Ref TObjectID="41024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-244013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1907.000000 105.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41025"/>
     <cge:Term_Ref ObjectID="62157"/>
    <cge:TPSR_Ref TObjectID="41025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-244014" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1907.000000 105.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41025"/>
     <cge:Term_Ref ObjectID="62157"/>
    <cge:TPSR_Ref TObjectID="41025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-244010" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1907.000000 105.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41025"/>
     <cge:Term_Ref ObjectID="62157"/>
    <cge:TPSR_Ref TObjectID="41025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-233180" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -453.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233180" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38886"/>
     <cge:Term_Ref ObjectID="58340"/>
    <cge:TPSR_Ref TObjectID="38886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-233181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -453.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38886"/>
     <cge:Term_Ref ObjectID="58340"/>
    <cge:TPSR_Ref TObjectID="38886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-233177" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -453.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38886"/>
     <cge:Term_Ref ObjectID="58340"/>
    <cge:TPSR_Ref TObjectID="38886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-107537" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 -433.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-107538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 -433.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-107539" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 -433.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-107597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 -433.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-107540" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 -433.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1581.000000 -677.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42818"/>
     <cge:Term_Ref ObjectID="18690"/>
    <cge:TPSR_Ref TObjectID="42818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1581.000000 -677.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42818"/>
     <cge:Term_Ref ObjectID="18690"/>
    <cge:TPSR_Ref TObjectID="42818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1581.000000 -677.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42818"/>
     <cge:Term_Ref ObjectID="18690"/>
    <cge:TPSR_Ref TObjectID="42818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -475.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42819"/>
     <cge:Term_Ref ObjectID="18692"/>
    <cge:TPSR_Ref TObjectID="42819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -475.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42819"/>
     <cge:Term_Ref ObjectID="18692"/>
    <cge:TPSR_Ref TObjectID="42819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -475.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42819"/>
     <cge:Term_Ref ObjectID="18692"/>
    <cge:TPSR_Ref TObjectID="42819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-107581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -562.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42816"/>
     <cge:Term_Ref ObjectID="18689"/>
    <cge:TPSR_Ref TObjectID="42816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-107582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -562.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42816"/>
     <cge:Term_Ref ObjectID="18689"/>
    <cge:TPSR_Ref TObjectID="42816"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="-150" y="-1060"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="-150" y="-1060"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-198" y="-1077"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="478" y="-240"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="478" y="-240"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="649" y="-239"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="649" y="-239"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1029" y="-242"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1029" y="-242"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1330" y="-242"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1330" y="-242"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1482" y="-242"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1482" y="-242"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="314" y="-241"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="314" y="-241"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="743" y="-931"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="743" y="-931"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1517" y="-939"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1517" y="-939"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="804" y="-243"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="804" y="-243"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="15" y="-1048"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="15" y="-1048"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="15" y="-1083"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="15" y="-1083"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="312" y="-437"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="312" y="-437"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="29" qtmmishow="hidden" width="67" x="736" y="-612"/>
    </a>
   <metadata/><rect fill="white" height="29" opacity="0" stroke="white" transform="" width="67" x="736" y="-612"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="87,-934 84,-937 84,-883 87,-886 87,-934" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="87,-934 84,-937 148,-937 145,-934 87,-934" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="87,-886 84,-883 148,-883 145,-886 87,-886" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="145,-934 148,-937 148,-883 145,-886 145,-934" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="48" stroke="rgb(255,255,255)" width="58" x="87" y="-934"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(0,0,0)" width="58" x="87" y="-934"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1912" y="-242"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1912" y="-242"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1669" y="-242"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1669" y="-242"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="80" x="-137" y="-676"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="80" x="-137" y="-676"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="69" x="1533" y="-603"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="69" x="1533" y="-603"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="-150" y="-1060"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/></g>
   <g href="35kV大庄变10kV洒利黑线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="478" y="-240"/></g>
   <g href="35kV大庄变10kV水泥厂线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="649" y="-239"/></g>
   <g href="35kV大庄变10kV干海资线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1029" y="-242"/></g>
   <g href="35kV大庄变10kV桃园线066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1330" y="-242"/></g>
   <g href="35kV大庄变10kV城街线067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1482" y="-242"/></g>
   <g href="35kV大庄变10kV1号电容器组061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="314" y="-241"/></g>
   <g href="35kV大庄变35kV桐大线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="743" y="-931"/></g>
   <g href="35kV大庄变35kV双妥大线362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1517" y="-939"/></g>
   <g href="35kV大庄变10kV马街子线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="804" y="-243"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="15" y="-1048"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="15" y="-1083"/></g>
   <g href="35kV大庄变10kV1号接地变及消弧线圈068间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="312" y="-437"/></g>
   <g href="35kV大庄变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="29" qtmmishow="hidden" width="67" x="736" y="-612"/></g>
   <g href="AVC大庄站.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(0,0,0)" width="58" x="87" y="-934"/></g>
   <g href="35kV大庄变10kV木章郎Ⅰ回线071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1912" y="-242"/></g>
   <g href="35kV大庄变10kV尹代箐线069间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1669" y="-242"/></g>
   <g href="35kV大庄变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="80" x="-137" y="-676"/></g>
   <g href="35kV大庄变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="69" x="1533" y="-603"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="280" x2="280" y1="-685" y2="-701"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="280" x2="280" y1="-708" y2="-724"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="289" x2="324" y1="-724" y2="-724"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="289" x2="322" y1="-709" y2="-709"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="289" x2="294" y1="-701" y2="-701"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="289" x2="294" y1="-686" y2="-686"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="223" x2="215" y1="-771" y2="-771"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="221" x2="217" y1="-773" y2="-773"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.339777" x1="219" x2="218" y1="-774" y2="-774"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.533936" x1="219" x2="219" y1="-758" y2="-770"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="268" x2="268" y1="-646" y2="-689"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="219" x2="219" y1="-655" y2="-676"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="268" x2="219" y1="-655" y2="-655"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="219" x2="219" y1="-675" y2="-684"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="218" x2="220" y1="-684" y2="-684"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="219" x2="211" y1="-702" y2="-685"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="219" x2="219" y1="-702" y2="-711"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="219" x2="219" y1="-727" y2="-736"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.25" x1="190" x2="190" y1="-600" y2="-597"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.39375" x1="192" x2="192" y1="-602" y2="-595"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.611465" x1="194" x2="194" y1="-593" y2="-604"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="194" x2="203" y1="-599" y2="-599"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="305" x2="209" y1="-598" y2="-598"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1.63636" x1="726" x2="744" y1="-1151" y2="-1133"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1.63636" x1="744" x2="726" y1="-1151" y2="-1133"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1.63636" x1="1500" x2="1518" y1="-1150" y2="-1132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1.63636" x1="1518" x2="1500" y1="-1150" y2="-1132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="268" x2="268" y1="-597" y2="-610"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_343a200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -1094.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d4cbf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -1041.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3562850">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 964.000000 -544.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17da430">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1399.000000 -1102.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35b1830">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1754.000000 -548.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 465.000000 55.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34049" ObjectName="EC-SB_DZ.062Ld"/>
    <cge:TPSR_Ref TObjectID="34049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.000000 52.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34050" ObjectName="EC-SB_DZ.063Ld"/>
    <cge:TPSR_Ref TObjectID="34050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 789.000000 50.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34051" ObjectName="EC-SB_DZ.064Ld"/>
    <cge:TPSR_Ref TObjectID="34051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.000000 51.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34052" ObjectName="EC-SB_DZ.065Ld"/>
    <cge:TPSR_Ref TObjectID="34052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1316.000000 55.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34053" ObjectName="EC-SB_DZ.066Ld"/>
    <cge:TPSR_Ref TObjectID="34053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1468.000000 57.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1655.000000 50.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1898.000000 55.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2be5b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-826 734,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20966@0" ObjectIDZND0="21014@0" Pin0InfoVect0LinkObjId="SW-108240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d9d030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="734,-826 734,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2be5430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-887 734,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21014@1" ObjectIDZND0="21008@x" ObjectIDZND1="21016@x" Pin0InfoVect0LinkObjId="SW-108239_0" Pin0InfoVect1LinkObjId="SW-108242_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="734,-887 734,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bf28f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-897 734,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21014@x" ObjectIDND1="21016@x" ObjectIDZND0="21008@0" Pin0InfoVect0LinkObjId="SW-108239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108240_0" Pin1InfoVect1LinkObjId="SW-108242_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="734,-897 734,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2be5670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="829,-955 829,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2deccd0@0" ObjectIDZND0="21016@1" Pin0InfoVect0LinkObjId="SW-108242_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2deccd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="829,-955 829,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2be61d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="829,-906 829,-897 734,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21016@0" ObjectIDZND0="21014@x" ObjectIDZND1="21008@x" Pin0InfoVect0LinkObjId="SW-108240_0" Pin0InfoVect1LinkObjId="SW-108239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="829,-906 829,-897 734,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d68640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-997 830,-988 735,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21017@0" ObjectIDZND0="21008@1" ObjectIDZND1="21015@x" Pin0InfoVect0LinkObjId="SW-108239_1" Pin0InfoVect1LinkObjId="SW-108241_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108243_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="830,-997 830,-988 735,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c060e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-1046 830,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2be51f0@0" ObjectIDZND0="21017@1" Pin0InfoVect0LinkObjId="SW-108243_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2be51f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="830,-1046 830,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca7d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-937 734,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21008@1" ObjectIDZND0="21017@x" ObjectIDZND1="21015@x" Pin0InfoVect0LinkObjId="SW-108243_0" Pin0InfoVect1LinkObjId="SW-108241_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="734,-937 734,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32ff950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-988 734,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21017@x" ObjectIDND1="21008@1" ObjectIDZND0="21015@0" Pin0InfoVect0LinkObjId="SW-108241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108243_0" Pin1InfoVect1LinkObjId="SW-108239_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="734,-988 734,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176c040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-1043 734,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="21015@1" ObjectIDZND0="21018@x" ObjectIDZND1="g_343a200@0" ObjectIDZND2="g_333c470@0" Pin0InfoVect0LinkObjId="SW-108244_0" Pin0InfoVect1LinkObjId="g_343a200_0" Pin0InfoVect2LinkObjId="g_333c470_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="734,-1043 734,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3383ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-1074 638,-1074 638,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21015@0" ObjectIDND1="g_343a200@0" ObjectIDND2="g_333c470@0" ObjectIDZND0="21018@1" Pin0InfoVect0LinkObjId="SW-108244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108241_0" Pin1InfoVect1LinkObjId="g_343a200_0" Pin1InfoVect2LinkObjId="g_333c470_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="734,-1074 638,-1074 638,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dc60c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-1027 638,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21018@0" ObjectIDZND0="g_17dadb0@0" Pin0InfoVect0LinkObjId="g_17dadb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-1027 638,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3326150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-1099 633,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="21015@x" ObjectIDND1="21018@x" ObjectIDND2="g_333c470@0" ObjectIDZND0="g_343a200@0" Pin0InfoVect0LinkObjId="g_343a200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108241_0" Pin1InfoVect1LinkObjId="SW-108244_0" Pin1InfoVect2LinkObjId="g_333c470_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="734,-1099 633,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32da310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-1074 734,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="21015@0" ObjectIDND1="21018@x" ObjectIDZND0="g_343a200@0" ObjectIDZND1="g_333c470@0" ObjectIDZND2="37768@1" Pin0InfoVect0LinkObjId="g_343a200_0" Pin0InfoVect1LinkObjId="g_333c470_0" Pin0InfoVect2LinkObjId="g_2b55e70_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108241_0" Pin1InfoVect1LinkObjId="SW-108244_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="734,-1074 734,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2be4fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-1117 760,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_343a200@0" ObjectIDND1="21015@x" ObjectIDND2="21018@x" ObjectIDZND0="g_333c470@0" Pin0InfoVect0LinkObjId="g_333c470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_343a200_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="SW-108244_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="734,-1117 760,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-1099 734,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_343a200@0" ObjectIDND1="21015@x" ObjectIDND2="21018@x" ObjectIDZND0="g_333c470@0" ObjectIDZND1="37768@1" Pin0InfoVect0LinkObjId="g_333c470_0" Pin0InfoVect1LinkObjId="g_2b55e70_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_343a200_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="SW-108244_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="734,-1099 734,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b55e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-1117 734,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_333c470@0" ObjectIDND1="g_343a200@0" ObjectIDND2="21015@x" ObjectIDZND0="37768@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_333c470_0" Pin1InfoVect1LinkObjId="g_343a200_0" Pin1InfoVect2LinkObjId="SW-108241_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="734,-1117 734,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae1670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,-899 1198,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20982@1" ObjectIDZND0="g_32f08f0@0" Pin0InfoVect0LinkObjId="g_32f08f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,-899 1198,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3341a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-916 1119,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20977@1" ObjectIDZND0="20981@x" ObjectIDZND1="g_2be7fb0@0" ObjectIDZND2="g_32e9040@0" Pin0InfoVect0LinkObjId="SW-107720_0" Pin0InfoVect1LinkObjId="g_2be7fb0_0" Pin0InfoVect2LinkObjId="g_32e9040_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107716_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-916 1119,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ab700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-938 1035,-938 1035,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20977@x" ObjectIDND1="g_2be7fb0@0" ObjectIDND2="g_32e9040@0" ObjectIDZND0="20981@1" Pin0InfoVect0LinkObjId="SW-107720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107716_0" Pin1InfoVect1LinkObjId="g_2be7fb0_0" Pin1InfoVect2LinkObjId="g_32e9040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-938 1035,-938 1035,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e28880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1035,-895 1035,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20981@0" ObjectIDZND0="g_2e2ab10@0" Pin0InfoVect0LinkObjId="g_2e2ab10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1035,-895 1035,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3318060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-1003 1119,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_32e9040@0" ObjectIDZND0="g_2d4cbf0@0" Pin0InfoVect0LinkObjId="g_2d4cbf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32e9040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-1003 1119,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_355a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-951 1159,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20981@x" ObjectIDND1="20977@x" ObjectIDND2="g_32e9040@0" ObjectIDZND0="g_2be7fb0@0" Pin0InfoVect0LinkObjId="g_2be7fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107720_0" Pin1InfoVect1LinkObjId="SW-107716_0" Pin1InfoVect2LinkObjId="g_32e9040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-951 1159,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_330f6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-938 1119,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20981@x" ObjectIDND1="20977@x" ObjectIDZND0="g_2be7fb0@0" ObjectIDZND1="g_32e9040@0" Pin0InfoVect0LinkObjId="g_2be7fb0_0" Pin0InfoVect1LinkObjId="g_32e9040_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107720_0" Pin1InfoVect1LinkObjId="SW-107716_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-938 1119,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d671a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-951 1119,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20981@x" ObjectIDND1="20977@x" ObjectIDND2="g_2be7fb0@0" ObjectIDZND0="g_32e9040@1" Pin0InfoVect0LinkObjId="g_32e9040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107720_0" Pin1InfoVect1LinkObjId="SW-107716_0" Pin1InfoVect2LinkObjId="g_2be7fb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-951 1119,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33bf450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-611 665,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="21009@0" ObjectIDZND0="20983@0" Pin0InfoVect0LinkObjId="SW-107763_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36ae260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-611 665,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-679 566,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20980@0" ObjectIDZND0="g_33360a0@0" Pin0InfoVect0LinkObjId="g_33360a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="566,-679 566,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2721cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-456 921,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="20976@x" ObjectIDND1="0@x" ObjectIDZND0="g_2ef2d20@0" Pin0InfoVect0LinkObjId="g_2ef2d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107715_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-456 921,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf09c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,-73 426,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c52c20@0" ObjectIDND1="20992@x" ObjectIDZND0="g_3599160@0" Pin0InfoVect0LinkObjId="g_3599160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c52c20_0" Pin1InfoVect1LinkObjId="SW-107835_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-73 426,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e67040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-66 597,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20989@x" ObjectIDND1="g_36a3e70@0" ObjectIDZND0="g_2defa90@0" Pin0InfoVect0LinkObjId="g_2defa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107804_0" Pin1InfoVect1LinkObjId="g_36a3e70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-66 597,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eee9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-95 641,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20989@0" ObjectIDZND0="g_2defa90@0" ObjectIDZND1="g_36a3e70@0" Pin0InfoVect0LinkObjId="g_2defa90_0" Pin0InfoVect1LinkObjId="g_36a3e70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="641,-95 641,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_337b3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1020,-67 976,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="20995@x" ObjectIDND1="34052@x" ObjectIDZND0="g_2ee6370@0" Pin0InfoVect0LinkObjId="g_2ee6370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107866_0" Pin1InfoVect1LinkObjId="EC-SB_DZ.065Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1020,-67 976,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d9e2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1020,-96 1020,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="20995@0" ObjectIDZND0="g_2ee6370@0" ObjectIDZND1="34052@x" Pin0InfoVect0LinkObjId="g_2ee6370_0" Pin0InfoVect1LinkObjId="EC-SB_DZ.065Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1020,-96 1020,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333ec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1020,-67 1020,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2ee6370@0" ObjectIDND1="20995@x" ObjectIDZND0="34052@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ee6370_0" Pin1InfoVect1LinkObjId="SW-107866_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1020,-67 1020,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35788c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-63 1277,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="20986@x" ObjectIDND1="34053@x" ObjectIDZND0="g_3a41800@0" Pin0InfoVect0LinkObjId="g_3a41800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107772_0" Pin1InfoVect1LinkObjId="EC-SB_DZ.066Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-63 1277,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a44500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-92 1321,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="20986@0" ObjectIDZND0="g_3a41800@0" ObjectIDZND1="34053@x" Pin0InfoVect0LinkObjId="g_3a41800_0" Pin0InfoVect1LinkObjId="EC-SB_DZ.066Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-92 1321,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-63 1321,34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3a41800@0" ObjectIDND1="20986@x" ObjectIDZND0="34053@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.066Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a41800_0" Pin1InfoVect1LinkObjId="SW-107772_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-63 1321,34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17cc1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1473,-61 1429,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21001@x" ObjectIDZND0="g_33200e0@0" Pin0InfoVect0LinkObjId="g_33200e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-107928_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1473,-61 1429,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3385320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1473,-61 1473,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_33200e0@0" ObjectIDND1="21001@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33200e0_0" Pin1InfoVect1LinkObjId="SW-107928_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1473,-61 1473,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d8e670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-728 1121,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="21011@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-728 1121,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2de4d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-826 1119,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20966@0" ObjectIDZND0="20977@x" ObjectIDZND1="20982@x" Pin0InfoVect0LinkObjId="SW-107716_0" Pin0InfoVect1LinkObjId="SW-107721_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d9d030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-826 1119,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35624f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-880 1119,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="20977@0" ObjectIDZND0="20982@x" ObjectIDZND1="20966@0" Pin0InfoVect0LinkObjId="SW-107721_0" Pin0InfoVect1LinkObjId="g_2d9d030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107716_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-880 1119,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35745c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-851 1198,-851 1198,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="20977@x" ObjectIDND1="20966@0" ObjectIDZND0="20982@0" Pin0InfoVect0LinkObjId="SW-107721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107716_0" Pin1InfoVect1LinkObjId="g_2d9d030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-851 1198,-851 1198,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17df350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-895 1508,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20971@1" ObjectIDZND0="21007@x" ObjectIDZND1="20973@x" Pin0InfoVect0LinkObjId="SW-107989_0" Pin0InfoVect1LinkObjId="SW-107712_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-895 1508,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2de86e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-905 1508,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20971@x" ObjectIDND1="20973@x" ObjectIDZND0="21007@0" Pin0InfoVect0LinkObjId="SW-107989_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107710_0" Pin1InfoVect1LinkObjId="SW-107712_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-905 1508,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d880f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1603,-963 1603,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ce6b60@0" ObjectIDZND0="20973@1" Pin0InfoVect0LinkObjId="SW-107712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce6b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1603,-963 1603,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332af80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1603,-914 1603,-905 1508,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20973@0" ObjectIDZND0="21007@x" ObjectIDZND1="20971@x" Pin0InfoVect0LinkObjId="SW-107989_0" Pin0InfoVect1LinkObjId="SW-107710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1603,-914 1603,-905 1508,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32ffbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1604,-1005 1604,-996 1509,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20974@0" ObjectIDZND0="21007@x" ObjectIDZND1="20972@x" Pin0InfoVect0LinkObjId="SW-107989_0" Pin0InfoVect1LinkObjId="SW-107711_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1604,-1005 1604,-996 1509,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39a3e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1604,-1054 1604,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b6e450@0" ObjectIDZND0="20974@1" Pin0InfoVect0LinkObjId="SW-107713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b6e450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1604,-1054 1604,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17b5860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-945 1508,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21007@1" ObjectIDZND0="20974@x" ObjectIDZND1="20972@x" Pin0InfoVect0LinkObjId="SW-107713_0" Pin0InfoVect1LinkObjId="SW-107711_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-945 1508,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3682720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-996 1508,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21007@x" ObjectIDND1="20974@x" ObjectIDZND0="20972@0" Pin0InfoVect0LinkObjId="SW-107711_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107989_0" Pin1InfoVect1LinkObjId="SW-107713_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-996 1508,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b9ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-1051 1508,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="20972@1" ObjectIDZND0="20975@x" ObjectIDZND1="g_17da430@0" ObjectIDZND2="g_17da130@0" Pin0InfoVect0LinkObjId="SW-107714_0" Pin0InfoVect1LinkObjId="g_17da430_0" Pin0InfoVect2LinkObjId="g_17da130_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107711_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-1051 1508,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3607880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-1082 1412,-1082 1412,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20972@x" ObjectIDND1="g_17da430@0" ObjectIDND2="g_17da130@0" ObjectIDZND0="20975@1" Pin0InfoVect0LinkObjId="SW-107714_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107711_0" Pin1InfoVect1LinkObjId="g_17da430_0" Pin1InfoVect2LinkObjId="g_17da130_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-1082 1412,-1082 1412,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33899d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1412,-1035 1412,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20975@0" ObjectIDZND0="g_2c55cf0@0" Pin0InfoVect0LinkObjId="g_2c55cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1412,-1035 1412,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3342bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-1107 1407,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDND2="g_17da130@0" ObjectIDZND0="g_17da430@0" Pin0InfoVect0LinkObjId="g_17da430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_17da130_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-1107 1407,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_175a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-1082 1508,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDZND0="g_17da430@0" ObjectIDZND1="g_17da130@0" ObjectIDZND2="34567@1" Pin0InfoVect0LinkObjId="g_17da430_0" Pin0InfoVect1LinkObjId="g_17da130_0" Pin0InfoVect2LinkObjId="g_332a910_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-1082 1508,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b65bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-1125 1534,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDND2="g_17da430@0" ObjectIDZND0="g_17da130@0" Pin0InfoVect0LinkObjId="g_17da130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_17da430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-1125 1534,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3556820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-1107 1508,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDND2="g_17da430@0" ObjectIDZND0="g_17da130@0" ObjectIDZND1="34567@1" Pin0InfoVect0LinkObjId="g_17da130_0" Pin0InfoVect1LinkObjId="g_332a910_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_17da430_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-1107 1508,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332a910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-1125 1508,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDND2="g_17da430@0" ObjectIDZND0="34567@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_17da430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-1125 1508,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9d030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-859 1508,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20971@0" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_36ae450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-859 1508,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d64b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-523 984,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3562850@0" Pin0InfoVect0LinkObjId="g_3562850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-523 984,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36ac900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-456 984,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="20976@x" ObjectIDND1="g_2ef2d20@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107715_0" Pin1InfoVect1LinkObjId="g_2ef2d20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-456 984,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d7e9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-409 984,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="20976@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2ef2d20@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2ef2d20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107715_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="984,-409 984,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e30620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-153 794,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="20998@0" ObjectIDZND0="g_2d69170@0" ObjectIDZND1="g_17de9e0@0" ObjectIDZND2="21012@x" Pin0InfoVect0LinkObjId="g_2d69170_0" Pin0InfoVect1LinkObjId="g_17de9e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107897_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="794,-153 794,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_271cf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="750,-129 794,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="g_2d69170@0" ObjectIDZND0="20998@x" ObjectIDZND1="g_17de9e0@0" ObjectIDZND2="21012@x" Pin0InfoVect0LinkObjId="SW-107897_0" Pin0InfoVect1LinkObjId="g_17de9e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d69170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="750,-129 794,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_182b940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="307,-11 307,8 343,8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="21006@0" Pin0InfoVect0LinkObjId="SW-107981_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-11 307,8 343,8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36992a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="379,8 379,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21006@1" ObjectIDZND0="g_355b140@0" Pin0InfoVect0LinkObjId="g_355b140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="379,8 379,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_338d2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="354,-91 354,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21013@1" ObjectIDZND0="g_33125e0@0" Pin0InfoVect0LinkObjId="g_33125e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="354,-91 354,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a1e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,-73 470,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3599160@0" ObjectIDND1="20992@x" ObjectIDZND0="g_2c52c20@1" Pin0InfoVect0LinkObjId="g_2c52c20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3599160_0" Pin1InfoVect1LinkObjId="SW-107835_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-73 470,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a20e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,-6 470,34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2c52c20@0" ObjectIDZND0="34049@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c52c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-6 470,34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32edbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-66 641,-41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2defa90@0" ObjectIDND1="20989@x" ObjectIDZND0="g_36a3e70@1" Pin0InfoVect0LinkObjId="g_36a3e70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2defa90_0" Pin1InfoVect1LinkObjId="SW-107804_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-66 641,-41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ede40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-2 641,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_36a3e70@0" ObjectIDZND0="34050@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36a3e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-2 641,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3685bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-129 794,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_2d69170@0" ObjectIDND1="20998@x" ObjectIDND2="21012@x" ObjectIDZND0="g_17de9e0@1" Pin0InfoVect0LinkObjId="g_17de9e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d69170_0" Pin1InfoVect1LinkObjId="SW-107897_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="794,-129 794,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b5dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-22 794,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_17de9e0@0" ObjectIDZND0="34051@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17de9e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="794,-22 794,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee5ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-443 305,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38886@1" ObjectIDZND0="38888@0" Pin0InfoVect0LinkObjId="SW-233132_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="305,-443 305,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35580b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-403 305,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38887@1" ObjectIDZND0="38886@0" Pin0InfoVect0LinkObjId="SW-233130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233131_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="305,-403 305,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3382600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-68 1616,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="41026@x" ObjectIDND1="0@x" ObjectIDZND0="g_2e62d40@0" Pin0InfoVect0LinkObjId="g_2e62d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244003_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-68 1616,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3382860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-97 1660,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="41026@0" ObjectIDZND0="g_2e62d40@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2e62d40_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-97 1660,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342e720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-68 1660,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2e62d40@0" ObjectIDND1="41026@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e62d40_0" Pin1InfoVect1LinkObjId="SW-244003_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-68 1660,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3572490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1903,-63 1859,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="41029@x" ObjectIDND1="0@x" ObjectIDZND0="g_187aff0@0" Pin0InfoVect0LinkObjId="g_187aff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244048_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1903,-63 1859,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35726f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1903,-92 1903,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="41029@0" ObjectIDZND0="g_187aff0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_187aff0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244048_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1903,-92 1903,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36839d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1903,-63 1903,34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_187aff0@0" ObjectIDND1="41029@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_187aff0_0" Pin1InfoVect1LinkObjId="SW-244048_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1903,-63 1903,34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b7310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-525 1462,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="42816@0" ObjectIDZND0="42819@1" Pin0InfoVect0LinkObjId="SW-261803_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-525 1462,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf2000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-446 1462,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42819@0" ObjectIDZND0="42822@1" Pin0InfoVect0LinkObjId="SW-261811_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261803_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-446 1462,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bf2260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-612 1462,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="42816@1" ObjectIDZND0="42818@0" Pin0InfoVect0LinkObjId="SW-261802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-612 1462,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_358b140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-701 1462,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42818@1" ObjectIDZND0="42820@x" ObjectIDZND1="42821@x" Pin0InfoVect0LinkObjId="SW-261809_0" Pin0InfoVect1LinkObjId="SW-261810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-701 1462,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2777e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-728 1462,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="42818@x" ObjectIDND1="42821@x" ObjectIDZND0="42820@0" Pin0InfoVect0LinkObjId="SW-261809_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261802_0" Pin1InfoVect1LinkObjId="SW-261810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-728 1462,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2778080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-728 1363,-728 1363,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42820@x" ObjectIDND1="42818@x" ObjectIDZND0="42821@1" Pin0InfoVect0LinkObjId="SW-261810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261809_0" Pin1InfoVect1LinkObjId="SW-261802_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-728 1363,-728 1363,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3681670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1363,-678 1363,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42821@0" ObjectIDZND0="g_2aceae0@0" Pin0InfoVect0LinkObjId="g_2aceae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1363,-678 1363,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32de5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-118 306,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="40826@0" ObjectIDZND0="21005@0" Pin0InfoVect0LinkObjId="SW-107960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-SB_DZ.SB_DZ_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-118 306,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e1b280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="252,-120 252,-137 354,-137 354,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3555db0@0" ObjectIDZND0="21013@0" Pin0InfoVect0LinkObjId="SW-107968_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3555db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="252,-120 252,-137 354,-137 354,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dfc460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1177,-410 1068,-410 1068,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42826@0" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_3326390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261901_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1177,-410 1068,-410 1068,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dfc6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-411 1310,-411 1310,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42826@1" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_36611a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261901_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-411 1310,-411 1310,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b9f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-739 665,-729 566,-729 566,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20983@x" ObjectIDND1="20978@x" ObjectIDZND0="20980@1" Pin0InfoVect0LinkObjId="SW-107719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107763_0" Pin1InfoVect1LinkObjId="SW-107717_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-739 665,-729 566,-729 566,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ba190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-583 305,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_35582e0@0" ObjectIDZND0="g_2d98930@0" Pin0InfoVect0LinkObjId="g_2d98930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35582e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="305,-583 305,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ba3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="351,-559 351,-504 305,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b73a80@0" ObjectIDZND0="g_2d98930@0" ObjectIDZND1="38888@x" Pin0InfoVect0LinkObjId="g_2d98930_0" Pin0InfoVect1LinkObjId="SW-233132_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b73a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="351,-559 351,-504 305,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de5850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-514 305,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2d98930@1" ObjectIDZND0="g_2b73a80@0" ObjectIDZND1="38888@x" Pin0InfoVect0LinkObjId="g_2b73a80_0" Pin0InfoVect1LinkObjId="SW-233132_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d98930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="305,-514 305,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de5ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-504 305,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b73a80@0" ObjectIDND1="g_2d98930@0" ObjectIDZND0="38888@1" Pin0InfoVect0LinkObjId="SW-233132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b73a80_0" Pin1InfoVect1LinkObjId="g_2d98930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="305,-504 305,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a04e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-189 794,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20998@1" ObjectIDZND0="20997@0" Pin0InfoVect0LinkObjId="SW-107896_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107897_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="794,-189 794,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a0740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-246 794,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20997@1" ObjectIDZND0="20999@0" Pin0InfoVect0LinkObjId="SW-107898_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107896_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="794,-246 794,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a09a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,-246 470,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20991@1" ObjectIDZND0="20993@0" Pin0InfoVect0LinkObjId="SW-107836_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-246 470,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3563ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1020,-132 1020,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20995@1" ObjectIDZND0="20994@0" Pin0InfoVect0LinkObjId="SW-107865_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107866_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1020,-132 1020,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3563d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1020,-246 1020,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20994@1" ObjectIDZND0="20996@0" Pin0InfoVect0LinkObjId="SW-107867_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107865_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1020,-246 1020,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3563fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-131 641,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20989@1" ObjectIDZND0="20988@0" Pin0InfoVect0LinkObjId="SW-107803_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-131 641,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3698030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-246 641,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20988@1" ObjectIDZND0="20990@0" Pin0InfoVect0LinkObjId="SW-107805_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107803_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-246 641,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3698290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-128 1321,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20986@1" ObjectIDZND0="20985@0" Pin0InfoVect0LinkObjId="SW-107771_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-128 1321,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36984f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-246 1321,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20985@1" ObjectIDZND0="42827@0" Pin0InfoVect0LinkObjId="SW-107773_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107771_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-246 1321,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e25ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-133 1660,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41026@1" ObjectIDZND0="41024@0" Pin0InfoVect0LinkObjId="SW-244016_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-133 1660,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e26120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-246 1660,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41024@1" ObjectIDZND0="42829@0" Pin0InfoVect0LinkObjId="SW-262184_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244016_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-246 1660,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e26380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1903,-128 1903,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41029@1" ObjectIDZND0="41025@0" Pin0InfoVect0LinkObjId="SW-244045_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244048_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1903,-128 1903,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29dc5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1903,-246 1903,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41025@1" ObjectIDZND0="42830@0" Pin0InfoVect0LinkObjId="SW-262221_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244045_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1903,-246 1903,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29dc830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,-73 470,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3599160@0" ObjectIDND1="g_2c52c20@0" ObjectIDZND0="20992@0" Pin0InfoVect0LinkObjId="SW-107835_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3599160_0" Pin1InfoVect1LinkObjId="g_2c52c20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-73 470,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29dca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,-128 470,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20992@1" ObjectIDZND0="20991@0" Pin0InfoVect0LinkObjId="SW-107834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107835_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-128 470,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3326390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-367 305,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38887@0" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_2dfc460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233131_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="305,-367 305,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c80680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-312 306,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21004@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_2dfc460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107959_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-312 306,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eeae60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="470,-312 470,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20993@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_2dfc460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107836_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-312 470,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-305 641,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20990@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_2dfc460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107805_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-305 641,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_289e3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-395 665,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20979@0" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_2dfc460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-395 665,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a6e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-373 984,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20976@0" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_2dfc460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107715_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-373 984,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a9430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1020,-306 1020,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20996@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_2dfc460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107867_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1020,-306 1020,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a49730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-307 794,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20999@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_2dfc460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107898_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="794,-307 794,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36611a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-302 1321,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42827@1" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_2dfc6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107773_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-302 1321,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3661400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-386 1462,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42822@0" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_2dfc6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261811_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-386 1462,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3425600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-307 1660,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42829@1" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_2dfc6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-262184_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-307 1660,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e09580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1903,-302 1903,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42830@1" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_2dfc6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-262221_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1903,-302 1903,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce57d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1711,-460 1774,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2def0f0@0" ObjectIDZND0="g_3426110@0" ObjectIDZND1="42824@x" Pin0InfoVect0LinkObjId="g_3426110_0" Pin0InfoVect1LinkObjId="SW-261895_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2def0f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1711,-460 1774,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3426660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1774,-550 1774,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_35b1830@0" ObjectIDZND0="g_3426110@0" Pin0InfoVect0LinkObjId="g_3426110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35b1830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1774,-550 1774,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3407f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1774,-485 1774,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3426110@1" ObjectIDZND0="g_2def0f0@0" ObjectIDZND1="42824@x" Pin0InfoVect0LinkObjId="g_2def0f0_0" Pin0InfoVect1LinkObjId="SW-261895_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3426110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1774,-485 1774,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36ae070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-431 665,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20979@1" ObjectIDZND0="20984@0" Pin0InfoVect0LinkObjId="SW-107764_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107718_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-431 665,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36ae260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-473 665,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="20984@1" ObjectIDZND0="21009@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107764_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-473 665,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ae450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-783 665,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20978@1" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_2d9d030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107717_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-783 665,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3310f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-701 665,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20983@1" ObjectIDZND0="20980@x" ObjectIDZND1="20978@x" Pin0InfoVect0LinkObjId="SW-107719_0" Pin0InfoVect1LinkObjId="SW-107717_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107763_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="665,-701 665,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33111f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-739 665,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20980@x" ObjectIDND1="20983@x" ObjectIDZND0="20978@0" Pin0InfoVect0LinkObjId="SW-107717_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107719_0" Pin1InfoVect1LinkObjId="SW-107763_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-739 665,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_336c4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-780 1120,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_2d9d030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-780 1120,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3301f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-780 1462,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42820@1" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_2d9d030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261809_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-780 1462,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dfb760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-276 306,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21004@0" ObjectIDZND0="21003@1" Pin0InfoVect0LinkObjId="SW-107958_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107959_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-276 306,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dfb950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-219 306,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21003@0" ObjectIDZND0="21005@1" Pin0InfoVect0LinkObjId="SW-107960_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107958_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-219 306,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17dd040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-88 867,-129 794,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="21012@1" ObjectIDZND0="g_2d69170@0" ObjectIDZND1="20998@x" ObjectIDZND2="g_17de9e0@0" Pin0InfoVect0LinkObjId="g_2d69170_0" Pin0InfoVect1LinkObjId="SW-107897_0" Pin0InfoVect2LinkObjId="g_17de9e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="867,-88 867,-129 794,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c58fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1774,-377 1774,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42824@0" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_2dfc6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261895_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1774,-377 1774,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c59690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1774,-413 1774,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="42824@1" ObjectIDZND0="g_2def0f0@0" ObjectIDZND1="g_3426110@0" Pin0InfoVect0LinkObjId="g_2def0f0_0" Pin0InfoVect1LinkObjId="g_3426110_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261895_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1774,-413 1774,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33902b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1473,-90 1473,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21001@0" ObjectIDZND0="g_33200e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_33200e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107928_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1473,-90 1473,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33904c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1473,-219 1473,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21000@0" ObjectIDZND0="21001@1" Pin0InfoVect0LinkObjId="SW-107928_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1473,-219 1473,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3390720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1473,-348 1473,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20968@0" ObjectIDZND0="42828@1" Pin0InfoVect0LinkObjId="SW-262107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dfc6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1473,-348 1473,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3390980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1473,-270 1473,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42828@0" ObjectIDZND0="21000@1" Pin0InfoVect0LinkObjId="SW-107927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-262107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1473,-270 1473,-246 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-107506" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 29.000000 -977.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20951" ObjectName="DYN-SB_DZ"/>
     <cge:Meas_Ref ObjectId="107506"/>
    </metadata>
   </g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="289,-693 288,-693 288,-693 287,-693 287,-693 286,-693 286,-692 286,-692 286,-692 285,-691 285,-691 285,-690 285,-690 285,-689 285,-689 285,-688 285,-688 286,-687 286,-687 286,-686 286,-686 287,-686 287,-686 288,-685 288,-685 289,-685 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="289,-701 288,-701 288,-701 287,-701 287,-701 286,-700 286,-700 286,-700 286,-699 285,-699 285,-698 285,-698 285,-697 285,-697 285,-696 285,-696 285,-695 286,-695 286,-695 286,-694 286,-694 287,-694 287,-693 288,-693 288,-693 289,-693 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="289,-717 288,-717 288,-716 287,-716 287,-716 286,-716 286,-715 286,-715 286,-715 285,-714 285,-714 285,-713 285,-713 285,-712 285,-712 285,-711 285,-711 286,-710 286,-710 286,-710 286,-709 287,-709 287,-709 288,-709 288,-708 289,-708 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="289,-724 288,-724 288,-724 287,-724 287,-724 286,-723 286,-723 286,-723 286,-722 285,-722 285,-721 285,-721 285,-720 285,-720 285,-719 285,-719 285,-718 286,-718 286,-718 286,-717 286,-717 287,-717 287,-716 288,-716 288,-716 289,-716 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="219,-747 218,-747 218,-747 217,-747 216,-747 216,-746 215,-746 214,-745 214,-745 214,-744 213,-743 213,-743 213,-742 213,-741 213,-740 213,-740 214,-739 214,-738 214,-738 215,-737 216,-737 216,-736 217,-736 218,-736 218,-736 219,-736 " stroke="rgb(0,255,0)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="219,-758 218,-758 218,-758 217,-758 216,-758 216,-757 215,-757 214,-756 214,-756 214,-755 213,-754 213,-754 213,-753 213,-752 213,-751 213,-751 214,-750 214,-749 214,-749 215,-748 216,-748 216,-747 217,-747 218,-747 218,-747 219,-747 " stroke="rgb(0,255,0)" stroke-width="0.171589"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_DZ" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shuangtuodaTdz" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1508,-1203 1508,-1154 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34567" ObjectName="AC-35kV.LN_shuangtuodaTdz"/>
    <cge:TPSR_Ref TObjectID="34567_SS-160"/></metadata>
   <polyline fill="none" opacity="0" points="1508,-1203 1508,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_DZ" endPointId="0" endStationName="CX_DZ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_tongda_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="734,-1209 734,-1160 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37768" ObjectName="AC-35kV.LN_tongda_line"/>
    <cge:TPSR_Ref TObjectID="37768_SS-160"/></metadata>
   <polyline fill="none" opacity="0" points="734,-1209 734,-1160 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="734" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="1119" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="1310" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="1068" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="305" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="306" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="470" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="641" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="665" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="984" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="1020" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="794" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="1462" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="1660" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="1903" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="665" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="1121" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="1462" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="1774" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="1321" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="1473" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-108239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.000000 -902.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21008" ObjectName="SW-SB_DZ.SB_DZ_361BK"/>
     <cge:Meas_Ref ObjectId="108239"/>
    <cge:TPSR_Ref TObjectID="21008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107763">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 -666.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20983" ObjectName="SW-SB_DZ.SB_DZ_301BK"/>
     <cge:Meas_Ref ObjectId="107763"/>
    <cge:TPSR_Ref TObjectID="20983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 -438.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20984" ObjectName="SW-SB_DZ.SB_DZ_001BK"/>
     <cge:Meas_Ref ObjectId="107764"/>
    <cge:TPSR_Ref TObjectID="20984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20991" ObjectName="SW-SB_DZ.SB_DZ_062BK"/>
     <cge:Meas_Ref ObjectId="107834"/>
    <cge:TPSR_Ref TObjectID="20991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107803">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20988" ObjectName="SW-SB_DZ.SB_DZ_063BK"/>
     <cge:Meas_Ref ObjectId="107803"/>
    <cge:TPSR_Ref TObjectID="20988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107896">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20997" ObjectName="SW-SB_DZ.SB_DZ_064BK"/>
     <cge:Meas_Ref ObjectId="107896"/>
    <cge:TPSR_Ref TObjectID="20997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107865">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1011.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20994" ObjectName="SW-SB_DZ.SB_DZ_065BK"/>
     <cge:Meas_Ref ObjectId="107865"/>
    <cge:TPSR_Ref TObjectID="20994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20985" ObjectName="SW-SB_DZ.SB_DZ_066BK"/>
     <cge:Meas_Ref ObjectId="107771"/>
    <cge:TPSR_Ref TObjectID="20985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107927">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1464.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21000" ObjectName="SW-SB_DZ.SB_DZ_067BK"/>
     <cge:Meas_Ref ObjectId="107927"/>
    <cge:TPSR_Ref TObjectID="21000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107989">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.000000 -910.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21007" ObjectName="SW-SB_DZ.SB_DZ_362BK"/>
     <cge:Meas_Ref ObjectId="107989"/>
    <cge:TPSR_Ref TObjectID="21007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107958">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21003" ObjectName="SW-SB_DZ.SB_DZ_061BK"/>
     <cge:Meas_Ref ObjectId="107958"/>
    <cge:TPSR_Ref TObjectID="21003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233130">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.000000 -408.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38886" ObjectName="SW-SB_DZ.SB_DZ_068BK"/>
     <cge:Meas_Ref ObjectId="233130"/>
    <cge:TPSR_Ref TObjectID="38886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244016">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41024" ObjectName="SW-SB_DZ.SB_DZ_069BK"/>
     <cge:Meas_Ref ObjectId="244016"/>
    <cge:TPSR_Ref TObjectID="41024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244045">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1894.000000 -211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41025" ObjectName="SW-SB_DZ.SB_DZ_071BK"/>
     <cge:Meas_Ref ObjectId="244045"/>
    <cge:TPSR_Ref TObjectID="41025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261802">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1453.000000 -666.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42818" ObjectName="SW-SB_DZ.SB_DZ_302BK"/>
     <cge:Meas_Ref ObjectId="261802"/>
    <cge:TPSR_Ref TObjectID="42818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261803">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1453.000000 -438.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42819" ObjectName="SW-SB_DZ.SB_DZ_002BK"/>
     <cge:Meas_Ref ObjectId="261803"/>
    <cge:TPSR_Ref TObjectID="42819"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_182e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_182e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_182e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_182e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_182e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_182e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_182e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_182e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_182e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33357c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_33b37d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -1049.500000) translate(0,16)">大庄变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2be7da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 -1241.000000) translate(0,18)">桐大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a14390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 -849.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d99100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -317.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2ca2530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 -1117.000000) translate(0,18)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_32e9680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 -640.000000) translate(0,18)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_32e9680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 -640.000000) translate(0,40)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3340760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1444.000000 68.000000) translate(0,18)">城街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_32e9960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 -1249.000000) translate(0,18)">双妥大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3581620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1517.000000 -939.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32f0330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1515.000000 -1040.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3551ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1515.000000 -884.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c8ae00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1610.000000 -939.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee7580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -1030.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3588070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1419.000000 -1060.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3412a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -888.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cdfd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1126.000000 -905.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e41680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -920.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e418c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -690.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3327680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -765.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3327890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -712.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3693d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -476.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3693f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -438.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_367ddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -115.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_367e010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -289.000000) translate(0,12)">0672</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3334540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1328.000000 -291.000000) translate(0,12)">0662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3334740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1328.000000 -117.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0ba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 -121.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0bc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 -295.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e01260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 801.000000 -296.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35af850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 801.000000 -178.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e19440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -294.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3350be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -120.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3350e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 479.000000 -240.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18421a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -301.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18423e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -127.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271d100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -398.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb3550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 743.000000 -931.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c56ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 741.000000 -1032.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c57230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -1052.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2de5f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 840.000000 -1022.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2de61c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -929.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33696f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 741.000000 -876.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_184ae80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -136.000000 -113.000000) translate(0,17)">7811107</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_338d520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -272.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be6760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 360.000000 -112.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3346bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 348.000000 28.000000) translate(0,12)">06100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3346df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -179.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33be960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -240.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c8a120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1029.000000 -240.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_367b5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -242.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_367b7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -242.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cea6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 315.000000 -240.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3348e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.000000 -240.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35890b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -638.000000) translate(0,15)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35890b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -638.000000) translate(0,33)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35890b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -638.000000) translate(0,51)">35000/10500V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35890b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -638.000000) translate(0,69)">标准档电流82.5/274.9</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35890b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -638.000000) translate(0,87)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35890b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -638.000000) translate(0,105)">Ud%=7.55%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e68eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1040.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2ad1530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1075.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3552660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -802.000000) translate(0,15)">10kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3552660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -802.000000) translate(0,33)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3570cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 314.000000 -436.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a2ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -483.000000) translate(0,12)">0686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2719780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -390.000000) translate(0,12)">0681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2c400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 280.000000 -636.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2d6add0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 70.000000) translate(0,18)">洒利黑线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2bf3400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 68.000000) translate(0,18)">水泥厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_357cd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 748.000000 62.000000) translate(0,18)">马街子线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1757520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 972.000000 66.000000) translate(0,18)">干海资线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2df5390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 71.000000) translate(0,18)">桃园线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3699b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -265.000000 -75.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3699b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -265.000000 -75.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2cf2030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -85.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2cf2030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -85.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2cf2030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -85.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_33bc080" transform="matrix(0.950820 -0.000000 -0.000000 0.827586 95.557377 -917.448276) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_36a0020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1617.000000 68.000000) translate(0,18)">尹代箐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3579fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1860.000000 73.000000) translate(0,18)">木章郎Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335add0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1669.000000 -242.000000) translate(0,12)">069</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3312fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1667.000000 -296.000000) translate(0,12)">0692</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2730930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1667.000000 -122.000000) translate(0,12)">0696</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2730b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1912.000000 -242.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ded4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1910.000000 -291.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ded710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1910.000000 -117.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36818d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 -682.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3586a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 -764.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3586c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1370.000000 -703.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3586e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 -467.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb4450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 -411.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3341f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -634.000000) translate(0,15)">2号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3341f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -634.000000) translate(0,33)">SZ11-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3341f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -634.000000) translate(0,51)">35±3×2.5kV/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3341f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -634.000000) translate(0,69)">高压额定电流：131.97A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3341f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -634.000000) translate(0,87)">低压额定电流：439.89A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3341f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -634.000000) translate(0,105)">联接组方式：YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3341f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -634.000000) translate(0,123)">冷却方式：0NAN</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3341f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -634.000000) translate(0,141)">Ud%=7.4%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2e2cd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1718.000000 -637.000000) translate(0,18)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2e2cd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1718.000000 -637.000000) translate(0,40)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a7d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1781.000000 -402.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1951.000000 -330.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_3381720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 738.000000 -603.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3408190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -1136.000000) translate(0,18)">A,B相</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_335a260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1327.000000 -1146.000000) translate(0,18)">A,B相</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_335a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 211.000000 69.000000) translate(0,18)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_35738b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1533.000000 -603.000000) translate(0,16)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2bf4460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1074.000000 -593.000000) translate(0,16)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3302650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 828.000000 -2.000000) translate(0,16)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dfbb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1180.000000 -436.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17dd230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -676.000000) translate(0,16)">公共信号</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_17dadb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -990.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2deccd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 -950.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be51f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.000000 -1041.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32f08f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1192.000000 -909.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e2ab10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.000000 -864.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33360a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 -640.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c55cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.000000 -998.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce6b60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1597.000000 -958.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b6e450" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1598.000000 -1049.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33125e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 348.000000 -81.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_355b140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 373.000000 -25.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aceae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 -639.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="SB_DZ"/>
</svg>