<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-191" aopId="3940870" id="thSvg" product="E8000V2" version="1.0" viewBox="1351 -1484 2201 1197">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="capacitor:shape13">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="23" y2="33"/>
   </symbol>
   <symbol id="capacitor:shape12">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="5" x2="15" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="15" x2="15" y1="20" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="24" x2="34" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="24" x2="24" y1="20" y2="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape197">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="14,14 20,27 7,27 14,14 14,15 14,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="42" y2="0"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="55" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape56_0">
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,43 41,43 41,72 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="57" y2="99"/>
    <polyline DF8003:Layer="PUBLIC" points="16,84 22,71 9,71 16,84 16,83 16,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="72" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="39" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="54" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="43" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="43" y2="38"/>
   </symbol>
   <symbol id="transformer2:shape56_1">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="14" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="16" y1="20" y2="14"/>
   </symbol>
   <symbol id="voltageTransformer:shape80">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="voltageTransformer:shape138">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="43" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="41" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="39" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="40" y1="45" y2="45"/>
    <ellipse cx="8" cy="13" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="25" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="16" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="9" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="9" y1="12" y2="10"/>
    <ellipse cx="21" cy="13" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="8" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="8" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="27" y2="24"/>
    <ellipse cx="21" cy="25" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="42" y1="21" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="42" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="21" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="14" y2="20"/>
    <rect height="13" stroke-width="1" width="5" x="35" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="14" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="27" y2="24"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_302c610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_302d020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_302d9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_302e660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_302f8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30304e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3030f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30319c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3032290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3032b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3032b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3034420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3034420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3035440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3036e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3037af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30389d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30392b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_303aa70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_303b270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_303b960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_303c380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_303d560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_303dee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_303ea00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_303f3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30408a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3041440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30426b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3043340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3051b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30449f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_30456b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3046bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1207" width="2211" x="1346" y="-1489"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2576" x2="2576" y1="-1046" y2="-1046"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="3543" x2="3552" y1="-784" y2="-784"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-228622">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -585.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37960" ObjectName="SW-YR_YJ.YR_YJ_051BK"/>
     <cge:Meas_Ref ObjectId="228622"/>
    <cge:TPSR_Ref TObjectID="37960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138240">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.200000 -800.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24943" ObjectName="SW-YR_YJ.YR_YJ_002BK"/>
     <cge:Meas_Ref ObjectId="138240"/>
    <cge:TPSR_Ref TObjectID="24943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138502">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2556.000000 -806.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24907" ObjectName="SW-YR_YJ.YR_YJ_012BK"/>
     <cge:Meas_Ref ObjectId="138502"/>
    <cge:TPSR_Ref TObjectID="24907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2264.200000 -799.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24887" ObjectName="SW-YR_YJ.YR_YJ_001BK"/>
     <cge:Meas_Ref ObjectId="138165"/>
    <cge:TPSR_Ref TObjectID="24887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138355">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2439.000000 -583.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24895" ObjectName="SW-YR_YJ.YR_YJ_053BK"/>
     <cge:Meas_Ref ObjectId="138355"/>
    <cge:TPSR_Ref TObjectID="24895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138403">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2699.000000 -585.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24899" ObjectName="SW-YR_YJ.YR_YJ_054BK"/>
     <cge:Meas_Ref ObjectId="138403"/>
    <cge:TPSR_Ref TObjectID="24899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 -585.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24903" ObjectName="SW-YR_YJ.YR_YJ_055BK"/>
     <cge:Meas_Ref ObjectId="138451"/>
    <cge:TPSR_Ref TObjectID="24903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229086">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3181.000000 -585.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38143" ObjectName="SW-YR_YJ.YR_YJ_056BK"/>
     <cge:Meas_Ref ObjectId="229086"/>
    <cge:TPSR_Ref TObjectID="38143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2221.000000 -586.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24891" ObjectName="SW-YR_YJ.YR_YJ_052BK"/>
     <cge:Meas_Ref ObjectId="138307"/>
    <cge:TPSR_Ref TObjectID="24891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2984.000000 -1170.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138159">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2265.000000 -1064.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24885" ObjectName="SW-YR_YJ.YR_YJ_301BK"/>
     <cge:Meas_Ref ObjectId="138159"/>
    <cge:TPSR_Ref TObjectID="24885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138234">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.000000 -1066.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24942" ObjectName="SW-YR_YJ.YR_YJ_302BK"/>
     <cge:Meas_Ref ObjectId="138234"/>
    <cge:TPSR_Ref TObjectID="24942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183092">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2568.000000 -1198.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27850" ObjectName="SW-YR_YJ.YR_YJ_353BK"/>
     <cge:Meas_Ref ObjectId="183092"/>
    <cge:TPSR_Ref TObjectID="27850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183002">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2184.000000 -1208.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27848" ObjectName="SW-YR_YJ.YR_YJ_351BK"/>
     <cge:Meas_Ref ObjectId="183002"/>
    <cge:TPSR_Ref TObjectID="27848"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_250bdf0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3038.000000 -1376.500000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25108d0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2237.000000 -1378.500000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a7730">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2553.000000 -935.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_243a130">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3298.000000 -947.000000)" xlink:href="#voltageTransformer:shape138"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24455e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1930.000000 -937.000000)" xlink:href="#voltageTransformer:shape138"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="YR_YJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_lianyiTyj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2193,-1402 2193,-1443 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37844" ObjectName="AC-35kV.LN_lianyiTyj"/>
    <cge:TPSR_Ref TObjectID="37844_SS-191"/></metadata>
   <polyline fill="none" opacity="0" points="2193,-1402 2193,-1443 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YR_YJ.YR_YJ_051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -370.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34008" ObjectName="EC-YR_YJ.YR_YJ_051Ld"/>
    <cge:TPSR_Ref TObjectID="34008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_YJ.YR_YJ_053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2439.000000 -372.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34007" ObjectName="EC-YR_YJ.YR_YJ_053Ld"/>
    <cge:TPSR_Ref TObjectID="34007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_YJ.YR_YJ_054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2699.000000 -370.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38688" ObjectName="EC-YR_YJ.YR_YJ_054Ld"/>
    <cge:TPSR_Ref TObjectID="38688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_YJ.YR_YJ_055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 -370.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34009" ObjectName="EC-YR_YJ.YR_YJ_055Ld"/>
    <cge:TPSR_Ref TObjectID="34009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_YJ.YR_YJ_056Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3181.000000 -370.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38687" ObjectName="EC-YR_YJ.YR_YJ_056Ld"/>
    <cge:TPSR_Ref TObjectID="38687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_YJ.YR_YJ_052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2221.000000 -371.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34006" ObjectName="EC-YR_YJ.YR_YJ_052Ld"/>
    <cge:TPSR_Ref TObjectID="34006"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_24c1a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2042.000000 -809.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b6220" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3410.000000 -812.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25173e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1952.000000 -388.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b6f00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2378.000000 -386.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_252be60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2638.000000 -388.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24f6fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2877.000000 -388.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2512bc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3120.000000 -388.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ba430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2160.000000 -389.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_251af30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2875.000000 -957.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2489b00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3076.000000 -1284.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_250ec10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2275.000000 -1286.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24998f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2650.000000 -1277.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_254c440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2236.000000 -959.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a4820" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2492.000000 -1277.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261d170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2539.000000 -989.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262aa00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2910.000000 -1284.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_243c910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3281.000000 -835.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24481e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1913.000000 -825.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_24bb8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-808 2912,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24943@0" ObjectIDZND0="24890@1" Pin0InfoVect0LinkObjId="SW-138243_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-808 2912,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24bba90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-746 2912,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24890@0" ObjectIDZND0="24880@0" Pin0InfoVect0LinkObjId="g_253c580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138243_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-746 2912,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24bc390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-692 2022,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37961@1" ObjectIDZND0="24879@0" Pin0InfoVect0LinkObjId="g_253c390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228623_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-692 2022,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24bc580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-593 2022,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37960@0" ObjectIDZND0="37962@1" Pin0InfoVect0LinkObjId="SW-228624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228622_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-593 2022,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2556ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-864 2912,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="24916@0" ObjectIDZND0="24943@1" Pin0InfoVect0LinkObjId="SW-138240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2439ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-864 2912,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253c390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2538,-754 2538,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24908@0" ObjectIDZND0="24879@0" Pin0InfoVect0LinkObjId="g_24bc390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138513_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2538,-754 2538,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253c580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2617,-756 2617,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24909@0" ObjectIDZND0="24880@0" Pin0InfoVect0LinkObjId="g_24bba90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2617,-756 2617,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253c770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2617,-792 2617,-816 2592,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24909@1" ObjectIDZND0="24907@0" Pin0InfoVect0LinkObjId="SW-138502_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2617,-792 2617,-816 2592,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253c960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2565,-816 2538,-816 2538,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24907@1" ObjectIDZND0="24908@1" Pin0InfoVect0LinkObjId="SW-138513_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138502_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2565,-816 2538,-816 2538,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259a500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1951,-749 1951,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24910@0" ObjectIDZND0="24879@0" Pin0InfoVect0LinkObjId="g_24bc390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138561_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1951,-749 1951,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_254fe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-745 2273,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24888@0" ObjectIDZND0="24879@0" Pin0InfoVect0LinkObjId="g_24bc390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-745 2273,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2550060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-863 2273,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="24915@1" ObjectIDZND0="24887@1" Pin0InfoVect0LinkObjId="SW-138165_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262d0b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-863 2273,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24c1880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-807 2273,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24887@0" ObjectIDZND0="24888@1" Pin0InfoVect0LinkObjId="SW-138168_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138165_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-807 2273,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259e090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2046,-815 2028,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24c1a70@0" ObjectIDZND0="24913@0" Pin0InfoVect0LinkObjId="SW-138572_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24c1a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2046,-815 2028,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259e280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-815 1951,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24913@1" ObjectIDZND0="24910@x" ObjectIDZND1="g_244a7e0@0" Pin0InfoVect0LinkObjId="SW-138561_0" Pin0InfoVect1LinkObjId="g_244a7e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138572_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-815 1951,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259eb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1951,-785 1951,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24910@1" ObjectIDZND0="24913@x" ObjectIDZND1="g_244a7e0@0" Pin0InfoVect0LinkObjId="SW-138572_0" Pin0InfoVect1LinkObjId="g_244a7e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138561_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1951,-785 1951,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259ed50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1951,-815 1951,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24913@x" ObjectIDND1="24910@x" ObjectIDZND0="g_244a7e0@1" Pin0InfoVect0LinkObjId="g_244a7e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138572_0" Pin1InfoVect1LinkObjId="SW-138561_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1951,-815 1951,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b6030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3319,-752 3319,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24911@0" ObjectIDZND0="24880@0" Pin0InfoVect0LinkObjId="g_24bba90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3319,-752 3319,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2552210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3414,-818 3396,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25b6220@0" ObjectIDZND0="24912@0" Pin0InfoVect0LinkObjId="SW-138567_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b6220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3414,-818 3396,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2552430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3360,-818 3319,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24912@1" ObjectIDZND0="24911@x" ObjectIDZND1="g_243ef10@0" Pin0InfoVect0LinkObjId="SW-138562_0" Pin0InfoVect1LinkObjId="g_243ef10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138567_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3360,-818 3319,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2515ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3319,-788 3319,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24911@1" ObjectIDZND0="24912@x" ObjectIDZND1="g_243ef10@0" Pin0InfoVect0LinkObjId="SW-138567_0" Pin0InfoVect1LinkObjId="g_243ef10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3319,-788 3319,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25165a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-656 2022,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37961@0" ObjectIDZND0="37960@1" Pin0InfoVect0LinkObjId="SW-228622_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228623_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-656 2022,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25167c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2085,-457 2022,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24bc770@0" ObjectIDZND0="34008@x" ObjectIDZND1="37963@x" ObjectIDZND2="37962@x" Pin0InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_051Ld_0" Pin0InfoVect1LinkObjId="SW-228625_0" Pin0InfoVect2LinkObjId="SW-228624_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24bc770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2085,-457 2022,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25171c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-397 2022,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34008@0" ObjectIDZND0="g_24bc770@0" ObjectIDZND1="37963@x" ObjectIDZND2="37962@x" Pin0InfoVect0LinkObjId="g_24bc770_0" Pin0InfoVect1LinkObjId="SW-228625_0" Pin0InfoVect2LinkObjId="SW-228624_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_051Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-397 2022,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2543980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1958,-406 1958,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25173e0@0" ObjectIDZND0="37963@0" Pin0InfoVect0LinkObjId="SW-228625_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25173e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1958,-406 1958,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2543ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-491 1958,-491 1958,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_24bc770@0" ObjectIDND1="34008@x" ObjectIDND2="37962@x" ObjectIDZND0="37963@1" Pin0InfoVect0LinkObjId="SW-228625_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24bc770_0" Pin1InfoVect1LinkObjId="EC-YR_YJ.YR_YJ_051Ld_0" Pin1InfoVect2LinkObjId="SW-228624_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-491 1958,-491 1958,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255cd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-457 2022,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_24bc770@0" ObjectIDND1="34008@x" ObjectIDZND0="37963@x" ObjectIDZND1="37962@x" Pin0InfoVect0LinkObjId="SW-228625_0" Pin0InfoVect1LinkObjId="SW-228624_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24bc770_0" Pin1InfoVect1LinkObjId="EC-YR_YJ.YR_YJ_051Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-457 2022,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255cf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-491 2022,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="37963@x" ObjectIDND1="g_24bc770@0" ObjectIDND2="34008@x" ObjectIDZND0="37962@0" Pin0InfoVect0LinkObjId="SW-228624_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-228625_0" Pin1InfoVect1LinkObjId="g_24bc770_0" Pin1InfoVect2LinkObjId="EC-YR_YJ.YR_YJ_051Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-491 2022,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b52b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2448,-690 2448,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24896@1" ObjectIDZND0="24879@0" Pin0InfoVect0LinkObjId="g_24bc390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138358_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2448,-690 2448,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b54d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2448,-591 2448,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24895@0" ObjectIDZND0="24897@1" Pin0InfoVect0LinkObjId="SW-138359_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2448,-591 2448,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b68a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2448,-654 2448,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24896@0" ObjectIDZND0="24895@1" Pin0InfoVect0LinkObjId="SW-138355_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138358_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2448,-654 2448,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b6ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2511,-455 2448,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24b56f0@0" ObjectIDZND0="34007@x" ObjectIDZND1="24898@x" ObjectIDZND2="24897@x" Pin0InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_053Ld_0" Pin0InfoVect1LinkObjId="SW-138360_0" Pin0InfoVect2LinkObjId="SW-138359_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24b56f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2511,-455 2448,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b6ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2448,-399 2448,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34007@0" ObjectIDZND0="g_24b56f0@0" ObjectIDZND1="24898@x" ObjectIDZND2="24897@x" Pin0InfoVect0LinkObjId="g_24b56f0_0" Pin0InfoVect1LinkObjId="SW-138360_0" Pin0InfoVect2LinkObjId="SW-138359_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_053Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2448,-399 2448,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2558ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2384,-404 2384,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24b6f00@0" ObjectIDZND0="24898@0" Pin0InfoVect0LinkObjId="SW-138360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24b6f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2384,-404 2384,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2559210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2448,-489 2384,-489 2384,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34007@x" ObjectIDND1="g_24b56f0@0" ObjectIDND2="24897@x" ObjectIDZND0="24898@1" Pin0InfoVect0LinkObjId="SW-138360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_053Ld_0" Pin1InfoVect1LinkObjId="g_24b56f0_0" Pin1InfoVect2LinkObjId="SW-138359_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2448,-489 2384,-489 2384,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257c7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2448,-455 2448,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="34007@x" ObjectIDND1="g_24b56f0@0" ObjectIDZND0="24898@x" ObjectIDZND1="24897@x" Pin0InfoVect0LinkObjId="SW-138360_0" Pin0InfoVect1LinkObjId="SW-138359_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_053Ld_0" Pin1InfoVect1LinkObjId="g_24b56f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2448,-455 2448,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257ca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2448,-489 2448,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34007@x" ObjectIDND1="g_24b56f0@0" ObjectIDND2="24898@x" ObjectIDZND0="24897@0" Pin0InfoVect0LinkObjId="SW-138359_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_053Ld_0" Pin1InfoVect1LinkObjId="g_24b56f0_0" Pin1InfoVect2LinkObjId="SW-138360_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2448,-489 2448,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2529ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2708,-692 2708,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24900@1" ObjectIDZND0="24880@0" Pin0InfoVect0LinkObjId="g_24bba90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2708,-692 2708,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_252a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2708,-593 2708,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24899@0" ObjectIDZND0="24901@1" Pin0InfoVect0LinkObjId="SW-138407_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2708,-593 2708,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_252b740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2708,-656 2708,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24900@0" ObjectIDZND0="24899@1" Pin0InfoVect0LinkObjId="SW-138403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2708,-656 2708,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_252b9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2771,-457 2708,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_252a3b0@0" ObjectIDZND0="38688@x" ObjectIDZND1="24902@x" ObjectIDZND2="24901@x" Pin0InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_054Ld_0" Pin0InfoVect1LinkObjId="SW-138408_0" Pin0InfoVect2LinkObjId="SW-138407_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_252a3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2771,-457 2708,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_252bc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2708,-397 2708,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38688@0" ObjectIDZND0="g_252a3b0@0" ObjectIDZND1="24902@x" ObjectIDZND2="24901@x" Pin0InfoVect0LinkObjId="g_252a3b0_0" Pin0InfoVect1LinkObjId="SW-138408_0" Pin0InfoVect2LinkObjId="SW-138407_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_054Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2708,-397 2708,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d6f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2644,-406 2644,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_252be60@0" ObjectIDZND0="24902@0" Pin0InfoVect0LinkObjId="SW-138408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_252be60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2644,-406 2644,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d71a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2708,-491 2644,-491 2644,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="38688@x" ObjectIDND1="g_252a3b0@0" ObjectIDND2="24901@x" ObjectIDZND0="24902@1" Pin0InfoVect0LinkObjId="SW-138408_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_054Ld_0" Pin1InfoVect1LinkObjId="g_252a3b0_0" Pin1InfoVect2LinkObjId="SW-138407_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2708,-491 2644,-491 2644,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d7400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2708,-457 2708,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38688@x" ObjectIDND1="g_252a3b0@0" ObjectIDZND0="24902@x" ObjectIDZND1="24901@x" Pin0InfoVect0LinkObjId="SW-138408_0" Pin0InfoVect1LinkObjId="SW-138407_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_054Ld_0" Pin1InfoVect1LinkObjId="g_252a3b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2708,-457 2708,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25595a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2708,-491 2708,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="38688@x" ObjectIDND1="g_252a3b0@0" ObjectIDND2="24902@x" ObjectIDZND0="24901@0" Pin0InfoVect0LinkObjId="SW-138407_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_054Ld_0" Pin1InfoVect1LinkObjId="g_252a3b0_0" Pin1InfoVect2LinkObjId="SW-138408_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2708,-491 2708,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24bf6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-692 2947,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24904@1" ObjectIDZND0="24880@0" Pin0InfoVect0LinkObjId="g_24bba90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-692 2947,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24bf920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-593 2947,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24903@0" ObjectIDZND0="24905@1" Pin0InfoVect0LinkObjId="SW-138455_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-593 2947,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f6890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-656 2947,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24904@0" ObjectIDZND0="24903@1" Pin0InfoVect0LinkObjId="SW-138451_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138454_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-656 2947,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f6af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3010,-457 2947,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24bfb80@0" ObjectIDZND0="34009@x" ObjectIDZND1="24906@x" ObjectIDZND2="24905@x" Pin0InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_055Ld_0" Pin0InfoVect1LinkObjId="SW-138456_0" Pin0InfoVect2LinkObjId="SW-138455_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24bfb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3010,-457 2947,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f6d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-397 2947,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34009@0" ObjectIDZND0="g_24bfb80@0" ObjectIDZND1="24906@x" ObjectIDZND2="24905@x" Pin0InfoVect0LinkObjId="g_24bfb80_0" Pin0InfoVect1LinkObjId="SW-138456_0" Pin0InfoVect2LinkObjId="SW-138455_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_055Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-397 2947,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253ed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2883,-406 2883,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24f6fb0@0" ObjectIDZND0="24906@0" Pin0InfoVect0LinkObjId="SW-138456_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24f6fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2883,-406 2883,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-491 2883,-491 2883,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34009@x" ObjectIDND1="g_24bfb80@0" ObjectIDND2="24905@x" ObjectIDZND0="24906@1" Pin0InfoVect0LinkObjId="SW-138456_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_055Ld_0" Pin1InfoVect1LinkObjId="g_24bfb80_0" Pin1InfoVect2LinkObjId="SW-138455_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-491 2883,-491 2883,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253f250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-457 2947,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="34009@x" ObjectIDND1="g_24bfb80@0" ObjectIDZND0="24906@x" ObjectIDZND1="24905@x" Pin0InfoVect0LinkObjId="SW-138456_0" Pin0InfoVect1LinkObjId="SW-138455_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_055Ld_0" Pin1InfoVect1LinkObjId="g_24bfb80_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-457 2947,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253f4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-491 2947,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34009@x" ObjectIDND1="g_24bfb80@0" ObjectIDND2="24906@x" ObjectIDZND0="24905@0" Pin0InfoVect0LinkObjId="SW-138455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_055Ld_0" Pin1InfoVect1LinkObjId="g_24bfb80_0" Pin1InfoVect2LinkObjId="SW-138456_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-491 2947,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e74b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3190,-593 3190,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38143@0" ObjectIDZND0="38145@1" Pin0InfoVect0LinkObjId="SW-229088_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3190,-593 3190,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25124a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3190,-656 3190,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38144@0" ObjectIDZND0="38143@1" Pin0InfoVect0LinkObjId="SW-229086_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229087_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3190,-656 3190,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2512700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3253,-457 3190,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24e7710@0" ObjectIDZND0="38687@x" ObjectIDZND1="38146@x" ObjectIDZND2="38145@x" Pin0InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_056Ld_0" Pin0InfoVect1LinkObjId="SW-229089_0" Pin0InfoVect2LinkObjId="SW-229088_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24e7710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3253,-457 3190,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2512960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3190,-397 3190,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38687@0" ObjectIDZND0="g_24e7710@0" ObjectIDZND1="38146@x" ObjectIDZND2="38145@x" Pin0InfoVect0LinkObjId="g_24e7710_0" Pin0InfoVect1LinkObjId="SW-229089_0" Pin0InfoVect2LinkObjId="SW-229088_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_056Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3190,-397 3190,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d9270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3126,-406 3126,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2512bc0@0" ObjectIDZND0="38146@0" Pin0InfoVect0LinkObjId="SW-229089_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2512bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3126,-406 3126,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d94d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3190,-491 3126,-491 3126,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="38687@x" ObjectIDND1="g_24e7710@0" ObjectIDND2="38145@x" ObjectIDZND0="38146@1" Pin0InfoVect0LinkObjId="SW-229089_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_056Ld_0" Pin1InfoVect1LinkObjId="g_24e7710_0" Pin1InfoVect2LinkObjId="SW-229088_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3190,-491 3126,-491 3126,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d9730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3190,-457 3190,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38687@x" ObjectIDND1="g_24e7710@0" ObjectIDZND0="38146@x" ObjectIDZND1="38145@x" Pin0InfoVect0LinkObjId="SW-229089_0" Pin0InfoVect1LinkObjId="SW-229088_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_056Ld_0" Pin1InfoVect1LinkObjId="g_24e7710_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3190,-457 3190,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d9990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3190,-491 3190,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="38687@x" ObjectIDND1="g_24e7710@0" ObjectIDND2="38146@x" ObjectIDZND0="38145@0" Pin0InfoVect0LinkObjId="SW-229088_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_056Ld_0" Pin1InfoVect1LinkObjId="g_24e7710_0" Pin1InfoVect2LinkObjId="SW-229089_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3190,-491 3190,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b8480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-693 2230,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24892@1" ObjectIDZND0="24879@0" Pin0InfoVect0LinkObjId="g_24bc390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-693 2230,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b86e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-594 2230,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24891@0" ObjectIDZND0="24893@1" Pin0InfoVect0LinkObjId="SW-138311_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-594 2230,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b9d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-657 2230,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24892@0" ObjectIDZND0="24891@1" Pin0InfoVect0LinkObjId="SW-138307_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-657 2230,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b9f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2293,-458 2230,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24b8940@0" ObjectIDZND0="34006@x" ObjectIDZND1="24894@x" ObjectIDZND2="24893@x" Pin0InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_052Ld_0" Pin0InfoVect1LinkObjId="SW-138312_0" Pin0InfoVect2LinkObjId="SW-138311_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24b8940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2293,-458 2230,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24ba1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-398 2230,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34006@0" ObjectIDZND0="g_24b8940@0" ObjectIDZND1="24894@x" ObjectIDZND2="24893@x" Pin0InfoVect0LinkObjId="g_24b8940_0" Pin0InfoVect1LinkObjId="SW-138312_0" Pin0InfoVect2LinkObjId="SW-138311_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_052Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-398 2230,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258ef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2166,-407 2166,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24ba430@0" ObjectIDZND0="24894@0" Pin0InfoVect0LinkObjId="SW-138312_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24ba430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2166,-407 2166,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258f1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-492 2166,-492 2166,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34006@x" ObjectIDND1="g_24b8940@0" ObjectIDND2="24893@x" ObjectIDZND0="24894@1" Pin0InfoVect0LinkObjId="SW-138312_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_052Ld_0" Pin1InfoVect1LinkObjId="g_24b8940_0" Pin1InfoVect2LinkObjId="SW-138311_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-492 2166,-492 2166,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258f400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-458 2230,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="34006@x" ObjectIDND1="g_24b8940@0" ObjectIDZND0="24894@x" ObjectIDZND1="24893@x" Pin0InfoVect0LinkObjId="SW-138312_0" Pin0InfoVect1LinkObjId="SW-138311_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_052Ld_0" Pin1InfoVect1LinkObjId="g_24b8940_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-458 2230,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258f660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-492 2230,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34006@x" ObjectIDND1="g_24b8940@0" ObjectIDND2="24894@x" ObjectIDZND0="24893@0" Pin0InfoVect0LinkObjId="SW-138311_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YR_YJ.YR_YJ_052Ld_0" Pin1InfoVect1LinkObjId="g_24b8940_0" Pin1InfoVect2LinkObjId="SW-138312_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-492 2230,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2518bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1132 2274,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38155@0" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_2518db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1132 2274,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2518db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1134 2913,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38157@0" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_2518bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1134 2913,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2519490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2242,-1016 2274,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_24b05c0@0" ObjectIDZND1="g_262c430@0" ObjectIDZND2="38156@x" Pin0InfoVect0LinkObjId="g_24b05c0_0" Pin0InfoVect1LinkObjId="g_262c430_0" Pin0InfoVect2LinkObjId="SW-183269_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2242,-1016 2274,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251a080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1040 2274,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="38156@0" ObjectIDZND0="g_24b05c0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_262c430@0" Pin0InfoVect0LinkObjId="g_24b05c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_262c430_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1040 2274,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251a2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1016 2304,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_262c430@0" ObjectIDND2="38156@x" ObjectIDZND0="g_24b05c0@0" Pin0InfoVect0LinkObjId="g_24b05c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_262c430_0" Pin1InfoVect2LinkObjId="SW-183269_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1016 2304,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_251b980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2881,-988 2881,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_251af30@0" Pin0InfoVect0LinkObjId="g_251af30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2881,-988 2881,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251bbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2881,-1015 2917,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="38158@x" ObjectIDZND1="g_24b1d70@0" ObjectIDZND2="g_262d310@0" Pin0InfoVect0LinkObjId="SW-183271_0" Pin0InfoVect1LinkObjId="g_24b1d70_0" Pin0InfoVect2LinkObjId="g_262d310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2881,-1015 2917,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251be40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2917,-1015 2947,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="38158@x" ObjectIDND2="g_262d310@0" ObjectIDZND0="g_24b1d70@0" Pin0InfoVect0LinkObjId="g_24b1d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-183271_0" Pin1InfoVect2LinkObjId="g_262d310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2917,-1015 2947,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251cb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1042 2913,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="38158@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24b1d70@0" ObjectIDZND2="g_262d310@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24b1d70_0" Pin0InfoVect2LinkObjId="g_262d310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1042 2913,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24892d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2994,-1177 2994,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_2518bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2994,-1177 2994,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_248a590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2994,-1290 3030,-1290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_248aa50@0" ObjectIDND2="g_250bdf0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_248aa50_0" Pin1InfoVect2LinkObjId="g_250bdf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2994,-1290 3030,-1290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_248a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3066,-1290 3080,-1290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2489b00@0" Pin0InfoVect0LinkObjId="g_2489b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3066,-1290 3080,-1290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_250c870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2959,-1310 2994,-1310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_248aa50@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_248aa50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2959,-1310 2994,-1310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_250d580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2994,-1269 2994,-1290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_248aa50@0" ObjectIDZND1="g_250bdf0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_248aa50_0" Pin0InfoVect1LinkObjId="g_250bdf0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2994,-1269 2994,-1290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_250e290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2994,-1290 2994,-1310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_248aa50@0" ObjectIDZND1="g_250bdf0@0" Pin0InfoVect0LinkObjId="g_248aa50_0" Pin0InfoVect1LinkObjId="g_250bdf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2994,-1290 2994,-1310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_250e4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2994,-1310 2994,-1384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" ObjectIDND0="g_248aa50@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_248aa50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2994,-1310 2994,-1384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_250e750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3030,-1310 2994,-1310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_250bdf0@0" ObjectIDZND0="g_248aa50@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_248aa50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_250bdf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3030,-1310 2994,-1310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250e9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,-1183 2193,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43247@0" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_2518bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-1183 2193,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250f6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,-1292 2229,-1292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_250fb60@0" ObjectIDND1="g_25108d0@0" ObjectIDND2="g_26280f0@0" ObjectIDZND0="27849@1" Pin0InfoVect0LinkObjId="SW-183006_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_250fb60_0" Pin1InfoVect1LinkObjId="g_25108d0_0" Pin1InfoVect2LinkObjId="g_26280f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-1292 2229,-1292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250f900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-1292 2279,-1292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27849@0" ObjectIDZND0="g_250ec10@0" Pin0InfoVect0LinkObjId="g_250ec10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-1292 2279,-1292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2511350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2158,-1312 2193,-1312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_250fb60@0" ObjectIDZND0="27849@x" ObjectIDZND1="43247@x" ObjectIDZND2="g_25108d0@0" Pin0InfoVect0LinkObjId="SW-183006_0" Pin0InfoVect1LinkObjId="SW-183004_0" Pin0InfoVect2LinkObjId="g_25108d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_250fb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2158,-1312 2193,-1312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25115b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,-1275 2193,-1292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="43247@0" ObjectIDZND0="g_250fb60@0" ObjectIDZND1="g_25108d0@0" ObjectIDZND2="g_26280f0@0" Pin0InfoVect0LinkObjId="g_250fb60_0" Pin0InfoVect1LinkObjId="g_25108d0_0" Pin0InfoVect2LinkObjId="g_26280f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-1275 2193,-1292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2511810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,-1292 2193,-1312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="27849@x" ObjectIDND1="43247@x" ObjectIDZND0="g_250fb60@0" ObjectIDZND1="g_25108d0@0" ObjectIDZND2="g_26280f0@0" Pin0InfoVect0LinkObjId="g_250fb60_0" Pin0InfoVect1LinkObjId="g_25108d0_0" Pin0InfoVect2LinkObjId="g_26280f0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183006_0" Pin1InfoVect1LinkObjId="SW-183004_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-1292 2193,-1312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2511a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-1312 2193,-1312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_25108d0@0" ObjectIDZND0="g_250fb60@0" ObjectIDZND1="27849@x" ObjectIDZND2="43247@x" Pin0InfoVect0LinkObjId="g_250fb60_0" Pin0InfoVect1LinkObjId="SW-183006_0" Pin0InfoVect2LinkObjId="SW-183004_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25108d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-1312 2193,-1312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25461f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1283 2604,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="27851@x" ObjectIDZND0="27851@1" Pin0InfoVect0LinkObjId="SW-183096_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-183096_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1283 2604,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2546450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2640,-1283 2654,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27851@0" ObjectIDZND0="g_24998f0@0" Pin0InfoVect0LinkObjId="g_24998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183096_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2640,-1283 2654,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25466b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1281 2577,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="27851@x" ObjectIDZND0="0@x" ObjectIDZND1="27851@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-183096_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-183096_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1281 2577,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_254ce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2242,-989 2242,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_254c440@0" Pin0InfoVect0LinkObjId="g_254c440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2242,-989 2242,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24a45c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2552,-1283 2577,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="27851@x" ObjectIDZND1="0@x" ObjectIDZND2="27851@x" Pin0InfoVect0LinkObjId="SW-183096_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-183096_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2552,-1283 2577,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24a52b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2510,-1283 2523,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="capacitor" ObjectIDND0="g_24a4820@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24a4820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2510,-1283 2523,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24a62c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2621,-1309 2577,-1309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_24a5510@0" ObjectIDZND0="g_2628d20@0" ObjectIDZND1="43245@x" Pin0InfoVect0LinkObjId="g_2628d20_0" Pin0InfoVect1LinkObjId="SW-183094_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24a5510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2621,-1309 2577,-1309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24a6db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1265 2577,-1309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43245@0" ObjectIDZND0="g_24a5510@0" ObjectIDZND1="g_2628d20@0" Pin0InfoVect0LinkObjId="g_24a5510_0" Pin0InfoVect1LinkObjId="g_2628d20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183094_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1265 2577,-1309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ae9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2578,-1171 2578,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="43245@0" ObjectIDZND0="24884@1" Pin0InfoVect0LinkObjId="SW-138151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183094_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2578,-1171 2578,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24af6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2578,-1065 2578,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="24884@0" ObjectIDZND0="g_24b1040@0" ObjectIDZND1="0@x" ObjectIDZND2="g_262b6f0@0" Pin0InfoVect0LinkObjId="g_24b1040_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_262b6f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2578,-1065 2578,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24af930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-1046 2578,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="g_24b1040@0" ObjectIDZND0="24884@x" ObjectIDZND1="0@x" ObjectIDZND2="g_262b6f0@0" Pin0InfoVect0LinkObjId="SW-138151_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_262b6f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24b1040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-1046 2578,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_261b270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="24878@0" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_2518bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2518bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_261c5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2545,-1046 2577,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="24884@x" ObjectIDZND1="g_24b1040@0" ObjectIDZND2="g_262b6f0@0" Pin0InfoVect0LinkObjId="SW-138151_0" Pin0InfoVect1LinkObjId="g_24b1040_0" Pin0InfoVect2LinkObjId="g_262b6f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2545,-1046 2577,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_261db60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2545,-1019 2545,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_261d170@0" Pin0InfoVect0LinkObjId="g_261d170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2545,-1019 2545,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2628860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,-1312 2193,-1324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_250fb60@0" ObjectIDND1="27849@x" ObjectIDND2="43247@x" ObjectIDZND0="g_26280f0@0" Pin0InfoVect0LinkObjId="g_26280f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_250fb60_0" Pin1InfoVect1LinkObjId="SW-183006_0" Pin1InfoVect2LinkObjId="SW-183004_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-1312 2193,-1324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2628ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,-1377 2193,-1402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_26280f0@1" ObjectIDZND0="37844@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26280f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-1377 2193,-1402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2629740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1309 2577,-1329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_24a5510@0" ObjectIDND1="43245@x" ObjectIDZND0="g_2628d20@0" Pin0InfoVect0LinkObjId="g_2628d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24a5510_0" Pin1InfoVect1LinkObjId="SW-183094_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1309 2577,-1329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26299a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1382 2577,-1401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2628d20@1" ObjectIDZND0="g_2549c10@0" Pin0InfoVect0LinkObjId="g_2549c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2628d20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1382 2577,-1401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_262a7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2970,-1290 2995,-1290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_248aa50@0" ObjectIDZND2="g_250bdf0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_248aa50_0" Pin0InfoVect2LinkObjId="g_250bdf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2970,-1290 2995,-1290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_262b490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2928,-1290 2941,-1290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="capacitor" ObjectIDND0="g_262aa00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262aa00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2928,-1290 2941,-1290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2578,-1046 2578,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="lightningRod" ObjectIDND0="24884@x" ObjectIDND1="g_24b1040@0" ObjectIDND2="0@x" ObjectIDZND0="g_262b6f0@0" Pin0InfoVect0LinkObjId="g_262b6f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138151_0" Pin1InfoVect1LinkObjId="g_24b1040_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2578,-1046 2578,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262c1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2578,-996 2578,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_262b6f0@1" ObjectIDZND0="g_24a7730@0" Pin0InfoVect0LinkObjId="g_24a7730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262b6f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2578,-996 2578,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262ce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1016 2274,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_24b05c0@0" ObjectIDND1="0@x" ObjectIDND2="38156@x" ObjectIDZND0="g_262c430@1" Pin0InfoVect0LinkObjId="g_262c430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24b05c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-183269_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1016 2274,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262d0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-967 2274,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_262c430@0" ObjectIDZND0="24915@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262c430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-967 2274,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1015 2913,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="38158@x" ObjectIDND2="g_24b1d70@0" ObjectIDZND0="g_262d310@1" Pin0InfoVect0LinkObjId="g_262d310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-183271_0" Pin1InfoVect2LinkObjId="g_24b1d70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1015 2913,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2439ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-971 2913,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_262d310@0" ObjectIDZND0="24916@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262d310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-971 2913,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_243d360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3287,-866 3287,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_243c910@0" Pin0InfoVect0LinkObjId="g_243c910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3287,-866 3287,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_243f790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3353,-893 3287,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_243d5c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_243d5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3353,-893 3287,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_243f9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3319,-818 3319,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24912@x" ObjectIDND1="24911@x" ObjectIDZND0="g_243ef10@1" Pin0InfoVect0LinkObjId="g_243ef10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138567_0" Pin1InfoVect1LinkObjId="SW-138562_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3319,-818 3319,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_243fc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3319,-937 3319,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_243ef10@0" ObjectIDZND0="g_243a130@0" Pin0InfoVect0LinkObjId="g_243a130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_243ef10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3319,-937 3319,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2445120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3150,-817 3150,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3150,-817 3150,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2445380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3150,-746 3150,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="24880@0" Pin0InfoVect0LinkObjId="g_24bba90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3150,-746 3150,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2448c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1919,-856 1919,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_24481e0@0" Pin0InfoVect0LinkObjId="g_24481e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1919,-856 1919,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_244b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1985,-883 1919,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_2448e90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2448e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1985,-883 1919,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_244b2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1951,-927 1951,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_244a7e0@0" ObjectIDZND0="g_24455e0@0" Pin0InfoVect0LinkObjId="g_24455e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_244a7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1951,-927 1951,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_244e380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3190,-692 3190,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38144@1" ObjectIDZND0="24880@0" Pin0InfoVect0LinkObjId="g_24bba90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229087_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3190,-692 3190,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245fff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1115 2274,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38155@1" ObjectIDZND0="24885@1" Pin0InfoVect0LinkObjId="SW-138159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1115 2274,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2460250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1057 2274,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38156@1" ObjectIDZND0="24885@0" Pin0InfoVect0LinkObjId="SW-138159_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1057 2274,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2468a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1117 2913,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38157@1" ObjectIDZND0="24942@1" Pin0InfoVect0LinkObjId="SW-138234_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1117 2913,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2468cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1059 2913,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38158@1" ObjectIDZND0="24942@0" Pin0InfoVect0LinkObjId="SW-138234_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1059 2913,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2749cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1249 2577,-1233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43245@1" ObjectIDZND0="27850@1" Pin0InfoVect0LinkObjId="SW-183092_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183094_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1249 2577,-1233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2749f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1191 2577,-1206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43245@1" ObjectIDZND0="27850@0" Pin0InfoVect0LinkObjId="SW-183092_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183094_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1191 2577,-1206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2752180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,-1259 2193,-1243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43247@1" ObjectIDZND0="27848@1" Pin0InfoVect0LinkObjId="SW-183002_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-1259 2193,-1243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27523e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,-1201 2193,-1216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43247@1" ObjectIDZND0="27848@0" Pin0InfoVect0LinkObjId="SW-183002_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-1201 2193,-1216 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24880" cx="2912" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24879" cx="2022" cy="-722" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24879" cx="2538" cy="-722" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24880" cx="2617" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24879" cx="1951" cy="-722" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24879" cx="2273" cy="-722" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24879" cx="2448" cy="-722" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24880" cx="2708" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24880" cx="2947" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24879" cx="2230" cy="-722" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24878" cx="2913" cy="-1156" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24878" cx="2994" cy="-1156" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24880" cx="3150" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24880" cx="3190" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24878" cx="2274" cy="-1156" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24878" cx="2577" cy="-1156" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24878" cx="2577" cy="-1156" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24878" cx="2193" cy="-1156" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130471" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1593.500000 -1258.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23911" ObjectName="DYN-YR_YJ"/>
     <cge:Meas_Ref ObjectId="130471"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_229cbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1994.000000 -365.000000) translate(0,15)">他克线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2262f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -714.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_222f7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3284.000000 -715.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23dcce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3126.000000 -1178.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24bc0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2167.000000 -1456.000000) translate(0,15)">莲</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24bc0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2167.000000 -1456.000000) translate(0,33)">宜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24bc0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2167.000000 -1456.000000) translate(0,51)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24bc0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2167.000000 -1456.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_24c8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2453b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2453b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2453b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2453b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2453b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2453b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2453b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2453b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2453b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(21,40,56)" font-family="SimHei" font-size="20" graphid="g_2583580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -1366.500000) translate(0,16)">宜就变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_259a1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -1005.000000) translate(0,15)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b5e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -1017.000000) translate(0,15)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_255d1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3127.000000 -934.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_255d570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2647.000000 -364.000000) translate(0,15)">永金高速Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_257e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2420.000000 -368.000000) translate(0,15)">火把线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_255ab90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2193.000000 -364.000000) translate(0,15)">老怀哨线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2540bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3133.000000 -365.000000) translate(0,15)">永金高速Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24dabb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2920.000000 -364.000000) translate(0,15)">木马线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2590d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2568.000000 -945.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24cdf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2283.000000 -828.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ce460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2280.000000 -770.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ce6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1994.000000 -808.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c35e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1958.000000 -774.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c3a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2566.000000 -834.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c3d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2626.000000 -778.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c41e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2495.000000 -776.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c4420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2919.000000 -771.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c4660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3352.000000 -839.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c48a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3326.000000 -777.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c4ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2239.000000 -615.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c4d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2235.000000 -680.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c4f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2234.500000 -552.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c52a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2113.000000 -455.000000) translate(0,12)">05267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c5700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2457.000000 -612.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c5a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2453.000000 -679.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2452.500000 -548.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c60e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2327.000000 -458.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c6320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2717.000000 -614.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c6560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2713.000000 -681.000000) translate(0,12)">0542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c67a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2712.500000 -551.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c69e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2587.000000 -460.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c6c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -614.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c6e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2952.000000 -681.000000) translate(0,12)">0552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c70a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2951.500000 -551.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c72e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2826.000000 -464.000000) translate(0,12)">05567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252fbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2922.000000 -829.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252fda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2588.000000 -1106.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252ffe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2174.000000 -923.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24a7010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2535.000000 -1484.000000) translate(0,15)"> 1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24a7010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2535.000000 -1484.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24a7010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2535.000000 -1484.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24a7010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2535.000000 -1484.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24a7010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2535.000000 -1484.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24afb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -1387.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24afb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -1387.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261a390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2590.000000 -1223.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261a970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3005.000000 -1228.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261abb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3052.000000 -1311.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261adf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2627.000000 -1303.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261b030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2251.000000 -1311.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261eef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2224.000000 -1234.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_261f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1404.000000 -989.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_26208a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1633.000000 -1374.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2622010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1634.000000 -1341.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2624900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2142.000000 -894.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2624900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2142.000000 -894.000000) translate(0,27)">SZ9-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2626200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2757.000000 -881.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2626200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2757.000000 -881.000000) translate(0,27)">SZ11-10000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2626200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2757.000000 -881.000000) translate(0,42)">10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2626200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2757.000000 -881.000000) translate(0,57)">Yn,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2626200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2757.000000 -881.000000) translate(0,72)">35±4×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2626200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2757.000000 -881.000000) translate(0,87)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -614.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244c370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2029.000000 -551.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244c5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1903.000000 -463.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244c7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2029.000000 -681.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244d890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3199.000000 -614.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244dcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3197.000000 -551.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244df00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3133.000000 -457.000000) translate(0,12)">05667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244e140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3197.000000 -681.000000) translate(0,12)">0562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2450e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2813.000000 -909.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2468f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2922.000000 -1095.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2469540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2283.000000 -1093.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246c600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -415.000000) translate(0,17)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246de60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.000000 -425.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246de60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.000000 -425.500000) translate(0,38)">13987885824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246fe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -455.000000) translate(0,17)">6811179</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_24701c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1794.500000 -1359.000000) translate(0,16)">AVC</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="1778" y="-1370"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YR_YJ.YR_YJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2003,-1156 3167,-1156 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24878" ObjectName="BS-YR_YJ.YR_YJ_3IM"/>
    <cge:TPSR_Ref TObjectID="24878"/></metadata>
   <polyline fill="none" opacity="0" points="2003,-1156 3167,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_YJ.YR_YJ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2600,-721 3348,-721 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24880" ObjectName="BS-YR_YJ.YR_YJ_9IIM"/>
    <cge:TPSR_Ref TObjectID="24880"/></metadata>
   <polyline fill="none" opacity="0" points="2600,-721 3348,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_YJ.YR_YJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1865,-722 2558,-722 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24879" ObjectName="BS-YR_YJ.YR_YJ_9IM"/>
    <cge:TPSR_Ref TObjectID="24879"/></metadata>
   <polyline fill="none" opacity="0" points="1865,-722 2558,-722 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YR_YJ.YR_YJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35105"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2239.000000 -858.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2239.000000 -858.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24915" ObjectName="TF-YR_YJ.YR_YJ_1T"/>
    <cge:TPSR_Ref TObjectID="24915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3134.000000 -812.000000)" xlink:href="#transformer2:shape56_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3134.000000 -812.000000)" xlink:href="#transformer2:shape56_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YR_YJ.YR_YJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35109"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2882.000000 -860.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2882.000000 -860.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24916" ObjectName="TF-YR_YJ.YR_YJ_2T"/>
    <cge:TPSR_Ref TObjectID="24916"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1436.000000 -1318.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-138078" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3023.000000 -900.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138078" ObjectName="YR_YJ:YR_YJ_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217881" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.000000 -1172.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217881" ObjectName="YR_YJ:YR_YJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217881" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -1249.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217881" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217881" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -1212.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217881" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-229171" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3023.000000 -918.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229171" ObjectName="YR_YJ:YR_YJ_2T_TAP"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="1448" y="-1377"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="1448" y="-1377"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1399" y="-1394"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1399" y="-1394"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2174" y="-923"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2174" y="-923"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2239" y="-615"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2239" y="-615"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="2809" y="-910"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="2809" y="-910"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2457" y="-612"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2457" y="-612"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2717" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2717" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2956" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2956" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2222" y="-1235"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2222" y="-1235"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2588" y="-1223"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2588" y="-1223"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="2566" y="-834"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="2566" y="-834"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="65" x="1400" y="-991"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="65" x="1400" y="-991"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1622" y="-1382"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1622" y="-1382"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1622" y="-1349"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1622" y="-1349"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2031" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2031" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3199" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3199" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="1778" y="-1371"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="1778" y="-1371"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ca8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1792.000000 1203.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24cb460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1808.000000 1187.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24cbff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1800.000000 1218.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24cc550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1800.000000 1233.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24cc7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1800.000000 1247.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ccb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2311.000000 902.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24cd690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2311.000000 917.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 106.000000 -36.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2561e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1852.000000 301.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2562a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1841.000000 286.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25632f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1866.000000 271.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2563d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2332.000000 842.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2564050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2321.000000 827.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2564290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2346.000000 812.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25646b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2973.000000 842.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2564970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2962.000000 827.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2564bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2987.000000 812.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2564ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1748.000000 786.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2565150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1764.000000 770.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2565390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1756.000000 801.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25655d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1756.000000 816.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2565810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1756.000000 830.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2565b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3417.000000 731.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2565db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3433.000000 715.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252ca40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3425.000000 746.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252cc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3425.000000 761.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252cec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3425.000000 775.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252d2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2976.000000 1089.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252d5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2965.000000 1074.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252d7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2990.000000 1059.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25314e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2950.000000 902.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25181d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2950.000000 917.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261bec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2072.000000 1245.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261c110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2061.000000 1230.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261c310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2086.000000 1215.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261e4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2337.000000 1089.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261e6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2326.000000 1074.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261e8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2351.000000 1059.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25567e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2525.000000 872.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a5fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2525.000000 857.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a61e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2525.000000 888.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2469960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2173.000000 334.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2469c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2162.000000 319.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2469e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2187.000000 304.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246a280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2398.000000 335.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246a540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2387.000000 320.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246a780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2412.000000 305.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246aba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2661.000000 335.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246ae60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2650.000000 320.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246b0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2675.000000 305.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246b4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2898.000000 336.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246b780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2887.000000 321.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246b9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2912.000000 306.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246bde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3142.000000 336.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246c0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3131.000000 321.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246c2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3156.000000 306.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2871.000000 -982.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2232.000000 -984.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2518.000000 -1273.000000)" xlink:href="#capacitor:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2535.000000 -1014.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2936.000000 -1280.000000)" xlink:href="#capacitor:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3277.000000 -860.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1909.000000 -850.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2224.000000 -332.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24891"/>
     <cge:Term_Ref ObjectID="35055"/>
    <cge:TPSR_Ref TObjectID="24891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2224.000000 -332.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24891"/>
     <cge:Term_Ref ObjectID="35055"/>
    <cge:TPSR_Ref TObjectID="24891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2224.000000 -332.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24891"/>
     <cge:Term_Ref ObjectID="35055"/>
    <cge:TPSR_Ref TObjectID="24891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2452.000000 -334.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24895"/>
     <cge:Term_Ref ObjectID="35063"/>
    <cge:TPSR_Ref TObjectID="24895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2452.000000 -334.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24895"/>
     <cge:Term_Ref ObjectID="35063"/>
    <cge:TPSR_Ref TObjectID="24895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138109" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2452.000000 -334.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24895"/>
     <cge:Term_Ref ObjectID="35063"/>
    <cge:TPSR_Ref TObjectID="24895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2714.000000 -333.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24899"/>
     <cge:Term_Ref ObjectID="35071"/>
    <cge:TPSR_Ref TObjectID="24899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2714.000000 -333.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24899"/>
     <cge:Term_Ref ObjectID="35071"/>
    <cge:TPSR_Ref TObjectID="24899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2714.000000 -333.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24899"/>
     <cge:Term_Ref ObjectID="35071"/>
    <cge:TPSR_Ref TObjectID="24899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138124" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 -334.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138124" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24903"/>
     <cge:Term_Ref ObjectID="35079"/>
    <cge:TPSR_Ref TObjectID="24903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138125" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 -334.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138125" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24903"/>
     <cge:Term_Ref ObjectID="35079"/>
    <cge:TPSR_Ref TObjectID="24903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138121" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 -334.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24903"/>
     <cge:Term_Ref ObjectID="35079"/>
    <cge:TPSR_Ref TObjectID="24903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-138087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -831.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24879"/>
     <cge:Term_Ref ObjectID="35033"/>
    <cge:TPSR_Ref TObjectID="24879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-138088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -831.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24879"/>
     <cge:Term_Ref ObjectID="35033"/>
    <cge:TPSR_Ref TObjectID="24879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-138089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -831.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24879"/>
     <cge:Term_Ref ObjectID="35033"/>
    <cge:TPSR_Ref TObjectID="24879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-138090" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -831.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138090" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24879"/>
     <cge:Term_Ref ObjectID="35033"/>
    <cge:TPSR_Ref TObjectID="24879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-138094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -831.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24879"/>
     <cge:Term_Ref ObjectID="35033"/>
    <cge:TPSR_Ref TObjectID="24879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-138095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3484.000000 -776.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24880"/>
     <cge:Term_Ref ObjectID="35034"/>
    <cge:TPSR_Ref TObjectID="24880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-138096" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3484.000000 -776.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24880"/>
     <cge:Term_Ref ObjectID="35034"/>
    <cge:TPSR_Ref TObjectID="24880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-138097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3484.000000 -776.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24880"/>
     <cge:Term_Ref ObjectID="35034"/>
    <cge:TPSR_Ref TObjectID="24880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-138098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3484.000000 -776.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24880"/>
     <cge:Term_Ref ObjectID="35034"/>
    <cge:TPSR_Ref TObjectID="24880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-138102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3484.000000 -776.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24880"/>
     <cge:Term_Ref ObjectID="35034"/>
    <cge:TPSR_Ref TObjectID="24880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-138079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 -1248.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24878"/>
     <cge:Term_Ref ObjectID="35032"/>
    <cge:TPSR_Ref TObjectID="24878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-138080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 -1248.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24878"/>
     <cge:Term_Ref ObjectID="35032"/>
    <cge:TPSR_Ref TObjectID="24878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-138081" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 -1248.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24878"/>
     <cge:Term_Ref ObjectID="35032"/>
    <cge:TPSR_Ref TObjectID="24878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-138082" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 -1248.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24878"/>
     <cge:Term_Ref ObjectID="35032"/>
    <cge:TPSR_Ref TObjectID="24878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-138086" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 -1248.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24878"/>
     <cge:Term_Ref ObjectID="35032"/>
    <cge:TPSR_Ref TObjectID="24878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2571.000000 -887.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24907"/>
     <cge:Term_Ref ObjectID="35087"/>
    <cge:TPSR_Ref TObjectID="24907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-138128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2571.000000 -887.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24907"/>
     <cge:Term_Ref ObjectID="35087"/>
    <cge:TPSR_Ref TObjectID="24907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-138129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2571.000000 -887.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24907"/>
     <cge:Term_Ref ObjectID="35087"/>
    <cge:TPSR_Ref TObjectID="24907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-138065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2378.000000 -915.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24915"/>
     <cge:Term_Ref ObjectID="35106"/>
    <cge:TPSR_Ref TObjectID="24915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-138064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2378.000000 -915.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138064" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24915"/>
     <cge:Term_Ref ObjectID="35106"/>
    <cge:TPSR_Ref TObjectID="24915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3031.000000 -1090.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24942"/>
     <cge:Term_Ref ObjectID="35145"/>
    <cge:TPSR_Ref TObjectID="24942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138070" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3031.000000 -1090.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24942"/>
     <cge:Term_Ref ObjectID="35145"/>
    <cge:TPSR_Ref TObjectID="24942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138066" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3031.000000 -1090.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138066" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24942"/>
     <cge:Term_Ref ObjectID="35145"/>
    <cge:TPSR_Ref TObjectID="24942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -842.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24943"/>
     <cge:Term_Ref ObjectID="35147"/>
    <cge:TPSR_Ref TObjectID="24943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -842.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24943"/>
     <cge:Term_Ref ObjectID="35147"/>
    <cge:TPSR_Ref TObjectID="24943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -842.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24943"/>
     <cge:Term_Ref ObjectID="35147"/>
    <cge:TPSR_Ref TObjectID="24943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2392.000000 -842.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24887"/>
     <cge:Term_Ref ObjectID="35047"/>
    <cge:TPSR_Ref TObjectID="24887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138062" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2392.000000 -842.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24887"/>
     <cge:Term_Ref ObjectID="35047"/>
    <cge:TPSR_Ref TObjectID="24887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138058" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2392.000000 -842.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138058" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24887"/>
     <cge:Term_Ref ObjectID="35047"/>
    <cge:TPSR_Ref TObjectID="24887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2126.000000 -1244.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27848"/>
     <cge:Term_Ref ObjectID="39413"/>
    <cge:TPSR_Ref TObjectID="27848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183262" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2126.000000 -1244.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27848"/>
     <cge:Term_Ref ObjectID="39413"/>
    <cge:TPSR_Ref TObjectID="27848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2126.000000 -1244.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27848"/>
     <cge:Term_Ref ObjectID="39413"/>
    <cge:TPSR_Ref TObjectID="27848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2389.000000 -1088.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24885"/>
     <cge:Term_Ref ObjectID="35043"/>
    <cge:TPSR_Ref TObjectID="24885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138056" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2389.000000 -1088.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24885"/>
     <cge:Term_Ref ObjectID="35043"/>
    <cge:TPSR_Ref TObjectID="24885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138052" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2389.000000 -1088.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138052" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24885"/>
     <cge:Term_Ref ObjectID="35043"/>
    <cge:TPSR_Ref TObjectID="24885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-228618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2012.000000 -334.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="228618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37960"/>
     <cge:Term_Ref ObjectID="56896"/>
    <cge:TPSR_Ref TObjectID="37960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-228619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2012.000000 -334.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="228619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37960"/>
     <cge:Term_Ref ObjectID="56896"/>
    <cge:TPSR_Ref TObjectID="37960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-228615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2012.000000 -334.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="228615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37960"/>
     <cge:Term_Ref ObjectID="56896"/>
    <cge:TPSR_Ref TObjectID="37960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-229143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3195.000000 -336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38143"/>
     <cge:Term_Ref ObjectID="23099"/>
    <cge:TPSR_Ref TObjectID="38143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-229144" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3195.000000 -336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38143"/>
     <cge:Term_Ref ObjectID="23099"/>
    <cge:TPSR_Ref TObjectID="38143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-229140" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3195.000000 -336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38143"/>
     <cge:Term_Ref ObjectID="23099"/>
    <cge:TPSR_Ref TObjectID="38143"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-228623">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37961" ObjectName="SW-YR_YJ.YR_YJ_0511SW"/>
     <cge:Meas_Ref ObjectId="228623"/>
    <cge:TPSR_Ref TObjectID="37961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -521.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37962" ObjectName="SW-YR_YJ.YR_YJ_0516SW"/>
     <cge:Meas_Ref ObjectId="228624"/>
    <cge:TPSR_Ref TObjectID="37962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138243">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2904.000000 -741.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24890" ObjectName="SW-YR_YJ.YR_YJ_0022SW"/>
     <cge:Meas_Ref ObjectId="138243"/>
    <cge:TPSR_Ref TObjectID="24890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2529.000000 -749.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24908" ObjectName="SW-YR_YJ.YR_YJ_0121SW"/>
     <cge:Meas_Ref ObjectId="138513"/>
    <cge:TPSR_Ref TObjectID="24908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138514">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2608.000000 -751.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24909" ObjectName="SW-YR_YJ.YR_YJ_0122SW"/>
     <cge:Meas_Ref ObjectId="138514"/>
    <cge:TPSR_Ref TObjectID="24909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138561">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 1943.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24910" ObjectName="SW-YR_YJ.YR_YJ_0901SW"/>
     <cge:Meas_Ref ObjectId="138561"/>
    <cge:TPSR_Ref TObjectID="24910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138168">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2265.000000 -740.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24888" ObjectName="SW-YR_YJ.YR_YJ_0011SW"/>
     <cge:Meas_Ref ObjectId="138168"/>
    <cge:TPSR_Ref TObjectID="24888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138572">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2019.000000 -789.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24913" ObjectName="SW-YR_YJ.YR_YJ_09017SW"/>
     <cge:Meas_Ref ObjectId="138572"/>
    <cge:TPSR_Ref TObjectID="24913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138562">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3311.000000 -747.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24911" ObjectName="SW-YR_YJ.YR_YJ_0902SW"/>
     <cge:Meas_Ref ObjectId="138562"/>
    <cge:TPSR_Ref TObjectID="24911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138567">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3387.000000 -792.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24912" ObjectName="SW-YR_YJ.YR_YJ_09027SW"/>
     <cge:Meas_Ref ObjectId="138567"/>
    <cge:TPSR_Ref TObjectID="24912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228625">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1949.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37963" ObjectName="SW-YR_YJ.YR_YJ_05167SW"/>
     <cge:Meas_Ref ObjectId="228625"/>
    <cge:TPSR_Ref TObjectID="37963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138358">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2439.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24896" ObjectName="SW-YR_YJ.YR_YJ_0531SW"/>
     <cge:Meas_Ref ObjectId="138358"/>
    <cge:TPSR_Ref TObjectID="24896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2439.000000 -519.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24897" ObjectName="SW-YR_YJ.YR_YJ_0536SW"/>
     <cge:Meas_Ref ObjectId="138359"/>
    <cge:TPSR_Ref TObjectID="24897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2375.000000 -425.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24898" ObjectName="SW-YR_YJ.YR_YJ_05367SW"/>
     <cge:Meas_Ref ObjectId="138360"/>
    <cge:TPSR_Ref TObjectID="24898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2699.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24900" ObjectName="SW-YR_YJ.YR_YJ_0542SW"/>
     <cge:Meas_Ref ObjectId="138406"/>
    <cge:TPSR_Ref TObjectID="24900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138407">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2699.000000 -521.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24901" ObjectName="SW-YR_YJ.YR_YJ_0546SW"/>
     <cge:Meas_Ref ObjectId="138407"/>
    <cge:TPSR_Ref TObjectID="24901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2635.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24902" ObjectName="SW-YR_YJ.YR_YJ_05467SW"/>
     <cge:Meas_Ref ObjectId="138408"/>
    <cge:TPSR_Ref TObjectID="24902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138454">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24904" ObjectName="SW-YR_YJ.YR_YJ_0552SW"/>
     <cge:Meas_Ref ObjectId="138454"/>
    <cge:TPSR_Ref TObjectID="24904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138455">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 -521.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24905" ObjectName="SW-YR_YJ.YR_YJ_0556SW"/>
     <cge:Meas_Ref ObjectId="138455"/>
    <cge:TPSR_Ref TObjectID="24905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138456">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2874.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24906" ObjectName="SW-YR_YJ.YR_YJ_05567SW"/>
     <cge:Meas_Ref ObjectId="138456"/>
    <cge:TPSR_Ref TObjectID="24906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229087">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3181.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38144" ObjectName="SW-YR_YJ.YR_YJ_0561SW"/>
     <cge:Meas_Ref ObjectId="229087"/>
    <cge:TPSR_Ref TObjectID="38144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3181.000000 -521.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38145" ObjectName="SW-YR_YJ.YR_YJ_0566SW"/>
     <cge:Meas_Ref ObjectId="229088"/>
    <cge:TPSR_Ref TObjectID="38145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3117.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38146" ObjectName="SW-YR_YJ.YR_YJ_05667SW"/>
     <cge:Meas_Ref ObjectId="229089"/>
    <cge:TPSR_Ref TObjectID="38146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138310">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2221.000000 -652.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24892" ObjectName="SW-YR_YJ.YR_YJ_0521SW"/>
     <cge:Meas_Ref ObjectId="138310"/>
    <cge:TPSR_Ref TObjectID="24892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138311">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2221.000000 -522.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24893" ObjectName="SW-YR_YJ.YR_YJ_0526SW"/>
     <cge:Meas_Ref ObjectId="138311"/>
    <cge:TPSR_Ref TObjectID="24893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138312">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2157.000000 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24894" ObjectName="SW-YR_YJ.YR_YJ_05267SW"/>
     <cge:Meas_Ref ObjectId="138312"/>
    <cge:TPSR_Ref TObjectID="24894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3057.000000 -1264.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183006">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2256.000000 -1266.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27849" ObjectName="SW-YR_YJ.YR_YJ_35167SW"/>
     <cge:Meas_Ref ObjectId="183006"/>
    <cge:TPSR_Ref TObjectID="27849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2631.000000 -1257.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27851" ObjectName="SW-YR_YJ.YR_YJ_3536SW"/>
     <cge:Meas_Ref ObjectId="183096"/>
    <cge:TPSR_Ref TObjectID="27851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138151">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2568.000000 -1060.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24884" ObjectName="SW-YR_YJ.YR_YJ_3901SW"/>
     <cge:Meas_Ref ObjectId="138151"/>
    <cge:TPSR_Ref TObjectID="24884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3143.000000 -741.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183269">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2264.000000 -1108.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38155" ObjectName="SW-YR_YJ.YR_YJ_301XC"/>
     <cge:Meas_Ref ObjectId="183269"/>
    <cge:TPSR_Ref TObjectID="38155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183269">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2264.000000 -1033.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38156" ObjectName="SW-YR_YJ.YR_YJ_301XC1"/>
     <cge:Meas_Ref ObjectId="183269"/>
    <cge:TPSR_Ref TObjectID="38156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.000000 -1110.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38157" ObjectName="SW-YR_YJ.YR_YJ_302XC"/>
     <cge:Meas_Ref ObjectId="183271"/>
    <cge:TPSR_Ref TObjectID="38157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.000000 -1035.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38158" ObjectName="SW-YR_YJ.YR_YJ_302XC1"/>
     <cge:Meas_Ref ObjectId="183271"/>
    <cge:TPSR_Ref TObjectID="38158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2567.000000 -1242.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43245" ObjectName="SW-YR_YJ.YR_YJ_353XC"/>
     <cge:Meas_Ref ObjectId="183094"/>
    <cge:TPSR_Ref TObjectID="43245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2567.000000 -1167.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43245" ObjectName="SW-YR_YJ.YR_YJ_353XC"/>
     <cge:Meas_Ref ObjectId="183094"/>
    <cge:TPSR_Ref TObjectID="43245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2183.000000 -1252.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43247" ObjectName="SW-YR_YJ.YR_YJ_351XC"/>
     <cge:Meas_Ref ObjectId="183004"/>
    <cge:TPSR_Ref TObjectID="43247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2183.000000 -1177.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43247" ObjectName="SW-YR_YJ.YR_YJ_351XC"/>
     <cge:Meas_Ref ObjectId="183004"/>
    <cge:TPSR_Ref TObjectID="43247"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="1448" y="-1377"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1399" y="-1394"/></g>
   <g href="35kV宜就变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2174" y="-923"/></g>
   <g href="35kV宜就变10kV老怀哨线052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2239" y="-615"/></g>
   <g href="35kV宜就变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="2809" y="-910"/></g>
   <g href="35kV宜就变10kV火把线053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2457" y="-612"/></g>
   <g href="35kV宜就变10kV永金高速Ⅰ回线054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2717" y="-614"/></g>
   <g href="35kV宜就变10kV木马线055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2956" y="-614"/></g>
   <g href="35kV宜就变35kV莲宜T线351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2222" y="-1235"/></g>
   <g href="35kV宜就变35kV1号站用变353间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2588" y="-1223"/></g>
   <g href="35kV宜就变YR_YJ_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="2566" y="-834"/></g>
   <g href="35kV宜就变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="65" x="1400" y="-991"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1622" y="-1382"/></g>
   <g href="cx_配调_配网接线图35_永仁.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1622" y="-1349"/></g>
   <g href="35kV宜就变10kV他克线051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2031" y="-614"/></g>
   <g href="35kV宜就变10kV永金高速Ⅱ回线056间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3199" y="-614"/></g>
   <g href="AVC宜就站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="1778" y="-1371"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_24bc770">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2091.961111 -403.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b56f0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2517.961111 -401.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_252a3b0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2777.961111 -403.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24bfb80">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3016.961111 -403.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24e7710">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3259.961111 -403.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b8940">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2299.961111 -404.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_248aa50">
    <use class="BV-0KV" transform="matrix(0.933333 0.000000 -0.000000 1.000000 2952.538889 -1364.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_250fb60">
    <use class="BV-35KV" transform="matrix(0.933333 0.000000 -0.000000 1.000000 2151.538889 -1366.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2549c10">
    <use class="BV-35KV" transform="matrix(-0.826087 -0.000000 -0.000000 0.755102 2590.000000 -1471.000000)" xlink:href="#lightningRod:shape197"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a5510">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2614.000000 -1305.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b05c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2297.000000 -962.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b1040">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2607.000000 -992.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b1d70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2940.000000 -961.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26280f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2188.000000 -1319.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2628d20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2572.000000 -1324.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262b6f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2569.000000 -991.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262c430">
    <use class="BV-35KV" transform="matrix(0.727273 -0.000000 0.000000 -0.539683 2270.000000 -964.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262d310">
    <use class="BV-35KV" transform="matrix(0.727273 -0.000000 0.000000 -0.539683 2909.000000 -966.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_243d5c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3346.000000 -839.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_243ef10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3310.000000 -901.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2448e90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1978.000000 -829.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_244a7e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1942.000000 -891.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YR_YJ"/>
</svg>