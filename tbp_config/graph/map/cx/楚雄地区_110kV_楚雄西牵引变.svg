<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-291" aopId="3940870" id="thSvg" product="E8000V2" version="1.0" viewBox="13 -1029 1235 772">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape6_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="38" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="38" y2="13"/>
   </symbol>
   <symbol id="switch2:shape6_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="13" y2="14"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="11" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape93_0">
    <ellipse cx="62" cy="27" fillStyle="0" rx="24.5" ry="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="74" x2="59" y1="25" y2="25"/>
   </symbol>
   <symbol id="transformer2:shape93_1">
    <circle cx="29" cy="25" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="16" y1="24" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <circle cx="15" cy="19" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="10" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="10" x2="15" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="15" cy="41" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="11" x2="15" y1="50" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="45" y2="50"/>
   </symbol>
   <symbol id="voltageTransformer:shape16">
    <polyline points="50,28 13,28 13,61 " stroke-width="1.57143"/>
    <polyline points="13,61 25,61 " stroke-width="1.57143"/>
    <polyline points="1,61 13,61 " stroke-width="1.57143"/>
    <polyline points="8,77 18,77 " stroke-width="1.57143"/>
    <polyline points="6,69 20,69 " stroke-width="1.57143"/>
    <circle cx="75" cy="68" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="107" cy="49" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="75" x2="75" y1="58" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="83" x2="75" y1="74" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="75" x2="67" y1="67" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="74" y1="20" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="82" x2="74" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="66" y1="29" y2="36"/>
    <circle cx="75" cy="29" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="98" x2="107" y1="43" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="116" x2="107" y1="43" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="116" x2="98" y1="43" y2="43"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33d00a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33d1060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d1a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d2c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d3f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d4c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33d57c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33d61c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2bcdf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2bcdf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33d9c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33d9c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33db7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33db7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_33dc7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33de480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33df0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33dffb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33e0890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e2050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e2d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e3610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33e3dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e4eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e5830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e6320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33e6ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33e8160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33e8d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33e9d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33ea970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33f9140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ec260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33ed850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33eed80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="782" width="1245" x="8" y="-1034"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-246164">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 -631.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41309" ObjectName="SW-CX_CXXQ.CX_CXXQ_1002SW"/>
     <cge:Meas_Ref ObjectId="246164"/>
    <cge:TPSR_Ref TObjectID="41309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246163">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 -631.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41308" ObjectName="SW-CX_CXXQ.CX_CXXQ_1001SW"/>
     <cge:Meas_Ref ObjectId="246163"/>
    <cge:TPSR_Ref TObjectID="41308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246159">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.970391 -690.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41304" ObjectName="SW-CX_CXXQ.CX_CXXQ_1021GKSW"/>
     <cge:Meas_Ref ObjectId="246159"/>
    <cge:TPSR_Ref TObjectID="41304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246161">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 576.970391 -771.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41306" ObjectName="SW-CX_CXXQ.CX_CXXQ_1024GKSW"/>
     <cge:Meas_Ref ObjectId="246161"/>
    <cge:TPSR_Ref TObjectID="41306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246155">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.247166 -691.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41300" ObjectName="SW-CX_CXXQ.CX_CXXQ_1011GKSW"/>
     <cge:Meas_Ref ObjectId="246155"/>
    <cge:TPSR_Ref TObjectID="41300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246157">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1061.386364 -772.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41302" ObjectName="SW-CX_CXXQ.CX_CXXQ_1014GKSW"/>
     <cge:Meas_Ref ObjectId="246157"/>
    <cge:TPSR_Ref TObjectID="41302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246172">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 550.000000 -595.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41318" ObjectName="SW-CX_CXXQ.CX_CXXQ_1022GKSW"/>
     <cge:Meas_Ref ObjectId="246172"/>
    <cge:TPSR_Ref TObjectID="41318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246173">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 657.000000 -545.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41319" ObjectName="SW-CX_CXXQ.CX_CXXQ_1022DSW"/>
     <cge:Meas_Ref ObjectId="246173"/>
    <cge:TPSR_Ref TObjectID="41319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246162">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 533.109589 -795.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41307" ObjectName="SW-CX_CXXQ.CX_CXXQ_1024DSW"/>
     <cge:Meas_Ref ObjectId="246162"/>
    <cge:TPSR_Ref TObjectID="41307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246158">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.386364 -803.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41303" ObjectName="SW-CX_CXXQ.CX_CXXQ_1014DSW"/>
     <cge:Meas_Ref ObjectId="246158"/>
    <cge:TPSR_Ref TObjectID="41303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246175">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -593.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41314" ObjectName="SW-CX_CXXQ.CX_CXXQ_1023GKSW"/>
     <cge:Meas_Ref ObjectId="246175"/>
    <cge:TPSR_Ref TObjectID="41314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246176">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 791.000000 -557.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41315" ObjectName="SW-CX_CXXQ.CX_CXXQ_1023DSW"/>
     <cge:Meas_Ref ObjectId="246176"/>
    <cge:TPSR_Ref TObjectID="41315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246166">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1010.000000 -594.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41311" ObjectName="SW-CX_CXXQ.CX_CXXQ_1013GKSW"/>
     <cge:Meas_Ref ObjectId="246166"/>
    <cge:TPSR_Ref TObjectID="41311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246167">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1092.000000 -558.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41312" ObjectName="SW-CX_CXXQ.CX_CXXQ_1013DSW"/>
     <cge:Meas_Ref ObjectId="246167"/>
    <cge:TPSR_Ref TObjectID="41312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246170">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -594.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41316" ObjectName="SW-CX_CXXQ.CX_CXXQ_1012GKSW"/>
     <cge:Meas_Ref ObjectId="246170"/>
    <cge:TPSR_Ref TObjectID="41316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246171">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1240.000000 -544.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41317" ObjectName="SW-CX_CXXQ.CX_CXXQ_1012DSW"/>
     <cge:Meas_Ref ObjectId="246171"/>
    <cge:TPSR_Ref TObjectID="41317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246156">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.696429 1003.386364 -728.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41301" ObjectName="SW-CX_CXXQ.CX_CXXQ_1011DSW"/>
     <cge:Meas_Ref ObjectId="246156"/>
    <cge:TPSR_Ref TObjectID="41301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246160">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.678571 659.109589 -729.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41305" ObjectName="SW-CX_CXXQ.CX_CXXQ_1021DSW"/>
     <cge:Meas_Ref ObjectId="246160"/>
    <cge:TPSR_Ref TObjectID="41305"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="509,-636 801,-636 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="509,-636 801,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-636 896,-636 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="833,-636 896,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-636 1228,-636 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="931,-636 1228,-636 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(0.000000 -0.480874 -0.466629 -0.000000 553.803586 -714.360656)" xlink:href="#transformer2:shape93_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.480874 -0.466629 -0.000000 553.803586 -714.360656)" xlink:href="#transformer2:shape93_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(0.000000 -0.502732 -0.460094 -0.000000 1153.882600 -708.377049)" xlink:href="#transformer2:shape93_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.502732 -0.460094 -0.000000 1153.882600 -708.377049)" xlink:href="#transformer2:shape93_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.600000 -0.000000 0.000000 -1.524590 694.000000 -384.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.600000 -0.000000 0.000000 -1.524590 694.000000 -384.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.600000 -0.000000 0.000000 -1.524590 995.000000 -384.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.600000 -0.000000 0.000000 -1.524590 995.000000 -384.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_2bce8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="618,-776 639,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="41306@1" ObjectIDZND0="41304@x" ObjectIDZND1="0@x" ObjectIDZND2="41305@x" Pin0InfoVect0LinkObjId="SW-246159_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="SW-246160_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246161_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="618,-776 639,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ca6960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="639,-695 639,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41304@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246159_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="639,-695 639,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf4060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="639,-731 639,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="41304@1" ObjectIDZND0="41306@x" ObjectIDZND1="0@x" ObjectIDZND2="41305@x" Pin0InfoVect0LinkObjId="SW-246161_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="SW-246160_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246159_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="639,-731 639,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2beea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="542,-776 542,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_2cbecb0@0" ObjectIDND1="41306@x" ObjectIDND2="41307@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cbecb0_0" Pin1InfoVect1LinkObjId="SW-246161_0" Pin1InfoVect2LinkObjId="SW-246162_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="542,-776 542,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c6a250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="639,-776 639,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="41306@x" ObjectIDND1="41304@x" ObjectIDND2="41305@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-246161_0" Pin1InfoVect1LinkObjId="SW-246159_0" Pin1InfoVect2LinkObjId="SW-246160_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="639,-776 639,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c66320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1066,-777 1040,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="41302@0" ObjectIDZND0="41300@x" ObjectIDZND1="0@x" ObjectIDZND2="41301@x" Pin0InfoVect0LinkObjId="SW-246155_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="SW-246156_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246157_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1066,-777 1040,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c66980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-732 1041,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="41300@1" ObjectIDZND0="41302@x" ObjectIDZND1="0@x" ObjectIDZND2="41301@x" Pin0InfoVect0LinkObjId="SW-246157_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="SW-246156_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246155_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-732 1041,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c66b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-777 1041,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="41302@x" ObjectIDND1="41300@x" ObjectIDND2="41301@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-246157_0" Pin1InfoVect1LinkObjId="SW-246155_0" Pin1InfoVect2LinkObjId="SW-246156_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-777 1041,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c96490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-774 1142,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="41302@x" ObjectIDND1="g_2c96930@0" ObjectIDND2="41303@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-246157_0" Pin1InfoVect1LinkObjId="g_2c96930_0" Pin1InfoVect2LinkObjId="SW-246158_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-774 1142,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c8ec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,-554 606,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="41318@x" ObjectIDND1="g_2bcacb0@0" ObjectIDZND0="41319@0" Pin0InfoVect0LinkObjId="SW-246173_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246172_0" Pin1InfoVect1LinkObjId="g_2bcacb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="559,-554 606,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c8ee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,-600 559,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="41318@0" ObjectIDZND0="41319@x" ObjectIDZND1="g_2bcacb0@0" Pin0InfoVect0LinkObjId="SW-246173_0" Pin0InfoVect1LinkObjId="g_2bcacb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246172_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="559,-600 559,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c8f020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,-554 559,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="41319@x" ObjectIDND1="41318@x" ObjectIDZND0="g_2bcacb0@0" Pin0InfoVect0LinkObjId="g_2bcacb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246173_0" Pin1InfoVect1LinkObjId="SW-246172_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="559,-554 559,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c24df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="505,-776 542,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_2cbecb0@0" ObjectIDZND0="41306@x" ObjectIDZND1="41307@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-246161_0" Pin0InfoVect1LinkObjId="SW-246162_0" Pin0InfoVect2LinkObjId="TF-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cbecb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="505,-776 542,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c24fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="542,-776 582,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="g_2cbecb0@0" ObjectIDND1="41307@x" ObjectIDND2="0@x" ObjectIDZND0="41306@0" Pin0InfoVect0LinkObjId="SW-246161_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cbecb0_0" Pin1InfoVect1LinkObjId="SW-246162_0" Pin1InfoVect2LinkObjId="TF-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="542,-776 582,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c251d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="542,-776 542,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="g_2cbecb0@0" ObjectIDND1="41306@x" ObjectIDND2="0@x" ObjectIDZND0="41307@0" Pin0InfoVect0LinkObjId="SW-246162_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cbecb0_0" Pin1InfoVect1LinkObjId="SW-246161_0" Pin1InfoVect2LinkObjId="TF-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="542,-776 542,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cfc610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1102,-777 1142,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="41302@1" ObjectIDZND0="g_2c96930@0" ObjectIDZND1="41303@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2c96930_0" Pin0InfoVect1LinkObjId="SW-246158_0" Pin0InfoVect2LinkObjId="TF-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246157_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1102,-777 1142,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cfc800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-777 1179,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="41302@x" ObjectIDND1="41303@x" ObjectIDND2="0@x" ObjectIDZND0="g_2c96930@0" Pin0InfoVect0LinkObjId="g_2c96930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-246157_0" Pin1InfoVect1LinkObjId="SW-246158_0" Pin1InfoVect2LinkObjId="TF-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-777 1179,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cfc9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-777 1142,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="41302@x" ObjectIDND1="g_2c96930@0" ObjectIDND2="0@x" ObjectIDZND0="41303@0" Pin0InfoVect0LinkObjId="SW-246158_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-246157_0" Pin1InfoVect1LinkObjId="g_2c96930_0" Pin1InfoVect2LinkObjId="TF-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-777 1142,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c8d040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="718,-566 740,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="41313@x" ObjectIDND1="41314@x" ObjectIDZND0="41315@0" Pin0InfoVect0LinkObjId="SW-246176_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246174_0" Pin1InfoVect1LinkObjId="SW-246175_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="718,-566 740,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bd7040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="718,-566 718,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="41315@x" ObjectIDND1="41314@x" ObjectIDZND0="41313@1" Pin0InfoVect0LinkObjId="SW-246174_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246176_0" Pin1InfoVect1LinkObjId="SW-246175_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="718,-566 718,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bd7230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="718,-598 718,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="41314@0" ObjectIDZND0="41315@x" ObjectIDZND1="41313@x" Pin0InfoVect0LinkObjId="SW-246176_0" Pin0InfoVect1LinkObjId="SW-246174_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246175_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="718,-598 718,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf2a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,-567 1041,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="41310@x" ObjectIDND1="41311@x" ObjectIDZND0="41312@0" Pin0InfoVect0LinkObjId="SW-246167_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246165_0" Pin1InfoVect1LinkObjId="SW-246166_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1019,-567 1041,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf2c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,-567 1019,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="41312@x" ObjectIDND1="41311@x" ObjectIDZND0="41310@1" Pin0InfoVect0LinkObjId="SW-246165_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246167_0" Pin1InfoVect1LinkObjId="SW-246166_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1019,-567 1019,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf2e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,-599 1019,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="41311@0" ObjectIDZND0="41312@x" ObjectIDZND1="41310@x" Pin0InfoVect0LinkObjId="SW-246167_0" Pin0InfoVect1LinkObjId="SW-246165_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1019,-599 1019,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c0a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-553 1189,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="41316@x" ObjectIDND1="g_2bfc9f0@0" ObjectIDZND0="41317@0" Pin0InfoVect0LinkObjId="SW-246171_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246170_0" Pin1InfoVect1LinkObjId="g_2bfc9f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-553 1189,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c0a790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-599 1142,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="41316@0" ObjectIDZND0="41317@0" ObjectIDZND1="g_2bfc9f0@0" Pin0InfoVect0LinkObjId="SW-246171_0" Pin0InfoVect1LinkObjId="g_2bfc9f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-599 1142,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c0a9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-553 1142,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="41316@x" ObjectIDND1="41317@0" ObjectIDZND0="g_2bfc9f0@0" Pin0InfoVect0LinkObjId="g_2bfc9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246170_0" Pin1InfoVect1LinkObjId="SW-246171_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-553 1142,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bce100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="668,-763 668,-764 668,-776 638,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="41305@0" ObjectIDZND0="41306@x" ObjectIDZND1="41304@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-246161_0" Pin0InfoVect1LinkObjId="SW-246159_0" Pin0InfoVect2LinkObjId="TF-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="668,-763 668,-764 668,-776 638,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bce350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-777 1012,-777 1012,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="41302@x" ObjectIDND1="41300@x" ObjectIDND2="0@x" ObjectIDZND0="41301@0" Pin0InfoVect0LinkObjId="SW-246156_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-246157_0" Pin1InfoVect1LinkObjId="SW-246155_0" Pin1InfoVect2LinkObjId="TF-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-777 1012,-777 1012,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ca5cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="718,-503 718,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="41313@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="718,-503 718,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bfc7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,-504 1019,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="41310@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246165_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1019,-504 1019,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bfe8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-696 1041,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41300@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246155_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-696 1041,-636 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-246142" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -381.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41313"/>
     <cge:Term_Ref ObjectID="62678"/>
    <cge:TPSR_Ref TObjectID="41313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-246143" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -381.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41313"/>
     <cge:Term_Ref ObjectID="62678"/>
    <cge:TPSR_Ref TObjectID="41313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-246144" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -381.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41313"/>
     <cge:Term_Ref ObjectID="62678"/>
    <cge:TPSR_Ref TObjectID="41313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-246150" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -381.000000) translate(0,57)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41313"/>
     <cge:Term_Ref ObjectID="62678"/>
    <cge:TPSR_Ref TObjectID="41313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-246151" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -381.000000) translate(0,72)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41313"/>
     <cge:Term_Ref ObjectID="62678"/>
    <cge:TPSR_Ref TObjectID="41313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-246152" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -381.000000) translate(0,87)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41313"/>
     <cge:Term_Ref ObjectID="62678"/>
    <cge:TPSR_Ref TObjectID="41313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-246154" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -381.000000) translate(0,102)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41313"/>
     <cge:Term_Ref ObjectID="62678"/>
    <cge:TPSR_Ref TObjectID="41313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-246129" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -381.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41310"/>
     <cge:Term_Ref ObjectID="62672"/>
    <cge:TPSR_Ref TObjectID="41310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-246130" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -381.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41310"/>
     <cge:Term_Ref ObjectID="62672"/>
    <cge:TPSR_Ref TObjectID="41310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-246131" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -381.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41310"/>
     <cge:Term_Ref ObjectID="62672"/>
    <cge:TPSR_Ref TObjectID="41310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-246137" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -381.000000) translate(0,57)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41310"/>
     <cge:Term_Ref ObjectID="62672"/>
    <cge:TPSR_Ref TObjectID="41310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-246138" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -381.000000) translate(0,72)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41310"/>
     <cge:Term_Ref ObjectID="62672"/>
    <cge:TPSR_Ref TObjectID="41310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-246139" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -381.000000) translate(0,87)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41310"/>
     <cge:Term_Ref ObjectID="62672"/>
    <cge:TPSR_Ref TObjectID="41310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-246141" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -381.000000) translate(0,102)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41310"/>
     <cge:Term_Ref ObjectID="62672"/>
    <cge:TPSR_Ref TObjectID="41310"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="163" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="163" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="163" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2bcacb0">
    <use class="BV-110KV" transform="matrix(-0.636364 -0.000000 -0.000000 0.693009 607.000000 -507.142857)" xlink:href="#voltageTransformer:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfc9f0">
    <use class="BV-110KV" transform="matrix(-0.636364 -0.000000 -0.000000 0.693009 1190.000000 -506.142857)" xlink:href="#voltageTransformer:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cbecb0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 447.970391 -769.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c96930">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1236.525561 -784.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1041" cy="-636" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="639" cy="-636" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c70560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 13.000000 -551.000000) translate(0,290)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2824ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 149.000000 -1001.500000) translate(0,16)">楚雄西牵引变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2b7ce10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 563.500000 -879.000000) translate(0,12)">110KV楚雄西牵Ⅰ回T接线（T接紫西东线）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2cbf480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -760.000000) translate(0,10)">102BL-A\B\C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2beec60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -713.000000) translate(0,10)">102YH-J</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c66510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 924.500000 -880.000000) translate(0,12)">110KV楚雄西牵Ⅱ回T接线（T接紫东线）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2c66810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 985.247166 -710.000000) translate(0,10)">1011GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2c96680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1114.000000 -704.000000) translate(0,10)">101YH-J</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2c97100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -754.000000) translate(0,10)">101BL-A\B\C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2bd7420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -435.000000) translate(0,10)">102YH-A\B\C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2bd77b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.860802 -439.000000) translate(0,10)">2B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2bf30b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 965.860802 -434.000000) translate(0,10)">1B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2c892a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1112.000000 -431.000000) translate(0,10)">101YH-A\B\C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2c896a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 583.970391 -712.000000) translate(0,10)">1021GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b63710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b63710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b63710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b63710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b63710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b63710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b63710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bce570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 957.386364 -762.000000) translate(0,12)">1011D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b64ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1060.386364 -800.000000) translate(0,12)">1014GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b64e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1152.386364 -835.000000) translate(0,12)">1014D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b651f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 677.109589 -755.000000) translate(0,12)">1021D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b653d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.109589 -826.000000) translate(0,12)">1024D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b655b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.109589 -804.000000) translate(0,12)">1024GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 595.000000 -540.000000) translate(0,12)">1022D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -619.000000) translate(0,12)">1022GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 736.000000 -590.000000) translate(0,12)">1023D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -621.000000) translate(0,12)">1023GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b65f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -523.000000) translate(0,12)">102DL</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b660f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.000000 -662.000000) translate(0,12)">1002GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca49a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -666.000000) translate(0,12)">1001GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca4b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 965.000000 -525.000000) translate(0,12)">101DL</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca4d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1039.000000 -593.000000) translate(0,12)">1013D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca4f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -620.000000) translate(0,12)">1013GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1149.000000 -620.000000) translate(0,12)">1012GK</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca5300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1181.000000 -541.000000) translate(0,12)">1012D</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-246174">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -495.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41313" ObjectName="SW-CX_CXXQ.CX_CXXQ_102BK"/>
     <cge:Meas_Ref ObjectId="246174"/>
    <cge:TPSR_Ref TObjectID="41313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246165">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1010.000000 -496.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41310" ObjectName="SW-CX_CXXQ.CX_CXXQ_101BK"/>
     <cge:Meas_Ref ObjectId="246165"/>
    <cge:TPSR_Ref TObjectID="41310"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 148.000000 -776.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 150.000000 -814.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.970391 -825.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1036.247166 -826.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-246033" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 317.500000 -917.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41292" ObjectName="DYN-CX_CXXQ"/>
     <cge:Meas_Ref ObjectId="246033"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_CXXQ"/>
</svg>