<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-133" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1202 2478 1203">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape131">
    <ellipse cx="32" cy="19" rx="7.5" ry="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="8" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="32" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="16" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="14" y2="16"/>
    <ellipse cx="21" cy="15" rx="7.5" ry="6.5" stroke-width="1"/>
    <ellipse cx="43" cy="19" rx="7.5" ry="6.5" stroke-width="1"/>
    <ellipse cx="32" cy="9" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="43" cy="9" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="2" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="3" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="32" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="32" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="43" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="43" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="41" y1="17" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="46" y1="17" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="41" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="32" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="32" y1="18" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape175">
    <polyline DF8003:Layer="PUBLIC" points="6,4 0,16 12,16 6,4 6,5 6,4 "/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape187">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="55" y2="46"/>
    <polyline arcFlag="1" points="6,35 5,35 5,35 4,35 3,35 3,36 2,36 1,37 1,37 1,38 0,39 0,39 0,40 0,41 0,42 0,42 1,43 1,44 1,44 2,45 3,45 3,46 4,46 5,46 5,46 6,46 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="6,24 5,24 5,24 4,24 3,24 3,25 2,25 1,26 1,26 1,27 0,28 0,28 0,29 0,30 0,31 0,31 1,32 1,33 1,33 2,34 3,34 3,35 4,35 5,35 5,35 6,35 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="6,13 5,13 5,13 4,13 3,13 3,14 2,14 1,15 1,15 1,16 0,17 0,17 0,18 0,19 0,20 0,20 1,21 1,22 1,22 2,23 3,23 3,24 4,24 5,24 5,24 6,24 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="13" y2="4"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_3d473c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape35_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
   </symbol>
   <symbol id="transformer2:shape35_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="81" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="32" y2="97"/>
    <circle cx="24" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="26" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="6" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="8" y2="11"/>
    <circle cx="37" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="23" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="26" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="21" y1="15" y2="15"/>
    <circle cx="50" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="37" cy="24" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="73" x2="70" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="67" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="71" x2="71" y1="30" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="65" x2="77" y1="31" y2="31"/>
    <rect height="27" stroke-width="0.416667" width="14" x="64" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="71" x2="71" y1="78" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="38" y1="78" y2="78"/>
    <rect height="27" stroke-width="0.416667" width="14" x="30" y="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="2" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="0" x2="12" y1="11" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="51" x2="51" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="48" x2="51" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="51" x2="54" y1="15" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape102">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="49" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="42" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="39" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="53" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="26" x2="26" y1="53" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="45" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="20" y1="45" y2="45"/>
    <circle cx="33" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="33" cy="18" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="53" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="49" x2="49" y1="53" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="26" x2="42" y1="45" y2="45"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1213" width="2488" x="3112" y="-1207"/>
  </g><g id="Line_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-753 3896,-753 3896,-775 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.222222" x1="3896" x2="3896" y1="-790" y2="-794"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="3896" x2="3896" y1="-832" y2="-811"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4399,-753 4416,-753 4416,-775 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.222222" x1="4416" x2="4416" y1="-790" y2="-794"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="4416" x2="4416" y1="-832" y2="-811"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-800 3891,-811 3902,-811 3896,-800 3896,-801 3896,-800 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-786 3891,-775 3902,-775 3896,-786 3896,-785 3896,-786 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-800 4411,-811 4422,-811 4416,-800 4416,-801 4416,-800 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-786 4411,-775 4422,-775 4416,-786 4416,-785 4416,-786 " stroke="rgb(170,85,127)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-82769">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -981.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18220" ObjectName="SW-CX_YTS.CX_YTS_101BK"/>
     <cge:Meas_Ref ObjectId="82769"/>
    <cge:TPSR_Ref TObjectID="18220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82776">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3967.000000 -635.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18227" ObjectName="SW-CX_YTS.CX_YTS_301BK"/>
     <cge:Meas_Ref ObjectId="82776"/>
    <cge:TPSR_Ref TObjectID="18227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82785">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3670.250000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18236" ObjectName="SW-CX_YTS.CX_YTS_384BK"/>
     <cge:Meas_Ref ObjectId="82785"/>
    <cge:TPSR_Ref TObjectID="18236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82760">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -983.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18211" ObjectName="SW-CX_YTS.CX_YTS_161BK"/>
     <cge:Meas_Ref ObjectId="82760"/>
    <cge:TPSR_Ref TObjectID="18211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82780">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3830.250000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18231" ObjectName="SW-CX_YTS.CX_YTS_383BK"/>
     <cge:Meas_Ref ObjectId="82780"/>
    <cge:TPSR_Ref TObjectID="18231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82799">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4007.250000 -477.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18250" ObjectName="SW-CX_YTS.CX_YTS_382BK"/>
     <cge:Meas_Ref ObjectId="82799"/>
    <cge:TPSR_Ref TObjectID="18250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82792">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4200.250000 -479.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18243" ObjectName="SW-CX_YTS.CX_YTS_381BK"/>
     <cge:Meas_Ref ObjectId="82792"/>
    <cge:TPSR_Ref TObjectID="18243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4930.000000 -788.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5079.000000 -789.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5177.000000 -682.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5397.000000 -683.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5300.000000 -788.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101629">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -981.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20421" ObjectName="SW-CX_YTS.CX_YTS_102BK"/>
     <cge:Meas_Ref ObjectId="101629"/>
    <cge:TPSR_Ref TObjectID="20421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101635">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -635.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20422" ObjectName="SW-CX_YTS.CX_YTS_302BK"/>
     <cge:Meas_Ref ObjectId="101635"/>
    <cge:TPSR_Ref TObjectID="20422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101640">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4346.000000 -486.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20423" ObjectName="SW-CX_YTS.CX_YTS_312BK"/>
     <cge:Meas_Ref ObjectId="101640"/>
    <cge:TPSR_Ref TObjectID="20423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4571.250000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20425" ObjectName="SW-CX_YTS.CX_YTS_386BK"/>
     <cge:Meas_Ref ObjectId="101650"/>
    <cge:TPSR_Ref TObjectID="20425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4739.250000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20427" ObjectName="SW-CX_YTS.CX_YTS_387BK"/>
     <cge:Meas_Ref ObjectId="101662"/>
    <cge:TPSR_Ref TObjectID="20427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4920.250000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20426" ObjectName="SW-CX_YTS.CX_YTS_388BK"/>
     <cge:Meas_Ref ObjectId="101655"/>
    <cge:TPSR_Ref TObjectID="20426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101645">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5098.250000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20424" ObjectName="SW-CX_YTS.CX_YTS_389BK"/>
     <cge:Meas_Ref ObjectId="101645"/>
    <cge:TPSR_Ref TObjectID="20424"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_356e890">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3478.000000 -370.000000)" xlink:href="#voltageTransformer:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3df6680">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5232.000000 -370.000000)" xlink:href="#voltageTransformer:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35f1830">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3630.000000 -1122.000000)" xlink:href="#voltageTransformer:shape102"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3670.000000 -185.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -185.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 -185.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 -185.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_4480370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3963.000000 -124.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_231c1e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4896.000000 -885.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35ddde0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5266.000000 -885.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22eff10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4287.000000 -414.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f0960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.000000 -414.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_250bd00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.000000 -125.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ab4c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -1095.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dbf440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 -1028.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dbfed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -966.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dc0960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -1093.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dc13f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -1027.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dc1e80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -964.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dc2910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -988.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29ef7d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -911.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f0230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4333.000000 -1027.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f0cc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -1093.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f1750" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4333.000000 -964.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f21e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -624.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f2c70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.000000 -482.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dd8f40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -354.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dd9990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3893.000000 -624.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dda3f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 -721.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ddae40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 -721.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ddb890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3434.000000 -480.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ddc320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3596.000000 -482.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ddcdb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3596.000000 -354.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e80b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -482.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e8b20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -353.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e95b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 -354.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ea040" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -481.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25eaad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4127.000000 -483.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25eb560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -355.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4351040" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 -482.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4351ad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -354.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4352560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -482.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4352ff0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -354.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4353a80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5025.000000 -482.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4354510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5188.000000 -480.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4354fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5024.000000 -354.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_3fe4b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-886 3896,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18209@0" ObjectIDZND0="18221@0" Pin0InfoVect0LinkObjId="SW-82770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-886 3896,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ce3cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-949 3896,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="18221@1" ObjectIDZND0="18220@x" ObjectIDZND1="18223@x" Pin0InfoVect0LinkObjId="SW-82769_0" Pin0InfoVect1LinkObjId="SW-82772_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-949 3896,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e2c260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-970 3896,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="18223@x" ObjectIDND1="18221@x" ObjectIDZND0="18220@0" Pin0InfoVect0LinkObjId="SW-82769_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82772_0" Pin1InfoVect1LinkObjId="SW-82770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-970 3896,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fa97c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-1099 3831,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18225@0" ObjectIDZND0="g_3dc0960@0" Pin0InfoVect0LinkObjId="g_3dc0960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-1099 3831,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25e60e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-970 3878,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18220@x" ObjectIDND1="18221@x" ObjectIDZND0="18223@1" Pin0InfoVect0LinkObjId="SW-82772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82769_0" Pin1InfoVect1LinkObjId="SW-82770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-970 3878,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_363e7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-970 3832,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18223@0" ObjectIDZND0="g_3dc1e80@0" Pin0InfoVect0LinkObjId="g_3dc1e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-970 3832,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3351760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-630 3957,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18227@x" ObjectIDND1="18228@x" ObjectIDZND0="18230@1" Pin0InfoVect0LinkObjId="SW-82779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82776_0" Pin1InfoVect1LinkObjId="SW-82777_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-630 3957,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dce00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-630 3911,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18230@0" ObjectIDZND0="g_3dd9990@0" Pin0InfoVect0LinkObjId="g_3dd9990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-630 3911,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af1f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-619 3976,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="18228@1" ObjectIDZND0="18230@x" ObjectIDZND1="18227@x" Pin0InfoVect0LinkObjId="SW-82779_0" Pin0InfoVect1LinkObjId="SW-82776_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82777_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-619 3976,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-630 3976,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="18230@x" ObjectIDND1="18228@x" ObjectIDZND0="18227@0" Pin0InfoVect0LinkObjId="SW-82776_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82779_0" Pin1InfoVect1LinkObjId="SW-82777_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-630 3976,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2651f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-571 3976,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18210@0" ObjectIDZND0="18228@0" Pin0InfoVect0LinkObjId="SW-82777_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fa320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-571 3976,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3516,-571 3516,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18210@0" ObjectIDZND0="18241@1" Pin0InfoVect0LinkObjId="SW-82790_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fa320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3516,-571 3516,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43acd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3516,-486 3498,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_356e890@0" ObjectIDND1="18241@x" ObjectIDZND0="18242@1" Pin0InfoVect0LinkObjId="SW-82791_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_356e890_0" Pin1InfoVect1LinkObjId="SW-82790_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3516,-486 3498,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5d3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3462,-486 3452,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18242@0" ObjectIDZND0="g_3ddb890@0" Pin0InfoVect0LinkObjId="g_3ddb890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3462,-486 3452,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_469a170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3516,-507 3516,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="18241@0" ObjectIDZND0="18242@x" ObjectIDZND1="g_356e890@0" Pin0InfoVect0LinkObjId="SW-82791_0" Pin0InfoVect1LinkObjId="g_356e890_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3516,-507 3516,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26002a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3516,-486 3516,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="18242@x" ObjectIDND1="18241@x" ObjectIDZND0="g_356e890@0" Pin0InfoVect0LinkObjId="g_356e890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82791_0" Pin1InfoVect1LinkObjId="SW-82790_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3516,-486 3516,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3541e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-571 3679,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18210@0" ObjectIDZND0="18237@1" Pin0InfoVect0LinkObjId="SW-82786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fa320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-571 3679,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c9a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-488 3661,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18236@x" ObjectIDND1="18237@x" ObjectIDZND0="18239@1" Pin0InfoVect0LinkObjId="SW-82788_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82785_0" Pin1InfoVect1LinkObjId="SW-82786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-488 3661,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3d830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3625,-488 3615,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18239@0" ObjectIDZND0="g_3ddc320@0" Pin0InfoVect0LinkObjId="g_3ddc320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82788_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3625,-488 3615,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c28b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-509 3679,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="18237@0" ObjectIDZND0="18239@x" ObjectIDZND1="18236@x" Pin0InfoVect0LinkObjId="SW-82788_0" Pin0InfoVect1LinkObjId="SW-82785_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-509 3679,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a54080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-488 3679,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="18239@x" ObjectIDND1="18237@x" ObjectIDZND0="18236@0" Pin0InfoVect0LinkObjId="SW-82785_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82788_0" Pin1InfoVect1LinkObjId="SW-82786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-488 3679,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3d5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-360 3660,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3f17830@0" ObjectIDND1="g_4370900@0" ObjectIDND2="18238@x" ObjectIDZND0="18240@1" Pin0InfoVect0LinkObjId="SW-82789_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f17830_0" Pin1InfoVect1LinkObjId="g_4370900_0" Pin1InfoVect2LinkObjId="SW-82787_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-360 3660,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f77e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3624,-360 3614,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18240@0" ObjectIDZND0="g_3ddcdb0@0" Pin0InfoVect0LinkObjId="g_3ddcdb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3624,-360 3614,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_425dc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-886 3729,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18209@0" ObjectIDZND0="18212@0" Pin0InfoVect0LinkObjId="SW-82761_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-886 3729,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35a7d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-951 3729,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="18212@1" ObjectIDZND0="18211@x" ObjectIDZND1="18214@x" Pin0InfoVect0LinkObjId="SW-82760_0" Pin0InfoVect1LinkObjId="SW-82763_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-951 3729,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_230dcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-972 3729,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="18214@x" ObjectIDND1="18212@x" ObjectIDZND0="18211@0" Pin0InfoVect0LinkObjId="SW-82760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82763_0" Pin1InfoVect1LinkObjId="SW-82761_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-972 3729,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4119a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3674,-1101 3664,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18216@0" ObjectIDZND0="g_2ab4c90@0" Pin0InfoVect0LinkObjId="g_2ab4c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3674,-1101 3664,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2384430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-972 3711,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18211@x" ObjectIDND1="18212@x" ObjectIDZND0="18214@1" Pin0InfoVect0LinkObjId="SW-82763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82760_0" Pin1InfoVect1LinkObjId="SW-82761_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-972 3711,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34a4de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3675,-972 3665,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18214@0" ObjectIDZND0="g_3dbfed0@0" Pin0InfoVect0LinkObjId="g_3dbfed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3675,-972 3665,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b418d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-832 3921,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18259@x" ObjectIDND1="18226@x" ObjectIDZND0="g_250ccd0@0" Pin0InfoVect0LinkObjId="g_250ccd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33525d0_0" Pin1InfoVect1LinkObjId="SW-82775_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-832 3921,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33525d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-832 3976,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_250ccd0@0" ObjectIDND1="18226@x" ObjectIDZND0="18259@x" Pin0InfoVect0LinkObjId="g_4370090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_250ccd0_0" Pin1InfoVect1LinkObjId="SW-82775_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-832 3976,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34cc8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-1099 3877,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2addc50@0" ObjectIDND1="18259@x" ObjectIDND2="18222@x" ObjectIDZND0="18225@1" Pin0InfoVect0LinkObjId="SW-82774_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2addc50_0" Pin1InfoVect1LinkObjId="g_33525d0_0" Pin1InfoVect2LinkObjId="SW-82771_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-1099 3877,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3351bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-917 4171,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18209@0" ObjectIDND1="18217@x" ObjectIDZND0="18219@1" Pin0InfoVect0LinkObjId="SW-82768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-82766_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-917 4171,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ed9a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-917 4125,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18219@0" ObjectIDZND0="g_29ef7d0@0" Pin0InfoVect0LinkObjId="g_29ef7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-917 4125,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a34e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-886 4190,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18209@0" ObjectIDZND0="18219@x" ObjectIDZND1="18217@x" Pin0InfoVect0LinkObjId="SW-82768_0" Pin0InfoVect1LinkObjId="SW-82766_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-886 4190,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b41230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-994 4125,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18218@0" ObjectIDZND0="g_3dc2910@0" Pin0InfoVect0LinkObjId="g_3dc2910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-994 4125,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33c59c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-993 4171,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3ff1c10@0" ObjectIDND1="g_22d72d0@0" ObjectIDND2="18217@x" ObjectIDZND0="18218@1" Pin0InfoVect0LinkObjId="SW-82767_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ff1c10_0" Pin1InfoVect1LinkObjId="g_22d72d0_0" Pin1InfoVect2LinkObjId="SW-82766_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-993 4171,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b20e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-993 4190,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="18218@x" ObjectIDND1="g_3ff1c10@0" ObjectIDND2="g_22d72d0@0" ObjectIDZND0="18217@1" Pin0InfoVect0LinkObjId="SW-82766_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-82767_0" Pin1InfoVect1LinkObjId="g_3ff1c10_0" Pin1InfoVect2LinkObjId="g_22d72d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-993 4190,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_425eca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-940 4190,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="18217@0" ObjectIDZND0="18219@x" ObjectIDZND1="18209@0" Pin0InfoVect0LinkObjId="SW-82768_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82766_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-940 4190,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_469cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-1013 4228,-1013 4228,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3ff1c10@0" ObjectIDND1="18218@x" ObjectIDND2="18217@x" ObjectIDZND0="g_22d72d0@0" Pin0InfoVect0LinkObjId="g_22d72d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ff1c10_0" Pin1InfoVect1LinkObjId="SW-82767_0" Pin1InfoVect2LinkObjId="SW-82766_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-1013 4228,-1013 4228,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_469bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-1034 4190,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3ff1c10@0" ObjectIDZND0="18218@x" ObjectIDZND1="18217@x" ObjectIDZND2="g_22d72d0@0" Pin0InfoVect0LinkObjId="SW-82767_0" Pin0InfoVect1LinkObjId="SW-82766_0" Pin0InfoVect2LinkObjId="g_22d72d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ff1c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-1034 4190,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40f57f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-1013 4190,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3ff1c10@0" ObjectIDND1="g_22d72d0@0" ObjectIDZND0="18218@x" ObjectIDZND1="18217@x" Pin0InfoVect0LinkObjId="SW-82767_0" Pin0InfoVect1LinkObjId="SW-82766_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ff1c10_0" Pin1InfoVect1LinkObjId="g_22d72d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-1013 4190,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3599d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-1083 3896,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="18222@1" ObjectIDZND0="18225@x" ObjectIDZND1="g_2addc50@0" ObjectIDZND2="18259@x" Pin0InfoVect0LinkObjId="SW-82774_0" Pin0InfoVect1LinkObjId="g_2addc50_0" Pin0InfoVect2LinkObjId="g_33525d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82771_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-1083 3896,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35a94b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-1033 3832,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18224@0" ObjectIDZND0="g_3dc13f0@0" Pin0InfoVect0LinkObjId="g_3dc13f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-1033 3832,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e14010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-1033 3878,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18220@x" ObjectIDND1="18222@x" ObjectIDZND0="18224@1" Pin0InfoVect0LinkObjId="SW-82773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82769_0" Pin1InfoVect1LinkObjId="SW-82771_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-1033 3878,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_411a770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-1016 3896,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18220@1" ObjectIDZND0="18224@x" ObjectIDZND1="18222@x" Pin0InfoVect0LinkObjId="SW-82773_0" Pin0InfoVect1LinkObjId="SW-82771_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-1016 3896,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b3cf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-1033 3896,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18220@x" ObjectIDND1="18224@x" ObjectIDZND0="18222@0" Pin0InfoVect0LinkObjId="SW-82771_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82769_0" Pin1InfoVect1LinkObjId="SW-82773_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-1033 3896,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40d84a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-1034 3666,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18215@0" ObjectIDZND0="g_3dbf440@0" Pin0InfoVect0LinkObjId="g_3dbf440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-1034 3666,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_42c8830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3730,-1034 3712,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18211@x" ObjectIDND1="18213@x" ObjectIDZND0="18215@1" Pin0InfoVect0LinkObjId="SW-82764_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82760_0" Pin1InfoVect1LinkObjId="SW-82762_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3730,-1034 3712,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a347f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1018 3729,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18211@1" ObjectIDZND0="18215@x" ObjectIDZND1="18213@x" Pin0InfoVect0LinkObjId="SW-82764_0" Pin0InfoVect1LinkObjId="SW-82762_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82760_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1018 3729,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_469c7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1034 3729,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18211@x" ObjectIDND1="18215@x" ObjectIDZND0="18213@0" Pin0InfoVect0LinkObjId="SW-82762_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82760_0" Pin1InfoVect1LinkObjId="SW-82764_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1034 3729,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4370090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-735 4023,-754 3976,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_4229b10@0" ObjectIDZND0="18259@x" ObjectIDZND1="18229@x" Pin0InfoVect0LinkObjId="g_33525d0_0" Pin0InfoVect1LinkObjId="SW-82778_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4229b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-735 4023,-754 3976,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dcd9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-332 3724,-332 3724,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18240@x" ObjectIDND1="18238@x" ObjectIDND2="g_3f17830@0" ObjectIDZND0="g_4370900@0" Pin0InfoVect0LinkObjId="g_4370900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-82789_0" Pin1InfoVect1LinkObjId="SW-82787_0" Pin1InfoVect2LinkObjId="g_3f17830_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-332 3724,-332 3724,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2653ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-1112 3896,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_2addc50@0" ObjectIDZND0="18225@x" ObjectIDZND1="18222@x" ObjectIDZND2="18259@x" Pin0InfoVect0LinkObjId="SW-82774_0" Pin0InfoVect1LinkObjId="SW-82771_0" Pin0InfoVect2LinkObjId="g_33525d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2addc50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-1112 3896,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_343f350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-850 3976,-1135 3896,-1135 3896,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="18259@1" ObjectIDZND0="g_2addc50@0" ObjectIDZND1="18225@x" ObjectIDZND2="18222@x" Pin0InfoVect0LinkObjId="g_2addc50_0" Pin0InfoVect1LinkObjId="SW-82774_0" Pin0InfoVect2LinkObjId="SW-82771_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33525d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-850 3976,-1135 3896,-1135 3896,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4098570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-1112 3896,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2addc50@0" ObjectIDND1="18259@x" ObjectIDZND0="18225@x" ObjectIDZND1="18222@x" Pin0InfoVect0LinkObjId="SW-82774_0" Pin0InfoVect1LinkObjId="SW-82771_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2addc50_0" Pin1InfoVect1LinkObjId="g_33525d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-1112 3896,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29e9c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-1101 3729,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="18216@1" ObjectIDZND0="18213@x" ObjectIDZND1="g_428a000@0" ObjectIDZND2="g_35f1830@0" Pin0InfoVect0LinkObjId="SW-82762_0" Pin0InfoVect1LinkObjId="g_428a000_0" Pin0InfoVect2LinkObjId="g_35f1830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82765_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-1101 3729,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e53b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1087 3729,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="18213@1" ObjectIDZND0="18216@x" ObjectIDZND1="g_428a000@0" ObjectIDZND2="g_35f1830@0" Pin0InfoVect0LinkObjId="SW-82765_0" Pin0InfoVect1LinkObjId="g_428a000_0" Pin0InfoVect2LinkObjId="g_35f1830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82762_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1087 3729,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f7de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1145 3771,-1152 3729,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_428a000@0" ObjectIDZND0="18216@x" ObjectIDZND1="18213@x" ObjectIDZND2="g_35f1830@0" Pin0InfoVect0LinkObjId="SW-82765_0" Pin0InfoVect1LinkObjId="SW-82762_0" Pin0InfoVect2LinkObjId="g_35f1830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_428a000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1145 3771,-1152 3729,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_230d1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1101 3729,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="18216@x" ObjectIDND1="18213@x" ObjectIDZND0="g_428a000@0" ObjectIDZND1="g_35f1830@0" Pin0InfoVect0LinkObjId="g_428a000_0" Pin0InfoVect1LinkObjId="g_35f1830_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82765_0" Pin1InfoVect1LinkObjId="SW-82762_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1101 3729,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263f8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-424 3679,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18238@1" ObjectIDZND0="18236@1" Pin0InfoVect0LinkObjId="SW-82785_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82787_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-424 3679,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af61f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-388 3679,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="18238@0" ObjectIDZND0="18240@x" ObjectIDZND1="g_3f17830@0" ObjectIDZND2="g_4370900@0" Pin0InfoVect0LinkObjId="SW-82789_0" Pin0InfoVect1LinkObjId="g_3f17830_0" Pin0InfoVect2LinkObjId="g_4370900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-388 3679,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26024d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-360 3679,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18240@x" ObjectIDND1="18238@x" ObjectIDZND0="g_3f17830@0" ObjectIDZND1="g_4370900@0" Pin0InfoVect0LinkObjId="g_3f17830_0" Pin0InfoVect1LinkObjId="g_4370900_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82789_0" Pin1InfoVect1LinkObjId="SW-82787_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-360 3679,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fa810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-332 3679,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18240@x" ObjectIDND1="18238@x" ObjectIDND2="g_4370900@0" ObjectIDZND0="g_3f17830@0" Pin0InfoVect0LinkObjId="g_3f17830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-82789_0" Pin1InfoVect1LinkObjId="SW-82787_0" Pin1InfoVect2LinkObjId="g_4370900_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-332 3679,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c1860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-287 3679,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3f17830@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f17830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-287 3679,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2adce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-571 3839,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18210@0" ObjectIDZND0="18232@1" Pin0InfoVect0LinkObjId="SW-82781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fa320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-571 3839,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2add020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-488 3821,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18231@x" ObjectIDND1="18232@x" ObjectIDZND0="18234@1" Pin0InfoVect0LinkObjId="SW-82783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82780_0" Pin1InfoVect1LinkObjId="SW-82781_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-488 3821,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db0d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3785,-488 3775,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18234@0" ObjectIDZND0="g_25e80b0@0" Pin0InfoVect0LinkObjId="g_25e80b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82783_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3785,-488 3775,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db0f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-509 3839,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="18232@0" ObjectIDZND0="18234@x" ObjectIDZND1="18231@x" Pin0InfoVect0LinkObjId="SW-82783_0" Pin0InfoVect1LinkObjId="SW-82780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-509 3839,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b6de30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-488 3839,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="18234@x" ObjectIDND1="18232@x" ObjectIDZND0="18231@0" Pin0InfoVect0LinkObjId="SW-82780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82783_0" Pin1InfoVect1LinkObjId="SW-82781_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-488 3839,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3542090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-360 3820,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2a4ba70@0" ObjectIDND1="g_25d20d0@0" ObjectIDND2="18233@x" ObjectIDZND0="18235@1" Pin0InfoVect0LinkObjId="SW-82784_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a4ba70_0" Pin1InfoVect1LinkObjId="g_25d20d0_0" Pin1InfoVect2LinkObjId="SW-82782_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-360 3820,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35422c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-360 3774,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18235@0" ObjectIDZND0="g_25e95b0@0" Pin0InfoVect0LinkObjId="g_25e95b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-360 3774,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3599770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-332 3884,-332 3884,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18235@x" ObjectIDND1="18233@x" ObjectIDND2="g_25d20d0@0" ObjectIDZND0="g_2a4ba70@0" Pin0InfoVect0LinkObjId="g_2a4ba70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-82784_0" Pin1InfoVect1LinkObjId="SW-82782_0" Pin1InfoVect2LinkObjId="g_25d20d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-332 3884,-332 3884,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40da310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-424 3839,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18233@1" ObjectIDZND0="18231@1" Pin0InfoVect0LinkObjId="SW-82780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82782_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-424 3839,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40da540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-388 3839,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="18233@0" ObjectIDZND0="18235@x" ObjectIDZND1="g_2a4ba70@0" ObjectIDZND2="g_25d20d0@0" Pin0InfoVect0LinkObjId="SW-82784_0" Pin0InfoVect1LinkObjId="g_2a4ba70_0" Pin0InfoVect2LinkObjId="g_25d20d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82782_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-388 3839,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d1ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-360 3839,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18235@x" ObjectIDND1="18233@x" ObjectIDZND0="g_2a4ba70@0" ObjectIDZND1="g_25d20d0@0" Pin0InfoVect0LinkObjId="g_2a4ba70_0" Pin0InfoVect1LinkObjId="g_25d20d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82784_0" Pin1InfoVect1LinkObjId="SW-82782_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-360 3839,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3598f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-332 3839,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18235@x" ObjectIDND1="18233@x" ObjectIDND2="g_2a4ba70@0" ObjectIDZND0="g_25d20d0@0" Pin0InfoVect0LinkObjId="g_25d20d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-82784_0" Pin1InfoVect1LinkObjId="SW-82782_0" Pin1InfoVect2LinkObjId="g_2a4ba70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-332 3839,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3599130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-287 3839,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_25d20d0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25d20d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-287 3839,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40f5aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-571 4016,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18210@0" ObjectIDZND0="18251@1" Pin0InfoVect0LinkObjId="SW-82800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fa320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-571 4016,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b36890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-487 3998,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18250@x" ObjectIDND1="18251@x" ObjectIDZND0="18253@1" Pin0InfoVect0LinkObjId="SW-82802_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82799_0" Pin1InfoVect1LinkObjId="SW-82800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-487 3998,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b36ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-487 3952,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18253@0" ObjectIDZND0="g_25ea040@0" Pin0InfoVect0LinkObjId="g_25ea040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82802_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-487 3952,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3646370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-508 4016,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="18251@0" ObjectIDZND0="18253@x" ObjectIDZND1="18250@x" Pin0InfoVect0LinkObjId="SW-82802_0" Pin0InfoVect1LinkObjId="SW-82799_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-508 4016,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b36ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-487 4016,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="18253@x" ObjectIDND1="18251@x" ObjectIDZND0="18250@0" Pin0InfoVect0LinkObjId="SW-82799_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82802_0" Pin1InfoVect1LinkObjId="SW-82800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-487 4016,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b37160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-359 3997,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_261e370@0" ObjectIDND1="g_2a66750@0" ObjectIDND2="18252@x" ObjectIDZND0="18254@1" Pin0InfoVect0LinkObjId="SW-82803_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_261e370_0" Pin1InfoVect1LinkObjId="g_2a66750_0" Pin1InfoVect2LinkObjId="SW-82801_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-359 3997,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3690700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-359 3951,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18254@0" ObjectIDZND0="g_25e8b20@0" Pin0InfoVect0LinkObjId="g_25e8b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82803_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-359 3951,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f28020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-331 4061,-331 4061,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18254@x" ObjectIDND1="18252@x" ObjectIDND2="g_2a66750@0" ObjectIDZND0="g_261e370@0" Pin0InfoVect0LinkObjId="g_261e370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-82803_0" Pin1InfoVect1LinkObjId="SW-82801_0" Pin1InfoVect2LinkObjId="g_2a66750_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-331 4061,-331 4061,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f28210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-423 4016,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18252@1" ObjectIDZND0="18250@1" Pin0InfoVect0LinkObjId="SW-82799_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82801_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-423 4016,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34dfaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-387 4016,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="18252@0" ObjectIDZND0="18254@x" ObjectIDZND1="g_261e370@0" ObjectIDZND2="g_2a66750@0" Pin0InfoVect0LinkObjId="SW-82803_0" Pin0InfoVect1LinkObjId="g_261e370_0" Pin0InfoVect2LinkObjId="g_2a66750_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82801_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-387 4016,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34dfce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-359 4016,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18254@x" ObjectIDND1="18252@x" ObjectIDZND0="g_261e370@0" ObjectIDZND1="g_2a66750@0" Pin0InfoVect0LinkObjId="g_261e370_0" Pin0InfoVect1LinkObjId="g_2a66750_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82803_0" Pin1InfoVect1LinkObjId="SW-82801_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-359 4016,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fd9f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-331 4016,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_261e370@0" ObjectIDND1="18254@x" ObjectIDND2="18252@x" ObjectIDZND0="g_2a66750@1" Pin0InfoVect0LinkObjId="g_2a66750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_261e370_0" Pin1InfoVect1LinkObjId="SW-82803_0" Pin1InfoVect2LinkObjId="SW-82801_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-331 4016,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43f9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-283 4016,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2a66750@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a66750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-283 4016,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4229ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-248 3969,-248 3969,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-248 3969,-248 3969,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b36570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,-207 3969,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_422a150@0" Pin0InfoVect0LinkObjId="g_422a150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,-207 3969,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4480110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,-151 3969,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_422a150@1" ObjectIDZND0="g_4480370@0" Pin0InfoVect0LinkObjId="g_4480370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_422a150_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,-151 3969,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a659c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-571 4209,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18210@0" ObjectIDZND0="18244@1" Pin0InfoVect0LinkObjId="SW-82793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fa320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-571 4209,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a65bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-489 4191,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18243@x" ObjectIDND1="18244@x" ObjectIDZND0="18247@1" Pin0InfoVect0LinkObjId="SW-82796_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82792_0" Pin1InfoVect1LinkObjId="SW-82793_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-489 4191,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32c3410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-489 4145,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18247@0" ObjectIDZND0="g_25eaad0@0" Pin0InfoVect0LinkObjId="g_25eaad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82796_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-489 4145,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32c3600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-510 4209,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="18244@0" ObjectIDZND0="18247@x" ObjectIDZND1="18243@x" Pin0InfoVect0LinkObjId="SW-82796_0" Pin0InfoVect1LinkObjId="SW-82792_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82793_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-510 4209,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4093ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-489 4209,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="18247@x" ObjectIDND1="18244@x" ObjectIDZND0="18243@0" Pin0InfoVect0LinkObjId="SW-82792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82796_0" Pin1InfoVect1LinkObjId="SW-82793_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-489 4209,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4093d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-361 4190,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3677130@0" ObjectIDND1="18245@x" ObjectIDND2="g_29f7860@0" ObjectIDZND0="18248@1" Pin0InfoVect0LinkObjId="SW-82797_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3677130_0" Pin1InfoVect1LinkObjId="SW-82794_0" Pin1InfoVect2LinkObjId="g_29f7860_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-361 4190,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34fd2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-361 4144,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18248@0" ObjectIDZND0="g_25eb560@0" Pin0InfoVect0LinkObjId="g_25eb560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-361 4144,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ef4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-356 4254,-356 4254,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="18248@x" ObjectIDND1="g_3677130@0" ObjectIDND2="18245@x" ObjectIDZND0="g_29f7860@0" Pin0InfoVect0LinkObjId="g_29f7860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-82797_0" Pin1InfoVect1LinkObjId="g_3677130_0" Pin1InfoVect2LinkObjId="SW-82794_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-356 4254,-356 4254,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ef6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-425 4209,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18245@1" ObjectIDZND0="18243@1" Pin0InfoVect0LinkObjId="SW-82792_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-425 4209,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43a5cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-389 4209,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="18245@0" ObjectIDZND0="18248@x" ObjectIDZND1="g_3677130@0" ObjectIDZND2="g_29f7860@0" Pin0InfoVect0LinkObjId="SW-82797_0" Pin0InfoVect1LinkObjId="g_3677130_0" Pin0InfoVect2LinkObjId="g_29f7860_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-389 4209,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e2eae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-361 4209,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18248@x" ObjectIDND1="18245@x" ObjectIDND2="g_29f7860@0" ObjectIDZND0="g_3677130@1" Pin0InfoVect0LinkObjId="g_3677130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-82797_0" Pin1InfoVect1LinkObjId="SW-82794_0" Pin1InfoVect2LinkObjId="g_29f7860_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-361 4209,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29b17a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-1009 4902,-1009 4902,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-1009 4902,-1009 4902,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_231bd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-968 4902,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_29b1a00@0" Pin0InfoVect0LinkObjId="g_29b1a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-968 4902,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_231bf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-912 4902,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_29b1a00@1" ObjectIDZND0="g_231c1e0@0" Pin0InfoVect0LinkObjId="g_231c1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b1a00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-912 4902,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3483280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-1116 4939,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3482ef0@0" Pin0InfoVect0LinkObjId="g_3482ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-1116 4939,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34834b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-1068 4939,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3482ef0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3482ef0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-1068 4939,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34dec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-970 4939,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_34de4f0@1" Pin0InfoVect0LinkObjId="g_34de4f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-970 4939,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a35ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-873 4939,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34de4f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34de4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-873 4939,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a36100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-836 4939,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-836 4939,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2507800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-796 4939,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-796 4939,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2507a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-765 4939,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-765 4939,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f77150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-1118 5088,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2507e90@0" Pin0InfoVect0LinkObjId="g_2507e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-1118 5088,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_267dd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-797 5088,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-797 5088,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_267df50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-766 5088,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-766 5088,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f55310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-836 5088,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-836 5088,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f55570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-874 5088,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3f773b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f773b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-874 5088,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23de290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-1070 5088,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2507e90@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2507e90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-1070 5088,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23de4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-971 5088,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3f773b0@1" Pin0InfoVect0LinkObjId="g_3f773b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-971 5088,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40f2fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5213,-692 5224,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5213,-692 5224,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3539590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5241,-692 5262,-692 5262,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5241,-692 5262,-692 5262,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35397f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5136,-738 5136,-692 5155,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5136,-738 5136,-692 5155,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3539a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5172,-692 5186,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5172,-692 5186,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f53890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5433,-693 5444,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5433,-693 5444,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35cb2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5461,-693 5482,-693 5482,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5461,-693 5482,-693 5482,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35cb520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5356,-740 5356,-693 5375,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5356,-740 5356,-693 5375,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35cb780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5392,-693 5406,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5392,-693 5406,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fdd0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-739 3879,-753 3864,-753 3864,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3dda3f0@0" ObjectIDZND0="18226@0" Pin0InfoVect0LinkObjId="SW-82775_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dda3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-739 3879,-753 3864,-753 3864,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fdd2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-832 3864,-832 3864,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="18259@x" ObjectIDND1="g_250ccd0@0" ObjectIDZND0="18226@1" Pin0InfoVect0LinkObjId="SW-82775_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33525d0_0" Pin1InfoVect1LinkObjId="g_250ccd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-832 3864,-832 3864,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e62b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5309,-1009 5272,-1009 5272,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5309,-1009 5272,-1009 5272,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35dd920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5272,-968 5272,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3f5dab0@0" Pin0InfoVect0LinkObjId="g_3f5dab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5272,-968 5272,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35ddb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5272,-912 5272,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_3f5dab0@1" ObjectIDZND0="g_35ddde0@0" Pin0InfoVect0LinkObjId="g_35ddde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f5dab0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5272,-912 5272,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4075710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5309,-1116 5309,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_4075250@0" Pin0InfoVect0LinkObjId="g_4075250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5309,-1116 5309,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4075940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5309,-1068 5309,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_4075250@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4075250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5309,-1068 5309,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dee610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5309,-970 5309,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3dedec0@1" Pin0InfoVect0LinkObjId="g_3dedec0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5309,-970 5309,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35331a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5309,-873 5309,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3dedec0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dedec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5309,-873 5309,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3533400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5309,-836 5309,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5309,-836 5309,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3533660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5309,-796 5309,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5309,-796 5309,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35338c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5309,-765 5309,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5309,-765 5309,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a892f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-886 4415,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18209@0" ObjectIDZND0="20430@0" Pin0InfoVect0LinkObjId="SW-101630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-886 4415,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a89550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-949 4415,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20430@1" ObjectIDZND0="20421@x" ObjectIDZND1="20432@x" Pin0InfoVect0LinkObjId="SW-101629_0" Pin0InfoVect1LinkObjId="SW-101632_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-949 4415,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40aa8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-970 4415,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20432@x" ObjectIDND1="20430@x" ObjectIDZND0="20421@0" Pin0InfoVect0LinkObjId="SW-101629_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101632_0" Pin1InfoVect1LinkObjId="SW-101630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-970 4415,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_363f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-1099 4350,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20434@0" ObjectIDZND0="g_29f0cc0@0" Pin0InfoVect0LinkObjId="g_29f0cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101634_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-1099 4350,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354c2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-970 4397,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20421@x" ObjectIDND1="20430@x" ObjectIDZND0="20432@1" Pin0InfoVect0LinkObjId="SW-101632_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101629_0" Pin1InfoVect1LinkObjId="SW-101630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-970 4397,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354c520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-970 4351,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20432@0" ObjectIDZND0="g_29f1750@0" Pin0InfoVect0LinkObjId="g_29f1750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101632_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-970 4351,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32c9810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-630 4476,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20422@x" ObjectIDND1="20435@x" ObjectIDZND0="20438@1" Pin0InfoVect0LinkObjId="SW-101639_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101635_0" Pin1InfoVect1LinkObjId="SW-101636_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-630 4476,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32c9a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4440,-630 4430,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20438@0" ObjectIDZND0="g_29f21e0@0" Pin0InfoVect0LinkObjId="g_29f21e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101639_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4440,-630 4430,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d0f370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-619 4495,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20435@1" ObjectIDZND0="20438@x" ObjectIDZND1="20422@x" Pin0InfoVect0LinkObjId="SW-101639_0" Pin0InfoVect1LinkObjId="SW-101635_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101636_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-619 4495,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d0f5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-630 4495,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20438@x" ObjectIDND1="20435@x" ObjectIDZND0="20422@0" Pin0InfoVect0LinkObjId="SW-101635_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101639_0" Pin1InfoVect1LinkObjId="SW-101636_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-630 4495,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2668000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-832 4441,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="20428@x" ObjectIDND1="20437@x" ObjectIDZND0="g_2a6a350@0" Pin0InfoVect0LinkObjId="g_2a6a350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a69e90_0" Pin1InfoVect1LinkObjId="SW-101638_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-832 4441,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a69e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-832 4496,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2a6a350@0" ObjectIDND1="20437@x" ObjectIDZND0="20428@x" Pin0InfoVect0LinkObjId="g_341bb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a6a350_0" Pin1InfoVect1LinkObjId="SW-101638_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-832 4496,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a6a0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-1099 4396,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_341c200@0" ObjectIDND1="20428@x" ObjectIDND2="20431@x" ObjectIDZND0="20434@1" Pin0InfoVect0LinkObjId="SW-101634_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_341c200_0" Pin1InfoVect1LinkObjId="g_2a69e90_0" Pin1InfoVect2LinkObjId="SW-101631_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-1099 4396,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_250f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-1083 4415,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="20431@1" ObjectIDZND0="20434@x" ObjectIDZND1="g_341c200@0" ObjectIDZND2="20428@x" Pin0InfoVect0LinkObjId="SW-101634_0" Pin0InfoVect1LinkObjId="g_341c200_0" Pin0InfoVect2LinkObjId="g_2a69e90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101631_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-1083 4415,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26817a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-1033 4351,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20433@0" ObjectIDZND0="g_29f0230@0" Pin0InfoVect0LinkObjId="g_29f0230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101633_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-1033 4351,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34804b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-1033 4397,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20421@x" ObjectIDND1="20431@x" ObjectIDZND0="20433@1" Pin0InfoVect0LinkObjId="SW-101633_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101629_0" Pin1InfoVect1LinkObjId="SW-101631_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-1033 4397,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3480710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-1016 4415,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20421@1" ObjectIDZND0="20433@x" ObjectIDZND1="20431@x" Pin0InfoVect0LinkObjId="SW-101633_0" Pin0InfoVect1LinkObjId="SW-101631_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101629_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-1016 4415,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3480970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-1033 4415,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20421@x" ObjectIDND1="20433@x" ObjectIDZND0="20431@0" Pin0InfoVect0LinkObjId="SW-101631_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101629_0" Pin1InfoVect1LinkObjId="SW-101633_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-1033 4415,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_341bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4542,-735 4542,-754 4495,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_250e6c0@0" ObjectIDZND0="20428@x" ObjectIDZND1="20436@x" Pin0InfoVect0LinkObjId="g_2a69e90_0" Pin0InfoVect1LinkObjId="SW-101637_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_250e6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4542,-735 4542,-754 4495,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec4a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4435,-1112 4415,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_341c200@0" ObjectIDZND0="20434@x" ObjectIDZND1="20431@x" ObjectIDZND2="20428@x" Pin0InfoVect0LinkObjId="SW-101634_0" Pin0InfoVect1LinkObjId="SW-101631_0" Pin0InfoVect2LinkObjId="g_2a69e90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_341c200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4435,-1112 4415,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec4cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-850 4496,-1135 4415,-1135 4415,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="20428@1" ObjectIDZND0="g_341c200@0" ObjectIDZND1="20434@x" ObjectIDZND2="20431@x" Pin0InfoVect0LinkObjId="g_341c200_0" Pin0InfoVect1LinkObjId="SW-101634_0" Pin0InfoVect2LinkObjId="SW-101631_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a69e90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-850 4496,-1135 4415,-1135 4415,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec4f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-1112 4415,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_341c200@0" ObjectIDND1="20428@x" ObjectIDZND0="20434@x" ObjectIDZND1="20431@x" Pin0InfoVect0LinkObjId="SW-101634_0" Pin0InfoVect1LinkObjId="SW-101631_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_341c200_0" Pin1InfoVect1LinkObjId="g_2a69e90_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-1112 4415,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a6cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4399,-739 4399,-753 4384,-753 4384,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3ddae40@0" ObjectIDZND0="20437@0" Pin0InfoVect0LinkObjId="SW-101638_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ddae40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4399,-739 4399,-753 4384,-753 4384,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a6d030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-832 4384,-832 4384,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="20428@x" ObjectIDND1="g_2a6a350@0" ObjectIDZND0="20437@1" Pin0InfoVect0LinkObjId="SW-101638_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a69e90_0" Pin1InfoVect1LinkObjId="g_2a6a350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-832 4384,-832 4384,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a6d290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-571 4495,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20420@0" ObjectIDZND0="20435@0" Pin0InfoVect0LinkObjId="SW-101636_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3da9770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-571 4495,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34fa320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-552 4293,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20439@1" ObjectIDZND0="18210@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101641_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4293,-552 4293,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3da9770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-552 4443,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20440@1" ObjectIDZND0="20420@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101642_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-552 4443,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad3280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-432 4293,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22eff10@0" ObjectIDZND0="20441@0" Pin0InfoVect0LinkObjId="SW-101643_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22eff10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4293,-432 4293,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad34e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-479 4293,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20441@1" ObjectIDZND0="20423@x" ObjectIDZND1="20439@x" Pin0InfoVect0LinkObjId="SW-101640_0" Pin0InfoVect1LinkObjId="SW-101641_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101643_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4293,-479 4293,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad3fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4355,-496 4293,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20423@1" ObjectIDZND0="20441@x" ObjectIDZND1="20439@x" Pin0InfoVect0LinkObjId="SW-101643_0" Pin0InfoVect1LinkObjId="SW-101641_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101640_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4355,-496 4293,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad4230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-496 4293,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20423@x" ObjectIDND1="20441@x" ObjectIDZND0="20439@0" Pin0InfoVect0LinkObjId="SW-101641_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101640_0" Pin1InfoVect1LinkObjId="SW-101643_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4293,-496 4293,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af90c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-496 4443,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20423@0" ObjectIDZND0="20440@x" ObjectIDZND1="20442@x" Pin0InfoVect0LinkObjId="SW-101642_0" Pin0InfoVect1LinkObjId="SW-101644_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-496 4443,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af9320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-496 4443,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20423@x" ObjectIDND1="20442@x" ObjectIDZND0="20440@0" Pin0InfoVect0LinkObjId="SW-101642_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101640_0" Pin1InfoVect1LinkObjId="SW-101644_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-496 4443,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af9580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-432 4443,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22f0960@0" ObjectIDZND0="20442@0" Pin0InfoVect0LinkObjId="SW-101644_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f0960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-432 4443,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af97e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-479 4443,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20442@1" ObjectIDZND0="20423@x" ObjectIDZND1="20440@x" Pin0InfoVect0LinkObjId="SW-101640_0" Pin0InfoVect1LinkObjId="SW-101642_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101644_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-479 4443,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af9a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-571 4580,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20420@0" ObjectIDZND0="20447@1" Pin0InfoVect0LinkObjId="SW-101651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3da9770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-571 4580,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fe2240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-488 4562,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20425@x" ObjectIDND1="20447@x" ObjectIDZND0="20449@1" Pin0InfoVect0LinkObjId="SW-101653_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101650_0" Pin1InfoVect1LinkObjId="SW-101651_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-488 4562,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fe24a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-488 4516,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20449@0" ObjectIDZND0="g_29f2c70@0" Pin0InfoVect0LinkObjId="g_29f2c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101653_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-488 4516,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fe2700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-509 4580,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20447@0" ObjectIDZND0="20449@x" ObjectIDZND1="20425@x" Pin0InfoVect0LinkObjId="SW-101653_0" Pin0InfoVect1LinkObjId="SW-101650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-509 4580,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f42a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-488 4580,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20449@x" ObjectIDND1="20447@x" ObjectIDZND0="20425@0" Pin0InfoVect0LinkObjId="SW-101650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101653_0" Pin1InfoVect1LinkObjId="SW-101651_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-488 4580,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fbd9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-360 4561,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3ca3c00@0" ObjectIDND1="g_32e5ea0@0" ObjectIDND2="20448@x" ObjectIDZND0="20450@1" Pin0InfoVect0LinkObjId="SW-101654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ca3c00_0" Pin1InfoVect1LinkObjId="g_32e5ea0_0" Pin1InfoVect2LinkObjId="SW-101652_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-360 4561,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fbdc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-360 4515,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20450@0" ObjectIDZND0="g_3dd8f40@0" Pin0InfoVect0LinkObjId="g_3dd8f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-360 4515,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e50a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-332 4625,-332 4625,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20450@x" ObjectIDND1="20448@x" ObjectIDND2="g_32e5ea0@0" ObjectIDZND0="g_3ca3c00@0" Pin0InfoVect0LinkObjId="g_3ca3c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-101654_0" Pin1InfoVect1LinkObjId="SW-101652_0" Pin1InfoVect2LinkObjId="g_32e5ea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-332 4625,-332 4625,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e5810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-424 4580,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20448@1" ObjectIDZND0="20425@1" Pin0InfoVect0LinkObjId="SW-101650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-424 4580,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e5a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-388 4580,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20448@0" ObjectIDZND0="20450@x" ObjectIDZND1="g_3ca3c00@0" ObjectIDZND2="g_32e5ea0@0" Pin0InfoVect0LinkObjId="SW-101654_0" Pin0InfoVect1LinkObjId="g_3ca3c00_0" Pin0InfoVect2LinkObjId="g_32e5ea0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101652_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-388 4580,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e5c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-360 4580,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20450@x" ObjectIDND1="20448@x" ObjectIDZND0="g_3ca3c00@0" ObjectIDZND1="g_32e5ea0@0" Pin0InfoVect0LinkObjId="g_3ca3c00_0" Pin0InfoVect1LinkObjId="g_32e5ea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101654_0" Pin1InfoVect1LinkObjId="SW-101652_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-360 4580,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f65960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-332 4580,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3ca3c00@0" ObjectIDND1="20450@x" ObjectIDND2="20448@x" ObjectIDZND0="g_32e5ea0@0" Pin0InfoVect0LinkObjId="g_32e5ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ca3c00_0" Pin1InfoVect1LinkObjId="SW-101654_0" Pin1InfoVect2LinkObjId="SW-101652_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-332 4580,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f65bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-287 4580,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_32e5ea0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32e5ea0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-287 4580,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db45d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-571 4748,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20420@0" ObjectIDZND0="20457@1" Pin0InfoVect0LinkObjId="SW-101663_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3da9770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-571 4748,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db47f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-488 4730,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20427@x" ObjectIDND1="20457@x" ObjectIDZND0="20459@1" Pin0InfoVect0LinkObjId="SW-101665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101662_0" Pin1InfoVect1LinkObjId="SW-101663_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-488 4730,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db4a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4694,-488 4684,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20459@0" ObjectIDZND0="g_4351040@0" Pin0InfoVect0LinkObjId="g_4351040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4694,-488 4684,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db4c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-509 4748,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20457@0" ObjectIDZND0="20459@x" ObjectIDZND1="20427@x" Pin0InfoVect0LinkObjId="SW-101665_0" Pin0InfoVect1LinkObjId="SW-101662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101663_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-509 4748,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4067a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-488 4748,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20459@x" ObjectIDND1="20457@x" ObjectIDZND0="20427@0" Pin0InfoVect0LinkObjId="SW-101662_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101665_0" Pin1InfoVect1LinkObjId="SW-101663_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-488 4748,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4067c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-360 4729,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3db5dd0@0" ObjectIDND1="g_40ac740@0" ObjectIDND2="20458@x" ObjectIDZND0="20460@1" Pin0InfoVect0LinkObjId="SW-101666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3db5dd0_0" Pin1InfoVect1LinkObjId="g_40ac740_0" Pin1InfoVect2LinkObjId="SW-101664_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-360 4729,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41794d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4693,-360 4683,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20460@0" ObjectIDZND0="g_4351ad0@0" Pin0InfoVect0LinkObjId="g_4351ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4693,-360 4683,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db76b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-332 4793,-332 4793,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20460@x" ObjectIDND1="20458@x" ObjectIDND2="g_40ac740@0" ObjectIDZND0="g_3db5dd0@0" Pin0InfoVect0LinkObjId="g_3db5dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-101666_0" Pin1InfoVect1LinkObjId="SW-101664_0" Pin1InfoVect2LinkObjId="g_40ac740_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-332 4793,-332 4793,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40ac0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-424 4748,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20458@1" ObjectIDZND0="20427@1" Pin0InfoVect0LinkObjId="SW-101662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101664_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-424 4748,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40ac2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-388 4748,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20458@0" ObjectIDZND0="20460@x" ObjectIDZND1="g_3db5dd0@0" ObjectIDZND2="g_40ac740@0" Pin0InfoVect0LinkObjId="SW-101666_0" Pin0InfoVect1LinkObjId="g_3db5dd0_0" Pin0InfoVect2LinkObjId="g_40ac740_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-388 4748,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40ac510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-360 4748,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20460@x" ObjectIDND1="20458@x" ObjectIDZND0="g_3db5dd0@0" ObjectIDZND1="g_40ac740@0" Pin0InfoVect0LinkObjId="g_3db5dd0_0" Pin0InfoVect1LinkObjId="g_40ac740_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101666_0" Pin1InfoVect1LinkObjId="SW-101664_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-360 4748,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40acdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-332 4748,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20460@x" ObjectIDND1="20458@x" ObjectIDND2="g_3db5dd0@0" ObjectIDZND0="g_40ac740@0" Pin0InfoVect0LinkObjId="g_40ac740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-101666_0" Pin1InfoVect1LinkObjId="SW-101664_0" Pin1InfoVect2LinkObjId="g_3db5dd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-332 4748,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40ad050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-287 4748,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_40ac740@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40ac740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-287 4748,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4094fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-571 4929,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20420@0" ObjectIDZND0="20451@1" Pin0InfoVect0LinkObjId="SW-101656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3da9770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-571 4929,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4095240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-488 4911,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20426@x" ObjectIDND1="20451@x" ObjectIDZND0="20453@1" Pin0InfoVect0LinkObjId="SW-101658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101655_0" Pin1InfoVect1LinkObjId="SW-101656_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-488 4911,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40954a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-488 4864,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20453@0" ObjectIDZND0="g_4352560@0" Pin0InfoVect0LinkObjId="g_4352560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-488 4864,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4095700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-509 4929,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20451@0" ObjectIDZND0="20453@x" ObjectIDZND1="20426@x" Pin0InfoVect0LinkObjId="SW-101658_0" Pin0InfoVect1LinkObjId="SW-101655_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-509 4929,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b008f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-488 4929,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20453@x" ObjectIDND1="20451@x" ObjectIDZND0="20426@0" Pin0InfoVect0LinkObjId="SW-101655_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101658_0" Pin1InfoVect1LinkObjId="SW-101656_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-488 4929,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b00b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-360 4864,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20454@0" ObjectIDZND0="g_4352ff0@0" Pin0InfoVect0LinkObjId="g_4352ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-360 4864,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4006600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-424 4929,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20452@1" ObjectIDZND0="20426@1" Pin0InfoVect0LinkObjId="SW-101655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101657_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-424 4929,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40633b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-488 5089,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20424@x" ObjectIDND1="20443@x" ObjectIDZND0="20445@1" Pin0InfoVect0LinkObjId="SW-101648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101645_0" Pin1InfoVect1LinkObjId="SW-101646_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-488 5089,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40635a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5053,-488 5043,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20445@0" ObjectIDZND0="g_4353a80@0" Pin0InfoVect0LinkObjId="g_4353a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5053,-488 5043,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4063790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-509 5107,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20443@0" ObjectIDZND0="20445@x" ObjectIDZND1="20424@x" Pin0InfoVect0LinkObjId="SW-101648_0" Pin0InfoVect1LinkObjId="SW-101645_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101646_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-509 5107,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4065970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-488 5107,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20445@x" ObjectIDND1="20443@x" ObjectIDZND0="20424@0" Pin0InfoVect0LinkObjId="SW-101645_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101648_0" Pin1InfoVect1LinkObjId="SW-101646_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-488 5107,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4065bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-360 5088,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_412a3b0@0" ObjectIDND1="g_362c6c0@0" ObjectIDND2="20444@x" ObjectIDZND0="20446@1" Pin0InfoVect0LinkObjId="SW-101649_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_412a3b0_0" Pin1InfoVect1LinkObjId="g_362c6c0_0" Pin1InfoVect2LinkObjId="SW-101647_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-360 5088,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4065e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5052,-360 5042,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20446@0" ObjectIDZND0="g_4354fa0@0" Pin0InfoVect0LinkObjId="g_4354fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5052,-360 5042,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_412bf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-332 5152,-332 5152,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20446@x" ObjectIDND1="20444@x" ObjectIDND2="g_362c6c0@0" ObjectIDZND0="g_412a3b0@0" Pin0InfoVect0LinkObjId="g_412a3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-101649_0" Pin1InfoVect1LinkObjId="SW-101647_0" Pin1InfoVect2LinkObjId="g_362c6c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-332 5152,-332 5152,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_412c140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-424 5107,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20444@1" ObjectIDZND0="20424@1" Pin0InfoVect0LinkObjId="SW-101645_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101647_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-424 5107,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_412c330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-388 5107,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20444@0" ObjectIDZND0="20446@x" ObjectIDZND1="g_412a3b0@0" ObjectIDZND2="g_362c6c0@0" Pin0InfoVect0LinkObjId="SW-101649_0" Pin0InfoVect1LinkObjId="g_412a3b0_0" Pin0InfoVect2LinkObjId="g_362c6c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-388 5107,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_412c520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-360 5107,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20446@x" ObjectIDND1="20444@x" ObjectIDZND0="g_412a3b0@0" ObjectIDZND1="g_362c6c0@0" Pin0InfoVect0LinkObjId="g_412a3b0_0" Pin0InfoVect1LinkObjId="g_362c6c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101649_0" Pin1InfoVect1LinkObjId="SW-101647_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-360 5107,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_362d140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-332 5107,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20446@x" ObjectIDND1="20444@x" ObjectIDND2="g_412a3b0@0" ObjectIDZND0="g_362c6c0@1" Pin0InfoVect0LinkObjId="g_362c6c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-101649_0" Pin1InfoVect1LinkObjId="SW-101647_0" Pin1InfoVect2LinkObjId="g_412a3b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-332 5107,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_362d3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-284 5107,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_362c6c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_362c6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-284 5107,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250a620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-249 5060,-249 5060,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-249 5060,-249 5060,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_250b840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-208 5060,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_250a880@0" Pin0InfoVect0LinkObjId="g_250a880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-208 5060,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_250baa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-152 5060,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_250a880@1" ObjectIDZND0="g_250bd00@0" Pin0InfoVect0LinkObjId="g_250bd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_250a880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-152 5060,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26216a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-571 5107,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20420@0" ObjectIDZND0="20443@1" Pin0InfoVect0LinkObjId="SW-101646_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3da9770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-571 5107,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2623060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5270,-571 5270,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20420@0" ObjectIDZND0="20461@1" Pin0InfoVect0LinkObjId="SW-101667_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3da9770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5270,-571 5270,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26232a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5270,-486 5252,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3df6680@0" ObjectIDND1="20461@x" ObjectIDZND0="20462@1" Pin0InfoVect0LinkObjId="SW-101668_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3df6680_0" Pin1InfoVect1LinkObjId="SW-101667_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5270,-486 5252,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2623500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5216,-486 5206,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20462@0" ObjectIDZND0="g_4354510@0" Pin0InfoVect0LinkObjId="g_4354510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101668_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5216,-486 5206,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2623760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5270,-507 5270,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="20461@0" ObjectIDZND0="20462@x" ObjectIDZND1="g_3df6680@0" Pin0InfoVect0LinkObjId="SW-101668_0" Pin0InfoVect1LinkObjId="g_3df6680_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5270,-507 5270,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26239c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5270,-486 5270,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="20462@x" ObjectIDND1="20461@x" ObjectIDZND0="g_3df6680@0" Pin0InfoVect0LinkObjId="g_3df6680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101668_0" Pin1InfoVect1LinkObjId="SW-101667_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5270,-486 5270,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab1e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-343 4929,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3e9c360@1" ObjectIDZND0="20452@0" Pin0InfoVect0LinkObjId="SW-101657_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e9c360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-343 4929,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab2070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-360 4929,-360 4929,-355 4974,-355 4974,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20454@1" ObjectIDZND0="g_4004a60@0" Pin0InfoVect0LinkObjId="g_4004a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101659_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-360 4929,-360 4929,-355 4974,-355 4974,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab3720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-770 3976,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="18259@0" ObjectIDZND0="g_4229b10@0" ObjectIDZND1="18229@x" Pin0InfoVect0LinkObjId="g_4229b10_0" Pin0InfoVect1LinkObjId="SW-82778_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33525d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-770 3976,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab3910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-754 3976,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_4229b10@0" ObjectIDND1="18259@x" ObjectIDZND0="18229@1" Pin0InfoVect0LinkObjId="SW-82778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4229b10_0" Pin1InfoVect1LinkObjId="g_33525d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-754 3976,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab3b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-670 3976,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18227@1" ObjectIDZND0="18229@0" Pin0InfoVect0LinkObjId="SW-82778_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82776_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-670 3976,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab3d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-691 4495,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20436@0" ObjectIDZND0="20422@1" Pin0InfoVect0LinkObjId="SW-101635_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101637_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-691 4495,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab47d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-770 4495,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="20428@0" ObjectIDZND0="g_250e6c0@0" ObjectIDZND1="20436@x" Pin0InfoVect0LinkObjId="g_250e6c0_0" Pin0InfoVect1LinkObjId="SW-101637_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a69e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-770 4495,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab4a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-755 4495,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_250e6c0@0" ObjectIDND1="20428@x" ObjectIDZND0="20436@1" Pin0InfoVect0LinkObjId="SW-101637_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_250e6c0_0" Pin1InfoVect1LinkObjId="g_2a69e90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-755 4495,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3665c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-310 4209,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3677130@0" ObjectIDZND0="18246@1" Pin0InfoVect0LinkObjId="SW-82795_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3677130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-310 4209,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3665e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4236,-240 4209,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="18249@0" ObjectIDZND0="18246@x" ObjectIDZND1="g_3d46e60@0" Pin0InfoVect0LinkObjId="SW-82795_0" Pin0InfoVect1LinkObjId="g_3d46e60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82798_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4236,-240 4209,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3666960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-256 4209,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="18246@0" ObjectIDZND0="18249@x" ObjectIDZND1="g_3d46e60@0" Pin0InfoVect0LinkObjId="SW-82798_0" Pin0InfoVect1LinkObjId="g_3d46e60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-82795_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-256 4209,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3666bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-240 4209,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18249@x" ObjectIDND1="18246@x" ObjectIDZND0="g_3d46e60@0" Pin0InfoVect0LinkObjId="g_3d46e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-82798_0" Pin1InfoVect1LinkObjId="SW-82795_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-240 4209,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35efea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-309 4929,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3e9c360@0" ObjectIDZND0="20455@1" Pin0InfoVect0LinkObjId="SW-101660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e9c360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-309 4929,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f0100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-238 4929,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="20456@0" ObjectIDZND0="20455@x" ObjectIDZND1="g_3e9d510@0" Pin0InfoVect0LinkObjId="SW-101660_0" Pin0InfoVect1LinkObjId="g_3e9d510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-238 4929,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f0bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-253 4929,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="20455@0" ObjectIDZND0="20456@x" ObjectIDZND1="g_3e9d510@0" Pin0InfoVect0LinkObjId="SW-101661_0" Pin0InfoVect1LinkObjId="g_3e9d510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-101660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-253 4929,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f0e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-238 4929,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="20456@x" ObjectIDND1="20455@x" ObjectIDZND0="g_3e9d510@0" Pin0InfoVect0LinkObjId="g_3e9d510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-101661_0" Pin1InfoVect1LinkObjId="SW-101660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-238 4929,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f4320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3690,-1167 3729,-1167 3729,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_35f1830@0" ObjectIDZND0="18216@x" ObjectIDZND1="18213@x" ObjectIDZND2="g_428a000@0" Pin0InfoVect0LinkObjId="SW-82765_0" Pin0InfoVect1LinkObjId="SW-82762_0" Pin0InfoVect2LinkObjId="g_428a000_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f1830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3690,-1167 3729,-1167 3729,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f4580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-1183 3729,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="18216@x" ObjectIDZND1="18213@x" ObjectIDZND2="g_428a000@0" Pin0InfoVect0LinkObjId="SW-82765_0" Pin0InfoVect1LinkObjId="SW-82762_0" Pin0InfoVect2LinkObjId="g_428a000_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-1183 3729,-1152 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4939" cy="-738" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5088" cy="-738" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5136" cy="-738" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5482" cy="-741" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5262" cy="-740" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5356" cy="-740" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5309" cy="-740" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18209" cx="4190" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18209" cx="3896" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18210" cx="3976" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18210" cx="3516" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18209" cx="3729" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18210" cx="3679" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18210" cx="3839" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18210" cx="4016" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18210" cx="4209" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18209" cx="4415" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18210" cx="4293" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20420" cx="4495" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20420" cx="4443" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20420" cx="4580" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20420" cx="4748" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20420" cx="4929" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20420" cx="5107" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20420" cx="5270" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-82719" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3435.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18197" ObjectName="DYN-CX_YTS"/>
     <cge:Meas_Ref ObjectId="82719"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b5d5a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3382.000000 -564.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25fe120" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3623.000000 -178.000000) translate(0,15)">云台山Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a804e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3609.500000 -157.000000) translate(0,15)">(1-17号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3521950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -1010.000000) translate(0,12)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_469b950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3674.000000 -996.000000) translate(0,12)">16117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41b3fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3673.500000 -1059.000000) translate(0,12)">16160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4373070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -938.000000) translate(0,12)">1611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4186b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3734.500000 -1076.000000) translate(0,12)">1616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42c97f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3673.500000 -1125.000000) translate(0,12)">K1617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f4990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4133.000000 -943.000000) translate(0,12)">K1900</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23841f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4197.000000 -965.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e7810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4133.000000 -1020.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3c1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.000000 -1010.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3faa260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3903.000000 -938.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f4500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3841.000000 -996.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4384da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3840.500000 -1059.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e8440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3840.500000 -1125.000000) translate(0,12)">K1017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b2340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3902.500000 -1076.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3621f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4006.000000 -825.000000) translate(0,12)">#1主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_428aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3930.500000 -713.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2653840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3987.000000 -664.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40fe850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -656.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4288ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3983.000000 -608.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d0560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.000000 -878.000000) translate(0,12)">110kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3435110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3476.500000 -530.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3620e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3456.500000 -478.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e635d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.500000 -463.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40d94c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3683.500000 -535.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f4f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3621.500000 -511.000000) translate(0,12)">38417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de68d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3683.000000 -413.000000) translate(0,12)">3846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b41690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3621.000000 -384.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4119c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3fc32c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3fc32c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3fc32c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3fc32c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3fc32c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3fc32c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3fc32c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3fc9160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -1165.500000) translate(0,16)">云台山升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,215,0)" font-family="SimSun" font-size="15" graphid="g_2a04180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -799.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fc6fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3823.000000 -792.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3c6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.500000 -1202.000000) translate(0,12)">紫南云线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_326e530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -821.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_326e530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -821.000000) translate(0,27)">SZ11-50000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_326e530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -821.000000) translate(0,42)">115±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_326e530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -821.000000) translate(0,57)">50000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_326e530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -821.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_326e530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -821.000000) translate(0,87)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a4af60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3470.000000 -366.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a4af60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3470.000000 -366.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3621130" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3789.000000 -175.000000) translate(0,15)">云台山Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a4b820" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3780.000000 -154.000000) translate(0,15)">(18-33号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_359c1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.500000 -463.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42a5f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.500000 -535.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42a6180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3781.500000 -511.000000) translate(0,12)">38317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4372ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.000000 -413.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4372ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3781.000000 -384.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cb270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.500000 -462.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c1b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.500000 -534.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3958.500000 -510.000000) translate(0,12)">38217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3340460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -412.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33406a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3958.000000 -383.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3520d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3928.000000 -231.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3521260" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3980.000000 -159.000000) translate(0,15)">35kV1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a65770" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4038.000000 -251.000000) translate(0,15)">750kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3312cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.500000 -464.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3313170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.500000 -536.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33133b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.500000 -512.000000) translate(0,12)">38117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ef060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 -414.000000) translate(0,12)">3813</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ef290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -385.000000) translate(0,12)">38137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e2ed20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -233.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d46c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4167.000000 -287.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ce48d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4122.500000 -171.000000) translate(0,15)">1号静止无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce4c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4148.500000 -150.000000) translate(0,12)">-16MVar~16MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25f2170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 -992.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25f2630" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4854.000000 -1043.000000) translate(0,15)">1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34836e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4876.000000 -1163.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34de310" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4889.500000 -1141.000000) translate(0,15)">1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23de750" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5018.000000 -1164.000000) translate(0,15)">10kV云台山分支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2afc5b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5036.000000 -1142.000000) translate(0,15)">蒿子菁分支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2afc7e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5112.000000 -1014.000000) translate(0,15)">备用所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2afc7e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5112.000000 -1014.000000) translate(0,33)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35cbf00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4892.000000 -732.000000) translate(0,15)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f53af0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5217.000000 -783.000000) translate(0,15)">0.4kVⅡ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f53af0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5217.000000 -783.000000) translate(0,33)">段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a168b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5460.000000 -763.000000) translate(0,15)">0.4kV生活段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33d1440" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3259.000000 -229.000000) translate(0,15)">4892</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33d1680" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3259.000000 -203.000000) translate(0,15)">0878-6144337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f5e850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5231.000000 -992.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4075050" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5224.000000 -1043.000000) translate(0,15)">2号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4075b70" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5246.000000 -1163.000000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3dedc80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5259.500000 -1141.000000) translate(0,15)">2号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3480bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -1010.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339e460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4422.000000 -938.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339e6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -996.000000) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339e8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.500000 -1059.000000) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339eb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.500000 -1125.000000) translate(0,12)">K1027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339ed60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.500000 -1076.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339efa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4525.000000 -825.000000) translate(0,12)">#2主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339f1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.500000 -713.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341b460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4506.000000 -664.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4438.000000 -656.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341b8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -608.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,215,0)" font-family="SimSun" font-size="15" graphid="g_341bd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4532.000000 -800.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341bfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -792.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3616e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -821.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3616e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -821.000000) translate(0,27)">SZ11-50000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3616e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -821.000000) translate(0,42)">115±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3616e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -821.000000) translate(0,57)">50000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3616e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -821.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3616e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -821.000000) translate(0,87)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca2fd0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4530.000000 -175.000000) translate(0,15)">瓦窑Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca3970" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4521.000000 -154.000000) translate(0,15)">(17-33号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce2890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4588.500000 -463.000000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce2d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4584.500000 -535.000000) translate(0,12)">3862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce2f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.500000 -511.000000) translate(0,12)">38627</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce31d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4584.000000 -413.000000) translate(0,12)">3866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce3410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -384.000000) translate(0,12)">38667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4008560" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4698.000000 -175.000000) translate(0,15)">瓦窑Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3db5b80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4689.000000 -154.000000) translate(0,15)">(1-16号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db68f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4756.500000 -463.000000) translate(0,12)">387</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db6db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4752.500000 -535.000000) translate(0,12)">3872</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db6ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4690.500000 -511.000000) translate(0,12)">38727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db7230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4752.000000 -413.000000) translate(0,12)">3876</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db7470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4690.000000 -384.000000) translate(0,12)">38767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4005810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4937.500000 -463.000000) translate(0,12)">388</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4005d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.500000 -535.000000) translate(0,12)">3882</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4005f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.500000 -511.000000) translate(0,12)">38827</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4006180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -413.000000) translate(0,12)">3883</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40063c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.000000 -384.000000) translate(0,12)">38837</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e9cde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -231.000000) translate(0,12)">38867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e9d2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -278.000000) translate(0,12)">3886</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40627c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.500000 -149.000000) translate(0,12)">-16MVar~16MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40629f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4842.500000 -170.000000) translate(0,15)">2号静止无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_412b160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5115.500000 -463.000000) translate(0,12)">389</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_412b650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5111.500000 -535.000000) translate(0,12)">3892</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_412b890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.500000 -511.000000) translate(0,12)">38927</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_412bad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5111.000000 -413.000000) translate(0,12)">3896</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_412bd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.000000 -384.000000) translate(0,12)">38967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2620410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5019.000000 -232.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2620900" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5071.000000 -160.000000) translate(0,15)">35kV2号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2620b50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5129.000000 -252.000000) translate(0,15)">750kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df5f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5230.500000 -530.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df6440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5210.500000 -478.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ab1210" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5224.000000 -366.000000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ab1210" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5224.000000 -366.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab2260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.500000 -486.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab26a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4298.500000 -539.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab28e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4401.500000 -539.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab2b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4296.500000 -461.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab2d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4395.500000 -461.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b05810" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5294.000000 -564.000000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4247520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3420.000000 -1161.000000) translate(0,16)">已退运</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4933,-271 4976,-271 4976,-243 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4213,-273 4256,-273 4256,-245 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-82770">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18221" ObjectName="SW-CX_YTS.CX_YTS_1011SW"/>
     <cge:Meas_Ref ObjectId="82770"/>
    <cge:TPSR_Ref TObjectID="18221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82771">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -1042.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18222" ObjectName="SW-CX_YTS.CX_YTS_1016SW"/>
     <cge:Meas_Ref ObjectId="82771"/>
    <cge:TPSR_Ref TObjectID="18222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82774">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3850.000000 -1073.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18225" ObjectName="SW-CX_YTS.CX_YTS_K1017SW"/>
     <cge:Meas_Ref ObjectId="82774"/>
    <cge:TPSR_Ref TObjectID="18225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82772">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18223" ObjectName="SW-CX_YTS.CX_YTS_10117SW"/>
     <cge:Meas_Ref ObjectId="82772"/>
    <cge:TPSR_Ref TObjectID="18223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82778">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3967.000000 -688.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18229" ObjectName="SW-CX_YTS.CX_YTS_3016SW"/>
     <cge:Meas_Ref ObjectId="82778"/>
    <cge:TPSR_Ref TObjectID="18229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82777">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3967.000000 -582.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18228" ObjectName="SW-CX_YTS.CX_YTS_3011SW"/>
     <cge:Meas_Ref ObjectId="82777"/>
    <cge:TPSR_Ref TObjectID="18228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82779">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 -604.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18230" ObjectName="SW-CX_YTS.CX_YTS_30117SW"/>
     <cge:Meas_Ref ObjectId="82779"/>
    <cge:TPSR_Ref TObjectID="18230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82791">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3471.000000 -460.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18242" ObjectName="SW-CX_YTS.CX_YTS_39017SW"/>
     <cge:Meas_Ref ObjectId="82791"/>
    <cge:TPSR_Ref TObjectID="18242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82790">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3507.000000 -502.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18241" ObjectName="SW-CX_YTS.CX_YTS_3901SW"/>
     <cge:Meas_Ref ObjectId="82790"/>
    <cge:TPSR_Ref TObjectID="18241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82788">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.250000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18239" ObjectName="SW-CX_YTS.CX_YTS_38417SW"/>
     <cge:Meas_Ref ObjectId="82788"/>
    <cge:TPSR_Ref TObjectID="18239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82789">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3633.250000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18240" ObjectName="SW-CX_YTS.CX_YTS_38467SW"/>
     <cge:Meas_Ref ObjectId="82789"/>
    <cge:TPSR_Ref TObjectID="18240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82761">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -910.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18212" ObjectName="SW-CX_YTS.CX_YTS_1611SW"/>
     <cge:Meas_Ref ObjectId="82761"/>
    <cge:TPSR_Ref TObjectID="18212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82762">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -1046.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18213" ObjectName="SW-CX_YTS.CX_YTS_1616SW"/>
     <cge:Meas_Ref ObjectId="82762"/>
    <cge:TPSR_Ref TObjectID="18213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82765">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3683.000000 -1075.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18216" ObjectName="SW-CX_YTS.CX_YTS_K1617SW"/>
     <cge:Meas_Ref ObjectId="82765"/>
    <cge:TPSR_Ref TObjectID="18216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82763">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.000000 -946.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18214" ObjectName="SW-CX_YTS.CX_YTS_16117SW"/>
     <cge:Meas_Ref ObjectId="82763"/>
    <cge:TPSR_Ref TObjectID="18214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82768">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -891.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18219" ObjectName="SW-CX_YTS.CX_YTS_K1900SW"/>
     <cge:Meas_Ref ObjectId="82768"/>
    <cge:TPSR_Ref TObjectID="18219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82767">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -967.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18218" ObjectName="SW-CX_YTS.CX_YTS_19017SW"/>
     <cge:Meas_Ref ObjectId="82767"/>
    <cge:TPSR_Ref TObjectID="18218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82766">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -935.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18217" ObjectName="SW-CX_YTS.CX_YTS_1901SW"/>
     <cge:Meas_Ref ObjectId="82766"/>
    <cge:TPSR_Ref TObjectID="18217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82773">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18224" ObjectName="SW-CX_YTS.CX_YTS_10160SW"/>
     <cge:Meas_Ref ObjectId="82773"/>
    <cge:TPSR_Ref TObjectID="18224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82764">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -1008.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18215" ObjectName="SW-CX_YTS.CX_YTS_16160SW"/>
     <cge:Meas_Ref ObjectId="82764"/>
    <cge:TPSR_Ref TObjectID="18215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82786">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3670.000000 -504.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18237" ObjectName="SW-CX_YTS.CX_YTS_3841SW"/>
     <cge:Meas_Ref ObjectId="82786"/>
    <cge:TPSR_Ref TObjectID="18237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82787">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3670.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18238" ObjectName="SW-CX_YTS.CX_YTS_3846SW"/>
     <cge:Meas_Ref ObjectId="82787"/>
    <cge:TPSR_Ref TObjectID="18238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82783">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3794.250000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18234" ObjectName="SW-CX_YTS.CX_YTS_38317SW"/>
     <cge:Meas_Ref ObjectId="82783"/>
    <cge:TPSR_Ref TObjectID="18234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82784">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.250000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18235" ObjectName="SW-CX_YTS.CX_YTS_38367SW"/>
     <cge:Meas_Ref ObjectId="82784"/>
    <cge:TPSR_Ref TObjectID="18235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82781">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -504.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18232" ObjectName="SW-CX_YTS.CX_YTS_3831SW"/>
     <cge:Meas_Ref ObjectId="82781"/>
    <cge:TPSR_Ref TObjectID="18232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82782">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18233" ObjectName="SW-CX_YTS.CX_YTS_3836SW"/>
     <cge:Meas_Ref ObjectId="82782"/>
    <cge:TPSR_Ref TObjectID="18233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82802">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.250000 -461.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18253" ObjectName="SW-CX_YTS.CX_YTS_38217SW"/>
     <cge:Meas_Ref ObjectId="82802"/>
    <cge:TPSR_Ref TObjectID="18253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82803">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.250000 -333.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18254" ObjectName="SW-CX_YTS.CX_YTS_38267SW"/>
     <cge:Meas_Ref ObjectId="82803"/>
    <cge:TPSR_Ref TObjectID="18254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82800">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -503.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18251" ObjectName="SW-CX_YTS.CX_YTS_3821SW"/>
     <cge:Meas_Ref ObjectId="82800"/>
    <cge:TPSR_Ref TObjectID="18251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82801">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18252" ObjectName="SW-CX_YTS.CX_YTS_3826SW"/>
     <cge:Meas_Ref ObjectId="82801"/>
    <cge:TPSR_Ref TObjectID="18252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82796">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.250000 -463.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18247" ObjectName="SW-CX_YTS.CX_YTS_38117SW"/>
     <cge:Meas_Ref ObjectId="82796"/>
    <cge:TPSR_Ref TObjectID="18247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82797">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.250000 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18248" ObjectName="SW-CX_YTS.CX_YTS_38137SW"/>
     <cge:Meas_Ref ObjectId="82797"/>
    <cge:TPSR_Ref TObjectID="18248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82793">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 -505.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18244" ObjectName="SW-CX_YTS.CX_YTS_3811SW"/>
     <cge:Meas_Ref ObjectId="82793"/>
    <cge:TPSR_Ref TObjectID="18244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82794">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 -384.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18245" ObjectName="SW-CX_YTS.CX_YTS_3813SW"/>
     <cge:Meas_Ref ObjectId="82794"/>
    <cge:TPSR_Ref TObjectID="18245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4893.000000 -963.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -829.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -758.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5078.000000 -759.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5078.000000 -829.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5152.000000 -682.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5220.000000 -682.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5371.000000 -683.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5440.000000 -683.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82775">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 -767.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18226" ObjectName="SW-CX_YTS.CX_YTS_1010SW"/>
     <cge:Meas_Ref ObjectId="82775"/>
    <cge:TPSR_Ref TObjectID="18226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5263.000000 -963.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5299.000000 -829.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5299.000000 -758.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101630">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20430" ObjectName="SW-CX_YTS.CX_YTS_1021SW"/>
     <cge:Meas_Ref ObjectId="101630"/>
    <cge:TPSR_Ref TObjectID="20430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101631">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -1042.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20431" ObjectName="SW-CX_YTS.CX_YTS_1026SW"/>
     <cge:Meas_Ref ObjectId="101631"/>
    <cge:TPSR_Ref TObjectID="20431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101634">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -1073.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20434" ObjectName="SW-CX_YTS.CX_YTS_K1027SW"/>
     <cge:Meas_Ref ObjectId="101634"/>
    <cge:TPSR_Ref TObjectID="20434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101632">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20432" ObjectName="SW-CX_YTS.CX_YTS_10217SW"/>
     <cge:Meas_Ref ObjectId="101632"/>
    <cge:TPSR_Ref TObjectID="20432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101637">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -686.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20436" ObjectName="SW-CX_YTS.CX_YTS_3026SW"/>
     <cge:Meas_Ref ObjectId="101637"/>
    <cge:TPSR_Ref TObjectID="20436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101636">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -582.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20435" ObjectName="SW-CX_YTS.CX_YTS_3022SW"/>
     <cge:Meas_Ref ObjectId="101636"/>
    <cge:TPSR_Ref TObjectID="20435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101639">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -604.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20438" ObjectName="SW-CX_YTS.CX_YTS_30227SW"/>
     <cge:Meas_Ref ObjectId="101639"/>
    <cge:TPSR_Ref TObjectID="20438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101633">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20433" ObjectName="SW-CX_YTS.CX_YTS_10260SW"/>
     <cge:Meas_Ref ObjectId="101633"/>
    <cge:TPSR_Ref TObjectID="20433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101638">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -767.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20437" ObjectName="SW-CX_YTS.CX_YTS_1020SW"/>
     <cge:Meas_Ref ObjectId="101638"/>
    <cge:TPSR_Ref TObjectID="20437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101641">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.000000 -511.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20439" ObjectName="SW-CX_YTS.CX_YTS_3121SW"/>
     <cge:Meas_Ref ObjectId="101641"/>
    <cge:TPSR_Ref TObjectID="20439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101642">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -511.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20440" ObjectName="SW-CX_YTS.CX_YTS_3122SW"/>
     <cge:Meas_Ref ObjectId="101642"/>
    <cge:TPSR_Ref TObjectID="20440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101643">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.000000 -438.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20441" ObjectName="SW-CX_YTS.CX_YTS_31217SW"/>
     <cge:Meas_Ref ObjectId="101643"/>
    <cge:TPSR_Ref TObjectID="20441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101644">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -438.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20442" ObjectName="SW-CX_YTS.CX_YTS_31227SW"/>
     <cge:Meas_Ref ObjectId="101644"/>
    <cge:TPSR_Ref TObjectID="20442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101653">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4535.250000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20449" ObjectName="SW-CX_YTS.CX_YTS_38627SW"/>
     <cge:Meas_Ref ObjectId="101653"/>
    <cge:TPSR_Ref TObjectID="20449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.250000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20450" ObjectName="SW-CX_YTS.CX_YTS_38667SW"/>
     <cge:Meas_Ref ObjectId="101654"/>
    <cge:TPSR_Ref TObjectID="20450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101651">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 -504.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20447" ObjectName="SW-CX_YTS.CX_YTS_3862SW"/>
     <cge:Meas_Ref ObjectId="101651"/>
    <cge:TPSR_Ref TObjectID="20447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101652">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20448" ObjectName="SW-CX_YTS.CX_YTS_3866SW"/>
     <cge:Meas_Ref ObjectId="101652"/>
    <cge:TPSR_Ref TObjectID="20448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4703.250000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20459" ObjectName="SW-CX_YTS.CX_YTS_38727SW"/>
     <cge:Meas_Ref ObjectId="101665"/>
    <cge:TPSR_Ref TObjectID="20459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4702.250000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20460" ObjectName="SW-CX_YTS.CX_YTS_38767SW"/>
     <cge:Meas_Ref ObjectId="101666"/>
    <cge:TPSR_Ref TObjectID="20460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101663">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 -504.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20457" ObjectName="SW-CX_YTS.CX_YTS_3872SW"/>
     <cge:Meas_Ref ObjectId="101663"/>
    <cge:TPSR_Ref TObjectID="20457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101664">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20458" ObjectName="SW-CX_YTS.CX_YTS_3876SW"/>
     <cge:Meas_Ref ObjectId="101664"/>
    <cge:TPSR_Ref TObjectID="20458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101658">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.250000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20453" ObjectName="SW-CX_YTS.CX_YTS_38827SW"/>
     <cge:Meas_Ref ObjectId="101658"/>
    <cge:TPSR_Ref TObjectID="20453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.250000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20454" ObjectName="SW-CX_YTS.CX_YTS_38837SW"/>
     <cge:Meas_Ref ObjectId="101659"/>
    <cge:TPSR_Ref TObjectID="20454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.000000 -504.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20451" ObjectName="SW-CX_YTS.CX_YTS_3882SW"/>
     <cge:Meas_Ref ObjectId="101656"/>
    <cge:TPSR_Ref TObjectID="20451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101657">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20452" ObjectName="SW-CX_YTS.CX_YTS_3883SW"/>
     <cge:Meas_Ref ObjectId="101657"/>
    <cge:TPSR_Ref TObjectID="20452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101648">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5062.250000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20445" ObjectName="SW-CX_YTS.CX_YTS_38927SW"/>
     <cge:Meas_Ref ObjectId="101648"/>
    <cge:TPSR_Ref TObjectID="20445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101649">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5061.250000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20446" ObjectName="SW-CX_YTS.CX_YTS_38967SW"/>
     <cge:Meas_Ref ObjectId="101649"/>
    <cge:TPSR_Ref TObjectID="20446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101646">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5098.000000 -504.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20443" ObjectName="SW-CX_YTS.CX_YTS_3892SW"/>
     <cge:Meas_Ref ObjectId="101646"/>
    <cge:TPSR_Ref TObjectID="20443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101647">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5098.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20444" ObjectName="SW-CX_YTS.CX_YTS_3896SW"/>
     <cge:Meas_Ref ObjectId="101647"/>
    <cge:TPSR_Ref TObjectID="20444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 -203.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101668">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5225.000000 -460.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20462" ObjectName="SW-CX_YTS.CX_YTS_39027SW"/>
     <cge:Meas_Ref ObjectId="101668"/>
    <cge:TPSR_Ref TObjectID="20462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101667">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5261.000000 -502.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20461" ObjectName="SW-CX_YTS.CX_YTS_3902SW"/>
     <cge:Meas_Ref ObjectId="101667"/>
    <cge:TPSR_Ref TObjectID="20461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82798">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4231.000000 -233.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18249" ObjectName="SW-CX_YTS.CX_YTS_38167SW"/>
     <cge:Meas_Ref ObjectId="82798"/>
    <cge:TPSR_Ref TObjectID="18249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4955.000000 -231.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20456" ObjectName="SW-CX_YTS.CX_YTS_38867SW"/>
     <cge:Meas_Ref ObjectId="101661"/>
    <cge:TPSR_Ref TObjectID="20456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-82795">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4218.000000 -251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18246" ObjectName="SW-CX_YTS.CX_YTS_3816SW"/>
     <cge:Meas_Ref ObjectId="82795"/>
    <cge:TPSR_Ref TObjectID="18246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-101660">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4938.000000 -248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20455" ObjectName="SW-CX_YTS.CX_YTS_3886SW"/>
     <cge:Meas_Ref ObjectId="101660"/>
    <cge:TPSR_Ref TObjectID="20455"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YTS.CX_YTS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="25393"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -765.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -765.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="18259" ObjectName="TF-CX_YTS.CX_YTS_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4000.000000 -167.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4000.000000 -167.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -928.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -928.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -929.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -929.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5293.000000 -928.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5293.000000 -928.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YTS.CX_YTS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28450"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4461.000000 -765.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4461.000000 -765.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20428" ObjectName="TF-CX_YTS.CX_YTS_2T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5091.000000 -168.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5091.000000 -168.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116472" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3249.538462 -1013.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116472" ObjectName="CX_YTS:CX_YTS_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116473" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3247.538462 -971.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116473" ObjectName="CX_YTS:CX_YTS_sumQ"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="42" opacity="0" stroke="white" transform="" width="164" x="3246" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2653a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3521.000000 1026.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250de90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3510.000000 1011.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42c9350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3535.000000 996.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3435a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3499.000000 937.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41af9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3499.000000 952.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250da30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3491.000000 909.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e631c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3499.000000 923.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34551b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3417.000000 623.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ca120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3417.000000 638.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348eb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3409.000000 595.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3435350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3417.000000 609.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3673790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3598.000000 121.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a17470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3587.000000 106.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3612.000000 91.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447d100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3769.000000 120.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a24750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 105.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a24960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.000000 90.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a24d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3955.000000 122.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a25040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 107.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a25280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 92.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40aff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 120.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e98a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 105.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e98cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 90.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2620f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 123.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2621220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5035.000000 108.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2621460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 93.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329ceb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.000000 120.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329d100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 101.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329d430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4127.000000 121.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329d690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 102.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329d9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5203.000000 620.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329dc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5203.000000 635.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329de70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5195.000000 592.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329e0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5203.000000 606.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YTS.CX_YTS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3464,-571 4329,-571 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18210" ObjectName="BS-CX_YTS.CX_YTS_3IM"/>
    <cge:TPSR_Ref TObjectID="18210"/></metadata>
   <polyline fill="none" opacity="0" points="3464,-571 4329,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YTS.CX_YTS_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3533,-886 4660,-886 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18209" ObjectName="BS-CX_YTS.CX_YTS_1IM"/>
    <cge:TPSR_Ref TObjectID="18209"/></metadata>
   <polyline fill="none" opacity="0" points="3533,-886 4660,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-738 5167,-738 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4889,-738 5167,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5231,-740 5392,-740 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5231,-740 5392,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5450,-741 5560,-741 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5450,-741 5560,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YTS.CX_YTS_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-571 5339,-571 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20420" ObjectName="BS-CX_YTS.CX_YTS_3IIM"/>
    <cge:TPSR_Ref TObjectID="20420"/></metadata>
   <polyline fill="none" opacity="0" points="4410,-571 5339,-571 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-82743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.000000 -800.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18259"/>
     <cge:Term_Ref ObjectID="25394"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-82732" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3581.000000 -1026.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18211"/>
     <cge:Term_Ref ObjectID="25299"/>
    <cge:TPSR_Ref TObjectID="18211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-82733" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3581.000000 -1026.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82733" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18211"/>
     <cge:Term_Ref ObjectID="25299"/>
    <cge:TPSR_Ref TObjectID="18211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-82734" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3581.000000 -1026.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18211"/>
     <cge:Term_Ref ObjectID="25299"/>
    <cge:TPSR_Ref TObjectID="18211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-82735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -954.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18209"/>
     <cge:Term_Ref ObjectID="25297"/>
    <cge:TPSR_Ref TObjectID="18209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-82736" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -954.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18209"/>
     <cge:Term_Ref ObjectID="25297"/>
    <cge:TPSR_Ref TObjectID="18209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-82737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -954.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18209"/>
     <cge:Term_Ref ObjectID="25297"/>
    <cge:TPSR_Ref TObjectID="18209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-82738" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -954.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82738" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18209"/>
     <cge:Term_Ref ObjectID="25297"/>
    <cge:TPSR_Ref TObjectID="18209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-82740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 -1018.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18220"/>
     <cge:Term_Ref ObjectID="25317"/>
    <cge:TPSR_Ref TObjectID="18220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-82741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 -1018.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18220"/>
     <cge:Term_Ref ObjectID="25317"/>
    <cge:TPSR_Ref TObjectID="18220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-82742" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 -1018.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82742" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18220"/>
     <cge:Term_Ref ObjectID="25317"/>
    <cge:TPSR_Ref TObjectID="18220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4048.000000 -682.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18227"/>
     <cge:Term_Ref ObjectID="25331"/>
    <cge:TPSR_Ref TObjectID="18227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83392" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4048.000000 -682.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18227"/>
     <cge:Term_Ref ObjectID="25331"/>
    <cge:TPSR_Ref TObjectID="18227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83393" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4048.000000 -682.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18227"/>
     <cge:Term_Ref ObjectID="25331"/>
    <cge:TPSR_Ref TObjectID="18227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-82750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -638.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18210"/>
     <cge:Term_Ref ObjectID="25298"/>
    <cge:TPSR_Ref TObjectID="18210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-82751" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -638.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82751" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18210"/>
     <cge:Term_Ref ObjectID="25298"/>
    <cge:TPSR_Ref TObjectID="18210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-82752" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -638.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18210"/>
     <cge:Term_Ref ObjectID="25298"/>
    <cge:TPSR_Ref TObjectID="18210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-82753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -638.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18210"/>
     <cge:Term_Ref ObjectID="25298"/>
    <cge:TPSR_Ref TObjectID="18210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-82747" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -122.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82747" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18236"/>
     <cge:Term_Ref ObjectID="25349"/>
    <cge:TPSR_Ref TObjectID="18236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-82748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -122.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18236"/>
     <cge:Term_Ref ObjectID="25349"/>
    <cge:TPSR_Ref TObjectID="18236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-82749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -122.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18236"/>
     <cge:Term_Ref ObjectID="25349"/>
    <cge:TPSR_Ref TObjectID="18236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-82744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3825.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18231"/>
     <cge:Term_Ref ObjectID="25339"/>
    <cge:TPSR_Ref TObjectID="18231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-82745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3825.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18231"/>
     <cge:Term_Ref ObjectID="25339"/>
    <cge:TPSR_Ref TObjectID="18231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-82746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3825.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18231"/>
     <cge:Term_Ref ObjectID="25339"/>
    <cge:TPSR_Ref TObjectID="18231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-82757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18250"/>
     <cge:Term_Ref ObjectID="25377"/>
    <cge:TPSR_Ref TObjectID="18250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-82758" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82758" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18250"/>
     <cge:Term_Ref ObjectID="25377"/>
    <cge:TPSR_Ref TObjectID="18250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-82759" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -121.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82759" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18250"/>
     <cge:Term_Ref ObjectID="25377"/>
    <cge:TPSR_Ref TObjectID="18250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-82755" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82755" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18243"/>
     <cge:Term_Ref ObjectID="25363"/>
    <cge:TPSR_Ref TObjectID="18243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-82756" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18243"/>
     <cge:Term_Ref ObjectID="25363"/>
    <cge:TPSR_Ref TObjectID="18243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-101391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -800.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20428"/>
     <cge:Term_Ref ObjectID="28451"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-101383" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -1024.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101383" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20421"/>
     <cge:Term_Ref ObjectID="28434"/>
    <cge:TPSR_Ref TObjectID="20421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-101384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -1024.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20421"/>
     <cge:Term_Ref ObjectID="28434"/>
    <cge:TPSR_Ref TObjectID="20421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-101385" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -1024.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20421"/>
     <cge:Term_Ref ObjectID="28434"/>
    <cge:TPSR_Ref TObjectID="20421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-101387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -677.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20422"/>
     <cge:Term_Ref ObjectID="28436"/>
    <cge:TPSR_Ref TObjectID="20422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-101388" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -677.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20422"/>
     <cge:Term_Ref ObjectID="28436"/>
    <cge:TPSR_Ref TObjectID="20422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-101389" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -677.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20422"/>
     <cge:Term_Ref ObjectID="28436"/>
    <cge:TPSR_Ref TObjectID="20422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-101397" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -467.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20423"/>
     <cge:Term_Ref ObjectID="28438"/>
    <cge:TPSR_Ref TObjectID="20423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-101398" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -467.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20423"/>
     <cge:Term_Ref ObjectID="28438"/>
    <cge:TPSR_Ref TObjectID="20423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-101399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -467.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20423"/>
     <cge:Term_Ref ObjectID="28438"/>
    <cge:TPSR_Ref TObjectID="20423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-101405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20425"/>
     <cge:Term_Ref ObjectID="28442"/>
    <cge:TPSR_Ref TObjectID="20425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-101406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20425"/>
     <cge:Term_Ref ObjectID="28442"/>
    <cge:TPSR_Ref TObjectID="20425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-101407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -121.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20425"/>
     <cge:Term_Ref ObjectID="28442"/>
    <cge:TPSR_Ref TObjectID="20425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-101412" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101412" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20427"/>
     <cge:Term_Ref ObjectID="28446"/>
    <cge:TPSR_Ref TObjectID="20427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-101413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20427"/>
     <cge:Term_Ref ObjectID="28446"/>
    <cge:TPSR_Ref TObjectID="20427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-101414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -121.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20427"/>
     <cge:Term_Ref ObjectID="28446"/>
    <cge:TPSR_Ref TObjectID="20427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-101409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4915.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20426"/>
     <cge:Term_Ref ObjectID="28444"/>
    <cge:TPSR_Ref TObjectID="20426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-101410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4915.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20426"/>
     <cge:Term_Ref ObjectID="28444"/>
    <cge:TPSR_Ref TObjectID="20426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-101401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -125.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20424"/>
     <cge:Term_Ref ObjectID="28440"/>
    <cge:TPSR_Ref TObjectID="20424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-101402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -125.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20424"/>
     <cge:Term_Ref ObjectID="28440"/>
    <cge:TPSR_Ref TObjectID="20424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-101403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -125.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20424"/>
     <cge:Term_Ref ObjectID="28440"/>
    <cge:TPSR_Ref TObjectID="20424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-101392" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5288.000000 -638.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20420"/>
     <cge:Term_Ref ObjectID="28433"/>
    <cge:TPSR_Ref TObjectID="20420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-101393" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5288.000000 -638.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20420"/>
     <cge:Term_Ref ObjectID="28433"/>
    <cge:TPSR_Ref TObjectID="20420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-101394" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5288.000000 -638.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20420"/>
     <cge:Term_Ref ObjectID="28433"/>
    <cge:TPSR_Ref TObjectID="20420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-101395" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5288.000000 -638.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="101395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20420"/>
     <cge:Term_Ref ObjectID="28433"/>
    <cge:TPSR_Ref TObjectID="20420"/></metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3ff1c10">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 -1032.000000)" xlink:href="#lightningRod:shape131"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d72d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4221.000000 -1014.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_428a000">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -1087.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_250ccd0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3914.000000 -760.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4229b10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 -677.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4370900">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -262.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2addc50">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 -1106.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f17830">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -282.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4ba70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3877.000000 -262.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d20d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -282.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261e370">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -261.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a66750">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 -278.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_422a150">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3963.000000 -146.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f7860">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -286.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3677130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 -305.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d46e60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.000000 -176.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b1a00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4896.000000 -907.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3482ef0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4933.000000 -1063.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34de4f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4934.000000 -868.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2507e90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5082.000000 -1065.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f773b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5083.000000 -869.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f5dab0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5266.000000 -907.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4075250">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5303.000000 -1063.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dedec0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5304.000000 -868.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a6a350">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -760.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_250e6c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4535.000000 -677.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_341c200">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 -1106.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ca3c00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4618.000000 -262.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32e5ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 -282.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3db5dd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 -262.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40ac740">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 -282.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4004a60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4967.000000 -285.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e9c360">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4919.000000 -304.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e9d510">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4912.000000 -175.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_412a3b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5145.000000 -262.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_362c6c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 -279.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_250a880">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.000000 -147.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YTS"/>
</svg>