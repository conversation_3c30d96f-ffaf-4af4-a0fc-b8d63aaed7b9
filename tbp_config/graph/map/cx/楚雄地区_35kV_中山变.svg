<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-69" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1199 1777 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="17" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="52" x2="52" y1="52" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="26" x2="26" y1="16" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="43" x2="43" y1="2" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="8" x2="8" y1="2" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="63" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="64" y2="64"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="32"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.501492" x1="26" x2="26" y1="74" y2="24"/>
    <polyline arcFlag="1" points="43,42 44,42 45,42 45,42 46,43 46,43 47,44 47,44 48,45 48,45 48,46 48,47 49,48 49,49 49,49 48,50 48,51 48,52 48,52 47,53 47,53 46,54 46,54 45,55 45,55 44,55 43,55 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,29 44,29 45,29 45,29 46,30 46,30 47,31 47,31 48,32 48,32 48,33 48,34 49,35 49,36 49,36 48,37 48,38 48,39 48,39 47,40 47,40 46,41 46,41 45,42 45,42 44,42 43,42 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,17 44,17 45,17 45,17 46,18 46,18 47,19 47,19 48,20 48,20 48,21 48,22 49,23 49,24 49,24 48,25 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,30 45,30 44,30 43,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="63" y2="55"/>
   </symbol>
   <symbol id="lightningRod:shape74">
    <circle cx="39" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <circle cx="30" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="71" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="31" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="24" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="8" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,17 39,15 45,15 43,18 " stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f28fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f299d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f2a340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f2b300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f2c560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f2d180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f2dbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f2e660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f2ef30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f2f820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f304d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2f30c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f32680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f33200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f33af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f343d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f35ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f36570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc3db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f377a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f38920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f392a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f39d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f3ef90" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f3fc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f3ba10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f3ce60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f3d9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2f40d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2f421d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1787" x="3112" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="4892" x2="4892" y1="-288" y2="-271"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-96496">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -288.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7429" ObjectName="SW-CX_ZS.CX_ZS_052BK"/>
     <cge:Meas_Ref ObjectId="96496"/>
    <cge:TPSR_Ref TObjectID="7429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96505">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -287.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7426" ObjectName="SW-CX_ZS.CX_ZS_057BK"/>
     <cge:Meas_Ref ObjectId="96505"/>
    <cge:TPSR_Ref TObjectID="7426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96499">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4260.000000 -284.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7421" ObjectName="SW-CX_ZS.CX_ZS_055BK"/>
     <cge:Meas_Ref ObjectId="96499"/>
    <cge:TPSR_Ref TObjectID="7421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96502">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -283.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7418" ObjectName="SW-CX_ZS.CX_ZS_056BK"/>
     <cge:Meas_Ref ObjectId="96502"/>
    <cge:TPSR_Ref TObjectID="7418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -286.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7409" ObjectName="SW-CX_ZS.CX_ZS_054BK"/>
     <cge:Meas_Ref ObjectId="96511"/>
    <cge:TPSR_Ref TObjectID="7409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -872.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7437" ObjectName="SW-CX_ZS.CX_ZS_351BK"/>
     <cge:Meas_Ref ObjectId="96524"/>
    <cge:TPSR_Ref TObjectID="7437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -871.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7441" ObjectName="SW-CX_ZS.CX_ZS_352BK"/>
     <cge:Meas_Ref ObjectId="96523"/>
    <cge:TPSR_Ref TObjectID="7441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -668.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7431" ObjectName="SW-CX_ZS.CX_ZS_301BK"/>
     <cge:Meas_Ref ObjectId="96515"/>
    <cge:TPSR_Ref TObjectID="7431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96514">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -491.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7406" ObjectName="SW-CX_ZS.CX_ZS_001BK"/>
     <cge:Meas_Ref ObjectId="96514"/>
    <cge:TPSR_Ref TObjectID="7406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96508">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3892.000000 -283.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7404" ObjectName="SW-CX_ZS.CX_ZS_053BK"/>
     <cge:Meas_Ref ObjectId="96508"/>
    <cge:TPSR_Ref TObjectID="7404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-44114">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.000000 -288.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7415" ObjectName="SW-CX_ZS.CX_ZS_058BK"/>
     <cge:Meas_Ref ObjectId="44114"/>
    <cge:TPSR_Ref TObjectID="7415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96493">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -289.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7412" ObjectName="SW-CX_ZS.CX_ZS_051BK"/>
     <cge:Meas_Ref ObjectId="96493"/>
    <cge:TPSR_Ref TObjectID="7412"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZS.CX_ZS_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3510,-395 4841,-395 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20089" ObjectName="BS-CX_ZS.CX_ZS_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="20089"/></metadata>
   <polyline fill="none" opacity="0" points="3510,-395 4841,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZS.CX_ZS_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3715,-805 4570,-805 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20088" ObjectName="BS-CX_ZS.CX_ZS_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="20088"/></metadata>
   <polyline fill="none" opacity="0" points="3715,-805 4570,-805 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2fd9060" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 -968.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb85f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4405.000000 -968.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31029f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 -778.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_29835b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3835,-784 3813,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20088@0" ObjectIDND1="7433@x" ObjectIDZND0="7432@1" Pin0InfoVect0LinkObjId="SW-96518_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ea7090_0" Pin1InfoVect1LinkObjId="SW-96520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3835,-784 3813,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3104380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-784 3759,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7432@0" ObjectIDZND0="g_31029f0@0" Pin0InfoVect0LinkObjId="g_31029f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96518_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-784 3759,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28475c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-395 3726,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7427@1" Pin0InfoVect0LinkObjId="SW-96498_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-395 3726,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2be1850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-338 3726,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7427@0" ObjectIDZND0="7429@1" Pin0InfoVect0LinkObjId="SW-96496_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96498_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-338 3726,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30fd5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-296 3726,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7429@0" ObjectIDZND0="7428@1" Pin0InfoVect0LinkObjId="SW-96497_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96496_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-296 3726,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_311b1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-395 4606,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7424@1" Pin0InfoVect0LinkObjId="SW-96507_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-395 4606,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30b2560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-335 4606,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7424@0" ObjectIDZND0="7426@1" Pin0InfoVect0LinkObjId="SW-96505_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96507_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-335 4606,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea6c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-295 4604,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7426@0" ObjectIDZND0="7425@1" Pin0InfoVect0LinkObjId="SW-96506_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96505_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-295 4604,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d8f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-395 4445,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7416@1" Pin0InfoVect0LinkObjId="SW-96503_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-395 4445,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4522070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-333 4445,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7416@0" ObjectIDZND0="7418@1" Pin0InfoVect0LinkObjId="SW-96502_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-333 4445,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4338a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-291 4445,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7418@0" ObjectIDZND0="7417@1" Pin0InfoVect0LinkObjId="SW-96504_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96502_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-291 4445,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ac8a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-395 4077,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7407@1" Pin0InfoVect0LinkObjId="SW-96513_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-395 4077,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ebce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-336 4077,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7407@0" ObjectIDZND0="7409@1" Pin0InfoVect0LinkObjId="SW-96511_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96513_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-336 4077,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d839b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-294 4077,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7409@0" ObjectIDZND0="7408@1" Pin0InfoVect0LinkObjId="SW-96512_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-294 4077,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44957c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4055,-974 4074,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7435@1" ObjectIDZND0="g_2fd9060@0" Pin0InfoVect0LinkObjId="g_2fd9060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96522_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4055,-974 4074,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4520f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-974 3997,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="7435@0" ObjectIDZND0="g_4331220@0" ObjectIDZND1="g_3f1d170@0" ObjectIDZND2="7436@x" Pin0InfoVect0LinkObjId="g_4331220_0" Pin0InfoVect1LinkObjId="g_3f1d170_0" Pin0InfoVect2LinkObjId="SW-96528_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96522_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-974 3997,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc8bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-1007 4029,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f1d170@0" ObjectIDND1="7435@x" ObjectIDND2="7436@x" ObjectIDZND0="g_4331220@0" Pin0InfoVect0LinkObjId="g_4331220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f1d170_0" Pin1InfoVect1LinkObjId="SW-96522_0" Pin1InfoVect2LinkObjId="SW-96528_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-1007 4029,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc4b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-1007 3997,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_4331220@0" ObjectIDND1="g_3f1d170@0" ObjectIDND2="7435@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4331220_0" Pin1InfoVect1LinkObjId="g_3f1d170_0" Pin1InfoVect2LinkObjId="SW-96522_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-1007 3997,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4424020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-1007 3948,-1007 3948,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_4331220@0" ObjectIDND1="7435@x" ObjectIDND2="7436@x" ObjectIDZND0="g_3f1d170@0" Pin0InfoVect0LinkObjId="g_3f1d170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4331220_0" Pin1InfoVect1LinkObjId="SW-96522_0" Pin1InfoVect2LinkObjId="SW-96528_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-1007 3948,-1007 3948,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fa97a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-974 4409,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7439@1" ObjectIDZND0="g_3fb85f0@0" Pin0InfoVect0LinkObjId="g_3fb85f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96521_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-974 4409,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c24770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4354,-974 4332,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="7439@0" ObjectIDZND0="g_2c23ca0@0" ObjectIDZND1="g_3f8d950@0" ObjectIDZND2="7440@x" Pin0InfoVect0LinkObjId="g_2c23ca0_0" Pin0InfoVect1LinkObjId="g_3f8d950_0" Pin0InfoVect2LinkObjId="SW-96526_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96521_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4354,-974 4332,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4aafbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1007 4364,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c23ca0@0" ObjectIDND1="7439@x" ObjectIDND2="7440@x" ObjectIDZND0="g_3f8d950@0" Pin0InfoVect0LinkObjId="g_3f8d950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c23ca0_0" Pin1InfoVect1LinkObjId="SW-96521_0" Pin1InfoVect2LinkObjId="SW-96526_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1007 4364,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e69d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1007 4332,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_2c23ca0@0" ObjectIDND1="g_3f8d950@0" ObjectIDND2="7439@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c23ca0_0" Pin1InfoVect1LinkObjId="g_3f8d950_0" Pin1InfoVect2LinkObjId="SW-96521_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1007 4332,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d488f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1007 4283,-1007 4283,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f8d950@0" ObjectIDND1="7439@x" ObjectIDND2="7440@x" ObjectIDZND0="g_2c23ca0@0" Pin0InfoVect0LinkObjId="g_2c23ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f8d950_0" Pin1InfoVect1LinkObjId="SW-96521_0" Pin1InfoVect2LinkObjId="SW-96526_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1007 4283,-1007 4283,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3068b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3836,-805 3836,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20088@0" ObjectIDZND0="7432@x" ObjectIDZND1="7433@x" Pin0InfoVect0LinkObjId="SW-96518_0" Pin0InfoVect1LinkObjId="SW-96520_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ea7090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3836,-805 3836,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e7f9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-395 4013,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7423@0" Pin0InfoVect0LinkObjId="SW-96519_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-395 4013,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d63a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-479 3980,-479 3980,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7423@x" ObjectIDND1="g_41291b0@0" ObjectIDZND0="g_34ac840@0" Pin0InfoVect0LinkObjId="g_34ac840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96519_0" Pin1InfoVect1LinkObjId="g_41291b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-479 3980,-479 3980,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_314dab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-479 4013,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_34ac840@0" ObjectIDND1="g_41291b0@0" ObjectIDZND0="7423@1" Pin0InfoVect0LinkObjId="SW-96519_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34ac840_0" Pin1InfoVect1LinkObjId="g_41291b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-479 4013,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fb3a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-479 4013,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_34ac840@0" ObjectIDND1="7423@x" ObjectIDZND0="g_41291b0@1" Pin0InfoVect0LinkObjId="g_41291b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34ac840_0" Pin1InfoVect1LinkObjId="SW-96519_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-479 4013,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bcbda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-543 4014,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_41291b0@0" ObjectIDZND0="g_3e7e600@0" Pin0InfoVect0LinkObjId="g_3e7e600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41291b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-543 4014,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f03f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-243 4077,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7408@0" ObjectIDZND0="g_3f99b60@0" Pin0InfoVect0LinkObjId="g_3f99b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-243 4077,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3eff400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-395 3901,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7402@1" Pin0InfoVect0LinkObjId="SW-96510_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-395 3901,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea3590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-333 3901,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7402@0" ObjectIDZND0="7404@1" Pin0InfoVect0LinkObjId="SW-96508_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-333 3901,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b7c640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-291 3901,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7404@0" ObjectIDZND0="7403@1" Pin0InfoVect0LinkObjId="SW-96509_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96508_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-291 3901,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b7f310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-240 3901,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7403@0" ObjectIDZND0="g_3f99f90@0" Pin0InfoVect0LinkObjId="g_3f99f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-240 3901,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f8fee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-395 4780,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7413@1" Pin0InfoVect0LinkObjId="SW-44112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-395 4780,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f9a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-336 4780,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7413@0" ObjectIDZND0="7415@1" Pin0InfoVect0LinkObjId="SW-44114_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-336 4780,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d8e520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4778,-296 4778,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7415@0" ObjectIDZND0="7414@1" Pin0InfoVect0LinkObjId="SW-44113_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44114_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4778,-296 4778,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f91b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4778,-243 4778,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="7414@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44113_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4778,-243 4778,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f927b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-224 3757,-224 3757,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7428@x" ObjectIDZND0="g_30cfa30@0" Pin0InfoVect0LinkObjId="g_30cfa30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-224 3757,-224 3757,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d4e550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-245 3726,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7428@0" ObjectIDZND0="g_30cfa30@0" Pin0InfoVect0LinkObjId="g_30cfa30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-245 3726,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d4e760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-224 3726,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="7428@x" ObjectIDND1="g_30cfa30@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96497_0" Pin1InfoVect1LinkObjId="g_30cfa30_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-224 3726,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f1cf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,-223 4301,-223 4301,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7420@x" ObjectIDZND0="g_30d67d0@0" Pin0InfoVect0LinkObjId="g_30d67d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,-223 4301,-223 4301,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3eebaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-224 4478,-224 4478,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7417@x" ObjectIDZND0="g_33c6c30@0" Pin0InfoVect0LinkObjId="g_33c6c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96504_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-224 4478,-224 4478,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c24ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-224 4635,-224 4635,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7425@x" ObjectIDZND0="g_33c81b0@0" Pin0InfoVect0LinkObjId="g_33c81b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-224 4635,-224 4635,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f93850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-242 4604,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7425@0" ObjectIDZND0="g_33c81b0@0" Pin0InfoVect0LinkObjId="g_33c81b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-242 4604,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c23280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-224 4604,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="7425@x" ObjectIDND1="g_33c81b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96506_0" Pin1InfoVect1LinkObjId="g_33c81b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-224 4604,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c25400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,-292 4269,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7421@0" ObjectIDZND0="7420@1" Pin0InfoVect0LinkObjId="SW-96500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96499_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,-292 4269,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f9aad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,-395 4269,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7419@1" Pin0InfoVect0LinkObjId="SW-96501_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,-395 4269,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cae170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,-337 4269,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7419@0" ObjectIDZND0="7421@1" Pin0InfoVect0LinkObjId="SW-96499_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96501_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,-337 4269,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30df1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-395 3570,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7410@1" Pin0InfoVect0LinkObjId="SW-96495_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-395 3570,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30d3ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-339 3570,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7410@0" ObjectIDZND0="7412@1" Pin0InfoVect0LinkObjId="SW-96493_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96495_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-339 3570,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30d1bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-297 3570,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7412@0" ObjectIDZND0="7411@1" Pin0InfoVect0LinkObjId="SW-96494_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96493_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-297 3570,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30d26d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-225 3601,-225 3601,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7411@x" ObjectIDZND0="g_30d2900@0" Pin0InfoVect0LinkObjId="g_30d2900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96494_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-225 3601,-225 3601,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30d6fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-246 3570,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7411@0" ObjectIDZND0="g_30d2900@0" Pin0InfoVect0LinkObjId="g_30d2900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96494_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-246 3570,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3319400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-225 3570,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="7411@x" ObjectIDND1="g_30d2900@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96494_0" Pin1InfoVect1LinkObjId="g_30d2900_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-225 3570,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea7090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-772 4333,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7430@1" ObjectIDZND0="20088@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96517_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-772 4333,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fddd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-703 4333,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7431@1" ObjectIDZND0="7430@0" Pin0InfoVect0LinkObjId="SW-96517_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96515_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-703 4333,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334a800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-647 4333,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="7442@1" ObjectIDZND0="7431@0" Pin0InfoVect0LinkObjId="SW-96515_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d80ca0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-647 4333,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_452b250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-395 4333,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20089@0" ObjectIDZND0="7405@0" Pin0InfoVect0LinkObjId="SW-96516_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-395 4333,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4474080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-461 4333,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7405@1" ObjectIDZND0="7406@0" Pin0InfoVect0LinkObjId="SW-96514_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96516_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-461 4333,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d80ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-526 4333,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="7406@1" ObjectIDZND0="7442@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-526 4333,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4532bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-974 3997,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7435@x" ObjectIDND1="7436@x" ObjectIDZND0="g_4331220@0" ObjectIDZND1="g_3f1d170@0" Pin0InfoVect0LinkObjId="g_4331220_0" Pin0InfoVect1LinkObjId="g_3f1d170_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96522_0" Pin1InfoVect1LinkObjId="SW-96528_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-974 3997,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d56800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-805 3997,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20088@0" ObjectIDZND0="7434@0" Pin0InfoVect0LinkObjId="SW-96527_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ea7090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-805 3997,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4129b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-859 3997,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7434@1" ObjectIDZND0="7437@0" Pin0InfoVect0LinkObjId="SW-96524_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96527_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-859 3997,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_313e660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-907 3997,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7437@1" ObjectIDZND0="7436@0" Pin0InfoVect0LinkObjId="SW-96528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96524_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-907 3997,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4461810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-959 3997,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="7436@1" ObjectIDZND0="7435@x" ObjectIDZND1="g_4331220@0" ObjectIDZND2="g_3f1d170@0" Pin0InfoVect0LinkObjId="SW-96522_0" Pin0InfoVect1LinkObjId="g_4331220_0" Pin0InfoVect2LinkObjId="g_3f1d170_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96528_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-959 3997,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_328e460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-974 4332,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7439@x" ObjectIDND1="7440@x" ObjectIDZND0="g_2c23ca0@0" ObjectIDZND1="g_3f8d950@0" Pin0InfoVect0LinkObjId="g_2c23ca0_0" Pin0InfoVect1LinkObjId="g_3f8d950_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96521_0" Pin1InfoVect1LinkObjId="SW-96526_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-974 4332,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_328e650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-805 4332,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20088@0" ObjectIDZND0="7438@0" Pin0InfoVect0LinkObjId="SW-96525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ea7090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-805 4332,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32408b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-857 4332,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7438@1" ObjectIDZND0="7441@0" Pin0InfoVect0LinkObjId="SW-96523_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-857 4332,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_413cc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-906 4332,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7441@1" ObjectIDZND0="7440@0" Pin0InfoVect0LinkObjId="SW-96526_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96523_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-906 4332,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e2fef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-959 4332,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="7440@1" ObjectIDZND0="7439@x" ObjectIDZND1="g_2c23ca0@0" ObjectIDZND2="g_3f8d950@0" Pin0InfoVect0LinkObjId="SW-96521_0" Pin0InfoVect1LinkObjId="g_2c23ca0_0" Pin0InfoVect2LinkObjId="g_3f8d950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96526_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-959 4332,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4abaaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3836,-784 3836,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="7432@x" ObjectIDND1="20088@0" ObjectIDZND0="7433@1" Pin0InfoVect0LinkObjId="SW-96520_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-96518_0" Pin1InfoVect1LinkObjId="g_3ea7090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3836,-784 3836,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e6a830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3836,-729 3836,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7433@0" ObjectIDZND0="g_2e6aa20@0" Pin0InfoVect0LinkObjId="g_2e6aa20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-96520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3836,-729 3836,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fba5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,-115 4269,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_30d67d0@0" ObjectIDZND1="7420@x" Pin0InfoVect0LinkObjId="g_30d67d0_0" Pin0InfoVect1LinkObjId="SW-96500_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4269,-115 4269,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fba7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,-223 4269,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30d67d0@0" ObjectIDZND0="7420@0" Pin0InfoVect0LinkObjId="SW-96500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30d67d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,-223 4269,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f84410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-115 4445,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_33c6c30@0" ObjectIDZND1="7417@x" Pin0InfoVect0LinkObjId="g_33c6c30_0" Pin0InfoVect1LinkObjId="SW-96504_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-115 4445,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f84650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-224 4445,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_33c6c30@0" ObjectIDZND0="7417@0" Pin0InfoVect0LinkObjId="SW-96504_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c6c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-224 4445,-240 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="3726" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="4606" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="4445" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="4077" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="4013" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="3901" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="4780" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="4269" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="4333" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20089" cx="3570" cy="-395" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20088" cx="3836" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20088" cx="4333" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20088" cx="3997" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20088" cx="4332" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37338" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3410.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5920" ObjectName="DYN-CX_ZS"/>
     <cge:Meas_Ref ObjectId="37338"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30b3460" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3788.000000 -626.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_465dc90" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3972.000000 -1156.000000) translate(0,15)">杜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_465dc90" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3972.000000 -1156.000000) translate(0,33)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_465dc90" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3972.000000 -1156.000000) translate(0,51)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_465dc90" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3972.000000 -1156.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_311bca0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3546.000000 -144.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_311bca0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3546.000000 -144.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_311bca0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3546.000000 -144.000000) translate(0,51)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4415770" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4025.000000 -121.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bdf3d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3965.000000 -604.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bdf610" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3917.000000 -916.000000) translate(0,15)">35kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bdf610" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3917.000000 -916.000000) translate(0,33)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4029470" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4303.000000 -1155.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4029470" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4303.000000 -1155.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2885e50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4255.000000 -916.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2da2760" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3703.000000 -144.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2da2760" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3703.000000 -144.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2da2760" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3703.000000 -144.000000) translate(0,51)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b7c8a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3849.000000 -121.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f91d60" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4243.000000 -144.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f91d60" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4243.000000 -144.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f91d60" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4243.000000 -144.000000) translate(0,51)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f93680" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4420.000000 -144.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f93680" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4420.000000 -144.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f93680" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4420.000000 -144.000000) translate(0,51)">四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f94ae0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4581.000000 -144.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f94ae0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4581.000000 -144.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f94ae0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4581.000000 -144.000000) translate(0,51)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_453afd0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4752.000000 -144.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_453afd0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4752.000000 -144.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_453afd0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4752.000000 -144.000000) translate(0,51)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d80f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4011.000000 -903.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_334aa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4008.000000 -848.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_452b4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -999.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4461a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4008.000000 -948.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32289d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -900.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e30120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 -948.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45636a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -1000.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e68c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.000000 -754.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e6ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3775.000000 -777.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f761e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -444.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d7e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 -846.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d8050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4342.000000 -697.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22c1e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -761.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22c2080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4342.000000 -520.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_433a8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -450.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433aaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -636.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43343f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.000000 -364.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4334640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.000000 -271.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c17d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -270.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c17f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -363.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc4770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3908.000000 -265.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3151050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3908.000000 -358.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3151290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -361.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb8bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -268.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb8e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4276.000000 -266.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331a6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4276.000000 -362.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331a920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 -265.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33431b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 -358.000000) translate(0,12)">0561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33433f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -267.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea3290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -360.000000) translate(0,12)">0571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea34a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -361.000000) translate(0,12)">0581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ffb190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4785.000000 -268.000000) translate(0,12)">0586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ffb3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -828.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3472770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.500000 -1166.500000) translate(0,16)">中山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e620b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e62390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e62390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e62390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e62390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e62390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e62390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e62390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f82d10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3253.000000 -229.000000) translate(0,15)">4775</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f85430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -611.000000) translate(0,12)">油温（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fb8d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3646.000000 -418.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_453b570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3579.000000 -318.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f97210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -317.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f97420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3910.000000 -312.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4127770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -315.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f9db40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4278.000000 -313.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f9dd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -312.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290b320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -316.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290b560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -317.000000) translate(0,12)">058</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4323cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.000000 -1154.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3232910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.000000 -1189.000000) translate(0,16)">主网返回</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1078"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-598"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_ZS.CX_ZS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10802"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.000000 -562.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.000000 -562.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7442" ObjectName="TF-CX_ZS.CX_ZS_1T"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-96483" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4499.000000 -610.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96483" ObjectName="CX_ZS:CX_ZS_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3228.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3240" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3240" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3192" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3192" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="4380" y="-636"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="4380" y="-636"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3576" y="-320"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3576" y="-320"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3733" y="-316"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3733" y="-316"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3908" y="-313"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3908" y="-313"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4084" y="-315"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4084" y="-315"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4276" y="-314"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4276" y="-314"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4452" y="-311"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4452" y="-311"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4612" y="-317"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4612" y="-317"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4785" y="-318"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4785" y="-318"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3397" y="-1162"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3397" y="-1162"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3397" y="-1197"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3397" y="-1197"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4173bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 1133.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 1118.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 1103.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4033240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4366.000000 1133.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43395f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 1118.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4339840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.000000 1103.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f03490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 532.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d7220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 517.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d7460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 502.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b51250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3517.000000 67.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e93c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3506.000000 52.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e93ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2faedf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 67.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9f4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3662.000000 52.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331b3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30888c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4212.000000 67.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3088b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 52.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3297b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4226.000000 37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30dd130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4388.000000 67.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30dd3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 52.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3318520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3114a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 67.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3114d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 52.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e61160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46431e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 67.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4313650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 52.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4313890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -150.000000 258.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bdbd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 318.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d840c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4010.000000 303.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -175.000000 252.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd0830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.000000 312.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e628c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 297.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f83660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3693.000000 879.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f91150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3693.000000 894.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6d2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 849.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6d4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3693.000000 864.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f86450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 453.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f866c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 468.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb7ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3523.000000 423.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb7ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 438.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d8c170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 707.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d8c3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 692.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 676.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4396.000000 722.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_41291b0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4008.000000 -493.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e7e600">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4028.000000 -585.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f1d170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -918.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c23ca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4273.000000 -918.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4331220">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4025.000000 -1001.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f8d950">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 -1001.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34ac840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 -490.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d2900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 -151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30cfa30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 -150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f99f90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3875.000000 -143.000000)" xlink:href="#lightningRod:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f99b60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4051.000000 -146.000000)" xlink:href="#lightningRod:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d67d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -149.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c6c30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4471.000000 -150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c81b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4628.000000 -150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e6aa20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -631.000000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3240" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3192" y="-1194"/></g>
   <g href="35kV中山变中山变35kV1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="4380" y="-636"/></g>
   <g href="35kV中山变中山变10kV中角线051断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3576" y="-320"/></g>
   <g href="35kV中山变中山变10kV中洋线052断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3733" y="-316"/></g>
   <g href="35kV中山变中山变10kV1号电容器组053断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3908" y="-313"/></g>
   <g href="35kV中山变中山变10kV2号电容器组054断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4084" y="-315"/></g>
   <g href="35kV中山变中山变10kV集镇线055断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4276" y="-314"/></g>
   <g href="35kV中山变中山变10kV大过口线056断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4452" y="-311"/></g>
   <g href="35kV中山变中山变10kV中三树线057断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4612" y="-317"/></g>
   <g href="35kV中山变中山变10kV备用线058断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4785" y="-318"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3397" y="-1162"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3397" y="-1197"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-96520">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3821.000000 -707.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7433" ObjectName="SW-CX_ZS.CX_ZS_3901SW"/>
     <cge:Meas_Ref ObjectId="96520"/>
    <cge:TPSR_Ref TObjectID="7433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96527">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3982.000000 -801.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7434" ObjectName="SW-CX_ZS.CX_ZS_3511SW"/>
     <cge:Meas_Ref ObjectId="96527"/>
    <cge:TPSR_Ref TObjectID="7434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96498">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3711.000000 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7427" ObjectName="SW-CX_ZS.CX_ZS_0521SW"/>
     <cge:Meas_Ref ObjectId="96498"/>
    <cge:TPSR_Ref TObjectID="7427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96497">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3711.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7428" ObjectName="SW-CX_ZS.CX_ZS_0526SW"/>
     <cge:Meas_Ref ObjectId="96497"/>
    <cge:TPSR_Ref TObjectID="7428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96507">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4591.000000 -313.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7424" ObjectName="SW-CX_ZS.CX_ZS_0571SW"/>
     <cge:Meas_Ref ObjectId="96507"/>
    <cge:TPSR_Ref TObjectID="7424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96506">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4589.000000 -220.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7425" ObjectName="SW-CX_ZS.CX_ZS_0576SW"/>
     <cge:Meas_Ref ObjectId="96506"/>
    <cge:TPSR_Ref TObjectID="7425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96501">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7419" ObjectName="SW-CX_ZS.CX_ZS_0551SW"/>
     <cge:Meas_Ref ObjectId="96501"/>
    <cge:TPSR_Ref TObjectID="7419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96500">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -219.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7420" ObjectName="SW-CX_ZS.CX_ZS_0556SW"/>
     <cge:Meas_Ref ObjectId="96500"/>
    <cge:TPSR_Ref TObjectID="7420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96503">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 -311.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7416" ObjectName="SW-CX_ZS.CX_ZS_0561SW"/>
     <cge:Meas_Ref ObjectId="96503"/>
    <cge:TPSR_Ref TObjectID="7416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96504">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 -218.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7417" ObjectName="SW-CX_ZS.CX_ZS_0566SW"/>
     <cge:Meas_Ref ObjectId="96504"/>
    <cge:TPSR_Ref TObjectID="7417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96519">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -397.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7423" ObjectName="SW-CX_ZS.CX_ZS_0901SW"/>
     <cge:Meas_Ref ObjectId="96519"/>
    <cge:TPSR_Ref TObjectID="7423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.000000 -314.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7407" ObjectName="SW-CX_ZS.CX_ZS_0541SW"/>
     <cge:Meas_Ref ObjectId="96513"/>
    <cge:TPSR_Ref TObjectID="7407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96512">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7408" ObjectName="SW-CX_ZS.CX_ZS_0546SW"/>
     <cge:Meas_Ref ObjectId="96512"/>
    <cge:TPSR_Ref TObjectID="7408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96522">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4028.000000 -948.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7435" ObjectName="SW-CX_ZS.CX_ZS_35167SW"/>
     <cge:Meas_Ref ObjectId="96522"/>
    <cge:TPSR_Ref TObjectID="7435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96525">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4317.000000 -799.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7438" ObjectName="SW-CX_ZS.CX_ZS_3521SW"/>
     <cge:Meas_Ref ObjectId="96525"/>
    <cge:TPSR_Ref TObjectID="7438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96521">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -948.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7439" ObjectName="SW-CX_ZS.CX_ZS_35267SW"/>
     <cge:Meas_Ref ObjectId="96521"/>
    <cge:TPSR_Ref TObjectID="7439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96516">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.000000 -403.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7405" ObjectName="SW-CX_ZS.CX_ZS_0011SW"/>
     <cge:Meas_Ref ObjectId="96516"/>
    <cge:TPSR_Ref TObjectID="7405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96517">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4318.000000 -714.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7430" ObjectName="SW-CX_ZS.CX_ZS_3011SW"/>
     <cge:Meas_Ref ObjectId="96517"/>
    <cge:TPSR_Ref TObjectID="7430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96510">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -311.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7402" ObjectName="SW-CX_ZS.CX_ZS_0531SW"/>
     <cge:Meas_Ref ObjectId="96510"/>
    <cge:TPSR_Ref TObjectID="7402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -218.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7403" ObjectName="SW-CX_ZS.CX_ZS_0536SW"/>
     <cge:Meas_Ref ObjectId="96509"/>
    <cge:TPSR_Ref TObjectID="7403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-44112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4765.000000 -314.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7413" ObjectName="SW-CX_ZS.CX_ZS_0581SW"/>
     <cge:Meas_Ref ObjectId="44112"/>
    <cge:TPSR_Ref TObjectID="7413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-44113">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4763.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7414" ObjectName="SW-CX_ZS.CX_ZS_0586SW"/>
     <cge:Meas_Ref ObjectId="44113"/>
    <cge:TPSR_Ref TObjectID="7414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96495">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3555.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7410" ObjectName="SW-CX_ZS.CX_ZS_0511SW"/>
     <cge:Meas_Ref ObjectId="96495"/>
    <cge:TPSR_Ref TObjectID="7410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96494">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3555.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7411" ObjectName="SW-CX_ZS.CX_ZS_0516SW"/>
     <cge:Meas_Ref ObjectId="96494"/>
    <cge:TPSR_Ref TObjectID="7411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96518">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 -758.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7432" ObjectName="SW-CX_ZS.CX_ZS_39017SW"/>
     <cge:Meas_Ref ObjectId="96518"/>
    <cge:TPSR_Ref TObjectID="7432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96528">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3982.000000 -901.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7436" ObjectName="SW-CX_ZS.CX_ZS_3516SW"/>
     <cge:Meas_Ref ObjectId="96528"/>
    <cge:TPSR_Ref TObjectID="7436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-96526">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4317.000000 -901.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7440" ObjectName="SW-CX_ZS.CX_ZS_3526SW"/>
     <cge:Meas_Ref ObjectId="96526"/>
    <cge:TPSR_Ref TObjectID="7440"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96485" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -1133.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7437"/>
     <cge:Term_Ref ObjectID="10790"/>
    <cge:TPSR_Ref TObjectID="7437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96486" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -1133.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7437"/>
     <cge:Term_Ref ObjectID="10790"/>
    <cge:TPSR_Ref TObjectID="7437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96484" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -1133.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7437"/>
     <cge:Term_Ref ObjectID="10790"/>
    <cge:TPSR_Ref TObjectID="7437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96488" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4423.000000 -1133.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7441"/>
     <cge:Term_Ref ObjectID="10798"/>
    <cge:TPSR_Ref TObjectID="7441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96489" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4423.000000 -1133.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7441"/>
     <cge:Term_Ref ObjectID="10798"/>
    <cge:TPSR_Ref TObjectID="7441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96487" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4423.000000 -1133.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7441"/>
     <cge:Term_Ref ObjectID="10798"/>
    <cge:TPSR_Ref TObjectID="7441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96481" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -532.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7406"/>
     <cge:Term_Ref ObjectID="10728"/>
    <cge:TPSR_Ref TObjectID="7406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96482" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -532.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7406"/>
     <cge:Term_Ref ObjectID="10728"/>
    <cge:TPSR_Ref TObjectID="7406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96480" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -532.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7406"/>
     <cge:Term_Ref ObjectID="10728"/>
    <cge:TPSR_Ref TObjectID="7406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96457" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3576.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7412"/>
     <cge:Term_Ref ObjectID="10740"/>
    <cge:TPSR_Ref TObjectID="7412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96458" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3576.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7412"/>
     <cge:Term_Ref ObjectID="10740"/>
    <cge:TPSR_Ref TObjectID="7412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96456" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3576.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7412"/>
     <cge:Term_Ref ObjectID="10740"/>
    <cge:TPSR_Ref TObjectID="7412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96460" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7429"/>
     <cge:Term_Ref ObjectID="10774"/>
    <cge:TPSR_Ref TObjectID="7429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96461" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7429"/>
     <cge:Term_Ref ObjectID="10774"/>
    <cge:TPSR_Ref TObjectID="7429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96459" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7429"/>
     <cge:Term_Ref ObjectID="10774"/>
    <cge:TPSR_Ref TObjectID="7429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96463" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7421"/>
     <cge:Term_Ref ObjectID="10758"/>
    <cge:TPSR_Ref TObjectID="7421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96464" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7421"/>
     <cge:Term_Ref ObjectID="10758"/>
    <cge:TPSR_Ref TObjectID="7421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96462" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7421"/>
     <cge:Term_Ref ObjectID="10758"/>
    <cge:TPSR_Ref TObjectID="7421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96466" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7418"/>
     <cge:Term_Ref ObjectID="10752"/>
    <cge:TPSR_Ref TObjectID="7418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96467" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7418"/>
     <cge:Term_Ref ObjectID="10752"/>
    <cge:TPSR_Ref TObjectID="7418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96465" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7418"/>
     <cge:Term_Ref ObjectID="10752"/>
    <cge:TPSR_Ref TObjectID="7418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96469" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7426"/>
     <cge:Term_Ref ObjectID="10768"/>
    <cge:TPSR_Ref TObjectID="7426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96470" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7426"/>
     <cge:Term_Ref ObjectID="10768"/>
    <cge:TPSR_Ref TObjectID="7426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96468" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7426"/>
     <cge:Term_Ref ObjectID="10768"/>
    <cge:TPSR_Ref TObjectID="7426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-44265" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="44265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7415"/>
     <cge:Term_Ref ObjectID="10746"/>
    <cge:TPSR_Ref TObjectID="7415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-44266" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="44266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7415"/>
     <cge:Term_Ref ObjectID="10746"/>
    <cge:TPSR_Ref TObjectID="7415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-44261" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="44261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7415"/>
     <cge:Term_Ref ObjectID="10746"/>
    <cge:TPSR_Ref TObjectID="7415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96473" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3904.000000 -59.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7404"/>
     <cge:Term_Ref ObjectID="10724"/>
    <cge:TPSR_Ref TObjectID="7404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96471" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3904.000000 -59.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7404"/>
     <cge:Term_Ref ObjectID="10724"/>
    <cge:TPSR_Ref TObjectID="7404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96476" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4080.000000 -59.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7409"/>
     <cge:Term_Ref ObjectID="10734"/>
    <cge:TPSR_Ref TObjectID="7409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96474" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4080.000000 -59.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7409"/>
     <cge:Term_Ref ObjectID="10734"/>
    <cge:TPSR_Ref TObjectID="7409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-96216" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -466.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20089"/>
     <cge:Term_Ref ObjectID="28036"/>
    <cge:TPSR_Ref TObjectID="20089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-96217" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -466.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96217" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20089"/>
     <cge:Term_Ref ObjectID="28036"/>
    <cge:TPSR_Ref TObjectID="20089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-96218" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -466.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20089"/>
     <cge:Term_Ref ObjectID="28036"/>
    <cge:TPSR_Ref TObjectID="20089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-96221" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -466.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96221" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20089"/>
     <cge:Term_Ref ObjectID="28036"/>
    <cge:TPSR_Ref TObjectID="20089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-96208" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3782.000000 -892.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20088"/>
     <cge:Term_Ref ObjectID="28035"/>
    <cge:TPSR_Ref TObjectID="20088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-96209" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3782.000000 -892.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20088"/>
     <cge:Term_Ref ObjectID="28035"/>
    <cge:TPSR_Ref TObjectID="20088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-96210" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3782.000000 -892.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20088"/>
     <cge:Term_Ref ObjectID="28035"/>
    <cge:TPSR_Ref TObjectID="20088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-96213" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3782.000000 -892.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20088"/>
     <cge:Term_Ref ObjectID="28035"/>
    <cge:TPSR_Ref TObjectID="20088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-96478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -720.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7431"/>
     <cge:Term_Ref ObjectID="10778"/>
    <cge:TPSR_Ref TObjectID="7431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-96479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -720.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7431"/>
     <cge:Term_Ref ObjectID="10778"/>
    <cge:TPSR_Ref TObjectID="7431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-96477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -720.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7431"/>
     <cge:Term_Ref ObjectID="10778"/>
    <cge:TPSR_Ref TObjectID="7431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-96211" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -720.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96211" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7431"/>
     <cge:Term_Ref ObjectID="10778"/>
    <cge:TPSR_Ref TObjectID="7431"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_ZS"/>
</svg>