<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-147" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3114 -1242 1884 1243">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_1c2df50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape175">
    <polyline DF8003:Layer="PUBLIC" points="6,4 0,16 12,16 6,4 6,5 6,4 "/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape49_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,11 43,33 43,64 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="51" y1="66" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="49" x2="49" y1="67" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="68" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="40" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="20" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="35" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="13" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="14" y1="44" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="22" y1="45" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="16" y1="64" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="25" y2="8"/>
    <circle cx="16" cy="40" fillStyle="0" r="15" stroke-width="1"/>
   </symbol>
   <symbol id="transformer2:shape49_1">
    <circle cx="16" cy="64" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="64" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="20" y1="64" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="59" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="13" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="14" y1="68" y2="70"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="22" y1="69" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="transformer2:shape72_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="8" y1="77" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="81" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="81" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="15" y1="86" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="17" y1="77" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape72_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="64" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="57" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="9" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="45" y1="9" y2="9"/>
    <circle cx="31" cy="46" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="40" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="31" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="23" x2="23" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="39" x2="22" y1="10" y2="10"/>
   </symbol>
   <symbol id="voltageTransformer:shape45">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="34" x2="34" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="24" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="24" x2="24" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="17" x2="17" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="41" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="41" x2="41" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="5" x2="5" y1="11" y2="4"/>
    <circle cx="29" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="26" x2="27" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="22" x2="21" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="21" x2="27" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="21" y2="23"/>
    <circle cx="19" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="21" y2="23"/>
    <circle cx="24" cy="42" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="29" y2="29"/>
    <circle cx="29" cy="33" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="29" y2="29"/>
    <circle cx="19" cy="33" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="31" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape87">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="58" y2="123"/>
    <rect height="13" stroke-width="1" width="7" x="8" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="6" y1="30" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="0" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="56" x2="59" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="53" x2="56" y1="44" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="56" x2="56" y1="41" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="42" x2="45" y1="50" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="39" x2="42" y1="53" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="42" x2="42" y1="50" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="5" x2="17" y1="9" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="15" x2="7" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="13" x2="10" y1="2" y2="2"/>
    <rect height="27" stroke-width="0.416667" width="14" x="35" y="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="76" x2="43" y1="104" y2="104"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="76" x2="76" y1="104" y2="70"/>
    <rect height="27" stroke-width="0.416667" width="14" x="69" y="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="70" x2="82" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="76" x2="76" y1="56" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="80" x2="72" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="75" y1="50" y2="50"/>
    <ellipse cx="42" cy="50" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="55" cy="41" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="26" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="31" y1="41" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="28" y1="41" y2="45"/>
    <circle cx="42" cy="34" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="42" x2="45" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="39" x2="42" y1="37" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="42" x2="42" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="11" y1="52" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="52" y2="9"/>
    <circle cx="29" cy="41" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c53c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d0ac30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f2d760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c16ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d867c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d5cc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d22e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d23130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d24090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d24090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf1e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf1e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf13a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf13a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1bdf810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cfed10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cfdf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d521e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d9d6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf3130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d24fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d87c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cfe520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d42630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cbcaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c529c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cea340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d512c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d07b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c535b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c496d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1be5ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c6f280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1bdb180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1be0270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1253" width="1894" x="3109" y="-1247"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9f550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3890.000000 680.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9f810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 665.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9fa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3904.000000 650.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9fd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 933.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 948.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca0230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3529.000000 905.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca0470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 919.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca07a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3475.000000 604.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca0a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3475.000000 619.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca0c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3467.000000 576.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca0e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3475.000000 590.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ff3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3658.000000 92.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ffa00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 77.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ffc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3672.000000 62.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6bff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4320.000000 1026.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6c2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 1011.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6c4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 996.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6d400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 1190.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6d6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 1175.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6d900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 1160.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4115,-272 4131,-272 4123,-262 4115,-272 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4272,-272 4288,-272 4280,-262 4272,-272 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4414,-273 4430,-273 4422,-263 4414,-273 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4677,-271 4693,-271 4685,-261 4677,-271 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="4638" y="-862"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="4677" y="-861"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="4609" y="-821"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="4635" y="-821"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="4843" y="-822"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="4869" y="-822"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="4829" y="-865"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="4790" y="-864"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-118440">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22038" ObjectName="SW-CX_HW.CX_HW_1011SW"/>
     <cge:Meas_Ref ObjectId="118440"/>
    <cge:TPSR_Ref TObjectID="22038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118441">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -1042.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22039" ObjectName="SW-CX_HW.CX_HW_1016SW"/>
     <cge:Meas_Ref ObjectId="118441"/>
    <cge:TPSR_Ref TObjectID="22039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118442">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -965.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22040" ObjectName="SW-CX_HW.CX_HW_10117SW"/>
     <cge:Meas_Ref ObjectId="118442"/>
    <cge:TPSR_Ref TObjectID="22040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -716.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22045" ObjectName="SW-CX_HW.CX_HW_3016SW"/>
     <cge:Meas_Ref ObjectId="118447"/>
    <cge:TPSR_Ref TObjectID="22045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118446">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -561.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22044" ObjectName="SW-CX_HW.CX_HW_3011SW"/>
     <cge:Meas_Ref ObjectId="118446"/>
    <cge:TPSR_Ref TObjectID="22044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118448">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -607.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22046" ObjectName="SW-CX_HW.CX_HW_30117SW"/>
     <cge:Meas_Ref ObjectId="118448"/>
    <cge:TPSR_Ref TObjectID="22046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118475">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -463.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22072" ObjectName="SW-CX_HW.CX_HW_36317SW"/>
     <cge:Meas_Ref ObjectId="118475"/>
    <cge:TPSR_Ref TObjectID="22072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118476">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22073" ObjectName="SW-CX_HW.CX_HW_36367SW"/>
     <cge:Meas_Ref ObjectId="118476"/>
    <cge:TPSR_Ref TObjectID="22073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118433">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -910.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22032" ObjectName="SW-CX_HW.CX_HW_1611SW"/>
     <cge:Meas_Ref ObjectId="118433"/>
    <cge:TPSR_Ref TObjectID="22032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118434">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -1046.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22033" ObjectName="SW-CX_HW.CX_HW_1616SW"/>
     <cge:Meas_Ref ObjectId="118434"/>
    <cge:TPSR_Ref TObjectID="22033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118435">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -967.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22034" ObjectName="SW-CX_HW.CX_HW_16117SW"/>
     <cge:Meas_Ref ObjectId="118435"/>
    <cge:TPSR_Ref TObjectID="22034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118451">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 -937.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22048" ObjectName="SW-CX_HW.CX_HW_1901SW"/>
     <cge:Meas_Ref ObjectId="118451"/>
    <cge:TPSR_Ref TObjectID="22048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118443">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -1028.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22041" ObjectName="SW-CX_HW.CX_HW_10160SW"/>
     <cge:Meas_Ref ObjectId="118443"/>
    <cge:TPSR_Ref TObjectID="22041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118436">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4194.000000 -1029.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22035" ObjectName="SW-CX_HW.CX_HW_16160SW"/>
     <cge:Meas_Ref ObjectId="118436"/>
    <cge:TPSR_Ref TObjectID="22035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118473">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -484.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22070" ObjectName="SW-CX_HW.CX_HW_3631SW"/>
     <cge:Meas_Ref ObjectId="118473"/>
    <cge:TPSR_Ref TObjectID="22070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118474">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -363.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22071" ObjectName="SW-CX_HW.CX_HW_3636SW"/>
     <cge:Meas_Ref ObjectId="118474"/>
    <cge:TPSR_Ref TObjectID="22071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118487">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3656.000000 -465.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22084" ObjectName="SW-CX_HW.CX_HW_36117SW"/>
     <cge:Meas_Ref ObjectId="118487"/>
    <cge:TPSR_Ref TObjectID="22084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118488">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3654.000000 -337.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22085" ObjectName="SW-CX_HW.CX_HW_36167SW"/>
     <cge:Meas_Ref ObjectId="118488"/>
    <cge:TPSR_Ref TObjectID="22085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118485">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22082" ObjectName="SW-CX_HW.CX_HW_3611SW"/>
     <cge:Meas_Ref ObjectId="118485"/>
    <cge:TPSR_Ref TObjectID="22082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118486">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -365.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22083" ObjectName="SW-CX_HW.CX_HW_3616SW"/>
     <cge:Meas_Ref ObjectId="118486"/>
    <cge:TPSR_Ref TObjectID="22083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118489">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 -215.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22086" ObjectName="SW-CX_HW.CX_HW_3010SW"/>
     <cge:Meas_Ref ObjectId="118489"/>
    <cge:TPSR_Ref TObjectID="22086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118480">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 -467.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22077" ObjectName="SW-CX_HW.CX_HW_36217SW"/>
     <cge:Meas_Ref ObjectId="118480"/>
    <cge:TPSR_Ref TObjectID="22077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118481">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3877.000000 -339.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22078" ObjectName="SW-CX_HW.CX_HW_36237SW"/>
     <cge:Meas_Ref ObjectId="118481"/>
    <cge:TPSR_Ref TObjectID="22078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118478">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3928.000000 -488.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22075" ObjectName="SW-CX_HW.CX_HW_3621SW"/>
     <cge:Meas_Ref ObjectId="118478"/>
    <cge:TPSR_Ref TObjectID="22075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118479">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3928.000000 -356.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22076" ObjectName="SW-CX_HW.CX_HW_3623SW"/>
     <cge:Meas_Ref ObjectId="118479"/>
    <cge:TPSR_Ref TObjectID="22076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118449">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -786.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22047" ObjectName="SW-CX_HW.CX_HW_1010SW"/>
     <cge:Meas_Ref ObjectId="118449"/>
    <cge:TPSR_Ref TObjectID="22047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118437">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.000000 -1096.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22036" ObjectName="SW-CX_HW.CX_HW_K1617SW"/>
     <cge:Meas_Ref ObjectId="118437"/>
    <cge:TPSR_Ref TObjectID="22036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118444">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 -1094.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22042" ObjectName="SW-CX_HW.CX_HW_K1017SW"/>
     <cge:Meas_Ref ObjectId="118444"/>
    <cge:TPSR_Ref TObjectID="22042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118452">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 -920.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22049" ObjectName="SW-CX_HW.CX_HW_K1900SW"/>
     <cge:Meas_Ref ObjectId="118452"/>
    <cge:TPSR_Ref TObjectID="22049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118482">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3928.000000 -243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22079" ObjectName="SW-CX_HW.CX_HW_3626SW"/>
     <cge:Meas_Ref ObjectId="118482"/>
    <cge:TPSR_Ref TObjectID="22079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118483">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 -225.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22080" ObjectName="SW-CX_HW.CX_HW_36267SW"/>
     <cge:Meas_Ref ObjectId="118483"/>
    <cge:TPSR_Ref TObjectID="22080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4221.000000 -463.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22067" ObjectName="SW-CX_HW.CX_HW_36417SW"/>
     <cge:Meas_Ref ObjectId="118470"/>
    <cge:TPSR_Ref TObjectID="22067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118471">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4220.000000 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22068" ObjectName="SW-CX_HW.CX_HW_36467SW"/>
     <cge:Meas_Ref ObjectId="118471"/>
    <cge:TPSR_Ref TObjectID="22068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -484.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22065" ObjectName="SW-CX_HW.CX_HW_3641SW"/>
     <cge:Meas_Ref ObjectId="118468"/>
    <cge:TPSR_Ref TObjectID="22065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118469">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -363.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22066" ObjectName="SW-CX_HW.CX_HW_3646SW"/>
     <cge:Meas_Ref ObjectId="118469"/>
    <cge:TPSR_Ref TObjectID="22066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118465">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -464.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22062" ObjectName="SW-CX_HW.CX_HW_36517SW"/>
     <cge:Meas_Ref ObjectId="118465"/>
    <cge:TPSR_Ref TObjectID="22062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118466">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4362.000000 -336.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22063" ObjectName="SW-CX_HW.CX_HW_36567SW"/>
     <cge:Meas_Ref ObjectId="118466"/>
    <cge:TPSR_Ref TObjectID="22063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118463">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -485.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22060" ObjectName="SW-CX_HW.CX_HW_3651SW"/>
     <cge:Meas_Ref ObjectId="118463"/>
    <cge:TPSR_Ref TObjectID="22060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118464">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -364.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22061" ObjectName="SW-CX_HW.CX_HW_3656SW"/>
     <cge:Meas_Ref ObjectId="118464"/>
    <cge:TPSR_Ref TObjectID="22061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118456">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 -465.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22053" ObjectName="SW-CX_HW.CX_HW_39017SW"/>
     <cge:Meas_Ref ObjectId="118456"/>
    <cge:TPSR_Ref TObjectID="22053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118455">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22052" ObjectName="SW-CX_HW.CX_HW_3901SW"/>
     <cge:Meas_Ref ObjectId="118455"/>
    <cge:TPSR_Ref TObjectID="22052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118491">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3518.000000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22088" ObjectName="SW-CX_HW.CX_HW_31217SW"/>
     <cge:Meas_Ref ObjectId="118491"/>
    <cge:TPSR_Ref TObjectID="22088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118490">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3568.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22087" ObjectName="SW-CX_HW.CX_HW_3121SW"/>
     <cge:Meas_Ref ObjectId="118490"/>
    <cge:TPSR_Ref TObjectID="22087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118460">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4626.000000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22057" ObjectName="SW-CX_HW.CX_HW_36617SW"/>
     <cge:Meas_Ref ObjectId="118460"/>
    <cge:TPSR_Ref TObjectID="22057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118461">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4625.000000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22058" ObjectName="SW-CX_HW.CX_HW_36667SW"/>
     <cge:Meas_Ref ObjectId="118461"/>
    <cge:TPSR_Ref TObjectID="22058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118458">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4676.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22055" ObjectName="SW-CX_HW.CX_HW_3661SW"/>
     <cge:Meas_Ref ObjectId="118458"/>
    <cge:TPSR_Ref TObjectID="22055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118459">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4676.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22056" ObjectName="SW-CX_HW.CX_HW_3666SW"/>
     <cge:Meas_Ref ObjectId="118459"/>
    <cge:TPSR_Ref TObjectID="22056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118453">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3779.000000 -1022.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22050" ObjectName="SW-CX_HW.CX_HW_19017SW"/>
     <cge:Meas_Ref ObjectId="118453"/>
    <cge:TPSR_Ref TObjectID="22050"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_HW.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -171.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43327" ObjectName="SM-CX_HW.P4"/>
    <cge:TPSR_Ref TObjectID="43327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HW.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4275.000000 -171.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43326" ObjectName="SM-CX_HW.P3"/>
    <cge:TPSR_Ref TObjectID="43326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HW.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4417.000000 -172.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43325" ObjectName="SM-CX_HW.P2"/>
    <cge:TPSR_Ref TObjectID="43325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HW.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -170.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43324" ObjectName="SM-CX_HW.P1"/>
    <cge:TPSR_Ref TObjectID="43324"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HGY" endPointId="0" endStationName="CX_HW" flowDrawDirect="1" flowShape="0" id="AC-110kV.hehuang_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4252,-1197 4252,-1224 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31722" ObjectName="AC-110kV.hehuang_line"/>
    <cge:TPSR_Ref TObjectID="31722_SS-147"/></metadata>
   <polyline fill="none" opacity="0" points="4252,-1197 4252,-1224 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1fba580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -141.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c3bd40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -1095.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c3c7d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -1028.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c3d260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4170.000000 -966.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c3dcf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -1093.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c3e780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -1027.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c3f210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -964.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c3fca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -745.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c41440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 -606.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c42130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -462.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c42bc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -334.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c44480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3632.000000 -464.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c44d20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3631.000000 -336.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e27d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 -466.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e31d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -338.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f6710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -919.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f71a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.000000 -1021.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2306a00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 -224.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2319c60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -462.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_231a4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.000000 -334.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f384e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 -463.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f38d00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.000000 -335.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f456a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4464.000000 -464.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f4cb40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3495.000000 -461.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f5d890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 -461.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f5e0b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4602.000000 -333.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_1d141f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-886 3962,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22030@0" ObjectIDZND0="22038@0" Pin0InfoVect0LinkObjId="SW-118440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-886 3962,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d14450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-949 3962,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22038@1" ObjectIDZND0="22037@x" ObjectIDZND1="22040@x" Pin0InfoVect0LinkObjId="SW-118439_0" Pin0InfoVect1LinkObjId="SW-118442_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-949 3962,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d16690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-970 3962,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22038@x" ObjectIDND1="22040@x" ObjectIDZND0="22037@0" Pin0InfoVect0LinkObjId="SW-118439_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118440_0" Pin1InfoVect1LinkObjId="SW-118442_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-970 3962,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c623c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3907,-1099 3897,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22042@1" ObjectIDZND0="g_1c3dcf0@0" Pin0InfoVect0LinkObjId="g_1c3dcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118444_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3907,-1099 3897,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c62620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-970 3944,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22037@x" ObjectIDND1="22038@x" ObjectIDZND0="22040@1" Pin0InfoVect0LinkObjId="SW-118442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118439_0" Pin1InfoVect1LinkObjId="SW-118440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-970 3944,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c62880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-970 3898,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22040@0" ObjectIDZND0="g_1c3f210@0" Pin0InfoVect0LinkObjId="g_1c3f210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-970 3898,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-612 4105,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c41440@0" ObjectIDZND0="22046@1" Pin0InfoVect0LinkObjId="SW-118448_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c41440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-612 4105,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdb0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4069,-612 4050,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22046@0" ObjectIDZND0="22043@x" ObjectIDZND1="22044@x" Pin0InfoVect0LinkObjId="SW-118445_0" Pin0InfoVect1LinkObjId="SW-118446_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4069,-612 4050,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c71f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-552 4050,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22093@0" ObjectIDZND0="22044@0" Pin0InfoVect0LinkObjId="SW-118446_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-552 4050,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c721d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-552 4123,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22093@0" ObjectIDZND0="22070@1" Pin0InfoVect0LinkObjId="SW-118473_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-552 4123,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c72430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-468 4105,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22069@x" ObjectIDND1="22070@x" ObjectIDZND0="22072@1" Pin0InfoVect0LinkObjId="SW-118475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118472_0" Pin1InfoVect1LinkObjId="SW-118473_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-468 4105,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c72690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4069,-468 4059,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22072@0" ObjectIDZND0="g_1c42130@0" Pin0InfoVect0LinkObjId="g_1c42130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4069,-468 4059,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c728f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-489 4123,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22070@0" ObjectIDZND0="22069@x" ObjectIDZND1="22072@x" Pin0InfoVect0LinkObjId="SW-118472_0" Pin0InfoVect1LinkObjId="SW-118475_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-489 4123,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c74bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-468 4123,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22070@x" ObjectIDND1="22072@x" ObjectIDZND0="22069@0" Pin0InfoVect0LinkObjId="SW-118472_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118473_0" Pin1InfoVect1LinkObjId="SW-118475_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-468 4123,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c74e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-340 4104,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1bf3ee0@0" ObjectIDND1="43327@x" ObjectIDND2="22071@x" ObjectIDZND0="22073@1" Pin0InfoVect0LinkObjId="SW-118476_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bf3ee0_0" Pin1InfoVect1LinkObjId="SM-CX_HW.P4_0" Pin1InfoVect2LinkObjId="SW-118474_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-340 4104,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c75070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-340 4058,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22073@0" ObjectIDZND0="g_1c42bc0@0" Pin0InfoVect0LinkObjId="g_1c42bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118476_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-340 4058,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c1edb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-886 4252,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22030@0" ObjectIDZND0="22032@0" Pin0InfoVect0LinkObjId="SW-118433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-886 4252,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c1f010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-951 4252,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22032@1" ObjectIDZND0="22034@x" ObjectIDZND1="22031@x" Pin0InfoVect0LinkObjId="SW-118435_0" Pin0InfoVect1LinkObjId="SW-118432_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-951 4252,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c1f270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-972 4252,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22032@x" ObjectIDND1="22034@x" ObjectIDZND0="22031@0" Pin0InfoVect0LinkObjId="SW-118432_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118433_0" Pin1InfoVect1LinkObjId="SW-118435_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-972 4252,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c21c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4197,-1101 4187,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22036@1" ObjectIDZND0="g_1c3bd40@0" Pin0InfoVect0LinkObjId="g_1c3bd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4197,-1101 4187,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c21ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-972 4234,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22032@x" ObjectIDND1="22031@x" ObjectIDZND0="22034@1" Pin0InfoVect0LinkObjId="SW-118435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118433_0" Pin1InfoVect1LinkObjId="SW-118432_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-972 4234,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c22130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-972 4188,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22034@0" ObjectIDZND0="g_1c3d260@0" Pin0InfoVect0LinkObjId="g_1c3d260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-972 4188,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cabf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-851 3995,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="22047@x" ObjectIDND1="22089@x" ObjectIDZND0="g_1bf23c0@0" Pin0InfoVect0LinkObjId="g_1bf23c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118449_0" Pin1InfoVect1LinkObjId="g_1cac1b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-851 3995,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cac1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-851 4050,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_1bf23c0@0" ObjectIDND1="22047@x" ObjectIDZND0="22089@x" Pin0InfoVect0LinkObjId="g_1d2ae20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bf23c0_0" Pin1InfoVect1LinkObjId="SW-118449_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-851 4050,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cae2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-1099 3943,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22089@x" ObjectIDND1="22039@x" ObjectIDZND0="22042@0" Pin0InfoVect0LinkObjId="SW-118444_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cac1b0_0" Pin1InfoVect1LinkObjId="SW-118441_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-1099 3943,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf4c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-1083 3962,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="22039@1" ObjectIDZND0="22042@x" ObjectIDZND1="22089@x" Pin0InfoVect0LinkObjId="SW-118444_0" Pin0InfoVect1LinkObjId="g_1cac1b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-1083 3962,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf4ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-1033 3898,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22041@0" ObjectIDZND0="g_1c3e780@0" Pin0InfoVect0LinkObjId="g_1c3e780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-1033 3898,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf7680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-1033 3944,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22037@x" ObjectIDND1="22039@x" ObjectIDZND0="22041@1" Pin0InfoVect0LinkObjId="SW-118443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118439_0" Pin1InfoVect1LinkObjId="SW-118441_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-1033 3944,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf78e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-1016 3962,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22037@1" ObjectIDZND0="22039@x" ObjectIDZND1="22041@x" Pin0InfoVect0LinkObjId="SW-118441_0" Pin0InfoVect1LinkObjId="SW-118443_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118439_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-1016 3962,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf7b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-1033 3962,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22037@x" ObjectIDND1="22041@x" ObjectIDZND0="22039@0" Pin0InfoVect0LinkObjId="SW-118441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118439_0" Pin1InfoVect1LinkObjId="SW-118443_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-1033 3962,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf7da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-1034 4189,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22035@0" ObjectIDZND0="g_1c3c7d0@0" Pin0InfoVect0LinkObjId="g_1c3c7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118436_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-1034 4189,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d27360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-1034 4235,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22033@x" ObjectIDND1="22031@x" ObjectIDZND0="22035@1" Pin0InfoVect0LinkObjId="SW-118436_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118434_0" Pin1InfoVect1LinkObjId="SW-118432_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-1034 4235,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d275c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1018 4252,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22031@1" ObjectIDZND0="22033@x" ObjectIDZND1="22035@x" Pin0InfoVect0LinkObjId="SW-118434_0" Pin0InfoVect1LinkObjId="SW-118436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118432_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1018 4252,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d27820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1034 4252,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22035@x" ObjectIDND1="22031@x" ObjectIDZND0="22033@0" Pin0InfoVect0LinkObjId="SW-118434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118436_0" Pin1InfoVect1LinkObjId="SW-118432_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1034 4252,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2ae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4098,-772 4050,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_1bf3130@0" ObjectIDZND0="22089@x" ObjectIDZND1="22045@x" Pin0InfoVect0LinkObjId="g_1cac1b0_0" Pin0InfoVect1LinkObjId="SW-118447_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf3130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4098,-772 4050,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c9b840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-312 4168,-312 4168,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="generator" EndDevType0="lightningRod" ObjectIDND0="22071@x" ObjectIDND1="22073@x" ObjectIDND2="43327@x" ObjectIDZND0="g_1bf3ee0@0" Pin0InfoVect0LinkObjId="g_1bf3ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118474_0" Pin1InfoVect1LinkObjId="SW-118476_0" Pin1InfoVect2LinkObjId="SM-CX_HW.P4_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-312 4168,-312 4168,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ca10d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-1168 4252,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1bc43a0@0" ObjectIDZND0="22036@x" ObjectIDZND1="22033@x" ObjectIDZND2="g_1cb1df0@0" Pin0InfoVect0LinkObjId="SW-118437_0" Pin0InfoVect1LinkObjId="SW-118434_0" Pin0InfoVect2LinkObjId="g_1cb1df0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc43a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-1168 4252,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ca12c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4233,-1101 4252,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22036@0" ObjectIDZND0="g_1bc43a0@0" ObjectIDZND1="g_1cb1df0@0" ObjectIDZND2="31722@1" Pin0InfoVect0LinkObjId="g_1bc43a0_0" Pin0InfoVect1LinkObjId="g_1cb1df0_0" Pin0InfoVect2LinkObjId="g_22eb3f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118437_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4233,-1101 4252,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ca14b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1087 4252,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="22033@1" ObjectIDZND0="22036@x" ObjectIDZND1="g_1bc43a0@0" ObjectIDZND2="g_1cb1df0@0" Pin0InfoVect0LinkObjId="SW-118437_0" Pin0InfoVect1LinkObjId="g_1bc43a0_0" Pin0InfoVect2LinkObjId="g_1cb1df0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1087 4252,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ca16a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-1176 4294,-1183 4252,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1cb1df0@0" ObjectIDZND0="g_1bc43a0@0" ObjectIDZND1="22036@x" ObjectIDZND2="22033@x" Pin0InfoVect0LinkObjId="g_1bc43a0_0" Pin0InfoVect1LinkObjId="SW-118437_0" Pin0InfoVect2LinkObjId="SW-118434_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cb1df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-1176 4294,-1183 4252,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc8370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-404 4123,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22071@1" ObjectIDZND0="22069@1" Pin0InfoVect0LinkObjId="SW-118472_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-404 4123,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc8e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-368 4123,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="22071@0" ObjectIDZND0="g_1bf3ee0@0" ObjectIDZND1="43327@x" ObjectIDZND2="22073@x" Pin0InfoVect0LinkObjId="g_1bf3ee0_0" Pin0InfoVect1LinkObjId="SM-CX_HW.P4_0" Pin0InfoVect2LinkObjId="SW-118476_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118474_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-368 4123,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc9950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-340 4123,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="22071@x" ObjectIDND1="22073@x" ObjectIDZND0="g_1bf3ee0@0" ObjectIDZND1="43327@x" Pin0InfoVect0LinkObjId="g_1bf3ee0_0" Pin0InfoVect1LinkObjId="SM-CX_HW.P4_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118474_0" Pin1InfoVect1LinkObjId="SW-118476_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-340 4123,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcf650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-552 3714,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22093@0" ObjectIDZND0="22082@1" Pin0InfoVect0LinkObjId="SW-118485_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-552 3714,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcf840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-470 3697,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22081@x" ObjectIDND1="22082@x" ObjectIDZND0="22084@1" Pin0InfoVect0LinkObjId="SW-118487_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118484_0" Pin1InfoVect1LinkObjId="SW-118485_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-470 3697,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcfa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-470 3650,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22084@0" ObjectIDZND0="g_1c44480@0" Pin0InfoVect0LinkObjId="g_1c44480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118487_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-470 3650,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcfc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-491 3714,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22082@0" ObjectIDZND0="22081@x" ObjectIDZND1="22084@x" Pin0InfoVect0LinkObjId="SW-118484_0" Pin0InfoVect1LinkObjId="SW-118487_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-491 3714,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd1db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-470 3714,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22082@x" ObjectIDND1="22084@x" ObjectIDZND0="22081@0" Pin0InfoVect0LinkObjId="SW-118484_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118485_0" Pin1InfoVect1LinkObjId="SW-118487_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-470 3714,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd2010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-342 3695,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1fafc50@0" ObjectIDND1="g_22fcb80@0" ObjectIDND2="22083@x" ObjectIDZND0="22085@1" Pin0InfoVect0LinkObjId="SW-118488_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fafc50_0" Pin1InfoVect1LinkObjId="g_22fcb80_0" Pin1InfoVect2LinkObjId="SW-118486_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-342 3695,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd2270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-342 3649,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22085@0" ObjectIDZND0="g_1c44d20@0" Pin0InfoVect0LinkObjId="g_1c44d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118488_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-342 3649,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb17f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-326 3759,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="22083@x" ObjectIDND1="22085@x" ObjectIDND2="g_22fcb80@0" ObjectIDZND0="g_1fafc50@0" Pin0InfoVect0LinkObjId="g_1fafc50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118486_0" Pin1InfoVect1LinkObjId="SW-118488_0" Pin1InfoVect2LinkObjId="g_22fcb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-326 3759,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb19e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-406 3714,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22083@1" ObjectIDZND0="22081@1" Pin0InfoVect0LinkObjId="SW-118484_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-406 3714,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb1bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-370 3714,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="22083@0" ObjectIDZND0="g_1fafc50@0" ObjectIDZND1="g_22fcb80@0" ObjectIDZND2="22085@x" Pin0InfoVect0LinkObjId="g_1fafc50_0" Pin0InfoVect1LinkObjId="g_22fcb80_0" Pin0InfoVect2LinkObjId="SW-118488_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118486_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-370 3714,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2328100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-552 3937,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22093@0" ObjectIDZND0="22075@1" Pin0InfoVect0LinkObjId="SW-118478_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-552 3937,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23282f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-472 3919,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22074@x" ObjectIDND1="22075@x" ObjectIDZND0="22077@1" Pin0InfoVect0LinkObjId="SW-118480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118477_0" Pin1InfoVect1LinkObjId="SW-118478_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-472 3919,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23284e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-472 3873,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22077@0" ObjectIDZND0="g_22e27d0@0" Pin0InfoVect0LinkObjId="g_22e27d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-472 3873,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23286d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-493 3937,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22075@0" ObjectIDZND0="22074@x" ObjectIDZND1="22077@x" Pin0InfoVect0LinkObjId="SW-118477_0" Pin0InfoVect1LinkObjId="SW-118480_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118478_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-493 3937,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_232a820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-472 3937,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22075@x" ObjectIDND1="22077@x" ObjectIDZND0="22074@0" Pin0InfoVect0LinkObjId="SW-118477_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118478_0" Pin1InfoVect1LinkObjId="SW-118480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-472 3937,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_232aa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-344 3918,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1c2bd10@0" ObjectIDND1="g_2300070@0" ObjectIDND2="22076@x" ObjectIDZND0="22078@1" Pin0InfoVect0LinkObjId="SW-118481_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c2bd10_0" Pin1InfoVect1LinkObjId="g_2300070_0" Pin1InfoVect2LinkObjId="SW-118479_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-344 3918,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_232ace0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-344 3872,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22078@0" ObjectIDZND0="g_22e31d0@0" Pin0InfoVect0LinkObjId="g_22e31d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118481_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-344 3872,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2330790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-361 3937,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="22076@0" ObjectIDZND0="g_1c2bd10@0" ObjectIDZND1="g_2300070@0" ObjectIDZND2="22078@x" Pin0InfoVect0LinkObjId="g_1c2bd10_0" Pin0InfoVect1LinkObjId="g_2300070_0" Pin0InfoVect2LinkObjId="SW-118481_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118479_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-361 3937,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c2f270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4616,-1116 4616,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1c2edb0@0" Pin0InfoVect0LinkObjId="g_1c2edb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4616,-1116 4616,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c2f4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4616,-1068 4616,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1c2edb0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1fba580_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c2edb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4616,-1068 4616,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c30830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4616,-873 4616,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1c300e0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c300e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4616,-873 4616,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c30a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4616,-765 4616,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1fba580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4616,-765 4616,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c314a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-1118 4875,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1c30ec0@0" Pin0InfoVect0LinkObjId="g_1c30ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-1118 4875,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c320e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-766 4855,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1fba580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-766 4855,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c32340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-874 4875,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1c31700@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c31700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-874 4875,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c325a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-1070 4875,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1c30ec0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1fba580_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c30ec0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-1070 4875,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c32800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-939 4875,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1c31700@1" Pin0InfoVect0LinkObjId="g_1c31700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fba580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-939 4875,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c38d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3953,-763 3953,-772 3937,-772 3937,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c3fca0@0" ObjectIDZND0="22047@0" Pin0InfoVect0LinkObjId="SW-118449_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c3fca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3953,-763 3953,-772 3937,-772 3937,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c38f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-851 3938,-851 3938,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1bf23c0@0" ObjectIDND1="22089@x" ObjectIDZND0="22047@1" Pin0InfoVect0LinkObjId="SW-118449_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bf23c0_0" Pin1InfoVect1LinkObjId="g_1cac1b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-851 3938,-851 3938,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c40f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-654 4050,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22043@0" ObjectIDZND0="22044@x" ObjectIDZND1="22046@x" Pin0InfoVect0LinkObjId="SW-118446_0" Pin0InfoVect1LinkObjId="SW-118448_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118445_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-654 4050,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c411e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-612 4050,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22043@x" ObjectIDND1="22046@x" ObjectIDZND0="22044@1" Pin0InfoVect0LinkObjId="SW-118446_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118445_0" Pin1InfoVect1LinkObjId="SW-118448_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-612 4050,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c41ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-312 4123,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_1bf3ee0@0" ObjectIDND1="22071@x" ObjectIDND2="22073@x" ObjectIDZND0="43327@0" Pin0InfoVect0LinkObjId="SM-CX_HW.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bf3ee0_0" Pin1InfoVect1LinkObjId="SW-118474_0" Pin1InfoVect2LinkObjId="SW-118476_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-312 4123,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e3c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-292 3714,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_22fcb80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1fba580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fcb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-292 3714,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e46d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-342 3714,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22083@x" ObjectIDND1="22085@x" ObjectIDZND0="g_1fafc50@0" ObjectIDZND1="g_22fcb80@0" Pin0InfoVect0LinkObjId="g_1fafc50_0" Pin0InfoVect1LinkObjId="g_22fcb80_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118486_0" Pin1InfoVect1LinkObjId="SW-118488_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-342 3714,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e4930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-312 3714,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_22fcb80@1" ObjectIDZND0="g_1fafc50@0" ObjectIDZND1="22083@x" ObjectIDZND2="22085@x" Pin0InfoVect0LinkObjId="g_1fafc50_0" Pin0InfoVect1LinkObjId="SW-118486_0" Pin0InfoVect2LinkObjId="SW-118488_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fcb80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-312 3714,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e4b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-220 3652,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="22086@0" ObjectIDZND0="g_22fe540@0" Pin0InfoVect0LinkObjId="g_22fe540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118489_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-220 3652,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e4df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-179 3652,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_22fe540@1" ObjectIDZND0="g_1fba580@0" Pin0InfoVect0LinkObjId="g_1fba580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fe540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-179 3652,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22ea8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1168 4252,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1bc43a0@0" ObjectIDND1="g_1cb1df0@0" ObjectIDND2="31722@1" ObjectIDZND0="22036@x" ObjectIDZND1="22033@x" Pin0InfoVect0LinkObjId="SW-118437_0" Pin0InfoVect1LinkObjId="SW-118434_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bc43a0_0" Pin1InfoVect1LinkObjId="g_1cb1df0_0" Pin1InfoVect2LinkObjId="g_22eb3f0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1168 4252,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22eb1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1168 4252,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1bc43a0@0" ObjectIDND1="22036@x" ObjectIDND2="22033@x" ObjectIDZND0="g_1cb1df0@0" ObjectIDZND1="31722@1" Pin0InfoVect0LinkObjId="g_1cb1df0_0" Pin0InfoVect1LinkObjId="g_22eb3f0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bc43a0_0" Pin1InfoVect1LinkObjId="SW-118437_0" Pin1InfoVect2LinkObjId="SW-118434_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1168 4252,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22eb3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1183 4252,-1198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1cb1df0@0" ObjectIDND1="g_1bc43a0@0" ObjectIDND2="22036@x" ObjectIDZND0="31722@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cb1df0_0" Pin1InfoVect1LinkObjId="g_1bc43a0_0" Pin1InfoVect2LinkObjId="SW-118437_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1183 4252,-1198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22ee550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-1099 3962,-1135 4050,-1135 4050,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="22042@x" ObjectIDND1="22039@x" ObjectIDZND0="22089@0" Pin0InfoVect0LinkObjId="g_1cac1b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118444_0" Pin1InfoVect1LinkObjId="SW-118441_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-1099 3962,-1135 4050,-1135 4050,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22efaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-680 4050,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22043@1" ObjectIDZND0="22045@0" Pin0InfoVect0LinkObjId="SW-118447_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118445_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-680 4050,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f05e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-786 4050,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22089@1" ObjectIDZND0="g_1bf3130@0" ObjectIDZND1="22045@x" Pin0InfoVect0LinkObjId="g_1bf3130_0" Pin0InfoVect1LinkObjId="SW-118447_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cac1b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-786 4050,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f0840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-772 4050,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22089@x" ObjectIDND1="g_1bf3130@0" ObjectIDZND0="22045@1" Pin0InfoVect0LinkObjId="SW-118447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cac1b0_0" Pin1InfoVect1LinkObjId="g_1bf3130_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-772 4050,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22f84c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-886 3769,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22030@0" ObjectIDZND0="22049@x" ObjectIDZND1="22048@x" Pin0InfoVect0LinkObjId="SW-118452_0" Pin0InfoVect1LinkObjId="SW-118451_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-886 3769,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22f8720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-925 3769,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="22049@x" ObjectIDND1="22030@0" ObjectIDZND0="22048@0" Pin0InfoVect0LinkObjId="SW-118451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-925 3769,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22f8980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-925 3818,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22f6710@0" ObjectIDZND0="22049@0" Pin0InfoVect0LinkObjId="SW-118452_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f6710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-925 3818,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22f8be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3782,-925 3769,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="22049@1" ObjectIDZND0="22048@x" ObjectIDZND1="22030@0" Pin0InfoVect0LinkObjId="SW-118451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118452_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3782,-925 3769,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22fc460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-978 3769,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="22048@1" ObjectIDZND0="g_22f2b70@0" ObjectIDZND1="g_1cb1040@0" ObjectIDZND2="22050@x" Pin0InfoVect0LinkObjId="g_22f2b70_0" Pin0InfoVect1LinkObjId="g_1cb1040_0" Pin0InfoVect2LinkObjId="SW-118453_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-978 3769,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22fc6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-1027 3769,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_22f2b70@0" ObjectIDND1="22048@x" ObjectIDND2="22050@x" ObjectIDZND0="g_1cb1040@0" Pin0InfoVect0LinkObjId="g_1cb1040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22f2b70_0" Pin1InfoVect1LinkObjId="SW-118451_0" Pin1InfoVect2LinkObjId="SW-118453_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-1027 3769,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22fc920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-1028 3748,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1cb1040@0" ObjectIDND1="22048@x" ObjectIDND2="22050@x" ObjectIDZND0="g_22f2b70@0" Pin0InfoVect0LinkObjId="g_22f2b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cb1040_0" Pin1InfoVect1LinkObjId="SW-118451_0" Pin1InfoVect2LinkObjId="SW-118453_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-1028 3748,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22fe080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-261 3685,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_22fd600@0" Pin0InfoVect0LinkObjId="g_22fd600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fba580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-261 3685,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22fe2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3667,-261 3652,-261 3652,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_22fd600@1" ObjectIDZND0="22086@1" Pin0InfoVect0LinkObjId="SW-118489_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fd600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3667,-261 3652,-261 3652,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ffe80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-397 3937,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22076@1" ObjectIDZND0="22074@1" Pin0InfoVect0LinkObjId="SW-118477_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118479_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-397 3937,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2300b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-332 3978,-332 3978,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="22076@x" ObjectIDND1="22078@x" ObjectIDND2="g_1c2bd10@0" ObjectIDZND0="g_2300070@0" Pin0InfoVect0LinkObjId="g_2300070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118479_0" Pin1InfoVect1LinkObjId="SW-118481_0" Pin1InfoVect2LinkObjId="g_1c2bd10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-332 3978,-332 3978,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2301650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-344 3937,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22076@x" ObjectIDND1="22078@x" ObjectIDZND0="g_1c2bd10@0" ObjectIDZND1="g_2300070@0" Pin0InfoVect0LinkObjId="g_1c2bd10_0" Pin0InfoVect1LinkObjId="g_2300070_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118479_0" Pin1InfoVect1LinkObjId="SW-118481_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-344 3937,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23018b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-319 3937,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c2bd10@1" ObjectIDZND0="22076@x" ObjectIDZND1="22078@x" ObjectIDZND2="g_2300070@0" Pin0InfoVect0LinkObjId="SW-118479_0" Pin0InfoVect1LinkObjId="SW-118481_0" Pin0InfoVect2LinkObjId="g_2300070_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c2bd10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-319 3937,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2304270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-284 3937,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="22079@1" ObjectIDZND0="g_1c2bd10@0" Pin0InfoVect0LinkObjId="g_1c2bd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118482_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-284 3937,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2307d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-230 3937,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="22080@x" ObjectIDZND0="22079@0" Pin0InfoVect0LinkObjId="SW-118482_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fba580_0" Pin1InfoVect1LinkObjId="SW-118483_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-230 3937,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2307f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3860,-230 3881,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2306a00@0" ObjectIDZND0="22080@0" Pin0InfoVect0LinkObjId="SW-118483_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2306a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3860,-230 3881,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23081e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-230 3937,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="22080@1" ObjectIDZND0="0@x" ObjectIDZND1="22079@x" Pin0InfoVect0LinkObjId="g_1fba580_0" Pin0InfoVect1LinkObjId="SW-118482_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118483_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-230 3937,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2309ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-168 3937,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1c2d7a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1fba580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c2d7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-168 3937,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230a140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-218 3937,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="22079@x" ObjectIDZND1="22080@x" Pin0InfoVect0LinkObjId="SW-118482_0" Pin0InfoVect1LinkObjId="SW-118483_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fba580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-218 3937,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230a4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-552 4280,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22093@0" ObjectIDZND0="22065@1" Pin0InfoVect0LinkObjId="SW-118468_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-552 4280,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-468 4262,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22064@x" ObjectIDND1="22065@x" ObjectIDZND0="22067@1" Pin0InfoVect0LinkObjId="SW-118470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118467_0" Pin1InfoVect1LinkObjId="SW-118468_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-468 4262,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230a9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4226,-468 4216,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22067@0" ObjectIDZND0="g_2319c60@0" Pin0InfoVect0LinkObjId="g_2319c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4226,-468 4216,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230ac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-489 4280,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22065@0" ObjectIDZND0="22064@x" ObjectIDZND1="22067@x" Pin0InfoVect0LinkObjId="SW-118467_0" Pin0InfoVect1LinkObjId="SW-118470_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-489 4280,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230ce10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-468 4280,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22065@x" ObjectIDND1="22067@x" ObjectIDZND0="22064@0" Pin0InfoVect0LinkObjId="SW-118467_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118468_0" Pin1InfoVect1LinkObjId="SW-118470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-468 4280,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230d070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-340 4261,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_23125f0@0" ObjectIDND1="43326@x" ObjectIDND2="22066@x" ObjectIDZND0="22068@1" Pin0InfoVect0LinkObjId="SW-118471_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23125f0_0" Pin1InfoVect1LinkObjId="SM-CX_HW.P3_0" Pin1InfoVect2LinkObjId="SW-118469_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-340 4261,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230d2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-340 4215,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22068@0" ObjectIDZND0="g_231a4e0@0" Pin0InfoVect0LinkObjId="g_231a4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-340 4215,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2313eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-312 4325,-312 4325,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="generator" EndDevType0="lightningRod" ObjectIDND0="22066@x" ObjectIDND1="22068@x" ObjectIDND2="43326@x" ObjectIDZND0="g_23125f0@0" Pin0InfoVect0LinkObjId="g_23125f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118469_0" Pin1InfoVect1LinkObjId="SW-118471_0" Pin1InfoVect2LinkObjId="SM-CX_HW.P3_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-312 4325,-312 4325,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23140a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-404 4280,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22066@1" ObjectIDZND0="22064@1" Pin0InfoVect0LinkObjId="SW-118467_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-404 4280,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2314290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-368 4280,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="22066@0" ObjectIDZND0="g_23125f0@0" ObjectIDZND1="43326@x" ObjectIDZND2="22068@x" Pin0InfoVect0LinkObjId="g_23125f0_0" Pin0InfoVect1LinkObjId="SM-CX_HW.P3_0" Pin0InfoVect2LinkObjId="SW-118471_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118469_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-368 4280,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2314480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-340 4280,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="22066@x" ObjectIDND1="22068@x" ObjectIDZND0="g_23125f0@0" ObjectIDZND1="43326@x" Pin0InfoVect0LinkObjId="g_23125f0_0" Pin0InfoVect1LinkObjId="SM-CX_HW.P3_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118469_0" Pin1InfoVect1LinkObjId="SW-118471_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-340 4280,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2319a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-312 4280,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="generator" ObjectIDND0="22066@x" ObjectIDND1="22068@x" ObjectIDND2="g_23125f0@0" ObjectIDZND0="43326@0" Pin0InfoVect0LinkObjId="SM-CX_HW.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118469_0" Pin1InfoVect1LinkObjId="SW-118471_0" Pin1InfoVect2LinkObjId="g_23125f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-312 4280,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231ca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-552 4422,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22093@0" ObjectIDZND0="22060@1" Pin0InfoVect0LinkObjId="SW-118463_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-552 4422,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231ccb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-469 4404,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22059@x" ObjectIDND1="22060@x" ObjectIDZND0="22062@1" Pin0InfoVect0LinkObjId="SW-118465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118462_0" Pin1InfoVect1LinkObjId="SW-118463_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-469 4404,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231cf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-469 4358,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22062@0" ObjectIDZND0="g_1f384e0@0" Pin0InfoVect0LinkObjId="g_1f384e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118465_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-469 4358,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231d170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-490 4422,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22060@0" ObjectIDZND0="22059@x" ObjectIDZND1="22062@x" Pin0InfoVect0LinkObjId="SW-118462_0" Pin0InfoVect1LinkObjId="SW-118465_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-490 4422,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231f420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-469 4422,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22060@x" ObjectIDND1="22062@x" ObjectIDZND0="22059@0" Pin0InfoVect0LinkObjId="SW-118462_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118463_0" Pin1InfoVect1LinkObjId="SW-118465_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-469 4422,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231f680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-341 4403,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1f30e10@0" ObjectIDND1="43325@x" ObjectIDND2="22061@x" ObjectIDZND0="22063@1" Pin0InfoVect0LinkObjId="SW-118466_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f30e10_0" Pin1InfoVect1LinkObjId="SM-CX_HW.P2_0" Pin1InfoVect2LinkObjId="SW-118464_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-341 4403,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231f8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4367,-341 4357,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22063@0" ObjectIDZND0="g_1f38d00@0" Pin0InfoVect0LinkObjId="g_1f38d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118466_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4367,-341 4357,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f32700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-313 4467,-313 4467,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="generator" EndDevType0="lightningRod" ObjectIDND0="22061@x" ObjectIDND1="22063@x" ObjectIDND2="43325@x" ObjectIDZND0="g_1f30e10@0" Pin0InfoVect0LinkObjId="g_1f30e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118464_0" Pin1InfoVect1LinkObjId="SW-118466_0" Pin1InfoVect2LinkObjId="SM-CX_HW.P2_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-313 4467,-313 4467,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f328f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-405 4422,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22061@1" ObjectIDZND0="22059@1" Pin0InfoVect0LinkObjId="SW-118462_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118464_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-405 4422,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f32ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-369 4422,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="22061@0" ObjectIDZND0="g_1f30e10@0" ObjectIDZND1="43325@x" ObjectIDZND2="22063@x" Pin0InfoVect0LinkObjId="g_1f30e10_0" Pin0InfoVect1LinkObjId="SM-CX_HW.P2_0" Pin0InfoVect2LinkObjId="SW-118466_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118464_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-369 4422,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f32cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-341 4422,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="22061@x" ObjectIDND1="22063@x" ObjectIDZND0="g_1f30e10@0" ObjectIDZND1="43325@x" Pin0InfoVect0LinkObjId="g_1f30e10_0" Pin0InfoVect1LinkObjId="SM-CX_HW.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118464_0" Pin1InfoVect1LinkObjId="SW-118466_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-341 4422,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f382f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-313 4422,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="generator" ObjectIDND0="22061@x" ObjectIDND1="22063@x" ObjectIDND2="g_1f30e10@0" ObjectIDZND0="43325@0" Pin0InfoVect0LinkObjId="SM-CX_HW.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118464_0" Pin1InfoVect1LinkObjId="SW-118466_0" Pin1InfoVect2LinkObjId="g_1f30e10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-313 4422,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3b1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-552 4546,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22093@0" ObjectIDZND0="22052@1" Pin0InfoVect0LinkObjId="SW-118455_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-552 4546,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3b400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-470 4528,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1f420d0@0" ObjectIDND1="22052@x" ObjectIDZND0="22053@1" Pin0InfoVect0LinkObjId="SW-118456_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f420d0_0" Pin1InfoVect1LinkObjId="SW-118455_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-470 4528,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3b660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-470 4482,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22053@0" ObjectIDZND0="g_1f456a0@0" Pin0InfoVect0LinkObjId="g_1f456a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-470 4482,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-491 4546,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="22052@0" ObjectIDZND0="g_1f420d0@0" ObjectIDZND1="22053@x" Pin0InfoVect0LinkObjId="g_1f420d0_0" Pin0InfoVect1LinkObjId="SW-118456_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118455_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-491 4546,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-470 4546,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="22052@x" ObjectIDND1="22053@x" ObjectIDZND0="g_1f420d0@0" Pin0InfoVect0LinkObjId="g_1f420d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118455_0" Pin1InfoVect1LinkObjId="SW-118456_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-470 4546,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f469c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-552 3577,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22093@0" ObjectIDZND0="22087@1" Pin0InfoVect0LinkObjId="SW-118490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-552 3577,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f46c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-467 3559,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22087@x" ObjectIDZND0="22088@1" Pin0InfoVect0LinkObjId="SW-118491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-467 3559,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f46e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3523,-467 3513,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22088@0" ObjectIDZND0="g_1f4cb40@0" Pin0InfoVect0LinkObjId="g_1f4cb40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3523,-467 3513,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f470e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-488 3577,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22087@0" ObjectIDZND0="22088@x" Pin0InfoVect0LinkObjId="SW-118491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-488 3577,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f47340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-467 3577,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="22087@x" ObjectIDND1="22088@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118490_0" Pin1InfoVect1LinkObjId="SW-118491_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-467 3577,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f4e310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-552 4685,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22093@0" ObjectIDZND0="22055@1" Pin0InfoVect0LinkObjId="SW-118458_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-552 4685,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f4e500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-467 4667,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22054@x" ObjectIDND1="22055@x" ObjectIDZND0="22057@1" Pin0InfoVect0LinkObjId="SW-118460_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118457_0" Pin1InfoVect1LinkObjId="SW-118458_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-467 4667,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f4e6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-467 4621,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22057@0" ObjectIDZND0="g_1f5d890@0" Pin0InfoVect0LinkObjId="g_1f5d890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-467 4621,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f4e920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-488 4685,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22055@0" ObjectIDZND0="22054@x" ObjectIDZND1="22057@x" Pin0InfoVect0LinkObjId="SW-118457_0" Pin0InfoVect1LinkObjId="SW-118460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118458_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-488 4685,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f509e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-467 4685,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22055@x" ObjectIDND1="22057@x" ObjectIDZND0="22054@0" Pin0InfoVect0LinkObjId="SW-118457_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118458_0" Pin1InfoVect1LinkObjId="SW-118460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-467 4685,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f50c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-339 4666,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1f561c0@0" ObjectIDND1="43324@x" ObjectIDND2="22056@x" ObjectIDZND0="22058@1" Pin0InfoVect0LinkObjId="SW-118461_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f561c0_0" Pin1InfoVect1LinkObjId="SM-CX_HW.P1_0" Pin1InfoVect2LinkObjId="SW-118459_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-339 4666,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f50ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4630,-339 4620,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22058@0" ObjectIDZND0="g_1f5e0b0@0" Pin0InfoVect0LinkObjId="g_1f5e0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118461_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4630,-339 4620,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f57ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-311 4730,-311 4730,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="generator" EndDevType0="lightningRod" ObjectIDND0="22056@x" ObjectIDND1="22058@x" ObjectIDND2="43324@x" ObjectIDZND0="g_1f561c0@0" Pin0InfoVect0LinkObjId="g_1f561c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118459_0" Pin1InfoVect1LinkObjId="SW-118461_0" Pin1InfoVect2LinkObjId="SM-CX_HW.P1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-311 4730,-311 4730,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f57ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-403 4685,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22056@1" ObjectIDZND0="22054@1" Pin0InfoVect0LinkObjId="SW-118457_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118459_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-403 4685,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f57e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-367 4685,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="22056@0" ObjectIDZND0="g_1f561c0@0" ObjectIDZND1="43324@x" ObjectIDZND2="22058@x" Pin0InfoVect0LinkObjId="g_1f561c0_0" Pin0InfoVect1LinkObjId="SM-CX_HW.P1_0" Pin0InfoVect2LinkObjId="SW-118461_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118459_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-367 4685,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f58080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-339 4685,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="22056@x" ObjectIDND1="22058@x" ObjectIDZND0="g_1f561c0@0" ObjectIDZND1="43324@x" Pin0InfoVect0LinkObjId="g_1f561c0_0" Pin0InfoVect1LinkObjId="SM-CX_HW.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118459_0" Pin1InfoVect1LinkObjId="SW-118461_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-339 4685,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f5d6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-311 4685,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="generator" ObjectIDND0="22056@x" ObjectIDND1="22058@x" ObjectIDND2="g_1f561c0@0" ObjectIDZND0="43324@0" Pin0InfoVect0LinkObjId="SM-CX_HW.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118459_0" Pin1InfoVect1LinkObjId="SW-118461_0" Pin1InfoVect2LinkObjId="g_1f561c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-311 4685,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f60550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4616,-926 4616,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1c300e0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1fba580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c300e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4616,-926 4616,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f6a3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-1027 3784,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_22f2b70@0" ObjectIDND1="g_1cb1040@0" ObjectIDND2="22048@x" ObjectIDZND0="22050@0" Pin0InfoVect0LinkObjId="SW-118453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22f2b70_0" Pin1InfoVect1LinkObjId="g_1cb1040_0" Pin1InfoVect2LinkObjId="SW-118451_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-1027 3784,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f6a650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-1027 3839,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22050@1" ObjectIDZND0="g_22f71a0@0" Pin0InfoVect0LinkObjId="g_22f71a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-1027 3839,-1027 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22030" cx="3962" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22093" cx="4050" cy="-552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22030" cx="4252" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22093" cx="4123" cy="-552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22093" cx="3714" cy="-552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22093" cx="3937" cy="-552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22093" cx="4280" cy="-552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22093" cx="4422" cy="-552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22093" cx="4546" cy="-552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22093" cx="3577" cy="-552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22093" cx="4685" cy="-552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22030" cx="3769" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4616" cy="-738" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4855" cy="-740" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-93764" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3435.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19737" ObjectName="DYN-CX_HW"/>
     <cge:Meas_Ref ObjectId="93764"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c54bb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3440.000000 -545.000000) translate(0,15)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cac410" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4064.500000 -146.000000) translate(0,15)">(1-12号方阵)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cac410" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4064.500000 -146.000000) translate(0,33)">   12MWp</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d27a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -938.000000) translate(0,12)">1611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d280b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -953.000000) translate(0,12)">K1900</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d282f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3718.000000 -975.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d28530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3779.000000 -1019.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d28770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.000000 -996.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d289b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3906.500000 -1059.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d28bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -764.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d29320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.500000 -738.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d29780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4062.000000 -678.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d299c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -638.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d29c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4057.000000 -587.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d29e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3527.000000 -878.000000) translate(0,12)">110kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2a2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4131.500000 -443.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.500000 -515.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2a760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4065.500000 -491.000000) translate(0,12)">36317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2a9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.000000 -393.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2abe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4065.000000 -364.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1d2c500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -1165.500000) translate(0,16)">河外升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9af60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -758.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9ba30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3888.000000 -810.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9bd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.500000 -1242.000000) translate(0,12)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9bd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.500000 -1242.000000) translate(0,27)">黄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9bd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.500000 -1242.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -873.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -873.000000) translate(0,27)">SFZ11-50000/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -873.000000) translate(0,42)">121±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -873.000000) translate(0,57)">50MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -873.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -873.000000) translate(0,87)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bcee60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4080.000000 -165.000000) translate(0,15)">4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb0a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3722.500000 -445.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb0ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3718.500000 -517.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb1130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3657.500000 -493.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb1370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3718.000000 -395.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb15b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3656.000000 -366.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23279d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3607.000000 -248.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2327ec0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3736.000000 -264.000000) translate(0,15)">315kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_232f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3945.500000 -447.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_232fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.500000 -519.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23300d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.500000 -495.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2330310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -386.000000) translate(0,12)">3623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2330550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -368.000000) translate(0,12)">36237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c2e580" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3849.500000 -134.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2eb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3908.000000 -115.000000) translate(0,12)">±20MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c2f6d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4564.000000 -1162.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c2fe60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4577.500000 -1140.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c32a60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4832.000000 -1176.000000) translate(0,15)">10kV凹鮓线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c32a60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4832.000000 -1176.000000) translate(0,33)">跨榜支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c343e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4899.000000 -1014.000000) translate(0,15)">3号站用用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c343e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4899.000000 -1014.000000) translate(0,33)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c352f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4579.000000 -729.000000) translate(0,15)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c35a50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4800.000000 -729.000000) translate(0,15)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c43650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3670.000000 -179.000000) translate(0,15)">1号站用变及</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c43650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3670.000000 -179.000000) translate(0,33)">小电阻接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22e5050" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4509.000000 -1048.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22e5680" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3250.000000 -230.000000) translate(0,15)">0878-6017507</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22e5680" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3250.000000 -230.000000) translate(0,33)">8386626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_22e59d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3114.000000 -764.000000) translate(0,15)">全站检修停电前应挂“全站检修”牌，“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_22e59d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3114.000000 -764.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_22e59d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3114.000000 -764.000000) translate(0,51)">全站检修完工后仅可摘除“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_22e59d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3114.000000 -764.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_22e59d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3114.000000 -764.000000) translate(0,87)">全站检修复电后才可以摘除“全站检修”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22edf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.500000 -1129.000000) translate(0,12)">K1017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23086e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -268.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2308d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -223.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2311f90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4221.500000 -146.000000) translate(0,15)">(13-25号方阵)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2311f90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4221.500000 -146.000000) translate(0,33)">   13MWp</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23130f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.500000 -443.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23135b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.500000 -515.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23137f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.500000 -491.000000) translate(0,12)">36417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2313a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -393.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2313c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -364.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2319580" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4237.000000 -165.000000) translate(0,15)">3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f307b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4363.500000 -147.000000) translate(0,15)">(26-37号方阵)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f307b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4363.500000 -147.000000) translate(0,33)">   12MWp</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f317d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.500000 -444.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f31e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.500000 -516.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f32040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4364.500000 -492.000000) translate(0,12)">36517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f32280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -394.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f324c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4364.000000 -365.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f37cc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4379.000000 -166.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f40ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4506.500000 -514.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f410e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4486.500000 -462.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f41320" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4496.000000 -326.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f41320" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4496.000000 -326.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f4c2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.500000 -511.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f4c900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.500000 -459.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f4d320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -435.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f55b60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4626.500000 -145.000000) translate(0,15)">(38-50号方阵)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f55b60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4626.500000 -145.000000) translate(0,33)">   13MWp</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f56b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4693.500000 -442.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f571b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4689.500000 -514.000000) translate(0,12)">3661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f573f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.500000 -490.000000) translate(0,12)">36617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f57630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4689.000000 -392.000000) translate(0,12)">3666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f57870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.000000 -363.000000) translate(0,12)">36667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f5d070" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4642.000000 -164.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f648e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4832.500000 -1140.000000) translate(0,15)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6a8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -734.000000) translate(0,12)">绕温（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6b8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -712.000000) translate(0,12)">油温1（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f788d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -1010.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f78f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3969.000000 -938.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f79140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3969.000000 -1072.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f79380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -1132.000000) translate(0,12)">K1617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f795c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -1076.000000) translate(0,12)">1616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f79800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4197.000000 -1060.000000) translate(0,12)">16160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f79a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4197.000000 -998.000000) translate(0,12)">16117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7b980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.000000 -1012.000000) translate(0,12)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_234f280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3433.000000 -1035.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="Line_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3953,-772 3970,-772 3970,-794 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.222222" x1="3970" x2="3970" y1="-809" y2="-813"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="3970" x2="3970" y1="-851" y2="-830"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3714" x2="3714" y1="-261" y2="-261"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3933" x2="3897" y1="-267" y2="-267"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3897" x2="3897" y1="-267" y2="-233"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4616" x2="4638" y1="-856" y2="-856"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4662" x2="4694" y1="-854" y2="-854"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4701" x2="4721" y1="-854" y2="-854"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4721" x2="4721" y1="-859" y2="-850"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4723" x2="4723" y1="-858" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4725" x2="4725" y1="-857" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4596" x2="4638" y1="-765" y2="-765"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4596" x2="4585" y1="-764" y2="-782"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4638" x2="4661" y1="-765" y2="-778"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4588" x2="4657" y1="-776" y2="-776"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4642" x2="4642" y1="-801" y2="-788"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4830" x2="4872" y1="-766" y2="-766"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4830" x2="4819" y1="-765" y2="-783"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4872" x2="4895" y1="-766" y2="-779"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4822" x2="4891" y1="-777" y2="-777"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4875" x2="4853" y1="-859" y2="-859"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4829" x2="4797" y1="-857" y2="-857"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4790" x2="4770" y1="-857" y2="-857"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4770" x2="4770" y1="-862" y2="-853"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4768" x2="4768" y1="-861" y2="-855"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4766" x2="4766" y1="-860" y2="-855"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4616" x2="4848" y1="-839" y2="-839"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4848" x2="4848" y1="-839" y2="-837"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4875" x2="4642" y1="-836" y2="-836"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4642" x2="4642" y1="-836" y2="-821"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4615" x2="4615" y1="-801" y2="-788"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4850" x2="4850" y1="-802" y2="-789"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4876" x2="4876" y1="-802" y2="-789"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4848" x2="4848" y1="-830" y2="-822"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-118439">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -981.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22037" ObjectName="SW-CX_HW.CX_HW_101BK"/>
     <cge:Meas_Ref ObjectId="118439"/>
    <cge:TPSR_Ref TObjectID="22037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118472">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4114.250000 -458.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22069" ObjectName="SW-CX_HW.CX_HW_363BK"/>
     <cge:Meas_Ref ObjectId="118472"/>
    <cge:TPSR_Ref TObjectID="22069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118484">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3705.250000 -460.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22081" ObjectName="SW-CX_HW.CX_HW_361BK"/>
     <cge:Meas_Ref ObjectId="118484"/>
    <cge:TPSR_Ref TObjectID="22081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118477">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3928.250000 -462.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22074" ObjectName="SW-CX_HW.CX_HW_362BK"/>
     <cge:Meas_Ref ObjectId="118477"/>
    <cge:TPSR_Ref TObjectID="22074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -644.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22043" ObjectName="SW-CX_HW.CX_HW_301BK"/>
     <cge:Meas_Ref ObjectId="118445"/>
    <cge:TPSR_Ref TObjectID="22043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118467">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4271.250000 -458.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22064" ObjectName="SW-CX_HW.CX_HW_364BK"/>
     <cge:Meas_Ref ObjectId="118467"/>
    <cge:TPSR_Ref TObjectID="22064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118462">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4413.250000 -459.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22059" ObjectName="SW-CX_HW.CX_HW_365BK"/>
     <cge:Meas_Ref ObjectId="118462"/>
    <cge:TPSR_Ref TObjectID="22059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118457">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4676.250000 -457.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22054" ObjectName="SW-CX_HW.CX_HW_366BK"/>
     <cge:Meas_Ref ObjectId="118457"/>
    <cge:TPSR_Ref TObjectID="22054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118432">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -983.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22031" ObjectName="SW-CX_HW.CX_HW_161BK"/>
     <cge:Meas_Ref ObjectId="118432"/>
    <cge:TPSR_Ref TObjectID="22031"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HW.CX_HW_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3464,-552 4734,-552 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22093" ObjectName="BS-CX_HW.CX_HW_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="22093"/></metadata>
   <polyline fill="none" opacity="0" points="3464,-552 4734,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HW.CX_HW_1ⅠM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3533,-886 4387,-886 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22030" ObjectName="BS-CX_HW.CX_HW_1ⅠM"/>
    <cge:TPSR_Ref TObjectID="22030"/></metadata>
   <polyline fill="none" opacity="0" points="3533,-886 4387,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4566,-738 4712,-738 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4566,-738 4712,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4773,-740 4920,-740 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4773,-740 4920,-740 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1cb1040">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -1040.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cb1df0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4287.000000 -1118.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf23c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -779.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf3130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.000000 -714.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf3ee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.000000 -242.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fafc50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -268.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c2bd10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.772727 3927.000000 -289.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c2d7a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -138.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c2edb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -1063.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c300e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 -868.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c30ec0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4869.000000 -1065.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c31700">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4870.000000 -869.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22fcb80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.613636 3704.000000 -289.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22fd600">
    <use class="BV-35KV" transform="matrix(0.547619 -0.000000 0.000000 -1.000000 3665.000000 -252.000000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22fe540">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3645.000000 -173.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2300070">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -265.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23125f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.000000 -242.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f30e10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4460.000000 -243.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f561c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 -241.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118400" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4379.000000 -1028.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118400" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22031"/>
     <cge:Term_Ref ObjectID="30856"/>
    <cge:TPSR_Ref TObjectID="22031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4379.000000 -1028.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22031"/>
     <cge:Term_Ref ObjectID="30856"/>
    <cge:TPSR_Ref TObjectID="22031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118398" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4379.000000 -1028.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22031"/>
     <cge:Term_Ref ObjectID="30856"/>
    <cge:TPSR_Ref TObjectID="22031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118386" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3991.000000 -1191.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22037"/>
     <cge:Term_Ref ObjectID="30868"/>
    <cge:TPSR_Ref TObjectID="22037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3991.000000 -1191.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22037"/>
     <cge:Term_Ref ObjectID="30868"/>
    <cge:TPSR_Ref TObjectID="22037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3991.000000 -1191.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22037"/>
     <cge:Term_Ref ObjectID="30868"/>
    <cge:TPSR_Ref TObjectID="22037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118393" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -681.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22043"/>
     <cge:Term_Ref ObjectID="30880"/>
    <cge:TPSR_Ref TObjectID="22043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118394" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -681.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22043"/>
     <cge:Term_Ref ObjectID="30880"/>
    <cge:TPSR_Ref TObjectID="22043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -681.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22043"/>
     <cge:Term_Ref ObjectID="30880"/>
    <cge:TPSR_Ref TObjectID="22043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3716.000000 -94.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22081"/>
     <cge:Term_Ref ObjectID="30956"/>
    <cge:TPSR_Ref TObjectID="22081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118420" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3716.000000 -94.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118420" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22081"/>
     <cge:Term_Ref ObjectID="30956"/>
    <cge:TPSR_Ref TObjectID="22081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118416" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3716.000000 -94.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118416" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22081"/>
     <cge:Term_Ref ObjectID="30956"/>
    <cge:TPSR_Ref TObjectID="22081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 -94.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118425" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22074"/>
     <cge:Term_Ref ObjectID="30942"/>
    <cge:TPSR_Ref TObjectID="22074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 -94.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22074"/>
     <cge:Term_Ref ObjectID="30942"/>
    <cge:TPSR_Ref TObjectID="22074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 -94.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22074"/>
     <cge:Term_Ref ObjectID="30942"/>
    <cge:TPSR_Ref TObjectID="22074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118380" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4095.000000 -94.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118380" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22069"/>
     <cge:Term_Ref ObjectID="30932"/>
    <cge:TPSR_Ref TObjectID="22069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118381" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4095.000000 -94.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22069"/>
     <cge:Term_Ref ObjectID="30932"/>
    <cge:TPSR_Ref TObjectID="22069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118377" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4095.000000 -94.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118377" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22069"/>
     <cge:Term_Ref ObjectID="30932"/>
    <cge:TPSR_Ref TObjectID="22069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118374" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -94.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118374" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22064"/>
     <cge:Term_Ref ObjectID="30922"/>
    <cge:TPSR_Ref TObjectID="22064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -94.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22064"/>
     <cge:Term_Ref ObjectID="30922"/>
    <cge:TPSR_Ref TObjectID="22064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118371" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -94.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118371" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22064"/>
     <cge:Term_Ref ObjectID="30922"/>
    <cge:TPSR_Ref TObjectID="22064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4396.000000 -94.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22059"/>
     <cge:Term_Ref ObjectID="30912"/>
    <cge:TPSR_Ref TObjectID="22059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118369" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4396.000000 -94.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118369" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22059"/>
     <cge:Term_Ref ObjectID="30912"/>
    <cge:TPSR_Ref TObjectID="22059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118365" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4396.000000 -94.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118365" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22059"/>
     <cge:Term_Ref ObjectID="30912"/>
    <cge:TPSR_Ref TObjectID="22059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118362" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4658.000000 -94.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118362" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22054"/>
     <cge:Term_Ref ObjectID="30902"/>
    <cge:TPSR_Ref TObjectID="22054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118363" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4658.000000 -94.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118363" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22054"/>
     <cge:Term_Ref ObjectID="30902"/>
    <cge:TPSR_Ref TObjectID="22054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118359" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4658.000000 -94.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118359" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22054"/>
     <cge:Term_Ref ObjectID="30902"/>
    <cge:TPSR_Ref TObjectID="22054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-118410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -620.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22093"/>
     <cge:Term_Ref ObjectID="27542"/>
    <cge:TPSR_Ref TObjectID="22093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-118411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -620.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22093"/>
     <cge:Term_Ref ObjectID="27542"/>
    <cge:TPSR_Ref TObjectID="22093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-118412" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -620.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118412" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22093"/>
     <cge:Term_Ref ObjectID="27542"/>
    <cge:TPSR_Ref TObjectID="22093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-118413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -620.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22093"/>
     <cge:Term_Ref ObjectID="27542"/>
    <cge:TPSR_Ref TObjectID="22093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-118404" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -951.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118404" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22030"/>
     <cge:Term_Ref ObjectID="30855"/>
    <cge:TPSR_Ref TObjectID="22030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-118405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -951.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22030"/>
     <cge:Term_Ref ObjectID="30855"/>
    <cge:TPSR_Ref TObjectID="22030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-118406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -951.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22030"/>
     <cge:Term_Ref ObjectID="30855"/>
    <cge:TPSR_Ref TObjectID="22030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-118407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -951.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22030"/>
     <cge:Term_Ref ObjectID="30855"/>
    <cge:TPSR_Ref TObjectID="22030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-118428" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -758.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118428" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22089"/>
     <cge:Term_Ref ObjectID="30975"/>
    <cge:TPSR_Ref TObjectID="22089"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
   <g href="AVC河外.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3407" y="-1049"/></g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4848,-830 4847,-829 4846,-830 4845,-830 4844,-830 4843,-830 4843,-830 4842,-831 4841,-831 4841,-831 4840,-832 4840,-832 4839,-833 4839,-833 4839,-834 4839,-834 4840,-835 4840,-835 4841,-836 4841,-836 4842,-836 4843,-837 4843,-837 4844,-837 4845,-837 4846,-837 4847,-838 4848,-837 " stroke="rgb(60,120,255)" stroke-width="0.264706"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1bc43a0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4218.000000 -1178.000000)" xlink:href="#voltageTransformer:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f2b70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3695.000000 -1020.000000)" xlink:href="#voltageTransformer:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f420d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -328.000000)" xlink:href="#voltageTransformer:shape87"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-118429" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4212.000000 -734.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118429" ObjectName="CX_HW:CX_HW_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-118430" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4212.000000 -712.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118430" ObjectName="CX_HW:CX_HW_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-118386" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3242.000000 -1010.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118386" ObjectName="CX_HW:CX_HW_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-118387" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3242.000000 -969.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118387" ObjectName="CX_HW:CX_HW_101BK_Q"/>
    </metadata>
   </g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3970,-819 3965,-830 3976,-830 3970,-819 3970,-820 3970,-819 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3970,-805 3965,-794 3976,-794 3970,-805 3970,-804 3970,-805 " stroke="rgb(170,85,127)"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="42" opacity="0" stroke="white" transform="" width="164" x="3246" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3407,-1049 3404,-1052 3404,-998 3407,-1001 3407,-1049" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3407,-1049 3404,-1052 3553,-1052 3550,-1049 3407,-1049" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="3407,-1001 3404,-998 3553,-998 3550,-1001 3407,-1001" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="3550,-1049 3553,-1052 3553,-998 3550,-1001 3550,-1049" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="3407" y="-1049"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3407" y="-1049"/>
    </a>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HW.CX_HW_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30974"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -781.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -781.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22089" ObjectName="TF-CX_HW.CX_HW_1T"/>
    <cge:TPSR_Ref TObjectID="22089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.560000 -0.000000 0.000000 -0.515015 3923.000000 -174.324324)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.560000 -0.000000 0.000000 -0.515015 3923.000000 -174.324324)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 -963.000000)" xlink:href="#transformer2:shape49_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 -963.000000)" xlink:href="#transformer2:shape49_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -934.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -934.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3699.000000 -180.000000)" xlink:href="#transformer2:shape72_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3699.000000 -180.000000)" xlink:href="#transformer2:shape72_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HW"/>
</svg>