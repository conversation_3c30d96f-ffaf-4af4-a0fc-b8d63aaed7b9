<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-242" aopId="3937022" id="thSvg" product="E8000V2" version="1.0" viewBox="2905 -1298 2309 1309">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="67" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <polyline arcFlag="1" points="44,21 45,21 46,21 46,21 47,22 47,22 48,23 48,23 49,24 49,24 49,25 49,26 50,27 50,28 50,28 49,29 49,30 49,31 49,31 48,32 48,32 47,33 47,33 46,34 46,34 45,34 44,34 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,34 45,34 46,34 46,34 47,35 47,35 48,36 48,36 49,37 49,37 49,38 49,39 50,40 50,41 50,41 49,42 49,43 49,44 49,44 48,45 48,45 47,46 47,46 46,47 46,47 45,47 44,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,47 45,47 46,47 46,47 47,48 47,48 48,49 48,49 49,50 49,50 49,51 49,52 50,53 50,54 50,54 49,55 49,56 49,57 49,57 48,58 48,58 47,59 47,59 46,60 46,60 45,60 44,60 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="13" y2="13"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="32"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="21" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="21" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="29" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="88" y2="88"/>
    <polyline points="25,101 27,101 29,100 30,100 32,99 33,98 35,97 36,95 37,93 37,92 38,90 38,88 38,86 37,84 37,83 36,81 35,80 33,78 32,77 30,76 29,76 27,75 25,75 23,75 21,76 20,76 18,77 17,78 15,80 14,81 13,83 13,84 12,86 12,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="67" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="68" y2="68"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="64" x2="64" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="13" stroke-width="0.424575" width="29" x="14" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="4" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="5" x2="5" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="20" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape13">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99998" x1="23" x2="24" y1="63" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="14" x2="14" y1="15" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="14" x2="14" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="11" x2="15" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="5" x2="21" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="9" x2="18" y1="6" y2="6"/>
    <polyline points="14,14 8,17 6,18 6,19 6,19 8,21 11,22 15,24 16,25 16,25 16,26 15,27 11,28 8,30 7,30 7,31 7,32 8,33 11,34 15,36 16,36 16,37 16,38 15,38 11,40 8,41 7,42 7,43 7,44 8,44 11,46 15,47 16,48 16,49 16,49 15,50 11,52 8,53 6,55 6,55 6,56 8,57 14,60 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="2" x2="23" y1="14" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99998" x1="22" x2="13" y1="63" y2="56"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape46">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="26" x2="26" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="45" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="18" x2="14" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="19" x2="12" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="22" x2="10" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="16" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="33" y1="31" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="33" x2="33" y1="38" y2="25"/>
    <circle cx="7" cy="12" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="3" y2="3"/>
    <circle cx="16" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="16" cy="17" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="voltageTransformer:shape45">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="34" x2="34" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="24" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="24" x2="24" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="17" x2="17" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="41" y1="7" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="41" x2="41" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="5" x2="5" y1="11" y2="4"/>
    <circle cx="29" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="26" x2="27" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="22" x2="21" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="21" x2="27" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="21" y2="23"/>
    <circle cx="19" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="21" y2="23"/>
    <circle cx="24" cy="42" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="29" y2="29"/>
    <circle cx="29" cy="33" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="29" y2="29"/>
    <circle cx="19" cy="33" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="31" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape115">
    <circle cx="49" cy="19" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="6" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="36" y1="44" y2="29"/>
    <rect height="6" stroke-width="1" width="14" x="22" y="33"/>
    <polyline points="36,36 50,36 50,19 " stroke-width="1"/>
    <circle cx="49" cy="8" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="48" x2="50" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="48" x2="50" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="50" x2="52" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="24" x2="24" y1="11" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="28" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="28" x2="24" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="35" x2="37" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="35" x2="37" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="37" x2="40" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="35" x2="37" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="37" x2="40" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="35" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="6" x2="6" y1="31" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="2" x2="2" y1="34" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="4" x2="4" y1="33" y2="39"/>
    <circle cx="37" cy="8" r="7.5" stroke-width="0.804311"/>
    <circle cx="37" cy="19" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="48" x2="50" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="48" x2="50" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="50" x2="53" y1="8" y2="8"/>
    <circle cx="27" cy="13" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape116">
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="10" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="9" x2="10" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="6" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="33" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="36" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="35" y1="42" y2="42"/>
    <polyline points="20,8 37,8 37,19 " stroke-width="1"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <rect height="13" stroke-width="1" width="8" x="33" y="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="32" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="31" y1="21" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="27" y2="30"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1585fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1578750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_153f3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14f3470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1541b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1543420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1543d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14ecfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14f8930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1550ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1561160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1561a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_154fde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1567330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14e9c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14ea3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15ad3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a1280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a1a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15400a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15410d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_155f620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1570b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15a2e30" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19ef000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19f8380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19f7a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19f4640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1319" width="2319" x="2900" y="-1303"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3207" x2="3298" y1="-154" y2="-154"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3211" x2="3211" y1="-135" y2="3"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3211" x2="3303" y1="3" y2="3"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3174" y="-1196"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="2906" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="2916" y="-1064"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-165859">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 -689.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26876" ObjectName="SW-CX_SF.CX_SF_1016SW"/>
     <cge:Meas_Ref ObjectId="165859"/>
    <cge:TPSR_Ref TObjectID="26876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165872">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -564.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26880" ObjectName="SW-CX_SF.CX_SF_1010SW"/>
     <cge:Meas_Ref ObjectId="165872"/>
    <cge:TPSR_Ref TObjectID="26880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165893">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 -471.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26889" ObjectName="SW-CX_SF.CX_SF_0016SW"/>
     <cge:Meas_Ref ObjectId="165893"/>
    <cge:TPSR_Ref TObjectID="26889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165892">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 -370.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26888" ObjectName="SW-CX_SF.CX_SF_0011SW"/>
     <cge:Meas_Ref ObjectId="165892"/>
    <cge:TPSR_Ref TObjectID="26888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165881">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26883" ObjectName="SW-CX_SF.CX_SF_3016SW"/>
     <cge:Meas_Ref ObjectId="165881"/>
    <cge:TPSR_Ref TObjectID="26883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165880">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.000000 -587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26882" ObjectName="SW-CX_SF.CX_SF_3011SW"/>
     <cge:Meas_Ref ObjectId="165880"/>
    <cge:TPSR_Ref TObjectID="26882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166293">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3628.000000 -917.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26951" ObjectName="SW-CX_SF.CX_SF_19010SW"/>
     <cge:Meas_Ref ObjectId="166293"/>
    <cge:TPSR_Ref TObjectID="26951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166292">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 -999.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26950" ObjectName="SW-CX_SF.CX_SF_19017SW"/>
     <cge:Meas_Ref ObjectId="166292"/>
    <cge:TPSR_Ref TObjectID="26950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166013">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4778.000000 -1068.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26891" ObjectName="SW-CX_SF.CX_SF_3611SW"/>
     <cge:Meas_Ref ObjectId="166013"/>
    <cge:TPSR_Ref TObjectID="26891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166014">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4932.000000 -1068.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26892" ObjectName="SW-CX_SF.CX_SF_3616SW"/>
     <cge:Meas_Ref ObjectId="166014"/>
    <cge:TPSR_Ref TObjectID="26892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166291">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -943.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26949" ObjectName="SW-CX_SF.CX_SF_1901SW"/>
     <cge:Meas_Ref ObjectId="166291"/>
    <cge:TPSR_Ref TObjectID="26949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165858">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 -836.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26875" ObjectName="SW-CX_SF.CX_SF_1011SW"/>
     <cge:Meas_Ref ObjectId="165858"/>
    <cge:TPSR_Ref TObjectID="26875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166344">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3382.000000 -364.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26957" ObjectName="SW-CX_SF.CX_SF_0901SW"/>
     <cge:Meas_Ref ObjectId="166344"/>
    <cge:TPSR_Ref TObjectID="26957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165791">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26869" ObjectName="SW-CX_SF.CX_SF_1621SW"/>
     <cge:Meas_Ref ObjectId="165791"/>
    <cge:TPSR_Ref TObjectID="26869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165792">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -1032.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26870" ObjectName="SW-CX_SF.CX_SF_1626SW"/>
     <cge:Meas_Ref ObjectId="165792"/>
    <cge:TPSR_Ref TObjectID="26870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165795">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -1086.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26873" ObjectName="SW-CX_SF.CX_SF_16267SW"/>
     <cge:Meas_Ref ObjectId="165795"/>
    <cge:TPSR_Ref TObjectID="26873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165794">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -1018.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26872" ObjectName="SW-CX_SF.CX_SF_16260SW"/>
     <cge:Meas_Ref ObjectId="165794"/>
    <cge:TPSR_Ref TObjectID="26872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165793">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -961.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26871" ObjectName="SW-CX_SF.CX_SF_16217SW"/>
     <cge:Meas_Ref ObjectId="165793"/>
    <cge:TPSR_Ref TObjectID="26871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166299">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.000000 -1003.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26953" ObjectName="SW-CX_SF.CX_SF_19027SW"/>
     <cge:Meas_Ref ObjectId="166299"/>
    <cge:TPSR_Ref TObjectID="26953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166298">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4460.000000 -950.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26952" ObjectName="SW-CX_SF.CX_SF_1902SW"/>
     <cge:Meas_Ref ObjectId="166298"/>
    <cge:TPSR_Ref TObjectID="26952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166300">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -921.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26954" ObjectName="SW-CX_SF.CX_SF_19020SW"/>
     <cge:Meas_Ref ObjectId="166300"/>
    <cge:TPSR_Ref TObjectID="26954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165862">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -670.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26879" ObjectName="SW-CX_SF.CX_SF_10167SW"/>
     <cge:Meas_Ref ObjectId="165862"/>
    <cge:TPSR_Ref TObjectID="26879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165861">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.000000 -746.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26878" ObjectName="SW-CX_SF.CX_SF_10160SW"/>
     <cge:Meas_Ref ObjectId="165861"/>
    <cge:TPSR_Ref TObjectID="26878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165860">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -816.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26877" ObjectName="SW-CX_SF.CX_SF_10117SW"/>
     <cge:Meas_Ref ObjectId="165860"/>
    <cge:TPSR_Ref TObjectID="26877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165726">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -909.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26863" ObjectName="SW-CX_SF.CX_SF_1632SW"/>
     <cge:Meas_Ref ObjectId="165726"/>
    <cge:TPSR_Ref TObjectID="26863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165727">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -1033.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26864" ObjectName="SW-CX_SF.CX_SF_1636SW"/>
     <cge:Meas_Ref ObjectId="165727"/>
    <cge:TPSR_Ref TObjectID="26864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165730">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4275.000000 -1087.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26867" ObjectName="SW-CX_SF.CX_SF_16367SW"/>
     <cge:Meas_Ref ObjectId="165730"/>
    <cge:TPSR_Ref TObjectID="26867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165729">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 -1019.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26866" ObjectName="SW-CX_SF.CX_SF_16360SW"/>
     <cge:Meas_Ref ObjectId="165729"/>
    <cge:TPSR_Ref TObjectID="26866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165728">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 -962.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26865" ObjectName="SW-CX_SF.CX_SF_16327SW"/>
     <cge:Meas_Ref ObjectId="165728"/>
    <cge:TPSR_Ref TObjectID="26865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.000000 -1091.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -1021.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26955" ObjectName="SW-CX_SF.CX_SF_3901SW"/>
     <cge:Meas_Ref ObjectId="166331"/>
    <cge:TPSR_Ref TObjectID="26955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166240">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3421.000000 -301.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26940" ObjectName="SW-CX_SF.CX_SF_0631SW"/>
     <cge:Meas_Ref ObjectId="166240"/>
    <cge:TPSR_Ref TObjectID="26940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3293.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26916" ObjectName="SW-CX_SF.CX_SF_0621SW"/>
     <cge:Meas_Ref ObjectId="166118"/>
    <cge:TPSR_Ref TObjectID="26916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166121">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3250.000000 -245.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26919" ObjectName="SW-CX_SF.CX_SF_06260SW"/>
     <cge:Meas_Ref ObjectId="166121"/>
    <cge:TPSR_Ref TObjectID="26919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166221">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -304.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26937" ObjectName="SW-CX_SF.CX_SF_0681SW"/>
     <cge:Meas_Ref ObjectId="166221"/>
    <cge:TPSR_Ref TObjectID="26937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166222">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -116.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26938" ObjectName="SW-CX_SF.CX_SF_0686SW"/>
     <cge:Meas_Ref ObjectId="166222"/>
    <cge:TPSR_Ref TObjectID="26938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166241">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26941" ObjectName="SW-CX_SF.CX_SF_0121SW"/>
     <cge:Meas_Ref ObjectId="166241"/>
    <cge:TPSR_Ref TObjectID="26941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166242">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26942" ObjectName="SW-CX_SF.CX_SF_1121SW"/>
     <cge:Meas_Ref ObjectId="166242"/>
    <cge:TPSR_Ref TObjectID="26942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166243">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26943" ObjectName="SW-CX_SF.CX_SF_1122SW"/>
     <cge:Meas_Ref ObjectId="166243"/>
    <cge:TPSR_Ref TObjectID="26943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166244">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -999.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26944" ObjectName="SW-CX_SF.CX_SF_11217SW"/>
     <cge:Meas_Ref ObjectId="166244"/>
    <cge:TPSR_Ref TObjectID="26944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166245">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -999.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26945" ObjectName="SW-CX_SF.CX_SF_11227SW"/>
     <cge:Meas_Ref ObjectId="166245"/>
    <cge:TPSR_Ref TObjectID="26945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165883">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4346.000000 -614.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26885" ObjectName="SW-CX_SF.CX_SF_30167SW"/>
     <cge:Meas_Ref ObjectId="165883"/>
    <cge:TPSR_Ref TObjectID="26885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165882">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 -615.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26884" ObjectName="SW-CX_SF.CX_SF_30160SW"/>
     <cge:Meas_Ref ObjectId="165882"/>
    <cge:TPSR_Ref TObjectID="26884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166015">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -1083.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26893" ObjectName="SW-CX_SF.CX_SF_36160SW"/>
     <cge:Meas_Ref ObjectId="166015"/>
    <cge:TPSR_Ref TObjectID="26893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166016">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 -1083.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26894" ObjectName="SW-CX_SF.CX_SF_36167SW"/>
     <cge:Meas_Ref ObjectId="166016"/>
    <cge:TPSR_Ref TObjectID="26894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166034">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -924.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26896" ObjectName="SW-CX_SF.CX_SF_3621SW"/>
     <cge:Meas_Ref ObjectId="166034"/>
    <cge:TPSR_Ref TObjectID="26896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166035">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4933.000000 -924.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26897" ObjectName="SW-CX_SF.CX_SF_3626SW"/>
     <cge:Meas_Ref ObjectId="166035"/>
    <cge:TPSR_Ref TObjectID="26897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.000000 -947.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166036">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -939.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26898" ObjectName="SW-CX_SF.CX_SF_36260SW"/>
     <cge:Meas_Ref ObjectId="166036"/>
    <cge:TPSR_Ref TObjectID="26898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166037">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -939.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26899" ObjectName="SW-CX_SF.CX_SF_36267SW"/>
     <cge:Meas_Ref ObjectId="166037"/>
    <cge:TPSR_Ref TObjectID="26899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.000000 -780.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26902" ObjectName="SW-CX_SF.CX_SF_3636SW"/>
     <cge:Meas_Ref ObjectId="166056"/>
    <cge:TPSR_Ref TObjectID="26902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 -803.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166057">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -795.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26903" ObjectName="SW-CX_SF.CX_SF_36360SW"/>
     <cge:Meas_Ref ObjectId="166057"/>
    <cge:TPSR_Ref TObjectID="26903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166058">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 -795.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26904" ObjectName="SW-CX_SF.CX_SF_36367SW"/>
     <cge:Meas_Ref ObjectId="166058"/>
    <cge:TPSR_Ref TObjectID="26904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166076">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4778.000000 -491.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26906" ObjectName="SW-CX_SF.CX_SF_3642SW"/>
     <cge:Meas_Ref ObjectId="166076"/>
    <cge:TPSR_Ref TObjectID="26906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166077">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4932.000000 -491.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26907" ObjectName="SW-CX_SF.CX_SF_3646SW"/>
     <cge:Meas_Ref ObjectId="166077"/>
    <cge:TPSR_Ref TObjectID="26907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5028.000000 -514.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 -406.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26956" ObjectName="SW-CX_SF.CX_SF_3902SW"/>
     <cge:Meas_Ref ObjectId="166335"/>
    <cge:TPSR_Ref TObjectID="26956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166078">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -506.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26908" ObjectName="SW-CX_SF.CX_SF_36460SW"/>
     <cge:Meas_Ref ObjectId="166078"/>
    <cge:TPSR_Ref TObjectID="26908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166079">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 -506.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26909" ObjectName="SW-CX_SF.CX_SF_36467SW"/>
     <cge:Meas_Ref ObjectId="166079"/>
    <cge:TPSR_Ref TObjectID="26909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166097">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -347.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26911" ObjectName="SW-CX_SF.CX_SF_3652SW"/>
     <cge:Meas_Ref ObjectId="166097"/>
    <cge:TPSR_Ref TObjectID="26911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166098">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4933.000000 -347.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26912" ObjectName="SW-CX_SF.CX_SF_3656SW"/>
     <cge:Meas_Ref ObjectId="166098"/>
    <cge:TPSR_Ref TObjectID="26912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.000000 -370.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166099">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26913" ObjectName="SW-CX_SF.CX_SF_36560SW"/>
     <cge:Meas_Ref ObjectId="166099"/>
    <cge:TPSR_Ref TObjectID="26913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166100">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26914" ObjectName="SW-CX_SF.CX_SF_36567SW"/>
     <cge:Meas_Ref ObjectId="166100"/>
    <cge:TPSR_Ref TObjectID="26914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166264">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4710.000000 -731.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26947" ObjectName="SW-CX_SF.CX_SF_3121SW"/>
     <cge:Meas_Ref ObjectId="166264"/>
    <cge:TPSR_Ref TObjectID="26947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166265">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 -538.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26948" ObjectName="SW-CX_SF.CX_SF_3122SW"/>
     <cge:Meas_Ref ObjectId="166265"/>
    <cge:TPSR_Ref TObjectID="26948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166223">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26939" ObjectName="SW-CX_SF.CX_SF_06860SW"/>
     <cge:Meas_Ref ObjectId="166223"/>
    <cge:TPSR_Ref TObjectID="26939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166201">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26933" ObjectName="SW-CX_SF.CX_SF_0671SW"/>
     <cge:Meas_Ref ObjectId="166201"/>
    <cge:TPSR_Ref TObjectID="26933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166202">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -115.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26934" ObjectName="SW-CX_SF.CX_SF_0676SW"/>
     <cge:Meas_Ref ObjectId="166202"/>
    <cge:TPSR_Ref TObjectID="26934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166203">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3963.000000 -199.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26935" ObjectName="SW-CX_SF.CX_SF_06760SW"/>
     <cge:Meas_Ref ObjectId="166203"/>
    <cge:TPSR_Ref TObjectID="26935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166181">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -305.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26929" ObjectName="SW-CX_SF.CX_SF_0661SW"/>
     <cge:Meas_Ref ObjectId="166181"/>
    <cge:TPSR_Ref TObjectID="26929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166182">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26930" ObjectName="SW-CX_SF.CX_SF_0666SW"/>
     <cge:Meas_Ref ObjectId="166182"/>
    <cge:TPSR_Ref TObjectID="26930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166183">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -201.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26931" ObjectName="SW-CX_SF.CX_SF_06660SW"/>
     <cge:Meas_Ref ObjectId="166183"/>
    <cge:TPSR_Ref TObjectID="26931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3677.000000 -304.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26925" ObjectName="SW-CX_SF.CX_SF_0651SW"/>
     <cge:Meas_Ref ObjectId="166161"/>
    <cge:TPSR_Ref TObjectID="26925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3677.000000 -116.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26926" ObjectName="SW-CX_SF.CX_SF_0656SW"/>
     <cge:Meas_Ref ObjectId="166162"/>
    <cge:TPSR_Ref TObjectID="26926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166163">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26927" ObjectName="SW-CX_SF.CX_SF_06560SW"/>
     <cge:Meas_Ref ObjectId="166163"/>
    <cge:TPSR_Ref TObjectID="26927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166141">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3549.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26921" ObjectName="SW-CX_SF.CX_SF_0641SW"/>
     <cge:Meas_Ref ObjectId="166141"/>
    <cge:TPSR_Ref TObjectID="26921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3549.000000 -115.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26922" ObjectName="SW-CX_SF.CX_SF_0646SW"/>
     <cge:Meas_Ref ObjectId="166142"/>
    <cge:TPSR_Ref TObjectID="26922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166143">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3578.000000 -199.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26923" ObjectName="SW-CX_SF.CX_SF_06460SW"/>
     <cge:Meas_Ref ObjectId="166143"/>
    <cge:TPSR_Ref TObjectID="26923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3293.000000 -131.888889)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26917" ObjectName="SW-CX_SF.CX_SF_0626SW"/>
     <cge:Meas_Ref ObjectId="166119"/>
    <cge:TPSR_Ref TObjectID="26917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3234.000000 -130.888889)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26918" ObjectName="SW-CX_SF.CX_SF_06267SW"/>
     <cge:Meas_Ref ObjectId="166120"/>
    <cge:TPSR_Ref TObjectID="26918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3202.000000 -130.888889)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165887">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4093.000000 -543.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26886" ObjectName="SW-CX_SF.CX_SF_3010SW"/>
     <cge:Meas_Ref ObjectId="165887"/>
    <cge:TPSR_Ref TObjectID="26886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3430.000000 -424.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26958" ObjectName="SW-CX_SF.CX_SF_09017SW"/>
     <cge:Meas_Ref ObjectId="166345"/>
    <cge:TPSR_Ref TObjectID="26958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166055">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26901" ObjectName="SW-CX_SF.CX_SF_3631SW"/>
     <cge:Meas_Ref ObjectId="166055"/>
    <cge:TPSR_Ref TObjectID="26901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 -712.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4311.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29511" ObjectName="SW-CX_SF.CX_SF_0122SW"/>
     <cge:Meas_Ref ObjectId="194318"/>
    <cge:TPSR_Ref TObjectID="29511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194295">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29505" ObjectName="SW-CX_SF.CX_SF_0716SW"/>
     <cge:Meas_Ref ObjectId="194295"/>
    <cge:TPSR_Ref TObjectID="29505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194296">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4453.000000 -198.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29506" ObjectName="SW-CX_SF.CX_SF_07160SW"/>
     <cge:Meas_Ref ObjectId="194296"/>
    <cge:TPSR_Ref TObjectID="29506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194294">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29507" ObjectName="SW-CX_SF.CX_SF_0712SW"/>
     <cge:Meas_Ref ObjectId="194294"/>
    <cge:TPSR_Ref TObjectID="29507"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f7a2d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3746.000000 -1127.000000)" xlink:href="#voltageTransformer:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f5ec60">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -1128.000000)" xlink:href="#voltageTransformer:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fa1190">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5101.000000 -1088.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f9e7b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4403.000000 -1000.000000)" xlink:href="#voltageTransformer:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1611400">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3549.000000 -996.000000)" xlink:href="#voltageTransformer:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35729f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5101.000000 -944.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3547940">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5104.000000 -800.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c3cb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4589.000000 -1018.000000)" xlink:href="#voltageTransformer:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35bbfa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5100.000000 -511.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b4880">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5101.000000 -367.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a44a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4587.000000 -403.000000)" xlink:href="#voltageTransformer:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_255f970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3385.000000 -473.000000)" xlink:href="#voltageTransformer:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_SF.CX_SF_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3277.000000 3.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28371" ObjectName="CB-CX_SF.CX_SF_Cb1"/>
    <cge:TPSR_Ref TObjectID="28371"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5078.000000 -702.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5078.000000 -702.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_GY.CX_WM_1TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3415.000000 -74.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3415.000000 -74.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-CX_GY.CX_WM_1TZyb"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f64d20">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -546.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f9b430">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.000000 -549.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fa00e0">
    <use class="BV-10KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 3933.000000 -522.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f80320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3348.000000 -432.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd50f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3384.000000 -426.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f5cde0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 -1014.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35d8160">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 3829.000000 -1103.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f88d60">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4462.000000 -1019.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16d12e0">
    <use class="BV-110KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 3934.000000 -657.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3523230">
    <use class="BV-35KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 5017.000000 -1038.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3521410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 -985.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3589310">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3421.000000 -234.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3589b90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3420.000000 -186.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35d4f80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3292.000000 -192.888889)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e5370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4060.000000 -169.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35384a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 4093.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37c29d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 4352.000000 -1104.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256c480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4249.000000 -613.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35733d0">
    <use class="BV-35KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 5018.000000 -894.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35351e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -706.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c3180">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4656.000000 -1017.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35bc850">
    <use class="BV-35KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 5017.000000 -461.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35c3540">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -370.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b5260">
    <use class="BV-35KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 5018.000000 -317.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c1580">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 -402.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20d0bf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -168.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20d2220">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 3966.000000 -52.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20dfa70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 -170.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e1310">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 3837.000000 -54.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_355a090">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -169.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_355b930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 3709.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2543620">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3548.000000 -168.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2544ec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 3581.000000 -52.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_255ac40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 -485.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_255e360">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -473.000000)" xlink:href="#lightningRod:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb2980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -167.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb3d60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 4456.000000 -51.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3014.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -468.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26887"/>
     <cge:Term_Ref ObjectID="37870"/>
    <cge:TPSR_Ref TObjectID="26887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-166519" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -468.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166519" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26887"/>
     <cge:Term_Ref ObjectID="37870"/>
    <cge:TPSR_Ref TObjectID="26887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-166520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -468.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26887"/>
     <cge:Term_Ref ObjectID="37870"/>
    <cge:TPSR_Ref TObjectID="26887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165679" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3222.000000 -317.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165679" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26915"/>
     <cge:Term_Ref ObjectID="37926"/>
    <cge:TPSR_Ref TObjectID="26915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165680" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3222.000000 -317.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26915"/>
     <cge:Term_Ref ObjectID="37926"/>
    <cge:TPSR_Ref TObjectID="26915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165682" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.000000 -34.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26920"/>
     <cge:Term_Ref ObjectID="37936"/>
    <cge:TPSR_Ref TObjectID="26920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165683" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.000000 -34.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26920"/>
     <cge:Term_Ref ObjectID="37936"/>
    <cge:TPSR_Ref TObjectID="26920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165684" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.000000 -34.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26920"/>
     <cge:Term_Ref ObjectID="37936"/>
    <cge:TPSR_Ref TObjectID="26920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3671.000000 -35.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26924"/>
     <cge:Term_Ref ObjectID="37944"/>
    <cge:TPSR_Ref TObjectID="26924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165687" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3671.000000 -35.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26924"/>
     <cge:Term_Ref ObjectID="37944"/>
    <cge:TPSR_Ref TObjectID="26924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165688" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3671.000000 -35.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165688" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26924"/>
     <cge:Term_Ref ObjectID="37944"/>
    <cge:TPSR_Ref TObjectID="26924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165690" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26928"/>
     <cge:Term_Ref ObjectID="37952"/>
    <cge:TPSR_Ref TObjectID="26928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165691" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26928"/>
     <cge:Term_Ref ObjectID="37952"/>
    <cge:TPSR_Ref TObjectID="26928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165692" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26928"/>
     <cge:Term_Ref ObjectID="37952"/>
    <cge:TPSR_Ref TObjectID="26928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165694" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3928.000000 -34.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165694" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26932"/>
     <cge:Term_Ref ObjectID="37960"/>
    <cge:TPSR_Ref TObjectID="26932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165695" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3928.000000 -34.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26932"/>
     <cge:Term_Ref ObjectID="37960"/>
    <cge:TPSR_Ref TObjectID="26932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165696" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3928.000000 -34.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26932"/>
     <cge:Term_Ref ObjectID="37960"/>
    <cge:TPSR_Ref TObjectID="26932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165698" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.000000 -35.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165698" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26936"/>
     <cge:Term_Ref ObjectID="37968"/>
    <cge:TPSR_Ref TObjectID="26936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165699" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.000000 -35.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26936"/>
     <cge:Term_Ref ObjectID="37968"/>
    <cge:TPSR_Ref TObjectID="26936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.000000 -35.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26936"/>
     <cge:Term_Ref ObjectID="37968"/>
    <cge:TPSR_Ref TObjectID="26936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165648" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -807.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26874"/>
     <cge:Term_Ref ObjectID="37844"/>
    <cge:TPSR_Ref TObjectID="26874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165649" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -807.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165649" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26874"/>
     <cge:Term_Ref ObjectID="37844"/>
    <cge:TPSR_Ref TObjectID="26874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165650" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -807.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165650" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26874"/>
     <cge:Term_Ref ObjectID="37844"/>
    <cge:TPSR_Ref TObjectID="26874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165675" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5180.000000 -383.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165675" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26910"/>
     <cge:Term_Ref ObjectID="37916"/>
    <cge:TPSR_Ref TObjectID="26910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165676" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5180.000000 -383.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165676" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26910"/>
     <cge:Term_Ref ObjectID="37916"/>
    <cge:TPSR_Ref TObjectID="26910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165677" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5180.000000 -383.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26910"/>
     <cge:Term_Ref ObjectID="37916"/>
    <cge:TPSR_Ref TObjectID="26910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165671" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -527.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26905"/>
     <cge:Term_Ref ObjectID="37906"/>
    <cge:TPSR_Ref TObjectID="26905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165672" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -527.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165672" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26905"/>
     <cge:Term_Ref ObjectID="37906"/>
    <cge:TPSR_Ref TObjectID="26905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165673" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -527.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165673" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26905"/>
     <cge:Term_Ref ObjectID="37906"/>
    <cge:TPSR_Ref TObjectID="26905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165667" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5183.000000 -816.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165667" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26900"/>
     <cge:Term_Ref ObjectID="37896"/>
    <cge:TPSR_Ref TObjectID="26900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165668" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5183.000000 -816.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165668" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26900"/>
     <cge:Term_Ref ObjectID="37896"/>
    <cge:TPSR_Ref TObjectID="26900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165669" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5183.000000 -816.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26900"/>
     <cge:Term_Ref ObjectID="37896"/>
    <cge:TPSR_Ref TObjectID="26900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165663" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5180.000000 -960.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26895"/>
     <cge:Term_Ref ObjectID="37886"/>
    <cge:TPSR_Ref TObjectID="26895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165664" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5180.000000 -960.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26895"/>
     <cge:Term_Ref ObjectID="37886"/>
    <cge:TPSR_Ref TObjectID="26895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165665" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5180.000000 -960.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26895"/>
     <cge:Term_Ref ObjectID="37886"/>
    <cge:TPSR_Ref TObjectID="26895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165659" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -1104.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26890"/>
     <cge:Term_Ref ObjectID="37876"/>
    <cge:TPSR_Ref TObjectID="26890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165660" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -1104.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26890"/>
     <cge:Term_Ref ObjectID="37876"/>
    <cge:TPSR_Ref TObjectID="26890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165661" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -1104.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165661" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26890"/>
     <cge:Term_Ref ObjectID="37876"/>
    <cge:TPSR_Ref TObjectID="26890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -1263.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26868"/>
     <cge:Term_Ref ObjectID="37832"/>
    <cge:TPSR_Ref TObjectID="26868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -1263.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26868"/>
     <cge:Term_Ref ObjectID="37832"/>
    <cge:TPSR_Ref TObjectID="26868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -1263.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26868"/>
     <cge:Term_Ref ObjectID="37832"/>
    <cge:TPSR_Ref TObjectID="26868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-165610" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -1263.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26862"/>
     <cge:Term_Ref ObjectID="37820"/>
    <cge:TPSR_Ref TObjectID="26862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-165611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -1263.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26862"/>
     <cge:Term_Ref ObjectID="37820"/>
    <cge:TPSR_Ref TObjectID="26862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-165612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -1263.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26862"/>
     <cge:Term_Ref ObjectID="37820"/>
    <cge:TPSR_Ref TObjectID="26862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-165624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -888.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26858"/>
     <cge:Term_Ref ObjectID="37815"/>
    <cge:TPSR_Ref TObjectID="26858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-165625" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -888.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26858"/>
     <cge:Term_Ref ObjectID="37815"/>
    <cge:TPSR_Ref TObjectID="26858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-165626" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -888.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165626" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26858"/>
     <cge:Term_Ref ObjectID="37815"/>
    <cge:TPSR_Ref TObjectID="26858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-165630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -888.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26858"/>
     <cge:Term_Ref ObjectID="37815"/>
    <cge:TPSR_Ref TObjectID="26858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-165627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -888.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26858"/>
     <cge:Term_Ref ObjectID="37815"/>
    <cge:TPSR_Ref TObjectID="26858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-165631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -888.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26858"/>
     <cge:Term_Ref ObjectID="37815"/>
    <cge:TPSR_Ref TObjectID="26858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-166525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -888.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26962"/>
     <cge:Term_Ref ObjectID="38025"/>
    <cge:TPSR_Ref TObjectID="26962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-166497" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -888.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26962"/>
     <cge:Term_Ref ObjectID="38025"/>
    <cge:TPSR_Ref TObjectID="26962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-166498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -888.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26962"/>
     <cge:Term_Ref ObjectID="38025"/>
    <cge:TPSR_Ref TObjectID="26962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-166502" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -888.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166502" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26962"/>
     <cge:Term_Ref ObjectID="38025"/>
    <cge:TPSR_Ref TObjectID="26962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-166499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -888.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26962"/>
     <cge:Term_Ref ObjectID="38025"/>
    <cge:TPSR_Ref TObjectID="26962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-166503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -888.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26962"/>
     <cge:Term_Ref ObjectID="38025"/>
    <cge:TPSR_Ref TObjectID="26962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-165640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -494.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26860"/>
     <cge:Term_Ref ObjectID="37817"/>
    <cge:TPSR_Ref TObjectID="26860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-165641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -494.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26860"/>
     <cge:Term_Ref ObjectID="37817"/>
    <cge:TPSR_Ref TObjectID="26860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-165642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -494.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26860"/>
     <cge:Term_Ref ObjectID="37817"/>
    <cge:TPSR_Ref TObjectID="26860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-165646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -494.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26860"/>
     <cge:Term_Ref ObjectID="37817"/>
    <cge:TPSR_Ref TObjectID="26860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-165643" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -494.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165643" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26860"/>
     <cge:Term_Ref ObjectID="37817"/>
    <cge:TPSR_Ref TObjectID="26860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-165647" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -494.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165647" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26860"/>
     <cge:Term_Ref ObjectID="37817"/>
    <cge:TPSR_Ref TObjectID="26860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-165632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -1253.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26859"/>
     <cge:Term_Ref ObjectID="37816"/>
    <cge:TPSR_Ref TObjectID="26859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-165633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -1253.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26859"/>
     <cge:Term_Ref ObjectID="37816"/>
    <cge:TPSR_Ref TObjectID="26859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-165634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -1253.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26859"/>
     <cge:Term_Ref ObjectID="37816"/>
    <cge:TPSR_Ref TObjectID="26859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-165638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -1253.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26859"/>
     <cge:Term_Ref ObjectID="37816"/>
    <cge:TPSR_Ref TObjectID="26859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-165635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -1253.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26859"/>
     <cge:Term_Ref ObjectID="37816"/>
    <cge:TPSR_Ref TObjectID="26859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-165639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -1253.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26859"/>
     <cge:Term_Ref ObjectID="37816"/>
    <cge:TPSR_Ref TObjectID="26859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-166504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -237.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26963"/>
     <cge:Term_Ref ObjectID="38026"/>
    <cge:TPSR_Ref TObjectID="26963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-166505" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -237.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166505" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26963"/>
     <cge:Term_Ref ObjectID="38026"/>
    <cge:TPSR_Ref TObjectID="26963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-166506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -237.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26963"/>
     <cge:Term_Ref ObjectID="38026"/>
    <cge:TPSR_Ref TObjectID="26963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-166510" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -237.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166510" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26963"/>
     <cge:Term_Ref ObjectID="38026"/>
    <cge:TPSR_Ref TObjectID="26963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-166507" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -237.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26963"/>
     <cge:Term_Ref ObjectID="38026"/>
    <cge:TPSR_Ref TObjectID="26963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-166511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -237.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26963"/>
     <cge:Term_Ref ObjectID="38026"/>
    <cge:TPSR_Ref TObjectID="26963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4492.000000 -560.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26881"/>
     <cge:Term_Ref ObjectID="37858"/>
    <cge:TPSR_Ref TObjectID="26881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4492.000000 -560.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26881"/>
     <cge:Term_Ref ObjectID="37858"/>
    <cge:TPSR_Ref TObjectID="26881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4492.000000 -560.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26881"/>
     <cge:Term_Ref ObjectID="37858"/>
    <cge:TPSR_Ref TObjectID="26881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-165654" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -639.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26961"/>
     <cge:Term_Ref ObjectID="38018"/>
    <cge:TPSR_Ref TObjectID="26961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-165655" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -639.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165655" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26961"/>
     <cge:Term_Ref ObjectID="38018"/>
    <cge:TPSR_Ref TObjectID="26961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194331" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194331" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29508"/>
     <cge:Term_Ref ObjectID="42042"/>
    <cge:TPSR_Ref TObjectID="29508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194332" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194332" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29508"/>
     <cge:Term_Ref ObjectID="42042"/>
    <cge:TPSR_Ref TObjectID="29508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-194333" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29508"/>
     <cge:Term_Ref ObjectID="42042"/>
    <cge:TPSR_Ref TObjectID="29508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-194335" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -221.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194335" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29510"/>
     <cge:Term_Ref ObjectID="42044"/>
    <cge:TPSR_Ref TObjectID="29510"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3026" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3026" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="2977" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="2977" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="2937" y="-761"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="2937" y="-761"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3818" y="-1001"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3818" y="-1001"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4341" y="-1002"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4341" y="-1002"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="3839" y="-535"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="3839" y="-535"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4836" y="-1097"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4836" y="-1097"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4837" y="-953"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4837" y="-953"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4837" y="-376"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4837" y="-376"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4836" y="-520"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4836" y="-520"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4839" y="-811"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4839" y="-811"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4647" y="-644"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4647" y="-644"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3311" y="-285"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3311" y="-285"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3567" y="-286"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3567" y="-286"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3695" y="-287"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3695" y="-287"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3823" y="-288"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3823" y="-288"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3952" y="-286"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3952" y="-286"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4079" y="-287"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4079" y="-287"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4442" y="-285"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4442" y="-285"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4239" y="-278"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4239" y="-278"/></g>
  </g><g id="MotifButton_Layer">
   <g href="ym_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3026" y="-1178"/></g>
   <g href="yr_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="2977" y="-1195"/></g>
   <g href="110kV哨房变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="2937" y="-761"/></g>
   <g href="110kV哨房变CX_SF_162间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3818" y="-1001"/></g>
   <g href="110kV哨房变CX_SF_163间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4341" y="-1002"/></g>
   <g href="110kV哨房变1＃主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="3839" y="-535"/></g>
   <g href="110kV哨房变CX_SF_361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4836" y="-1097"/></g>
   <g href="110kV哨房变CX_SF_362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4837" y="-953"/></g>
   <g href="110kV哨房变CX_SF_365间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4837" y="-376"/></g>
   <g href="110kV哨房变CX_SF_364间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4836" y="-520"/></g>
   <g href="110kV哨房变CX_SF_363间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4839" y="-811"/></g>
   <g href="110kV哨房变CX_SF_312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4647" y="-644"/></g>
   <g href="110kV哨房变CX_SF_062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3311" y="-285"/></g>
   <g href="110kV哨房变CX_SF_064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3567" y="-286"/></g>
   <g href="110kV哨房变CX_SF_065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3695" y="-287"/></g>
   <g href="110kV哨房变CX_SF_066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3823" y="-288"/></g>
   <g href="110kV哨房变CX_SF_067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3952" y="-286"/></g>
   <g href="110kV哨房变CX_SF_068间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4079" y="-287"/></g>
   <g href="110kV哨房变CX_SF_071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4442" y="-285"/></g>
   <g href="110kV哨房变CX_SF_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4239" y="-278"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4902,-1073 4892,-1068 4892,-1078 4902,-1073 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4913,-1073 4923,-1068 4923,-1078 4913,-1073 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4903,-929 4893,-924 4893,-934 4903,-929 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4914,-929 4924,-924 4924,-934 4914,-929 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4906,-785 4896,-780 4896,-790 4906,-785 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4917,-785 4927,-780 4927,-790 4917,-785 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4902,-496 4892,-491 4892,-501 4902,-496 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4913,-496 4923,-491 4923,-501 4913,-496 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4903,-352 4893,-347 4893,-357 4903,-352 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4914,-352 4924,-347 4924,-357 4914,-352 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SF.CX_SF_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38019"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -545.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="38021"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -545.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="38023"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -545.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26961" ObjectName="TF-CX_SF.CX_SF_1T"/>
    <cge:TPSR_Ref TObjectID="26961"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5153.000000 -1064.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -45.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5154.000000 -920.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5157.000000 -776.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5153.000000 -487.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5154.000000 -343.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -44.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -46.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3677.000000 -45.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3549.000000 -44.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -43.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_35a92d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-751 3905,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26876@x" ObjectIDND1="26874@x" ObjectIDZND0="26878@1" Pin0InfoVect0LinkObjId="SW-165861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165859_0" Pin1InfoVect1LinkObjId="SW-165857_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-751 3905,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35a9530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-616 3867,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="26880@x" ObjectIDND1="g_2f9b430@0" ObjectIDND2="26961@x" ObjectIDZND0="g_2f64d20@0" Pin0InfoVect0LinkObjId="g_2f64d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-165872_0" Pin1InfoVect1LinkObjId="g_2f9b430_0" Pin1InfoVect2LinkObjId="g_35a7860_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-616 3867,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f708c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-555 3839,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20a82a0@0" ObjectIDZND0="26880@1" Pin0InfoVect0LinkObjId="SW-165872_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a82a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-555 3839,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f70b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-922 3632,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26858@0" ObjectIDND1="26949@x" ObjectIDZND0="26951@0" Pin0InfoVect0LinkObjId="SW-166293_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-166291_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-922 3632,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fb8ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-922 3682,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26951@1" ObjectIDZND0="g_20a9530@0" Pin0InfoVect0LinkObjId="g_20a9530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-922 3682,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fb9120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-1004 3634,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="26949@x" ObjectIDND1="g_2f5cde0@0" ObjectIDND2="g_1611400@0" ObjectIDZND0="26950@0" Pin0InfoVect0LinkObjId="SW-166292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166291_0" Pin1InfoVect1LinkObjId="g_2f5cde0_0" Pin1InfoVect2LinkObjId="g_1611400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-1004 3634,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fc2220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-1004 3681,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26950@1" ObjectIDZND0="g_34e4d00@0" Pin0InfoVect0LinkObjId="g_34e4d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-1004 3681,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc2480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-529 3937,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="26961@x" ObjectIDND1="26889@x" ObjectIDZND0="g_2fa00e0@0" Pin0InfoVect0LinkObjId="g_2fa00e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35a7860_0" Pin1InfoVect1LinkObjId="SW-165893_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-529 3937,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd93b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-1073 4783,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26859@0" ObjectIDZND0="26891@0" Pin0InfoVect0LinkObjId="SW-166013_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37ae860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-1073 4783,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd9610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-1073 4819,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26890@1" ObjectIDZND0="26891@1" Pin0InfoVect0LinkObjId="SW-166013_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166012_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-1073 4819,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_189ae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-456 3919,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26887@1" ObjectIDZND0="26889@1" Pin0InfoVect0LinkObjId="SW-165893_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165891_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-456 3919,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_189b090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-411 3919,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26888@0" ObjectIDZND0="26887@0" Pin0InfoVect0LinkObjId="SW-165891_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165892_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-411 3919,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35b2160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-1019 3615,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2f5cde0@0" ObjectIDZND0="26950@x" ObjectIDZND1="26949@x" ObjectIDZND2="g_1611400@0" Pin0InfoVect0LinkObjId="SW-166292_0" Pin0InfoVect1LinkObjId="SW-166291_0" Pin0InfoVect2LinkObjId="g_1611400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f5cde0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-1019 3615,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35b23c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-984 3615,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="26949@0" ObjectIDZND0="26950@x" ObjectIDZND1="g_2f5cde0@0" ObjectIDZND2="g_1611400@0" Pin0InfoVect0LinkObjId="SW-166292_0" Pin0InfoVect1LinkObjId="g_2f5cde0_0" Pin0InfoVect2LinkObjId="g_1611400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-984 3615,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35a7600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-675 3906,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="26961@x" ObjectIDND1="g_16d12e0@0" ObjectIDND2="26876@x" ObjectIDZND0="26879@1" Pin0InfoVect0LinkObjId="SW-165862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35a7860_0" Pin1InfoVect1LinkObjId="g_16d12e0_0" Pin1InfoVect2LinkObjId="SW-165859_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-675 3906,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35a7860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-694 3920,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26876@1" ObjectIDZND0="26961@x" ObjectIDZND1="g_16d12e0@0" ObjectIDZND2="26879@x" Pin0InfoVect0LinkObjId="g_359e960_0" Pin0InfoVect1LinkObjId="g_16d12e0_0" Pin0InfoVect2LinkObjId="SW-165862_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165859_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-694 3920,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f82480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-821 3906,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26874@x" ObjectIDND1="26875@x" ObjectIDZND0="26877@1" Pin0InfoVect0LinkObjId="SW-165860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165857_0" Pin1InfoVect1LinkObjId="SW-165858_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-821 3906,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f826b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-895 3920,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26858@0" ObjectIDZND0="26875@0" Pin0InfoVect0LinkObjId="SW-165858_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-895 3920,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a6b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-475 3393,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_255f970@0" ObjectIDZND0="g_2fd50f0@0" Pin0InfoVect0LinkObjId="g_2fd50f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_255f970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-475 3393,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a53170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3354,-436 3354,-418 3393,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f80320@0" ObjectIDZND0="g_2fd50f0@0" ObjectIDZND1="26957@x" ObjectIDZND2="26958@x" Pin0InfoVect0LinkObjId="g_2fd50f0_0" Pin0InfoVect1LinkObjId="SW-166344_0" Pin0InfoVect2LinkObjId="SW-166345_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f80320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3354,-436 3354,-418 3393,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a533d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-431 3393,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2fd50f0@1" ObjectIDZND0="g_2f80320@0" ObjectIDZND1="26957@x" ObjectIDZND2="26958@x" Pin0InfoVect0LinkObjId="g_2f80320_0" Pin0InfoVect1LinkObjId="SW-166344_0" Pin0InfoVect2LinkObjId="SW-166345_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fd50f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-431 3393,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35af200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-405 3393,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26957@0" ObjectIDZND0="g_2f80320@0" ObjectIDZND1="g_2fd50f0@0" ObjectIDZND2="26958@x" Pin0InfoVect0LinkObjId="g_2f80320_0" Pin0InfoVect1LinkObjId="g_2fd50f0_0" Pin0InfoVect2LinkObjId="SW-166345_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166344_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-405 3393,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_359e960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-616 3922,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="g_2f64d20@0" ObjectIDND1="26880@x" ObjectIDND2="g_2f9b430@0" ObjectIDZND0="26961@x" Pin0InfoVect0LinkObjId="g_35a7860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f64d20_0" Pin1InfoVect1LinkObjId="SW-165872_0" Pin1InfoVect2LinkObjId="g_2f9b430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-616 3922,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_359eba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-605 3839,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="26880@0" ObjectIDZND0="g_2f64d20@0" ObjectIDZND1="26961@x" ObjectIDZND2="g_2f9b430@0" Pin0InfoVect0LinkObjId="g_2f64d20_0" Pin0InfoVect1LinkObjId="g_35a7860_0" Pin0InfoVect2LinkObjId="g_2f9b430_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-605 3839,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f7f120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3785,-603 3785,-616 3839,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="g_2f9b430@0" ObjectIDZND0="26880@x" ObjectIDZND1="g_2f64d20@0" ObjectIDZND2="26961@x" Pin0InfoVect0LinkObjId="SW-165872_0" Pin0InfoVect1LinkObjId="g_2f64d20_0" Pin0InfoVect2LinkObjId="g_35a7860_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f9b430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3785,-603 3785,-616 3839,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f7f380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-616 3839,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2f64d20@0" ObjectIDND1="26961@x" ObjectIDZND0="26880@x" ObjectIDZND1="g_2f9b430@0" Pin0InfoVect0LinkObjId="SW-165872_0" Pin0InfoVect1LinkObjId="g_2f9b430_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f64d20_0" Pin1InfoVect1LinkObjId="g_35a7860_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-616 3839,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a32b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-512 3919,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="26889@0" ObjectIDZND0="26961@x" ObjectIDZND1="g_2fa00e0@0" Pin0InfoVect0LinkObjId="g_35a7860_0" Pin0InfoVect1LinkObjId="g_2fa00e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165893_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-512 3919,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a3510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-550 3919,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26961@2" ObjectIDZND0="26889@x" ObjectIDZND1="g_2fa00e0@0" Pin0InfoVect0LinkObjId="SW-165893_0" Pin0InfoVect1LinkObjId="g_2fa00e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a7860_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-550 3919,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_359bc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-751 3920,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26874@x" ObjectIDND1="26878@x" ObjectIDZND0="26876@0" Pin0InfoVect0LinkObjId="SW-165859_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165857_0" Pin1InfoVect1LinkObjId="SW-165861_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-751 3920,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_359bec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-773 3920,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26874@0" ObjectIDZND0="26876@x" ObjectIDZND1="26878@x" Pin0InfoVect0LinkObjId="SW-165859_0" Pin0InfoVect1LinkObjId="SW-165861_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165857_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-773 3920,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_359a060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-821 3920,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26875@x" ObjectIDND1="26877@x" ObjectIDZND0="26874@1" Pin0InfoVect0LinkObjId="SW-165857_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165858_0" Pin1InfoVect1LinkObjId="SW-165860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-821 3920,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_359a2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-841 3920,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26875@1" ObjectIDZND0="26874@x" ObjectIDZND1="26877@x" Pin0InfoVect0LinkObjId="SW-165857_0" Pin0InfoVect1LinkObjId="SW-165860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165858_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-841 3920,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fc0fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-895 3615,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26858@0" ObjectIDZND0="26951@x" ObjectIDZND1="26949@x" Pin0InfoVect0LinkObjId="SW-166293_0" Pin0InfoVect1LinkObjId="SW-166291_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-895 3615,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f5cb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-948 3615,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="26949@1" ObjectIDZND0="26951@x" ObjectIDZND1="26858@0" Pin0InfoVect0LinkObjId="SW-166293_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-948 3615,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ac350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-895 3809,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26858@0" ObjectIDZND0="26869@0" Pin0InfoVect0LinkObjId="SW-165791_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-895 3809,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f95860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-949 3809,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26869@1" ObjectIDZND0="26868@x" ObjectIDZND1="26871@x" Pin0InfoVect0LinkObjId="SW-165790_0" Pin0InfoVect1LinkObjId="SW-165793_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165791_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-949 3809,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_205e420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-966 3809,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26869@x" ObjectIDND1="26871@x" ObjectIDZND0="26868@0" Pin0InfoVect0LinkObjId="SW-165790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165791_0" Pin1InfoVect1LinkObjId="SW-165793_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-966 3809,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_31594a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1073 3809,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="26870@1" ObjectIDZND0="26873@x" ObjectIDZND1="g_2f7a2d0@0" ObjectIDZND2="g_35d8160@0" Pin0InfoVect0LinkObjId="SW-165795_0" Pin0InfoVect1LinkObjId="g_2f7a2d0_0" Pin0InfoVect2LinkObjId="g_35d8160_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1073 3809,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2060ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1007 3809,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26868@1" ObjectIDZND0="26870@x" ObjectIDZND1="26872@x" Pin0InfoVect0LinkObjId="SW-165792_0" Pin0InfoVect1LinkObjId="SW-165794_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1007 3809,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2061120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1023 3809,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26868@x" ObjectIDND1="26872@x" ObjectIDZND0="26870@0" Pin0InfoVect0LinkObjId="SW-165792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165790_0" Pin1InfoVect1LinkObjId="SW-165794_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1023 3809,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f8cad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1091 3791,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26870@x" ObjectIDND1="g_2f7a2d0@0" ObjectIDND2="g_35d8160@0" ObjectIDZND0="26873@1" Pin0InfoVect0LinkObjId="SW-165795_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-165792_0" Pin1InfoVect1LinkObjId="g_2f7a2d0_0" Pin1InfoVect2LinkObjId="g_35d8160_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1091 3791,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f8cd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-1091 3745,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26873@0" ObjectIDZND0="g_2f88000@0" Pin0InfoVect0LinkObjId="g_2f88000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165795_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-1091 3745,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f7aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1023 3789,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26868@x" ObjectIDND1="26870@x" ObjectIDZND0="26872@1" Pin0InfoVect0LinkObjId="SW-165794_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165790_0" Pin1InfoVect1LinkObjId="SW-165792_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1023 3789,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f7ac90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-1023 3745,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26872@0" ObjectIDZND0="g_2f62be0@0" Pin0InfoVect0LinkObjId="g_2f62be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-1023 3745,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f7aef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-966 3790,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26869@x" ObjectIDND1="26868@x" ObjectIDZND0="26871@1" Pin0InfoVect0LinkObjId="SW-165793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165791_0" Pin1InfoVect1LinkObjId="SW-165790_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-966 3790,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f7a070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-966 3745,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26871@0" ObjectIDZND0="g_1a64200@0" Pin0InfoVect0LinkObjId="g_1a64200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165793_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-966 3745,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a56300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-1158 3809,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2f7a2d0@0" ObjectIDZND0="26870@x" ObjectIDZND1="26873@x" ObjectIDZND2="g_35d8160@0" Pin0InfoVect0LinkObjId="SW-165792_0" Pin0InfoVect1LinkObjId="SW-165795_0" Pin0InfoVect2LinkObjId="g_35d8160_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f7a2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-1158 3809,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f7d150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1091 3809,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="26870@x" ObjectIDND1="26873@x" ObjectIDZND0="g_2f7a2d0@0" ObjectIDZND1="g_35d8160@0" Pin0InfoVect0LinkObjId="g_2f7a2d0_0" Pin0InfoVect1LinkObjId="g_35d8160_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165792_0" Pin1InfoVect1LinkObjId="SW-165795_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1091 3809,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35b5df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-926 4485,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26962@0" ObjectIDND1="26952@x" ObjectIDZND0="26954@0" Pin0InfoVect0LinkObjId="SW-166300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-166298_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-926 4485,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35b6050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-926 4535,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26954@1" ObjectIDZND0="g_3586e30@0" Pin0InfoVect0LinkObjId="g_3586e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4522,-926 4535,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_356b6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4521,-1008 4534,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26953@1" ObjectIDZND0="g_2f85620@0" Pin0InfoVect0LinkObjId="g_2f85620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4521,-1008 4534,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_356b950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-991 4468,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="26952@0" ObjectIDZND0="g_2f88d60@0" ObjectIDZND1="26953@x" ObjectIDZND2="g_2f9e7b0@0" Pin0InfoVect0LinkObjId="g_2f88d60_0" Pin0InfoVect1LinkObjId="SW-166299_0" Pin0InfoVect2LinkObjId="g_2f9e7b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-991 4468,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_356bbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-895 4468,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26962@0" ObjectIDZND0="26954@x" ObjectIDZND1="26952@x" Pin0InfoVect0LinkObjId="SW-166300_0" Pin0InfoVect1LinkObjId="SW-166298_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-895 4468,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f88b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-955 4468,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="26952@1" ObjectIDZND0="26954@x" ObjectIDZND1="26962@0" Pin0InfoVect0LinkObjId="SW-166300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166298_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-955 4468,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d5650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-664 3938,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="26961@x" ObjectIDND1="26876@x" ObjectIDND2="26879@x" ObjectIDZND0="g_16d12e0@0" Pin0InfoVect0LinkObjId="g_16d12e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35a7860_0" Pin1InfoVect1LinkObjId="SW-165859_0" Pin1InfoVect2LinkObjId="SW-165862_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-664 3938,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d58b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-675 3920,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="26876@x" ObjectIDND1="26879@x" ObjectIDZND0="26961@x" ObjectIDZND1="g_16d12e0@0" Pin0InfoVect0LinkObjId="g_35a7860_0" Pin0InfoVect1LinkObjId="g_16d12e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165859_0" Pin1InfoVect1LinkObjId="SW-165862_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-675 3920,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d5b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-664 3920,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="26876@x" ObjectIDND1="26879@x" ObjectIDND2="g_16d12e0@0" ObjectIDZND0="26961@1" Pin0InfoVect0LinkObjId="g_35a7860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-165859_0" Pin1InfoVect1LinkObjId="SW-165862_0" Pin1InfoVect2LinkObjId="g_16d12e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-664 3920,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1898b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1158 3809,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2f7a2d0@0" ObjectIDND1="26870@x" ObjectIDND2="26873@x" ObjectIDZND0="g_35d8160@0" Pin0InfoVect0LinkObjId="g_35d8160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f7a2d0_0" Pin1InfoVect1LinkObjId="SW-165792_0" Pin1InfoVect2LinkObjId="SW-165795_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1158 3809,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1898df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1169 3836,-1169 3836,-1160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2f7a2d0@0" ObjectIDND1="26870@x" ObjectIDND2="26873@x" ObjectIDZND0="g_35d8160@0" Pin0InfoVect0LinkObjId="g_35d8160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f7a2d0_0" Pin1InfoVect1LinkObjId="SW-165792_0" Pin1InfoVect2LinkObjId="SW-165795_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1169 3836,-1169 3836,-1160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f76c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-895 4332,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26962@0" ObjectIDZND0="26863@0" Pin0InfoVect0LinkObjId="SW-165726_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-895 4332,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fc0580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-950 4332,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26863@1" ObjectIDZND0="26862@x" ObjectIDZND1="26865@x" Pin0InfoVect0LinkObjId="SW-165725_0" Pin0InfoVect1LinkObjId="SW-165728_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-950 4332,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fc07e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-967 4332,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26863@x" ObjectIDND1="26865@x" ObjectIDZND0="26862@0" Pin0InfoVect0LinkObjId="SW-165725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165726_0" Pin1InfoVect1LinkObjId="SW-165728_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-967 4332,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fc0a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1074 4332,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="26864@1" ObjectIDZND0="26867@x" ObjectIDZND1="g_2f5ec60@0" ObjectIDZND2="g_37c29d0@0" Pin0InfoVect0LinkObjId="SW-165730_0" Pin0InfoVect1LinkObjId="g_2f5ec60_0" Pin0InfoVect2LinkObjId="g_37c29d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1074 4332,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f740f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1008 4332,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26862@1" ObjectIDZND0="26864@x" ObjectIDZND1="26866@x" Pin0InfoVect0LinkObjId="SW-165727_0" Pin0InfoVect1LinkObjId="SW-165729_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1008 4332,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f74350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1024 4332,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26862@x" ObjectIDND1="26866@x" ObjectIDZND0="26864@0" Pin0InfoVect0LinkObjId="SW-165727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165725_0" Pin1InfoVect1LinkObjId="SW-165729_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1024 4332,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f6a030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1092 4314,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26864@x" ObjectIDND1="g_2f5ec60@0" ObjectIDND2="g_37c29d0@0" ObjectIDZND0="26867@1" Pin0InfoVect0LinkObjId="SW-165730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-165727_0" Pin1InfoVect1LinkObjId="g_2f5ec60_0" Pin1InfoVect2LinkObjId="g_37c29d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1092 4314,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f6a290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-1092 4268,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26867@0" ObjectIDZND0="g_2f4fd80@0" Pin0InfoVect0LinkObjId="g_2f4fd80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-1092 4268,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f6a4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1024 4312,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26864@x" ObjectIDND1="26862@x" ObjectIDZND0="26866@1" Pin0InfoVect0LinkObjId="SW-165729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165727_0" Pin1InfoVect1LinkObjId="SW-165725_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1024 4312,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f6a750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4279,-1024 4268,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26866@0" ObjectIDZND0="g_2f507b0@0" Pin0InfoVect0LinkObjId="g_2f507b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4279,-1024 4268,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f6a9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-967 4313,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26862@x" ObjectIDND1="26863@x" ObjectIDZND0="26865@1" Pin0InfoVect0LinkObjId="SW-165728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165725_0" Pin1InfoVect1LinkObjId="SW-165726_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-967 4313,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f6ac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4279,-967 4268,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26865@0" ObjectIDZND0="g_2f686e0@0" Pin0InfoVect0LinkObjId="g_2f686e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4279,-967 4268,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30dd420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4313,-1159 4332,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2f5ec60@0" ObjectIDZND0="26867@x" ObjectIDZND1="26864@x" ObjectIDZND2="g_37c29d0@0" Pin0InfoVect0LinkObjId="SW-165730_0" Pin0InfoVect1LinkObjId="SW-165727_0" Pin0InfoVect2LinkObjId="g_37c29d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f5ec60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4313,-1159 4332,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30dd680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1092 4332,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="26867@x" ObjectIDND1="26864@x" ObjectIDZND0="g_2f5ec60@0" ObjectIDZND1="g_37c29d0@0" Pin0InfoVect0LinkObjId="g_2f5ec60_0" Pin0InfoVect1LinkObjId="g_37c29d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165730_0" Pin1InfoVect1LinkObjId="SW-165727_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1092 4332,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30dd8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1170 4332,-1253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" ObjectIDND0="26867@x" ObjectIDND1="26864@x" ObjectIDND2="g_2f5ec60@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-165730_0" Pin1InfoVect1LinkObjId="SW-165727_0" Pin1InfoVect2LinkObjId="g_2f5ec60_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1170 4332,-1253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30ddb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1159 4332,-1170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="26867@x" ObjectIDND1="26864@x" ObjectIDND2="g_2f5ec60@0" ObjectIDZND0="g_37c29d0@0" Pin0InfoVect0LinkObjId="g_37c29d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-165730_0" Pin1InfoVect1LinkObjId="SW-165727_0" Pin1InfoVect2LinkObjId="g_2f5ec60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1159 4332,-1170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30ddda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1170 4359,-1170 4359,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="26867@x" ObjectIDND1="26864@x" ObjectIDND2="g_2f5ec60@0" ObjectIDZND0="g_37c29d0@0" Pin0InfoVect0LinkObjId="g_37c29d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-165730_0" Pin1InfoVect1LinkObjId="SW-165727_0" Pin1InfoVect2LinkObjId="g_2f5ec60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1170 4359,-1170 4359,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a64b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-593 4253,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="26883@x" ObjectIDND1="26885@x" ObjectIDND2="26961@x" ObjectIDZND0="g_256c480@0" Pin0InfoVect0LinkObjId="g_256c480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-165881_0" Pin1InfoVect1LinkObjId="SW-165883_0" Pin1InfoVect2LinkObjId="g_35a7860_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-593 4253,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b0980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5021,-1045 4999,-1045 4999,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_3523230@0" ObjectIDZND0="26892@x" ObjectIDZND1="26894@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-166014_0" Pin0InfoVect1LinkObjId="SW-166016_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3523230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5021,-1045 4999,-1045 4999,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35b1660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-717 5083,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-717 5083,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35b18c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-821 3858,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26877@0" ObjectIDZND0="g_3581d00@0" Pin0InfoVect0LinkObjId="g_3581d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-821 3858,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35b1b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3869,-751 3858,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26878@0" ObjectIDZND0="g_3582730@0" Pin0InfoVect0LinkObjId="g_3582730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3869,-751 3858,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3581aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-675 3858,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26879@0" ObjectIDZND0="g_353b7b0@0" Pin0InfoVect0LinkObjId="g_353b7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-675 3858,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353c210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-1026 4754,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26859@0" ObjectIDZND0="26955@1" Pin0InfoVect0LinkObjId="SW-166331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37ae860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-1026 4754,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3524d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-993 4707,-993 4707,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3521410@0" ObjectIDZND0="g_25c3180@0" ObjectIDZND1="26955@x" Pin0InfoVect0LinkObjId="g_25c3180_0" Pin0InfoVect1LinkObjId="SW-166331_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3521410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-993 4707,-993 4707,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3525810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-1026 4707,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_25c3180@1" ObjectIDZND0="g_3521410@0" ObjectIDZND1="26955@x" Pin0InfoVect0LinkObjId="g_3521410_0" Pin0InfoVect1LinkObjId="SW-166331_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c3180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-1026 4707,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3525a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4707,-1026 4718,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3521410@0" ObjectIDND1="g_25c3180@0" ObjectIDZND0="26955@0" Pin0InfoVect0LinkObjId="SW-166331_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3521410_0" Pin1InfoVect1LinkObjId="g_25c3180_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4707,-1026 4718,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35890b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3430,-357 3430,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26860@0" ObjectIDZND0="26940@1" Pin0InfoVect0LinkObjId="SW-166240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353f310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3430,-357 3430,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30d30c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3430,-239 3430,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3589310@1" ObjectIDZND0="g_3589b90@1" Pin0InfoVect0LinkObjId="g_3589b90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3589310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3430,-239 3430,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30d3320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3430,-191 3430,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3589b90@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3589b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3430,-191 3430,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f9e1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-1023 4468,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_2f88d60@0" ObjectIDZND0="26953@x" ObjectIDZND1="g_2f9e7b0@0" ObjectIDZND2="26952@x" Pin0InfoVect0LinkObjId="SW-166299_0" Pin0InfoVect1LinkObjId="g_2f9e7b0_0" Pin0InfoVect2LinkObjId="SW-166298_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f88d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-1023 4468,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f9e390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-1008 4487,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2f88d60@0" ObjectIDND1="g_2f9e7b0@0" ObjectIDND2="26952@x" ObjectIDZND0="26953@0" Pin0InfoVect0LinkObjId="SW-166299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f88d60_0" Pin1InfoVect1LinkObjId="g_2f9e7b0_0" Pin1InfoVect2LinkObjId="SW-166298_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-1008 4487,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f9e580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-1008 4455,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2f88d60@0" ObjectIDND1="26953@x" ObjectIDND2="26952@x" ObjectIDZND0="g_2f9e7b0@0" Pin0InfoVect0LinkObjId="g_2f9e7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f88d60_0" Pin1InfoVect1LinkObjId="SW-166299_0" Pin1InfoVect2LinkObjId="SW-166298_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-1008 4455,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26de3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-1004 3615,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1611400@0" ObjectIDZND0="26950@x" ObjectIDZND1="26949@x" ObjectIDZND2="g_2f5cde0@0" Pin0InfoVect0LinkObjId="SW-166292_0" Pin0InfoVect1LinkObjId="SW-166291_0" Pin0InfoVect2LinkObjId="g_2f5cde0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1611400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-1004 3615,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35d5a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3302,-357 3302,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26860@0" ObjectIDZND0="26916@1" Pin0InfoVect0LinkObjId="SW-166118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353f310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3302,-357 3302,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35d5c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3302,-307 3302,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26916@0" ObjectIDZND0="26915@1" Pin0InfoVect0LinkObjId="SW-166117_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166118_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3302,-307 3302,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3598c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3302,-197 3302,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_35d4f80@0" ObjectIDZND0="26917@1" Pin0InfoVect0LinkObjId="SW-166119_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35d4f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3302,-197 3302,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3598ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-250 3257,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_35d5ec0@0" ObjectIDZND0="26919@0" Pin0InfoVect0LinkObjId="SW-166121_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35d5ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-250 3257,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3537680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4070,-357 4070,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26860@0" ObjectIDZND0="26937@1" Pin0InfoVect0LinkObjId="SW-166221_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353f310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4070,-357 4070,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35378e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4070,-309 4070,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26937@0" ObjectIDZND0="26936@1" Pin0InfoVect0LinkObjId="SW-166220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166221_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4070,-309 4070,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3537b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4070,-173 4070,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20e5370@0" ObjectIDZND0="26938@1" Pin0InfoVect0LinkObjId="SW-166222_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e5370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4070,-173 4070,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353f310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3391,-369 3391,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26957@1" ObjectIDZND0="26860@0" Pin0InfoVect0LinkObjId="g_351a450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3391,-369 3391,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3518ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1243 3809,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_2f7a2d0@0" ObjectIDZND1="26870@x" ObjectIDZND2="26873@x" Pin0InfoVect0LinkObjId="g_2f7a2d0_0" Pin0InfoVect1LinkObjId="SW-165792_0" Pin0InfoVect2LinkObjId="SW-165795_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1243 3809,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_351a450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4182,-343 4182,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26941@1" ObjectIDZND0="26860@0" Pin0InfoVect0LinkObjId="g_353f310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4182,-343 4182,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37c7970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-895 3943,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26858@0" ObjectIDZND0="26942@0" Pin0InfoVect0LinkObjId="SW-166242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-895 3943,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37ca3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-895 4152,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26962@0" ObjectIDZND0="26943@0" Pin0InfoVect0LinkObjId="SW-166243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-895 4152,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37cac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-986 4152,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26942@x" ObjectIDND1="26944@x" ObjectIDZND0="26943@x" ObjectIDZND1="26945@x" Pin0InfoVect0LinkObjId="SW-166243_0" Pin0InfoVect1LinkObjId="SW-166245_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166242_0" Pin1InfoVect1LinkObjId="SW-166244_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-986 4152,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3526cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-949 3943,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="26942@1" ObjectIDZND0="26943@x" ObjectIDZND1="26945@x" ObjectIDZND2="26944@x" Pin0InfoVect0LinkObjId="SW-166243_0" Pin0InfoVect1LinkObjId="SW-166245_0" Pin0InfoVect2LinkObjId="SW-166244_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-949 3943,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3527790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-949 4152,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="26943@1" ObjectIDZND0="26942@x" ObjectIDZND1="26944@x" ObjectIDZND2="26945@x" Pin0InfoVect0LinkObjId="SW-166242_0" Pin0InfoVect1LinkObjId="SW-166244_0" Pin0InfoVect2LinkObjId="SW-166245_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-949 4152,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_256a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-986 3943,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="26943@x" ObjectIDND1="26945@x" ObjectIDND2="26942@x" ObjectIDZND0="26944@0" Pin0InfoVect0LinkObjId="SW-166244_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166243_0" Pin1InfoVect1LinkObjId="SW-166245_0" Pin1InfoVect2LinkObjId="SW-166242_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-986 3943,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_256a840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-1040 3943,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26944@1" ObjectIDZND0="g_256af60@0" Pin0InfoVect0LinkObjId="g_256af60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166244_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-1040 3943,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_256aaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-986 4152,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="26942@x" ObjectIDND1="26944@x" ObjectIDND2="26943@x" ObjectIDZND0="26945@0" Pin0InfoVect0LinkObjId="SW-166245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166242_0" Pin1InfoVect1LinkObjId="SW-166244_0" Pin1InfoVect2LinkObjId="SW-166243_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-986 4152,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_256ad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-1040 4152,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26945@1" ObjectIDZND0="g_256b9f0@0" Pin0InfoVect0LinkObjId="g_256b9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-1040 4152,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_256da80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-592 4355,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="26883@0" ObjectIDZND0="26885@x" ObjectIDZND1="g_256c480@0" ObjectIDZND2="26961@x" Pin0InfoVect0LinkObjId="SW-165883_0" Pin0InfoVect1LinkObjId="g_256c480_0" Pin0InfoVect2LinkObjId="g_35a7860_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165881_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-592 4355,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f60310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4355,-592 4355,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="26883@x" ObjectIDND1="g_256c480@0" ObjectIDND2="26961@x" ObjectIDZND0="26885@0" Pin0InfoVect0LinkObjId="SW-165883_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-165881_0" Pin1InfoVect1LinkObjId="g_256c480_0" Pin1InfoVect2LinkObjId="g_35a7860_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4355,-592 4355,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f60570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4355,-655 4355,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26885@1" ObjectIDZND0="g_1f63490@0" Pin0InfoVect0LinkObjId="g_1f63490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165883_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4355,-655 4355,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f62fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4439,-592 4439,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26881@x" ObjectIDND1="26883@x" ObjectIDZND0="26884@0" Pin0InfoVect0LinkObjId="SW-165882_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165879_0" Pin1InfoVect1LinkObjId="SW-165881_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4439,-592 4439,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f63230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4439,-656 4439,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26884@1" ObjectIDZND0="g_1f63f20@0" Pin0InfoVect0LinkObjId="g_1f63f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165882_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4439,-656 4439,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f65580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-1073 4880,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26892@0" ObjectIDZND0="26890@x" ObjectIDZND1="26893@x" Pin0InfoVect0LinkObjId="SW-166012_0" Pin0InfoVect1LinkObjId="SW-166015_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-1073 4880,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f657e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-1073 4862,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26892@x" ObjectIDND1="26893@x" ObjectIDZND0="26890@0" Pin0InfoVect0LinkObjId="SW-166012_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166014_0" Pin1InfoVect1LinkObjId="SW-166015_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-1073 4862,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f662d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4973,-1073 4986,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="26892@1" ObjectIDZND0="g_3523230@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3523230_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166014_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4973,-1073 4986,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c7870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-1073 4999,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="26892@x" ObjectIDND1="26894@x" ObjectIDZND0="g_3523230@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3523230_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166014_0" Pin1InfoVect1LinkObjId="SW-166016_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-1073 4999,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c7ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5126,-1073 4999,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3523230@0" ObjectIDZND1="26892@x" ObjectIDZND2="26894@x" Pin0InfoVect0LinkObjId="g_3523230_0" Pin0InfoVect1LinkObjId="SW-166014_0" Pin0InfoVect2LinkObjId="SW-166016_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5126,-1073 4999,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ca4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-1073 4880,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26892@x" ObjectIDND1="26890@x" ObjectIDZND0="26893@0" Pin0InfoVect0LinkObjId="SW-166015_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166014_0" Pin1InfoVect1LinkObjId="SW-166012_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-1073 4880,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ca700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-1124 4880,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26893@1" ObjectIDZND0="g_35ce0b0@0" Pin0InfoVect0LinkObjId="g_35ce0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166015_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-1124 4880,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35cd160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-1073 4986,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="26892@x" ObjectIDND1="g_3523230@0" ObjectIDND2="0@x" ObjectIDZND0="26894@0" Pin0InfoVect0LinkObjId="SW-166016_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166014_0" Pin1InfoVect1LinkObjId="g_3523230_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-1073 4986,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35cd3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-1124 4986,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26894@1" ObjectIDZND0="g_35cd620@0" Pin0InfoVect0LinkObjId="g_35cd620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166016_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-1124 4986,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3572050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-929 4784,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26859@0" ObjectIDZND0="26896@0" Pin0InfoVect0LinkObjId="SW-166034_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37ae860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-929 4784,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3572240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4836,-929 4820,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26895@1" ObjectIDZND0="26896@1" Pin0InfoVect0LinkObjId="SW-166034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166033_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4836,-929 4820,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3577ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5034,-952 5000,-952 5000,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_35733d0@0" ObjectIDZND1="0@x" ObjectIDZND2="26897@x" Pin0InfoVect0LinkObjId="g_35733d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-166035_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5034,-952 5000,-952 5000,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3577f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-952 5106,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_35729f0@0" Pin0InfoVect0LinkObjId="g_35729f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-952 5106,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35781a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5022,-901 5000,-901 5000,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_35733d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="26897@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-166035_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35733d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5022,-901 5000,-901 5000,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3578740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-929 4881,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26897@0" ObjectIDZND0="26895@x" ObjectIDZND1="26898@x" Pin0InfoVect0LinkObjId="SW-166033_0" Pin0InfoVect1LinkObjId="SW-166036_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166035_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-929 4881,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35789a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-929 4863,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26897@x" ObjectIDND1="26898@x" ObjectIDZND0="26895@0" Pin0InfoVect0LinkObjId="SW-166033_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166035_0" Pin1InfoVect1LinkObjId="SW-166036_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-929 4863,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3578c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-929 4987,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="26897@1" ObjectIDZND0="g_35733d0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_35733d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166035_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-929 4987,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3578e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-929 5000,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="26897@x" ObjectIDND1="26899@x" ObjectIDZND0="g_35733d0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_35733d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166035_0" Pin1InfoVect1LinkObjId="SW-166037_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-929 5000,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35790c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-929 5000,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_35733d0@0" ObjectIDZND1="0@x" ObjectIDZND2="26897@x" Pin0InfoVect0LinkObjId="g_35733d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-166035_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-929 5000,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-929 4881,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26895@x" ObjectIDND1="26897@x" ObjectIDZND0="26898@0" Pin0InfoVect0LinkObjId="SW-166036_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166033_0" Pin1InfoVect1LinkObjId="SW-166035_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-929 4881,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357bd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-980 4881,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26898@1" ObjectIDZND0="g_357f730@0" Pin0InfoVect0LinkObjId="g_357f730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166036_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-980 4881,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357e7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-929 4987,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_35733d0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="26899@0" Pin0InfoVect0LinkObjId="SW-166037_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35733d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-929 4987,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-980 4987,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26899@1" ObjectIDZND0="g_357eca0@0" Pin0InfoVect0LinkObjId="g_357eca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166037_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-980 4987,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354b390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5037,-808 5003,-808 5003,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="26902@x" ObjectIDZND1="26904@x" ObjectIDZND2="g_35351e0@0" Pin0InfoVect0LinkObjId="SW-166056_0" Pin0InfoVect1LinkObjId="SW-166058_0" Pin0InfoVect2LinkObjId="g_35351e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5037,-808 5003,-808 5003,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_354b5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-808 5109,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3547940@0" Pin0InfoVect0LinkObjId="g_3547940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-808 5109,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354bb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4941,-785 4884,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26902@0" ObjectIDZND0="26903@x" ObjectIDZND1="26900@x" Pin0InfoVect0LinkObjId="SW-166057_0" Pin0InfoVect1LinkObjId="SW-166054_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166056_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4941,-785 4884,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-785 4866,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26902@x" ObjectIDND1="26903@x" ObjectIDZND0="26900@0" Pin0InfoVect0LinkObjId="SW-166054_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166056_0" Pin1InfoVect1LinkObjId="SW-166057_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-785 4866,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354c050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-785 4990,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="26902@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-785 4990,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354c2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-785 5003,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="26902@x" ObjectIDND1="26904@x" ObjectIDND2="g_35351e0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166056_0" Pin1InfoVect1LinkObjId="SW-166058_0" Pin1InfoVect2LinkObjId="g_35351e0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-785 5003,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354c510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-785 5003,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="26902@x" ObjectIDZND2="26904@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-166056_0" Pin0InfoVect2LinkObjId="SW-166058_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-785 5003,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352eaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-785 4884,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26902@x" ObjectIDND1="26900@x" ObjectIDZND0="26903@0" Pin0InfoVect0LinkObjId="SW-166057_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166056_0" Pin1InfoVect1LinkObjId="SW-166054_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-785 4884,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352ed50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-836 4884,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26903@1" ObjectIDZND0="g_3532700@0" Pin0InfoVect0LinkObjId="g_3532700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166057_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-836 4884,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35317b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-785 4990,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="26902@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="26904@0" Pin0InfoVect0LinkObjId="SW-166058_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166056_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-785 4990,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3531a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-836 4990,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26904@1" ObjectIDZND0="g_3531c70@0" Pin0InfoVect0LinkObjId="g_3531c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-836 4990,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3534f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-785 4990,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="26902@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_35351e0@0" Pin0InfoVect0LinkObjId="g_35351e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166056_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-785 4990,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3535f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5003,-787 5003,-717 5021,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="26902@x" ObjectIDND2="26904@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-166056_0" Pin1InfoVect2LinkObjId="SW-166058_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5003,-787 5003,-717 5021,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c3a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-1026 4661,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_25c3cb0@0" ObjectIDZND0="g_25c3180@0" Pin0InfoVect0LinkObjId="g_25c3180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c3cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-1026 4661,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25cd950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-496 4783,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26963@0" ObjectIDZND0="26906@0" Pin0InfoVect0LinkObjId="SW-166076_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-496 4783,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25cdb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-496 4819,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26905@1" ObjectIDZND0="26906@1" Pin0InfoVect0LinkObjId="SW-166076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-496 4819,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c0690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-519 4999,-519 4999,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_35bc850@0" ObjectIDZND1="26907@x" ObjectIDZND2="26909@x" Pin0InfoVect0LinkObjId="g_35bc850_0" Pin0InfoVect1LinkObjId="SW-166077_0" Pin0InfoVect2LinkObjId="SW-166079_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-519 4999,-519 4999,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35c08f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5085,-519 5105,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_35bbfa0@0" Pin0InfoVect0LinkObjId="g_35bbfa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5085,-519 5105,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c0b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5021,-468 4999,-468 4999,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_35bc850@0" ObjectIDZND0="0@x" ObjectIDZND1="26907@x" ObjectIDZND2="26909@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-166077_0" Pin0InfoVect2LinkObjId="SW-166079_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35bc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5021,-468 4999,-468 4999,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c0db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-411 4752,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26963@0" ObjectIDZND0="26956@1" Pin0InfoVect0LinkObjId="SW-166335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-411 4752,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c42f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4690,-378 4705,-378 4705,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_35c3540@0" ObjectIDZND0="g_25c1580@0" ObjectIDZND1="26956@x" Pin0InfoVect0LinkObjId="g_25c1580_0" Pin0InfoVect1LinkObjId="SW-166335_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35c3540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4690,-378 4705,-378 4705,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c4550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4690,-411 4705,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_25c1580@1" ObjectIDZND0="g_35c3540@0" ObjectIDZND1="26956@x" Pin0InfoVect0LinkObjId="g_35c3540_0" Pin0InfoVect1LinkObjId="SW-166335_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c1580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4690,-411 4705,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c47b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-411 4716,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_35c3540@0" ObjectIDND1="g_25c1580@0" ObjectIDZND0="26956@0" Pin0InfoVect0LinkObjId="SW-166335_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35c3540_0" Pin1InfoVect1LinkObjId="g_25c1580_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-411 4716,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c5fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-496 4880,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26907@0" ObjectIDZND0="26905@x" ObjectIDZND1="26908@x" Pin0InfoVect0LinkObjId="SW-166075_0" Pin0InfoVect1LinkObjId="SW-166078_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-496 4880,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c61a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-496 4862,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26907@x" ObjectIDND1="26908@x" ObjectIDZND0="26905@0" Pin0InfoVect0LinkObjId="SW-166075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166077_0" Pin1InfoVect1LinkObjId="SW-166078_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-496 4862,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c6390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4973,-496 4986,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="26907@1" ObjectIDZND0="0@x" ObjectIDZND1="g_35bc850@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_35bc850_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4973,-496 4986,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c65c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-496 4999,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="26907@x" ObjectIDND1="26909@x" ObjectIDZND0="0@x" ObjectIDZND1="g_35bc850@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_35bc850_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166077_0" Pin1InfoVect1LinkObjId="SW-166079_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-496 4999,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c67f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5126,-496 4999,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_35bc850@0" ObjectIDZND2="26907@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_35bc850_0" Pin0InfoVect2LinkObjId="SW-166077_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5126,-496 4999,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25978d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-496 4880,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26907@x" ObjectIDND1="26905@x" ObjectIDZND0="26908@0" Pin0InfoVect0LinkObjId="SW-166078_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166077_0" Pin1InfoVect1LinkObjId="SW-166075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-496 4880,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2597b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-547 4880,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26908@1" ObjectIDZND0="g_259b4e0@0" Pin0InfoVect0LinkObjId="g_259b4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166078_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-547 4880,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_259a590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-496 4986,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_35bc850@0" ObjectIDND2="0@x" ObjectIDZND0="26909@0" Pin0InfoVect0LinkObjId="SW-166079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_35bc850_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-496 4986,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_259a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-547 4986,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26909@1" ObjectIDZND0="g_259aa50@0" Pin0InfoVect0LinkObjId="g_259aa50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-547 4986,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b3ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-352 4784,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26963@0" ObjectIDZND0="26911@0" Pin0InfoVect0LinkObjId="SW-166097_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-352 4784,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b40d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4836,-352 4820,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26910@1" ObjectIDZND0="26911@1" Pin0InfoVect0LinkObjId="SW-166097_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166096_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4836,-352 4820,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b90a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5034,-375 5000,-375 5000,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_25b5260@0" ObjectIDZND1="26912@x" ObjectIDZND2="26914@x" Pin0InfoVect0LinkObjId="g_25b5260_0" Pin0InfoVect1LinkObjId="SW-166098_0" Pin0InfoVect2LinkObjId="SW-166100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5034,-375 5000,-375 5000,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25b9300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-375 5106,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_25b4880@0" Pin0InfoVect0LinkObjId="g_25b4880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-375 5106,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b9560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5022,-324 5000,-324 5000,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_25b5260@0" ObjectIDZND0="0@x" ObjectIDZND1="26912@x" ObjectIDZND2="26914@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-166098_0" Pin0InfoVect2LinkObjId="SW-166100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b5260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5022,-324 5000,-324 5000,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b9b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-352 4881,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26912@0" ObjectIDZND0="26910@x" ObjectIDZND1="26913@x" Pin0InfoVect0LinkObjId="SW-166096_0" Pin0InfoVect1LinkObjId="SW-166099_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166098_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-352 4881,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-352 4863,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26912@x" ObjectIDND1="26913@x" ObjectIDZND0="26910@0" Pin0InfoVect0LinkObjId="SW-166096_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166098_0" Pin1InfoVect1LinkObjId="SW-166099_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-352 4863,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b9fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-352 4987,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="26912@1" ObjectIDZND0="0@x" ObjectIDZND1="g_25b5260@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_25b5260_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166098_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-352 4987,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ba220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-352 5000,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="26912@x" ObjectIDND1="26914@x" ObjectIDZND0="0@x" ObjectIDZND1="g_25b5260@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_25b5260_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166098_0" Pin1InfoVect1LinkObjId="SW-166100_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-352 5000,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ba480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-352 5000,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_25b5260@0" ObjectIDZND2="26912@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_25b5260_0" Pin0InfoVect2LinkObjId="SW-166098_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-352 5000,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25bcee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-352 4881,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26912@x" ObjectIDND1="26910@x" ObjectIDZND0="26913@0" Pin0InfoVect0LinkObjId="SW-166099_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166098_0" Pin1InfoVect1LinkObjId="SW-166096_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-352 4881,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25bd140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-403 4881,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26913@1" ObjectIDZND0="g_25c0af0@0" Pin0InfoVect0LinkObjId="g_25c0af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166099_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-403 4881,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25bfba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-352 4987,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_25b5260@0" ObjectIDND2="0@x" ObjectIDZND0="26914@0" Pin0InfoVect0LinkObjId="SW-166100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_25b5260_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-352 4987,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25bfe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-403 4987,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26914@1" ObjectIDZND0="g_25c0060@0" Pin0InfoVect0LinkObjId="g_25c0060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-403 4987,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-411 4659,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_25a44a0@0" ObjectIDZND0="g_25c1580@0" Pin0InfoVect0LinkObjId="g_25c1580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a44a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-411 4659,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ab410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4493,-592 4527,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26881@0" ObjectIDZND0="26882@0" Pin0InfoVect0LinkObjId="SW-165880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165879_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4493,-592 4527,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ab670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4439,-592 4466,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26884@x" ObjectIDND1="26883@x" ObjectIDZND0="26881@1" Pin0InfoVect0LinkObjId="SW-165879_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165882_0" Pin1InfoVect1LinkObjId="SW-165881_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4439,-592 4466,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ab8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-592 4439,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26883@1" ObjectIDZND0="26884@x" ObjectIDZND1="26881@x" Pin0InfoVect0LinkObjId="SW-165882_0" Pin0InfoVect1LinkObjId="SW-165879_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165881_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-592 4439,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37ae600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4679,-650 4679,-736 4715,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26946@1" ObjectIDZND0="26947@0" Pin0InfoVect0LinkObjId="SW-166264_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4679,-650 4679,-736 4715,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37ae860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4751,-736 4768,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26947@1" ObjectIDZND0="26859@0" Pin0InfoVect0LinkObjId="g_2539650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4751,-736 4768,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37b0f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-543 4752,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26963@0" ObjectIDZND0="26948@1" Pin0InfoVect0LinkObjId="SW-166265_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-543 4752,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37b11b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-543 4679,-543 4679,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26948@0" ObjectIDZND0="26946@0" Pin0InfoVect0LinkObjId="SW-166262_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166265_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-543 4679,-543 4679,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37b3c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4070,-249 4099,-249 4099,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_20e5370@0" ObjectIDND1="26936@x" ObjectIDZND0="26939@1" Pin0InfoVect0LinkObjId="SW-166223_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20e5370_0" Pin1InfoVect1LinkObjId="SW-166220_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4070,-249 4099,-249 4099,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37b3e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-205 4099,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26939@0" ObjectIDZND0="g_37b40d0@0" Pin0InfoVect0LinkObjId="g_37b40d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166223_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-205 4099,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37b4b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4070,-110 4100,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="26938@x" ObjectIDND1="0@x" ObjectIDZND0="g_35384a0@0" Pin0InfoVect0LinkObjId="g_35384a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166222_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4070,-110 4100,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37b5610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4070,-121 4070,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26938@0" ObjectIDZND0="g_35384a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_35384a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166222_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4070,-121 4070,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37b5870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4070,-110 4070,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_35384a0@0" ObjectIDND1="26938@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35384a0_0" Pin1InfoVect1LinkObjId="SW-166222_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4070,-110 4070,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37b6360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4070,-249 4070,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="26939@x" ObjectIDND1="26936@x" ObjectIDZND0="g_20e5370@1" Pin0InfoVect0LinkObjId="g_20e5370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166223_0" Pin1InfoVect1LinkObjId="SW-166220_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4070,-249 4070,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37b65c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4070,-266 4070,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26936@0" ObjectIDZND0="26939@x" ObjectIDZND1="g_20e5370@0" Pin0InfoVect0LinkObjId="SW-166223_0" Pin0InfoVect1LinkObjId="g_20e5370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4070,-266 4070,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d1460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-357 3943,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26860@0" ObjectIDZND0="26933@1" Pin0InfoVect0LinkObjId="SW-166201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353f310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-357 3943,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d16c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-308 3943,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26933@0" ObjectIDZND0="26932@1" Pin0InfoVect0LinkObjId="SW-166200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166201_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-308 3943,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d1920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-172 3943,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20d0bf0@0" ObjectIDZND0="26934@1" Pin0InfoVect0LinkObjId="SW-166202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20d0bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-172 3943,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d5d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-248 3972,-248 3972,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_20d0bf0@0" ObjectIDND1="26932@x" ObjectIDZND0="26935@1" Pin0InfoVect0LinkObjId="SW-166203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20d0bf0_0" Pin1InfoVect1LinkObjId="SW-166200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-248 3972,-248 3972,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d5ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3972,-204 3972,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26935@0" ObjectIDZND0="g_20d6250@0" Pin0InfoVect0LinkObjId="g_20d6250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3972,-204 3972,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d6ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-109 3973,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="26934@x" ObjectIDND1="0@x" ObjectIDZND0="g_20d2220@0" Pin0InfoVect0LinkObjId="g_20d2220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166202_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-109 3973,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d6f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-120 3943,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26934@0" ObjectIDZND0="g_20d2220@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_20d2220_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-120 3943,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d7160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-109 3943,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_20d2220@0" ObjectIDND1="26934@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20d2220_0" Pin1InfoVect1LinkObjId="SW-166202_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-109 3943,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d73c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-248 3943,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="26935@x" ObjectIDND1="26932@x" ObjectIDZND0="g_20d0bf0@1" Pin0InfoVect0LinkObjId="g_20d0bf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166203_0" Pin1InfoVect1LinkObjId="SW-166200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-248 3943,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d7620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-265 3943,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26932@0" ObjectIDZND0="26935@x" ObjectIDZND1="g_20d0bf0@0" Pin0InfoVect0LinkObjId="SW-166203_0" Pin0InfoVect1LinkObjId="g_20d0bf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-265 3943,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e04f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-357 3814,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26860@0" ObjectIDZND0="26929@1" Pin0InfoVect0LinkObjId="SW-166181_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353f310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-357 3814,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e0750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-310 3814,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26929@0" ObjectIDZND0="26928@1" Pin0InfoVect0LinkObjId="SW-166180_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-310 3814,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e09b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-174 3814,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20dfa70@0" ObjectIDZND0="26930@1" Pin0InfoVect0LinkObjId="SW-166182_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dfa70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-174 3814,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35503b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-250 3843,-250 3843,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_20dfa70@0" ObjectIDND1="26928@x" ObjectIDZND0="26931@1" Pin0InfoVect0LinkObjId="SW-166183_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20dfa70_0" Pin1InfoVect1LinkObjId="SW-166180_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-250 3843,-250 3843,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3550610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-206 3843,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26931@0" ObjectIDZND0="g_3550870@0" Pin0InfoVect0LinkObjId="g_3550870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-206 3843,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35512c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-111 3844,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="26930@x" ObjectIDND1="0@x" ObjectIDZND0="g_20e1310@0" Pin0InfoVect0LinkObjId="g_20e1310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166182_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-111 3844,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3551520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-122 3814,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26930@0" ObjectIDZND0="g_20e1310@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_20e1310_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-122 3814,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3551780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-111 3814,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_20e1310@0" ObjectIDND1="26930@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20e1310_0" Pin1InfoVect1LinkObjId="SW-166182_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-111 3814,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35519e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-250 3814,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="26931@x" ObjectIDND1="26928@x" ObjectIDZND0="g_20dfa70@1" Pin0InfoVect0LinkObjId="g_20dfa70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166183_0" Pin1InfoVect1LinkObjId="SW-166180_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-250 3814,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3551c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-267 3814,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26928@0" ObjectIDZND0="26931@x" ObjectIDZND1="g_20dfa70@0" Pin0InfoVect0LinkObjId="SW-166183_0" Pin0InfoVect1LinkObjId="g_20dfa70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-267 3814,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355ab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-357 3686,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26860@0" ObjectIDZND0="26925@1" Pin0InfoVect0LinkObjId="SW-166161_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353f310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-357 3686,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355ad70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-309 3686,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26925@0" ObjectIDZND0="26924@1" Pin0InfoVect0LinkObjId="SW-166160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166161_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-309 3686,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-173 3686,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_355a090@0" ObjectIDZND0="26926@1" Pin0InfoVect0LinkObjId="SW-166162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_355a090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-173 3686,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355f1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-249 3715,-249 3715,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_355a090@0" ObjectIDND1="26924@x" ObjectIDZND0="26927@1" Pin0InfoVect0LinkObjId="SW-166163_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_355a090_0" Pin1InfoVect1LinkObjId="SW-166160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-249 3715,-249 3715,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3715,-205 3715,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26927@0" ObjectIDZND0="g_355f6a0@0" Pin0InfoVect0LinkObjId="g_355f6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166163_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3715,-205 3715,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35600f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-110 3716,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="26926@x" ObjectIDND1="0@x" ObjectIDZND0="g_355b930@0" Pin0InfoVect0LinkObjId="g_355b930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166162_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-110 3716,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3560350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-121 3686,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26926@0" ObjectIDZND0="g_355b930@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_355b930_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-121 3686,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35605b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-110 3686,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_355b930@0" ObjectIDND1="26926@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_355b930_0" Pin1InfoVect1LinkObjId="SW-166162_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-110 3686,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3560810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-249 3686,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="26927@x" ObjectIDND1="26924@x" ObjectIDZND0="g_355a090@1" Pin0InfoVect0LinkObjId="g_355a090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166163_0" Pin1InfoVect1LinkObjId="SW-166160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-249 3686,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3560a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-266 3686,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26924@0" ObjectIDZND0="26927@x" ObjectIDZND1="g_355a090@0" Pin0InfoVect0LinkObjId="SW-166163_0" Pin0InfoVect1LinkObjId="g_355a090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-266 3686,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25440a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-357 3558,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26860@0" ObjectIDZND0="26921@1" Pin0InfoVect0LinkObjId="SW-166141_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353f310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-357 3558,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2544300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-308 3558,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26921@0" ObjectIDZND0="26920@1" Pin0InfoVect0LinkObjId="SW-166140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166141_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-308 3558,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2544560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-172 3558,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2543620@0" ObjectIDZND0="26922@1" Pin0InfoVect0LinkObjId="SW-166142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2543620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-172 3558,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2548630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-248 3587,-248 3587,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2543620@0" ObjectIDND1="26920@x" ObjectIDZND0="26923@1" Pin0InfoVect0LinkObjId="SW-166143_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2543620_0" Pin1InfoVect1LinkObjId="SW-166140_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-248 3587,-248 3587,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2548890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3587,-204 3587,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26923@0" ObjectIDZND0="g_2548af0@0" Pin0InfoVect0LinkObjId="g_2548af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166143_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3587,-204 3587,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2549540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-109 3588,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="26922@x" ObjectIDND1="0@x" ObjectIDZND0="g_2544ec0@0" Pin0InfoVect0LinkObjId="g_2544ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166142_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-109 3588,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25497a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-120 3558,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26922@0" ObjectIDZND0="g_2544ec0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2544ec0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-120 3558,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2549a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-109 3558,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2544ec0@0" ObjectIDND1="26922@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2544ec0_0" Pin1InfoVect1LinkObjId="SW-166142_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-109 3558,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2549c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-248 3558,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="26923@x" ObjectIDND1="26920@x" ObjectIDZND0="g_2543620@1" Pin0InfoVect0LinkObjId="g_2543620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166143_0" Pin1InfoVect1LinkObjId="SW-166140_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-248 3558,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2549ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-265 3558,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26920@0" ObjectIDZND0="26923@x" ObjectIDZND1="g_2543620@0" Pin0InfoVect0LinkObjId="SW-166143_0" Pin0InfoVect1LinkObjId="g_2543620_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3558,-265 3558,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_254da40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3302,-250 3291,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="26915@x" ObjectIDND1="g_35d4f80@0" ObjectIDZND0="26919@1" Pin0InfoVect0LinkObjId="SW-166121_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166117_0" Pin1InfoVect1LinkObjId="g_35d4f80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3302,-250 3291,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_254dca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3302,-264 3302,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26915@0" ObjectIDZND0="26919@x" ObjectIDZND1="g_35d4f80@0" Pin0InfoVect0LinkObjId="SW-166121_0" Pin0InfoVect1LinkObjId="g_35d4f80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166117_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3302,-264 3302,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_254df00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3302,-250 3302,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="26919@x" ObjectIDND1="26915@x" ObjectIDZND0="g_35d4f80@1" Pin0InfoVect0LinkObjId="g_35d4f80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166121_0" Pin1InfoVect1LinkObjId="SW-166117_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3302,-250 3302,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2553100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3211,-172 3211,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_25535c0@0" Pin0InfoVect0LinkObjId="g_25535c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3211,-172 3211,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2553360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3243,-172 3243,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26918@1" ObjectIDZND0="g_2554050@0" Pin0InfoVect0LinkObjId="g_2554050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3243,-172 3243,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2554ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3302,-118 3302,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="26917@x" ObjectIDND1="26918@x" ObjectIDZND0="28371@0" Pin0InfoVect0LinkObjId="CB-CX_SF.CX_SF_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166119_0" Pin1InfoVect1LinkObjId="SW-166120_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3302,-118 3302,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2554d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3243,-136 3243,-118 3302,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="26918@0" ObjectIDZND0="26917@x" ObjectIDZND1="28371@x" Pin0InfoVect0LinkObjId="SW-166119_0" Pin0InfoVect1LinkObjId="CB-CX_SF.CX_SF_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3243,-136 3243,-118 3302,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2554fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3302,-118 3302,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="26918@x" ObjectIDND1="28371@x" ObjectIDZND0="26917@0" Pin0InfoVect0LinkObjId="SW-166119_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166120_0" Pin1InfoVect1LinkObjId="CB-CX_SF.CX_SF_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3302,-118 3302,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2559a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4355,-592 4253,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="26883@x" ObjectIDND1="26885@x" ObjectIDZND0="g_256c480@0" ObjectIDZND1="26961@x" Pin0InfoVect0LinkObjId="g_256c480_0" Pin0InfoVect1LinkObjId="g_35a7860_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-165881_0" Pin1InfoVect1LinkObjId="SW-165883_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4355,-592 4253,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2559c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-592 4253,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="26961@0" ObjectIDZND0="g_256c480@0" ObjectIDZND1="26883@x" ObjectIDZND2="26885@x" Pin0InfoVect0LinkObjId="g_256c480_0" Pin0InfoVect1LinkObjId="SW-165881_0" Pin0InfoVect2LinkObjId="SW-165883_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a7860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-592 4253,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2559ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-548 4063,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="26961@x" ObjectIDND1="26886@x" ObjectIDZND0="g_255ac40@0" Pin0InfoVect0LinkObjId="g_255ac40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35a7860_0" Pin1InfoVect1LinkObjId="SW-165887_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-548 4063,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_255a9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-592 4032,-548 4063,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="26961@x" ObjectIDZND0="g_255ac40@0" ObjectIDZND1="26886@x" Pin0InfoVect0LinkObjId="g_255ac40_0" Pin0InfoVect1LinkObjId="SW-165887_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a7860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-592 4032,-548 4063,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_255dea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-548 4098,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="26961@x" ObjectIDND1="g_255ac40@0" ObjectIDZND0="26886@0" Pin0InfoVect0LinkObjId="SW-165887_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35a7860_0" Pin1InfoVect1LinkObjId="g_255ac40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-548 4098,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_255e100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-539 4161,-548 4134,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_255e360@0" ObjectIDZND0="26886@1" Pin0InfoVect0LinkObjId="SW-165887_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_255e360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-539 4161,-548 4134,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3430,-306 3430,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26940@0" ObjectIDZND0="g_3589310@0" Pin0InfoVect0LinkObjId="g_3589310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3430,-306 3430,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2519a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-418 3439,-418 3439,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2f80320@0" ObjectIDND1="g_2fd50f0@0" ObjectIDND2="26957@x" ObjectIDZND0="26958@0" Pin0InfoVect0LinkObjId="SW-166345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f80320_0" Pin1InfoVect1LinkObjId="g_2fd50f0_0" Pin1InfoVect2LinkObjId="SW-166344_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-418 3439,-418 3439,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2519cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3439,-465 3439,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26958@1" ObjectIDZND0="g_2519f20@0" Pin0InfoVect0LinkObjId="g_2519f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166345_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3439,-465 3439,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25251e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-1096 5086,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2fa1190@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fa1190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-1096 5086,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25253d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5034,-1096 4999,-1096 4999,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_3523230@0" ObjectIDZND1="26892@x" ObjectIDZND2="26894@x" Pin0InfoVect0LinkObjId="g_3523230_0" Pin0InfoVect1LinkObjId="SW-166014_0" Pin0InfoVect2LinkObjId="SW-166016_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5034,-1096 4999,-1096 4999,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2534060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-787 4785,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26859@0" ObjectIDZND0="26901@0" Pin0InfoVect0LinkObjId="SW-166055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37ae860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-787 4785,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25342c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-787 4821,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26900@1" ObjectIDZND0="26901@1" Pin0InfoVect0LinkObjId="SW-166055_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166054_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-787 4821,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2539650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-592 4614,-592 4614,-833 4768,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26882@1" ObjectIDZND0="26859@0" Pin0InfoVect0LinkObjId="g_37ae860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-592 4614,-592 4614,-833 4768,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fa9f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-375 3919,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26888@1" ObjectIDZND0="26860@0" Pin0InfoVect0LinkObjId="g_353f310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-165892_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-375 3919,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1faa990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-344 4320,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29511@1" ObjectIDZND0="29515@0" Pin0InfoVect0LinkObjId="g_1fbd060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-344 4320,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fad9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4182,-307 4182,-254 4238,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26941@0" ObjectIDZND0="29510@1" Pin0InfoVect0LinkObjId="SW-194315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166241_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4182,-307 4182,-254 4238,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb3400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-171 4433,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fb2980@0" ObjectIDZND0="29505@1" Pin0InfoVect0LinkObjId="SW-194295_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb2980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-171 4433,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb77f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4462,-203 4462,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29506@0" ObjectIDZND0="g_1fb7a50@0" Pin0InfoVect0LinkObjId="g_1fb7a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4462,-203 4462,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb84a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-108 4463,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="29505@x" ObjectIDND1="0@x" ObjectIDZND0="g_1fb3d60@0" Pin0InfoVect0LinkObjId="g_1fb3d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194295_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-108 4463,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb8700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-119 4433,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="29505@0" ObjectIDZND0="g_1fb3d60@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1fb3d60_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-119 4433,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb8960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-108 4433,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1fb3d60@0" ObjectIDND1="29505@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fb3d60_0" Pin1InfoVect1LinkObjId="SW-194295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-108 4433,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbd060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-343 4433,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29507@1" ObjectIDZND0="29515@0" Pin0InfoVect0LinkObjId="g_1faa990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194294_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-343 4433,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbd740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-307 4433,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29507@0" ObjectIDZND0="29508@1" Pin0InfoVect0LinkObjId="SW-194292_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-307 4433,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbd930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-264 4433,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="29508@0" ObjectIDZND0="29506@x" ObjectIDZND1="g_1fb2980@0" Pin0InfoVect0LinkObjId="SW-194296_0" Pin0InfoVect1LinkObjId="g_1fb2980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-264 4433,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbe3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4462,-239 4462,-247 4433,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="29506@1" ObjectIDZND0="29508@x" ObjectIDZND1="g_1fb2980@0" Pin0InfoVect0LinkObjId="SW-194292_0" Pin0InfoVect1LinkObjId="g_1fb2980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194296_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4462,-239 4462,-247 4433,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbe610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-247 4433,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29508@x" ObjectIDND1="29506@x" ObjectIDZND0="g_1fb2980@1" Pin0InfoVect0LinkObjId="g_1fb2980_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194292_0" Pin1InfoVect1LinkObjId="SW-194296_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-247 4433,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbf4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-254 4320,-254 4320,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29510@0" ObjectIDZND0="29511@0" Pin0InfoVect0LinkObjId="SW-194318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-254 4320,-254 4320,-308 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-165047" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3185.000000 -1087.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26840" ObjectName="DYN-CX_SF"/>
     <cge:Meas_Ref ObjectId="165047"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30d9320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 560.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f847e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.000000 545.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_189d460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4450.000000 530.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd1990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 467.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd1c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 452.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d09c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 437.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd3a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 806.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd3d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 791.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18990e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 776.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353a270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 1263.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353a530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.000000 1248.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353a770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 1233.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353ab90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3484.000000 36.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353e050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3473.000000 21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353e290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3498.000000 6.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351a730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 874.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351a9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 859.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351ac30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 844.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351ae70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3529.000000 828.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351b0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 888.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351b2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3545.000000 812.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351b620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 874.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bfba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 859.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bfde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4426.000000 844.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c0020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 828.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c0260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 888.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c04a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4428.000000 812.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c07d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3195.000000 480.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c0a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3195.000000 465.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c0c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3201.000000 450.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c0ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3187.000000 434.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c1110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3195.000000 494.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c1350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3203.000000 418.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c1680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 626.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c18e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 641.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c1c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 1238.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c1e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 1223.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c20d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 1208.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c2310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 1192.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c2550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 1252.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c2790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 1176.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c4ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 222.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c5270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 207.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c54b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 192.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c56f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 176.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c5930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 236.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c5b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 160.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2529d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3153.000000 317.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2529f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3178.000000 302.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_SF.CX_SF_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3230,-358 4229,-358 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26860" ObjectName="BS-CX_SF.CX_SF_9IM"/>
    <cge:TPSR_Ref TObjectID="26860"/></metadata>
   <polyline fill="none" opacity="0" points="3230,-358 4229,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SF.CX_SF_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-896 4021,-896 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26858" ObjectName="BS-CX_SF.CX_SF_1IM"/>
    <cge:TPSR_Ref TObjectID="26858"/></metadata>
   <polyline fill="none" opacity="0" points="3560,-896 4021,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SF.CX_SF_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-896 4550,-896 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26962" ObjectName="BS-CX_SF.CX_SF_1IIM"/>
    <cge:TPSR_Ref TObjectID="26962"/></metadata>
   <polyline fill="none" opacity="0" points="4076,-896 4550,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SF.CX_SF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-1148 4768,-686 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26859" ObjectName="BS-CX_SF.CX_SF_3IM"/>
    <cge:TPSR_Ref TObjectID="26859"/></metadata>
   <polyline fill="none" opacity="0" points="4768,-1148 4768,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SF.CX_SF_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-575 4768,-256 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26963" ObjectName="BS-CX_SF.CX_SF_3IIM"/>
    <cge:TPSR_Ref TObjectID="26963"/></metadata>
   <polyline fill="none" opacity="0" points="4768,-575 4768,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SF.CX_SF_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4288,-359 4554,-359 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29515" ObjectName="BS-CX_SF.CX_SF_9ⅡM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4288,-359 4554,-359 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="26962" cx="4468" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26962" cx="4332" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26858" cx="3615" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26858" cx="3809" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26858" cx="3943" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26962" cx="4152" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26859" cx="4768" cy="-1073" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26859" cx="4768" cy="-929" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26859" cx="4768" cy="-1026" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26963" cx="4768" cy="-496" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26963" cx="4768" cy="-352" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26963" cx="4768" cy="-411" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26963" cx="4768" cy="-543" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26859" cx="4768" cy="-736" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="3302" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="3430" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="3558" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="3686" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="3814" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="3943" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="4070" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="4182" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26859" cx="4768" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26859" cx="4768" cy="-833" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="3391" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26860" cx="3919" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29515" cx="4320" cy="-359" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29515" cx="4433" cy="-359" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-165857">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 -765.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26874" ObjectName="SW-CX_SF.CX_SF_101BK"/>
     <cge:Meas_Ref ObjectId="165857"/>
    <cge:TPSR_Ref TObjectID="26874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165891">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 -421.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26887" ObjectName="SW-CX_SF.CX_SF_001BK"/>
     <cge:Meas_Ref ObjectId="165891"/>
    <cge:TPSR_Ref TObjectID="26887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165879">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4457.000000 -582.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26881" ObjectName="SW-CX_SF.CX_SF_301BK"/>
     <cge:Meas_Ref ObjectId="165879"/>
    <cge:TPSR_Ref TObjectID="26881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166012">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.000000 -1063.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26890" ObjectName="SW-CX_SF.CX_SF_361BK"/>
     <cge:Meas_Ref ObjectId="166012"/>
    <cge:TPSR_Ref TObjectID="26890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165790">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -972.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26868" ObjectName="SW-CX_SF.CX_SF_162BK"/>
     <cge:Meas_Ref ObjectId="165790"/>
    <cge:TPSR_Ref TObjectID="26868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-165725">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -973.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26862" ObjectName="SW-CX_SF.CX_SF_163BK"/>
     <cge:Meas_Ref ObjectId="165725"/>
    <cge:TPSR_Ref TObjectID="26862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166117">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3293.000000 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26915" ObjectName="SW-CX_SF.CX_SF_062BK"/>
     <cge:Meas_Ref ObjectId="166117"/>
    <cge:TPSR_Ref TObjectID="26915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166220">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -258.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26936" ObjectName="SW-CX_SF.CX_SF_068BK"/>
     <cge:Meas_Ref ObjectId="166220"/>
    <cge:TPSR_Ref TObjectID="26936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166033">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4827.000000 -919.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26895" ObjectName="SW-CX_SF.CX_SF_362BK"/>
     <cge:Meas_Ref ObjectId="166033"/>
    <cge:TPSR_Ref TObjectID="26895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.000000 -486.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26905" ObjectName="SW-CX_SF.CX_SF_364BK"/>
     <cge:Meas_Ref ObjectId="166075"/>
    <cge:TPSR_Ref TObjectID="26905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4827.000000 -342.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26910" ObjectName="SW-CX_SF.CX_SF_365BK"/>
     <cge:Meas_Ref ObjectId="166096"/>
    <cge:TPSR_Ref TObjectID="26910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166262">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4670.000000 -615.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26946" ObjectName="SW-CX_SF.CX_SF_312BK"/>
     <cge:Meas_Ref ObjectId="166262"/>
    <cge:TPSR_Ref TObjectID="26946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166200">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26932" ObjectName="SW-CX_SF.CX_SF_067BK"/>
     <cge:Meas_Ref ObjectId="166200"/>
    <cge:TPSR_Ref TObjectID="26932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166180">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -259.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26928" ObjectName="SW-CX_SF.CX_SF_066BK"/>
     <cge:Meas_Ref ObjectId="166180"/>
    <cge:TPSR_Ref TObjectID="26928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3677.000000 -258.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26924" ObjectName="SW-CX_SF.CX_SF_065BK"/>
     <cge:Meas_Ref ObjectId="166160"/>
    <cge:TPSR_Ref TObjectID="26924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166140">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3549.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26920" ObjectName="SW-CX_SF.CX_SF_064BK"/>
     <cge:Meas_Ref ObjectId="166140"/>
    <cge:TPSR_Ref TObjectID="26920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166054">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4828.000000 -777.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26900" ObjectName="SW-CX_SF.CX_SF_363BK"/>
     <cge:Meas_Ref ObjectId="166054"/>
    <cge:TPSR_Ref TObjectID="26900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -244.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29510" ObjectName="SW-CX_SF.CX_SF_012BK"/>
     <cge:Meas_Ref ObjectId="194315"/>
    <cge:TPSR_Ref TObjectID="29510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194292">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29508" ObjectName="SW-CX_SF.CX_SF_071BK"/>
     <cge:Meas_Ref ObjectId="194292"/>
    <cge:TPSR_Ref TObjectID="29508"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -690.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -690.000000) translate(0,27)">SFSZ10-40000/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -690.000000) translate(0,42)">110±8×1.25%/38.5±1×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -690.000000) translate(0,57)">U12%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -690.000000) translate(0,72)">U13%=17.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -690.000000) translate(0,87)">U23%=6.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -690.000000) translate(0,102)">40MVA/40MVA/40MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -690.000000) translate(0,117)">YN,yn0,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_359e6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2933.000000 -1041.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_359e6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2933.000000 -1041.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_359e6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2933.000000 -1041.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_359e6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2933.000000 -1041.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_359e6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2933.000000 -1041.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_359e6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2933.000000 -1041.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_359e6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2933.000000 -1041.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fba9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -589.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f67020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -1058.000000) translate(0,15)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_317a3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4478.000000 -387.000000) translate(0,15)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2f81fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3066.000000 -1167.500000) translate(0,16)">哨房变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f9d330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -157.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f9d330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -157.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f9d330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -157.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f9d330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -157.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f9d330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -157.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35391d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -133.000000) translate(0,15)">凤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35391d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -133.000000) translate(0,33)">凰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35391d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -133.000000) translate(0,51)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35391d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -133.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3539be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -104.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3539be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -104.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3539be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -104.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3539be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -104.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3539be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -104.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3539be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -104.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3539e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3048.000000 -252.000000) translate(0,12)">4916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -1297.000000) translate(0,15)">元</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -1297.000000) translate(0,33)">黄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -1297.000000) translate(0,51)">哨</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -1297.000000) translate(0,69)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -1297.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -1297.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -1298.000000) translate(0,15)">元</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -1298.000000) translate(0,33)">黄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -1298.000000) translate(0,51)">哨</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -1298.000000) translate(0,69)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -1298.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_353e6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -1298.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3519140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -1113.000000) translate(0,12)">110kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3519140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -1113.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3519f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4433.000000 -1113.000000) translate(0,12)">110kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3519f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4433.000000 -1113.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351a220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4604.000000 -1101.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351a220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4604.000000 -1101.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3571b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -914.000000) translate(0,15)">哨黄能线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3546f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5118.000000 -771.000000) translate(0,15)">哨能线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3536170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5095.000000 -700.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25cd460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -481.000000) translate(0,15)">哨平线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c4a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4602.000000 -486.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c4a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4602.000000 -486.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b39f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -337.000000) translate(0,15)">哨老麻丙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20d2eb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3914.000000 -139.000000) translate(0,15)">清</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20d2eb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3914.000000 -139.000000) translate(0,33)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20d2eb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3914.000000 -139.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_354d6e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3780.000000 -157.000000) translate(0,15)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_354d6e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3780.000000 -157.000000) translate(0,33)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_354d6e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3780.000000 -157.000000) translate(0,51)">Ⅲ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_354d6e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3780.000000 -157.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_354d6e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3780.000000 -157.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_355c660" transform="matrix(1.000000 0.000000 0.000000 1.000000 3657.000000 -157.000000) translate(0,15)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_355c660" transform="matrix(1.000000 0.000000 0.000000 1.000000 3657.000000 -157.000000) translate(0,33)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_355c660" transform="matrix(1.000000 0.000000 0.000000 1.000000 3657.000000 -157.000000) translate(0,51)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_355c660" transform="matrix(1.000000 0.000000 0.000000 1.000000 3657.000000 -157.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_355c660" transform="matrix(1.000000 0.000000 0.000000 1.000000 3657.000000 -157.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2545bf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3522.000000 -157.000000) translate(0,15)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2545bf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3522.000000 -157.000000) translate(0,33)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2545bf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3522.000000 -157.000000) translate(0,51)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2545bf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3522.000000 -157.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2545bf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3522.000000 -157.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251a9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -973.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251afe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3631.000000 -948.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251b220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3630.000000 -1030.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251b460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3818.000000 -1001.000000) translate(0,12)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3816.000000 -938.000000) translate(0,12)">1621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251b8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3758.000000 -992.000000) translate(0,12)">16217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251bb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3757.000000 -1049.000000) translate(0,12)">16260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3758.000000 -1117.000000) translate(0,12)">16267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251bfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3816.000000 -1062.000000) translate(0,12)">1626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251c1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -938.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251c420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4159.000000 -938.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251c660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -1029.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4159.000000 -1029.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251cae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -1002.000000) translate(0,12)">163</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251cd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 -939.000000) translate(0,12)">1632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251cf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 -1063.000000) translate(0,12)">1636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -993.000000) translate(0,12)">16327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -1050.000000) translate(0,12)">16360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4281.000000 -1118.000000) translate(0,12)">16367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -980.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251daa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -952.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251dce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4485.000000 -1034.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251df20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3714.000000 -915.000000) translate(0,12)">110kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251e160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -915.000000) translate(0,12)">110kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251e3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -587.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251e5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.000000 -794.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251e820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3927.000000 -719.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251ea60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3927.000000 -866.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251eca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -847.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251eee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3867.000000 -777.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -701.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251f360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4467.000000 -616.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251f5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4529.000000 -618.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251f7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4446.000000 -645.000000) translate(0,12)">30160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251fa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -618.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251fc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -644.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3928.000000 -450.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25200e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3926.000000 -501.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3926.000000 -400.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3311.000000 -285.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25207a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3309.000000 -162.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25209e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3248.000000 -168.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3309.000000 -332.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3244.000000 -276.000000) translate(0,12)">06260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25210a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3437.000000 -331.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25212e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -286.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3565.000000 -145.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3594.000000 -229.000000) translate(0,12)">06460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25219a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3565.000000 -333.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.000000 -287.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3693.000000 -334.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3722.000000 -230.000000) translate(0,12)">06560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25222a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3693.000000 -146.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25224e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3823.000000 -288.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3821.000000 -335.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -231.000000) translate(0,12)">06660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3821.000000 -147.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3952.000000 -286.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2523020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -145.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2523260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 -229.000000) translate(0,12)">06760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25234a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -333.000000) translate(0,12)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25236e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -287.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2523920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4077.000000 -146.000000) translate(0,12)">0686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2523b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -230.000000) translate(0,12)">06860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2523da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4077.000000 -334.000000) translate(0,12)">0681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2523fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3400.000000 -394.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2524220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3446.000000 -454.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2524460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4189.000000 -332.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25246a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4836.000000 -1097.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25248e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -1099.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2524b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4797.000000 -1099.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2524d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4887.000000 -1113.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2524fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4991.000000 -1127.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25255c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4837.000000 -953.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25258e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -955.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2525b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4888.000000 -969.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2525d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4994.000000 -982.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2525fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -955.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25261e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -811.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2526420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4997.000000 -838.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2526660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -825.000000) translate(0,12)">36360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25268a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4836.000000 -520.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2526ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -522.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2526d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -549.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2526f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4887.000000 -536.000000) translate(0,12)">36460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25271a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4796.000000 -522.000000) translate(0,12)">3642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25273e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4837.000000 -376.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2527620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -378.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2527860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4994.000000 -405.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2527aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4888.000000 -392.000000) translate(0,12)">36560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2527ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4797.000000 -378.000000) translate(0,12)">3652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2527f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4647.000000 -644.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2528160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4729.000000 -762.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25283a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4729.000000 -569.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25285e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4773.000000 -1148.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2528820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4773.000000 -575.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2528a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -1052.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2528ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4717.000000 -437.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2528ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3839.000000 -535.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2530f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -574.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2531770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4116.000000 -467.000000) translate(0,12)">1号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2531940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -646.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2531e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5080.000000 -1131.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25320d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5085.000000 -981.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2532310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5091.000000 -838.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2532550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5083.000000 -555.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2532790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5086.000000 -403.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25329d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3324.000000 -537.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25329d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3324.000000 -537.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2532c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2939.000000 -760.000000) translate(0,15)">公共信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2537020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4795.000000 -813.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2539160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 -811.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fadc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -333.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fae250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4239.000000 -278.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -156.000000) translate(0,15)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -156.000000) translate(0,33)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -156.000000) translate(0,51)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -156.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -156.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbbc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -285.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbc280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4440.000000 -332.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbc4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4440.000000 -144.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbc700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -228.000000) translate(0,12)">07160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fbf2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -244.000000) translate(0,15)">分段</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20a82a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -538.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a9530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.666667 -916.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e4d00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.666667 -998.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3586e30" refnum="0">
    <use class="BV-0KV" transform="matrix(0.954545 -0.000000 0.000000 -1.000000 4530.000000 -920.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f85620" refnum="0">
    <use class="BV-0KV" transform="matrix(0.954545 -0.000000 0.000000 -1.000000 4529.000000 -1002.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f88000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 -1085.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f62be0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 -1017.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a64200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 -960.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f4fd80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -1086.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f507b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -1018.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f686e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -961.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3581d00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -815.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3582730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -745.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_353b7b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -669.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35d5ec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3226.000000 -244.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256af60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3937.000000 -1053.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256b9f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4146.000000 -1052.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f63490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4349.000000 -665.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f63f20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.000000 -666.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35cd620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4980.000000 -1133.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35ce0b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.000000 -1133.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_357eca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 -989.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_357f730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -989.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3531c70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.000000 -845.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3532700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4878.000000 -845.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_259aa50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4980.000000 -556.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_259b4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.000000 -556.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c0060" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 -412.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c0af0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -412.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37b40d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4093.000000 -176.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20d6250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -175.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3550870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3837.000000 -177.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_355f6a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3709.000000 -176.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2548af0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3581.000000 -175.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25535c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3205.000000 -182.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2554050" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3237.000000 -183.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2519f20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3433.000000 -471.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb7a50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 -174.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_SF"/>
</svg>