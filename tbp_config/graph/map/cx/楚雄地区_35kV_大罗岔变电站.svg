<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-134" aopId="254" id="thSvg" viewBox="3117 -1247 1359 1370">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.7" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
   </symbol>
   <symbol id="capacitor:shape29">
    <polyline arcFlag="1" fill="none" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <polyline arcFlag="1" fill="none" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="19" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <polyline arcFlag="1" fill="none" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <polyline arcFlag="1" fill="none" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape163">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <polyline fill="none" points="34,21 34,9 19,9 " stroke-width="1"/>
    <polyline fill="none" points="35,33 35,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="29" y1="28" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="44" y1="28" y2="28"/>
    <rect height="9" stroke-width="1" width="5" x="32" y="21"/>
    <ellipse cx="8" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="7" x2="7" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="10" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="4" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="10" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="9" x2="9" y1="12" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape190">
    <ellipse cx="24" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="33" x2="39" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="35" x2="33" y1="29" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="36" x2="39" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="40" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="37" x2="37" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="32" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="13" x2="5" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="10" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="12" x2="6" y1="4" y2="4"/>
    <ellipse cx="35" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="24" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="35" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <rect height="9" stroke-width="1" width="5" x="7" y="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="0" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="12" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="19" y2="19"/>
    <polyline fill="none" points="9,7 9,10 " stroke-width="1"/>
    <polyline fill="none" points="10,19 10,31 25,31 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape191">
    <circle cx="32" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="31" y1="81" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="35" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="27" y1="87" y2="81"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="31,59 6,59 6,30 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,16 38,28 26,28 32,16 32,16 32,16 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="43" y2="1"/>
    <circle cx="32" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="28" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="36" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b2090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b2970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b32b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b4500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b57f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b6490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b7030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b7a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b82a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b8c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b9870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21ba080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21ba660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bb000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bb880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21bbfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bd760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21be460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bed20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21bf710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c08f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c1270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c1d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c70e0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c7e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c3a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c4f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21d44c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21c9ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1380" width="1369" x="3112" y="-1252"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3215" y="-477"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-83654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -1068.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18440" ObjectName="SW-CX_DLC.CX_DLC_3916SW"/>
     <cge:Meas_Ref ObjectId="83654"/>
    <cge:TPSR_Ref TObjectID="18440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 -1088.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18441" ObjectName="SW-CX_DLC.CX_DLC_39167SW"/>
     <cge:Meas_Ref ObjectId="83655"/>
    <cge:TPSR_Ref TObjectID="18441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83668">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -945.000000)" xlink:href="#switch2:shape2-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18447" ObjectName="SW-CX_DLC.CX_DLC_35ZybXC1"/>
     <cge:Meas_Ref ObjectId="83668"/>
    <cge:TPSR_Ref TObjectID="18447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83668">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -904.000000)" xlink:href="#switch2:shape3-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18446" ObjectName="SW-CX_DLC.CX_DLC_35ZybXC"/>
     <cge:Meas_Ref ObjectId="83668"/>
    <cge:TPSR_Ref TObjectID="18446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83621">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3628.000000 -161.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18429" ObjectName="SW-CX_DLC.CX_DLC_0916SW"/>
     <cge:Meas_Ref ObjectId="83621"/>
    <cge:TPSR_Ref TObjectID="18429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83622">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3596.000000 -235.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18430" ObjectName="SW-CX_DLC.CX_DLC_09167SW"/>
     <cge:Meas_Ref ObjectId="83622"/>
    <cge:TPSR_Ref TObjectID="18430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83631">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 -164.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18432" ObjectName="SW-CX_DLC.CX_DLC_0926SW"/>
     <cge:Meas_Ref ObjectId="83631"/>
    <cge:TPSR_Ref TObjectID="18432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83632">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18433" ObjectName="SW-CX_DLC.CX_DLC_09267SW"/>
     <cge:Meas_Ref ObjectId="83632"/>
    <cge:TPSR_Ref TObjectID="18433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83644">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.000000 -238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18438" ObjectName="SW-CX_DLC.CX_DLC_09360SW"/>
     <cge:Meas_Ref ObjectId="83644"/>
    <cge:TPSR_Ref TObjectID="18438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -356.000000)" xlink:href="#switch2:shape2-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18448" ObjectName="SW-CX_DLC.CX_DLC_0901XC"/>
     <cge:Meas_Ref ObjectId="83669"/>
    <cge:TPSR_Ref TObjectID="18448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -294.000000)" xlink:href="#switch2:shape3-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18449" ObjectName="SW-CX_DLC.CX_DLC_0901XC1"/>
     <cge:Meas_Ref ObjectId="83669"/>
    <cge:TPSR_Ref TObjectID="18449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83668">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -850.000000)" xlink:href="#switch2:shape2-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18446" ObjectName="SW-CX_DLC.CX_DLC_35ZybXC"/>
     <cge:Meas_Ref ObjectId="83668"/>
    <cge:TPSR_Ref TObjectID="18446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83668">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -776.000000)" xlink:href="#switch2:shape3-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18447" ObjectName="SW-CX_DLC.CX_DLC_35ZybXC1"/>
     <cge:Meas_Ref ObjectId="83668"/>
    <cge:TPSR_Ref TObjectID="18447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83641">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18435" ObjectName="SW-CX_DLC.CX_DLC_0936SW"/>
     <cge:Meas_Ref ObjectId="83641"/>
    <cge:TPSR_Ref TObjectID="18435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83643">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -115.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18437" ObjectName="SW-CX_DLC.CX_DLC_09367SW"/>
     <cge:Meas_Ref ObjectId="83643"/>
    <cge:TPSR_Ref TObjectID="18437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 25.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18436" ObjectName="SW-CX_DLC.CX_DLC_09300SW"/>
     <cge:Meas_Ref ObjectId="83642"/>
    <cge:TPSR_Ref TObjectID="18436"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3929,-1223 3929,-1246 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3929,-1223 3929,-1246 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DLC.CX_DLC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="25699"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -569.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -569.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="18451" ObjectName="TF-CX_DLC.CX_DLC_1T"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b60b50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4234.000000 -1014.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21e3100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -1001.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ec6950">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3959.000000 -933.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2132c60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4194.000000 -997.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a5e40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4184.000000 -1041.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2939b50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.841270 4046.000000 -662.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bf050">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3632.000000 -216.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_268fd30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -209.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26870b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3665.000000 -75.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dbd420">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 -43.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24c4530">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -219.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29e2aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c57c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3915.000000 -78.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_b3f5200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3877.000000 -46.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27214d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -219.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23efdd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.000000 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef52b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 -225.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c17ba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4337.000000 -232.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a3200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4322.000000 -171.000000)" xlink:href="#lightningRod:shape190"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18a8800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -651.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218c460">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -807.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_b3efc40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3759.000000 -640.000000)" xlink:href="#lightningRod:shape191"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18cd9a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -1152.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ae4560">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.000000 -1067.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2595520">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.000000 -1153.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2228c20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.888889 4045.000000 -826.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b4a40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.841270 4045.000000 -516.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-83617" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3256.538462 -1017.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83617" ObjectName="CX_DLC:CX_DLC_001BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-83671" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4195.000000 -581.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83671" ObjectName="CX_DLC:CX_DLC_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-83670" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4195.000000 -564.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83670" ObjectName="CX_DLC:CX_DLC_1T_Tmp"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-83594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 -956.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18426"/>
     <cge:Term_Ref ObjectID="25649"/>
    <cge:TPSR_Ref TObjectID="18426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-83595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 -956.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18426"/>
     <cge:Term_Ref ObjectID="25649"/>
    <cge:TPSR_Ref TObjectID="18426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-83596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 -956.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18426"/>
     <cge:Term_Ref ObjectID="25649"/>
    <cge:TPSR_Ref TObjectID="18426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-83597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 -956.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18426"/>
     <cge:Term_Ref ObjectID="25649"/>
    <cge:TPSR_Ref TObjectID="18426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4070.000000 -979.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18439"/>
     <cge:Term_Ref ObjectID="25673"/>
    <cge:TPSR_Ref TObjectID="18439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4070.000000 -979.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18439"/>
     <cge:Term_Ref ObjectID="25673"/>
    <cge:TPSR_Ref TObjectID="18439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4070.000000 -979.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18439"/>
     <cge:Term_Ref ObjectID="25673"/>
    <cge:TPSR_Ref TObjectID="18439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -804.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18442"/>
     <cge:Term_Ref ObjectID="25679"/>
    <cge:TPSR_Ref TObjectID="18442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -804.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18442"/>
     <cge:Term_Ref ObjectID="25679"/>
    <cge:TPSR_Ref TObjectID="18442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -804.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18442"/>
     <cge:Term_Ref ObjectID="25679"/>
    <cge:TPSR_Ref TObjectID="18442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4194.000000 -504.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18443"/>
     <cge:Term_Ref ObjectID="25681"/>
    <cge:TPSR_Ref TObjectID="18443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4194.000000 -504.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18443"/>
     <cge:Term_Ref ObjectID="25681"/>
    <cge:TPSR_Ref TObjectID="18443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4194.000000 -504.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18443"/>
     <cge:Term_Ref ObjectID="25681"/>
    <cge:TPSR_Ref TObjectID="18443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-83601" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3634.000000 -492.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18427"/>
     <cge:Term_Ref ObjectID="25650"/>
    <cge:TPSR_Ref TObjectID="18427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-83602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3634.000000 -492.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18427"/>
     <cge:Term_Ref ObjectID="25650"/>
    <cge:TPSR_Ref TObjectID="18427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-83603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3634.000000 -492.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18427"/>
     <cge:Term_Ref ObjectID="25650"/>
    <cge:TPSR_Ref TObjectID="18427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-83604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3634.000000 -492.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18427"/>
     <cge:Term_Ref ObjectID="25650"/>
    <cge:TPSR_Ref TObjectID="18427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3661.000000 31.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18428"/>
     <cge:Term_Ref ObjectID="25651"/>
    <cge:TPSR_Ref TObjectID="18428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3661.000000 31.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18428"/>
     <cge:Term_Ref ObjectID="25651"/>
    <cge:TPSR_Ref TObjectID="18428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3661.000000 31.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18428"/>
     <cge:Term_Ref ObjectID="25651"/>
    <cge:TPSR_Ref TObjectID="18428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 33.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18431"/>
     <cge:Term_Ref ObjectID="25657"/>
    <cge:TPSR_Ref TObjectID="18431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 33.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18431"/>
     <cge:Term_Ref ObjectID="25657"/>
    <cge:TPSR_Ref TObjectID="18431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 33.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18431"/>
     <cge:Term_Ref ObjectID="25657"/>
    <cge:TPSR_Ref TObjectID="18431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4200.000000 37.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18434"/>
     <cge:Term_Ref ObjectID="25663"/>
    <cge:TPSR_Ref TObjectID="18434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4200.000000 37.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18434"/>
     <cge:Term_Ref ObjectID="25663"/>
    <cge:TPSR_Ref TObjectID="18434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4200.000000 37.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18434"/>
     <cge:Term_Ref ObjectID="25663"/>
    <cge:TPSR_Ref TObjectID="18434"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="42" opacity="0" stroke="white" transform="" width="164" x="3246" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/></g>
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-83653">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 -892.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18439" ObjectName="SW-CX_DLC.CX_DLC_391BK"/>
     <cge:Meas_Ref ObjectId="83653"/>
    <cge:TPSR_Ref TObjectID="18439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -721.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18442" ObjectName="SW-CX_DLC.CX_DLC_301BK"/>
     <cge:Meas_Ref ObjectId="83665"/>
    <cge:TPSR_Ref TObjectID="18442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83666">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -411.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18443" ObjectName="SW-CX_DLC.CX_DLC_001BK"/>
     <cge:Meas_Ref ObjectId="83666"/>
    <cge:TPSR_Ref TObjectID="18443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83620">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 -286.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18428" ObjectName="SW-CX_DLC.CX_DLC_091BK"/>
     <cge:Meas_Ref ObjectId="83620"/>
    <cge:TPSR_Ref TObjectID="18428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83630">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3877.000000 -289.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18431" ObjectName="SW-CX_DLC.CX_DLC_092BK"/>
     <cge:Meas_Ref ObjectId="83630"/>
    <cge:TPSR_Ref TObjectID="18431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83640">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4097.000000 -289.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18434" ObjectName="SW-CX_DLC.CX_DLC_093BK"/>
     <cge:Meas_Ref ObjectId="83640"/>
    <cge:TPSR_Ref TObjectID="18434"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_DLC.CX_DLC_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 -26.000000)" xlink:href="#capacitor:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18453" ObjectName="CB-CX_DLC.CX_DLC_1C"/>
    <cge:TPSR_Ref TObjectID="18453"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_29a26d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1059 3929,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_21e3100@1" ObjectIDZND0="18440@0" Pin0InfoVect0LinkObjId="SW-83654_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21e3100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1059 3929,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b2cce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-1093 3867,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18441@0" ObjectIDZND0="g_24e4100@0" Pin0InfoVect0LinkObjId="g_24e4100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-1093 3867,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfbcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-1033 4203,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2132c60@0" ObjectIDZND0="g_20a5e40@0" Pin0InfoVect0LinkObjId="g_20a5e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2132c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-1033 4203,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c3690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-951 4203,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="18447@1" ObjectIDZND0="18446@1" Pin0InfoVect0LinkObjId="SW-83668_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83668_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-951 4203,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253bde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-910 4203,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18446@0" ObjectIDZND0="18426@0" Pin0InfoVect0LinkObjId="g_2931db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83668_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-910 4203,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c07a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-886 3929,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="18426@0" ObjectIDZND0="18439@0" Pin0InfoVect0LinkObjId="SW-83653_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_253bde0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-886 3929,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24c7500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-408 4050,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="18427@0" ObjectIDZND0="18443@0" Pin0InfoVect0LinkObjId="SW-83666_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-408 4050,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f61500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-408 3637,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="18427@0" ObjectIDZND0="18428@1" Pin0InfoVect0LinkObjId="SW-83620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-408 3637,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_220fbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-274 3637,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_29bf050@1" ObjectIDZND0="18428@0" Pin0InfoVect0LinkObjId="SW-83620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29bf050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-274 3637,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206d7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3688,-267 3688,-288 3605,-288 3605,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_268fd30@0" ObjectIDZND0="18430@1" Pin0InfoVect0LinkObjId="SW-83622_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_268fd30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3688,-267 3688,-288 3605,-288 3605,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e56460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3605,-240 3605,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18430@0" ObjectIDZND0="g_1cece80@0" Pin0InfoVect0LinkObjId="g_1cece80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83622_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3605,-240 3605,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16d7820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3672,-133 3672,-149 3637,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_26870b0@0" ObjectIDZND0="g_1dbd420@0" ObjectIDZND1="18429@x" Pin0InfoVect0LinkObjId="g_1dbd420_0" Pin0InfoVect1LinkObjId="SW-83621_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26870b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3672,-133 3672,-149 3637,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2608280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-62 3637,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1dbd420@0" ObjectIDZND0="g_26870b0@0" ObjectIDZND1="18429@x" Pin0InfoVect0LinkObjId="g_26870b0_0" Pin0InfoVect1LinkObjId="SW-83621_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbd420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-62 3637,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21fd2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-149 3637,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_26870b0@0" ObjectIDND1="g_1dbd420@0" ObjectIDZND0="18429@0" Pin0InfoVect0LinkObjId="SW-83621_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26870b0_0" Pin1InfoVect1LinkObjId="g_1dbd420_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-149 3637,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2dc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-202 3637,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18429@1" ObjectIDZND0="g_29bf050@0" Pin0InfoVect0LinkObjId="g_29bf050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83621_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-202 3637,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2073b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-408 3887,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="18427@0" ObjectIDZND0="18431@1" Pin0InfoVect0LinkObjId="SW-83630_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-408 3887,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee8710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-277 3887,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_24c4530@1" ObjectIDZND0="18431@0" Pin0InfoVect0LinkObjId="SW-83630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24c4530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-277 3887,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_270d7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3938,-270 3938,-291 3855,-291 3855,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_29e2aa0@0" ObjectIDZND0="18433@1" Pin0InfoVect0LinkObjId="SW-83632_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e2aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3938,-270 3938,-291 3855,-291 3855,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_185c950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-243 3855,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18433@0" ObjectIDZND0="g_1b695f0@0" Pin0InfoVect0LinkObjId="g_1b695f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83632_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-243 3855,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245b2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3922,-136 3922,-152 3887,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_29c57c0@0" ObjectIDZND0="g_b3f5200@0" ObjectIDZND1="18432@x" Pin0InfoVect0LinkObjId="g_b3f5200_0" Pin0InfoVect1LinkObjId="SW-83631_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c57c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3922,-136 3922,-152 3887,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16f9bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-65 3887,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_b3f5200@0" ObjectIDZND0="g_29c57c0@0" ObjectIDZND1="18432@x" Pin0InfoVect0LinkObjId="g_29c57c0_0" Pin0InfoVect1LinkObjId="SW-83631_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_b3f5200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-65 3887,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2299710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-152 3887,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_29c57c0@0" ObjectIDND1="g_b3f5200@0" ObjectIDZND0="18432@0" Pin0InfoVect0LinkObjId="SW-83631_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c57c0_0" Pin1InfoVect1LinkObjId="g_b3f5200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-152 3887,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2963430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-205 3887,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18432@1" ObjectIDZND0="g_24c4530@0" Pin0InfoVect0LinkObjId="g_24c4530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83631_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-205 3887,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d6120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-408 4107,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="18427@0" ObjectIDZND0="18434@1" Pin0InfoVect0LinkObjId="SW-83640_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-408 4107,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a72090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-277 4107,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_27214d0@1" ObjectIDZND0="18434@0" Pin0InfoVect0LinkObjId="SW-83640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27214d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-277 4107,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ef64a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-270 4158,-291 4075,-291 4075,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_23efdd0@0" ObjectIDZND0="18438@1" Pin0InfoVect0LinkObjId="SW-83644_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23efdd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-270 4158,-291 4075,-291 4075,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26606c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4075,-243 4075,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18438@0" ObjectIDZND0="g_b409f30@0" Pin0InfoVect0LinkObjId="g_b409f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83644_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4075,-243 4075,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_b40aa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-408 4346,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18427@0" ObjectIDZND0="18448@0" Pin0InfoVect0LinkObjId="SW-83669_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-408 4346,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3011840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-362 4346,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="18448@1" ObjectIDZND0="18449@1" Pin0InfoVect0LinkObjId="SW-83669_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83669_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-362 4346,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e2c240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4409,-283 4409,-289 4346,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1ef52b0@0" ObjectIDZND0="18449@x" ObjectIDZND1="g_1c17ba0@0" Pin0InfoVect0LinkObjId="SW-83669_0" Pin0InfoVect1LinkObjId="g_1c17ba0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ef52b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4409,-283 4409,-289 4346,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-300 4346,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18449@0" ObjectIDZND0="g_1ef52b0@0" ObjectIDZND1="g_1c17ba0@0" Pin0InfoVect0LinkObjId="g_1ef52b0_0" Pin0InfoVect1LinkObjId="g_1c17ba0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83669_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-300 4346,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2920450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-289 4346,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1ef52b0@0" ObjectIDND1="18449@x" ObjectIDZND0="g_1c17ba0@0" Pin0InfoVect0LinkObjId="g_1c17ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ef52b0_0" Pin1InfoVect1LinkObjId="SW-83669_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-289 4346,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29840e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-237 4346,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c17ba0@1" ObjectIDZND0="g_24a3200@0" Pin0InfoVect0LinkObjId="g_24a3200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c17ba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-237 4346,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24f0d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-886 3790,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18426@0" ObjectIDZND0="18446@0" Pin0InfoVect0LinkObjId="SW-83668_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_253bde0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-886 3790,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206a5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-856 3790,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18446@1" ObjectIDZND0="g_218c460@0" Pin0InfoVect0LinkObjId="g_218c460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83668_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-856 3790,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c9840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-812 3790,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_218c460@1" ObjectIDZND0="18447@1" Pin0InfoVect0LinkObjId="SW-83668_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218c460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-812 3790,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-782 3790,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18447@0" ObjectIDZND0="g_b3efc40@0" Pin0InfoVect0LinkObjId="g_b3efc40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83668_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-782 3790,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b4e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1109 3929,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18440@1" ObjectIDZND0="g_18cd9a0@0" Pin0InfoVect0LinkObjId="g_18cd9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1109 3929,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a83da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-1129 3867,-1139 3985,-1139 3985,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18441@1" ObjectIDZND0="g_1ae4560@0" Pin0InfoVect0LinkObjId="g_1ae4560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83655_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-1129 3867,-1139 3985,-1139 3985,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21e3300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1000 3966,-1000 3966,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18439@x" ObjectIDND1="g_21e3100@0" ObjectIDZND0="g_1ec6950@0" Pin0InfoVect0LinkObjId="g_1ec6950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-83653_0" Pin1InfoVect1LinkObjId="g_21e3100_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1000 3966,-1000 3966,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1878c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1210 3929,-1224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="g_18cd9a0@1" ObjectIDZND0="0@1" ObjectIDZND1="g_2595520@0" Pin0InfoVect0LinkObjId="g_1b60b50_1" Pin0InfoVect1LinkObjId="g_2595520_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18cd9a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1210 3929,-1224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23b2690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1220 3888,-1220 3888,-1211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_18cd9a0@0" ObjectIDND1="0@1" ObjectIDZND0="g_2595520@0" Pin0InfoVect0LinkObjId="g_2595520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18cd9a0_0" Pin1InfoVect1LinkObjId="g_1b60b50_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1220 3888,-1220 3888,-1211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24215b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-709 4097,-720 4051,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_18a8800@0" ObjectIDZND0="g_2939b50@0" ObjectIDZND1="g_18a8800@0" ObjectIDZND2="g_2939b50@0" Pin0InfoVect0LinkObjId="g_2939b50_0" Pin0InfoVect1LinkObjId="g_18a8800_0" Pin0InfoVect2LinkObjId="g_2939b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18a8800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-709 4097,-720 4051,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d9fab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-654 4051,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="18451@1" ObjectIDZND0="g_2939b50@0" Pin0InfoVect0LinkObjId="g_2939b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-654 4051,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1de98c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-710 4051,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2939b50@1" ObjectIDZND0="g_18a8800@0" ObjectIDZND1="g_18a8800@0" ObjectIDZND2="g_2939b50@0" Pin0InfoVect0LinkObjId="g_18a8800_0" Pin0InfoVect1LinkObjId="g_18a8800_0" Pin0InfoVect2LinkObjId="g_2939b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2939b50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-710 4051,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa7430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-724 4051,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_18a8800@0" ObjectIDND1="g_2939b50@0" ObjectIDND2="18442@x" ObjectIDZND0="g_18a8800@0" ObjectIDZND1="g_2939b50@0" ObjectIDZND2="18442@x" Pin0InfoVect0LinkObjId="g_18a8800_0" Pin0InfoVect1LinkObjId="g_2939b50_0" Pin0InfoVect2LinkObjId="SW-83665_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18a8800_0" Pin1InfoVect1LinkObjId="g_2939b50_0" Pin1InfoVect2LinkObjId="SW-83665_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-724 4051,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2931db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-877 4050,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2228c20@1" ObjectIDZND0="18426@0" Pin0InfoVect0LinkObjId="g_253bde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2228c20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-877 4050,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25070b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-210 4107,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18435@1" ObjectIDZND0="g_27214d0@0" Pin0InfoVect0LinkObjId="g_27214d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83641_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-210 4107,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac5b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-156 4063,-166 4106,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="18437@1" ObjectIDZND0="18453@x" ObjectIDZND1="18435@x" Pin0InfoVect0LinkObjId="CB-CX_DLC.CX_DLC_1C_0" Pin0InfoVect1LinkObjId="SW-83641_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83643_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-156 4063,-166 4106,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245d530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-142 4107,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18453@1" ObjectIDZND0="18437@x" ObjectIDZND1="18435@x" Pin0InfoVect0LinkObjId="SW-83643_0" Pin0InfoVect1LinkObjId="SW-83641_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_DLC.CX_DLC_1C_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-142 4107,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_209ed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-166 4107,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="18437@x" ObjectIDND1="18453@x" ObjectIDZND0="18435@0" Pin0InfoVect0LinkObjId="SW-83641_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-83643_0" Pin1InfoVect1LinkObjId="CB-CX_DLC.CX_DLC_1C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-166 4107,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29fb0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-120 4063,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18437@0" ObjectIDZND0="g_2254310@0" Pin0InfoVect0LinkObjId="g_2254310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83643_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-120 4063,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16da830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-34 4107,-16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="18453@0" ObjectIDZND0="18436@1" Pin0InfoVect0LinkObjId="SW-83642_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_DLC.CX_DLC_1C_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-34 4107,-16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bfb140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,20 4107,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18436@0" ObjectIDZND0="g_293a7e0@0" Pin0InfoVect0LinkObjId="g_293a7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83642_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,20 4107,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e1230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-575 4050,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="18451@0" ObjectIDZND0="g_21b4a40@1" Pin0InfoVect0LinkObjId="g_21b4a40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-575 4050,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df76c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-520 4050,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_21b4a40@0" ObjectIDZND0="18443@1" Pin0InfoVect0LinkObjId="SW-83666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b4a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-520 4050,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d548f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-991 3929,-1000 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18439@1" ObjectIDZND0="g_1ec6950@0" ObjectIDZND1="g_21e3100@0" Pin0InfoVect0LinkObjId="g_1ec6950_0" Pin0InfoVect1LinkObjId="g_21e3100_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83653_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-991 3929,-1000 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_189f670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1000 3929,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_1ec6950@0" ObjectIDND1="18439@x" ObjectIDZND0="g_21e3100@0" Pin0InfoVect0LinkObjId="g_21e3100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ec6950_0" Pin1InfoVect1LinkObjId="SW-83653_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1000 3929,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ab6490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-822 4050,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="18442@1" ObjectIDZND0="g_2228c20@0" Pin0InfoVect0LinkObjId="g_2228c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-822 4050,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff7c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-729 4051,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="18442@0" ObjectIDZND0="g_18a8800@0" ObjectIDZND1="g_2939b50@0" ObjectIDZND2="g_18a8800@0" Pin0InfoVect0LinkObjId="g_18a8800_0" Pin0InfoVect1LinkObjId="g_2939b50_0" Pin0InfoVect2LinkObjId="g_18a8800_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-729 4051,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ab3650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-988 4203,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b60b50@0" ObjectIDND1="g_2132c60@0" ObjectIDZND0="18447@0" Pin0InfoVect0LinkObjId="SW-83668_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b60b50_0" Pin1InfoVect1LinkObjId="g_2132c60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-988 4203,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2de41d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4241,-1019 4241,-988 4203,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1b60b50@0" ObjectIDZND0="18447@x" ObjectIDZND1="g_2132c60@0" Pin0InfoVect0LinkObjId="SW-83668_0" Pin0InfoVect1LinkObjId="g_2132c60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b60b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4241,-1019 4241,-988 4203,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bd310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-988 4203,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18447@x" ObjectIDND1="g_1b60b50@0" ObjectIDZND0="g_2132c60@1" Pin0InfoVect0LinkObjId="g_2132c60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-83668_0" Pin1InfoVect1LinkObjId="g_1b60b50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-988 4203,-1002 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-83550" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3435.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18412" ObjectName="DYN-CX_DLC"/>
     <cge:Meas_Ref ObjectId="83550"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247fa00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 502.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202a4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 487.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2921af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 472.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_265d870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 981.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29ad550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 966.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_265d470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 951.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abe2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 943.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26945e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 958.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac2d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 915.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2565d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 929.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16f0ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 477.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266a820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 492.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb56a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3535.000000 449.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a4fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 463.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25756b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4140.000000 -37.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d9c6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4129.000000 -52.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e5a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 -67.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d14b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -31.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb23f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.000000 -46.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26df1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 -61.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2003b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -31.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20710d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 -46.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2d080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3616.000000 -61.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 566.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d9d7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 581.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfde10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 805.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_295a9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4115.000000 790.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ddb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4140.000000 775.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_24e4100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -1059.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cece80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 -208.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b695f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -211.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_b409f30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -211.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2254310" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -88.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_293a7e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.000000 53.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c304e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3514.000000 -400.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e07a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4228.000000 -951.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_189da90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3956.000000 -617.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2023cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3889.500000 -958.000000) translate(0,12)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b24ec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4061.000000 -472.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0f040" transform="matrix(1.000000 0.000000 0.000000 1.000000 4065.000000 -784.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b0cfb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4358.000000 -355.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7e310" transform="matrix(1.000000 0.000000 0.000000 1.000000 3645.000000 -879.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21aa370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c26240" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c26240" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c26240" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c26240" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c26240" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c26240" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c26240" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_29bc7d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -1167.500000) translate(0,16)">大罗岔变电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f9bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4096.000000 -645.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f9bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4096.000000 -645.000000) translate(0,27)">SZ11-10000/35/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f9bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4096.000000 -645.000000) translate(0,42)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f9bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4096.000000 -645.000000) translate(0,57)">U%=7.5  Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d48130" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4315.000000 -164.000000) translate(0,15)">10kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23941d0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4125.000000 11.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2293ed0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3588.000000 4.000000) translate(0,15)">10kV雷依线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202b460" transform="matrix(1.000000 0.000000 0.000000 1.000000 4181.000000 -1099.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270a00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3742.000000 -632.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23fb860" transform="matrix(1.000000 0.000000 0.000000 1.000000 3811.500000 -1115.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2024630" transform="matrix(1.000000 0.000000 0.000000 1.000000 3884.500000 -1095.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29bbd30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3945.500000 -1219.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29bbd30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3945.500000 -1219.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29bbd30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3945.500000 -1219.000000) translate(0,42)">铁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29bbd30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3945.500000 -1219.000000) translate(0,57)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29bbd30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3945.500000 -1219.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dc330" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.500000 -348.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29a2390" transform="matrix(1.000000 0.000000 0.000000 1.000000 3552.500000 -274.000000) translate(0,12)">09167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2682030" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.500000 -193.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c02d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3902.500000 -197.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20732b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3905.500000 -350.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2117c70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3802.500000 -264.000000) translate(0,12)">09267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad4f60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4126.500000 -353.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c0cfd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4022.500000 -264.000000) translate(0,12)">09360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25988c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3835.000000 4.000000) translate(0,15)">10kV明太线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23bc140" transform="matrix(1.000000 0.000000 0.000000 1.000000 4006.500000 -143.000000) translate(0,12)">09367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6ab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4050.500000 2.000000) translate(0,12)">09300</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261ba20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4117.500000 -195.000000) translate(0,12)">0936</text>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DLC.CX_DLC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3537,-408 4476,-408 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18427" ObjectName="BS-CX_DLC.CX_DLC_9IM"/>
    <cge:TPSR_Ref TObjectID="18427"/></metadata>
   <polyline fill="none" opacity="0" points="3537,-408 4476,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DLC.CX_DLC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-886 4285,-886 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18426" ObjectName="BS-CX_DLC.CX_DLC_3IM"/>
    <cge:TPSR_Ref TObjectID="18426"/></metadata>
   <polyline fill="none" opacity="0" points="3638,-886 4285,-886 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="18427" cx="4346" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18426" cx="4203" cy="-886" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18426" cx="3790" cy="-886" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18426" cx="4050" cy="-886" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18426" cx="3929" cy="-886" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18427" cx="4050" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18427" cx="3637" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18427" cx="3887" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18427" cx="4107" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DLC"/>
</svg>