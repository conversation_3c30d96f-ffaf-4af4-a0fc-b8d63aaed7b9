<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-306" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-270 -1201 4003 2093">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_2121500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="reactance:shape1">
    <polyline points="13,13 11,13 9,14 8,14 6,15 5,16 3,17 2,19 1,21 1,22 0,24 0,26 0,28 1,30 1,31 2,33 3,34 5,36 6,37 8,38 9,38 11,39 13,39 15,39 17,38 18,38 20,37 21,36 23,34 24,33 25,31 25,30 26,28 26,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="13" x2="25" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="47" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="13" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape32_0">
    <ellipse cx="59" cy="95" fillStyle="0" rx="25.5" ry="26.5" stroke-width="0.524983"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="30" x2="13" y1="73" y2="63"/>
    <ellipse cx="26" cy="62" fillStyle="0" rx="25.5" ry="26.5" stroke-width="0.535277"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="13" x2="30" y1="63" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="30" x2="30" y1="73" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="60" x2="51" y1="95" y2="101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="67" x2="60" y1="102" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="60" x2="60" y1="95" y2="85"/>
   </symbol>
   <symbol id="transformer:shape32_1">
    <ellipse cx="60" cy="32" fillStyle="0" rx="25.5" ry="26.5" stroke-width="0.535277"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="59" x2="50" y1="33" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="66" x2="59" y1="40" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="59" x2="59" y1="33" y2="23"/>
   </symbol>
   <symbol id="transformer:shape32-2">
    <ellipse cx="92" cy="64" fillStyle="0" rx="25.5" ry="26" stroke-width="0.535277"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="93" x2="93" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="100" x2="93" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="93" x2="87" y1="66" y2="58"/>
   </symbol>
   <symbol id="transformer2:shape59_0">
    <circle cx="24" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="26,10 20,23 33,23 26,10 26,11 26,10 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="30" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="20" y1="57" y2="52"/>
    <circle cx="24" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="42" y2="0"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="25,57 58,57 58,27 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="25" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="58" y1="23" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="63" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="61" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="59" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="29" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="29" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="21" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="21" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="76" y2="76"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="25,76 22,76 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="25,76 22,76 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="29,85 31,83 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="29,85 31,83 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="23" y1="85" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="23" y1="85" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="81" y2="81"/>
   </symbol>
   <symbol id="transformer2:shape59_1">
    <circle cx="24" cy="79" fillStyle="0" r="15" stroke-width="1"/>
   </symbol>
   <symbol id="transformer2:shape97_0">
    <circle cx="17" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="12,78 19,78 16,85 12,78 "/>
   </symbol>
   <symbol id="transformer2:shape97_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape147">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="34" y1="46" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="33" y1="35" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="33" x2="40" y1="40" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="33" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="33" x2="40" y1="16" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="34" y1="22" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="14" y1="46" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="13" y1="34" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="13" x2="20" y1="40" y2="39"/>
    <ellipse cx="34" cy="40" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="27" y2="27"/>
    <ellipse cx="33" cy="15" fillStyle="0" rx="12" ry="14.5" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="3" y2="3"/>
    <ellipse cx="50" cy="27" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="13" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="13" x2="20" y1="16" y2="15"/>
    <ellipse cx="14" cy="39" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="14" y1="22" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="56" x2="56" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="49" x2="56" y1="30" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="49" x2="56" y1="25" y2="22"/>
    <ellipse cx="13" cy="16" fillStyle="0" rx="12" ry="14.5" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="3" y2="3"/>
   </symbol>
   <symbol id="voltageTransformer:shape99">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="34" x2="34" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="24" y1="30" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="30" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="24" x2="24" y1="32" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="39" x2="39" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="36" x2="39" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="23" y2="26"/>
    <circle cx="24" cy="32" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="19" y2="19"/>
    <circle cx="39" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="19" y2="19"/>
    <circle cx="34" cy="32" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="23" y2="26"/>
    <circle cx="19" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="31" x2="37" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="32" x2="31" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="36" x2="37" y1="31" y2="34"/>
    <circle cx="29" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="41" x2="41" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="41" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="17" x2="17" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="24" x2="24" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="24" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="8" y2="8"/>
   </symbol>
   <symbol id="voltageTransformer:shape148">
    <circle cx="27" cy="55" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="62" x2="68" y1="3" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="66" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="69" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="65" x2="65" y1="5" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="65" x2="40" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="27" x2="32" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="27" x2="27" y1="52" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="22" x2="27" y1="62" y2="57"/>
    <circle cx="39" cy="35" fillStyle="0" r="15" stroke-width="0.306122"/>
    <circle cx="15" cy="35" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="14" x2="19" y1="33" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="14" x2="14" y1="28" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="9" x2="14" y1="38" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="40" x2="45" y1="34" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="40" x2="40" y1="29" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="35" x2="40" y1="39" y2="34"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_28b00b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28b10a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28b1b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28b2d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2936b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29375e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29381a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2938c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22a3230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22a3230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_293be90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_293be90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_293db80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_293db80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_293eb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2940770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2941400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2942270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29429c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2944240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2944ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2945760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2945f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2947000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2947980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2948470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2948e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_294a450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_294ae70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_294c010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_294cca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_295b0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29531d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2954980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_294ee90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="2103" width="4013" x="-275" y="-1206"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da1260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 77.000000 161.666667) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 72.000000 148.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dac480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 80.000000 203.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2119c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 80.000000 175.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8ff20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 80.000000 189.333333) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210c8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3245.000000 129.666667) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8f640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3240.000000 116.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8f7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3248.000000 171.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8f920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3248.000000 143.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2077ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3248.000000 157.333333) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f29a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1018.000000 211.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2a580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1007.000000 196.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2ad90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.000000 181.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 667.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2c660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.000000 652.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2c8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.000000 637.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2d740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1243.000000 613.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2d970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1232.000000 598.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2dbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1257.000000 583.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2dee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 -412.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2e140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -427.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2e380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 -442.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2e6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 495.000000 -411.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2e910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 -426.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2eb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 509.000000 -441.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2ee80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 689.000000 -412.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2f0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 678.000000 -427.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2f320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 -442.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f30780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 -483.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f309e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1085.000000 -498.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f30c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1110.000000 -513.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f31a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1544.000000 -24.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f31c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1533.000000 -39.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f31ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1558.000000 -54.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f32820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1807.000000 -393.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f32a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1796.000000 -408.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f32c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1821.000000 -423.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f334d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1991.000000 -395.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f33730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1980.000000 -410.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f33970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2005.000000 -425.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f34290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2242.000000 -468.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f344f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2231.000000 -483.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f34730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2256.000000 -498.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f35640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2724.000000 -366.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f35830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2713.000000 -381.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f35a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2738.000000 -396.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f36350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2958.000000 -366.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f36580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2947.000000 -381.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f36790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2972.000000 -396.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f37090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3161.000000 -370.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f372c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3150.000000 -385.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f37500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3175.000000 -400.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f3bbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1486.000000 -423.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f3c050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 -438.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f3fab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2642.000000 -494.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f3ff80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2667.000000 -509.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-268841">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 932.000000 -171.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43526" ObjectName="SW-CX_LL.CX_LL_301BK"/>
     <cge:Meas_Ref ObjectId="268841"/>
    <cge:TPSR_Ref TObjectID="43526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268875">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1397.000000 28.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43550" ObjectName="SW-CX_LL.CX_LL_355BK"/>
     <cge:Meas_Ref ObjectId="268875"/>
    <cge:TPSR_Ref TObjectID="43550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268869">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1171.000000 28.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43545" ObjectName="SW-CX_LL.CX_LL_354BK"/>
     <cge:Meas_Ref ObjectId="268869"/>
    <cge:TPSR_Ref TObjectID="43545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268817">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 930.000000 -608.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43510" ObjectName="SW-CX_LL.CX_LL_291BK"/>
     <cge:Meas_Ref ObjectId="268817"/>
    <cge:TPSR_Ref TObjectID="43510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268829">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1112.000000 -566.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43517" ObjectName="SW-CX_LL.CX_LL_191BK"/>
     <cge:Meas_Ref ObjectId="268829"/>
    <cge:TPSR_Ref TObjectID="43517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268864">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 31.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43541" ObjectName="SW-CX_LL.CX_LL_353BK"/>
     <cge:Meas_Ref ObjectId="268864"/>
    <cge:TPSR_Ref TObjectID="43541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268859">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 30.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43537" ObjectName="SW-CX_LL.CX_LL_352BK"/>
     <cge:Meas_Ref ObjectId="268859"/>
    <cge:TPSR_Ref TObjectID="43537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268854">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 350.000000 31.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43533" ObjectName="SW-CX_LL.CX_LL_351BK"/>
     <cge:Meas_Ref ObjectId="268854"/>
    <cge:TPSR_Ref TObjectID="43533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268922">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 35.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43591" ObjectName="SW-CX_LL.CX_LL_312BK"/>
     <cge:Meas_Ref ObjectId="268922"/>
    <cge:TPSR_Ref TObjectID="43591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268883">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1895.000000 31.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43558" ObjectName="SW-CX_LL.CX_LL_361BK"/>
     <cge:Meas_Ref ObjectId="268883"/>
    <cge:TPSR_Ref TObjectID="43558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268888">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2040.000000 31.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43562" ObjectName="SW-CX_LL.CX_LL_362BK"/>
     <cge:Meas_Ref ObjectId="268888"/>
    <cge:TPSR_Ref TObjectID="43562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268899">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2531.000000 25.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43571" ObjectName="SW-CX_LL.CX_LL_364BK"/>
     <cge:Meas_Ref ObjectId="268899"/>
    <cge:TPSR_Ref TObjectID="43571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268893">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2328.000000 29.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43566" ObjectName="SW-CX_LL.CX_LL_363BK"/>
     <cge:Meas_Ref ObjectId="268893"/>
    <cge:TPSR_Ref TObjectID="43566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268912">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3002.000000 30.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43583" ObjectName="SW-CX_LL.CX_LL_367BK"/>
     <cge:Meas_Ref ObjectId="268912"/>
    <cge:TPSR_Ref TObjectID="43583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268917">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3197.000000 30.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43587" ObjectName="SW-CX_LL.CX_LL_368BK"/>
     <cge:Meas_Ref ObjectId="268917"/>
    <cge:TPSR_Ref TObjectID="43587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268907">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2777.000000 30.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43579" ObjectName="SW-CX_LL.CX_LL_366BK"/>
     <cge:Meas_Ref ObjectId="268907"/>
    <cge:TPSR_Ref TObjectID="43579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268878">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1397.000000 429.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43554" ObjectName="SW-CX_LL.CX_LL_356BK"/>
     <cge:Meas_Ref ObjectId="268878"/>
    <cge:TPSR_Ref TObjectID="43554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268902">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2531.000000 429.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43575" ObjectName="SW-CX_LL.CX_LL_365BK"/>
     <cge:Meas_Ref ObjectId="268902"/>
    <cge:TPSR_Ref TObjectID="43575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2256.000000 -790.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2256.000000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_LL.CX_LL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="19767"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 -390.000000)" xlink:href="#transformer:shape32_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="19769"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 -390.000000)" xlink:href="#transformer:shape32_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="19771"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 -390.000000)" xlink:href="#transformer:shape32-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="43595" ObjectName="TF-CX_LL.CX_LL_1T"/>
    <cge:TPSR_Ref TObjectID="43595"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_LL.CX_LL_351_P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 354.000000 294.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48815" ObjectName="SM-CX_LL.CX_LL_351_P1"/>
    <cge:TPSR_Ref TObjectID="48815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LL.CX_LL_352_P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 293.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48816" ObjectName="SM-CX_LL.CX_LL_352_P2"/>
    <cge:TPSR_Ref TObjectID="48816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LL.CX_LL_353_P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 294.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48817" ObjectName="SM-CX_LL.CX_LL_353_P3"/>
    <cge:TPSR_Ref TObjectID="48817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LL.CX_LL_361_P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1899.000000 294.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48818" ObjectName="SM-CX_LL.CX_LL_361_P4"/>
    <cge:TPSR_Ref TObjectID="48818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LL.CX_LL_362_P5">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2044.000000 294.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48819" ObjectName="SM-CX_LL.CX_LL_362_P5"/>
    <cge:TPSR_Ref TObjectID="48819"/></metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1419.000000 538.000000)" xlink:href="#reactance:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2553.000000 535.000000)" xlink:href="#reactance:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 364.000000)" xlink:href="#transformer2:shape59_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 364.000000)" xlink:href="#transformer2:shape59_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.000000 -396.000000)" xlink:href="#transformer2:shape97_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.000000 -396.000000)" xlink:href="#transformer2:shape97_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2078030">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 864.000000 -118.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c5d20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 864.000000 -270.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20d47f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1328.000000 80.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2125410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1401.000000 247.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2125d10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1451.000000 357.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20085d0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1474.000000 432.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2120f60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 596.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20f3d80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1103.000000 80.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c7270">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1175.000000 239.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_209e500">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1133.000000 380.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ce9a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 974.000000 93.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20cf590">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.000000 90.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2111f60">
    <use class="BV-110KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 951.000000 -306.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2099d00">
    <use class="BV-0KV" transform="matrix(0.519880 -0.000000 0.000000 0.500000 828.888840 -467.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_209ad30">
    <use class="BV-0KV" transform="matrix(0.519880 -0.000000 0.000000 0.500000 828.524094 -457.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_209b920">
    <use class="BV-0KV" transform="matrix(0.519880 -0.000000 0.000000 0.500000 828.888840 -449.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e9ff0">
    <use class="BV-220KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 787.000000 -392.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ecb60">
    <use class="BV-110KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1072.000000 -379.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b9bd0">
    <use class="BV-220KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1023.000000 -850.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20727c0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1204.000000 -848.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2067190">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 684.000000 83.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_201f2d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 242.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b0100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 494.000000 82.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20939b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 241.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_201af10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 282.000000 83.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_207e9b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 354.000000 242.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2063550">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1827.000000 83.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f71850">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1899.000000 242.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fafdb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1972.000000 83.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fdc410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2044.000000 242.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe1bd0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2222.000000 93.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe2980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2158.000000 90.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe4940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 2463.000000 77.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe9560">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2535.000000 244.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fea1e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2586.000000 354.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb5c70">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2609.000000 429.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb7eb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2523.000000 593.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc0fd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 2260.000000 81.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc5bf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2332.000000 240.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc9530">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2290.000000 384.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_200bd10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2320.000000 304.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2015300">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 2934.000000 82.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2035990">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3006.000000 241.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fcc4f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3129.000000 82.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd1110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3201.000000 241.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f914e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 2709.000000 82.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f92690">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2781.000000 241.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fa3020">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1424.000000 93.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2186860">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2349.000000 -836.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21883c0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2349.000000 -759.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0b1c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 2183.000000 -633.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0eb20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 2183.000000 -565.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -179.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-268756" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -205.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43507"/>
     <cge:Term_Ref ObjectID="19590"/>
    <cge:TPSR_Ref TObjectID="43507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-268757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -205.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43507"/>
     <cge:Term_Ref ObjectID="19590"/>
    <cge:TPSR_Ref TObjectID="43507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-268758" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -205.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268758" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43507"/>
     <cge:Term_Ref ObjectID="19590"/>
    <cge:TPSR_Ref TObjectID="43507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-268759" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -205.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268759" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43507"/>
     <cge:Term_Ref ObjectID="19590"/>
    <cge:TPSR_Ref TObjectID="43507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-268753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -205.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43507"/>
     <cge:Term_Ref ObjectID="19590"/>
    <cge:TPSR_Ref TObjectID="43507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-268764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3305.000000 -173.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43508"/>
     <cge:Term_Ref ObjectID="19591"/>
    <cge:TPSR_Ref TObjectID="43508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-268765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3305.000000 -173.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43508"/>
     <cge:Term_Ref ObjectID="19591"/>
    <cge:TPSR_Ref TObjectID="43508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-268766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3305.000000 -173.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43508"/>
     <cge:Term_Ref ObjectID="19591"/>
    <cge:TPSR_Ref TObjectID="43508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-268767" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3305.000000 -173.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43508"/>
     <cge:Term_Ref ObjectID="19591"/>
    <cge:TPSR_Ref TObjectID="43508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-268761" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3305.000000 -173.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268761" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43508"/>
     <cge:Term_Ref ObjectID="19591"/>
    <cge:TPSR_Ref TObjectID="43508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-268751" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 -365.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268751" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43595"/>
     <cge:Term_Ref ObjectID="19768"/>
    <cge:TPSR_Ref TObjectID="43595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1072.000000 -210.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43526"/>
     <cge:Term_Ref ObjectID="19626"/>
    <cge:TPSR_Ref TObjectID="43526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268742" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1072.000000 -210.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268742" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43526"/>
     <cge:Term_Ref ObjectID="19626"/>
    <cge:TPSR_Ref TObjectID="43526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1072.000000 -210.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43526"/>
     <cge:Term_Ref ObjectID="19626"/>
    <cge:TPSR_Ref TObjectID="43526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268719" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 764.000000 -669.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43510"/>
     <cge:Term_Ref ObjectID="19594"/>
    <cge:TPSR_Ref TObjectID="43510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268720" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 764.000000 -669.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268720" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43510"/>
     <cge:Term_Ref ObjectID="19594"/>
    <cge:TPSR_Ref TObjectID="43510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268721" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 764.000000 -669.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43510"/>
     <cge:Term_Ref ObjectID="19594"/>
    <cge:TPSR_Ref TObjectID="43510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268730" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 -613.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268730" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43517"/>
     <cge:Term_Ref ObjectID="19608"/>
    <cge:TPSR_Ref TObjectID="43517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268731" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 -613.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268731" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43517"/>
     <cge:Term_Ref ObjectID="19608"/>
    <cge:TPSR_Ref TObjectID="43517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268732" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 -613.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43517"/>
     <cge:Term_Ref ObjectID="19608"/>
    <cge:TPSR_Ref TObjectID="43517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268768" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 411.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268768" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43533"/>
     <cge:Term_Ref ObjectID="19640"/>
    <cge:TPSR_Ref TObjectID="43533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 411.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43533"/>
     <cge:Term_Ref ObjectID="19640"/>
    <cge:TPSR_Ref TObjectID="43533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 411.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43533"/>
     <cge:Term_Ref ObjectID="19640"/>
    <cge:TPSR_Ref TObjectID="43533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268772" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 412.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43537"/>
     <cge:Term_Ref ObjectID="19648"/>
    <cge:TPSR_Ref TObjectID="43537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 412.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43537"/>
     <cge:Term_Ref ObjectID="19648"/>
    <cge:TPSR_Ref TObjectID="43537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 412.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43537"/>
     <cge:Term_Ref ObjectID="19648"/>
    <cge:TPSR_Ref TObjectID="43537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 741.000000 412.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43541"/>
     <cge:Term_Ref ObjectID="19656"/>
    <cge:TPSR_Ref TObjectID="43541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 741.000000 412.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43541"/>
     <cge:Term_Ref ObjectID="19656"/>
    <cge:TPSR_Ref TObjectID="43541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 741.000000 412.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43541"/>
     <cge:Term_Ref ObjectID="19656"/>
    <cge:TPSR_Ref TObjectID="43541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268780" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1150.000000 482.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268780" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43545"/>
     <cge:Term_Ref ObjectID="19664"/>
    <cge:TPSR_Ref TObjectID="43545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1150.000000 482.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43545"/>
     <cge:Term_Ref ObjectID="19664"/>
    <cge:TPSR_Ref TObjectID="43545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1150.000000 482.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43545"/>
     <cge:Term_Ref ObjectID="19664"/>
    <cge:TPSR_Ref TObjectID="43545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1559.000000 426.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43550"/>
     <cge:Term_Ref ObjectID="19674"/>
    <cge:TPSR_Ref TObjectID="43550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1559.000000 426.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43550"/>
     <cge:Term_Ref ObjectID="19674"/>
    <cge:TPSR_Ref TObjectID="43550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268812" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1602.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43591"/>
     <cge:Term_Ref ObjectID="19758"/>
    <cge:TPSR_Ref TObjectID="43591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268813" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1602.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268813" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43591"/>
     <cge:Term_Ref ObjectID="19758"/>
    <cge:TPSR_Ref TObjectID="43591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1602.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43591"/>
     <cge:Term_Ref ObjectID="19758"/>
    <cge:TPSR_Ref TObjectID="43591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 393.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43558"/>
     <cge:Term_Ref ObjectID="19690"/>
    <cge:TPSR_Ref TObjectID="43558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 393.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43558"/>
     <cge:Term_Ref ObjectID="19690"/>
    <cge:TPSR_Ref TObjectID="43558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 393.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43558"/>
     <cge:Term_Ref ObjectID="19690"/>
    <cge:TPSR_Ref TObjectID="43558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2042.000000 395.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43562"/>
     <cge:Term_Ref ObjectID="19698"/>
    <cge:TPSR_Ref TObjectID="43562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268791" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2042.000000 395.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268791" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43562"/>
     <cge:Term_Ref ObjectID="19698"/>
    <cge:TPSR_Ref TObjectID="43562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268792" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2042.000000 395.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268792" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43562"/>
     <cge:Term_Ref ObjectID="19698"/>
    <cge:TPSR_Ref TObjectID="43562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268794" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2295.000000 467.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43566"/>
     <cge:Term_Ref ObjectID="19706"/>
    <cge:TPSR_Ref TObjectID="43566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268795" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2295.000000 467.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43566"/>
     <cge:Term_Ref ObjectID="19706"/>
    <cge:TPSR_Ref TObjectID="43566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268796" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2295.000000 467.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43566"/>
     <cge:Term_Ref ObjectID="19706"/>
    <cge:TPSR_Ref TObjectID="43566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2714.000000 494.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43571"/>
     <cge:Term_Ref ObjectID="19716"/>
    <cge:TPSR_Ref TObjectID="43571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268799" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2714.000000 494.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268799" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43571"/>
     <cge:Term_Ref ObjectID="19716"/>
    <cge:TPSR_Ref TObjectID="43571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268800" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2774.000000 366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43579"/>
     <cge:Term_Ref ObjectID="19734"/>
    <cge:TPSR_Ref TObjectID="43579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2774.000000 366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43579"/>
     <cge:Term_Ref ObjectID="19734"/>
    <cge:TPSR_Ref TObjectID="43579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2774.000000 366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43579"/>
     <cge:Term_Ref ObjectID="19734"/>
    <cge:TPSR_Ref TObjectID="43579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268804" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3009.000000 366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268804" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43583"/>
     <cge:Term_Ref ObjectID="19742"/>
    <cge:TPSR_Ref TObjectID="43583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268805" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3009.000000 366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43583"/>
     <cge:Term_Ref ObjectID="19742"/>
    <cge:TPSR_Ref TObjectID="43583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268806" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3009.000000 366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268806" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43583"/>
     <cge:Term_Ref ObjectID="19742"/>
    <cge:TPSR_Ref TObjectID="43583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-268808" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3214.000000 370.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268808" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43587"/>
     <cge:Term_Ref ObjectID="19750"/>
    <cge:TPSR_Ref TObjectID="43587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-268809" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3214.000000 370.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268809" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43587"/>
     <cge:Term_Ref ObjectID="19750"/>
    <cge:TPSR_Ref TObjectID="43587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-268810" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3214.000000 370.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="268810" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43587"/>
     <cge:Term_Ref ObjectID="19750"/>
    <cge:TPSR_Ref TObjectID="43587"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="164" x="-175" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="164" x="-175" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-224" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-224" y="-957"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="164" x="-175" y="-940"/></g>
   <g href="cx_索引_接线图_省地共调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-224" y="-957"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="3731" x2="3731" y1="-323" y2="334"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1572" x2="2229" y1="891" y2="891"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1376" x2="2032" y1="-1200" y2="-1200"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20cfdb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.000000 160.000000)" xlink:href="#voltageTransformer:shape147"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2025fb0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 850.000000 -826.000000)" xlink:href="#voltageTransformer:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2107980">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1206.000000 -786.000000)" xlink:href="#voltageTransformer:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe3280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2132.000000 160.000000)" xlink:href="#voltageTransformer:shape147"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fa0a70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2667.000000 242.000000)" xlink:href="#voltageTransformer:shape148"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.000000 -890.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3006.000000 293.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3201.000000 293.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2781.000000 293.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_20781e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="921,-111 941,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_2078030@0" ObjectIDZND0="43507@0" ObjectIDZND1="43527@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-268842_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2078030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="921,-111 941,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20783d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="941,-72 941,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43507@0" ObjectIDZND0="g_2078030@0" ObjectIDZND1="43527@x" Pin0InfoVect0LinkObjId="g_2078030_0" Pin0InfoVect1LinkObjId="SW-268842_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20781e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="941,-72 941,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c5750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="941,-111 941,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="g_2078030@0" ObjectIDND1="43507@0" ObjectIDZND0="43527@0" Pin0InfoVect0LinkObjId="SW-268842_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2078030_0" Pin1InfoVect1LinkObjId="g_20781e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="941,-111 941,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c5940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="941,-164 941,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43527@1" ObjectIDZND0="43526@0" Pin0InfoVect0LinkObjId="SW-268841_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268842_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="941,-164 941,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c5b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="941,-206 941,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43526@1" ObjectIDZND0="43528@1" Pin0InfoVect0LinkObjId="SW-268842_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268841_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="941,-206 941,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206b5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="921,-263 941,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="g_20c5d20@0" ObjectIDZND0="43528@x" ObjectIDZND1="43595@x" Pin0InfoVect0LinkObjId="SW-268842_0" Pin0InfoVect1LinkObjId="g_206b990_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c5d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="921,-263 941,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206b7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="941,-237 941,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="43528@0" ObjectIDZND0="g_20c5d20@0" ObjectIDZND1="43595@x" Pin0InfoVect0LinkObjId="g_20c5d20_0" Pin0InfoVect1LinkObjId="g_206b990_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268842_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="941,-237 941,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206b990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="941,-263 941,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="g_20c5d20@0" ObjectIDND1="43528@x" ObjectIDZND0="43595@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20c5d20_0" Pin1InfoVect1LinkObjId="SW-268842_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="941,-263 941,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ab390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,-72 1406,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43507@0" ObjectIDZND0="43551@0" Pin0InfoVect0LinkObjId="SW-268876_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20781e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,-72 1406,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ab580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,-20 1406,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43551@1" ObjectIDZND0="43550@1" Pin0InfoVect0LinkObjId="SW-268875_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268876_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,-20 1406,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ab770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,19 1406,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43550@0" ObjectIDZND0="43552@1" Pin0InfoVect0LinkObjId="SW-268876_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,19 1406,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d5200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,87 1406,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_20d47f0@0" ObjectIDZND0="43552@x" ObjectIDZND1="43553@x" ObjectIDZND2="g_2125410@0" Pin0InfoVect0LinkObjId="SW-268876_0" Pin0InfoVect1LinkObjId="SW-268877_0" Pin0InfoVect2LinkObjId="g_2125410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20d47f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1385,87 1406,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d53f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,53 1406,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43552@0" ObjectIDZND0="g_20d47f0@0" ObjectIDZND1="43553@x" ObjectIDZND2="g_2125410@0" Pin0InfoVect0LinkObjId="g_20d47f0_0" Pin0InfoVect1LinkObjId="SW-268877_0" Pin0InfoVect2LinkObjId="g_2125410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268876_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1406,53 1406,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2124690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,145 1385,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_20d47f0@0" ObjectIDND1="43552@x" ObjectIDND2="g_2125410@0" ObjectIDZND0="43553@0" Pin0InfoVect0LinkObjId="SW-268877_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20d47f0_0" Pin1InfoVect1LinkObjId="SW-268876_0" Pin1InfoVect2LinkObjId="g_2125410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,145 1385,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2124880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,145 1337,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43553@1" ObjectIDZND0="g_2124a70@0" Pin0InfoVect0LinkObjId="g_2124a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268877_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1349,145 1337,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2125220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,145 1406,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43553@x" ObjectIDND1="g_2125410@0" ObjectIDZND0="g_20d47f0@0" ObjectIDZND1="43552@x" Pin0InfoVect0LinkObjId="g_20d47f0_0" Pin0InfoVect1LinkObjId="SW-268876_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268877_0" Pin1InfoVect1LinkObjId="g_2125410_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1406,145 1406,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2125b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,189 1406,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2125410@1" ObjectIDZND0="43553@x" ObjectIDZND1="g_20d47f0@0" ObjectIDZND2="43552@x" Pin0InfoVect0LinkObjId="SW-268877_0" Pin0InfoVect1LinkObjId="g_20d47f0_0" Pin0InfoVect2LinkObjId="SW-268876_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2125410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1406,189 1406,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2126720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,270 1444,270 1444,299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2125410@0" ObjectIDND1="43555@x" ObjectIDZND0="g_2125d10@0" Pin0InfoVect0LinkObjId="g_2125d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2125410_0" Pin1InfoVect1LinkObjId="SW-268879_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,270 1444,270 1444,299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2126910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,270 1406,242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2125d10@0" ObjectIDND1="43555@x" ObjectIDZND0="g_2125410@0" Pin0InfoVect0LinkObjId="g_2125410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2125d10_0" Pin1InfoVect1LinkObjId="SW-268879_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,270 1406,242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2005ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,296 1406,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43555@1" ObjectIDZND0="g_2125d10@0" ObjectIDZND1="g_2125410@0" Pin0InfoVect0LinkObjId="g_2125d10_0" Pin0InfoVect1LinkObjId="g_2125410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268879_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1406,296 1406,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2007c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,354 1337,354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43557@1" ObjectIDZND0="g_2007e20@0" Pin0InfoVect0LinkObjId="g_2007e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1349,354 1337,354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2120380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1467,427 1467,459 1406,459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="reactance" EndDevType1="breaker" ObjectIDND0="g_20085d0@1" ObjectIDZND0="0@x" ObjectIDZND1="43554@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-268878_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20085d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1467,427 1467,459 1406,459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2120570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,459 1406,420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="reactance" EndDevType0="breaker" ObjectIDND0="g_20085d0@0" ObjectIDND1="0@x" ObjectIDZND0="43554@0" Pin0InfoVect0LinkObjId="SW-268878_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20085d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,459 1406,420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2120b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,566 1406,533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="reactance" ObjectIDND0="g_2120f60@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2120f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,566 1406,533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2120d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,491 1406,459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="g_20085d0@0" ObjectIDZND1="43554@x" Pin0InfoVect0LinkObjId="g_20085d0_0" Pin0InfoVect1LinkObjId="SW-268878_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1406,491 1406,459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f37b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,-72 1180,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43507@0" ObjectIDZND0="43546@0" Pin0InfoVect0LinkObjId="SW-268870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20781e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1180,-72 1180,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f39a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,-20 1180,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43546@1" ObjectIDZND0="43545@1" Pin0InfoVect0LinkObjId="SW-268869_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1180,-20 1180,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f3b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,19 1180,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43545@0" ObjectIDZND0="43547@1" Pin0InfoVect0LinkObjId="SW-268870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1180,19 1180,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f4790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1160,87 1180,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_20f3d80@0" ObjectIDZND0="43547@x" ObjectIDZND1="43548@x" ObjectIDZND2="g_20c7270@0" Pin0InfoVect0LinkObjId="SW-268870_0" Pin0InfoVect1LinkObjId="SW-268871_0" Pin0InfoVect2LinkObjId="g_20c7270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20f3d80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1160,87 1180,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f4980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,53 1180,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43547@0" ObjectIDZND0="g_20f3d80@0" ObjectIDZND1="43548@x" ObjectIDZND2="g_20c7270@0" Pin0InfoVect0LinkObjId="g_20f3d80_0" Pin0InfoVect1LinkObjId="SW-268871_0" Pin0InfoVect2LinkObjId="g_20c7270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1180,53 1180,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c62e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,145 1160,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_20f3d80@0" ObjectIDND1="43547@x" ObjectIDND2="g_20c7270@0" ObjectIDZND0="43548@0" Pin0InfoVect0LinkObjId="SW-268871_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20f3d80_0" Pin1InfoVect1LinkObjId="SW-268870_0" Pin1InfoVect2LinkObjId="g_20c7270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1180,145 1160,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c6500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,145 1112,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43548@1" ObjectIDZND0="g_20c6720@0" Pin0InfoVect0LinkObjId="g_20c6720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268871_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,145 1112,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c7050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,145 1180,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43548@x" ObjectIDND1="g_20c7270@0" ObjectIDZND0="g_20f3d80@0" ObjectIDZND1="43547@x" Pin0InfoVect0LinkObjId="g_20f3d80_0" Pin0InfoVect1LinkObjId="SW-268870_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268871_0" Pin1InfoVect1LinkObjId="g_20c7270_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1180,145 1180,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c8150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,270 1180,234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_20c7270@0" Pin0InfoVect0LinkObjId="g_20c7270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1180,270 1180,234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c8370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,181 1180,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_20c7270@1" ObjectIDZND0="43548@x" ObjectIDZND1="g_20f3d80@0" ObjectIDZND2="43547@x" Pin0InfoVect0LinkObjId="SW-268871_0" Pin0InfoVect1LinkObjId="g_20f3d80_0" Pin0InfoVect2LinkObjId="SW-268870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c7270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1180,181 1180,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209e2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1160,283 1126,283 1126,293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="43549@1" Pin0InfoVect0LinkObjId="SW-268872_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1160,283 1126,283 1126,293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209efd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1126,329 1126,341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43549@0" ObjectIDZND0="g_209e500@0" Pin0InfoVect0LinkObjId="g_209e500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1126,329 1126,341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209f1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1126,375 1126,392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_209e500@1" ObjectIDZND0="g_209f410@0" Pin0InfoVect0LinkObjId="g_209f410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_209e500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1126,375 1126,392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ce120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-72 942,-18 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43507@0" ObjectIDZND0="43529@0" Pin0InfoVect0LinkObjId="SW-268843_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20781e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,-72 942,-18 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ce340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,0 942,19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43529@1" ObjectIDZND0="g_20cf590@0" ObjectIDZND1="g_20ce9a0@0" Pin0InfoVect0LinkObjId="g_20cf590_0" Pin0InfoVect1LinkObjId="g_20ce9a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268843_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="942,0 942,19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ce560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,41 917,19 942,19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_20cf590@1" ObjectIDZND0="43529@x" ObjectIDZND1="g_20ce9a0@0" Pin0InfoVect0LinkObjId="SW-268843_0" Pin0InfoVect1LinkObjId="g_20ce9a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20cf590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="917,41 917,19 942,19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ce780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,19 967,19 967,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43529@x" ObjectIDND1="g_20cf590@0" ObjectIDZND0="g_20ce9a0@0" Pin0InfoVect0LinkObjId="g_20ce9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268843_0" Pin1InfoVect1LinkObjId="g_20cf590_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,19 967,19 967,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_210fd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,85 917,104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_20cf590@0" ObjectIDZND0="g_20cfdb0@0" Pin0InfoVect0LinkObjId="g_20cfdb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20cf590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="917,85 917,104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2111d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="974,-454 974,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="earth" ObjectIDND0="43595@x" ObjectIDZND0="g_2112b50@0" Pin0InfoVect0LinkObjId="g_2112b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206b990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="974,-454 974,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2099670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="958,-363 958,-377 990,-377 990,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2111f60@0" ObjectIDZND0="43525@1" Pin0InfoVect0LinkObjId="SW-268840_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2111f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="958,-363 958,-377 990,-377 990,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20998c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="990,-332 990,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43525@0" ObjectIDZND0="g_2098d80@0" Pin0InfoVect0LinkObjId="g_2098d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="990,-332 990,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2099ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="859,-463 911,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2099d00@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2099d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="859,-463 911,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_209a8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="859,-445 910,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_209b920@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_209b920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="859,-445 910,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_209ab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-453 895,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_209ad30@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_209ad30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="858,-453 895,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20ec550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="940,-485 768,-485 768,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="earth" ObjectIDND0="43595@x" ObjectIDZND0="g_20eabe0@0" Pin0InfoVect0LinkObjId="g_20eabe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206b990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="940,-485 768,-485 768,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20ec740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-449 794,-464 735,-464 735,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20e9ff0@0" ObjectIDZND0="43524@1" Pin0InfoVect0LinkObjId="SW-268839_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e9ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="794,-449 794,-464 735,-464 735,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20ec930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="735,-419 735,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43524@0" ObjectIDZND0="g_20eb4d0@0" Pin0InfoVect0LinkObjId="g_20eb4d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268839_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="735,-419 735,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20ece60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1079,-454 1079,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="43523@x" ObjectIDND1="43521@x" ObjectIDND2="43595@x" ObjectIDZND0="g_20ecb60@0" Pin0InfoVect0LinkObjId="g_20ecb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268835_0" Pin1InfoVect1LinkObjId="SW-268833_0" Pin1InfoVect2LinkObjId="g_206b990_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1079,-454 1079,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20ed0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1001,-454 1079,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43595@2" ObjectIDZND0="g_20ecb60@0" ObjectIDZND1="43523@x" ObjectIDZND2="43521@x" Pin0InfoVect0LinkObjId="g_20ecb60_0" Pin0InfoVect1LinkObjId="SW-268835_0" Pin0InfoVect2LinkObjId="SW-268833_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206b990_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1001,-454 1079,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fef8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-511 939,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43595@0" ObjectIDZND0="43514@x" ObjectIDZND1="43516@x" Pin0InfoVect0LinkObjId="SW-268821_0" Pin0InfoVect1LinkObjId="SW-268823_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206b990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="939,-511 939,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fefb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-546 921,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43595@x" ObjectIDND1="43514@x" ObjectIDZND0="43516@1" Pin0InfoVect0LinkObjId="SW-268823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_206b990_0" Pin1InfoVect1LinkObjId="SW-268821_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="939,-546 921,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fefda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="885,-546 866,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43516@0" ObjectIDZND0="g_1feeeb0@0" Pin0InfoVect0LinkObjId="g_1feeeb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="885,-546 866,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ff0a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-601 921,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43510@x" ObjectIDND1="43514@x" ObjectIDZND0="43515@1" Pin0InfoVect0LinkObjId="SW-268822_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268817_0" Pin1InfoVect1LinkObjId="SW-268821_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="939,-601 921,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ff0c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="885,-601 866,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43515@0" ObjectIDZND0="g_1ff0000@0" Pin0InfoVect0LinkObjId="g_1ff0000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268822_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="885,-601 866,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ff18f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="938,-658 920,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="43511@x" ObjectIDND1="43510@x" ObjectIDZND0="43513@1" Pin0InfoVect0LinkObjId="SW-268820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268818_0" Pin1InfoVect1LinkObjId="SW-268817_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="938,-658 920,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ff1b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="884,-658 865,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43513@0" ObjectIDZND0="g_1ff0ec0@0" Pin0InfoVect0LinkObjId="g_1ff0ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="884,-658 865,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20248c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="940,-719 922,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43511@x" ObjectIDND1="g_2025fb0@0" ObjectIDND2="g_20b9bd0@0" ObjectIDZND0="43512@1" Pin0InfoVect0LinkObjId="SW-268819_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268818_0" Pin1InfoVect1LinkObjId="g_2025fb0_0" Pin1InfoVect2LinkObjId="g_20b9bd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="940,-719 922,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2024b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-719 867,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43512@0" ObjectIDZND0="g_1ff1db0@0" Pin0InfoVect0LinkObjId="g_1ff1db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268819_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="886,-719 867,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2024d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-546 939,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43595@x" ObjectIDND1="43516@x" ObjectIDZND0="43514@0" Pin0InfoVect0LinkObjId="SW-268821_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_206b990_0" Pin1InfoVect1LinkObjId="SW-268823_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="939,-546 939,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2024fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-591 939,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="43514@1" ObjectIDZND0="43510@x" ObjectIDZND1="43515@x" Pin0InfoVect0LinkObjId="SW-268817_0" Pin0InfoVect1LinkObjId="SW-268822_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268821_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="939,-591 939,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2025240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-658 939,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43510@x" ObjectIDND1="43513@x" ObjectIDZND0="43511@0" Pin0InfoVect0LinkObjId="SW-268818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268817_0" Pin1InfoVect1LinkObjId="SW-268820_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="939,-658 939,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20254a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-708 939,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="43511@1" ObjectIDZND0="43512@x" ObjectIDZND1="g_2025fb0@0" ObjectIDZND2="g_20b9bd0@0" Pin0InfoVect0LinkObjId="SW-268819_0" Pin0InfoVect1LinkObjId="g_2025fb0_0" Pin0InfoVect2LinkObjId="g_20b9bd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268818_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="939,-708 939,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2025b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-601 939,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="43514@x" ObjectIDND1="43515@x" ObjectIDZND0="43510@0" Pin0InfoVect0LinkObjId="SW-268817_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268821_0" Pin1InfoVect1LinkObjId="SW-268822_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="939,-601 939,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2025d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-643 939,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43510@1" ObjectIDZND0="43511@x" ObjectIDZND1="43513@x" Pin0InfoVect0LinkObjId="SW-268818_0" Pin0InfoVect1LinkObjId="SW-268820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268817_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="939,-643 939,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20272e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="903,-818 939,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2025fb0@0" ObjectIDZND0="43512@x" ObjectIDZND1="43511@x" ObjectIDZND2="g_20b9bd0@0" Pin0InfoVect0LinkObjId="SW-268819_0" Pin0InfoVect1LinkObjId="SW-268818_0" Pin0InfoVect2LinkObjId="g_20b9bd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2025fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="903,-818 939,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2027510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-719 939,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="43512@x" ObjectIDND1="43511@x" ObjectIDZND0="g_2025fb0@0" ObjectIDZND1="g_20b9bd0@0" Pin0InfoVect0LinkObjId="g_2025fb0_0" Pin0InfoVect1LinkObjId="g_20b9bd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268819_0" Pin1InfoVect1LinkObjId="SW-268818_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="939,-719 939,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2027770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-857 965,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2025fb0@0" ObjectIDND1="43512@x" ObjectIDND2="43511@x" ObjectIDZND0="g_20b9bd0@0" Pin0InfoVect0LinkObjId="g_20b9bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2025fb0_0" Pin1InfoVect1LinkObjId="SW-268819_0" Pin1InfoVect2LinkObjId="SW-268818_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="939,-857 965,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20279d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-818 939,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2025fb0@0" ObjectIDND1="43512@x" ObjectIDND2="43511@x" ObjectIDZND0="g_20b9bd0@0" Pin0InfoVect0LinkObjId="g_20b9bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2025fb0_0" Pin1InfoVect1LinkObjId="SW-268819_0" Pin1InfoVect2LinkObjId="SW-268818_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="939,-818 939,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20b9970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-857 939,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" ObjectIDND0="g_20b9bd0@0" ObjectIDND1="g_2025fb0@0" ObjectIDND2="43512@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20b9bd0_0" Pin1InfoVect1LinkObjId="g_2025fb0_0" Pin1InfoVect2LinkObjId="SW-268819_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="939,-857 939,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20bac80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1079,-454 1121,-454 1121,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_20ecb60@0" ObjectIDND1="43595@x" ObjectIDZND0="43523@x" ObjectIDZND1="43521@x" Pin0InfoVect0LinkObjId="SW-268835_0" Pin0InfoVect1LinkObjId="SW-268833_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20ecb60_0" Pin1InfoVect1LinkObjId="g_206b990_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1079,-454 1121,-454 1121,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20bae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-492 1140,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_20ecb60@0" ObjectIDND1="43595@x" ObjectIDND2="43521@x" ObjectIDZND0="43523@0" Pin0InfoVect0LinkObjId="SW-268835_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20ecb60_0" Pin1InfoVect1LinkObjId="g_206b990_0" Pin1InfoVect2LinkObjId="SW-268833_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-492 1140,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20bb060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-492 1197,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43523@1" ObjectIDZND0="g_20bb290@0" Pin0InfoVect0LinkObjId="g_20bb290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268835_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-492 1197,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20bbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-492 1121,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_20ecb60@0" ObjectIDND1="43595@x" ObjectIDND2="43523@x" ObjectIDZND0="43521@0" Pin0InfoVect0LinkObjId="SW-268833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20ecb60_0" Pin1InfoVect1LinkObjId="g_206b990_0" Pin1InfoVect2LinkObjId="SW-268835_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-492 1121,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20bbe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-557 1140,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43517@x" ObjectIDND1="43521@x" ObjectIDZND0="43522@0" Pin0InfoVect0LinkObjId="SW-268834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268829_0" Pin1InfoVect1LinkObjId="SW-268833_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-557 1140,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20bc090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-557 1197,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43522@1" ObjectIDZND0="g_20bc2f0@0" Pin0InfoVect0LinkObjId="g_20bc2f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-557 1197,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20bcd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-542 1121,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="43521@1" ObjectIDZND0="43517@x" ObjectIDZND1="43522@x" Pin0InfoVect0LinkObjId="SW-268829_0" Pin0InfoVect1LinkObjId="SW-268834_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268833_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-542 1121,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2105220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-557 1121,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="43522@x" ObjectIDND1="43521@x" ObjectIDZND0="43517@0" Pin0InfoVect0LinkObjId="SW-268829_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268834_0" Pin1InfoVect1LinkObjId="SW-268833_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-557 1121,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2105480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-620 1140,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43517@x" ObjectIDND1="43518@x" ObjectIDZND0="43520@0" Pin0InfoVect0LinkObjId="SW-268832_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268829_0" Pin1InfoVect1LinkObjId="SW-268830_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-620 1140,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21056e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-620 1197,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43520@1" ObjectIDZND0="g_2105940@0" Pin0InfoVect0LinkObjId="g_2105940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268832_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-620 1197,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2106370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-601 1121,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43517@1" ObjectIDZND0="43520@x" ObjectIDZND1="43518@x" Pin0InfoVect0LinkObjId="SW-268832_0" Pin0InfoVect1LinkObjId="SW-268830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268829_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-601 1121,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21065d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-620 1121,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43517@x" ObjectIDND1="43520@x" ObjectIDZND0="43518@0" Pin0InfoVect0LinkObjId="SW-268830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268829_0" Pin1InfoVect1LinkObjId="SW-268832_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-620 1121,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2106830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-688 1140,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2107980@0" ObjectIDND1="g_20727c0@0" ObjectIDND2="0@x" ObjectIDZND0="43519@0" Pin0InfoVect0LinkObjId="SW-268831_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2107980_0" Pin1InfoVect1LinkObjId="g_20727c0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-688 1140,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2106a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-688 1197,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43519@1" ObjectIDZND0="g_2106cf0@0" Pin0InfoVect0LinkObjId="g_2106cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268831_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-688 1197,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2107720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-674 1121,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="43518@1" ObjectIDZND0="g_2107980@0" ObjectIDZND1="g_20727c0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2107980_0" Pin0InfoVect1LinkObjId="g_20727c0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-674 1121,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2072100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-778 1153,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="43519@x" ObjectIDND1="43518@x" ObjectIDND2="g_20727c0@0" ObjectIDZND0="g_2107980@0" Pin0InfoVect0LinkObjId="g_2107980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268831_0" Pin1InfoVect1LinkObjId="SW-268830_0" Pin1InfoVect2LinkObjId="g_20727c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-778 1153,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2072330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-688 1121,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="43519@x" ObjectIDND1="43518@x" ObjectIDZND0="g_2107980@0" ObjectIDZND1="g_20727c0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2107980_0" Pin0InfoVect1LinkObjId="g_20727c0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268831_0" Pin1InfoVect1LinkObjId="SW-268830_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-688 1121,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2072560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-855 1146,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2107980@0" ObjectIDND1="43519@x" ObjectIDND2="43518@x" ObjectIDZND0="g_20727c0@0" Pin0InfoVect0LinkObjId="g_20727c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2107980_0" Pin1InfoVect1LinkObjId="SW-268831_0" Pin1InfoVect2LinkObjId="SW-268830_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-855 1146,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2073320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-778 1121,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2107980@0" ObjectIDND1="43519@x" ObjectIDND2="43518@x" ObjectIDZND0="g_20727c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_20727c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2107980_0" Pin1InfoVect1LinkObjId="SW-268831_0" Pin1InfoVect2LinkObjId="SW-268830_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-778 1121,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2073580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-855 1121,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_20727c0@0" ObjectIDND1="g_2107980@0" ObjectIDND2="43519@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20727c0_0" Pin1InfoVect1LinkObjId="g_2107980_0" Pin1InfoVect2LinkObjId="SW-268831_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-855 1121,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2066a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,-72 761,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43507@0" ObjectIDZND0="43542@0" Pin0InfoVect0LinkObjId="SW-268865_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20781e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,-72 761,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2066cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,-17 761,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43542@1" ObjectIDZND0="43541@1" Pin0InfoVect0LinkObjId="SW-268864_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268865_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,-17 761,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2066f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,22 761,38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43541@0" ObjectIDZND0="43543@1" Pin0InfoVect0LinkObjId="SW-268865_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,22 761,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201b4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="741,90 761,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2067190@0" ObjectIDZND0="43543@x" ObjectIDZND1="43544@x" ObjectIDZND2="g_201f2d0@0" Pin0InfoVect0LinkObjId="SW-268865_0" Pin0InfoVect1LinkObjId="SW-268866_0" Pin0InfoVect2LinkObjId="g_201f2d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2067190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="741,90 761,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201b730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,56 761,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43543@0" ObjectIDZND0="g_2067190@0" ObjectIDZND1="43544@x" ObjectIDZND2="g_201f2d0@0" Pin0InfoVect0LinkObjId="g_2067190_0" Pin0InfoVect1LinkObjId="SW-268866_0" Pin0InfoVect2LinkObjId="g_201f2d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="761,56 761,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201e120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,148 741,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2067190@0" ObjectIDND1="43543@x" ObjectIDND2="g_201f2d0@0" ObjectIDZND0="43544@0" Pin0InfoVect0LinkObjId="SW-268866_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2067190_0" Pin1InfoVect1LinkObjId="SW-268865_0" Pin1InfoVect2LinkObjId="g_201f2d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,148 741,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201e380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,148 693,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43544@1" ObjectIDZND0="g_201e5e0@0" Pin0InfoVect0LinkObjId="g_201e5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268866_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,148 693,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201f070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,148 761,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43544@x" ObjectIDND1="g_201f2d0@0" ObjectIDZND0="g_2067190@0" ObjectIDZND1="43543@x" Pin0InfoVect0LinkObjId="g_2067190_0" Pin0InfoVect1LinkObjId="SW-268865_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268866_0" Pin1InfoVect1LinkObjId="g_201f2d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="761,148 761,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,273 761,237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" ObjectIDND0="48817@0" ObjectIDZND0="g_201f2d0@0" Pin0InfoVect0LinkObjId="g_201f2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_LL.CX_LL_353_P3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,273 761,237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204db60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,184 761,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_201f2d0@1" ObjectIDZND0="43544@x" ObjectIDZND1="g_2067190@0" ObjectIDZND2="43543@x" Pin0InfoVect0LinkObjId="SW-268866_0" Pin0InfoVect1LinkObjId="g_2067190_0" Pin0InfoVect2LinkObjId="SW-268865_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_201f2d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="761,184 761,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20af9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-72 571,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43507@0" ObjectIDZND0="43538@0" Pin0InfoVect0LinkObjId="SW-268860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20781e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-72 571,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20afc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-18 571,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43538@1" ObjectIDZND0="43537@1" Pin0InfoVect0LinkObjId="SW-268859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-18 571,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20afea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,21 571,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43537@0" ObjectIDZND0="43539@1" Pin0InfoVect0LinkObjId="SW-268860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,21 571,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b0eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="551,89 571,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_20b0100@0" ObjectIDZND0="43539@x" ObjectIDZND1="43540@x" ObjectIDZND2="g_20939b0@0" Pin0InfoVect0LinkObjId="SW-268860_0" Pin0InfoVect1LinkObjId="SW-268861_0" Pin0InfoVect2LinkObjId="g_20939b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b0100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="551,89 571,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b1110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,55 571,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43539@0" ObjectIDZND0="g_20b0100@0" ObjectIDZND1="43540@x" ObjectIDZND2="g_20939b0@0" Pin0InfoVect0LinkObjId="g_20b0100_0" Pin0InfoVect1LinkObjId="SW-268861_0" Pin0InfoVect2LinkObjId="g_20939b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="571,55 571,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b3b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,147 551,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_20b0100@0" ObjectIDND1="43539@x" ObjectIDND2="g_20939b0@0" ObjectIDZND0="43540@0" Pin0InfoVect0LinkObjId="SW-268861_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20b0100_0" Pin1InfoVect1LinkObjId="SW-268860_0" Pin1InfoVect2LinkObjId="g_20939b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,147 551,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b3dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,147 503,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43540@1" ObjectIDZND0="g_20b4030@0" Pin0InfoVect0LinkObjId="g_20b4030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268861_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,147 503,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2093750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,147 571,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43540@x" ObjectIDND1="g_20939b0@0" ObjectIDZND0="g_20b0100@0" ObjectIDZND1="43539@x" Pin0InfoVect0LinkObjId="g_20b0100_0" Pin0InfoVect1LinkObjId="SW-268860_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268861_0" Pin1InfoVect1LinkObjId="g_20939b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="571,147 571,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20943d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,272 571,236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" ObjectIDND0="48816@0" ObjectIDZND0="g_20939b0@0" Pin0InfoVect0LinkObjId="g_20939b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_LL.CX_LL_352_P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,272 571,236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2094630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,183 571,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_20939b0@1" ObjectIDZND0="43540@x" ObjectIDZND1="g_20b0100@0" ObjectIDZND2="43539@x" Pin0InfoVect0LinkObjId="SW-268861_0" Pin0InfoVect1LinkObjId="g_20b0100_0" Pin0InfoVect2LinkObjId="SW-268860_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20939b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="571,183 571,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-72 359,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43507@0" ObjectIDZND0="43534@0" Pin0InfoVect0LinkObjId="SW-268855_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20781e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-72 359,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-17 359,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43534@1" ObjectIDZND0="43533@1" Pin0InfoVect0LinkObjId="SW-268854_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268855_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-17 359,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,22 359,38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43533@0" ObjectIDZND0="43535@1" Pin0InfoVect0LinkObjId="SW-268855_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268854_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,22 359,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207ab40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="339,90 359,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_201af10@0" ObjectIDZND0="43535@x" ObjectIDZND1="43536@x" ObjectIDZND2="g_207e9b0@0" Pin0InfoVect0LinkObjId="SW-268855_0" Pin0InfoVect1LinkObjId="SW-268856_0" Pin0InfoVect2LinkObjId="g_207e9b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_201af10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="339,90 359,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207ada0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,56 359,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43535@0" ObjectIDZND0="g_201af10@0" ObjectIDZND1="43536@x" ObjectIDZND2="g_207e9b0@0" Pin0InfoVect0LinkObjId="g_201af10_0" Pin0InfoVect1LinkObjId="SW-268856_0" Pin0InfoVect2LinkObjId="g_207e9b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268855_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="359,56 359,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207d800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,148 339,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_201af10@0" ObjectIDND1="43535@x" ObjectIDND2="g_207e9b0@0" ObjectIDZND0="43536@0" Pin0InfoVect0LinkObjId="SW-268856_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_201af10_0" Pin1InfoVect1LinkObjId="SW-268855_0" Pin1InfoVect2LinkObjId="g_207e9b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,148 339,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="303,148 291,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43536@1" ObjectIDZND0="g_207dcc0@0" Pin0InfoVect0LinkObjId="g_207dcc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="303,148 291,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207e750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,148 359,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43536@x" ObjectIDND1="g_207e9b0@0" ObjectIDZND0="g_201af10@0" ObjectIDZND1="43535@x" Pin0InfoVect0LinkObjId="g_201af10_0" Pin0InfoVect1LinkObjId="SW-268855_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268856_0" Pin1InfoVect1LinkObjId="g_207e9b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="359,148 359,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207f3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,273 359,237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" ObjectIDND0="48815@0" ObjectIDZND0="g_207e9b0@0" Pin0InfoVect0LinkObjId="g_207e9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_LL.CX_LL_351_P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,273 359,237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,184 359,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_207e9b0@1" ObjectIDZND0="43536@x" ObjectIDZND1="g_201af10@0" ObjectIDZND2="43535@x" Pin0InfoVect0LinkObjId="SW-268856_0" Pin0InfoVect1LinkObjId="g_201af10_0" Pin0InfoVect2LinkObjId="SW-268855_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_207e9b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="359,184 359,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f7e570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,-72 1503,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43507@0" ObjectIDZND0="43592@0" Pin0InfoVect0LinkObjId="SW-268923_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20781e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-72 1503,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f7e7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,-15 1503,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43592@1" ObjectIDZND0="43591@1" Pin0InfoVect0LinkObjId="SW-268922_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268923_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-15 1503,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f7ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,26 1503,42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43591@0" ObjectIDZND0="43593@1" Pin0InfoVect0LinkObjId="SW-268923_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268922_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,26 1503,42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2062e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,-71 1904,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43508@0" ObjectIDZND0="43559@0" Pin0InfoVect0LinkObjId="SW-268884_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f1eb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1904,-71 1904,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2063090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,-17 1904,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43559@1" ObjectIDZND0="43558@1" Pin0InfoVect0LinkObjId="SW-268883_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268884_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1904,-17 1904,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20632f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,22 1904,38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43558@0" ObjectIDZND0="43560@1" Pin0InfoVect0LinkObjId="SW-268884_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268883_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1904,22 1904,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f6d9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1884,90 1904,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2063550@0" ObjectIDZND0="43560@x" ObjectIDZND1="43561@x" ObjectIDZND2="g_1f71850@0" Pin0InfoVect0LinkObjId="SW-268884_0" Pin0InfoVect1LinkObjId="SW-268885_0" Pin0InfoVect2LinkObjId="g_1f71850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2063550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1884,90 1904,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f6dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,56 1904,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43560@0" ObjectIDZND0="g_2063550@0" ObjectIDZND1="43561@x" ObjectIDZND2="g_1f71850@0" Pin0InfoVect0LinkObjId="g_2063550_0" Pin0InfoVect1LinkObjId="SW-268885_0" Pin0InfoVect2LinkObjId="g_1f71850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268884_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1904,56 1904,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f706a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,148 1884,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43560@x" ObjectIDND1="g_2063550@0" ObjectIDND2="g_1f71850@0" ObjectIDZND0="43561@0" Pin0InfoVect0LinkObjId="SW-268885_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268884_0" Pin1InfoVect1LinkObjId="g_2063550_0" Pin1InfoVect2LinkObjId="g_1f71850_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1904,148 1884,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f70900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1848,148 1836,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43561@1" ObjectIDZND0="g_1f70b60@0" Pin0InfoVect0LinkObjId="g_1f70b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268885_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1848,148 1836,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f715f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,148 1904,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="43561@x" ObjectIDND1="g_1f71850@0" ObjectIDZND0="43560@x" ObjectIDZND1="g_2063550@0" Pin0InfoVect0LinkObjId="SW-268884_0" Pin0InfoVect1LinkObjId="g_2063550_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268885_0" Pin1InfoVect1LinkObjId="g_1f71850_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1904,148 1904,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f72270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,273 1904,237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" ObjectIDND0="48818@0" ObjectIDZND0="g_1f71850@0" Pin0InfoVect0LinkObjId="g_1f71850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_LL.CX_LL_361_P4_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1904,273 1904,237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f724d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,184 1904,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1f71850@1" ObjectIDZND0="43560@x" ObjectIDZND1="g_2063550@0" ObjectIDZND2="43561@x" Pin0InfoVect0LinkObjId="SW-268884_0" Pin0InfoVect1LinkObjId="g_2063550_0" Pin0InfoVect2LinkObjId="SW-268885_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f71850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1904,184 1904,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1faf690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2049,-71 2049,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43508@0" ObjectIDZND0="43563@0" Pin0InfoVect0LinkObjId="SW-268889_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f1eb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2049,-71 2049,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1faf8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2049,-17 2049,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43563@1" ObjectIDZND0="43562@1" Pin0InfoVect0LinkObjId="SW-268888_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268889_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2049,-17 2049,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fafb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2049,22 2049,38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43562@0" ObjectIDZND0="43564@1" Pin0InfoVect0LinkObjId="SW-268889_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268888_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2049,22 2049,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb0b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2029,90 2049,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1fafdb0@0" ObjectIDZND0="43564@x" ObjectIDZND1="43565@x" ObjectIDZND2="g_1fdc410@0" Pin0InfoVect0LinkObjId="SW-268889_0" Pin0InfoVect1LinkObjId="SW-268890_0" Pin0InfoVect2LinkObjId="g_1fdc410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fafdb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2029,90 2049,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb0dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2049,56 2049,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43564@0" ObjectIDZND0="g_1fafdb0@0" ObjectIDZND1="43565@x" ObjectIDZND2="g_1fdc410@0" Pin0InfoVect0LinkObjId="g_1fafdb0_0" Pin0InfoVect1LinkObjId="SW-268890_0" Pin0InfoVect2LinkObjId="g_1fdc410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268889_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2049,56 2049,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fdb260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2049,148 2029,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43564@x" ObjectIDND1="g_1fafdb0@0" ObjectIDND2="g_1fdc410@0" ObjectIDZND0="43565@0" Pin0InfoVect0LinkObjId="SW-268890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268889_0" Pin1InfoVect1LinkObjId="g_1fafdb0_0" Pin1InfoVect2LinkObjId="g_1fdc410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2049,148 2029,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fdb4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,148 1981,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43565@1" ObjectIDZND0="g_1fdb720@0" Pin0InfoVect0LinkObjId="g_1fdb720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1993,148 1981,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fdc1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2049,148 2049,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="43565@x" ObjectIDND1="g_1fdc410@0" ObjectIDZND0="43564@x" ObjectIDZND1="g_1fafdb0@0" Pin0InfoVect0LinkObjId="SW-268889_0" Pin0InfoVect1LinkObjId="g_1fafdb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268890_0" Pin1InfoVect1LinkObjId="g_1fdc410_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2049,148 2049,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fdce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2049,273 2049,237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" ObjectIDND0="48819@0" ObjectIDZND0="g_1fdc410@0" Pin0InfoVect0LinkObjId="g_1fdc410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_LL.CX_LL_362_P5_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2049,273 2049,237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fdd090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2049,184 2049,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1fdc410@1" ObjectIDZND0="43564@x" ObjectIDZND1="g_1fafdb0@0" ObjectIDZND2="43565@x" Pin0InfoVect0LinkObjId="SW-268889_0" Pin0InfoVect1LinkObjId="g_1fafdb0_0" Pin0InfoVect2LinkObjId="SW-268890_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fdc410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2049,184 2049,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe1250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2190,-71 2190,-18 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43508@0" ObjectIDZND0="43531@0" Pin0InfoVect0LinkObjId="SW-268844_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f1eb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2190,-71 2190,-18 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe14b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2190,0 2190,19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43531@1" ObjectIDZND0="g_1fe2980@0" ObjectIDZND1="g_1fe1bd0@0" Pin0InfoVect0LinkObjId="g_1fe2980_0" Pin0InfoVect1LinkObjId="g_1fe1bd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268844_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2190,0 2190,19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe1710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2165,41 2165,19 2190,19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1fe2980@1" ObjectIDZND0="43531@x" ObjectIDZND1="g_1fe1bd0@0" Pin0InfoVect0LinkObjId="SW-268844_0" Pin0InfoVect1LinkObjId="g_1fe1bd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fe2980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2165,41 2165,19 2190,19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe1970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2190,19 2215,19 2215,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1fe2980@0" ObjectIDND1="43531@x" ObjectIDZND0="g_1fe1bd0@0" Pin0InfoVect0LinkObjId="g_1fe1bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fe2980_0" Pin1InfoVect1LinkObjId="SW-268844_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2190,19 2215,19 2215,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2041480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2165,85 2165,104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1fe2980@0" ObjectIDZND0="g_1fe3280@0" Pin0InfoVect0LinkObjId="g_1fe3280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fe2980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2165,85 2165,104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2049f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,-71 2540,-41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43508@0" ObjectIDZND0="43572@0" Pin0InfoVect0LinkObjId="SW-268900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f1eb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,-71 2540,-41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204a1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,-23 2540,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43572@1" ObjectIDZND0="43571@1" Pin0InfoVect0LinkObjId="SW-268899_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,-23 2540,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe4710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,16 2540,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43571@0" ObjectIDZND0="43573@1" Pin0InfoVect0LinkObjId="SW-268900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268899_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,16 2540,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe56f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2520,84 2540,84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1fe4940@0" ObjectIDZND0="43573@x" ObjectIDZND1="43574@x" ObjectIDZND2="g_1fe9560@0" Pin0InfoVect0LinkObjId="SW-268900_0" Pin0InfoVect1LinkObjId="SW-268901_0" Pin0InfoVect2LinkObjId="g_1fe9560_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fe4940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2520,84 2540,84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe5950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,50 2540,84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43573@0" ObjectIDZND0="g_1fe4940@0" ObjectIDZND1="43574@x" ObjectIDZND2="g_1fe9560@0" Pin0InfoVect0LinkObjId="g_1fe4940_0" Pin0InfoVect1LinkObjId="SW-268901_0" Pin0InfoVect2LinkObjId="g_1fe9560_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2540,50 2540,84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe83b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,142 2520,142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fe4940@0" ObjectIDND1="43573@x" ObjectIDND2="g_1fe9560@0" ObjectIDZND0="43574@0" Pin0InfoVect0LinkObjId="SW-268901_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fe4940_0" Pin1InfoVect1LinkObjId="SW-268900_0" Pin1InfoVect2LinkObjId="g_1fe9560_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,142 2520,142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe8610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2484,142 2472,142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43574@1" ObjectIDZND0="g_1fe8870@0" Pin0InfoVect0LinkObjId="g_1fe8870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268901_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2484,142 2472,142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe9300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,142 2540,84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43574@x" ObjectIDND1="g_1fe9560@0" ObjectIDZND0="g_1fe4940@0" ObjectIDZND1="43573@x" Pin0InfoVect0LinkObjId="g_1fe4940_0" Pin0InfoVect1LinkObjId="SW-268900_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268901_0" Pin1InfoVect1LinkObjId="g_1fe9560_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2540,142 2540,84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe9f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,186 2540,142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1fe9560@1" ObjectIDZND0="43574@x" ObjectIDZND1="g_1fe4940@0" ObjectIDZND2="43573@x" Pin0InfoVect0LinkObjId="SW-268901_0" Pin0InfoVect1LinkObjId="g_1fe4940_0" Pin0InfoVect2LinkObjId="SW-268900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fe9560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2540,186 2540,142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1feaf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,267 2579,267 2579,296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1fe9560@0" ObjectIDND1="43576@x" ObjectIDZND0="g_1fea1e0@0" Pin0InfoVect0LinkObjId="g_1fea1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fe9560_0" Pin1InfoVect1LinkObjId="SW-268903_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,267 2579,267 2579,296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1feb1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,267 2540,239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1fea1e0@0" ObjectIDND1="43576@x" ObjectIDZND0="g_1fe9560@0" Pin0InfoVect0LinkObjId="g_1fe9560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fea1e0_0" Pin1InfoVect1LinkObjId="SW-268903_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,267 2540,239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fedc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,293 2540,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43576@1" ObjectIDZND0="g_1fea1e0@0" ObjectIDZND1="g_1fe9560@0" Pin0InfoVect0LinkObjId="g_1fea1e0_0" Pin0InfoVect1LinkObjId="g_1fe9560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268903_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2540,293 2540,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2484,351 2472,351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43578@1" ObjectIDZND0="g_1fb51e0@0" Pin0InfoVect0LinkObjId="g_1fb51e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268904_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2484,351 2472,351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb68a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2602,424 2602,456 2541,456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="reactance" EndDevType1="breaker" ObjectIDND0="g_1fb5c70@1" ObjectIDZND0="0@x" ObjectIDZND1="43575@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-268902_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb5c70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2602,424 2602,456 2541,456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb6b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,456 2540,423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="reactance" EndDevType0="breaker" ObjectIDND0="g_1fb5c70@0" ObjectIDND1="0@x" ObjectIDZND0="43575@0" Pin0InfoVect0LinkObjId="SW-268902_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fb5c70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,456 2540,423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fb79f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,563 2540,530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="reactance" ObjectIDND0="g_1fb7eb0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb7eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,563 2540,530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb7c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,488 2540,456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="g_1fb5c70@0" ObjectIDZND1="43575@x" Pin0InfoVect0LinkObjId="g_1fb5c70_0" Pin0InfoVect1LinkObjId="SW-268902_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2540,488 2540,456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc08b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2337,-71 2337,-37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43508@0" ObjectIDZND0="43567@0" Pin0InfoVect0LinkObjId="SW-268894_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f1eb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2337,-71 2337,-37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc0b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2337,-19 2337,-6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43567@1" ObjectIDZND0="43566@1" Pin0InfoVect0LinkObjId="SW-268893_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268894_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2337,-19 2337,-6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc0d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2337,20 2337,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43566@0" ObjectIDZND0="43568@1" Pin0InfoVect0LinkObjId="SW-268894_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268893_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2337,20 2337,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc1d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2317,88 2337,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1fc0fd0@0" ObjectIDZND0="43568@x" ObjectIDZND1="43569@x" ObjectIDZND2="g_1fc5bf0@0" Pin0InfoVect0LinkObjId="SW-268894_0" Pin0InfoVect1LinkObjId="SW-268895_0" Pin0InfoVect2LinkObjId="g_1fc5bf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc0fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2317,88 2337,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc1fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2337,54 2337,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43568@0" ObjectIDZND0="g_1fc0fd0@0" ObjectIDZND1="43569@x" ObjectIDZND2="g_1fc5bf0@0" Pin0InfoVect0LinkObjId="g_1fc0fd0_0" Pin0InfoVect1LinkObjId="SW-268895_0" Pin0InfoVect2LinkObjId="g_1fc5bf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268894_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2337,54 2337,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc4a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2337,146 2317,146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43568@x" ObjectIDND1="g_1fc0fd0@0" ObjectIDND2="g_1fc5bf0@0" ObjectIDZND0="43569@0" Pin0InfoVect0LinkObjId="SW-268895_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268894_0" Pin1InfoVect1LinkObjId="g_1fc0fd0_0" Pin1InfoVect2LinkObjId="g_1fc5bf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2337,146 2317,146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc4ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2281,146 2269,146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43569@1" ObjectIDZND0="g_1fc4f00@0" Pin0InfoVect0LinkObjId="g_1fc4f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268895_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2281,146 2269,146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc5990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2337,146 2337,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="43569@x" ObjectIDND1="g_1fc5bf0@0" ObjectIDZND0="43568@x" ObjectIDZND1="g_1fc0fd0@0" Pin0InfoVect0LinkObjId="SW-268894_0" Pin0InfoVect1LinkObjId="g_1fc0fd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268895_0" Pin1InfoVect1LinkObjId="g_1fc5bf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2337,146 2337,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc6610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2337,271 2337,235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_200bd10@0" ObjectIDZND0="g_1fc5bf0@0" Pin0InfoVect0LinkObjId="g_1fc5bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200bd10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2337,271 2337,235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc6870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2337,182 2337,146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1fc5bf0@1" ObjectIDZND0="43568@x" ObjectIDZND1="g_1fc0fd0@0" ObjectIDZND2="43569@x" Pin0InfoVect0LinkObjId="SW-268894_0" Pin0InfoVect1LinkObjId="g_1fc0fd0_0" Pin0InfoVect2LinkObjId="SW-268895_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc5bf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2337,182 2337,146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc92d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2335,287 2283,287 2283,297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="43570@1" Pin0InfoVect0LinkObjId="SW-268896_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2335,287 2283,287 2283,297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fca160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2283,333 2283,345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43570@0" ObjectIDZND0="g_1fc9530@0" Pin0InfoVect0LinkObjId="g_1fc9530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268896_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2283,333 2283,345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fca3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2283,379 2283,396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_1fc9530@1" ObjectIDZND0="g_1fca620@0" Pin0InfoVect0LinkObjId="g_1fca620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc9530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2283,379 2283,396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2014be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,-71 3011,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43508@0" ObjectIDZND0="43584@0" Pin0InfoVect0LinkObjId="SW-268913_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f1eb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,-71 3011,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2014e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,-18 3011,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43584@1" ObjectIDZND0="43583@1" Pin0InfoVect0LinkObjId="SW-268912_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268913_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,-18 3011,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20150a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,21 3011,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43583@0" ObjectIDZND0="43585@1" Pin0InfoVect0LinkObjId="SW-268913_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268912_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,21 3011,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2031b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2991,89 3011,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2015300@0" ObjectIDZND0="43585@x" ObjectIDZND1="43586@x" ObjectIDZND2="g_2035990@0" Pin0InfoVect0LinkObjId="SW-268913_0" Pin0InfoVect1LinkObjId="SW-268914_0" Pin0InfoVect2LinkObjId="g_2035990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2015300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2991,89 3011,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2031d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,55 3011,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43585@0" ObjectIDZND0="g_2015300@0" ObjectIDZND1="43586@x" ObjectIDZND2="g_2035990@0" Pin0InfoVect0LinkObjId="g_2015300_0" Pin0InfoVect1LinkObjId="SW-268914_0" Pin0InfoVect2LinkObjId="g_2035990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268913_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3011,55 3011,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20347e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,147 2991,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43585@x" ObjectIDND1="g_2015300@0" ObjectIDND2="g_2035990@0" ObjectIDZND0="43586@0" Pin0InfoVect0LinkObjId="SW-268914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268913_0" Pin1InfoVect1LinkObjId="g_2015300_0" Pin1InfoVect2LinkObjId="g_2035990_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,147 2991,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2034a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2955,147 2943,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43586@1" ObjectIDZND0="g_2034ca0@0" Pin0InfoVect0LinkObjId="g_2034ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268914_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2955,147 2943,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2035730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,147 3011,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="43586@x" ObjectIDND1="g_2035990@0" ObjectIDZND0="43585@x" ObjectIDZND1="g_2015300@0" Pin0InfoVect0LinkObjId="SW-268913_0" Pin0InfoVect1LinkObjId="g_2015300_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268914_0" Pin1InfoVect1LinkObjId="g_2035990_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3011,147 3011,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20363b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,272 3011,236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2035990@0" Pin0InfoVect0LinkObjId="g_2035990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,272 3011,236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2036610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,183 3011,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2035990@1" ObjectIDZND0="43585@x" ObjectIDZND1="g_2015300@0" ObjectIDZND2="43586@x" Pin0InfoVect0LinkObjId="SW-268913_0" Pin0InfoVect1LinkObjId="g_2015300_0" Pin0InfoVect2LinkObjId="SW-268914_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2035990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3011,183 3011,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fcbdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3206,-71 3206,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43508@0" ObjectIDZND0="43588@0" Pin0InfoVect0LinkObjId="SW-268918_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f1eb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3206,-71 3206,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fcc030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3206,-18 3206,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43588@1" ObjectIDZND0="43587@1" Pin0InfoVect0LinkObjId="SW-268917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268918_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3206,-18 3206,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fcc290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3206,21 3206,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43587@0" ObjectIDZND0="43589@1" Pin0InfoVect0LinkObjId="SW-268918_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3206,21 3206,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fcd2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3186,89 3206,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1fcc4f0@0" ObjectIDZND0="43589@x" ObjectIDZND1="43590@x" ObjectIDZND2="g_1fd1110@0" Pin0InfoVect0LinkObjId="SW-268918_0" Pin0InfoVect1LinkObjId="SW-268919_0" Pin0InfoVect2LinkObjId="g_1fd1110_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fcc4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3186,89 3206,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fcd500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3206,55 3206,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43589@0" ObjectIDZND0="g_1fcc4f0@0" ObjectIDZND1="43590@x" ObjectIDZND2="g_1fd1110@0" Pin0InfoVect0LinkObjId="g_1fcc4f0_0" Pin0InfoVect1LinkObjId="SW-268919_0" Pin0InfoVect2LinkObjId="g_1fd1110_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268918_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3206,55 3206,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fcff60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3206,147 3186,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43589@x" ObjectIDND1="g_1fcc4f0@0" ObjectIDND2="g_1fd1110@0" ObjectIDZND0="43590@0" Pin0InfoVect0LinkObjId="SW-268919_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268918_0" Pin1InfoVect1LinkObjId="g_1fcc4f0_0" Pin1InfoVect2LinkObjId="g_1fd1110_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3206,147 3186,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fd01c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3150,147 3138,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43590@1" ObjectIDZND0="g_1fd0420@0" Pin0InfoVect0LinkObjId="g_1fd0420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268919_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3150,147 3138,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fd0eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3206,147 3206,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="43590@x" ObjectIDND1="g_1fd1110@0" ObjectIDZND0="43589@x" ObjectIDZND1="g_1fcc4f0@0" Pin0InfoVect0LinkObjId="SW-268918_0" Pin0InfoVect1LinkObjId="g_1fcc4f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268919_0" Pin1InfoVect1LinkObjId="g_1fd1110_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3206,147 3206,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fd1b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3206,272 3206,236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1fd1110@0" Pin0InfoVect0LinkObjId="g_1fd1110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3206,272 3206,236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fd1d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3206,183 3206,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1fd1110@1" ObjectIDZND0="43589@x" ObjectIDZND1="g_1fcc4f0@0" ObjectIDZND2="43590@x" Pin0InfoVect0LinkObjId="SW-268918_0" Pin0InfoVect1LinkObjId="g_1fcc4f0_0" Pin0InfoVect2LinkObjId="SW-268919_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd1110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3206,183 3206,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f90dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2786,-71 2786,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43508@0" ObjectIDZND0="43580@0" Pin0InfoVect0LinkObjId="SW-268908_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f1eb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2786,-71 2786,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f91020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2786,-18 2786,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43580@1" ObjectIDZND0="43579@1" Pin0InfoVect0LinkObjId="SW-268907_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268908_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2786,-18 2786,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f91280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2786,21 2786,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43579@0" ObjectIDZND0="43581@1" Pin0InfoVect0LinkObjId="SW-268908_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268907_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2786,21 2786,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f921d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2766,89 2786,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1f914e0@0" ObjectIDZND0="43581@x" ObjectIDZND1="g_1f92690@0" ObjectIDZND2="43582@x" Pin0InfoVect0LinkObjId="SW-268908_0" Pin0InfoVect1LinkObjId="g_1f92690_0" Pin0InfoVect2LinkObjId="SW-268909_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f914e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2766,89 2786,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f92430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2786,55 2786,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43581@0" ObjectIDZND0="g_1f914e0@0" ObjectIDZND1="g_1f92690@0" ObjectIDZND2="43582@x" Pin0InfoVect0LinkObjId="g_1f914e0_0" Pin0InfoVect1LinkObjId="g_1f92690_0" Pin0InfoVect2LinkObjId="SW-268909_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268908_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2786,55 2786,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f92f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2786,272 2786,236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1f92690@0" Pin0InfoVect0LinkObjId="g_1f92690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2786,272 2786,236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f94dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2786,89 2786,138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="43581@x" ObjectIDND1="g_1f914e0@0" ObjectIDZND0="g_1f92690@0" ObjectIDZND1="43582@x" ObjectIDZND2="g_1fa0a70@0" Pin0InfoVect0LinkObjId="g_1f92690_0" Pin0InfoVect1LinkObjId="SW-268909_0" Pin0InfoVect2LinkObjId="g_1fa0a70_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268908_0" Pin1InfoVect1LinkObjId="g_1f914e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2786,89 2786,138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f95000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2786,138 2786,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43581@x" ObjectIDND1="g_1f914e0@0" ObjectIDND2="43582@x" ObjectIDZND0="g_1f92690@1" Pin0InfoVect0LinkObjId="g_1f92690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268908_0" Pin1InfoVect1LinkObjId="g_1f914e0_0" Pin1InfoVect2LinkObjId="SW-268909_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2786,138 2786,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f95ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2786,138 2745,138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="43581@x" ObjectIDND1="g_1f914e0@0" ObjectIDND2="g_1f92690@0" ObjectIDZND0="43582@x" ObjectIDZND1="g_1fa0a70@0" Pin0InfoVect0LinkObjId="SW-268909_0" Pin0InfoVect1LinkObjId="g_1fa0a70_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268908_0" Pin1InfoVect1LinkObjId="g_1f914e0_0" Pin1InfoVect2LinkObjId="g_1f92690_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2786,138 2745,138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f95d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2745,138 2694,138 2694,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="43581@x" ObjectIDND1="g_1f914e0@0" ObjectIDND2="g_1f92690@0" ObjectIDZND0="g_1fa0a70@0" Pin0InfoVect0LinkObjId="g_1fa0a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268908_0" Pin1InfoVect1LinkObjId="g_1f914e0_0" Pin1InfoVect2LinkObjId="g_1f92690_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2745,138 2694,138 2694,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f98560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2745,138 2745,154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43581@x" ObjectIDND1="g_1f914e0@0" ObjectIDND2="g_1f92690@0" ObjectIDZND0="43582@1" Pin0InfoVect0LinkObjId="SW-268909_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-268908_0" Pin1InfoVect1LinkObjId="g_1f914e0_0" Pin1InfoVect2LinkObjId="g_1f92690_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2745,138 2745,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f987c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2745,190 2745,204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43582@0" ObjectIDZND0="g_1f98a20@0" Pin0InfoVect0LinkObjId="g_1f98a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268909_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2745,190 2745,204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fa3b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1481,100 1503,100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1fa3020@0" ObjectIDZND0="43593@x" ObjectIDZND1="43594@x" Pin0InfoVect0LinkObjId="SW-268923_0" Pin0InfoVect1LinkObjId="SW-268924_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fa3020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1481,100 1503,100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fa4610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,60 1503,100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43593@0" ObjectIDZND0="g_1fa3020@0" ObjectIDZND1="43594@x" Pin0InfoVect0LinkObjId="g_1fa3020_0" Pin0InfoVect1LinkObjId="SW-268924_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268923_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1503,60 1503,100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_217fa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2116,-951 2265,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2116,-951 2265,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_217fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-951 2419,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-951 2419,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2184de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-951 2265,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-951 2265,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2186670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-843 2291,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2186860@0" Pin0InfoVect0LinkObjId="g_2186860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-843 2291,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2187ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-875 2265,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="g_2186860@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2186860_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-875 2265,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2187f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-843 2265,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2186860@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2186860_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-843 2265,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2188160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-766 2291,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_21883c0@0" Pin0InfoVect0LinkObjId="g_21883c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-766 2291,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2189a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-798 2265,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_21883c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_21883c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-798 2265,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f0a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-766 2265,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_21883c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21883c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-766 2265,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f0afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2266,-640 2240,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1f0b1c0@0" Pin0InfoVect0LinkObjId="g_1f0b1c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2266,-640 2240,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f0c3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-704 2265,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="g_1f0b1c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1f0b1c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-704 2265,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f0e660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-640 2265,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1f0b1c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f0b1c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-640 2265,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f0e8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2266,-572 2240,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1f0eb20@0" Pin0InfoVect0LinkObjId="g_1f0eb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2266,-572 2240,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f10160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-590 2265,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="g_1f0eb20@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1f0eb20_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-590 2265,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f103c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,-572 2265,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="g_1f0eb20@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f0eb20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-572 2265,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f1e910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,100 1503,121 1756,121 1756,39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1fa3020@0" ObjectIDND1="43593@x" ObjectIDZND0="43594@0" Pin0InfoVect0LinkObjId="SW-268924_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fa3020_0" Pin1InfoVect1LinkObjId="SW-268923_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,100 1503,121 1756,121 1756,39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f1eb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1756,-30 1756,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43594@1" ObjectIDZND0="43508@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268924_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1756,-30 1756,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3c290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,354 1406,354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="43557@0" ObjectIDZND0="43555@x" ObjectIDZND1="g_20085d0@0" ObjectIDZND2="43554@x" Pin0InfoVect0LinkObjId="SW-268879_0" Pin0InfoVect1LinkObjId="g_20085d0_0" Pin0InfoVect2LinkObjId="SW-268878_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1385,354 1406,354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3cc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,332 1406,354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="43555@0" ObjectIDZND0="43557@x" ObjectIDZND1="g_20085d0@0" ObjectIDZND2="43554@x" Pin0InfoVect0LinkObjId="SW-268880_0" Pin0InfoVect1LinkObjId="g_20085d0_0" Pin0InfoVect2LinkObjId="SW-268878_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268879_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1406,332 1406,354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3cdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1467,393 1467,371 1406,371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_20085d0@0" ObjectIDZND0="43557@x" ObjectIDZND1="43555@x" ObjectIDZND2="43554@x" Pin0InfoVect0LinkObjId="SW-268880_0" Pin0InfoVect1LinkObjId="SW-268879_0" Pin0InfoVect2LinkObjId="SW-268878_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20085d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1467,393 1467,371 1406,371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3d840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,354 1406,371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="43557@x" ObjectIDND1="43555@x" ObjectIDZND0="g_20085d0@0" ObjectIDZND1="43554@x" Pin0InfoVect0LinkObjId="g_20085d0_0" Pin0InfoVect1LinkObjId="SW-268878_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268880_0" Pin1InfoVect1LinkObjId="SW-268879_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1406,354 1406,371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3daa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,371 1406,393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_20085d0@0" ObjectIDND1="43557@x" ObjectIDND2="43555@x" ObjectIDZND0="43554@1" Pin0InfoVect0LinkObjId="SW-268878_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20085d0_0" Pin1InfoVect1LinkObjId="SW-268880_0" Pin1InfoVect2LinkObjId="SW-268879_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,371 1406,393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3dd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2520,351 2540,351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="43578@0" ObjectIDZND0="43576@x" ObjectIDZND1="g_1fb5c70@0" ObjectIDZND2="43575@x" Pin0InfoVect0LinkObjId="SW-268903_0" Pin0InfoVect1LinkObjId="g_1fb5c70_0" Pin0InfoVect2LinkObjId="SW-268902_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268904_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2520,351 2540,351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3e7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,329 2540,351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="43576@0" ObjectIDZND0="43578@x" ObjectIDZND1="g_1fb5c70@0" ObjectIDZND2="43575@x" Pin0InfoVect0LinkObjId="SW-268904_0" Pin0InfoVect1LinkObjId="g_1fb5c70_0" Pin0InfoVect2LinkObjId="SW-268902_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-268903_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2540,329 2540,351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2602,390 2602,368 2540,368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_1fb5c70@0" ObjectIDZND0="43578@x" ObjectIDZND1="43576@x" ObjectIDZND2="43575@x" Pin0InfoVect0LinkObjId="SW-268904_0" Pin0InfoVect1LinkObjId="SW-268903_0" Pin0InfoVect2LinkObjId="SW-268902_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb5c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2602,390 2602,368 2540,368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3f500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,351 2540,368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="43578@x" ObjectIDND1="43576@x" ObjectIDZND0="g_1fb5c70@0" ObjectIDZND1="43575@x" Pin0InfoVect0LinkObjId="g_1fb5c70_0" Pin0InfoVect1LinkObjId="SW-268902_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-268904_0" Pin1InfoVect1LinkObjId="SW-268903_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2540,351 2540,368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3f760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,368 2540,393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_1fb5c70@0" ObjectIDND1="43578@x" ObjectIDND2="43576@x" ObjectIDZND0="43575@1" Pin0InfoVect0LinkObjId="SW-268902_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fb5c70_0" Pin1InfoVect1LinkObjId="SW-268904_0" Pin1InfoVect2LinkObjId="SW-268903_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,368 2540,393 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-268537" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 -854.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43486" ObjectName="DYN-CX_LL"/>
     <cge:Meas_Ref ObjectId="268537"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="2264" cy="-928" fill="none" fillStyle="0" r="3.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2264" cy="-861" fill="none" fillStyle="0" r="3.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2265" cy="-539" fill="none" fillStyle="0" r="3.5" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LL.CX_LL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="157,-72 1577,-72 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43507" ObjectName="BS-CX_LL.CX_LL_3IM"/>
    <cge:TPSR_Ref TObjectID="43507"/></metadata>
   <polyline fill="none" opacity="0" points="157,-72 1577,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LL.CX_LL_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1703,-71 3324,-71 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43508" ObjectName="BS-CX_LL.CX_LL_3IIM"/>
    <cge:TPSR_Ref TObjectID="43508"/></metadata>
   <polyline fill="none" opacity="0" points="1703,-71 3324,-71 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="43507" cx="941" cy="-72" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43507" cx="1406" cy="-72" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43507" cx="1180" cy="-72" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43507" cx="942" cy="-72" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43507" cx="761" cy="-72" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43507" cx="571" cy="-72" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43507" cx="359" cy="-72" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43507" cx="1503" cy="-72" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43508" cx="1904" cy="-71" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43508" cx="2049" cy="-71" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43508" cx="2190" cy="-71" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43508" cx="2540" cy="-71" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43508" cx="2337" cy="-71" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43508" cx="3011" cy="-71" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43508" cx="3206" cy="-71" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43508" cx="2786" cy="-71" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43508" cx="1756" cy="-71" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-268842">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 931.000000 -213.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43528" ObjectName="SW-CX_LL.CX_LL_301XC1"/>
     <cge:Meas_Ref ObjectId="268842"/>
    <cge:TPSR_Ref TObjectID="43528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268842">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 931.000000 -140.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43527" ObjectName="SW-CX_LL.CX_LL_301XC"/>
     <cge:Meas_Ref ObjectId="268842"/>
    <cge:TPSR_Ref TObjectID="43527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 59.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43552" ObjectName="SW-CX_LL.CX_LL_355XC1"/>
     <cge:Meas_Ref ObjectId="268876"/>
    <cge:TPSR_Ref TObjectID="43552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -14.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43551" ObjectName="SW-CX_LL.CX_LL_355XC"/>
     <cge:Meas_Ref ObjectId="268876"/>
    <cge:TPSR_Ref TObjectID="43551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268877">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1390.000000 136.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43553" ObjectName="SW-CX_LL.CX_LL_35567SW"/>
     <cge:Meas_Ref ObjectId="268877"/>
    <cge:TPSR_Ref TObjectID="43553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268879">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1415.000000 337.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43555" ObjectName="SW-CX_LL.CX_LL_3561SW"/>
     <cge:Meas_Ref ObjectId="268879"/>
    <cge:TPSR_Ref TObjectID="43555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268880">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1390.000000 345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43557" ObjectName="SW-CX_LL.CX_LL_35667SW"/>
     <cge:Meas_Ref ObjectId="268880"/>
    <cge:TPSR_Ref TObjectID="43557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268870">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.000000 59.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43547" ObjectName="SW-CX_LL.CX_LL_354XC1"/>
     <cge:Meas_Ref ObjectId="268870"/>
    <cge:TPSR_Ref TObjectID="43547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268870">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.000000 -14.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43546" ObjectName="SW-CX_LL.CX_LL_354XC"/>
     <cge:Meas_Ref ObjectId="268870"/>
    <cge:TPSR_Ref TObjectID="43546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268871">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1165.000000 136.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43548" ObjectName="SW-CX_LL.CX_LL_35467SW"/>
     <cge:Meas_Ref ObjectId="268871"/>
    <cge:TPSR_Ref TObjectID="43548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268872">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1135.000000 334.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43549" ObjectName="SW-CX_LL.CX_LL_010SW"/>
     <cge:Meas_Ref ObjectId="268872"/>
    <cge:TPSR_Ref TObjectID="43549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268843">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 932.000000 6.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43529" ObjectName="SW-CX_LL.CX_LL_3901XC"/>
     <cge:Meas_Ref ObjectId="268843"/>
    <cge:TPSR_Ref TObjectID="43529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268865">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.000000 62.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43543" ObjectName="SW-CX_LL.CX_LL_353XC1"/>
     <cge:Meas_Ref ObjectId="268865"/>
    <cge:TPSR_Ref TObjectID="43543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268865">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.000000 -11.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43542" ObjectName="SW-CX_LL.CX_LL_353XC"/>
     <cge:Meas_Ref ObjectId="268865"/>
    <cge:TPSR_Ref TObjectID="43542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268866">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 746.000000 139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43544" ObjectName="SW-CX_LL.CX_LL_35367SW"/>
     <cge:Meas_Ref ObjectId="268866"/>
    <cge:TPSR_Ref TObjectID="43544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268860">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 561.000000 61.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43539" ObjectName="SW-CX_LL.CX_LL_352XC1"/>
     <cge:Meas_Ref ObjectId="268860"/>
    <cge:TPSR_Ref TObjectID="43539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268860">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 561.000000 -12.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43538" ObjectName="SW-CX_LL.CX_LL_352XC"/>
     <cge:Meas_Ref ObjectId="268860"/>
    <cge:TPSR_Ref TObjectID="43538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 556.000000 138.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43540" ObjectName="SW-CX_LL.CX_LL_35267SW"/>
     <cge:Meas_Ref ObjectId="268861"/>
    <cge:TPSR_Ref TObjectID="43540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268855">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.000000 62.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43535" ObjectName="SW-CX_LL.CX_LL_351XC1"/>
     <cge:Meas_Ref ObjectId="268855"/>
    <cge:TPSR_Ref TObjectID="43535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268855">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.000000 -11.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43534" ObjectName="SW-CX_LL.CX_LL_351XC"/>
     <cge:Meas_Ref ObjectId="268855"/>
    <cge:TPSR_Ref TObjectID="43534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 344.000000 139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43536" ObjectName="SW-CX_LL.CX_LL_35167SW"/>
     <cge:Meas_Ref ObjectId="268856"/>
    <cge:TPSR_Ref TObjectID="43536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268923">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 66.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43593" ObjectName="SW-CX_LL.CX_LL_312XC1"/>
     <cge:Meas_Ref ObjectId="268923"/>
    <cge:TPSR_Ref TObjectID="43593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268923">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 -9.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43592" ObjectName="SW-CX_LL.CX_LL_312XC"/>
     <cge:Meas_Ref ObjectId="268923"/>
    <cge:TPSR_Ref TObjectID="43592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268884">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1894.000000 62.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43560" ObjectName="SW-CX_LL.CX_LL_361XC1"/>
     <cge:Meas_Ref ObjectId="268884"/>
    <cge:TPSR_Ref TObjectID="43560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268884">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1894.000000 -11.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43559" ObjectName="SW-CX_LL.CX_LL_361XC"/>
     <cge:Meas_Ref ObjectId="268884"/>
    <cge:TPSR_Ref TObjectID="43559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268885">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1889.000000 139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43561" ObjectName="SW-CX_LL.CX_LL_36167SW"/>
     <cge:Meas_Ref ObjectId="268885"/>
    <cge:TPSR_Ref TObjectID="43561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268889">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2039.000000 62.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43564" ObjectName="SW-CX_LL.CX_LL_362XC1"/>
     <cge:Meas_Ref ObjectId="268889"/>
    <cge:TPSR_Ref TObjectID="43564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268889">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2039.000000 -11.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43563" ObjectName="SW-CX_LL.CX_LL_362XC"/>
     <cge:Meas_Ref ObjectId="268889"/>
    <cge:TPSR_Ref TObjectID="43563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268890">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2034.000000 139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43565" ObjectName="SW-CX_LL.CX_LL_36267SW"/>
     <cge:Meas_Ref ObjectId="268890"/>
    <cge:TPSR_Ref TObjectID="43565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268844">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2180.000000 6.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43531" ObjectName="SW-CX_LL.CX_LL_3902XC"/>
     <cge:Meas_Ref ObjectId="268844"/>
    <cge:TPSR_Ref TObjectID="43531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2530.000000 56.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43573" ObjectName="SW-CX_LL.CX_LL_364XC1"/>
     <cge:Meas_Ref ObjectId="268900"/>
    <cge:TPSR_Ref TObjectID="43573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2530.000000 -17.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43572" ObjectName="SW-CX_LL.CX_LL_364XC"/>
     <cge:Meas_Ref ObjectId="268900"/>
    <cge:TPSR_Ref TObjectID="43572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268901">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2525.000000 133.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43574" ObjectName="SW-CX_LL.CX_LL_36467SW"/>
     <cge:Meas_Ref ObjectId="268901"/>
    <cge:TPSR_Ref TObjectID="43574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268903">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2549.000000 334.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43576" ObjectName="SW-CX_LL.CX_LL_3651SW"/>
     <cge:Meas_Ref ObjectId="268903"/>
    <cge:TPSR_Ref TObjectID="43576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268904">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2525.000000 342.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43578" ObjectName="SW-CX_LL.CX_LL_36567SW"/>
     <cge:Meas_Ref ObjectId="268904"/>
    <cge:TPSR_Ref TObjectID="43578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268894">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2327.000000 60.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43568" ObjectName="SW-CX_LL.CX_LL_363XC1"/>
     <cge:Meas_Ref ObjectId="268894"/>
    <cge:TPSR_Ref TObjectID="43568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268894">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2327.000000 -13.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43567" ObjectName="SW-CX_LL.CX_LL_363XC"/>
     <cge:Meas_Ref ObjectId="268894"/>
    <cge:TPSR_Ref TObjectID="43567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268895">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2322.000000 137.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43569" ObjectName="SW-CX_LL.CX_LL_36367SW"/>
     <cge:Meas_Ref ObjectId="268895"/>
    <cge:TPSR_Ref TObjectID="43569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268896">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2292.000000 338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43570" ObjectName="SW-CX_LL.CX_LL_020SW"/>
     <cge:Meas_Ref ObjectId="268896"/>
    <cge:TPSR_Ref TObjectID="43570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268913">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3001.000000 61.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43585" ObjectName="SW-CX_LL.CX_LL_367XC1"/>
     <cge:Meas_Ref ObjectId="268913"/>
    <cge:TPSR_Ref TObjectID="43585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268913">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3001.000000 -12.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43584" ObjectName="SW-CX_LL.CX_LL_367XC"/>
     <cge:Meas_Ref ObjectId="268913"/>
    <cge:TPSR_Ref TObjectID="43584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268914">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2996.000000 138.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43586" ObjectName="SW-CX_LL.CX_LL_36767SW"/>
     <cge:Meas_Ref ObjectId="268914"/>
    <cge:TPSR_Ref TObjectID="43586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268918">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3196.000000 61.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43589" ObjectName="SW-CX_LL.CX_LL_368XC1"/>
     <cge:Meas_Ref ObjectId="268918"/>
    <cge:TPSR_Ref TObjectID="43589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268918">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3196.000000 -12.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43588" ObjectName="SW-CX_LL.CX_LL_368XC"/>
     <cge:Meas_Ref ObjectId="268918"/>
    <cge:TPSR_Ref TObjectID="43588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268919">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3191.000000 138.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43590" ObjectName="SW-CX_LL.CX_LL_36867SW"/>
     <cge:Meas_Ref ObjectId="268919"/>
    <cge:TPSR_Ref TObjectID="43590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268908">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2776.000000 61.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43581" ObjectName="SW-CX_LL.CX_LL_366XC1"/>
     <cge:Meas_Ref ObjectId="268908"/>
    <cge:TPSR_Ref TObjectID="43581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268908">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2776.000000 -12.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43580" ObjectName="SW-CX_LL.CX_LL_366XC"/>
     <cge:Meas_Ref ObjectId="268908"/>
    <cge:TPSR_Ref TObjectID="43580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268909">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2754.000000 195.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43582" ObjectName="SW-CX_LL.CX_LL_36667SW"/>
     <cge:Meas_Ref ObjectId="268909"/>
    <cge:TPSR_Ref TObjectID="43582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2274.000000 -916.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2274.000000 -745.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268924">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1746.000000 44.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43594" ObjectName="SW-CX_LL.CX_LL_31267SW"/>
     <cge:Meas_Ref ObjectId="268924"/>
    <cge:TPSR_Ref TObjectID="43594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268818">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 930.000000 -667.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43511" ObjectName="SW-CX_LL.CX_LL_2916SW"/>
     <cge:Meas_Ref ObjectId="268818"/>
    <cge:TPSR_Ref TObjectID="43511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268819">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 -714.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43512" ObjectName="SW-CX_LL.CX_LL_29167SW"/>
     <cge:Meas_Ref ObjectId="268819"/>
    <cge:TPSR_Ref TObjectID="43512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268821">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 930.000000 -550.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43514" ObjectName="SW-CX_LL.CX_LL_2911SW"/>
     <cge:Meas_Ref ObjectId="268821"/>
    <cge:TPSR_Ref TObjectID="43514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268822">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 880.000000 -596.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43515" ObjectName="SW-CX_LL.CX_LL_29117SW"/>
     <cge:Meas_Ref ObjectId="268822"/>
    <cge:TPSR_Ref TObjectID="43515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268823">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 880.000000 -541.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43516" ObjectName="SW-CX_LL.CX_LL_29110SW"/>
     <cge:Meas_Ref ObjectId="268823"/>
    <cge:TPSR_Ref TObjectID="43516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268820">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 879.000000 -653.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43513" ObjectName="SW-CX_LL.CX_LL_29160SW"/>
     <cge:Meas_Ref ObjectId="268820"/>
    <cge:TPSR_Ref TObjectID="43513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268832">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 -615.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43520" ObjectName="SW-CX_LL.CX_LL_19160SW"/>
     <cge:Meas_Ref ObjectId="268832"/>
    <cge:TPSR_Ref TObjectID="43520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268834">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 -552.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43522" ObjectName="SW-CX_LL.CX_LL_19117SW"/>
     <cge:Meas_Ref ObjectId="268834"/>
    <cge:TPSR_Ref TObjectID="43522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268831">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 -683.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43519" ObjectName="SW-CX_LL.CX_LL_19167SW"/>
     <cge:Meas_Ref ObjectId="268831"/>
    <cge:TPSR_Ref TObjectID="43519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268835">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 -487.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43523" ObjectName="SW-CX_LL.CX_LL_19110SW"/>
     <cge:Meas_Ref ObjectId="268835"/>
    <cge:TPSR_Ref TObjectID="43523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268830">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1112.000000 -633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43518" ObjectName="SW-CX_LL.CX_LL_1916SW"/>
     <cge:Meas_Ref ObjectId="268830"/>
    <cge:TPSR_Ref TObjectID="43518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268833">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1112.000000 -501.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43521" ObjectName="SW-CX_LL.CX_LL_1911SW"/>
     <cge:Meas_Ref ObjectId="268833"/>
    <cge:TPSR_Ref TObjectID="43521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268839">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 -414.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43524" ObjectName="SW-CX_LL.CX_LL_2010SW"/>
     <cge:Meas_Ref ObjectId="268839"/>
    <cge:TPSR_Ref TObjectID="43524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-268840">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 981.000000 -327.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43525" ObjectName="SW-CX_LL.CX_LL_1010SW"/>
     <cge:Meas_Ref ObjectId="268840"/>
    <cge:TPSR_Ref TObjectID="43525"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_212d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c5b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c5b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c5b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c5b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c5b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c5b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c5b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2119f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -148.000000 -929.500000) translate(0,16)">立竜光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eedd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -102.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ebdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -605.000000) translate(0,12)">       1号联变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ebdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -605.000000) translate(0,27)">SSZ11-150000/220(带平衡绕组)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ebdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -605.000000) translate(0,42)">230±8×1.25%/115/36kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ebdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -605.000000) translate(0,57)">YN,yn0,yn0+d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ebdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -605.000000) translate(0,72)">U1,3=24% U1,2=14%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ebdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -605.000000) translate(0,87)">U2,3=8%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b9fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -812.000000) translate(0,12)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ba4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 888.000000 -941.000000) translate(0,12)">C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -942.000000) translate(0,12)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d56d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 -944.000000) translate(0,12)">B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2074360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1069.000000 -938.000000) translate(0,12)">C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2074990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -937.000000) translate(0,12)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2074bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1118.000000 -937.000000) translate(0,12)">B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2074e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1067.000000 -979.000000) translate(0,16)">110kV竜鲁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f8f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2232.000000 -369.000000) translate(0,16)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f9c1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1710.000000 -100.000000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa2b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1714.000000 -2.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 131.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fa5230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3177.000000 313.000000) translate(0,15)">预留回路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fa6120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2962.000000 313.000000) translate(0,15)">预留储能回路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fa6aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2754.000000 313.000000) translate(0,15)">立维线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2174db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2454.000000 610.000000) translate(0,15)">35kV2号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2176e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2001.000000 301.000000) translate(0,15)">5号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2176e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2001.000000 301.000000) translate(0,33)">1、9、28号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2176e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2001.000000 301.000000) translate(0,51)">17-20号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2176e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2001.000000 301.000000) translate(0,69)"> 20.75MWP</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217a150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 431.000000) translate(0,15)">2号接地变及接地电阻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217b220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2122.000000 168.000000) translate(0,15)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217c270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1865.000000 308.000000) translate(0,15)">4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217c270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1865.000000 308.000000) translate(0,33)">2-8号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217c270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1865.000000 308.000000) translate(0,51)"> 21.4MWP</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217c970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1329.000000 620.000000) translate(0,15)">35kV1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217cbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 421.000000) translate(0,15)">1号接地变兼站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217cbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 421.000000) translate(0,33)">   及接地电阻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217d770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 869.000000 171.000000) translate(0,15)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217d9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 310.000000) translate(0,15)">3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217d9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 310.000000) translate(0,33)">10-12号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217d9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 310.000000) translate(0,51)">13-16号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217d9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 310.000000) translate(0,69)"> 20.1MWP</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217df60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 529.000000 306.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217df60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 529.000000 306.000000) translate(0,33)">21-23号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217df60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 529.000000 306.000000) translate(0,51)">25-27号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217df60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 529.000000 306.000000) translate(0,69)"> 18.25MWP</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 303.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 303.000000) translate(0,33)"> 24号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 303.000000) translate(0,51)">29-34号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_217e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 303.000000) translate(0,69)"> 20.1MWP</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 345.000000) translate(0,12)">DKSC-1100/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 345.000000) translate(0,27)">-400/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 345.000000) translate(0,42)">ZN,yn11 Ud=6%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 345.000000) translate(0,57)">37±2×2.5%/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217f070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2146.000000 332.000000) translate(0,12)">DKSC-650/37kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217f070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2146.000000 332.000000) translate(0,27)">-400/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217f070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2146.000000 332.000000) translate(0,42)">ZN,yn11 Ud=6%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217f070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2146.000000 332.000000) translate(0,57)">37±2×2.5%/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_217fe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2176.000000 -983.000000) translate(0,16)">10kV立竜1号变支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_2185190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2236.000000 -932.000000) translate(0,8)">01号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_2185f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2237.000000 -867.000000) translate(0,8)">02号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2189c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.000000 -817.000000) translate(0,12)">A02</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0a9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2288.000000 -724.000000) translate(0,12)">A032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f10620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2284.000000 -612.000000) translate(0,12)">A03</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1f10d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2235.000000 -544.000000) translate(0,8)">18号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f12b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2387.000000 -563.000000) translate(0,12)">2号站用变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f12b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2387.000000 -563.000000) translate(0,27)">S13-M-500/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f12b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2387.000000 -563.000000) translate(0,42)">Dyn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f12b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2387.000000 -563.000000) translate(0,57)">U1,2=3.92%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f13a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1226.000000 -773.000000) translate(0,12)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,16)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,36)">立</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,56)">竜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,76)">光</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,96)">伏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,116)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,136)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,156)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,176)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,196)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,216)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,236)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,256)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2154.000000 -838.000000) translate(0,276)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f14bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2654.000000 253.000000) translate(0,15)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f14f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 -199.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f15220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -357.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f15460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 695.000000 -444.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f156a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 884.000000 -535.000000) translate(0,12)">29110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f158e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 -580.000000) translate(0,12)">2911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f15b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 882.000000 -589.000000) translate(0,12)">29117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f15d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -637.000000) translate(0,12)">291</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f15fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 880.000000 -649.000000) translate(0,12)">29160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f161e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 -689.000000) translate(0,12)">2916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f16420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 881.000000 -709.000000) translate(0,12)">29167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f16660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1146.000000 -481.000000) translate(0,12)">19110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f168a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -531.000000) translate(0,12)">1911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f16ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1144.000000 -547.000000) translate(0,12)">19117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f16d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 -595.000000) translate(0,12)">191</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f16f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1141.000000 -608.000000) translate(0,12)">19160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f171a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -663.000000) translate(0,12)">1916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f173e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1141.000000 -680.000000) translate(0,12)">19167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f17620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 320.000000 1.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f17860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 296.000000 156.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f17aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 3.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f17ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 510.000000 158.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f17f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 1.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f18160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 161.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f183a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 889.000000 -15.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f185e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2144.000000 -12.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f18820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1144.000000 -1.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f18a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1118.000000 154.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f18ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1093.000000 305.000000) translate(0,12)">010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f18ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1369.000000 -0.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f19120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1339.000000 155.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f19360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1369.000000 400.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f195a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 360.000000) translate(0,12)">35667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f197e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1356.000000 302.000000) translate(0,12)">3561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f19a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1467.000000 4.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f1ede0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1871.000000 1.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f1f2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1844.000000 155.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f1f510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2007.000000 3.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f1f750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1985.000000 154.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f1f990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2296.000000 1.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f1fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 156.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f1fe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 305.000000) translate(0,12)">020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f20050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2504.000000 -6.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f20290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 150.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f204d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2500.000000 401.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f20710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2482.000000 360.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f20950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2501.000000 302.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f20b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2752.000000 1.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f20dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2700.000000 162.000000) translate(0,12)">36667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f21010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2968.000000 -0.000000) translate(0,12)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f21250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 156.000000) translate(0,12)">36767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f21490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3167.000000 2.000000) translate(0,12)">368</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f216d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 155.000000) translate(0,12)">36867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f29000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -366.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2b370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 846.000000 -404.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f39770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -978.000000) translate(0,16)">220kV竜腊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" graphid="g_1ffa9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -142.000000 -6.000000) translate(0,21)">6011259</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2124a70" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1342.000000 151.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2007e20" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1342.000000 360.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c6720" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1117.000000 151.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_209f410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1120.000000 410.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2112b50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.000000 -306.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2098d80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -305.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20eabe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 762.000000 -391.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20eb4d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 729.000000 -389.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1feeeb0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 871.000000 -540.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff0000" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 871.000000 -595.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff0ec0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 870.000000 -652.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff1db0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 872.000000 -713.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20bb290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1193.000000 -486.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20bc2f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1193.000000 -551.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2105940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1193.000000 -614.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2106cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1193.000000 -682.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_201e5e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 698.000000 154.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b4030" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 508.000000 153.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_207dcc0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 296.000000 154.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f70b60" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1841.000000 154.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fdb720" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1986.000000 154.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe8870" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2477.000000 148.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb51e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2477.000000 357.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc4f00" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2274.000000 152.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fca620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2277.000000 414.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2034ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2948.000000 153.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd0420" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3143.000000 153.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f98a20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2739.000000 222.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LL"/>
</svg>