<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-226" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-686 -1417 1987 1415">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape25">
    <polyline arcFlag="1" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <polyline arcFlag="1" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <polyline arcFlag="1" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape159">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="9" x2="13" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="13" x2="13" y1="13" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="17" x2="13" y1="17" y2="13"/>
    <ellipse cx="12" cy="13" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="33" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="37" y2="33"/>
    <ellipse cx="12" cy="33" rx="11" ry="12" stroke-width="1.22172"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape97_0">
    <circle cx="17" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="12,78 19,78 16,85 12,78 "/>
   </symbol>
   <symbol id="transformer2:shape97_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="11" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f7afd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26910e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f7d610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f7e2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f7f550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f80170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f80bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f816f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f7c650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f7c650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f84ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f84ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f86850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f86850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2f87870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f89470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f8a060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f8ae20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f8b760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f8ce20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f8da00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f8e2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f8ea80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f8fb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f904e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f90eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f91870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f92cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f93890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f948c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f95500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fa3cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f96ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2f97d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2f992e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1425" width="1997" x="-691" y="-1422"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1101" x2="1132" y1="-267" y2="-267"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-184576">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 376.241796 -913.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28032" ObjectName="SW-DY_SCH.DY_SCH_301BK"/>
     <cge:Meas_Ref ObjectId="184576"/>
    <cge:TPSR_Ref TObjectID="28032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184586">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 376.241796 -549.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28035" ObjectName="SW-DY_SCH.DY_SCH_001BK"/>
     <cge:Meas_Ref ObjectId="184586"/>
    <cge:TPSR_Ref TObjectID="28035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -211.787729 -371.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28062" ObjectName="SW-DY_SCH.DY_SCH_051BK"/>
     <cge:Meas_Ref ObjectId="184772"/>
    <cge:TPSR_Ref TObjectID="28062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308258">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 321.000000 -1099.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47937" ObjectName="SW-DY_SCH.DY_SCH_391BK"/>
     <cge:Meas_Ref ObjectId="308258"/>
    <cge:TPSR_Ref TObjectID="47937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308318">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 -1100.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47941" ObjectName="SW-DY_SCH.DY_SCH_392BK"/>
     <cge:Meas_Ref ObjectId="308318"/>
    <cge:TPSR_Ref TObjectID="47941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184748">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 23.212271 -364.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28058" ObjectName="SW-DY_SCH.DY_SCH_052BK"/>
     <cge:Meas_Ref ObjectId="184748"/>
    <cge:TPSR_Ref TObjectID="28058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184724">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 201.212271 -365.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28054" ObjectName="SW-DY_SCH.DY_SCH_053BK"/>
     <cge:Meas_Ref ObjectId="184724"/>
    <cge:TPSR_Ref TObjectID="28054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.212271 -372.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28042" ObjectName="SW-DY_SCH.DY_SCH_056BK"/>
     <cge:Meas_Ref ObjectId="184652"/>
    <cge:TPSR_Ref TObjectID="28042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308371">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1126.212271 -376.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47930" ObjectName="SW-DY_SCH.DY_SCH_057BK"/>
     <cge:Meas_Ref ObjectId="308371"/>
    <cge:TPSR_Ref TObjectID="47930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184700">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 390.212271 -365.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28050" ObjectName="SW-DY_SCH.DY_SCH_054BK"/>
     <cge:Meas_Ref ObjectId="184700"/>
    <cge:TPSR_Ref TObjectID="28050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.212271 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28046" ObjectName="SW-DY_SCH.DY_SCH_055BK"/>
     <cge:Meas_Ref ObjectId="184676"/>
    <cge:TPSR_Ref TObjectID="28046"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_32c9b70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 960.000000 -1200.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c6f10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 404.000000 -1261.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d7450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.000000 -1262.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e6670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.000000 -690.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_SCH" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dashiheTsch" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="713,-1380 713,-1335 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38090" ObjectName="AC-35kV.LN_dashiheTsch"/>
    <cge:TPSR_Ref TObjectID="38090_SS-226"/></metadata>
   <polyline fill="none" opacity="0" points="713,-1380 713,-1335 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_SCH.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -211.238710 -202.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34239" ObjectName="EC-DY_SCH.051Ld"/>
    <cge:TPSR_Ref TObjectID="34239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_SCH.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.761290 -195.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34238" ObjectName="EC-DY_SCH.052Ld"/>
    <cge:TPSR_Ref TObjectID="34238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_SCH.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 200.761290 -196.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34237" ObjectName="EC-DY_SCH.053Ld"/>
    <cge:TPSR_Ref TObjectID="34237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_SCH.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.761290 -196.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34236" ObjectName="EC-DY_SCH.054Ld"/>
    <cge:TPSR_Ref TObjectID="34236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_SCH.055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.761290 -181.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34235" ObjectName="EC-DY_SCH.055Ld"/>
    <cge:TPSR_Ref TObjectID="34235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.761290 -196.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_31640b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 469.000000 -646.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3186bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.000000 -571.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c5750" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 405.000000 -1220.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d5c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 788.000000 -1221.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e1ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -137.000000 -359.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34f2fe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98.000000 -352.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35017e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 -353.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_350df10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 465.000000 -353.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_351a3e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 738.000000 -370.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3454930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -353.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_346c720" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 634.000000 -157.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347a6f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -290.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347b180" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1075.000000 -281.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347cd70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1057.000000 -353.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2691570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-960 385,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28033@0" ObjectIDZND0="28032@1" Pin0InfoVect0LinkObjId="SW-184576_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-960 385,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2596eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-996 385,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28033@1" ObjectIDZND0="28029@0" Pin0InfoVect0LinkObjId="g_32ceca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-996 385,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-921 385,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28032@0" ObjectIDZND0="28034@1" Pin0InfoVect0LinkObjId="SW-184579_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184576_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-921 385,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f45600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-661 385,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36bf010@1" ObjectIDZND0="28037@x" ObjectIDZND1="28038@x" Pin0InfoVect0LinkObjId="SW-184589_0" Pin0InfoVect1LinkObjId="SW-184590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36bf010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="385,-661 385,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f7cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-652 473,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28038@1" ObjectIDZND0="g_31640b0@0" Pin0InfoVect0LinkObjId="g_31640b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-652 473,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c1e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-600 385,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28037@0" ObjectIDZND0="28035@1" Pin0InfoVect0LinkObjId="SW-184586_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184589_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-600 385,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2590ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-557 385,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28035@0" ObjectIDZND0="28036@1" Pin0InfoVect0LinkObjId="SW-184588_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184586_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-557 385,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3abb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-508 385,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28036@0" ObjectIDZND0="28030@0" Pin0InfoVect0LinkObjId="g_346d410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184588_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-508 385,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e82dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-202,-431 -202,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28063@0" ObjectIDZND0="28062@1" Pin0InfoVect0LinkObjId="SW-184772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-202,-431 -202,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3195be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-487 801,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28030@0" ObjectIDZND0="28040@0" Pin0InfoVect0LinkObjId="SW-184646_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3abb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-487 801,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3186950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="882,-577 898,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28041@1" ObjectIDZND0="g_3186bb0@0" Pin0InfoVect0LinkObjId="g_3186bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184647_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="882,-577 898,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c5d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-643 385,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_36bf010@0" ObjectIDND1="28038@x" ObjectIDZND0="28037@1" Pin0InfoVect0LinkObjId="SW-184589_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36bf010_0" Pin1InfoVect1LinkObjId="SW-184590_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-643 385,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c5f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="418,-652 385,-652 385,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="28038@0" ObjectIDZND0="28037@x" ObjectIDZND1="g_36bf010@0" Pin0InfoVect0LinkObjId="SW-184589_0" Pin0InfoVect1LinkObjId="g_36bf010_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="418,-652 385,-652 385,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32ceca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-1047 976,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28039@0" ObjectIDZND0="28029@0" Pin0InfoVect0LinkObjId="g_2596eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184645_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-1047 976,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32d54b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-1175 101,-1148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_32d16c0@0" ObjectIDZND0="g_32d22e0@0" Pin0InfoVect0LinkObjId="g_32d22e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32d16c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-1175 101,-1148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32d5710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-1234 101,-1214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_32d16c0@1" Pin0InfoVect0LinkObjId="g_32d16c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c9b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-1234 101,-1214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32d66e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="123,-1097 101,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_32d5970@0" ObjectIDZND0="g_32d22e0@0" ObjectIDZND1="47945@x" Pin0InfoVect0LinkObjId="g_32d22e0_0" Pin0InfoVect1LinkObjId="SW-308723_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32d5970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="123,-1097 101,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32d71d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-1116 101,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_32d22e0@1" ObjectIDZND0="g_32d5970@0" ObjectIDZND1="47945@x" Pin0InfoVect0LinkObjId="g_32d5970_0" Pin0InfoVect1LinkObjId="SW-308723_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32d22e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="101,-1116 101,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32d7430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-1097 101,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_32d5970@0" ObjectIDND1="g_32d22e0@0" ObjectIDZND0="47945@1" Pin0InfoVect0LinkObjId="SW-308723_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32d5970_0" Pin1InfoVect1LinkObjId="g_32d22e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-1097 101,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32d7690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-1047 101,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47945@0" ObjectIDZND0="28029@0" Pin0InfoVect0LinkObjId="g_2596eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308723_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-1047 101,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-1044 330,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47938@0" ObjectIDZND0="28029@0" Pin0InfoVect0LinkObjId="g_2596eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308259_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="330,-1044 330,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c84a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-1107 330,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47937@0" ObjectIDZND0="47938@1" Pin0InfoVect0LinkObjId="SW-308259_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308258_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="330,-1107 330,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c8cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-1162 330,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47939@0" ObjectIDZND0="47937@1" Pin0InfoVect0LinkObjId="SW-308258_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="330,-1162 330,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c9780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-1226 398,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34c5750@0" ObjectIDZND0="47940@1" Pin0InfoVect0LinkObjId="SW-308261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c5750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-1226 398,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c99e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,-1226 330,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47940@0" ObjectIDZND0="47939@x" ObjectIDZND1="g_34c7940@0" ObjectIDZND2="g_34c61e0@0" Pin0InfoVect0LinkObjId="SW-308260_0" Pin0InfoVect1LinkObjId="g_34c7940_0" Pin0InfoVect2LinkObjId="g_34c61e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="362,-1226 330,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ca4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-1198 330,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47939@1" ObjectIDZND0="47940@x" ObjectIDZND1="g_34c7940@0" ObjectIDZND2="g_34c61e0@0" Pin0InfoVect0LinkObjId="SW-308261_0" Pin0InfoVect1LinkObjId="g_34c7940_0" Pin0InfoVect2LinkObjId="g_34c61e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="330,-1198 330,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ca730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="350,-1270 330,-1270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34c7940@0" ObjectIDZND0="47940@x" ObjectIDZND1="47939@x" ObjectIDZND2="g_34c61e0@0" Pin0InfoVect0LinkObjId="SW-308261_0" Pin0InfoVect1LinkObjId="SW-308260_0" Pin0InfoVect2LinkObjId="g_34c61e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c7940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="350,-1270 330,-1270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34cb220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-1226 330,-1270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47940@x" ObjectIDND1="47939@x" ObjectIDZND0="g_34c7940@0" ObjectIDZND1="g_34c61e0@0" Pin0InfoVect0LinkObjId="g_34c7940_0" Pin0InfoVect1LinkObjId="g_34c61e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308261_0" Pin1InfoVect1LinkObjId="SW-308260_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="330,-1226 330,-1270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34cb480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-1270 381,-1270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_34c6f10@0" ObjectIDZND0="g_34c7940@1" Pin0InfoVect0LinkObjId="g_34c7940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c6f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-1270 381,-1270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34cb6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="297,-1268 297,-1297 330,-1297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_34c61e0@0" ObjectIDZND0="g_34c7940@0" ObjectIDZND1="47940@x" ObjectIDZND2="47939@x" Pin0InfoVect0LinkObjId="g_34c7940_0" Pin0InfoVect1LinkObjId="SW-308261_0" Pin0InfoVect2LinkObjId="SW-308260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c61e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="297,-1268 297,-1297 330,-1297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34cc1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-1270 330,-1297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_34c7940@0" ObjectIDND1="47940@x" ObjectIDND2="47939@x" ObjectIDZND0="g_34c61e0@0" Pin0InfoVect0LinkObjId="g_34c61e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34c7940_0" Pin1InfoVect1LinkObjId="SW-308261_0" Pin1InfoVect2LinkObjId="SW-308260_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="330,-1270 330,-1297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34cc430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-1297 330,-1338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_34c61e0@0" ObjectIDND1="g_34c7940@0" ObjectIDND2="47940@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34c61e0_0" Pin1InfoVect1LinkObjId="g_34c7940_0" Pin1InfoVect2LinkObjId="SW-308261_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="330,-1297 330,-1338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d8780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-1108 713,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47941@0" ObjectIDZND0="47942@1" Pin0InfoVect0LinkObjId="SW-308319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-1108 713,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d89e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-1163 713,-1135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47943@0" ObjectIDZND0="47941@1" Pin0InfoVect0LinkObjId="SW-308318_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-1163 713,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d8e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="792,-1227 781,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34d5c90@0" ObjectIDZND0="47944@1" Pin0InfoVect0LinkObjId="SW-308321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d5c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="792,-1227 781,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d90e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="745,-1227 713,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47944@0" ObjectIDZND0="47943@x" ObjectIDZND1="g_34d7e80@0" ObjectIDZND2="g_34d6720@0" Pin0InfoVect0LinkObjId="SW-308320_0" Pin0InfoVect1LinkObjId="g_34d7e80_0" Pin0InfoVect2LinkObjId="g_34d6720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="745,-1227 713,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d9340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-1199 713,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47943@1" ObjectIDZND0="47944@x" ObjectIDZND1="g_34d7e80@0" ObjectIDZND2="g_34d6720@0" Pin0InfoVect0LinkObjId="SW-308321_0" Pin0InfoVect1LinkObjId="g_34d7e80_0" Pin0InfoVect2LinkObjId="g_34d6720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="713,-1199 713,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d95a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="733,-1271 713,-1271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34d7e80@0" ObjectIDZND0="47943@x" ObjectIDZND1="47944@x" ObjectIDZND2="g_34d6720@0" Pin0InfoVect0LinkObjId="SW-308320_0" Pin0InfoVect1LinkObjId="SW-308321_0" Pin0InfoVect2LinkObjId="g_34d6720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d7e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="733,-1271 713,-1271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d9800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-1227 713,-1271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="47943@x" ObjectIDND1="47944@x" ObjectIDZND0="g_34d7e80@0" ObjectIDZND1="g_34d6720@0" ObjectIDZND2="38090@1" Pin0InfoVect0LinkObjId="g_34d7e80_0" Pin0InfoVect1LinkObjId="g_34d6720_0" Pin0InfoVect2LinkObjId="g_34da180_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308320_0" Pin1InfoVect1LinkObjId="SW-308321_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="713,-1227 713,-1271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d9a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="792,-1271 764,-1271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_34d7450@0" ObjectIDZND0="g_34d7e80@1" Pin0InfoVect0LinkObjId="g_34d7e80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d7450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="792,-1271 764,-1271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d9cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="680,-1269 680,-1298 713,-1298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34d6720@0" ObjectIDZND0="47943@x" ObjectIDZND1="47944@x" ObjectIDZND2="g_34d7e80@0" Pin0InfoVect0LinkObjId="SW-308320_0" Pin0InfoVect1LinkObjId="SW-308321_0" Pin0InfoVect2LinkObjId="g_34d7e80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d6720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="680,-1269 680,-1298 713,-1298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d9f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-1271 713,-1298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="47943@x" ObjectIDND1="47944@x" ObjectIDND2="g_34d7e80@0" ObjectIDZND0="g_34d6720@0" ObjectIDZND1="38090@1" Pin0InfoVect0LinkObjId="g_34d6720_0" Pin0InfoVect1LinkObjId="g_34da180_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-308320_0" Pin1InfoVect1LinkObjId="SW-308321_0" Pin1InfoVect2LinkObjId="g_34d7e80_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-1271 713,-1298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34da180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-1298 713,-1339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="47943@x" ObjectIDND1="47944@x" ObjectIDND2="g_34d7e80@0" ObjectIDZND0="38090@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-308320_0" Pin1InfoVect1LinkObjId="SW-308321_0" Pin1InfoVect2LinkObjId="g_34d7e80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-1298 713,-1339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34da3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-1045 713,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47942@0" ObjectIDZND0="28029@0" Pin0InfoVect0LinkObjId="g_2596eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308319_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-1045 713,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34dcbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-873 385,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="28034@0" ObjectIDZND0="28068@1" Pin0InfoVect0LinkObjId="g_34ddb80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184579_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-873 385,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34dce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="356,-723 385,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_32c28c0@0" ObjectIDZND0="g_36bf010@0" ObjectIDZND1="28068@x" Pin0InfoVect0LinkObjId="g_36bf010_0" Pin0InfoVect1LinkObjId="g_34dcbd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c28c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="356,-723 385,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34dd920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-713 385,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_36bf010@0" ObjectIDZND0="g_32c28c0@0" ObjectIDZND1="28068@x" Pin0InfoVect0LinkObjId="g_32c28c0_0" Pin0InfoVect1LinkObjId="g_34dcbd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36bf010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="385,-713 385,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34ddb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-723 385,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_32c28c0@0" ObjectIDND1="g_36bf010@0" ObjectIDZND0="28068@0" Pin0InfoVect0LinkObjId="g_34dcbd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32c28c0_0" Pin1InfoVect1LinkObjId="g_36bf010_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-723 385,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34deb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-810 304,-821 384,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_34ddde0@0" ObjectIDZND0="28068@x" Pin0InfoVect0LinkObjId="g_34dcbd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34ddde0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-810 304,-821 384,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e2730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-365 -143,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34e1ca0@0" ObjectIDZND0="28065@1" Pin0InfoVect0LinkObjId="SW-184776_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e1ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-365 -143,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e2990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-202,-295 -202,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28064@1" ObjectIDZND0="g_34e14e0@0" Pin0InfoVect0LinkObjId="g_34e14e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184775_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-202,-295 -202,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e2bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-179,-365 -202,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="28065@0" ObjectIDZND0="g_34e14e0@0" ObjectIDZND1="28062@x" Pin0InfoVect0LinkObjId="g_34e14e0_0" Pin0InfoVect1LinkObjId="SW-184772_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-179,-365 -202,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e36e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-202,-353 -202,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_34e14e0@1" ObjectIDZND0="28065@x" ObjectIDZND1="28062@x" Pin0InfoVect0LinkObjId="SW-184776_0" Pin0InfoVect1LinkObjId="SW-184772_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e14e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-202,-353 -202,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e3940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-202,-365 -202,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="28065@x" ObjectIDND1="g_34e14e0@0" ObjectIDZND0="28062@0" Pin0InfoVect0LinkObjId="SW-184772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-184776_0" Pin1InfoVect1LinkObjId="g_34e14e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-202,-365 -202,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e4910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-244 -202,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_34e3ba0@0" ObjectIDZND0="34239@x" ObjectIDZND1="28064@x" Pin0InfoVect0LinkObjId="EC-DY_SCH.051Ld_0" Pin0InfoVect1LinkObjId="SW-184775_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e3ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-244 -202,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-202,-229 -202,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34239@0" ObjectIDZND0="g_34e3ba0@0" ObjectIDZND1="28064@x" Pin0InfoVect0LinkObjId="g_34e3ba0_0" Pin0InfoVect1LinkObjId="SW-184775_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_SCH.051Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-202,-229 -202,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-202,-244 -202,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_34e3ba0@0" ObjectIDND1="34239@x" ObjectIDZND0="28064@0" Pin0InfoVect0LinkObjId="SW-184775_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34e3ba0_0" Pin1InfoVect1LinkObjId="EC-DY_SCH.051Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-202,-244 -202,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34ef200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="32,-424 32,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28059@0" ObjectIDZND0="28058@1" Pin0InfoVect0LinkObjId="SW-184748_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="32,-424 32,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f3a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="102,-358 91,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34f2fe0@0" ObjectIDZND0="28061@1" Pin0InfoVect0LinkObjId="SW-184752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34f2fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="102,-358 91,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f3cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="32,-288 32,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28060@1" ObjectIDZND0="g_34f23c0@0" Pin0InfoVect0LinkObjId="g_34f23c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184751_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="32,-288 32,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f3f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="55,-358 32,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="28061@0" ObjectIDZND0="g_34f23c0@0" ObjectIDZND1="28058@x" Pin0InfoVect0LinkObjId="g_34f23c0_0" Pin0InfoVect1LinkObjId="SW-184748_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="55,-358 32,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f4190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="32,-346 32,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_34f23c0@1" ObjectIDZND0="28061@x" ObjectIDZND1="28058@x" Pin0InfoVect0LinkObjId="SW-184752_0" Pin0InfoVect1LinkObjId="SW-184748_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34f23c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="32,-346 32,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f43f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="32,-358 32,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_34f23c0@0" ObjectIDND1="28061@x" ObjectIDZND0="28058@0" Pin0InfoVect0LinkObjId="SW-184748_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34f23c0_0" Pin1InfoVect1LinkObjId="SW-184752_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="32,-358 32,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f53c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="47,-237 32,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_34f4650@0" ObjectIDZND0="34238@x" ObjectIDZND1="28060@x" Pin0InfoVect0LinkObjId="EC-DY_SCH.052Ld_0" Pin0InfoVect1LinkObjId="SW-184751_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34f4650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="47,-237 32,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f5620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="32,-222 32,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34238@0" ObjectIDZND0="g_34f4650@0" ObjectIDZND1="28060@x" Pin0InfoVect0LinkObjId="g_34f4650_0" Pin0InfoVect1LinkObjId="SW-184751_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_SCH.052Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="32,-222 32,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f5880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="32,-237 32,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34238@x" ObjectIDND1="g_34f4650@0" ObjectIDZND0="28060@0" Pin0InfoVect0LinkObjId="SW-184751_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-DY_SCH.052Ld_0" Pin1InfoVect1LinkObjId="g_34f4650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="32,-237 32,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34fda00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="210,-425 210,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28055@0" ObjectIDZND0="28054@1" Pin0InfoVect0LinkObjId="SW-184724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="210,-425 210,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3502270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="280,-359 269,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_35017e0@0" ObjectIDZND0="28057@1" Pin0InfoVect0LinkObjId="SW-184728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35017e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="280,-359 269,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35024d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="210,-289 210,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28056@1" ObjectIDZND0="g_3500bc0@0" Pin0InfoVect0LinkObjId="g_3500bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="210,-289 210,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3502730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="233,-359 210,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="28057@0" ObjectIDZND0="g_3500bc0@0" ObjectIDZND1="28054@x" Pin0InfoVect0LinkObjId="g_3500bc0_0" Pin0InfoVect1LinkObjId="SW-184724_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="233,-359 210,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3502990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="210,-347 210,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_3500bc0@1" ObjectIDZND0="28057@x" ObjectIDZND1="28054@x" Pin0InfoVect0LinkObjId="SW-184728_0" Pin0InfoVect1LinkObjId="SW-184724_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3500bc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="210,-347 210,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3502bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="210,-359 210,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_3500bc0@0" ObjectIDND1="28057@x" ObjectIDZND0="28054@0" Pin0InfoVect0LinkObjId="SW-184724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3500bc0_0" Pin1InfoVect1LinkObjId="SW-184728_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="210,-359 210,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3503bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-238 210,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3502e50@0" ObjectIDZND0="34237@x" ObjectIDZND1="28056@x" Pin0InfoVect0LinkObjId="EC-DY_SCH.053Ld_0" Pin0InfoVect1LinkObjId="SW-184727_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3502e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="225,-238 210,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3503e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="210,-223 210,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34237@0" ObjectIDZND0="g_3502e50@0" ObjectIDZND1="28056@x" Pin0InfoVect0LinkObjId="g_3502e50_0" Pin0InfoVect1LinkObjId="SW-184727_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_SCH.053Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="210,-223 210,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3504080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="210,-238 210,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34237@x" ObjectIDND1="g_3502e50@0" ObjectIDZND0="28056@0" Pin0InfoVect0LinkObjId="SW-184727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-DY_SCH.053Ld_0" Pin1InfoVect1LinkObjId="g_3502e50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="210,-238 210,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_350a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-425 399,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28051@0" ObjectIDZND0="28050@1" Pin0InfoVect0LinkObjId="SW-184700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184702_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-425 399,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_350e9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="469,-359 458,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_350df10@0" ObjectIDZND0="28053@1" Pin0InfoVect0LinkObjId="SW-184704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_350df10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="469,-359 458,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_350ec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-289 399,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28052@1" ObjectIDZND0="g_350d2f0@0" Pin0InfoVect0LinkObjId="g_350d2f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184703_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-289 399,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_350ee60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-359 399,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="28053@0" ObjectIDZND0="g_350d2f0@0" ObjectIDZND1="28050@x" Pin0InfoVect0LinkObjId="g_350d2f0_0" Pin0InfoVect1LinkObjId="SW-184700_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="422,-359 399,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_350f0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-347 399,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_350d2f0@1" ObjectIDZND0="28053@x" ObjectIDZND1="28050@x" Pin0InfoVect0LinkObjId="SW-184704_0" Pin0InfoVect1LinkObjId="SW-184700_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_350d2f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="399,-347 399,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_350f320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-359 399,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_350d2f0@0" ObjectIDND1="28053@x" ObjectIDZND0="28050@0" Pin0InfoVect0LinkObjId="SW-184700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_350d2f0_0" Pin1InfoVect1LinkObjId="SW-184704_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-359 399,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35102f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-238 399,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_350f580@0" ObjectIDZND0="34236@x" ObjectIDZND1="28052@x" Pin0InfoVect0LinkObjId="EC-DY_SCH.054Ld_0" Pin0InfoVect1LinkObjId="SW-184703_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_350f580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="414,-238 399,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3510550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-223 399,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34236@0" ObjectIDZND0="g_350f580@0" ObjectIDZND1="28052@x" Pin0InfoVect0LinkObjId="g_350f580_0" Pin0InfoVect1LinkObjId="SW-184703_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_SCH.054Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="399,-223 399,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35107b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-238 399,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34236@x" ObjectIDND1="g_350f580@0" ObjectIDZND0="28052@0" Pin0InfoVect0LinkObjId="SW-184703_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-DY_SCH.054Ld_0" Pin1InfoVect1LinkObjId="g_350f580_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-238 399,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3450bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-425 896,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28043@0" ObjectIDZND0="28042@1" Pin0InfoVect0LinkObjId="SW-184652_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-425 896,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34553c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-359 955,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3454930@0" ObjectIDZND0="28045@1" Pin0InfoVect0LinkObjId="SW-184656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3454930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-359 955,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3455620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-289 896,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28044@1" ObjectIDZND0="g_3453d10@0" Pin0InfoVect0LinkObjId="g_3453d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184655_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-289 896,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3455880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="919,-359 896,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="28045@0" ObjectIDZND0="g_3453d10@0" ObjectIDZND1="28042@x" ObjectIDZND2="g_3485dd0@0" Pin0InfoVect0LinkObjId="g_3453d10_0" Pin0InfoVect1LinkObjId="SW-184652_0" Pin0InfoVect2LinkObjId="g_3485dd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="919,-359 896,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3455ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-347 896,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_3453d10@1" ObjectIDZND0="28045@x" ObjectIDZND1="28042@x" ObjectIDZND2="g_3485dd0@0" Pin0InfoVect0LinkObjId="SW-184656_0" Pin0InfoVect1LinkObjId="SW-184652_0" Pin0InfoVect2LinkObjId="g_3485dd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3453d10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="896,-347 896,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3456ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="911,-238 896,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3455d40@0" ObjectIDZND0="0@x" ObjectIDZND1="28044@x" Pin0InfoVect0LinkObjId="g_32c9b70_0" Pin0InfoVect1LinkObjId="SW-184655_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3455d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="911,-238 896,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3456d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-223 896,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3455d40@0" ObjectIDZND1="28044@x" Pin0InfoVect0LinkObjId="g_3455d40_0" Pin0InfoVect1LinkObjId="SW-184655_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c9b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-223 896,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3456f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-238 896,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3455d40@0" ObjectIDZND0="28044@0" Pin0InfoVect0LinkObjId="SW-184655_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32c9b70_0" Pin1InfoVect1LinkObjId="g_3455d40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-238 896,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345f0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-425 1135,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47931@0" ObjectIDZND0="47930@1" Pin0InfoVect0LinkObjId="SW-308371_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-425 1135,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345ff70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-289 1135,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="47932@1" ObjectIDZND0="g_345f350@0" Pin0InfoVect0LinkObjId="g_345f350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308373_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-289 1135,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3468860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="691,-230 674,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_351ae70@0" ObjectIDZND0="34235@x" ObjectIDZND1="0@x" ObjectIDZND2="28048@x" Pin0InfoVect0LinkObjId="EC-DY_SCH.055Ld_0" Pin0InfoVect1LinkObjId="g_32c9b70_0" Pin0InfoVect2LinkObjId="SW-184679_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_351ae70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="691,-230 674,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3469350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-208 673,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34235@0" ObjectIDZND0="g_351ae70@0" ObjectIDZND1="0@x" ObjectIDZND2="28048@x" Pin0InfoVect0LinkObjId="g_351ae70_0" Pin0InfoVect1LinkObjId="g_32c9b70_0" Pin0InfoVect2LinkObjId="SW-184679_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_SCH.055Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="673,-208 673,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-163 576,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="28067@0" ObjectIDZND0="28066@x" ObjectIDZND1="g_34885b0@0" Pin0InfoVect0LinkObjId="SW-184796_0" Pin0InfoVect1LinkObjId="g_34885b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="593,-163 576,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346d1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-163 629,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_346c720@0" ObjectIDZND0="28067@1" Pin0InfoVect0LinkObjId="SW-184797_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_346c720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-163 629,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346d410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-202,-467 -202,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28063@1" ObjectIDZND0="28030@0" Pin0InfoVect0LinkObjId="g_2e3abb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-202,-467 -202,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="32,-460 32,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28059@1" ObjectIDZND0="28030@0" Pin0InfoVect0LinkObjId="g_2e3abb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184750_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="32,-460 32,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346e470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="210,-461 210,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28055@1" ObjectIDZND0="28030@0" Pin0InfoVect0LinkObjId="g_2e3abb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="210,-461 210,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346eca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-461 399,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28051@1" ObjectIDZND0="28030@0" Pin0InfoVect0LinkObjId="g_2e3abb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184702_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-461 399,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346faa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-461 896,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28043@1" ObjectIDZND0="28030@0" Pin0InfoVect0LinkObjId="g_2e3abb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-461 896,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-250 1081,-110 1135,-110 1135,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" ObjectIDZND0="48073@0" Pin0InfoVect0LinkObjId="CB-DY_SCH.DY_SCH_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-250 1081,-110 1135,-110 1135,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347bc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-295 1105,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_347a6f0@0" ObjectIDZND0="47934@1" Pin0InfoVect0LinkObjId="SW-308375_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_347a6f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-295 1105,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347d800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1075,-359 1087,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_347cd70@0" ObjectIDZND0="47933@0" Pin0InfoVect0LinkObjId="SW-308374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_347cd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1075,-359 1087,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-369 1135,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_347c000@0" ObjectIDZND0="47930@x" ObjectIDZND1="47933@x" ObjectIDZND2="g_345f350@0" Pin0InfoVect0LinkObjId="SW-308371_0" Pin0InfoVect1LinkObjId="SW-308374_0" Pin0InfoVect2LinkObjId="g_345f350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_347c000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-369 1135,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347e550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-369 1135,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_347c000@0" ObjectIDND1="47933@x" ObjectIDND2="g_345f350@0" ObjectIDZND0="47930@0" Pin0InfoVect0LinkObjId="SW-308371_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_347c000_0" Pin1InfoVect1LinkObjId="SW-308374_0" Pin1InfoVect2LinkObjId="g_345f350_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-369 1135,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347e7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1123,-359 1135,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="47933@1" ObjectIDZND0="g_345f350@0" ObjectIDZND1="g_347c000@0" ObjectIDZND2="47930@x" Pin0InfoVect0LinkObjId="g_345f350_0" Pin0InfoVect1LinkObjId="g_347c000_0" Pin0InfoVect2LinkObjId="SW-308371_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1123,-359 1135,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347f2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-347 1135,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_345f350@1" ObjectIDZND0="47933@x" ObjectIDZND1="g_347c000@0" ObjectIDZND2="47930@x" Pin0InfoVect0LinkObjId="SW-308374_0" Pin0InfoVect1LinkObjId="g_347c000_0" Pin0InfoVect2LinkObjId="SW-308371_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_345f350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-347 1135,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347f500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-359 1135,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="47933@x" ObjectIDND1="g_345f350@0" ObjectIDZND0="g_347c000@0" ObjectIDZND1="47930@x" Pin0InfoVect0LinkObjId="g_347c000_0" Pin0InfoVect1LinkObjId="SW-308371_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308374_0" Pin1InfoVect1LinkObjId="g_345f350_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-359 1135,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34810c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-359 896,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_3453d10@0" ObjectIDND1="28045@x" ObjectIDZND0="28042@x" ObjectIDZND1="g_3485dd0@0" Pin0InfoVect0LinkObjId="SW-184652_0" Pin0InfoVect1LinkObjId="g_3485dd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3453d10_0" Pin1InfoVect1LinkObjId="SW-184656_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-359 896,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3481320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-367 896,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3453d10@0" ObjectIDND1="28045@x" ObjectIDND2="g_3485dd0@0" ObjectIDZND0="28042@0" Pin0InfoVect0LinkObjId="SW-184652_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3453d10_0" Pin1InfoVect1LinkObjId="SW-184656_0" Pin1InfoVect2LinkObjId="g_3485dd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-367 896,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3482290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1031,-1126 1031,-1107 976,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_32c8850@0" ObjectIDZND0="28039@x" ObjectIDZND1="g_3486a30@0" Pin0InfoVect0LinkObjId="SW-184645_0" Pin0InfoVect1LinkObjId="g_3486a30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c8850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1031,-1126 1031,-1107 976,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3482c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-1107 976,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_32c8850@0" ObjectIDND1="g_3486a30@0" ObjectIDZND0="28039@1" Pin0InfoVect0LinkObjId="SW-184645_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32c8850_0" Pin1InfoVect1LinkObjId="g_3486a30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-1107 976,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3482df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="850,-648 850,-618 801,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34e58c0@0" ObjectIDZND0="28041@x" ObjectIDZND1="28040@x" ObjectIDZND2="g_34877f0@0" Pin0InfoVect0LinkObjId="SW-184647_0" Pin0InfoVect1LinkObjId="SW-184646_0" Pin0InfoVect2LinkObjId="g_34877f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e58c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="850,-648 850,-618 801,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3483dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="846,-577 801,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28041@0" ObjectIDZND0="g_34e58c0@0" ObjectIDZND1="g_34877f0@0" ObjectIDZND2="28040@x" Pin0InfoVect0LinkObjId="g_34e58c0_0" Pin0InfoVect1LinkObjId="g_34877f0_0" Pin0InfoVect2LinkObjId="SW-184646_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="846,-577 801,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3484890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-618 801,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_34e58c0@0" ObjectIDND1="g_34877f0@0" ObjectIDZND0="28041@x" ObjectIDZND1="28040@x" Pin0InfoVect0LinkObjId="SW-184647_0" Pin0InfoVect1LinkObjId="SW-184646_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34e58c0_0" Pin1InfoVect1LinkObjId="g_34877f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="801,-618 801,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3484ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-577 801,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28041@x" ObjectIDND1="g_34e58c0@0" ObjectIDND2="g_34877f0@0" ObjectIDZND0="28040@1" Pin0InfoVect0LinkObjId="SW-184646_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-184647_0" Pin1InfoVect1LinkObjId="g_34e58c0_0" Pin1InfoVect2LinkObjId="g_34877f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-577 801,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3484d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-257 576,-267 592,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34695b0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_32c9b70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34695b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-257 576,-267 592,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3484f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-163 576,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="28067@x" ObjectIDND1="g_34885b0@0" ObjectIDZND0="28066@0" Pin0InfoVect0LinkObjId="SW-184796_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-184797_0" Pin1InfoVect1LinkObjId="g_34885b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-163 576,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34851f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-208 576,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28066@1" ObjectIDZND0="g_34695b0@0" Pin0InfoVect0LinkObjId="g_34695b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184796_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-208 576,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3485450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-469 673,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28047@1" ObjectIDZND0="28030@0" Pin0InfoVect0LinkObjId="g_2e3abb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-469 673,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34856b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-420 673,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28046@1" ObjectIDZND0="28047@0" Pin0InfoVect0LinkObjId="SW-184678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-420 673,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3485910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-376 731,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_351a3e0@0" ObjectIDZND0="28049@1" Pin0InfoVect0LinkObjId="SW-184680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_351a3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="742,-376 731,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3485b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-313 673,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28048@1" ObjectIDZND0="g_35197c0@0" Pin0InfoVect0LinkObjId="g_35197c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184679_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-313 673,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3486570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-367 839,-367 839,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_3453d10@0" ObjectIDND1="28045@x" ObjectIDND2="28042@x" ObjectIDZND0="g_3485dd0@1" Pin0InfoVect0LinkObjId="g_3485dd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3453d10_0" Pin1InfoVect1LinkObjId="SW-184656_0" Pin1InfoVect2LinkObjId="SW-184652_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-367 839,-367 839,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34867d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="839,-307 839,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3485dd0@0" ObjectIDZND0="g_347f760@0" Pin0InfoVect0LinkObjId="g_347f760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3485dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="839,-307 839,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3487330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-1204 976,-1178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_32c9b70@0" ObjectIDZND0="g_3486a30@1" Pin0InfoVect0LinkObjId="g_3486a30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c9b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-1204 976,-1178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3487590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-1134 976,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3486a30@0" ObjectIDZND0="g_32c8850@0" ObjectIDZND1="28039@x" Pin0InfoVect0LinkObjId="g_32c8850_0" Pin0InfoVect1LinkObjId="SW-184645_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3486a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="976,-1134 976,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34880f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-694 801,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_34e6670@0" ObjectIDZND0="g_34877f0@1" Pin0InfoVect0LinkObjId="g_34877f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e6670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-694 801,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3488350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-629 801,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_34877f0@0" ObjectIDZND0="g_34e58c0@0" ObjectIDZND1="28041@x" ObjectIDZND2="28040@x" Pin0InfoVect0LinkObjId="g_34e58c0_0" Pin0InfoVect1LinkObjId="SW-184647_0" Pin0InfoVect2LinkObjId="SW-184646_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34877f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="801,-629 801,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3488eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-152 576,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_34885b0@1" ObjectIDZND0="28067@x" ObjectIDZND1="28066@x" Pin0InfoVect0LinkObjId="SW-184797_0" Pin0InfoVect1LinkObjId="SW-184796_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34885b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="576,-152 576,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3489110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-96 576,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_34885b0@0" Pin0InfoVect0LinkObjId="g_34885b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c9b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-96 576,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,-376 673,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="28049@0" ObjectIDZND0="g_35197c0@0" ObjectIDZND1="28046@x" Pin0InfoVect0LinkObjId="g_35197c0_0" Pin0InfoVect1LinkObjId="SW-184676_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="695,-376 673,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348d220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-364 673,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_35197c0@1" ObjectIDZND0="28049@x" ObjectIDZND1="28046@x" Pin0InfoVect0LinkObjId="SW-184680_0" Pin0InfoVect1LinkObjId="SW-184676_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35197c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="673,-364 673,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348d410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-376 673,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="28049@x" ObjectIDND1="g_35197c0@0" ObjectIDZND0="28046@0" Pin0InfoVect0LinkObjId="SW-184676_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-184680_0" Pin1InfoVect1LinkObjId="g_35197c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-376 673,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3492840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-240 1105,-240 1105,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="47932@x" ObjectIDND1="48073@x" ObjectIDZND0="47934@0" Pin0InfoVect0LinkObjId="SW-308375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308373_0" Pin1InfoVect1LinkObjId="CB-DY_SCH.DY_SCH_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-240 1105,-240 1105,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34931b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-251 1135,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="47932@0" ObjectIDZND0="47934@x" ObjectIDZND1="48073@x" Pin0InfoVect0LinkObjId="SW-308375_0" Pin0InfoVect1LinkObjId="CB-DY_SCH.DY_SCH_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-251 1135,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34933a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-240 1135,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="47934@x" ObjectIDND1="47932@x" ObjectIDZND0="48073@1" Pin0InfoVect0LinkObjId="CB-DY_SCH.DY_SCH_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308375_0" Pin1InfoVect1LinkObjId="SW-308373_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-240 1135,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_349bd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-461 1135,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47931@1" ObjectIDZND0="28030@0" Pin0InfoVect0LinkObjId="g_2e3abb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308372_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-461 1135,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34a1c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="644,-267 673,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_351ae70@0" ObjectIDZND1="34235@x" ObjectIDZND2="28048@x" Pin0InfoVect0LinkObjId="g_351ae70_0" Pin0InfoVect1LinkObjId="EC-DY_SCH.055Ld_0" Pin0InfoVect2LinkObjId="SW-184679_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c9b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="644,-267 673,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34a25e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-230 673,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_351ae70@0" ObjectIDND1="34235@x" ObjectIDZND0="0@x" ObjectIDZND1="28048@x" Pin0InfoVect0LinkObjId="g_32c9b70_0" Pin0InfoVect1LinkObjId="SW-184679_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_351ae70_0" Pin1InfoVect1LinkObjId="EC-DY_SCH.055Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="673,-230 673,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34a2820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-267 673,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_351ae70@0" ObjectIDND2="34235@x" ObjectIDZND0="28048@0" Pin0InfoVect0LinkObjId="SW-184679_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_32c9b70_0" Pin1InfoVect1LinkObjId="g_351ae70_0" Pin1InfoVect2LinkObjId="EC-DY_SCH.055Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-267 673,-277 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="28030" cx="385" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28029" cx="385" cy="-1005" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28029" cx="101" cy="-1005" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28029" cx="976" cy="-1005" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28029" cx="330" cy="-1005" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28029" cx="713" cy="-1005" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28030" cx="-202" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28030" cx="32" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28030" cx="210" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28030" cx="399" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28030" cx="673" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28030" cx="896" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28030" cx="801" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28030" cx="1135" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153543" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -341.500000 -1153.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26033" ObjectName="DYN-DY_SCH"/>
     <cge:Meas_Ref ObjectId="153543"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2f079b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -559.000000 -1243.500000) translate(0,16)">三岔河变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ec7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ec7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ec7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ec7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ec7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ec7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ec7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ec7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ec7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eadc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,353)">联系方式：0878-6148329</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3001400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 736.000000 -729.000000) translate(0,12)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2618ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.096525 -18.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f62a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -173.000000) translate(0,12)">备用线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3187d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -86.000000 -994.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f61c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -276.000000 -478.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f61ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 395.000000 -938.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3188150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 392.000000 -891.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31884a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 392.000000 -985.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3188900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 395.000000 -578.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3188b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 392.000000 -625.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3188d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 392.000000 -524.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3188fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 425.000000 -674.000000) translate(0,12)">00167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3160100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 808.000000 -544.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3192990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 855.000000 -603.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_318c420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 538.000000 -824.000000) translate(0,12)">SZ11-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_318c420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 538.000000 -824.000000) translate(0,27)">35±3×2.5%/10.05kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_318c420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 538.000000 -824.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_318c420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 538.000000 -824.000000) translate(0,57)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3163810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -409.000000 -1227.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_318e540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -409.000000 -1262.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_318ebf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -621.000000 -759.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31908e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -837.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32c3f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -686.000000 -221.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_32c4c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -532.000000 -231.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_32c4c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -532.000000 -231.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -1252.000000) translate(0,12)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c9540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 983.000000 -1072.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e0100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -1367.000000) translate(0,12)">天河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e09e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 69.000000 -1355.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e0c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.000000 -1417.000000) translate(0,12)">大石河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34601d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.096525 -71.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3481580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 813.000000 -235.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3489370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 339.000000 -1128.000000) translate(0,12)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34899a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 337.000000 -1069.000000) translate(0,12)">3911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3489be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 337.000000 -1187.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3489e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 360.000000 -1252.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348a060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -1129.000000) translate(0,12)">392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348a2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -1070.000000) translate(0,12)">3921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348a4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 742.000000 -1253.000000) translate(0,12)">39267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348a720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -1188.000000) translate(0,12)">3926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348a960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -195.000000 -456.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -250.000000 -164.000000) translate(0,12)">10kV街区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348b4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -357.000000) translate(0,12)">05160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348b700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -195.000000 -284.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348b940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -193.000000 -400.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348bb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 679.000000 -173.000000) translate(0,12)">10kV达么线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348c420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 680.000000 -458.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348c670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -365.000000) translate(0,12)">05560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348d620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 680.000000 -302.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348daa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -24.000000 -157.000000) translate(0,12)">10kV白泥田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348e640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 39.000000 -449.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348e8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42.000000 -393.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348eb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 39.000000 -277.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348ed40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 56.000000 -351.000000) translate(0,12)">05260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -158.000000) translate(0,12)">10kV荞苴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348f820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 217.000000 -450.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 220.000000 -394.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 217.000000 -278.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348fec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -349.000000) translate(0,12)">05360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3490100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 350.000000 -158.000000) translate(0,12)">10kV铁锁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34909a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 -450.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3490f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 -278.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34911c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 421.000000 -349.000000) translate(0,12)">05460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3491400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 906.000000 -401.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3491640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -278.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3491880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -450.000000) translate(0,12)">0561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3491ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 918.000000 -349.000000) translate(0,12)">05660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3491d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1142.000000 -450.000000) translate(0,12)">0571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3491f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1085.000000 -385.000000) translate(0,12)">05760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3492180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1142.000000 -276.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34923c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -295.000000) translate(0,12)">05767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3492600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1145.000000 -405.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="Kaiti_GB2312" font-size="16" graphid="g_3499740" transform="matrix(0.979920 -0.000000 -0.000000 0.832895 -636.719364 -820.821279) translate(0,13)">站用变一次接线图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a0a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 409.000000 -394.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a10c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 683.000000 -414.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a1300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 115.000000 -1072.000000) translate(0,12)">3931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a1540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 516.000000 -203.000000) translate(0,12)">0036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a1780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 595.000000 -187.000000) translate(0,12)">00367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_34a2da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -426.500000 -1110.000000) translate(0,16)">AVC</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-443" y="-1121"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_SCH.DY_SCH_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-74,-1005 1291,-1005 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28029" ObjectName="BS-DY_SCH.DY_SCH_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="28029"/></metadata>
   <polyline fill="none" opacity="0" points="-74,-1005 1291,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_SCH.DY_SCH_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-264,-487 1301,-487 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28030" ObjectName="BS-DY_SCH.DY_SCH_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="28030"/></metadata>
   <polyline fill="none" opacity="0" points="-264,-487 1301,-487 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-DY_SCH.DY_SCH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="39778"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 346.000000 -745.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 346.000000 -745.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28068" ObjectName="TF-DY_SCH.DY_SCH_1T"/>
    <cge:TPSR_Ref TObjectID="28068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.000000 -1229.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.000000 -1229.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 -2.000000)" xlink:href="#transformer2:shape97_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 -2.000000)" xlink:href="#transformer2:shape97_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -655.000000 -1176.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184467" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -523.000000 -958.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184467" ObjectName="DY_SCH:DY_SCH_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184468" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -523.000000 -914.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184468" ObjectName="DY_SCH:DY_SCH_301BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184467" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -524.000000 -1040.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184467" ObjectName="DY_SCH:DY_SCH_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-184467" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -523.000000 -999.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184467" ObjectName="DY_SCH:DY_SCH_301BK_P"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-605" y="-1254"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-654" y="-1271"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-420" y="-1235"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-420" y="-1235"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-420" y="-1270"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-420" y="-1270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/chinaz5.png" UpImage="image/an.png" imageHeight="100" imageWidth="400">
    <a>
     
     <polygon fill="rgb(0,150,0)" points="-645,-824 -648,-827 -648,-800 -645,-803 -645,-824" stroke="rgb(0,150,0)"/>
     <polygon fill="rgb(0,150,0)" points="-645,-824 -648,-827 -495,-827 -498,-824 -645,-824" stroke="rgb(0,150,0)"/>
     <polygon fill="rgb(0,50,0)" points="-645,-803 -648,-800 -495,-800 -498,-803 -645,-803" stroke="rgb(0,50,0)"/>
     <polygon fill="rgb(0,50,0)" points="-498,-824 -495,-827 -495,-800 -498,-803 -498,-824" stroke="rgb(0,50,0)"/>
     <rect fill="rgb(0,100,0)" height="21" stroke="rgb(0,100,0)" width="147" x="-645" y="-824"/>
     <rect fill="none" height="21" qtmmishow="hidden" stroke="rgb(255,255,255)" width="147" x="-645" y="-824"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="339" y="-1128"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="339" y="-1128"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="722" y="-1129"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="722" y="-1129"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="426" y="-837"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="426" y="-837"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-193" y="-400"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-193" y="-400"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="42" y="-393"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="42" y="-393"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="220" y="-394"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="220" y="-394"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="906" y="-401"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="906" y="-401"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1145" y="-405"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1145" y="-405"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="409" y="-394"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="409" y="-394"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="683" y="-414"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="683" y="-414"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="-625" y="-761"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="-625" y="-761"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-443" y="-1122"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-443" y="-1122"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31608e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 455.000000 755.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3191840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 425.500000 733.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cb590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.000000 1109.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cbf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.000000 1090.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cc480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -133.000000 1039.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cc690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.000000 1073.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ccc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -119.000000 1056.000000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ccf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.000000 977.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cdb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 458.500000 958.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ce420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 940.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cef80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -264.000000 586.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cf2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -264.000000 567.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cf4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -272.000000 516.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cf720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -264.000000 550.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cf960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -258.000000 533.000000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dee60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.000000 596.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34df4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 458.500000 577.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34df6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 559.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dfa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -310.000000 130.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dfc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -321.500000 111.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dfec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -296.000000 93.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3496560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 404.000000 1143.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34967c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.500000 1124.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3496a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 418.000000 1106.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3496d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.000000 1152.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3496f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.500000 1133.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34971d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 796.000000 1115.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-DY_SCH.DY_SCH_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 -115.000000)" xlink:href="#capacitor:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48073" ObjectName="CB-DY_SCH.DY_SCH_Cb1"/>
    <cge:TPSR_Ref TObjectID="48073"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -127.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28062"/>
     <cge:Term_Ref ObjectID="39764"/>
    <cge:TPSR_Ref TObjectID="28062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -127.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28062"/>
     <cge:Term_Ref ObjectID="39764"/>
    <cge:TPSR_Ref TObjectID="28062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -127.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28062"/>
     <cge:Term_Ref ObjectID="39764"/>
    <cge:TPSR_Ref TObjectID="28062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -11.000000 -127.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28058"/>
     <cge:Term_Ref ObjectID="39756"/>
    <cge:TPSR_Ref TObjectID="28058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184522" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -11.000000 -127.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28058"/>
     <cge:Term_Ref ObjectID="39756"/>
    <cge:TPSR_Ref TObjectID="28058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -11.000000 -127.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28058"/>
     <cge:Term_Ref ObjectID="39756"/>
    <cge:TPSR_Ref TObjectID="28058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 167.000000 -127.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28054"/>
     <cge:Term_Ref ObjectID="39748"/>
    <cge:TPSR_Ref TObjectID="28054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184516" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 167.000000 -127.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28054"/>
     <cge:Term_Ref ObjectID="39748"/>
    <cge:TPSR_Ref TObjectID="28054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 167.000000 -127.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28054"/>
     <cge:Term_Ref ObjectID="39748"/>
    <cge:TPSR_Ref TObjectID="28054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -127.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28050"/>
     <cge:Term_Ref ObjectID="39740"/>
    <cge:TPSR_Ref TObjectID="28050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184510" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -127.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184510" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28050"/>
     <cge:Term_Ref ObjectID="39740"/>
    <cge:TPSR_Ref TObjectID="28050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -127.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28050"/>
     <cge:Term_Ref ObjectID="39740"/>
    <cge:TPSR_Ref TObjectID="28050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -127.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28046"/>
     <cge:Term_Ref ObjectID="39732"/>
    <cge:TPSR_Ref TObjectID="28046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -127.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28046"/>
     <cge:Term_Ref ObjectID="39732"/>
    <cge:TPSR_Ref TObjectID="28046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184500" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -127.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184500" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28046"/>
     <cge:Term_Ref ObjectID="39732"/>
    <cge:TPSR_Ref TObjectID="28046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184497" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -127.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28042"/>
     <cge:Term_Ref ObjectID="39724"/>
    <cge:TPSR_Ref TObjectID="28042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -127.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28042"/>
     <cge:Term_Ref ObjectID="39724"/>
    <cge:TPSR_Ref TObjectID="28042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -127.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28042"/>
     <cge:Term_Ref ObjectID="39724"/>
    <cge:TPSR_Ref TObjectID="28042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -590.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28035"/>
     <cge:Term_Ref ObjectID="39710"/>
    <cge:TPSR_Ref TObjectID="28035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -590.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28035"/>
     <cge:Term_Ref ObjectID="39710"/>
    <cge:TPSR_Ref TObjectID="28035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -590.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28035"/>
     <cge:Term_Ref ObjectID="39710"/>
    <cge:TPSR_Ref TObjectID="28035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -971.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28032"/>
     <cge:Term_Ref ObjectID="39704"/>
    <cge:TPSR_Ref TObjectID="28032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -971.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28032"/>
     <cge:Term_Ref ObjectID="39704"/>
    <cge:TPSR_Ref TObjectID="28032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -971.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28032"/>
     <cge:Term_Ref ObjectID="39704"/>
    <cge:TPSR_Ref TObjectID="28032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.000000 -1138.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47937"/>
     <cge:Term_Ref ObjectID="46722"/>
    <cge:TPSR_Ref TObjectID="47937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.000000 -1138.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47937"/>
     <cge:Term_Ref ObjectID="46722"/>
    <cge:TPSR_Ref TObjectID="47937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.000000 -1138.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47937"/>
     <cge:Term_Ref ObjectID="46722"/>
    <cge:TPSR_Ref TObjectID="47937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -1143.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47941"/>
     <cge:Term_Ref ObjectID="46730"/>
    <cge:TPSR_Ref TObjectID="47941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -1143.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47941"/>
     <cge:Term_Ref ObjectID="46730"/>
    <cge:TPSR_Ref TObjectID="47941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308245" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -1143.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47941"/>
     <cge:Term_Ref ObjectID="46730"/>
    <cge:TPSR_Ref TObjectID="47941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308208" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -108.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47930"/>
     <cge:Term_Ref ObjectID="46712"/>
    <cge:TPSR_Ref TObjectID="47930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308205" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -108.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47930"/>
     <cge:Term_Ref ObjectID="46712"/>
    <cge:TPSR_Ref TObjectID="47930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-184477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 499.000000 -750.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28068"/>
     <cge:Term_Ref ObjectID="39779"/>
    <cge:TPSR_Ref TObjectID="28068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-184476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 499.000000 -750.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28068"/>
     <cge:Term_Ref ObjectID="39779"/>
    <cge:TPSR_Ref TObjectID="28068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-184478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -68.000000 -1103.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28029"/>
     <cge:Term_Ref ObjectID="39700"/>
    <cge:TPSR_Ref TObjectID="28029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-184479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -68.000000 -1103.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28029"/>
     <cge:Term_Ref ObjectID="39700"/>
    <cge:TPSR_Ref TObjectID="28029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-184480" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -68.000000 -1103.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28029"/>
     <cge:Term_Ref ObjectID="39700"/>
    <cge:TPSR_Ref TObjectID="28029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-184484" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -68.000000 -1103.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28029"/>
     <cge:Term_Ref ObjectID="39700"/>
    <cge:TPSR_Ref TObjectID="28029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-184481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -68.000000 -1103.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28029"/>
     <cge:Term_Ref ObjectID="39700"/>
    <cge:TPSR_Ref TObjectID="28029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-184486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 -581.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28030"/>
     <cge:Term_Ref ObjectID="39701"/>
    <cge:TPSR_Ref TObjectID="28030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-184487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 -581.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28030"/>
     <cge:Term_Ref ObjectID="39701"/>
    <cge:TPSR_Ref TObjectID="28030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-184488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 -581.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28030"/>
     <cge:Term_Ref ObjectID="39701"/>
    <cge:TPSR_Ref TObjectID="28030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-184492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 -581.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28030"/>
     <cge:Term_Ref ObjectID="39701"/>
    <cge:TPSR_Ref TObjectID="28030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-184489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 -581.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28030"/>
     <cge:Term_Ref ObjectID="39701"/>
    <cge:TPSR_Ref TObjectID="28030"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-184578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 376.241796 -955.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28033" ObjectName="SW-DY_SCH.DY_SCH_3011SW"/>
     <cge:Meas_Ref ObjectId="184578"/>
    <cge:TPSR_Ref TObjectID="28033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 376.241796 -868.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28034" ObjectName="SW-DY_SCH.DY_SCH_3016SW"/>
     <cge:Meas_Ref ObjectId="184579"/>
    <cge:TPSR_Ref TObjectID="28034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184590">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 -647.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28038" ObjectName="SW-DY_SCH.DY_SCH_00167SW"/>
     <cge:Meas_Ref ObjectId="184590"/>
    <cge:TPSR_Ref TObjectID="28038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184589">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 376.241796 -595.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28037" ObjectName="SW-DY_SCH.DY_SCH_0016SW"/>
     <cge:Meas_Ref ObjectId="184589"/>
    <cge:TPSR_Ref TObjectID="28037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184588">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 376.241796 -494.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28036" ObjectName="SW-DY_SCH.DY_SCH_0011SW"/>
     <cge:Meas_Ref ObjectId="184588"/>
    <cge:TPSR_Ref TObjectID="28036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -211.238710 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28063" ObjectName="SW-DY_SCH.DY_SCH_0511SW"/>
     <cge:Meas_Ref ObjectId="184774"/>
    <cge:TPSR_Ref TObjectID="28063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -183.847458 -359.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28065" ObjectName="SW-DY_SCH.DY_SCH_05160SW"/>
     <cge:Meas_Ref ObjectId="184776"/>
    <cge:TPSR_Ref TObjectID="28065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -211.238710 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28064" ObjectName="SW-DY_SCH.DY_SCH_0516SW"/>
     <cge:Meas_Ref ObjectId="184775"/>
    <cge:TPSR_Ref TObjectID="28064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184646">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.761290 -514.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28040" ObjectName="SW-DY_SCH.DY_SCH_0901SW"/>
     <cge:Meas_Ref ObjectId="184646"/>
    <cge:TPSR_Ref TObjectID="28040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 841.000000 -572.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28041" ObjectName="SW-DY_SCH.DY_SCH_09017SW"/>
     <cge:Meas_Ref ObjectId="184647"/>
    <cge:TPSR_Ref TObjectID="28041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184645">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 966.761290 -1042.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28039" ObjectName="SW-DY_SCH.DY_SCH_3901SW"/>
     <cge:Meas_Ref ObjectId="184645"/>
    <cge:TPSR_Ref TObjectID="28039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 92.000000 -1042.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47945" ObjectName="SW-DY_SCH.DY_SCH_3931SW"/>
     <cge:Meas_Ref ObjectId="308723"/>
    <cge:TPSR_Ref TObjectID="47945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308259">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 321.000000 -1039.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47938" ObjectName="SW-DY_SCH.DY_SCH_3911SW"/>
     <cge:Meas_Ref ObjectId="308259"/>
    <cge:TPSR_Ref TObjectID="47938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308260">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 321.000000 -1157.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47939" ObjectName="SW-DY_SCH.DY_SCH_3916SW"/>
     <cge:Meas_Ref ObjectId="308260"/>
    <cge:TPSR_Ref TObjectID="47939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308261">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 357.000000 -1221.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47940" ObjectName="SW-DY_SCH.DY_SCH_39167SW"/>
     <cge:Meas_Ref ObjectId="308261"/>
    <cge:TPSR_Ref TObjectID="47940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308319">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 -1040.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47942" ObjectName="SW-DY_SCH.DY_SCH_3921SW"/>
     <cge:Meas_Ref ObjectId="308319"/>
    <cge:TPSR_Ref TObjectID="47942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308320">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 -1158.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47943" ObjectName="SW-DY_SCH.DY_SCH_3926SW"/>
     <cge:Meas_Ref ObjectId="308320"/>
    <cge:TPSR_Ref TObjectID="47943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308321">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 740.000000 -1222.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47944" ObjectName="SW-DY_SCH.DY_SCH_39267SW"/>
     <cge:Meas_Ref ObjectId="308321"/>
    <cge:TPSR_Ref TObjectID="47944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184750">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.761290 -419.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28059" ObjectName="SW-DY_SCH.DY_SCH_0521SW"/>
     <cge:Meas_Ref ObjectId="184750"/>
    <cge:TPSR_Ref TObjectID="28059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 50.152542 -352.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28061" ObjectName="SW-DY_SCH.DY_SCH_05260SW"/>
     <cge:Meas_Ref ObjectId="184752"/>
    <cge:TPSR_Ref TObjectID="28061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184751">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.761290 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28060" ObjectName="SW-DY_SCH.DY_SCH_0526SW"/>
     <cge:Meas_Ref ObjectId="184751"/>
    <cge:TPSR_Ref TObjectID="28060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 200.761290 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28055" ObjectName="SW-DY_SCH.DY_SCH_0531SW"/>
     <cge:Meas_Ref ObjectId="184726"/>
    <cge:TPSR_Ref TObjectID="28055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.152542 -353.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28057" ObjectName="SW-DY_SCH.DY_SCH_05360SW"/>
     <cge:Meas_Ref ObjectId="184728"/>
    <cge:TPSR_Ref TObjectID="28057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 200.761290 -248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28056" ObjectName="SW-DY_SCH.DY_SCH_0536SW"/>
     <cge:Meas_Ref ObjectId="184727"/>
    <cge:TPSR_Ref TObjectID="28056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184702">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.761290 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28051" ObjectName="SW-DY_SCH.DY_SCH_0541SW"/>
     <cge:Meas_Ref ObjectId="184702"/>
    <cge:TPSR_Ref TObjectID="28051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184704">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 417.152542 -353.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28053" ObjectName="SW-DY_SCH.DY_SCH_05460SW"/>
     <cge:Meas_Ref ObjectId="184704"/>
    <cge:TPSR_Ref TObjectID="28053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184703">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.761290 -248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28052" ObjectName="SW-DY_SCH.DY_SCH_0546SW"/>
     <cge:Meas_Ref ObjectId="184703"/>
    <cge:TPSR_Ref TObjectID="28052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.761290 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28047" ObjectName="SW-DY_SCH.DY_SCH_0551SW"/>
     <cge:Meas_Ref ObjectId="184678"/>
    <cge:TPSR_Ref TObjectID="28047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.152542 -370.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28049" ObjectName="SW-DY_SCH.DY_SCH_05560SW"/>
     <cge:Meas_Ref ObjectId="184680"/>
    <cge:TPSR_Ref TObjectID="28049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.761290 -272.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28048" ObjectName="SW-DY_SCH.DY_SCH_0556SW"/>
     <cge:Meas_Ref ObjectId="184679"/>
    <cge:TPSR_Ref TObjectID="28048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.761290 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28043" ObjectName="SW-DY_SCH.DY_SCH_0561SW"/>
     <cge:Meas_Ref ObjectId="184654"/>
    <cge:TPSR_Ref TObjectID="28043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.152542 -353.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28045" ObjectName="SW-DY_SCH.DY_SCH_05660SW"/>
     <cge:Meas_Ref ObjectId="184656"/>
    <cge:TPSR_Ref TObjectID="28045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184655">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.761290 -248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28044" ObjectName="SW-DY_SCH.DY_SCH_0566SW"/>
     <cge:Meas_Ref ObjectId="184655"/>
    <cge:TPSR_Ref TObjectID="28044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308372">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1125.761290 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47931" ObjectName="SW-DY_SCH.DY_SCH_0571SW"/>
     <cge:Meas_Ref ObjectId="308372"/>
    <cge:TPSR_Ref TObjectID="47931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.152542 -353.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47933" ObjectName="SW-DY_SCH.DY_SCH_05760SW"/>
     <cge:Meas_Ref ObjectId="308374"/>
    <cge:TPSR_Ref TObjectID="47933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184796">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 -167.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28066" ObjectName="SW-DY_SCH.DY_SCH_0121SW"/>
     <cge:Meas_Ref ObjectId="184796"/>
    <cge:TPSR_Ref TObjectID="28066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 588.000000 -158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28067" ObjectName="SW-DY_SCH.DY_SCH_01217SW"/>
     <cge:Meas_Ref ObjectId="184797"/>
    <cge:TPSR_Ref TObjectID="28067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 587.000000 -262.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308373">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1126.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47932" ObjectName="SW-DY_SCH.DY_SCH_0576SW"/>
     <cge:Meas_Ref ObjectId="308373"/>
    <cge:TPSR_Ref TObjectID="47932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308375">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -245.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47934" ObjectName="SW-DY_SCH.DY_SCH_05767SW"/>
     <cge:Meas_Ref ObjectId="308375"/>
    <cge:TPSR_Ref TObjectID="47934"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-420" y="-1235"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-420" y="-1270"/></g>
   <g href="楚雄地区_35kV_三岔河站用变一次接线图.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" stroke="rgb(255,255,255)" width="147" x="-645" y="-824"/></g>
   <g href="35kV三岔河变35kV天河线391开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="339" y="-1128"/></g>
   <g href="35kV三岔河变35kV大石河线392开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="722" y="-1129"/></g>
   <g href="35kV三岔河变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="426" y="-837"/></g>
   <g href="35kV三岔河变10kV街区线051开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-193" y="-400"/></g>
   <g href="35kV三岔河变10kV白泥田线052开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="42" y="-393"/></g>
   <g href="35kV三岔河变10kV荞苴线053开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="220" y="-394"/></g>
   <g href="35kV三岔河变10kV备用线056开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="906" y="-401"/></g>
   <g href="35kV三岔河变10kV1号电容器组057开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1145" y="-405"/></g>
   <g href="35kV三岔河变10kV铁锁线054开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="409" y="-394"/></g>
   <g href="35kV三岔河变10kV达么线055开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="683" y="-414"/></g>
   <g href="35kV三岔河变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="-625" y="-761"/></g>
   <g href="AVC三岔河站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-443" y="-1122"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_36bf010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 380.000000 -655.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32c28c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -716.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32c8850">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -1122.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32d16c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 -1170.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32d22e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 92.000000 -1111.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32d5970">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 119.000000 -1089.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c61e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 -1214.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c7940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 345.000000 -1261.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d6720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 673.000000 -1215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d7e80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -1262.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34ddde0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -756.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e14e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -212.000000 -309.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e3ba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -192.000000 -236.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e58c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 843.000000 -644.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34f23c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 -302.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34f4650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 43.000000 -229.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3500bc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 200.000000 -303.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3502e50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 221.000000 -230.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_350d2f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.000000 -303.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_350f580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 410.000000 -230.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35197c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -320.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_351ae70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 687.000000 -222.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3453d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 -303.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3455d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -230.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345f350">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1125.000000 -303.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34695b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 -213.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347c000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1138.000000 -361.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347f760">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 827.000000 -246.000000)" xlink:href="#lightningRod:shape159"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3485dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 -302.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3486a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 -1129.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34877f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.000000 -624.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34885b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.000000 -103.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_SCH"/>
</svg>