<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-31" aopId="786686" id="thSvg" viewBox="3114 -1174 1889 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="0" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="9" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="7" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape81">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="40" y2="27"/>
    <ellipse cx="9" cy="18" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="12" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2993470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2993e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2994830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2995510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2996440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2997050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29978b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2998390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2998c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2999550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_299a200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_299aae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_299b3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_299bf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_299c870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_299d150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_299e860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_299f4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_299fd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a0780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a1960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a22e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a2dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a8150" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a8ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a4ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a5fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29a7240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29aabd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1899" x="3109" y="-1179"/>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3793,-288 3808,-288 3802,-276 3793,-288 " stroke="rgb(124,252,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3793,-256 3809,-256 3802,-267 3793,-256 " stroke="rgb(124,252,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4317,-255 4333,-255 4325,-265 4317,-255 " stroke="rgb(124,252,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4317,-287 4332,-287 4326,-275 4317,-287 " stroke="rgb(124,252,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4544,-253 4560,-253 4553,-264 4544,-253 " stroke="rgb(124,252,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4544,-285 4559,-285 4553,-273 4544,-285 " stroke="rgb(124,252,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4912,-258 4928,-258 4921,-269 4912,-258 " stroke="rgb(124,252,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4912,-290 4927,-290 4921,-278 4912,-290 " stroke="rgb(124,252,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-35807">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -695.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5691" ObjectName="SW-CX_DD.CX_DD_301BK"/>
     <cge:Meas_Ref ObjectId="35807"/>
    <cge:TPSR_Ref TObjectID="5691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35808">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -545.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5692" ObjectName="SW-CX_DD.CX_DD_001BK"/>
     <cge:Meas_Ref ObjectId="35808"/>
    <cge:TPSR_Ref TObjectID="5692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35809">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -697.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5693" ObjectName="SW-CX_DD.CX_DD_302BK"/>
     <cge:Meas_Ref ObjectId="35809"/>
    <cge:TPSR_Ref TObjectID="5693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35810">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -547.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5694" ObjectName="SW-CX_DD.CX_DD_002BK"/>
     <cge:Meas_Ref ObjectId="35810"/>
    <cge:TPSR_Ref TObjectID="5694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4912.000000 -364.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5690" ObjectName="SW-CX_DD.CX_DD_086BK"/>
     <cge:Meas_Ref ObjectId="35806"/>
    <cge:TPSR_Ref TObjectID="5690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35802">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -368.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5685" ObjectName="SW-CX_DD.CX_DD_085BK"/>
     <cge:Meas_Ref ObjectId="35802"/>
    <cge:TPSR_Ref TObjectID="5685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35805">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 -365.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5689" ObjectName="SW-CX_DD.CX_DD_083BK"/>
     <cge:Meas_Ref ObjectId="35805"/>
    <cge:TPSR_Ref TObjectID="5689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.000000 -364.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5688" ObjectName="SW-CX_DD.CX_DD_082BK"/>
     <cge:Meas_Ref ObjectId="35804"/>
    <cge:TPSR_Ref TObjectID="5688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35803">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.000000 -367.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5687" ObjectName="SW-CX_DD.CX_DD_081BK"/>
     <cge:Meas_Ref ObjectId="35803"/>
    <cge:TPSR_Ref TObjectID="5687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35811">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -903.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5695" ObjectName="SW-CX_DD.CX_DD_381BK"/>
     <cge:Meas_Ref ObjectId="35811"/>
    <cge:TPSR_Ref TObjectID="5695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35812">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -902.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5696" ObjectName="SW-CX_DD.CX_DD_382BK"/>
     <cge:Meas_Ref ObjectId="35812"/>
    <cge:TPSR_Ref TObjectID="5696"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DD.CX_DD_9PM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-244 5002,-244 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9806" ObjectName="BS-CX_DD.CX_DD_9PM"/>
    <cge:TPSR_Ref TObjectID="9806"/></metadata>
   <polyline fill="none" opacity="0" points="3668,-244 5002,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DD.CX_DD_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3765,-834 4901,-834 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5683" ObjectName="BS-CX_DD.CX_DD_3M"/>
    <cge:TPSR_Ref TObjectID="5683"/></metadata>
   <polyline fill="none" opacity="0" points="3765,-834 4901,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DD.CX_DD_9M">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-478 5002,-478 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5684" ObjectName="BS-CX_DD.CX_DD_9M"/>
    <cge:TPSR_Ref TObjectID="5684"/></metadata>
   <polyline fill="none" opacity="0" points="3668,-478 5002,-478 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DD.CX_DD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8361"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -602.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -602.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5728" ObjectName="TF-CX_DD.CX_DD_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DD.CX_DD_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8365"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4720.000000 -604.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4720.000000 -604.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5729" ObjectName="TF-CX_DD.CX_DD_2T"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34a4f40">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4373.000000 -651.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_366b7a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 -666.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2744eb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 -616.000000)" xlink:href="#lightningRod:shape81"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a6db80">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -373.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e2e30">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -313.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e1dc0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4137.000000 -322.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e5a40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -337.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247bdd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -282.000000)" xlink:href="#lightningRod:shape81"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c2d00">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4936.000000 -159.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e29a0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4568.000000 -160.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26189d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4341.000000 -159.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_366bc10">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3816.000000 -162.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_377fc00">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3836.000000 -1004.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_377c5c0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4662.000000 -999.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d04f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -971.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33cbfc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4818.000000 -911.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-55149" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 -655.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55149" ObjectName="CX_DD:CX_DD_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-55150" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 -641.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55150" ObjectName="CX_DD:CX_DD_1T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-55155" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -664.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55155" ObjectName="CX_DD:CX_DD_2T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-55154" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -650.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55154" ObjectName="CX_DD:CX_DD_2T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3225.500000 -1093.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62649" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3265.538462 -959.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62649" ObjectName="CX_DD:CX_DD_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79817" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3265.538462 -917.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79817" ObjectName="CX_DD:CX_DD_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35772" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -946.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5695"/>
     <cge:Term_Ref ObjectID="8293"/>
    <cge:TPSR_Ref TObjectID="5695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35773" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -946.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5695"/>
     <cge:Term_Ref ObjectID="8293"/>
    <cge:TPSR_Ref TObjectID="5695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35768" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -946.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35768" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5695"/>
     <cge:Term_Ref ObjectID="8293"/>
    <cge:TPSR_Ref TObjectID="5695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35765" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4686.000000 -584.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5694"/>
     <cge:Term_Ref ObjectID="8291"/>
    <cge:TPSR_Ref TObjectID="5694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35766" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4686.000000 -584.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5694"/>
     <cge:Term_Ref ObjectID="8291"/>
    <cge:TPSR_Ref TObjectID="5694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35754" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4686.000000 -584.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5694"/>
     <cge:Term_Ref ObjectID="8291"/>
    <cge:TPSR_Ref TObjectID="5694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35734" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4948.000000 -112.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5690"/>
     <cge:Term_Ref ObjectID="8283"/>
    <cge:TPSR_Ref TObjectID="5690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35735" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4948.000000 -112.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5690"/>
     <cge:Term_Ref ObjectID="8283"/>
    <cge:TPSR_Ref TObjectID="5690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35732" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4948.000000 -112.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5690"/>
     <cge:Term_Ref ObjectID="8283"/>
    <cge:TPSR_Ref TObjectID="5690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35718" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4780.000000 -179.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5685"/>
     <cge:Term_Ref ObjectID="8273"/>
    <cge:TPSR_Ref TObjectID="5685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35719" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4780.000000 -179.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5685"/>
     <cge:Term_Ref ObjectID="8273"/>
    <cge:TPSR_Ref TObjectID="5685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35716" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4780.000000 -179.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35716" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5685"/>
     <cge:Term_Ref ObjectID="8273"/>
    <cge:TPSR_Ref TObjectID="5685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35730" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4571.000000 -112.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35730" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5689"/>
     <cge:Term_Ref ObjectID="8281"/>
    <cge:TPSR_Ref TObjectID="5689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35731" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4571.000000 -112.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35731" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5689"/>
     <cge:Term_Ref ObjectID="8281"/>
    <cge:TPSR_Ref TObjectID="5689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35728" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4571.000000 -112.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35728" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5689"/>
     <cge:Term_Ref ObjectID="8281"/>
    <cge:TPSR_Ref TObjectID="5689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35726" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4338.000000 -112.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5688"/>
     <cge:Term_Ref ObjectID="8279"/>
    <cge:TPSR_Ref TObjectID="5688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35727" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4338.000000 -112.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5688"/>
     <cge:Term_Ref ObjectID="8279"/>
    <cge:TPSR_Ref TObjectID="5688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35724" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4338.000000 -112.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35724" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5688"/>
     <cge:Term_Ref ObjectID="8279"/>
    <cge:TPSR_Ref TObjectID="5688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35778" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4686.000000 -946.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5696"/>
     <cge:Term_Ref ObjectID="8295"/>
    <cge:TPSR_Ref TObjectID="5696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35779" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4686.000000 -946.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5696"/>
     <cge:Term_Ref ObjectID="8295"/>
    <cge:TPSR_Ref TObjectID="5696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35774" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4686.000000 -946.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5696"/>
     <cge:Term_Ref ObjectID="8295"/>
    <cge:TPSR_Ref TObjectID="5696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-35745" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -584.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5692"/>
     <cge:Term_Ref ObjectID="8287"/>
    <cge:TPSR_Ref TObjectID="5692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-35746" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -584.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5692"/>
     <cge:Term_Ref ObjectID="8287"/>
    <cge:TPSR_Ref TObjectID="5692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-35739" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -584.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5692"/>
     <cge:Term_Ref ObjectID="8287"/>
    <cge:TPSR_Ref TObjectID="5692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35722" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3810.000000 -112.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5687"/>
     <cge:Term_Ref ObjectID="8277"/>
    <cge:TPSR_Ref TObjectID="5687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35723" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3810.000000 -112.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5687"/>
     <cge:Term_Ref ObjectID="8277"/>
    <cge:TPSR_Ref TObjectID="5687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35720" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3810.000000 -112.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35720" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5687"/>
     <cge:Term_Ref ObjectID="8277"/>
    <cge:TPSR_Ref TObjectID="5687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-55153" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4973.000000 -679.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5729"/>
     <cge:Term_Ref ObjectID="8366"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-35780" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3794.000000 -825.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35780" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5683"/>
     <cge:Term_Ref ObjectID="8271"/>
    <cge:TPSR_Ref TObjectID="5683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-35781" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3794.000000 -825.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5683"/>
     <cge:Term_Ref ObjectID="8271"/>
    <cge:TPSR_Ref TObjectID="5683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-55156" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3794.000000 -825.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5683"/>
     <cge:Term_Ref ObjectID="8271"/>
    <cge:TPSR_Ref TObjectID="5683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-55161" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3794.000000 -825.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5683"/>
     <cge:Term_Ref ObjectID="8271"/>
    <cge:TPSR_Ref TObjectID="5683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-35782" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3794.000000 -825.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5683"/>
     <cge:Term_Ref ObjectID="8271"/>
    <cge:TPSR_Ref TObjectID="5683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-35784" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3794.000000 -825.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5683"/>
     <cge:Term_Ref ObjectID="8271"/>
    <cge:TPSR_Ref TObjectID="5683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-35785" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3690.000000 -467.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5684"/>
     <cge:Term_Ref ObjectID="8272"/>
    <cge:TPSR_Ref TObjectID="5684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-35786" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3690.000000 -467.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5684"/>
     <cge:Term_Ref ObjectID="8272"/>
    <cge:TPSR_Ref TObjectID="5684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-55159" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3690.000000 -467.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5684"/>
     <cge:Term_Ref ObjectID="8272"/>
    <cge:TPSR_Ref TObjectID="5684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-35787" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3690.000000 -467.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5684"/>
     <cge:Term_Ref ObjectID="8272"/>
    <cge:TPSR_Ref TObjectID="5684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-55168" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3690.000000 -467.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5684"/>
     <cge:Term_Ref ObjectID="8272"/>
    <cge:TPSR_Ref TObjectID="5684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-55151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3880.000000 -671.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="55151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5728"/>
     <cge:Term_Ref ObjectID="8362"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35742" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4106.000000 -734.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35742" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5691"/>
     <cge:Term_Ref ObjectID="8285"/>
    <cge:TPSR_Ref TObjectID="5691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4106.000000 -734.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5691"/>
     <cge:Term_Ref ObjectID="8285"/>
    <cge:TPSR_Ref TObjectID="5691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35736" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4106.000000 -734.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5691"/>
     <cge:Term_Ref ObjectID="8285"/>
    <cge:TPSR_Ref TObjectID="5691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-35744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4106.000000 -734.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5691"/>
     <cge:Term_Ref ObjectID="8285"/>
    <cge:TPSR_Ref TObjectID="5691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35762" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4666.000000 -757.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5693"/>
     <cge:Term_Ref ObjectID="8289"/>
    <cge:TPSR_Ref TObjectID="5693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4666.000000 -757.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5693"/>
     <cge:Term_Ref ObjectID="8289"/>
    <cge:TPSR_Ref TObjectID="5693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4666.000000 -757.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5693"/>
     <cge:Term_Ref ObjectID="8289"/>
    <cge:TPSR_Ref TObjectID="5693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-35764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4666.000000 -757.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5693"/>
     <cge:Term_Ref ObjectID="8289"/>
    <cge:TPSR_Ref TObjectID="5693"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3237" y="-1152"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3237" y="-1152"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3189" y="-1169"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3189" y="-1169"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3968" y="-931"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3968" y="-931"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="3987" y="-654"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="3987" y="-654"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4764" y="-931"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4764" y="-931"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4786" y="-656"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4786" y="-656"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4930" y="-393"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4930" y="-393"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4763" y="-397"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4763" y="-397"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4562" y="-394"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4562" y="-394"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4335" y="-393"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4335" y="-393"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3810" y="-396"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3810" y="-396"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3237" y="-1152"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1169"/></g>
   <g href="35kV大德变35kV上大线381断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3968" y="-931"/></g>
   <g href="35kV大德变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="3987" y="-654"/></g>
   <g href="35kV大德变35kV大九线382断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4764" y="-931"/></g>
   <g href="35kV大德变2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4786" y="-656"/></g>
   <g href="35kV大德变10kV和平线086断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4930" y="-393"/></g>
   <g href="35kV大德变10kV旁路085断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4763" y="-397"/></g>
   <g href="35kV大德变10kV卸油泵房线083断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4562" y="-394"/></g>
   <g href="35kV大德变10kV库区线082断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4335" y="-393"/></g>
   <g href="35kV大德变10kV路溪线081断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3810" y="-396"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1173"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-573"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-1053"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3687,-1073 3788,-1073 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3687,-1073 3788,-1073 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_366a8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-687 3955,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="5728@1" ObjectIDZND0="5691@0" Pin0InfoVect0LinkObjId="SW-35807_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-687 3955,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e1a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-803 3955,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5712@1" ObjectIDZND0="5683@0" Pin0InfoVect0LinkObjId="g_25c4310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35884_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-803 3955,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e2ebc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-730 3955,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5691@1" ObjectIDZND0="5712@x" ObjectIDZND1="5714@x" Pin0InfoVect0LinkObjId="SW-35884_0" Pin0InfoVect1LinkObjId="SW-35886_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35807_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-730 3955,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34b6910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-748 3955,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5691@x" ObjectIDND1="5714@x" ObjectIDZND0="5712@0" Pin0InfoVect0LinkObjId="SW-35884_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35807_0" Pin1InfoVect1LinkObjId="SW-35886_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-748 3955,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af7040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-748 3977,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5691@x" ObjectIDND1="5712@x" ObjectIDZND0="5714@0" Pin0InfoVect0LinkObjId="SW-35886_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35807_0" Pin1InfoVect1LinkObjId="SW-35884_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-748 3977,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bf2420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-748 4031,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5714@1" ObjectIDZND0="g_35e8fd0@0" Pin0InfoVect0LinkObjId="g_35e8fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35886_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-748 4031,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_281b420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-834 4347,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5683@0" ObjectIDZND0="5724@1" Pin0InfoVect0LinkObjId="SW-35982_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e1a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-834 4347,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2726f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-757 4370,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_34a4f40@0" ObjectIDND1="g_366b7a0@0" ObjectIDND2="5724@x" ObjectIDZND0="5725@0" Pin0InfoVect0LinkObjId="SW-35985_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34a4f40_0" Pin1InfoVect1LinkObjId="g_366b7a0_0" Pin1InfoVect2LinkObjId="SW-35982_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-757 4370,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2729c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-757 4424,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5725@1" ObjectIDZND0="g_37a08d0@0" Pin0InfoVect0LinkObjId="g_37a08d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35985_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4406,-757 4424,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27557c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-776 4347,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5724@0" ObjectIDZND0="g_34a4f40@0" ObjectIDZND1="g_366b7a0@0" ObjectIDZND2="5725@x" Pin0InfoVect0LinkObjId="g_34a4f40_0" Pin0InfoVect1LinkObjId="g_366b7a0_0" Pin0InfoVect2LinkObjId="SW-35985_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35982_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-776 4347,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272ff30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-705 4380,-717 4347,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34a4f40@0" ObjectIDZND0="5724@x" ObjectIDZND1="5725@x" ObjectIDZND2="g_366b7a0@0" Pin0InfoVect0LinkObjId="SW-35982_0" Pin0InfoVect1LinkObjId="SW-35985_0" Pin0InfoVect2LinkObjId="g_366b7a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34a4f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-705 4380,-717 4347,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2785a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-717 4347,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_34a4f40@0" ObjectIDND1="g_366b7a0@0" ObjectIDZND0="5724@x" ObjectIDZND1="5725@x" Pin0InfoVect0LinkObjId="SW-35982_0" Pin0InfoVect1LinkObjId="SW-35985_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34a4f40_0" Pin1InfoVect1LinkObjId="g_366b7a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-717 4347,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277c140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-717 4308,-717 4308,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_34a4f40@0" ObjectIDND1="5724@x" ObjectIDND2="5725@x" ObjectIDZND0="g_366b7a0@1" Pin0InfoVect0LinkObjId="g_366b7a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34a4f40_0" Pin1InfoVect1LinkObjId="SW-35982_0" Pin1InfoVect2LinkObjId="SW-35985_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-717 4308,-717 4308,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2727980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-671 4308,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_366b7a0@0" ObjectIDZND0="g_2744eb0@0" Pin0InfoVect0LinkObjId="g_2744eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_366b7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-671 4308,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37a4830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-607 3954,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="5728@0" ObjectIDZND0="5692@1" Pin0InfoVect0LinkObjId="SW-35808_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-607 3954,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37fa310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-553 3954,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5692@0" ObjectIDZND0="5713@1" Pin0InfoVect0LinkObjId="SW-35885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35808_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-553 3954,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37a4d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-496 3954,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5713@0" ObjectIDZND0="5684@0" Pin0InfoVect0LinkObjId="g_2786f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-496 3954,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2783970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-478 3954,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5684@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_34a4f40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a4d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-478 3954,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_35e0900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-424 3954,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2a6db80@1" Pin0InfoVect0LinkObjId="g_2a6db80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34a4f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-424 3954,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_361c700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-378 3954,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2a6db80@0" ObjectIDZND0="g_25e2e30@0" Pin0InfoVect0LinkObjId="g_25e2e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a6db80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-378 3954,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34a4a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-478 4110,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5684@0" ObjectIDZND0="5726@1" Pin0InfoVect0LinkObjId="SW-35990_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a4d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-478 4110,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf8e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4072,-342 4072,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_25e5a40@0" ObjectIDZND0="g_247bdd0@0" Pin0InfoVect0LinkObjId="g_247bdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e5a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4072,-342 4072,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_277ac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-414 4110,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5726@0" ObjectIDZND0="g_25e1dc0@0" ObjectIDZND1="g_25e5a40@0" Pin0InfoVect0LinkObjId="g_25e1dc0_0" Pin0InfoVect1LinkObjId="g_25e5a40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-414 4110,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36e6cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-376 4144,-388 4110,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_25e1dc0@0" ObjectIDZND0="g_25e5a40@0" ObjectIDZND1="5726@x" Pin0InfoVect0LinkObjId="g_25e5a40_0" Pin0InfoVect1LinkObjId="SW-35990_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e1dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-376 4144,-388 4110,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2730bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-388 4072,-388 4072,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25e1dc0@0" ObjectIDND1="5726@x" ObjectIDZND0="g_25e5a40@1" Pin0InfoVect0LinkObjId="g_25e5a40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25e1dc0_0" Pin1InfoVect1LinkObjId="SW-35990_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-388 4072,-388 4072,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277fc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-689 4755,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="5729@1" ObjectIDZND0="5693@0" Pin0InfoVect0LinkObjId="SW-35809_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-689 4755,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c4310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-805 4755,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5715@1" ObjectIDZND0="5683@0" Pin0InfoVect0LinkObjId="g_35e1a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35919_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-805 4755,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c37d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-732 4755,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5693@1" ObjectIDZND0="5715@x" ObjectIDZND1="5717@x" Pin0InfoVect0LinkObjId="SW-35919_0" Pin0InfoVect1LinkObjId="SW-35921_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35809_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-732 4755,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25f50a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-750 4755,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5693@x" ObjectIDND1="5717@x" ObjectIDZND0="5715@0" Pin0InfoVect0LinkObjId="SW-35919_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35809_0" Pin1InfoVect1LinkObjId="SW-35921_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-750 4755,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25f52c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-750 4777,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5693@x" ObjectIDND1="5715@x" ObjectIDZND0="5717@0" Pin0InfoVect0LinkObjId="SW-35921_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35809_0" Pin1InfoVect1LinkObjId="SW-35919_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-750 4777,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25f5760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4813,-750 4831,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5717@1" ObjectIDZND0="g_36a54b0@0" Pin0InfoVect0LinkObjId="g_36a54b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35921_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4813,-750 4831,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25fb820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-609 4754,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="5729@0" ObjectIDZND0="5694@1" Pin0InfoVect0LinkObjId="SW-35810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-609 4754,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27476f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-555 4754,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5694@0" ObjectIDZND0="5716@1" Pin0InfoVect0LinkObjId="SW-35920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-555 4754,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2786f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-498 4754,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5716@0" ObjectIDZND0="5684@0" Pin0InfoVect0LinkObjId="g_37a4d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-498 4754,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27485a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-372 4921,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5690@0" ObjectIDZND0="5711@1" Pin0InfoVect0LinkObjId="SW-35865_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-372 4921,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_277aa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-478 4921,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5684@0" ObjectIDZND0="5710@1" Pin0InfoVect0LinkObjId="SW-35864_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a4d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-478 4921,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37bb1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-420 4921,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5710@0" ObjectIDZND0="5690@1" Pin0InfoVect0LinkObjId="SW-35806_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-420 4921,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c4790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-315 4921,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="5711@0" ObjectIDZND0="5709@x" ObjectIDZND1="g_25c2d00@0" ObjectIDZND2="9806@0" Pin0InfoVect0LinkObjId="SW-35861_0" Pin0InfoVect1LinkObjId="g_25c2d00_0" Pin0InfoVect2LinkObjId="g_36946d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-315 4921,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2612270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-304 4874,-304 4874,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="busSection" EndDevType0="switch" ObjectIDND0="5711@x" ObjectIDND1="g_25c2d00@0" ObjectIDND2="9806@0" ObjectIDZND0="5709@1" Pin0InfoVect0LinkObjId="SW-35861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35865_0" Pin1InfoVect1LinkObjId="g_25c2d00_0" Pin1InfoVect2LinkObjId="g_36946d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-304 4874,-304 4874,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36946d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-257 4874,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5709@0" ObjectIDZND0="9806@0" Pin0InfoVect0LinkObjId="g_36f2bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-257 4874,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_390a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-376 4754,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5685@0" ObjectIDZND0="5727@1" Pin0InfoVect0LinkObjId="SW-36093_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35802_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-376 4754,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35ea090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-478 4754,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5684@0" ObjectIDZND0="9799@1" Pin0InfoVect0LinkObjId="SW-55225_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a4d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-478 4754,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34b6e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-424 4754,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9799@0" ObjectIDZND0="5685@1" Pin0InfoVect0LinkObjId="SW-35802_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55225_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-424 4754,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf6380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-373 4553,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5689@0" ObjectIDZND0="5708@1" Pin0InfoVect0LinkObjId="SW-35850_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35805_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-373 4553,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e32d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-478 4553,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5684@0" ObjectIDZND0="5707@1" Pin0InfoVect0LinkObjId="SW-35849_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a4d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-478 4553,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e324f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-421 4553,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5707@0" ObjectIDZND0="5689@1" Pin0InfoVect0LinkObjId="SW-35805_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35849_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-421 4553,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2783ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-316 4553,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5708@0" ObjectIDZND0="g_36e29a0@0" ObjectIDZND1="5706@x" Pin0InfoVect0LinkObjId="g_36e29a0_0" Pin0InfoVect1LinkObjId="SW-35846_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-316 4553,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b4940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-305 4506,-305 4506,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_36e29a0@0" ObjectIDND1="5708@x" ObjectIDZND0="5706@1" Pin0InfoVect0LinkObjId="SW-35846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36e29a0_0" Pin1InfoVect1LinkObjId="SW-35850_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-305 4506,-305 4506,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36f2bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-258 4506,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5706@0" ObjectIDZND0="9806@0" Pin0InfoVect0LinkObjId="g_36946d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-258 4506,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3451c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-372 4326,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5688@0" ObjectIDZND0="5705@1" Pin0InfoVect0LinkObjId="SW-35835_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-372 4326,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a3ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-478 4326,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5684@0" ObjectIDZND0="5704@1" Pin0InfoVect0LinkObjId="SW-35834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a4d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-478 4326,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e327a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-420 4326,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5704@0" ObjectIDZND0="5688@1" Pin0InfoVect0LinkObjId="SW-35804_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-420 4326,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af23c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-315 4326,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5705@0" ObjectIDZND0="g_26189d0@0" ObjectIDZND1="5703@x" Pin0InfoVect0LinkObjId="g_26189d0_0" Pin0InfoVect1LinkObjId="SW-35831_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35835_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-315 4326,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b6eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-304 4279,-304 4279,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_26189d0@0" ObjectIDND1="5705@x" ObjectIDZND0="5703@1" Pin0InfoVect0LinkObjId="SW-35831_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26189d0_0" Pin1InfoVect1LinkObjId="SW-35835_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-304 4279,-304 4279,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36f1ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4279,-257 4279,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5703@0" ObjectIDZND0="9806@0" Pin0InfoVect0LinkObjId="g_36946d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4279,-257 4279,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_366ade0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-375 3801,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5687@0" ObjectIDZND0="5702@1" Pin0InfoVect0LinkObjId="SW-35820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35803_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-375 3801,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27bfb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-478 3801,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5684@0" ObjectIDZND0="5701@1" Pin0InfoVect0LinkObjId="SW-35819_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a4d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-478 3801,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36e4820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-423 3801,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5701@0" ObjectIDZND0="5687@1" Pin0InfoVect0LinkObjId="SW-35803_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35819_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-423 3801,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_272b970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-318 3801,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5702@0" ObjectIDZND0="g_366bc10@0" ObjectIDZND1="5700@x" Pin0InfoVect0LinkObjId="g_366bc10_0" Pin0InfoVect1LinkObjId="SW-35816_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-318 3801,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3446310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-307 3754,-307 3754,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_366bc10@0" ObjectIDND1="5702@x" ObjectIDZND0="5700@1" Pin0InfoVect0LinkObjId="SW-35816_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_366bc10_0" Pin1InfoVect1LinkObjId="SW-35820_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-307 3754,-307 3754,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26fb360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3754,-260 3754,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5700@0" ObjectIDZND0="9806@0" Pin0InfoVect0LinkObjId="g_36946d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3754,-260 3754,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_277ed60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-319 4754,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5727@0" ObjectIDZND0="9806@0" Pin0InfoVect0LinkObjId="g_36946d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-36093_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-319 4754,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e2ff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-834 3955,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5683@0" ObjectIDZND0="5718@0" Pin0InfoVect0LinkObjId="SW-35947_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e1a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-834 3955,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e5d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-890 3955,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5718@1" ObjectIDZND0="5695@0" Pin0InfoVect0LinkObjId="SW-35811_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35947_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-890 3955,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2604c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-938 3955,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5695@1" ObjectIDZND0="5719@0" Pin0InfoVect0LinkObjId="SW-35948_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35811_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-938 3955,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_344fdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-995 3955,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="5719@1" ObjectIDZND0="g_377fc00@0" ObjectIDZND1="0@1" ObjectIDZND2="5720@x" Pin0InfoVect0LinkObjId="g_377fc00_0" Pin0InfoVect1LinkObjId="g_34a4f40_1" Pin0InfoVect2LinkObjId="SW-35949_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35948_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-995 3955,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3802530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-1024 3941,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_377fc00@0" ObjectIDND1="0@1" ObjectIDND2="5719@x" ObjectIDZND0="5720@1" Pin0InfoVect0LinkObjId="SW-35949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_377fc00_0" Pin1InfoVect1LinkObjId="g_34a4f40_1" Pin1InfoVect2LinkObjId="SW-35948_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-1024 3941,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3802740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3905,-1024 3891,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5720@0" ObjectIDZND0="g_37f36b0@0" Pin0InfoVect0LinkObjId="g_37f36b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3905,-1024 3891,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_378f370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-1073 3843,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="5719@x" ObjectIDND1="5720@x" ObjectIDND2="0@1" ObjectIDZND0="g_377fc00@0" Pin0InfoVect0LinkObjId="g_377fc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35948_0" Pin1InfoVect1LinkObjId="SW-35949_0" Pin1InfoVect2LinkObjId="g_34a4f40_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-1073 3843,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_378fa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3788,-1073 3843,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_377fc00@0" ObjectIDZND1="5719@x" ObjectIDZND2="5720@x" Pin0InfoVect0LinkObjId="g_377fc00_0" Pin0InfoVect1LinkObjId="SW-35948_0" Pin0InfoVect2LinkObjId="SW-35949_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34a4f40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3788,-1073 3843,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_378e950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-1073 3955,-1073 3955,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_377fc00@0" ObjectIDND1="0@1" ObjectIDZND0="5719@x" ObjectIDZND1="5720@x" Pin0InfoVect0LinkObjId="SW-35948_0" Pin0InfoVect1LinkObjId="SW-35949_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_377fc00_0" Pin1InfoVect1LinkObjId="g_34a4f40_1" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-1073 3955,-1073 3955,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3786ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-834 4755,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5683@0" ObjectIDZND0="5721@0" Pin0InfoVect0LinkObjId="SW-35964_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e1a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-834 4755,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37871d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-889 4755,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5721@1" ObjectIDZND0="5696@0" Pin0InfoVect0LinkObjId="SW-35812_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-889 4755,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3784700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-937 4755,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5696@1" ObjectIDZND0="5722@0" Pin0InfoVect0LinkObjId="SW-35965_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35812_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-937 4755,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_377f9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-1063 4669,-1063 4669,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5723@x" ObjectIDND1="5722@x" ObjectIDZND0="g_377c5c0@0" Pin0InfoVect0LinkObjId="g_377c5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35966_0" Pin1InfoVect1LinkObjId="SW-35965_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-1063 4669,-1063 4669,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35eb060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-1074 4830,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_377c5c0@0" ObjectIDND1="5722@x" ObjectIDZND0="5723@1" Pin0InfoVect0LinkObjId="SW-35966_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_377c5c0_0" Pin1InfoVect1LinkObjId="SW-35965_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-1074 4830,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33ccbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-1022 4830,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5723@0" ObjectIDZND0="g_33d04f0@1" Pin0InfoVect0LinkObjId="g_33d04f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35966_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-1022 4830,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cbd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-976 4830,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_33d04f0@0" ObjectIDZND0="g_33cbfc0@0" Pin0InfoVect0LinkObjId="g_33cbfc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d04f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-976 4830,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c7c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-1074 4830,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_377c5c0@0" ObjectIDZND1="5722@x" ObjectIDZND2="5723@x" Pin0InfoVect0LinkObjId="g_377c5c0_0" Pin0InfoVect1LinkObjId="SW-35965_0" Pin0InfoVect2LinkObjId="SW-35966_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-1074 4830,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c7e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-1074 4755,-1074 4755,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5723@x" ObjectIDZND0="g_377c5c0@0" ObjectIDZND1="5722@x" Pin0InfoVect0LinkObjId="g_377c5c0_0" Pin0InfoVect1LinkObjId="SW-35965_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35966_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-1074 4755,-1074 4755,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c6b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-994 4755,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5722@1" ObjectIDZND0="g_377c5c0@0" ObjectIDZND1="5723@x" Pin0InfoVect0LinkObjId="g_377c5c0_0" Pin0InfoVect1LinkObjId="SW-35966_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35965_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-994 4755,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e8260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-225 4921,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" BeginDevType2="switch" ObjectIDND0="g_25c2d00@0" ObjectIDND1="9806@0" ObjectIDND2="5711@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25c2d00_0" Pin1InfoVect1LinkObjId="g_36946d0_0" Pin1InfoVect2LinkObjId="SW-35865_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-225 4921,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_261e740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-213 4943,-225 4921,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_25c2d00@0" ObjectIDZND0="9806@0" ObjectIDZND1="5711@x" ObjectIDZND2="5709@x" Pin0InfoVect0LinkObjId="g_36946d0_0" Pin0InfoVect1LinkObjId="SW-35865_0" Pin0InfoVect2LinkObjId="SW-35861_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c2d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-213 4943,-225 4921,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2776530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-225 4921,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_25c2d00@0" ObjectIDZND0="9806@0" ObjectIDZND1="5711@x" ObjectIDZND2="5709@x" Pin0InfoVect0LinkObjId="g_36946d0_0" Pin0InfoVect1LinkObjId="SW-35865_0" Pin0InfoVect2LinkObjId="SW-35861_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c2d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-225 4921,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2776720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-226 4553,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_36e29a0@0" ObjectIDND1="5708@x" ObjectIDND2="5706@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36e29a0_0" Pin1InfoVect1LinkObjId="SW-35850_0" Pin1InfoVect2LinkObjId="SW-35846_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-226 4553,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_377d140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-226 4575,-226 4575,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5708@x" ObjectIDND1="5706@x" ObjectIDZND0="g_36e29a0@0" Pin0InfoVect0LinkObjId="g_36e29a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35850_0" Pin1InfoVect1LinkObjId="SW-35846_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-226 4575,-226 4575,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26fcae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-225 4326,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_26189d0@0" ObjectIDND1="5705@x" ObjectIDND2="5703@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26189d0_0" Pin1InfoVect1LinkObjId="SW-35835_0" Pin1InfoVect2LinkObjId="SW-35831_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-225 4326,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273eb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-213 4348,-225 4326,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_26189d0@0" ObjectIDZND0="5705@x" ObjectIDZND1="5703@x" Pin0InfoVect0LinkObjId="SW-35835_0" Pin0InfoVect1LinkObjId="SW-35831_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26189d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-213 4348,-225 4326,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35d4200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-228 3801,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_366bc10@0" ObjectIDND1="5702@x" ObjectIDND2="5700@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_366bc10_0" Pin1InfoVect1LinkObjId="SW-35820_0" Pin1InfoVect2LinkObjId="SW-35816_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-228 3801,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35d6170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-216 3823,-228 3801,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_366bc10@0" ObjectIDZND0="5702@x" ObjectIDZND1="5700@x" Pin0InfoVect0LinkObjId="SW-35820_0" Pin0InfoVect1LinkObjId="SW-35816_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_366bc10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-216 3823,-228 3801,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c0ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-226 4553,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36e29a0@0" ObjectIDZND0="5708@x" ObjectIDZND1="5706@x" Pin0InfoVect0LinkObjId="SW-35850_0" Pin0InfoVect1LinkObjId="SW-35846_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36e29a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-226 4553,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c1100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-244 4921,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="9806@0" ObjectIDZND0="g_25c2d00@0" ObjectIDZND1="5711@x" ObjectIDZND2="5709@x" Pin0InfoVect0LinkObjId="g_25c2d00_0" Pin0InfoVect1LinkObjId="SW-35865_0" Pin0InfoVect2LinkObjId="SW-35861_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36946d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-244 4921,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_378bee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-256 4921,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_25c2d00@0" ObjectIDND1="9806@0" ObjectIDZND0="5711@x" ObjectIDZND1="5709@x" Pin0InfoVect0LinkObjId="SW-35865_0" Pin0InfoVect1LinkObjId="SW-35861_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25c2d00_0" Pin1InfoVect1LinkObjId="g_36946d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-256 4921,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_378c140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-225 4326,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_26189d0@0" ObjectIDZND0="5705@x" ObjectIDZND1="5703@x" Pin0InfoVect0LinkObjId="SW-35835_0" Pin0InfoVect1LinkObjId="SW-35831_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26189d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-225 4326,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37b8830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-307 3801,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5702@x" ObjectIDND1="5700@x" ObjectIDZND0="g_366bc10@0" Pin0InfoVect0LinkObjId="g_366bc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35820_0" Pin1InfoVect1LinkObjId="SW-35816_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-307 3801,-228 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-31" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3409.000000 -1061.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31" ObjectName="DYN-CX_DD"/>
     <cge:Meas_Ref ObjectId="31"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2747140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 946.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2747330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 931.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27453e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 916.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2745610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4626.000000 946.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2779540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.000000 931.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2779750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4640.000000 916.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e44b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 584.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e4740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 569.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2611980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 554.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2611d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4626.000000 584.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2616aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.000000 569.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2616c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4640.000000 554.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d9b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.000000 112.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d9da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 97.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2726810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.000000 82.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2726c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4509.000000 112.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261b890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.000000 97.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261ba90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4523.000000 82.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ac810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4721.000000 179.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25aca90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4710.000000 164.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f6670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 149.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f6a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4886.000000 112.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2734ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 97.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2734d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 82.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b9ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 112.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2723d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 82.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2723f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3737.000000 97.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_273e560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 672.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2601300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 657.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26014d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 642.000000) translate(0,12)">绕组温度(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26017c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 424.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26019d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.000000 409.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259b430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 438.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259b630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 453.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259b7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 467.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259bae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 679.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2722ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 664.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27230b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4870.000000 649.000000) translate(0,12)">绕组温度(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27233a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 720.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27235d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 705.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a8690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 689.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a8860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 735.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2597640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.000000 740.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2597830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 725.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2597a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4626.000000 709.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2597c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.000000 755.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24895e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.000000 751.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2489850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 801.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2489a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 816.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2489c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 830.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2604fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.000000 783.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26051d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3731.000000 766.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_35e8fd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 -742.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37a08d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -751.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a54b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.000000 -744.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37f36b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 -1018.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2f920" transform="matrix(1.000000 0.000000 0.000000 1.000000 3968.000000 -931.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e231c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3962.000000 -879.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2cc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3962.000000 -984.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2ce70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3903.000000 -1050.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2c3a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4764.000000 -931.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e34fb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -878.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2a6e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -983.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2a970" transform="matrix(1.000000 0.000000 0.000000 1.000000 4837.000000 -1047.000000) translate(0,12)">3829</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d0d80" transform="matrix(1.000000 0.000000 0.000000 1.000000 4848.000000 -1095.000000) translate(0,12)">35kV果大T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_260d840" transform="matrix(1.000000 0.000000 0.000000 1.000000 3779.000000 -1095.000000) translate(0,12)">35kV上大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2771860" transform="matrix(1.000000 0.000000 0.000000 1.000000 3767.000000 -860.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3668ea0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3669.000000 -502.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2745bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -266.000000) translate(0,15)">10kV旁母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2745d50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3966.000000 -723.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2746a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3962.000000 -792.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2746c00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3972.000000 -773.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25f0d60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3987.000000 -654.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25f0fe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4786.000000 -656.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d5f5b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3966.000000 -573.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d5f750" transform="matrix(1.000000 0.000000 0.000000 1.000000 3964.000000 -520.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2747910" transform="matrix(1.000000 0.000000 0.000000 1.000000 4354.000000 -799.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2747b50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4365.000000 -782.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368c5e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4764.000000 -724.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368c820" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -793.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2786270" transform="matrix(1.000000 0.000000 0.000000 1.000000 4772.000000 -775.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2786480" transform="matrix(1.000000 0.000000 0.000000 1.000000 4763.000000 -576.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2608b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -523.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2608d80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3810.000000 -396.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2610300" transform="matrix(1.000000 0.000000 0.000000 1.000000 3808.000000 -448.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2610510" transform="matrix(1.000000 0.000000 0.000000 1.000000 3808.000000 -343.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1d980" transform="matrix(1.000000 0.000000 0.000000 1.000000 4117.000000 -439.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4335.000000 -393.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2610950" transform="matrix(1.000000 0.000000 0.000000 1.000000 4333.000000 -445.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2610b50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4286.000000 -282.000000) translate(0,12)">0825</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_260ef70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4333.000000 -340.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_260f1b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4562.000000 -394.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2615070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4560.000000 -446.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2615290" transform="matrix(1.000000 0.000000 0.000000 1.000000 4513.000000 -283.000000) translate(0,12)">0835</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3424730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4560.000000 -341.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3424970" transform="matrix(1.000000 0.000000 0.000000 1.000000 4763.000000 -397.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261c9b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -449.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261cbc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -344.000000) translate(0,12)">0855</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26067e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4930.000000 -393.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2606a20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4928.000000 -445.000000) translate(0,12)">0861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2786990" transform="matrix(1.000000 0.000000 0.000000 1.000000 4881.000000 -282.000000) translate(0,12)">0865</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2786b90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4928.000000 -340.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26099e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4303.000000 -606.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2609c20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3774.000000 -184.000000) translate(0,15)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2609c20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3774.000000 -184.000000) translate(0,33)">溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2609c20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3774.000000 -184.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_366c2b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3925.000000 -304.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_366c4d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4063.000000 -277.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_277c4f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -184.000000) translate(0,15)">库</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_277c4f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -184.000000) translate(0,33)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_277c4f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -184.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_277c710" transform="matrix(1.000000 0.000000 0.000000 1.000000 4528.000000 -220.000000) translate(0,15)">卸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_277c710" transform="matrix(1.000000 0.000000 0.000000 1.000000 4528.000000 -220.000000) translate(0,33)">油</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_277c710" transform="matrix(1.000000 0.000000 0.000000 1.000000 4528.000000 -220.000000) translate(0,51)">泵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_277c710" transform="matrix(1.000000 0.000000 0.000000 1.000000 4528.000000 -220.000000) translate(0,69)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_277c710" transform="matrix(1.000000 0.000000 0.000000 1.000000 4528.000000 -220.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26058e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -226.000000) translate(0,15)">旁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26058e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -226.000000) translate(0,33)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2605ab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4893.000000 -184.000000) translate(0,15)">和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2605ab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4893.000000 -184.000000) translate(0,33)">平</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2605ab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4893.000000 -184.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_36a6e40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3277.500000 -1141.500000) translate(0,16)">大德变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -563.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3134.000000 -1001.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3134.000000 -1001.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3134.000000 -1001.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3134.000000 -1001.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3134.000000 -1001.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3134.000000 -1001.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_37b9bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3134.000000 -1001.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2724150" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.000000 -285.000000) translate(0,12)">0815</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277b640" transform="matrix(1.000000 0.000000 0.000000 1.000000 3961.000000 -449.000000) translate(0,12)">0031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2595fe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3985.000000 -623.000000) translate(0,12)">SZ11-31500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2596240" transform="matrix(1.000000 0.000000 0.000000 1.000000 4784.000000 -628.000000) translate(0,12)">S9-20000/35</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-35884">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -762.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5712" ObjectName="SW-CX_DD.CX_DD_3011SW"/>
     <cge:Meas_Ref ObjectId="35884"/>
    <cge:TPSR_Ref TObjectID="5712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35886">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -743.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5714" ObjectName="SW-CX_DD.CX_DD_30117SW"/>
     <cge:Meas_Ref ObjectId="35886"/>
    <cge:TPSR_Ref TObjectID="5714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35982">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -771.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5724" ObjectName="SW-CX_DD.CX_DD_3901SW"/>
     <cge:Meas_Ref ObjectId="35982"/>
    <cge:TPSR_Ref TObjectID="5724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35985">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -752.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5725" ObjectName="SW-CX_DD.CX_DD_39017SW"/>
     <cge:Meas_Ref ObjectId="35985"/>
    <cge:TPSR_Ref TObjectID="5725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35885">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -491.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5713" ObjectName="SW-CX_DD.CX_DD_0011SW"/>
     <cge:Meas_Ref ObjectId="35885"/>
    <cge:TPSR_Ref TObjectID="5713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -419.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35990">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5726" ObjectName="SW-CX_DD.CX_DD_0901SW"/>
     <cge:Meas_Ref ObjectId="35990"/>
    <cge:TPSR_Ref TObjectID="5726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35919">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -764.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5715" ObjectName="SW-CX_DD.CX_DD_3021SW"/>
     <cge:Meas_Ref ObjectId="35919"/>
    <cge:TPSR_Ref TObjectID="5715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35921">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4772.000000 -745.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5717" ObjectName="SW-CX_DD.CX_DD_30217SW"/>
     <cge:Meas_Ref ObjectId="35921"/>
    <cge:TPSR_Ref TObjectID="5717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35920">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -493.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5716" ObjectName="SW-CX_DD.CX_DD_0021SW"/>
     <cge:Meas_Ref ObjectId="35920"/>
    <cge:TPSR_Ref TObjectID="5716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35864">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4912.000000 -415.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5710" ObjectName="SW-CX_DD.CX_DD_0861SW"/>
     <cge:Meas_Ref ObjectId="35864"/>
    <cge:TPSR_Ref TObjectID="5710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35865">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4912.000000 -310.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5711" ObjectName="SW-CX_DD.CX_DD_0866SW"/>
     <cge:Meas_Ref ObjectId="35865"/>
    <cge:TPSR_Ref TObjectID="5711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55225">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -419.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9799" ObjectName="SW-CX_DD.CX_DD_0851SW"/>
     <cge:Meas_Ref ObjectId="55225"/>
    <cge:TPSR_Ref TObjectID="9799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-36093">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -314.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5727" ObjectName="SW-CX_DD.CX_DD_0855SW"/>
     <cge:Meas_Ref ObjectId="36093"/>
    <cge:TPSR_Ref TObjectID="5727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35849">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 -416.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5707" ObjectName="SW-CX_DD.CX_DD_0831SW"/>
     <cge:Meas_Ref ObjectId="35849"/>
    <cge:TPSR_Ref TObjectID="5707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35850">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 -311.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5708" ObjectName="SW-CX_DD.CX_DD_0836SW"/>
     <cge:Meas_Ref ObjectId="35850"/>
    <cge:TPSR_Ref TObjectID="5708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35846">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5706" ObjectName="SW-CX_DD.CX_DD_0835SW"/>
     <cge:Meas_Ref ObjectId="35846"/>
    <cge:TPSR_Ref TObjectID="5706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.000000 -415.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5704" ObjectName="SW-CX_DD.CX_DD_0821SW"/>
     <cge:Meas_Ref ObjectId="35834"/>
    <cge:TPSR_Ref TObjectID="5704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35835">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.000000 -310.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5705" ObjectName="SW-CX_DD.CX_DD_0826SW"/>
     <cge:Meas_Ref ObjectId="35835"/>
    <cge:TPSR_Ref TObjectID="5705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4270.000000 -252.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5703" ObjectName="SW-CX_DD.CX_DD_0825SW"/>
     <cge:Meas_Ref ObjectId="35831"/>
    <cge:TPSR_Ref TObjectID="5703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35819">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.000000 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5701" ObjectName="SW-CX_DD.CX_DD_0811SW"/>
     <cge:Meas_Ref ObjectId="35819"/>
    <cge:TPSR_Ref TObjectID="5701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35820">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.000000 -313.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5702" ObjectName="SW-CX_DD.CX_DD_0816SW"/>
     <cge:Meas_Ref ObjectId="35820"/>
    <cge:TPSR_Ref TObjectID="5702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35816">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3745.000000 -255.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5700" ObjectName="SW-CX_DD.CX_DD_0815SW"/>
     <cge:Meas_Ref ObjectId="35816"/>
    <cge:TPSR_Ref TObjectID="5700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35947">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -849.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5718" ObjectName="SW-CX_DD.CX_DD_3811SW"/>
     <cge:Meas_Ref ObjectId="35947"/>
    <cge:TPSR_Ref TObjectID="5718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35948">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -954.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5719" ObjectName="SW-CX_DD.CX_DD_3816SW"/>
     <cge:Meas_Ref ObjectId="35948"/>
    <cge:TPSR_Ref TObjectID="5719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35949">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -1019.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5720" ObjectName="SW-CX_DD.CX_DD_38167SW"/>
     <cge:Meas_Ref ObjectId="35949"/>
    <cge:TPSR_Ref TObjectID="5720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35964">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -848.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5721" ObjectName="SW-CX_DD.CX_DD_3821SW"/>
     <cge:Meas_Ref ObjectId="35964"/>
    <cge:TPSR_Ref TObjectID="5721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35965">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -953.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5722" ObjectName="SW-CX_DD.CX_DD_3826SW"/>
     <cge:Meas_Ref ObjectId="35965"/>
    <cge:TPSR_Ref TObjectID="5722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35966">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -1017.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5723" ObjectName="SW-CX_DD.CX_DD_3829SW"/>
     <cge:Meas_Ref ObjectId="35966"/>
    <cge:TPSR_Ref TObjectID="5723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35861">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.000000 -252.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5709" ObjectName="SW-CX_DD.CX_DD_0865SW"/>
     <cge:Meas_Ref ObjectId="35861"/>
    <cge:TPSR_Ref TObjectID="5709"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="5683" cx="3955" cy="-834" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5683" cx="4347" cy="-834" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5684" cx="3954" cy="-478" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5684" cx="3954" cy="-478" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5684" cx="4110" cy="-478" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5683" cx="4755" cy="-834" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5684" cx="4754" cy="-478" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5684" cx="4921" cy="-478" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5684" cx="4754" cy="-478" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9806" cx="4754" cy="-244" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5684" cx="4553" cy="-478" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9806" cx="4506" cy="-244" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5684" cx="4326" cy="-478" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9806" cx="4279" cy="-244" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5684" cx="3801" cy="-478" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9806" cx="3754" cy="-244" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5683" cx="3955" cy="-834" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5683" cx="4755" cy="-834" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9806" cx="4874" cy="-244" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DD"/>
</svg>