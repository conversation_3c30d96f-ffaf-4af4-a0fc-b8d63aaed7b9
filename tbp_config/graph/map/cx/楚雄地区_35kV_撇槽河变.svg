<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-277" aopId="3932678" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1029 2136 1333">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="122" y2="130"/>
    <polyline arcFlag="1" points="37,122 35,122 33,121 32,121 30,120 29,119 27,118 26,116 25,114 25,113 24,111 24,109 24,107 25,105 25,104 26,102 27,101 29,99 30,98 32,97 33,97 35,96 37,96 39,96 41,97 42,97 44,98 45,99 47,101 48,102 49,104 49,105 50,107 50,109 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="50" x2="38" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.915724" x1="37" x2="37" y1="109" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="23" y2="14"/>
    <polyline arcFlag="1" points="13,23 12,23 12,23 11,23 10,23 10,24 9,24 8,25 8,25 8,26 7,27 7,27 7,28 7,29 7,30 7,30 8,31 8,32 8,32 9,33 10,33 10,34 11,34 12,34 12,34 13,34 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,34 12,34 12,34 11,34 10,34 10,35 9,35 8,36 8,36 8,37 7,38 7,38 7,39 7,40 7,41 7,41 8,42 8,43 8,43 9,44 10,44 10,45 11,45 12,45 12,45 13,45 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,45 12,45 12,45 11,45 10,45 10,46 9,46 8,47 8,47 8,48 7,49 7,49 7,50 7,51 7,52 7,52 8,53 8,54 8,54 9,55 10,55 10,56 11,56 12,56 12,56 13,56 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="65" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="13" y2="13"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="56" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="16" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="voltageTransformer:shape135">
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="52"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8e690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8f050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a8f9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a906b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a918b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a924c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a93070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a93aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2a998a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9b530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9c180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9d060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a9d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa0a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa1bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa2570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa3a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa4f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa5a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa6aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa76e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ab5eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa8d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aa9f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aab4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1343" width="2146" x="11" y="-1034"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="1065" x2="1065" y1="214" y2="207"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="1546" x2="1546" y1="213" y2="206"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-230357">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 -321.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38355" ObjectName="SW-CX_PCH.CX_PCH_001XC1"/>
     <cge:Meas_Ref ObjectId="230357"/>
    <cge:TPSR_Ref TObjectID="38355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230357">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 -256.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38354" ObjectName="SW-CX_PCH.CX_PCH_001XC"/>
     <cge:Meas_Ref ObjectId="230357"/>
    <cge:TPSR_Ref TObjectID="38354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230336">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 -793.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38348" ObjectName="SW-CX_PCH.CX_PCH_371XC1"/>
     <cge:Meas_Ref ObjectId="230336"/>
    <cge:TPSR_Ref TObjectID="38348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230336">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 -728.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38347" ObjectName="SW-CX_PCH.CX_PCH_371XC"/>
     <cge:Meas_Ref ObjectId="230336"/>
    <cge:TPSR_Ref TObjectID="38347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230337">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1337.000000 -849.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38349" ObjectName="SW-CX_PCH.CX_PCH_37167SW"/>
     <cge:Meas_Ref ObjectId="230337"/>
    <cge:TPSR_Ref TObjectID="38349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230353">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 -651.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38351" ObjectName="SW-CX_PCH.CX_PCH_301XC"/>
     <cge:Meas_Ref ObjectId="230353"/>
    <cge:TPSR_Ref TObjectID="38351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230353">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 -586.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38352" ObjectName="SW-CX_PCH.CX_PCH_301XC1"/>
     <cge:Meas_Ref ObjectId="230353"/>
    <cge:TPSR_Ref TObjectID="38352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230367">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 -320.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38361" ObjectName="SW-CX_PCH.CX_PCH_002XC1"/>
     <cge:Meas_Ref ObjectId="230367"/>
    <cge:TPSR_Ref TObjectID="38361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230367">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 -255.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38360" ObjectName="SW-CX_PCH.CX_PCH_002XC"/>
     <cge:Meas_Ref ObjectId="230367"/>
    <cge:TPSR_Ref TObjectID="38360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230363">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 -649.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38357" ObjectName="SW-CX_PCH.CX_PCH_302XC"/>
     <cge:Meas_Ref ObjectId="230363"/>
    <cge:TPSR_Ref TObjectID="38357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230363">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 -584.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38358" ObjectName="SW-CX_PCH.CX_PCH_302XC1"/>
     <cge:Meas_Ref ObjectId="230363"/>
    <cge:TPSR_Ref TObjectID="38358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230373">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38363" ObjectName="SW-CX_PCH.CX_PCH_072XC"/>
     <cge:Meas_Ref ObjectId="230373"/>
    <cge:TPSR_Ref TObjectID="38363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230373">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38364" ObjectName="SW-CX_PCH.CX_PCH_072XC1"/>
     <cge:Meas_Ref ObjectId="230373"/>
    <cge:TPSR_Ref TObjectID="38364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38365" ObjectName="SW-CX_PCH.CX_PCH_07267SW"/>
     <cge:Meas_Ref ObjectId="230374"/>
    <cge:TPSR_Ref TObjectID="38365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38367" ObjectName="SW-CX_PCH.CX_PCH_073XC"/>
     <cge:Meas_Ref ObjectId="230378"/>
    <cge:TPSR_Ref TObjectID="38367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38368" ObjectName="SW-CX_PCH.CX_PCH_073XC1"/>
     <cge:Meas_Ref ObjectId="230378"/>
    <cge:TPSR_Ref TObjectID="38368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 -27.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38369" ObjectName="SW-CX_PCH.CX_PCH_07367SW"/>
     <cge:Meas_Ref ObjectId="230379"/>
    <cge:TPSR_Ref TObjectID="38369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230388">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1055.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38375" ObjectName="SW-CX_PCH.CX_PCH_074XC"/>
     <cge:Meas_Ref ObjectId="230388"/>
    <cge:TPSR_Ref TObjectID="38375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230388">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1055.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38376" ObjectName="SW-CX_PCH.CX_PCH_074XC1"/>
     <cge:Meas_Ref ObjectId="230388"/>
    <cge:TPSR_Ref TObjectID="38376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230390">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1090.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38378" ObjectName="SW-CX_PCH.CX_PCH_07430SW"/>
     <cge:Meas_Ref ObjectId="230390"/>
    <cge:TPSR_Ref TObjectID="38378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230401">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1420.000000 -154.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38387" ObjectName="SW-CX_PCH.CX_PCH_012XC"/>
     <cge:Meas_Ref ObjectId="230401"/>
    <cge:TPSR_Ref TObjectID="38387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230401">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1420.000000 -89.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38388" ObjectName="SW-CX_PCH.CX_PCH_012XC1"/>
     <cge:Meas_Ref ObjectId="230401"/>
    <cge:TPSR_Ref TObjectID="38388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230389">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 53.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38377" ObjectName="SW-CX_PCH.CX_PCH_0746SW"/>
     <cge:Meas_Ref ObjectId="230389"/>
    <cge:TPSR_Ref TObjectID="38377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 255.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 104.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38379" ObjectName="SW-CX_PCH.CX_PCH_07460SW"/>
     <cge:Meas_Ref ObjectId="230391"/>
    <cge:TPSR_Ref TObjectID="38379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230395">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1536.000000 -154.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38381" ObjectName="SW-CX_PCH.CX_PCH_075XC"/>
     <cge:Meas_Ref ObjectId="230395"/>
    <cge:TPSR_Ref TObjectID="38381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230395">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1536.000000 -89.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38382" ObjectName="SW-CX_PCH.CX_PCH_075XC1"/>
     <cge:Meas_Ref ObjectId="230395"/>
    <cge:TPSR_Ref TObjectID="38382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230397">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1571.000000 -30.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38384" ObjectName="SW-CX_PCH.CX_PCH_07530SW"/>
     <cge:Meas_Ref ObjectId="230397"/>
    <cge:TPSR_Ref TObjectID="38384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230396">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.000000 52.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38383" ObjectName="SW-CX_PCH.CX_PCH_0756SW"/>
     <cge:Meas_Ref ObjectId="230396"/>
    <cge:TPSR_Ref TObjectID="38383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.000000 254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230398">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1579.000000 103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38385" ObjectName="SW-CX_PCH.CX_PCH_07560SW"/>
     <cge:Meas_Ref ObjectId="230398"/>
    <cge:TPSR_Ref TObjectID="38385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1716.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38449" ObjectName="SW-CX_PCH.CX_PCH_076XC"/>
     <cge:Meas_Ref ObjectId="231052"/>
    <cge:TPSR_Ref TObjectID="38449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1716.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38450" ObjectName="SW-CX_PCH.CX_PCH_076XC1"/>
     <cge:Meas_Ref ObjectId="231052"/>
    <cge:TPSR_Ref TObjectID="38450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231053">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1751.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38451" ObjectName="SW-CX_PCH.CX_PCH_07667SW"/>
     <cge:Meas_Ref ObjectId="231053"/>
    <cge:TPSR_Ref TObjectID="38451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230384">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1937.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38373" ObjectName="SW-CX_PCH.CX_PCH_07767SW"/>
     <cge:Meas_Ref ObjectId="230384"/>
    <cge:TPSR_Ref TObjectID="38373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230383">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1902.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38371" ObjectName="SW-CX_PCH.CX_PCH_077XC"/>
     <cge:Meas_Ref ObjectId="230383"/>
    <cge:TPSR_Ref TObjectID="38371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230383">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1902.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38372" ObjectName="SW-CX_PCH.CX_PCH_077XC1"/>
     <cge:Meas_Ref ObjectId="230383"/>
    <cge:TPSR_Ref TObjectID="38372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1097.000000 -781.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38392" ObjectName="SW-CX_PCH.CX_PCH_3901XC1"/>
     <cge:Meas_Ref ObjectId="230404"/>
    <cge:TPSR_Ref TObjectID="38392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1097.000000 -730.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38391" ObjectName="SW-CX_PCH.CX_PCH_3901XC"/>
     <cge:Meas_Ref ObjectId="230404"/>
    <cge:TPSR_Ref TObjectID="38391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1548.000000 -780.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38394" ObjectName="SW-CX_PCH.CX_PCH_3721XC1"/>
     <cge:Meas_Ref ObjectId="230405"/>
    <cge:TPSR_Ref TObjectID="38394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1548.000000 -729.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38393" ObjectName="SW-CX_PCH.CX_PCH_3721XC"/>
     <cge:Meas_Ref ObjectId="230405"/>
    <cge:TPSR_Ref TObjectID="38393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230407">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -279.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38398" ObjectName="SW-CX_PCH.CX_PCH_0901XC1"/>
     <cge:Meas_Ref ObjectId="230407"/>
    <cge:TPSR_Ref TObjectID="38398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230407">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -228.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38397" ObjectName="SW-CX_PCH.CX_PCH_0901XC"/>
     <cge:Meas_Ref ObjectId="230407"/>
    <cge:TPSR_Ref TObjectID="38397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1966.000000 -282.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38400" ObjectName="SW-CX_PCH.CX_PCH_0902XC1"/>
     <cge:Meas_Ref ObjectId="230408"/>
    <cge:TPSR_Ref TObjectID="38400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1966.000000 -231.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38399" ObjectName="SW-CX_PCH.CX_PCH_0902XC"/>
     <cge:Meas_Ref ObjectId="230408"/>
    <cge:TPSR_Ref TObjectID="38399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230402">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1265.000000 -147.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38389" ObjectName="SW-CX_PCH.CX_PCH_0121XC"/>
     <cge:Meas_Ref ObjectId="230402"/>
    <cge:TPSR_Ref TObjectID="38389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230402">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1265.000000 -96.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38390" ObjectName="SW-CX_PCH.CX_PCH_0121XC1"/>
     <cge:Meas_Ref ObjectId="230402"/>
    <cge:TPSR_Ref TObjectID="38390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 479.000000 -85.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38396" ObjectName="SW-CX_PCH.CX_PCH_0711XC1"/>
     <cge:Meas_Ref ObjectId="230406"/>
    <cge:TPSR_Ref TObjectID="38396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 479.000000 -128.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38395" ObjectName="SW-CX_PCH.CX_PCH_0711XC"/>
     <cge:Meas_Ref ObjectId="230406"/>
    <cge:TPSR_Ref TObjectID="38395"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_PCH.CX_PCH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="978,-708 1703,-708 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38405" ObjectName="BS-CX_PCH.CX_PCH_3IM"/>
    <cge:TPSR_Ref TObjectID="38405"/></metadata>
   <polyline fill="none" opacity="0" points="978,-708 1703,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_PCH.CX_PCH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="421,-205 1307,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38406" ObjectName="BS-CX_PCH.CX_PCH_9IM"/>
    <cge:TPSR_Ref TObjectID="38406"/></metadata>
   <polyline fill="none" opacity="0" points="421,-205 1307,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_PCH.CX_PCH_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-205 2059,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38407" ObjectName="BS-CX_PCH.CX_PCH_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="38407"/></metadata>
   <polyline fill="none" opacity="0" points="1393,-205 2059,-205 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1544.000000 -863.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1544.000000 -863.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_PCH.CX_PCH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="57509"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.000000 -375.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.000000 -375.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="38401" ObjectName="TF-CX_PCH.CX_PCH_1T"/>
    <cge:TPSR_Ref TObjectID="38401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_PCH.CX_PCH_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="57513"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1521.000000 -374.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1521.000000 -374.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="38402" ObjectName="TF-CX_PCH.CX_PCH_2T"/>
    <cge:TPSR_Ref TObjectID="38402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 474.000000 91.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 474.000000 91.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2226810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1103.000000 -490.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27b71b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1126.000000 -353.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_216b840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 585.000000 -371.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f96f80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1277.000000 -849.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_325b610">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 -808.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3272ac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 -814.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2169e70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1060.000000 -863.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_225ebf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1053.000000 -502.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28338e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1555.000000 -486.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3272fd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1579.000000 -352.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_216dad0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1506.000000 -498.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b88710">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.000000 -316.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b69f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.000000 7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2220890">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 614.000000 -16.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a95f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 843.000000 9.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28043d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 -14.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_228fce0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1060.000000 7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2229480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -16.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_228b8f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 448.000000 28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21bbcb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -41.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2264c60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 986.000000 70.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2232dc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1541.000000 6.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ded40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1505.000000 -17.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2252e40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 69.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b21f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1721.000000 7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22bdc20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1685.000000 -16.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22cff80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1907.000000 7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22a4460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1871.000000 -16.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21e1630">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1922.000000 -373.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21e2b00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1966.000000 -318.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2271f40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1597.000000 -851.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 175.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38362"/>
     <cge:Term_Ref ObjectID="57429"/>
    <cge:TPSR_Ref TObjectID="38362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 175.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38362"/>
     <cge:Term_Ref ObjectID="57429"/>
    <cge:TPSR_Ref TObjectID="38362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 175.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38362"/>
     <cge:Term_Ref ObjectID="57429"/>
    <cge:TPSR_Ref TObjectID="38362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 175.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38366"/>
     <cge:Term_Ref ObjectID="57437"/>
    <cge:TPSR_Ref TObjectID="38366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 175.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38366"/>
     <cge:Term_Ref ObjectID="57437"/>
    <cge:TPSR_Ref TObjectID="38366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 175.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38366"/>
     <cge:Term_Ref ObjectID="57437"/>
    <cge:TPSR_Ref TObjectID="38366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230536" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.000000 -68.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38386"/>
     <cge:Term_Ref ObjectID="57477"/>
    <cge:TPSR_Ref TObjectID="38386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230537" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.000000 -68.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38386"/>
     <cge:Term_Ref ObjectID="57477"/>
    <cge:TPSR_Ref TObjectID="38386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.000000 -68.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38386"/>
     <cge:Term_Ref ObjectID="57477"/>
    <cge:TPSR_Ref TObjectID="38386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 175.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38408"/>
     <cge:Term_Ref ObjectID="57518"/>
    <cge:TPSR_Ref TObjectID="38408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230519" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 175.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230519" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38408"/>
     <cge:Term_Ref ObjectID="57518"/>
    <cge:TPSR_Ref TObjectID="38408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 175.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38408"/>
     <cge:Term_Ref ObjectID="57518"/>
    <cge:TPSR_Ref TObjectID="38408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1898.000000 175.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38370"/>
     <cge:Term_Ref ObjectID="57445"/>
    <cge:TPSR_Ref TObjectID="38370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1898.000000 175.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38370"/>
     <cge:Term_Ref ObjectID="57445"/>
    <cge:TPSR_Ref TObjectID="38370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1898.000000 175.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38370"/>
     <cge:Term_Ref ObjectID="57445"/>
    <cge:TPSR_Ref TObjectID="38370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 -318.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38359"/>
     <cge:Term_Ref ObjectID="57423"/>
    <cge:TPSR_Ref TObjectID="38359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 -318.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38359"/>
     <cge:Term_Ref ObjectID="57423"/>
    <cge:TPSR_Ref TObjectID="38359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 -318.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38359"/>
     <cge:Term_Ref ObjectID="57423"/>
    <cge:TPSR_Ref TObjectID="38359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1701.000000 -653.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38356"/>
     <cge:Term_Ref ObjectID="57417"/>
    <cge:TPSR_Ref TObjectID="38356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1701.000000 -653.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38356"/>
     <cge:Term_Ref ObjectID="57417"/>
    <cge:TPSR_Ref TObjectID="38356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1701.000000 -653.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38356"/>
     <cge:Term_Ref ObjectID="57417"/>
    <cge:TPSR_Ref TObjectID="38356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -652.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38350"/>
     <cge:Term_Ref ObjectID="57405"/>
    <cge:TPSR_Ref TObjectID="38350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -652.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38350"/>
     <cge:Term_Ref ObjectID="57405"/>
    <cge:TPSR_Ref TObjectID="38350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -652.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38350"/>
     <cge:Term_Ref ObjectID="57405"/>
    <cge:TPSR_Ref TObjectID="38350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -320.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38353"/>
     <cge:Term_Ref ObjectID="57411"/>
    <cge:TPSR_Ref TObjectID="38353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -320.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38353"/>
     <cge:Term_Ref ObjectID="57411"/>
    <cge:TPSR_Ref TObjectID="38353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230438" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -320.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230438" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38353"/>
     <cge:Term_Ref ObjectID="57411"/>
    <cge:TPSR_Ref TObjectID="38353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1435.000000 -796.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38346"/>
     <cge:Term_Ref ObjectID="57397"/>
    <cge:TPSR_Ref TObjectID="38346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1435.000000 -796.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38346"/>
     <cge:Term_Ref ObjectID="57397"/>
    <cge:TPSR_Ref TObjectID="38346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1435.000000 -796.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38346"/>
     <cge:Term_Ref ObjectID="57397"/>
    <cge:TPSR_Ref TObjectID="38346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-230485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -315.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38406"/>
     <cge:Term_Ref ObjectID="57516"/>
    <cge:TPSR_Ref TObjectID="38406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-230486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -315.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38406"/>
     <cge:Term_Ref ObjectID="57516"/>
    <cge:TPSR_Ref TObjectID="38406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-230487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -315.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38406"/>
     <cge:Term_Ref ObjectID="57516"/>
    <cge:TPSR_Ref TObjectID="38406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-230491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -315.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38406"/>
     <cge:Term_Ref ObjectID="57516"/>
    <cge:TPSR_Ref TObjectID="38406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-230488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -315.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38406"/>
     <cge:Term_Ref ObjectID="57516"/>
    <cge:TPSR_Ref TObjectID="38406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-230492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2115.000000 -310.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38407"/>
     <cge:Term_Ref ObjectID="57517"/>
    <cge:TPSR_Ref TObjectID="38407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-230493" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2115.000000 -310.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38407"/>
     <cge:Term_Ref ObjectID="57517"/>
    <cge:TPSR_Ref TObjectID="38407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-230494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2115.000000 -310.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38407"/>
     <cge:Term_Ref ObjectID="57517"/>
    <cge:TPSR_Ref TObjectID="38407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-230498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2115.000000 -310.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38407"/>
     <cge:Term_Ref ObjectID="57517"/>
    <cge:TPSR_Ref TObjectID="38407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-230495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2115.000000 -310.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38407"/>
     <cge:Term_Ref ObjectID="57517"/>
    <cge:TPSR_Ref TObjectID="38407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230502" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -158.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230502" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38374"/>
     <cge:Term_Ref ObjectID="57453"/>
    <cge:TPSR_Ref TObjectID="38374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -158.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38374"/>
     <cge:Term_Ref ObjectID="57453"/>
    <cge:TPSR_Ref TObjectID="38374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-230503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -158.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38374"/>
     <cge:Term_Ref ObjectID="57453"/>
    <cge:TPSR_Ref TObjectID="38374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230507" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1658.000000 -161.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38380"/>
     <cge:Term_Ref ObjectID="57465"/>
    <cge:TPSR_Ref TObjectID="38380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1658.000000 -161.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38380"/>
     <cge:Term_Ref ObjectID="57465"/>
    <cge:TPSR_Ref TObjectID="38380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-230508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1658.000000 -161.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38380"/>
     <cge:Term_Ref ObjectID="57465"/>
    <cge:TPSR_Ref TObjectID="38380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-230478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38405"/>
     <cge:Term_Ref ObjectID="57515"/>
    <cge:TPSR_Ref TObjectID="38405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-230479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38405"/>
     <cge:Term_Ref ObjectID="57515"/>
    <cge:TPSR_Ref TObjectID="38405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-230480" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38405"/>
     <cge:Term_Ref ObjectID="57515"/>
    <cge:TPSR_Ref TObjectID="38405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-230484" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38405"/>
     <cge:Term_Ref ObjectID="57515"/>
    <cge:TPSR_Ref TObjectID="38405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-230481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38405"/>
     <cge:Term_Ref ObjectID="57515"/>
    <cge:TPSR_Ref TObjectID="38405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-230450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1334.000000 -489.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38401"/>
     <cge:Term_Ref ObjectID="57510"/>
    <cge:TPSR_Ref TObjectID="38401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-230451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1334.000000 -489.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38401"/>
     <cge:Term_Ref ObjectID="57510"/>
    <cge:TPSR_Ref TObjectID="38401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-230476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1779.000000 -489.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38402"/>
     <cge:Term_Ref ObjectID="57514"/>
    <cge:TPSR_Ref TObjectID="38402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-230477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1779.000000 -489.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38402"/>
     <cge:Term_Ref ObjectID="57514"/>
    <cge:TPSR_Ref TObjectID="38402"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="664" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="664" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="857" y="-141"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="857" y="-141"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1074" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1074" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1439" y="-144"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1439" y="-144"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1555" y="-144"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1555" y="-144"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1735" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1735" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1921" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1921" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1168" y="-474"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1168" y="-474"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1610" y="-494"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1610" y="-494"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1321" y="-783"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1321" y="-783"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="26" qtmmishow="hidden" width="78" x="31" y="-616"/>
    </a>
   <metadata/><rect fill="white" height="26" opacity="0" stroke="white" transform="" width="78" x="31" y="-616"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
   <g href="35kV撇槽河变10kV备用一072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="664" y="-143"/></g>
   <g href="35kV撇槽河变10kVK210-K225线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="857" y="-141"/></g>
   <g href="35kV撇槽河变10kV1号电容器组074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1074" y="-143"/></g>
   <g href="35kV撇槽河变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1439" y="-144"/></g>
   <g href="35kV撇槽河变10kV2号电容器组075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1555" y="-144"/></g>
   <g href="35kV撇槽河变10kV备用二076间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1735" y="-143"/></g>
   <g href="35kV撇槽河变10kVK210-K195线077间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1921" y="-143"/></g>
   <g href="35kV撇槽河变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1168" y="-474"/></g>
   <g href="35kV撇槽河变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1610" y="-494"/></g>
   <g href="35kV撇槽河变35kV撇槽河T接线371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1321" y="-783"/></g>
   <g href="35kV撇槽河变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="26" qtmmishow="hidden" width="78" x="31" y="-616"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-230356">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -282.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38353" ObjectName="SW-CX_PCH.CX_PCH_001BK"/>
     <cge:Meas_Ref ObjectId="230356"/>
    <cge:TPSR_Ref TObjectID="38353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 -754.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38346" ObjectName="SW-CX_PCH.CX_PCH_371BK"/>
     <cge:Meas_Ref ObjectId="230335"/>
    <cge:TPSR_Ref TObjectID="38346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230352">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -612.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38350" ObjectName="SW-CX_PCH.CX_PCH_301BK"/>
     <cge:Meas_Ref ObjectId="230352"/>
    <cge:TPSR_Ref TObjectID="38350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230366">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1551.000000 -281.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38359" ObjectName="SW-CX_PCH.CX_PCH_002BK"/>
     <cge:Meas_Ref ObjectId="230366"/>
    <cge:TPSR_Ref TObjectID="38359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230362">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1551.000000 -610.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38356" ObjectName="SW-CX_PCH.CX_PCH_302BK"/>
     <cge:Meas_Ref ObjectId="230362"/>
    <cge:TPSR_Ref TObjectID="38356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230372">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 -114.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38362" ObjectName="SW-CX_PCH.CX_PCH_072BK"/>
     <cge:Meas_Ref ObjectId="230372"/>
    <cge:TPSR_Ref TObjectID="38362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230377">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 839.000000 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38366" ObjectName="SW-CX_PCH.CX_PCH_073BK"/>
     <cge:Meas_Ref ObjectId="230377"/>
    <cge:TPSR_Ref TObjectID="38366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 -114.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38374" ObjectName="SW-CX_PCH.CX_PCH_074BK"/>
     <cge:Meas_Ref ObjectId="230387"/>
    <cge:TPSR_Ref TObjectID="38374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230400">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.000000 -115.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38386" ObjectName="SW-CX_PCH.CX_PCH_012BK"/>
     <cge:Meas_Ref ObjectId="230400"/>
    <cge:TPSR_Ref TObjectID="38386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230394">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.000000 -115.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38380" ObjectName="SW-CX_PCH.CX_PCH_075BK"/>
     <cge:Meas_Ref ObjectId="230394"/>
    <cge:TPSR_Ref TObjectID="38380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231051">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1717.000000 -114.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38408" ObjectName="SW-CX_PCH.CX_PCH_076BK"/>
     <cge:Meas_Ref ObjectId="231051"/>
    <cge:TPSR_Ref TObjectID="38408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230382">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1903.000000 -114.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38370" ObjectName="SW-CX_PCH.CX_PCH_077BK"/>
     <cge:Meas_Ref ObjectId="230382"/>
    <cge:TPSR_Ref TObjectID="38370"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 210.000000)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1509.000000 209.000000)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_32a1550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-495 1108,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2226810@0" ObjectIDZND0="38401@1" Pin0InfoVect0LinkObjId="g_2832d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2226810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-495 1108,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32580e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-317 1108,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38353@1" ObjectIDZND0="38355@1" Pin0InfoVect0LinkObjId="SW-230357_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-317 1108,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32585d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-280 1108,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38354@1" ObjectIDZND0="38353@0" Pin0InfoVect0LinkObjId="SW-230356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230357_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-280 1108,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2832d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1130,-361 1108,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_27b71b0@0" ObjectIDZND0="38401@x" ObjectIDZND1="38355@x" Pin0InfoVect0LinkObjId="g_32a1550_0" Pin0InfoVect1LinkObjId="SW-230357_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b71b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1130,-361 1108,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2164ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-380 1108,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="38401@0" ObjectIDZND0="g_27b71b0@0" ObjectIDZND1="38355@x" Pin0InfoVect0LinkObjId="g_27b71b0_0" Pin0InfoVect1LinkObjId="SW-230357_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a1550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-380 1108,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3259940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-361 1108,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_27b71b0@0" ObjectIDND1="38401@x" ObjectIDZND0="38355@0" Pin0InfoVect0LinkObjId="SW-230357_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27b71b0_0" Pin1InfoVect1LinkObjId="g_32a1550_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-361 1108,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3290fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-366 638,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_216b840@0" ObjectIDND1="g_2b88710@0" ObjectIDZND0="g_27c9810@0" Pin0InfoVect0LinkObjId="g_27c9810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_216b840_0" Pin1InfoVect1LinkObjId="g_2b88710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-366 638,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab6340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-789 1312,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38346@1" ObjectIDZND0="38348@1" Pin0InfoVect0LinkObjId="SW-230336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230335_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-789 1312,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21f5610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-752 1312,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38347@1" ObjectIDZND0="38346@0" Pin0InfoVect0LinkObjId="SW-230335_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-752 1312,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2169130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1346,-890 1346,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38349@1" ObjectIDZND0="g_2169a30@0" Pin0InfoVect0LinkObjId="g_2169a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230337_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1346,-890 1346,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ed2d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-817 1312,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="38348@0" ObjectIDZND0="38349@x" ObjectIDZND1="g_1fd0c60@0" ObjectIDZND2="g_1f96f80@0" Pin0InfoVect0LinkObjId="SW-230337_0" Pin0InfoVect1LinkObjId="g_1fd0c60_0" Pin0InfoVect2LinkObjId="g_1f96f80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-817 1312,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_326a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-842 1312,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" ObjectIDND0="38348@x" ObjectIDND1="38349@x" ObjectIDND2="g_1fd0c60@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230336_0" Pin1InfoVect1LinkObjId="SW-230337_0" Pin1InfoVect2LinkObjId="g_1fd0c60_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-842 1312,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b90370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1346,-854 1346,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38349@0" ObjectIDZND0="g_1fd0c60@0" ObjectIDZND1="38348@x" ObjectIDZND2="g_1f96f80@0" Pin0InfoVect0LinkObjId="g_1fd0c60_0" Pin0InfoVect1LinkObjId="SW-230336_0" Pin0InfoVect2LinkObjId="g_1f96f80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1346,-854 1346,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a61a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-842 1346,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1fd0c60@0" ObjectIDZND0="38349@x" ObjectIDZND1="38348@x" ObjectIDZND2="g_1f96f80@0" Pin0InfoVect0LinkObjId="SW-230337_0" Pin0InfoVect1LinkObjId="SW-230336_0" Pin0InfoVect2LinkObjId="g_1f96f80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd0c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-842 1346,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3259ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1346,-842 1312,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="38349@x" ObjectIDND1="g_1fd0c60@0" ObjectIDZND0="38348@x" ObjectIDZND1="g_1f96f80@0" Pin0InfoVect0LinkObjId="SW-230336_0" Pin0InfoVect1LinkObjId="g_1f96f80_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-230337_0" Pin1InfoVect1LinkObjId="g_1fd0c60_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1346,-842 1312,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31fd6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-842 1284,-842 1284,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="38348@x" ObjectIDND1="38349@x" ObjectIDND2="g_1fd0c60@0" ObjectIDZND0="g_1f96f80@0" Pin0InfoVect0LinkObjId="g_1f96f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230336_0" Pin1InfoVect1LinkObjId="SW-230337_0" Pin1InfoVect2LinkObjId="g_1fd0c60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-842 1284,-842 1284,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_325c9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-813 1559,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_325b610@1" ObjectIDZND0="38394@0" Pin0InfoVect0LinkObjId="SW-230405_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_325b610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-813 1559,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3279aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1107,-819 1107,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3272ac0@1" ObjectIDZND0="38392@0" Pin0InfoVect0LinkObjId="SW-230404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3272ac0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1107,-819 1107,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b80ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1067,-867 1107,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="g_2169e70@0" ObjectIDZND0="g_3279210@0" ObjectIDZND1="g_3272ac0@0" Pin0InfoVect0LinkObjId="g_3279210_0" Pin0InfoVect1LinkObjId="g_3272ac0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2169e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1067,-867 1107,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ed3030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1107,-893 1107,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3279210@0" ObjectIDZND0="g_2169e70@0" ObjectIDZND1="g_3272ac0@0" Pin0InfoVect0LinkObjId="g_2169e70_0" Pin0InfoVect1LinkObjId="g_3272ac0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3279210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1107,-893 1107,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28553f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1107,-867 1107,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2169e70@0" ObjectIDND1="g_3279210@0" ObjectIDZND0="g_3272ac0@0" Pin0InfoVect0LinkObjId="g_3272ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2169e70_0" Pin1InfoVect1LinkObjId="g_3279210_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1107,-867 1107,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27baf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1060,-556 1060,-571 1108,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_225ebf0@0" ObjectIDZND0="g_2226810@0" ObjectIDZND1="38352@x" Pin0InfoVect0LinkObjId="g_2226810_0" Pin0InfoVect1LinkObjId="SW-230353_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_225ebf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1060,-556 1060,-571 1108,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b86f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-592 1108,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="38352@0" ObjectIDZND0="g_225ebf0@0" ObjectIDZND1="g_2226810@0" Pin0InfoVect0LinkObjId="g_225ebf0_0" Pin0InfoVect1LinkObjId="g_2226810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230353_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-592 1108,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27fc680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-571 1108,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_225ebf0@0" ObjectIDND1="38352@x" ObjectIDZND0="g_2226810@1" Pin0InfoVect0LinkObjId="g_2226810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_225ebf0_0" Pin1InfoVect1LinkObjId="SW-230353_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-571 1108,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b91a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-647 1108,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38350@1" ObjectIDZND0="38351@1" Pin0InfoVect0LinkObjId="SW-230353_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230352_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-647 1108,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2811670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-610 1108,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38352@1" ObjectIDZND0="38350@0" Pin0InfoVect0LinkObjId="SW-230352_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230353_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-610 1108,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2324f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-491 1560,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_28338e0@0" ObjectIDZND0="38402@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28338e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-491 1560,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3278c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-316 1560,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38359@1" ObjectIDZND0="38361@1" Pin0InfoVect0LinkObjId="SW-230367_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230366_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-316 1560,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3272d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-279 1560,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38360@1" ObjectIDZND0="38359@0" Pin0InfoVect0LinkObjId="SW-230366_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230367_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-279 1560,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a97700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1583,-360 1560,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_3272fd0@0" ObjectIDZND0="38361@x" ObjectIDZND1="38402@x" Pin0InfoVect0LinkObjId="SW-230367_0" Pin0InfoVect1LinkObjId="g_2324f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3272fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1583,-360 1560,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3270a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-379 1560,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="38402@0" ObjectIDZND0="g_3272fd0@0" ObjectIDZND1="38361@x" Pin0InfoVect0LinkObjId="g_3272fd0_0" Pin0InfoVect1LinkObjId="SW-230367_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2324f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-379 1560,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_326c8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-360 1560,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_3272fd0@0" ObjectIDND1="38402@x" ObjectIDZND0="38361@0" Pin0InfoVect0LinkObjId="SW-230367_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3272fd0_0" Pin1InfoVect1LinkObjId="g_2324f30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-360 1560,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_216f390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,-552 1513,-567 1560,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_216dad0@0" ObjectIDZND0="38358@x" ObjectIDZND1="g_28338e0@0" Pin0InfoVect0LinkObjId="SW-230363_0" Pin0InfoVect1LinkObjId="g_28338e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_216dad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1513,-552 1513,-567 1560,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2171940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-591 1560,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="38358@0" ObjectIDZND0="g_216dad0@0" ObjectIDZND1="g_28338e0@0" Pin0InfoVect0LinkObjId="g_216dad0_0" Pin0InfoVect1LinkObjId="g_28338e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-591 1560,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2171ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-567 1560,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_216dad0@0" ObjectIDND1="38358@x" ObjectIDZND0="g_28338e0@1" Pin0InfoVect0LinkObjId="g_28338e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_216dad0_0" Pin1InfoVect1LinkObjId="SW-230363_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-567 1560,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27ea4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-645 1560,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38356@1" ObjectIDZND0="38357@1" Pin0InfoVect0LinkObjId="SW-230363_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-645 1560,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27e3960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-608 1560,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38358@1" ObjectIDZND0="38356@0" Pin0InfoVect0LinkObjId="SW-230362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230363_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-608 1560,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b80aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-321 638,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b88710@1" ObjectIDZND0="38398@0" Pin0InfoVect0LinkObjId="SW-230407_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b88710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-321 638,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b6530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-149 655,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38362@1" ObjectIDZND0="38363@1" Pin0InfoVect0LinkObjId="SW-230373_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230372_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-149 655,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b6790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-112 655,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38364@1" ObjectIDZND0="38362@0" Pin0InfoVect0LinkObjId="SW-230372_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230373_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-112 655,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2220630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-26 689,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27fbef0@0" ObjectIDZND0="38365@0" Pin0InfoVect0LinkObjId="SW-230374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27fbef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="689,-26 689,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_228e3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,2 655,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_21b69f0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b69f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="655,2 655,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a95a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-147 848,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38366@1" ObjectIDZND0="38367@1" Pin0InfoVect0LinkObjId="SW-230378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="848,-147 848,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a95ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-110 848,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38368@1" ObjectIDZND0="38366@0" Pin0InfoVect0LinkObjId="SW-230377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="848,-110 848,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2804170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="882,-24 882,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b8b7b0@0" ObjectIDZND0="38369@0" Pin0InfoVect0LinkObjId="SW-230379_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b8b7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="882,-24 882,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2805100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,4 848,48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2a95f00@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a95f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="848,4 848,48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c47f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="814,-68 848,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_28043d0@0" ObjectIDZND0="38368@x" ObjectIDZND1="g_2a95f00@0" ObjectIDZND2="38369@x" Pin0InfoVect0LinkObjId="SW-230378_0" Pin0InfoVect1LinkObjId="g_2a95f00_0" Pin0InfoVect2LinkObjId="SW-230379_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28043d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="814,-68 848,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c49e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-93 848,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38368@0" ObjectIDZND0="g_28043d0@0" ObjectIDZND1="g_2a95f00@0" ObjectIDZND2="38369@x" Pin0InfoVect0LinkObjId="g_28043d0_0" Pin0InfoVect1LinkObjId="g_2a95f00_0" Pin0InfoVect2LinkObjId="SW-230379_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="848,-93 848,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c4bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-68 848,-49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_28043d0@0" ObjectIDND1="38368@x" ObjectIDND2="38369@x" ObjectIDZND0="g_2a95f00@1" Pin0InfoVect0LinkObjId="g_2a95f00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28043d0_0" Pin1InfoVect1LinkObjId="SW-230378_0" Pin1InfoVect2LinkObjId="SW-230379_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="848,-68 848,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c4de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="882,-68 846,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38369@1" ObjectIDZND0="g_28043d0@0" ObjectIDZND1="38368@x" ObjectIDZND2="g_2a95f00@0" Pin0InfoVect0LinkObjId="g_28043d0_0" Pin0InfoVect1LinkObjId="SW-230378_0" Pin0InfoVect2LinkObjId="g_2a95f00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230379_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="882,-68 846,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_228f820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-149 1065,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38374@1" ObjectIDZND0="38375@1" Pin0InfoVect0LinkObjId="SW-230388_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,-149 1065,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_228fa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-112 1065,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38376@1" ObjectIDZND0="38374@0" Pin0InfoVect0LinkObjId="SW-230387_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,-112 1065,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2229220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1099,-26 1099,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22287d0@0" ObjectIDZND0="38378@0" Pin0InfoVect0LinkObjId="SW-230390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22287d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1099,-26 1099,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d3ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1031,-70 1065,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2229480@0" ObjectIDZND0="38376@x" ObjectIDZND1="g_228fce0@0" ObjectIDZND2="38378@x" Pin0InfoVect0LinkObjId="SW-230388_0" Pin0InfoVect1LinkObjId="g_228fce0_0" Pin0InfoVect2LinkObjId="SW-230390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2229480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1031,-70 1065,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d4100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-95 1065,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38376@0" ObjectIDZND0="g_2229480@0" ObjectIDZND1="g_228fce0@0" ObjectIDZND2="38378@x" Pin0InfoVect0LinkObjId="g_2229480_0" Pin0InfoVect1LinkObjId="g_228fce0_0" Pin0InfoVect2LinkObjId="SW-230390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1065,-95 1065,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d4360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-70 1065,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2229480@0" ObjectIDND1="38376@x" ObjectIDND2="38378@x" ObjectIDZND0="g_228fce0@1" Pin0InfoVect0LinkObjId="g_228fce0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2229480_0" Pin1InfoVect1LinkObjId="SW-230388_0" Pin1InfoVect2LinkObjId="SW-230390_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,-70 1065,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d45c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1099,-70 1063,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38378@1" ObjectIDZND0="g_2229480@0" ObjectIDZND1="38376@x" ObjectIDZND2="g_228fce0@0" Pin0InfoVect0LinkObjId="g_2229480_0" Pin0InfoVect1LinkObjId="SW-230388_0" Pin0InfoVect2LinkObjId="g_228fce0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1099,-70 1063,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_231b230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-150 1430,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38386@1" ObjectIDZND0="38387@1" Pin0InfoVect0LinkObjId="SW-230401_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-150 1430,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_231b490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-113 1430,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38388@1" ObjectIDZND0="38386@0" Pin0InfoVect0LinkObjId="SW-230400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-113 1430,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_233ff70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-70 655,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2220890@0" ObjectIDZND0="38364@x" ObjectIDZND1="g_21b69f0@0" ObjectIDZND2="38365@x" Pin0InfoVect0LinkObjId="SW-230373_0" Pin0InfoVect1LinkObjId="g_21b69f0_0" Pin0InfoVect2LinkObjId="SW-230374_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2220890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="621,-70 655,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_228b1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-95 655,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38364@0" ObjectIDZND0="g_2220890@0" ObjectIDZND1="g_21b69f0@0" ObjectIDZND2="38365@x" Pin0InfoVect0LinkObjId="g_2220890_0" Pin0InfoVect1LinkObjId="g_21b69f0_0" Pin0InfoVect2LinkObjId="SW-230374_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="655,-95 655,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_228b430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-70 655,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2220890@0" ObjectIDND1="38364@x" ObjectIDND2="38365@x" ObjectIDZND0="g_21b69f0@1" Pin0InfoVect0LinkObjId="g_21b69f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2220890_0" Pin1InfoVect1LinkObjId="SW-230373_0" Pin1InfoVect2LinkObjId="SW-230374_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-70 655,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_228b690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-70 654,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38365@1" ObjectIDZND0="g_2220890@0" ObjectIDZND1="38364@x" ObjectIDZND2="g_21b69f0@0" Pin0InfoVect0LinkObjId="g_2220890_0" Pin0InfoVect1LinkObjId="SW-230373_0" Pin0InfoVect2LinkObjId="g_21b69f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="689,-70 654,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_228c5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="455,-26 489,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_228b8f0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_21bbcb0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_21bbcb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_228b8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="455,-26 489,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21bcc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-2 489,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_228b8f0@0" ObjectIDZND1="g_21bbcb0@0" Pin0InfoVect0LinkObjId="g_228b8f0_0" Pin0InfoVect1LinkObjId="g_21bbcb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="489,-2 489,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21bcee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-26 489,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_228b8f0@0" ObjectIDND1="0@x" ObjectIDZND0="g_21bbcb0@1" Pin0InfoVect0LinkObjId="g_21bbcb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_228b8f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-26 489,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22643f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,2 1065,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_228fce0@0" ObjectIDZND0="38377@1" Pin0InfoVect0LinkObjId="SW-230389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_228fce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,2 1065,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2264650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,250 1065,258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_22639d0@0" Pin0InfoVect0LinkObjId="g_22639d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,250 1065,258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d0c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1107,107 1107,99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22fe260@0" ObjectIDZND0="38379@0" Pin0InfoVect0LinkObjId="SW-230391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fe260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1107,107 1107,99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d0e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1107,63 1064,63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="38379@1" ObjectIDZND0="38377@x" ObjectIDZND1="g_2264c60@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-230389_0" Pin0InfoVect1LinkObjId="g_2264c60_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1107,63 1064,63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d1b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,63 1065,48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="38379@x" ObjectIDND1="g_2264c60@0" ObjectIDND2="0@x" ObjectIDZND0="38377@0" Pin0InfoVect0LinkObjId="SW-230389_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230391_0" Pin1InfoVect1LinkObjId="g_2264c60_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,63 1065,48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d1dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,63 1043,63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" ObjectIDND0="38379@x" ObjectIDND1="38377@x" ObjectIDND2="0@x" ObjectIDZND0="g_2264c60@0" Pin0InfoVect0LinkObjId="g_2264c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230391_0" Pin1InfoVect1LinkObjId="SW-230389_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,63 1043,63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2232900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-150 1546,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38380@1" ObjectIDZND0="38381@1" Pin0InfoVect0LinkObjId="SW-230395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230394_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1546,-150 1546,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2232b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-113 1546,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38382@1" ObjectIDZND0="38380@0" Pin0InfoVect0LinkObjId="SW-230394_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230395_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1546,-113 1546,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22deae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,-27 1580,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22de090@0" ObjectIDZND0="38384@0" Pin0InfoVect0LinkObjId="SW-230397_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22de090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,-27 1580,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22dfa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-71 1546,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_22ded40@0" ObjectIDZND0="38382@x" ObjectIDZND1="38384@x" ObjectIDZND2="g_2232dc0@0" Pin0InfoVect0LinkObjId="SW-230395_0" Pin0InfoVect1LinkObjId="SW-230397_0" Pin0InfoVect2LinkObjId="g_2232dc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ded40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1512,-71 1546,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22dfcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-96 1546,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38382@0" ObjectIDZND0="g_22ded40@0" ObjectIDZND1="38384@x" ObjectIDZND2="g_2232dc0@0" Pin0InfoVect0LinkObjId="g_22ded40_0" Pin0InfoVect1LinkObjId="SW-230397_0" Pin0InfoVect2LinkObjId="g_2232dc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1546,-96 1546,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22dff30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-71 1546,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38382@x" ObjectIDND1="g_22ded40@0" ObjectIDND2="38384@x" ObjectIDZND0="g_2232dc0@1" Pin0InfoVect0LinkObjId="g_2232dc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230395_0" Pin1InfoVect1LinkObjId="g_22ded40_0" Pin1InfoVect2LinkObjId="SW-230397_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1546,-71 1546,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e0190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,-71 1544,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="38384@1" ObjectIDZND0="38382@x" ObjectIDZND1="g_22ded40@0" ObjectIDZND2="g_2232dc0@0" Pin0InfoVect0LinkObjId="SW-230395_0" Pin0InfoVect1LinkObjId="g_22ded40_0" Pin0InfoVect2LinkObjId="g_2232dc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230397_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1580,-71 1544,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2252830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,1 1546,11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2232dc0@0" ObjectIDZND0="38383@1" Pin0InfoVect0LinkObjId="SW-230396_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2232dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1546,1 1546,11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2252a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,249 1546,257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2251de0@0" Pin0InfoVect0LinkObjId="g_2251de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1546,249 1546,257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a0730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1588,106 1588,98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_229fce0@0" ObjectIDZND0="38385@0" Pin0InfoVect0LinkObjId="SW-230398_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_229fce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1588,106 1588,98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a0990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1588,62 1545,62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38385@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2252e40@0" ObjectIDZND2="38383@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2252e40_0" Pin0InfoVect2LinkObjId="SW-230396_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230398_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1588,62 1545,62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a0bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,78 1546,62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="38385@x" ObjectIDZND1="g_2252e40@0" ObjectIDZND2="38383@x" Pin0InfoVect0LinkObjId="SW-230398_0" Pin0InfoVect1LinkObjId="g_2252e40_0" Pin0InfoVect2LinkObjId="SW-230396_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1546,78 1546,62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a0e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,62 1546,47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="38385@x" ObjectIDND2="g_2252e40@0" ObjectIDZND0="38383@0" Pin0InfoVect0LinkObjId="SW-230396_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-230398_0" Pin1InfoVect2LinkObjId="g_2252e40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1546,62 1546,47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a10b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,62 1524,62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="38385@x" ObjectIDND2="38383@x" ObjectIDZND0="g_2252e40@0" Pin0InfoVect0LinkObjId="g_2252e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-230398_0" Pin1InfoVect2LinkObjId="SW-230396_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1546,62 1524,62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22b1d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-149 1726,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38408@1" ObjectIDZND0="38449@1" Pin0InfoVect0LinkObjId="SW-231052_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231051_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-149 1726,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22b1f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-112 1726,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38450@1" ObjectIDZND0="38408@0" Pin0InfoVect0LinkObjId="SW-231051_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231052_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-112 1726,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22bd9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-26 1760,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22bcf70@0" ObjectIDZND0="38451@0" Pin0InfoVect0LinkObjId="SW-231053_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22bcf70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-26 1760,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22006c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,2 1726,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_22b21f0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b21f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1726,2 1726,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2201150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1692,-70 1726,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_22bdc20@0" ObjectIDZND0="38450@1" ObjectIDZND1="38451@x" ObjectIDZND2="g_22b21f0@0" Pin0InfoVect0LinkObjId="SW-231052_1" Pin0InfoVect1LinkObjId="SW-231053_0" Pin0InfoVect2LinkObjId="g_22b21f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22bdc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1692,-70 1726,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2201340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-95 1726,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38450@0" ObjectIDZND0="g_22bdc20@0" ObjectIDZND1="38451@x" ObjectIDZND2="g_22b21f0@0" Pin0InfoVect0LinkObjId="g_22bdc20_0" Pin0InfoVect1LinkObjId="SW-231053_0" Pin0InfoVect2LinkObjId="g_22b21f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231052_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-95 1726,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2201530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-70 1726,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38450@1" ObjectIDND1="g_22bdc20@0" ObjectIDND2="38451@x" ObjectIDZND0="g_22b21f0@1" Pin0InfoVect0LinkObjId="g_22b21f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-231052_1" Pin1InfoVect1LinkObjId="g_22bdc20_0" Pin1InfoVect2LinkObjId="SW-231053_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-70 1726,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2201760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-70 1724,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="38451@1" ObjectIDZND0="38450@1" ObjectIDZND1="g_22bdc20@0" ObjectIDZND2="g_22b21f0@0" Pin0InfoVect0LinkObjId="SW-231052_1" Pin0InfoVect1LinkObjId="g_22bdc20_0" Pin0InfoVect2LinkObjId="g_22b21f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231053_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-70 1724,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cfac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1912,-149 1912,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38370@1" ObjectIDZND0="38371@1" Pin0InfoVect0LinkObjId="SW-230383_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1912,-149 1912,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cfd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1912,-112 1912,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38372@1" ObjectIDZND0="38370@0" Pin0InfoVect0LinkObjId="SW-230382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230383_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1912,-112 1912,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a4200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,-26 1946,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22a37b0@0" ObjectIDZND0="38373@0" Pin0InfoVect0LinkObjId="SW-230384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22a37b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1946,-26 1946,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a5190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1912,2 1912,46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_22cff80@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22cff80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1912,2 1912,46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a53f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1878,-70 1912,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_22a4460@0" ObjectIDZND0="38372@x" ObjectIDZND1="38373@x" ObjectIDZND2="g_22cff80@0" Pin0InfoVect0LinkObjId="SW-230383_0" Pin0InfoVect1LinkObjId="SW-230384_0" Pin0InfoVect2LinkObjId="g_22cff80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22a4460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1878,-70 1912,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a5650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1912,-95 1912,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38372@0" ObjectIDZND0="g_22a4460@0" ObjectIDZND1="38373@x" ObjectIDZND2="g_22cff80@0" Pin0InfoVect0LinkObjId="g_22a4460_0" Pin0InfoVect1LinkObjId="SW-230384_0" Pin0InfoVect2LinkObjId="g_22cff80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1912,-95 1912,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a58b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1912,-70 1912,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38372@x" ObjectIDND1="g_22a4460@0" ObjectIDND2="38373@x" ObjectIDZND0="g_22cff80@1" Pin0InfoVect0LinkObjId="g_22cff80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230383_0" Pin1InfoVect1LinkObjId="g_22a4460_0" Pin1InfoVect2LinkObjId="SW-230384_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1912,-70 1912,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a5b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,-70 1910,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="38373@1" ObjectIDZND0="38372@x" ObjectIDZND1="g_22a4460@0" ObjectIDZND2="g_22cff80@0" Pin0InfoVect0LinkObjId="SW-230383_0" Pin0InfoVect1LinkObjId="g_22a4460_0" Pin0InfoVect2LinkObjId="g_22cff80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1946,-70 1910,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e23e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-372 1929,-372 1929,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_21e2b00@0" ObjectIDND1="g_21ef8b0@0" ObjectIDZND0="g_21e1630@0" Pin0InfoVect0LinkObjId="g_21e1630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21e2b00_0" Pin1InfoVect1LinkObjId="g_21ef8b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-372 1929,-372 1929,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e2640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-433 1975,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_21ef8b0@0" ObjectIDZND0="g_21e1630@0" ObjectIDZND1="g_21e2b00@0" Pin0InfoVect0LinkObjId="g_21e1630_0" Pin0InfoVect1LinkObjId="g_21e2b00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ef8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-433 1975,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e28a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-238 1975,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38399@0" ObjectIDZND0="38407@0" Pin0InfoVect0LinkObjId="g_237d910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-238 1975,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e3380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-372 1975,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_21e1630@0" ObjectIDND1="g_21ef8b0@0" ObjectIDZND0="g_21e2b00@0" Pin0InfoVect0LinkObjId="g_21e2b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21e1630_0" Pin1InfoVect1LinkObjId="g_21ef8b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-372 1975,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_237aef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-323 1975,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_21e2b00@1" ObjectIDZND0="38400@0" Pin0InfoVect0LinkObjId="SW-230408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21e2b00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-323 1975,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_237b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1107,-736 1107,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38391@0" ObjectIDZND0="38405@0" Pin0InfoVect0LinkObjId="g_237bc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1107,-736 1107,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_237bc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-675 1108,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38351@0" ObjectIDZND0="38405@0" Pin0InfoVect0LinkObjId="g_237b9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230353_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-675 1108,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_237be80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-735 1312,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38347@0" ObjectIDZND0="38405@0" Pin0InfoVect0LinkObjId="g_237b9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-735 1312,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_237c0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-736 1559,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38393@0" ObjectIDZND0="38405@0" Pin0InfoVect0LinkObjId="g_237b9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-736 1559,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_237c340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-673 1560,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38357@0" ObjectIDZND0="38405@0" Pin0InfoVect0LinkObjId="g_237b9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-673 1560,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_237d6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-263 1108,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38354@0" ObjectIDZND0="38406@0" Pin0InfoVect0LinkObjId="g_2266e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230357_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-263 1108,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_237d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,-262 1560,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38360@0" ObjectIDZND0="38407@0" Pin0InfoVect0LinkObjId="g_21e28a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,-262 1560,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_237db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-178 1546,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38381@0" ObjectIDZND0="38407@0" Pin0InfoVect0LinkObjId="g_21e28a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1546,-178 1546,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_237ddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-178 1430,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38387@0" ObjectIDZND0="38407@0" Pin0InfoVect0LinkObjId="g_21e28a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-178 1430,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2266e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1275,-171 1275,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38389@0" ObjectIDZND0="38406@0" Pin0InfoVect0LinkObjId="g_237d6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1275,-171 1275,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22670a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1275,-103 1275,-79 1430,-79 1430,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38390@0" ObjectIDZND0="38388@0" Pin0InfoVect0LinkObjId="SW-230401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1275,-103 1275,-79 1430,-79 1430,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2267310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-177 1065,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38375@0" ObjectIDZND0="38406@0" Pin0InfoVect0LinkObjId="g_237d6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,-177 1065,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2267570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-175 848,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38367@0" ObjectIDZND0="38406@0" Pin0InfoVect0LinkObjId="g_237d6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="848,-175 848,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22677d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-177 655,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38363@0" ObjectIDZND0="38406@0" Pin0InfoVect0LinkObjId="g_237d6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-177 655,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2267a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-236 638,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38397@0" ObjectIDZND0="38406@0" Pin0InfoVect0LinkObjId="g_237d6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230407_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-236 638,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22693d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-177 1726,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38449@0" ObjectIDZND0="38407@0" Pin0InfoVect0LinkObjId="g_21e28a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231052_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-177 1726,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2269c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1912,-177 1912,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38371@0" ObjectIDZND0="38407@0" Pin0InfoVect0LinkObjId="g_21e28a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1912,-177 1912,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21c0cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1107,-788 1107,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38392@1" ObjectIDZND0="38391@1" Pin0InfoVect0LinkObjId="SW-230404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1107,-788 1107,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2276e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1558,-787 1558,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38394@1" ObjectIDZND0="38393@1" Pin0InfoVect0LinkObjId="SW-230405_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1558,-787 1558,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_217edd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="637,-286 637,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38398@1" ObjectIDZND0="38397@1" Pin0InfoVect0LinkObjId="SW-230407_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230407_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="637,-286 637,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22b7080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1976,-289 1976,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38400@1" ObjectIDZND0="38399@1" Pin0InfoVect0LinkObjId="SW-230408_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1976,-289 1976,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_222bba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1275,-154 1275,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38389@1" ObjectIDZND0="38390@1" Pin0InfoVect0LinkObjId="SW-230402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230402_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1275,-154 1275,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_222ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,63 1065,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="38379@x" ObjectIDND1="38377@x" ObjectIDND2="g_2264c60@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230391_0" Pin1InfoVect1LinkObjId="SW-230389_0" Pin1InfoVect2LinkObjId="g_2264c60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,63 1065,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2154cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-135 489,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38395@1" ObjectIDZND0="38396@1" Pin0InfoVect0LinkObjId="SW-230406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-135 489,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2154f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-152 489,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38395@0" ObjectIDZND0="38406@0" Pin0InfoVect0LinkObjId="g_237d6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-152 489,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f94f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-92 489,-77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38396@0" ObjectIDZND0="g_21bbcb0@0" Pin0InfoVect0LinkObjId="g_21bbcb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-92 489,-77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2271b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="592,-375 592,-370 638,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="g_216b840@0" ObjectIDZND0="g_27c9810@0" ObjectIDZND1="g_2b88710@0" Pin0InfoVect0LinkObjId="g_27c9810_0" Pin0InfoVect1LinkObjId="g_2b88710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_216b840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="592,-375 592,-370 638,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2271d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-370 638,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_27c9810@0" ObjectIDND1="g_216b840@0" ObjectIDZND0="g_2b88710@0" Pin0InfoVect0LinkObjId="g_2b88710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27c9810_0" Pin1InfoVect1LinkObjId="g_216b840_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-370 638,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22729e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1604,-855 1559,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_2271f40@0" ObjectIDZND0="g_325b610@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_325b610_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2271f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1604,-855 1559,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-844 1559,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_325b610@0" ObjectIDZND0="g_2271f40@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2271f40_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_325b610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-844 1559,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223aac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-855 1559,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2271f40@0" ObjectIDND1="g_325b610@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2271f40_0" Pin1InfoVect1LinkObjId="g_325b610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-855 1559,-868 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-230316" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 292.500000 -972.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38334" ObjectName="DYN-CX_PCH"/>
     <cge:Meas_Ref ObjectId="230316"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f97d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 271.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f9ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.000000 256.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f9cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 317.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f9f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 302.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fa170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 287.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fa4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1138.000000 130.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fa700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 160.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fa940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1132.000000 145.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fac70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 478.000000 -175.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21faed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 492.000000 -205.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fb110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 468.000000 -190.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fdd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 321.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fdef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.000000 291.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fe100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.000000 306.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fe400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1641.000000 318.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fe630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1655.000000 288.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1631.000000 303.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 656.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1657.000000 626.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1633.000000 641.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226dca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 651.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226df00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.000000 621.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226e140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.000000 636.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226e470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 797.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226e6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 767.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226e910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 782.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226f300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2061.000000 266.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226f510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2045.000000 251.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226f720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2055.000000 312.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226f930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2055.000000 297.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2055.000000 282.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fe40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1306.000000 69.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1320.000000 39.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22702b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1296.000000 54.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22705e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 163.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1613.000000 133.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1589.000000 148.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223c0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 769.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223c6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 883.000000 754.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223c910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 815.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223cb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 800.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223cd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 785.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223d430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 475.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223d640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 490.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223d900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1687.000000 473.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223db20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1687.000000 488.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2169a30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 -892.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27fbef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 -8.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b8b7b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.000000 -6.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22287d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1093.000000 -8.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22639d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1059.000000 276.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22fe260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1101.000000 125.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22de090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1574.000000 -9.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2251de0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1540.000000 275.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_229fce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1582.000000 124.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22bcf70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1754.000000 -8.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22a37b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1940.000000 -8.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27f99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b98610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b98610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b98610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b98610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b98610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b98610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b98610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2eced40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -1001.500000) translate(0,16)">撇槽河变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281f6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 379.000000 -235.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b7de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -452.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b7de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -452.000000) translate(0,33)">SZ11-8000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b7de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -452.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b7de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -452.000000) translate(0,69)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b7de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -452.000000) translate(0,87)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3298190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.500000 -732.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a9e4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -611.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28d7eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1058.500000 -978.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28d7eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1058.500000 -978.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28d6ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.500000 -971.000000) translate(0,15)">35kV姚西线及撇槽河T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b7d230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1513.500000 -975.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28227a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.000000 103.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22c3f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.000000 101.000000) translate(0,15)">10kV K210-K225线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21bd140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 100.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2200920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 94.000000) translate(0,15)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226a430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1021.000000 286.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dbc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 229.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1507.000000 283.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1855.000000 95.000000) translate(0,15)">10kV K210-K195线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2044.000000 -228.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 590.000000 -519.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 590.000000 -519.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1914.000000 -519.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1914.000000 -519.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dcbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 -453.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dcbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 -453.000000) translate(0,33)">SZ11-8000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dcbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 -453.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dcbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 -453.000000) translate(0,69)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dcbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 -453.000000) translate(0,87)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b78b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1118.000000 -776.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b7ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1328.000000 -927.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b8120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1321.000000 -783.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b8360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1567.000000 -781.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b85a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1117.000000 -641.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b87e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 -640.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b8a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1168.000000 -474.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b8c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1610.000000 -494.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b8ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1117.000000 -311.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b90e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 -310.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b9320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1989.000000 -277.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b9560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.000000 -275.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b97a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -59.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b99e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -143.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b9c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 857.000000 -141.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b9e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 889.000000 -57.000000) translate(0,12)">07367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ba0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1074.000000 -143.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ba2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1106.000000 -59.000000) translate(0,12)">07430</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ba520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1072.000000 22.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ba760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1114.000000 73.000000) translate(0,12)">07460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222c3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 -146.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222cbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -144.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222cea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -144.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1587.000000 -60.000000) translate(0,12)">07530</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222d320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1553.000000 21.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222d560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1595.000000 72.000000) translate(0,12)">07560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222d7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1556.000000 227.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222d9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -143.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222dc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 -59.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222de60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1921.000000 -143.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222e0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1953.000000 -59.000000) translate(0,12)">07767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f8ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 -128.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_223e560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -66.000000) translate(0,15)">15096461682</text>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_27c9810">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 619.000000 -427.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd0c60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 -837.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3279210">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1088.000000 -888.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ef8b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1956.000000 -429.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="38405" cx="1108" cy="-708" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38405" cx="1312" cy="-708" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38405" cx="1560" cy="-708" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38407" cx="1560" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38407" cx="1546" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38407" cx="1430" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38406" cx="1108" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38406" cx="1065" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38406" cx="848" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38406" cx="655" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38407" cx="1726" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38407" cx="1912" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38405" cx="1107" cy="-708" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38405" cx="1559" cy="-708" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38406" cx="638" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38407" cx="1975" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38406" cx="1275" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38406" cx="489" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_PCH"/>
</svg>