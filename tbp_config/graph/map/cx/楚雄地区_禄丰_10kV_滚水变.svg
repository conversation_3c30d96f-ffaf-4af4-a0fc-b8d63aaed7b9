<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-95" aopId="256" id="thSvg" viewBox="3116 -1198 2126 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape9">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="21" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="51" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="14" x2="22" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="8" x2="18" y1="29" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="18" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="0" y1="22" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="36" x2="28" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="42" x2="32" y1="29" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="32" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="51" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="21" x2="21" y1="13" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="28" x2="28" y1="13" y2="0"/>
   </symbol>
   <symbol id="generator:shape3">
    <polyline fill="none" points="26,-19 26,-18 26,-17 26,-16 25,-15 25,-14 24,-13 24,-13 23,-12 22,-12 21,-11 20,-11 19,-11 18,-10 17,-11 16,-11 15,-11 14,-12 14,-12 13,-13 12,-13 12,-14 11,-15 11,-16 11,-17 10,-18 11,-19 "/>
    <circle cx="25" cy="-20" r="24" stroke-width="0.5"/>
    <polyline fill="none" points="41,-19 42,-20 41,-20 41,-21 41,-22 40,-23 40,-24 39,-25 38,-25 38,-26 37,-26 36,-27 35,-27 34,-27 33,-27 32,-27 31,-26 30,-26 29,-25 28,-25 28,-24 27,-23 27,-22 26,-21 26,-20 26,-20 26,-19 "/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1211" width="2136" x="3111" y="-1203"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-78326">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -903.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17160" ObjectName="SW-LF_GSB.LF_GSB_431BK"/>
     <cge:Meas_Ref ObjectId="78326"/>
    <cge:TPSR_Ref TObjectID="17160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -734.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17156" ObjectName="SW-LF_GSB.LF_GSB_434BK"/>
     <cge:Meas_Ref ObjectId="78322"/>
    <cge:TPSR_Ref TObjectID="17156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -510.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 -738.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17152" ObjectName="SW-LF_GSB.LF_GSB_433BK"/>
     <cge:Meas_Ref ObjectId="78318"/>
    <cge:TPSR_Ref TObjectID="17152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.500000 -744.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17148" ObjectName="SW-LF_GSB.LF_GSB_432BK"/>
     <cge:Meas_Ref ObjectId="78308"/>
    <cge:TPSR_Ref TObjectID="17148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.708955 -339.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4428.947761 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.111940 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78316">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 -737.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17150" ObjectName="SW-LF_GSB.LF_GSB_435BK"/>
     <cge:Meas_Ref ObjectId="78316"/>
    <cge:TPSR_Ref TObjectID="17150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -505.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4778.000000 -292.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5064.947761 -343.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5178.111940 -343.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.947761 -218.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4526.111940 -218.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5048.947761 -220.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5162.111940 -220.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3526.000000 -389.000000)" xlink:href="#capacitor:shape9"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2009de0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4433.000000 -949.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef5380">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4390.500000 -952.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d9f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 -981.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21df130">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -604.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21e1ba0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -557.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21e3ba0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -463.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21e4950">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3582.000000 -438.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ed7e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3817.000000 -608.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f09a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -734.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f4cf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 -716.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f5bd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -704.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f68c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -670.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_220d070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.119403 -307.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_220f030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.119403 -300.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_220fce0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.119403 -234.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2215e80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4342.477612 -320.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2216d80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.477612 -308.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2217a50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4337.477612 -274.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_221e0f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4472.947761 -287.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_221ed90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4469.947761 -244.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_221f3b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4432.947761 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22260c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.111940 -287.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2226d60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4583.111940 -244.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2227380">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.111940 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2231f00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 -653.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2233850">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.111940 -588.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2234280">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.111940 -545.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223d0d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.111940 -485.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223d6c0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4872.000000 -556.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223f790">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4885.000000 -473.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_224fd50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5108.947761 -289.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2317790">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.947761 -246.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2317e90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5068.947761 -248.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_231fba0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5222.111940 -289.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23208e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5219.111940 -246.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2320fe0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5182.111940 -248.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-78330">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -856.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17164" ObjectName="SW-LF_GSB.LF_GSB_4311SW"/>
     <cge:Meas_Ref ObjectId="78330"/>
    <cge:TPSR_Ref TObjectID="17164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -991.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17165" ObjectName="SW-LF_GSB.LF_GSB_4313SW"/>
     <cge:Meas_Ref ObjectId="78331"/>
    <cge:TPSR_Ref TObjectID="17165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78334">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -783.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17168" ObjectName="SW-LF_GSB.LF_GSB_4341SW"/>
     <cge:Meas_Ref ObjectId="78334"/>
    <cge:TPSR_Ref TObjectID="17168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -680.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -604.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78333">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17167" ObjectName="SW-LF_GSB.LF_GSB_4331SW"/>
     <cge:Meas_Ref ObjectId="78333"/>
    <cge:TPSR_Ref TObjectID="17167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 -684.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -786.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17171" ObjectName="SW-LF_GSB.LF_GSB_4381SW"/>
     <cge:Meas_Ref ObjectId="78337"/>
    <cge:TPSR_Ref TObjectID="17171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78336">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17170" ObjectName="SW-LF_GSB.LF_GSB_4371SW"/>
     <cge:Meas_Ref ObjectId="78336"/>
    <cge:TPSR_Ref TObjectID="17170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78332">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4273.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17166" ObjectName="SW-LF_GSB.LF_GSB_4321SW"/>
     <cge:Meas_Ref ObjectId="78332"/>
    <cge:TPSR_Ref TObjectID="17166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -387.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17171" ObjectName="SW-LF_GSB.LF_GSB_4381SW"/>
     <cge:Meas_Ref ObjectId="78337"/>
    <cge:TPSR_Ref TObjectID="17171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4343.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78335">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 -785.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17169" ObjectName="SW-LF_GSB.LF_GSB_4351SW"/>
     <cge:Meas_Ref ObjectId="78335"/>
    <cge:TPSR_Ref TObjectID="17169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -663.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.000000 -407.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4778.000000 -341.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.000000 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5178.000000 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -673.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -673.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.076124 -652.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.076124 -652.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.119403 -171.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.119403 -171.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4992.000000 -320.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4992.000000 -320.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer"/><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3117" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3117" y="-597"/>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_202e8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-841 4341,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="17164@0" Pin0InfoVect0LinkObjId="SW-78330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-841 4341,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21c7d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-897 4341,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17164@1" ObjectIDZND0="17160@0" Pin0InfoVect0LinkObjId="SW-78326_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-897 4341,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d0ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-961 4354,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="17160@x" ObjectIDND1="g_21d9f20@0" ObjectIDND2="17165@x" ObjectIDZND0="g_1ef5380@1" Pin0InfoVect0LinkObjId="g_1ef5380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-78326_0" Pin1InfoVect1LinkObjId="g_21d9f20_0" Pin1InfoVect2LinkObjId="SW-78331_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-961 4354,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d7e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-961 4397,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1ef5380@0" ObjectIDZND0="g_2009de0@0" Pin0InfoVect0LinkObjId="g_2009de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ef5380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-961 4397,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d8010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-938 4341,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="17160@1" ObjectIDZND0="g_1ef5380@0" ObjectIDZND1="g_21d9f20@0" ObjectIDZND2="17165@x" Pin0InfoVect0LinkObjId="g_1ef5380_0" Pin0InfoVect1LinkObjId="g_21d9f20_0" Pin0InfoVect2LinkObjId="SW-78331_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78326_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-938 4341,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d8200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-1032 4341,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="17165@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78331_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-1032 4341,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d83f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-979 4307,-979 4307,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="17160@x" ObjectIDND1="g_1ef5380@0" ObjectIDND2="17165@x" ObjectIDZND0="g_21d9f20@0" Pin0InfoVect0LinkObjId="g_21d9f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-78326_0" Pin1InfoVect1LinkObjId="g_1ef5380_0" Pin1InfoVect2LinkObjId="SW-78331_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-979 4307,-979 4307,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d85e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-961 4341,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="17160@x" ObjectIDND1="g_1ef5380@0" ObjectIDZND0="g_21d9f20@0" ObjectIDZND1="17165@x" Pin0InfoVect0LinkObjId="g_21d9f20_0" Pin0InfoVect1LinkObjId="SW-78331_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78326_0" Pin1InfoVect1LinkObjId="g_1ef5380_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-961 4341,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d9d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-979 4341,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="17160@x" ObjectIDND1="g_1ef5380@0" ObjectIDND2="g_21d9f20@0" ObjectIDZND0="17165@0" Pin0InfoVect0LinkObjId="SW-78331_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-78326_0" Pin1InfoVect1LinkObjId="g_1ef5380_0" Pin1InfoVect2LinkObjId="g_21d9f20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-979 4341,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21dbe80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-841 3616,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="17168@1" Pin0InfoVect0LinkObjId="SW-78334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-841 3616,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21dd1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-788 3616,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17168@0" ObjectIDZND0="17156@1" Pin0InfoVect0LinkObjId="SW-78322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-788 3616,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21deb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-742 3616,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17156@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-742 3616,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21ded50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-674 3659,-674 3659,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_21df130@0" Pin0InfoVect0LinkObjId="g_21df130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-674 3659,-674 3659,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21def40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-685 3616,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_21df130@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_21df130_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-685 3616,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21dfb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-674 3616,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_21df130@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21df130_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-674 3616,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21dfcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-658 3616,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_21df130@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21df130_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-658 3616,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e19b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-658 3551,-658 3551,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_21df130@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21df130_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-658 3551,-658 3551,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e2190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-593 3551,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_21e1ba0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21e1ba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-593 3551,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e39b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-545 3551,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_21e1ba0@0" Pin0InfoVect0LinkObjId="g_21e1ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-545 3551,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e4190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-499 3551,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_21e3ba0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21e3ba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-499 3551,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e4380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-451 3558,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_21e3ba0@0" ObjectIDZND0="g_21e4950@0" Pin0InfoVect0LinkObjId="g_21e4950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_21e3ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-451 3558,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e4570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-424 3551,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_21e4950@0" ObjectIDZND1="g_21e3ba0@0" Pin0InfoVect0LinkObjId="g_21e4950_0" Pin0InfoVect1LinkObjId="g_21e3ba0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-424 3551,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e4760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-451 3551,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_21e4950@0" ObjectIDZND0="g_21e3ba0@0" Pin0InfoVect0LinkObjId="g_21e3ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_21e4950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-451 3551,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e8f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-841 3781,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="17167@1" Pin0InfoVect0LinkObjId="SW-78333_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-841 3781,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21eadd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-792 3781,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17167@0" ObjectIDZND0="17152@1" Pin0InfoVect0LinkObjId="SW-78318_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-792 3781,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ed180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-746 3781,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17152@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-746 3781,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21ed3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-678 3824,-678 3824,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_21ed7e0@0" Pin0InfoVect0LinkObjId="g_21ed7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-678 3824,-678 3824,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21ed5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-689 3781,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_21ed7e0@0" Pin0InfoVect0LinkObjId="g_21ed7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-689 3781,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21ee3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-516 3781,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_21ed7e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_21ed7e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-516 3781,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f0780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-841 4141,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="17171@1" Pin0InfoVect0LinkObjId="SW-78337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-841 4141,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f1140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-791 4141,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17171@0" ObjectIDZND0="g_21f09a0@1" Pin0InfoVect0LinkObjId="g_21f09a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-791 4141,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f1360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-739 4141,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_21f09a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f09a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-739 4141,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f54b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-721 3985,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_21f4cf0@0" ObjectIDZND0="g_21f68c0@0" Pin0InfoVect0LinkObjId="g_21f68c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f4cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-721 3985,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f5710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-769 3952,-769 3952,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_21f4cf0@0" ObjectIDND1="17170@x" ObjectIDZND0="g_21f5bd0@0" Pin0InfoVect0LinkObjId="g_21f5bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21f4cf0_0" Pin1InfoVect1LinkObjId="SW-78336_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-769 3952,-769 3952,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f5970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-769 3985,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_21f5bd0@0" ObjectIDND1="17170@x" ObjectIDZND0="g_21f4cf0@1" Pin0InfoVect0LinkObjId="g_21f4cf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21f5bd0_0" Pin1InfoVect1LinkObjId="SW-78336_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-769 3985,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21fd680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-737 4281,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="17148@0" Pin0InfoVect0LinkObjId="SW-78308_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-737 4281,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22053c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-779 4281,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17148@1" ObjectIDZND0="17166@0" Pin0InfoVect0LinkObjId="SW-78332_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78308_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-779 4281,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22055b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-829 4281,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17166@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-829 4281,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22057a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-769 3985,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_21f4cf0@0" ObjectIDND1="g_21f5bd0@0" ObjectIDZND0="17170@0" Pin0InfoVect0LinkObjId="SW-78336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21f4cf0_0" Pin1InfoVect1LinkObjId="g_21f5bd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-769 3985,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2205990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-828 3985,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17170@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-828 3985,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2208100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-444 4064,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-444 4064,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2209f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-393 4064,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-393 4064,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_220a1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-347 4064,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-347 4064,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_220ce10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-444 4143,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="17171@1" Pin0InfoVect0LinkObjId="SW-78337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-444 4143,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_220e910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-367 4108,-367 4108,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_220d070@0" ObjectIDND1="17171@x" ObjectIDZND0="g_220f030@0" Pin0InfoVect0LinkObjId="g_220f030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_220d070_0" Pin1InfoVect1LinkObjId="SW-78337_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-367 4108,-367 4108,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_220eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-392 4143,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="17171@0" ObjectIDZND0="g_220f030@0" ObjectIDZND1="g_220d070@0" Pin0InfoVect0LinkObjId="g_220f030_0" Pin0InfoVect1LinkObjId="g_220d070_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-392 4143,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_220edd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-367 4143,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_220f030@0" ObjectIDND1="17171@x" ObjectIDZND0="g_220d070@1" Pin0InfoVect0LinkObjId="g_220d070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_220f030_0" Pin1InfoVect1LinkObjId="SW-78337_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-367 4143,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22108f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-312 4143,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_220d070@0" ObjectIDZND0="g_220fce0@0" Pin0InfoVect0LinkObjId="g_220fce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220d070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-312 4143,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2210b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-239 4143,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_220fce0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220fce0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-239 4143,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2216660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-325 4351,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2215e80@0" ObjectIDZND0="g_2217a50@0" Pin0InfoVect0LinkObjId="g_2217a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2215e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-325 4351,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22168c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-373 4318,-373 4318,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2215e80@0" ObjectIDND1="0@x" ObjectIDZND0="g_2216d80@0" Pin0InfoVect0LinkObjId="g_2216d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2215e80_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-373 4318,-373 4318,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2216b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-373 4351,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2216d80@0" ObjectIDND1="0@x" ObjectIDZND0="g_2215e80@1" Pin0InfoVect0LinkObjId="g_2215e80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2216d80_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-373 4351,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2219750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-373 4351,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2215e80@0" ObjectIDND1="g_2216d80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2215e80_0" Pin1InfoVect1LinkObjId="g_2216d80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-373 4351,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2219940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-429 4351,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-429 4351,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_221bde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-444 4438,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-444 4438,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_221dc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-393 4438,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-393 4438,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_221de90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-349 4438,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_221e0f0@0" ObjectIDZND1="g_221f3b0@0" Pin0InfoVect0LinkObjId="g_221e0f0_0" Pin0InfoVect1LinkObjId="g_221f3b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-349 4438,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_221e8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-335 4482,-335 4482,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_221f3b0@0" ObjectIDZND0="g_221e0f0@1" Pin0InfoVect0LinkObjId="g_221e0f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_221f3b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-335 4482,-335 4482,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_221eb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-292 4482,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_221e0f0@0" ObjectIDZND0="g_221ed90@0" Pin0InfoVect0LinkObjId="g_221ed90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_221e0f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-292 4482,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_221ffc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-335 4438,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_221e0f0@0" ObjectIDND1="0@x" ObjectIDZND0="g_221f3b0@0" Pin0InfoVect0LinkObjId="g_221f3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_221e0f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-335 4438,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2220220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-251 4438,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_221f3b0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_221f3b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-251 4438,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2223db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-444 4551,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-444 4551,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2225c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-393 4551,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-393 4551,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2225e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-349 4551,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_22260c0@0" ObjectIDZND1="g_2227380@0" Pin0InfoVect0LinkObjId="g_22260c0_0" Pin0InfoVect1LinkObjId="g_2227380_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-349 4551,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22268a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-335 4595,-335 4595,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2227380@0" ObjectIDZND0="g_22260c0@1" Pin0InfoVect0LinkObjId="g_22260c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2227380_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-335 4595,-335 4595,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2226b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-292 4595,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_22260c0@0" ObjectIDZND0="g_2226d60@0" Pin0InfoVect0LinkObjId="g_2226d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22260c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-292 4595,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2227f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-335 4551,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_22260c0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2227380@0" Pin0InfoVect0LinkObjId="g_2227380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22260c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-335 4551,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22281f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-251 4551,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2227380@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2227380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-251 4551,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2229ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-660 4281,-479 3916,-479 3916,-128 4245,-128 4245,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-660 4281,-479 3916,-479 3916,-128 4245,-128 4245,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_222a0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-432 4245,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-432 4245,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_222cc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4805,-841 4805,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="17169@1" Pin0InfoVect0LinkObjId="SW-78335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4805,-841 4805,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_222edd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4805,-790 4805,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17169@0" ObjectIDZND0="17150@1" Pin0InfoVect0LinkObjId="SW-78316_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4805,-790 4805,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22317d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-704 4709,-718 4805,-718 4805,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="17150@0" Pin0InfoVect0LinkObjId="SW-78316_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-704 4709,-718 4805,-718 4805,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2231a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-647 4673,-647 4673,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2233850@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2231f00@0" Pin0InfoVect0LinkObjId="g_2231f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2233850_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-647 4673,-647 4673,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2231ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-647 4709,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2231f00@0" ObjectIDND1="g_2233850@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2231f00_0" Pin1InfoVect1LinkObjId="g_2233850_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-647 4709,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2233dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-636 4753,-636 4753,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_2231f00@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2233850@1" Pin0InfoVect0LinkObjId="g_2233850_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2231f00_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-636 4753,-636 4753,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2234020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-593 4753,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2233850@0" ObjectIDZND0="g_2234280@0" Pin0InfoVect0LinkObjId="g_2234280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2233850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-593 4753,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2234980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-636 4709,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2233850@0" ObjectIDND1="0@x" ObjectIDZND0="g_2231f00@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2231f00_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2233850_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-636 4709,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2236c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-540 4709,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2231f00@0" ObjectIDZND1="0@x" ObjectIDZND2="g_2233850@0" Pin0InfoVect0LinkObjId="g_2231f00_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2233850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-540 4709,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2239650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-397 4709,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-397 4709,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22398b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-494 4709,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-494 4709,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_223c500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-397 4858,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-397 4858,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_223ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-521 4858,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_223d0d0@1" ObjectIDZND0="g_223d6c0@0" Pin0InfoVect0LinkObjId="g_223d6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_223d0d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-521 4858,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_223f070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-468 4892,-468 4892,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_223d0d0@0" ObjectIDND1="0@x" ObjectIDZND0="g_223f790@0" Pin0InfoVect0LinkObjId="g_223f790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_223d0d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-468 4892,-468 4892,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_223f2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-448 4858,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_223f790@0" ObjectIDZND1="g_223d0d0@0" Pin0InfoVect0LinkObjId="g_223f790_0" Pin0InfoVect1LinkObjId="g_223d0d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-448 4858,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_223f530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-468 4858,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_223f790@0" ObjectIDND1="0@x" ObjectIDZND0="g_223d0d0@0" Pin0InfoVect0LinkObjId="g_223d0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_223f790_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-468 4858,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2242ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-397 4787,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-397 4787,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22451b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-346 4787,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-346 4787,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_224aa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-429 5005,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-429 5005,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_224ac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-300 4787,-273 5005,-273 5005,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-300 4787,-273 5005,-273 5005,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_224ae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-367 5005,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-367 5005,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_224d5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-444 5074,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-444 5074,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_224f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-395 5074,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-395 5074,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_224faf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-351 5074,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_224fd50@0" ObjectIDZND1="g_2317e90@0" Pin0InfoVect0LinkObjId="g_224fd50_0" Pin0InfoVect1LinkObjId="g_2317e90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-351 5074,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23172d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-337 5118,-337 5118,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2317e90@0" ObjectIDZND0="g_224fd50@1" Pin0InfoVect0LinkObjId="g_224fd50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2317e90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-337 5118,-337 5118,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2317530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5118,-294 5118,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_224fd50@0" ObjectIDZND0="g_2317790@0" Pin0InfoVect0LinkObjId="g_2317790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_224fd50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5118,-294 5118,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2318be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-337 5074,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_224fd50@0" ObjectIDND1="0@x" ObjectIDZND0="g_2317e90@0" Pin0InfoVect0LinkObjId="g_2317e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_224fd50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-337 5074,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2318e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-253 5074,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2317e90@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2317e90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-253 5074,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_231d410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-444 5187,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-444 5187,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_231f6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-395 5187,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-395 5187,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_231f940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-351 5187,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_231fba0@0" ObjectIDZND1="g_2320fe0@0" Pin0InfoVect0LinkObjId="g_231fba0_0" Pin0InfoVect1LinkObjId="g_2320fe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-351 5187,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2320420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-337 5231,-337 5231,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2320fe0@0" ObjectIDZND0="g_231fba0@1" Pin0InfoVect0LinkObjId="g_231fba0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2320fe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-337 5231,-337 5231,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2320680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5231,-294 5231,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_231fba0@0" ObjectIDZND0="g_23208e0@0" Pin0InfoVect0LinkObjId="g_23208e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_231fba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5231,-294 5231,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2321d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-337 5187,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_231fba0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2320fe0@0" Pin0InfoVect0LinkObjId="g_2320fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_231fba0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-337 5187,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2321f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-253 5187,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2320fe0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2320fe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-253 5187,-225 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-841 5088,-841 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3525,-841 5088,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4025,-444 4609,-444 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4025,-444 4609,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-397 4915,-397 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4682,-397 4915,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4969,-444 5232,-444 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4969,-444 5232,-444 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">滚水变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3511.000000 -878.000000) translate(0,18)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4299.500000 -1133.000000) translate(0,18)">10kV白星线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3579.000000 -506.000000) translate(0,18)">水泥厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3743.000000 -506.000000) translate(0,18)">罗川线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -659.000000) translate(0,18)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -659.000000) translate(0,18)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4293.500000 -777.000000) translate(0,15)">432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 -712.000000) translate(0,18)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4038.000000 -219.000000) translate(0,18)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4110.000000 -165.000000) translate(0,18)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4259.500000 -418.000000) translate(0,15)">6212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4354.500000 -419.000000) translate(0,15)">6211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4405.000000 -170.000000) translate(0,18)">400kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4517.000000 -170.000000) translate(0,18)">400kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4562.500000 -371.000000) translate(0,15)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4448.000000 -125.000000) translate(0,18)">I级发电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4248.000000 -472.000000) translate(0,18)">6.3kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4715.500000 -695.000000) translate(0,15)">#1杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4725.500000 -536.000000) translate(0,15)">436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4863.500000 -438.000000) translate(0,15)">4381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4797.500000 -323.000000) translate(0,15)">437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4910.500000 -355.000000) translate(0,15)">630kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.500000 -418.000000) translate(0,15)">2131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5041.000000 -172.000000) translate(0,18)">250kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5086.500000 -373.000000) translate(0,15)">211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5153.000000 -172.000000) translate(0,18)">250kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5198.500000 -373.000000) translate(0,15)">212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5084.000000 -127.000000) translate(0,18)">II级发电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5053.000000 -473.000000) translate(0,18)">0.4kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4714.500000 -474.000000) translate(0,15)">4361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5073.500000 -426.000000) translate(0,15)">2111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5188.500000 -427.000000) translate(0,15)">2121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4787.500000 -373.000000) translate(0,15)">4371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3785.000000 -714.000000) translate(0,15)">4336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3622.000000 -706.000000) translate(0,15)">4346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4450.500000 -371.000000) translate(0,15)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4440.500000 -421.000000) translate(0,15)">6121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4561.500000 -419.000000) translate(0,15)">6111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3625.000000 -763.000000) translate(0,12)">434</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3623.000000 -813.000000) translate(0,12)">4341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3790.000000 -767.000000) translate(0,12)">433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3788.000000 -817.000000) translate(0,12)">4331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -817.000000) translate(0,12)">4371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4148.000000 -816.000000) translate(0,12)">4381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4150.000000 -417.000000) translate(0,12)">4381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4350.000000 -932.000000) translate(0,12)">431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4348.000000 -886.000000) translate(0,12)">4311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4348.000000 -1021.000000) translate(0,12)">4313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -766.000000) translate(0,12)">435</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4812.000000 -815.000000) translate(0,12)">4351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4288.000000 -818.000000) translate(0,12)">4321</text>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" stationName="LF_GSB"/>
</svg>