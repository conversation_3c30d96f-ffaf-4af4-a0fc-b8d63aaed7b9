<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-228" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-753 -1470 2142 1461">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.27031"/>
    <polyline points="58,100 64,100 " stroke-width="1.27031"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.27031"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape85_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="14" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="14" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="30" y2="30"/>
   </symbol>
   <symbol id="transformer2:shape85_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape41_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="15,84 21,71 8,71 15,84 15,83 15,84 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="32,42 41,42 41,70 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="44" x2="41" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="15" y1="45" y2="72"/>
    <polyline DF8003:Layer="PUBLIC" points="17,11 21,20 11,20 17,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="17" y2="17"/>
   </symbol>
   <symbol id="transformer2:shape41_1">
    <circle cx="17" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="41" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="41" y2="45"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="voltageTransformer:shape120">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="31" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="31" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="31" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="31" y1="37" y2="35"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="28" y2="19"/>
    <ellipse cx="31" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="18" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="18" y1="37" y2="35"/>
    <ellipse cx="31" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="15" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="21" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="15" y1="19" y2="19"/>
    <ellipse cx="18" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="18" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a7e460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a7f410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aac510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aad4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aae510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aaeff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aaf930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ab0230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2156fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2156fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd2880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd2880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd4310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd4310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2cd5150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a56960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a575d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a58380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a58ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2af2490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a5a080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a5a8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aed930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aeea10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aef390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aefe80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2af0840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2af1440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a1c210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a1d3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a1dfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a90e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a6a9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2a6ba60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2a6d020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1471" width="2152" x="-758" y="-1475"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1387" x2="1387" y1="-566" y2="-544"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-155114">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 146.241796 -785.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26195" ObjectName="SW-DY_LJ.DY_LJ_302BK"/>
     <cge:Meas_Ref ObjectId="155114"/>
    <cge:TPSR_Ref TObjectID="26195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 144.241796 -572.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26198" ObjectName="SW-DY_LJ.DY_LJ_002BK"/>
     <cge:Meas_Ref ObjectId="155123"/>
    <cge:TPSR_Ref TObjectID="26198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155037">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.650861 -784.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26190" ObjectName="SW-DY_LJ.DY_LJ_301BK"/>
     <cge:Meas_Ref ObjectId="155037"/>
    <cge:TPSR_Ref TObjectID="26190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155046">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.650861 -586.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26193" ObjectName="SW-DY_LJ.DY_LJ_001BK"/>
     <cge:Meas_Ref ObjectId="155046"/>
    <cge:TPSR_Ref TObjectID="26193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155334">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 678.000000 -387.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26219" ObjectName="SW-DY_LJ.DY_LJ_012BK"/>
     <cge:Meas_Ref ObjectId="155334"/>
    <cge:TPSR_Ref TObjectID="26219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155217">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.212271 -362.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26203" ObjectName="SW-DY_LJ.DY_LJ_081BK"/>
     <cge:Meas_Ref ObjectId="155217"/>
    <cge:TPSR_Ref TObjectID="26203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155191">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1055.212271 -364.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26200" ObjectName="SW-DY_LJ.DY_LJ_082BK"/>
     <cge:Meas_Ref ObjectId="155191"/>
    <cge:TPSR_Ref TObjectID="26200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155243">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.212271 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26206" ObjectName="SW-DY_LJ.DY_LJ_065BK"/>
     <cge:Meas_Ref ObjectId="155243"/>
    <cge:TPSR_Ref TObjectID="26206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 256.212271 -360.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26209" ObjectName="SW-DY_LJ.DY_LJ_064BK"/>
     <cge:Meas_Ref ObjectId="155269"/>
    <cge:TPSR_Ref TObjectID="26209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155295">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 14.212271 -357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26212" ObjectName="SW-DY_LJ.DY_LJ_063BK"/>
     <cge:Meas_Ref ObjectId="155295"/>
    <cge:TPSR_Ref TObjectID="26212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-269654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.544703 -1013.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43605" ObjectName="SW-DY_LJ.DY_LJ_362BK"/>
     <cge:Meas_Ref ObjectId="269654"/>
    <cge:TPSR_Ref TObjectID="43605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-283807">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 128.544703 -1053.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43611" ObjectName="SW-DY_LJ.DY_LJ_361BK"/>
     <cge:Meas_Ref ObjectId="283807"/>
    <cge:TPSR_Ref TObjectID="43611"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_35436e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.000000 -1157.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35470e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -278.000000 -269.000000)" xlink:href="#voltageTransformer:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3551450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 -273.000000)" xlink:href="#voltageTransformer:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_356d020">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.544703 -1071.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b6380">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 50.544703 -1111.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_LJ" endPointId="0" endStationName="CX_YSQ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shenglong" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="981,-1328 981,-1379 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43719" ObjectName="AC-35kV.LN_shenglong"/>
    <cge:TPSR_Ref TObjectID="43719_SS-228"/></metadata>
   <polyline fill="none" opacity="0" points="981,-1328 981,-1379 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_LJ" endPointId="0" endStationName="DY_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_beilong" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="138,-1353 138,-1381 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38075" ObjectName="AC-35kV.LN_beilong"/>
    <cge:TPSR_Ref TObjectID="38075_SS-228"/></metadata>
   <polyline fill="none" opacity="0" points="138,-1353 138,-1381 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 -353.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 -353.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_LJ.DY_LJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37041"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.787209 -0.000000 0.000000 -0.811765 123.000000 -684.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.787209 -0.000000 0.000000 -0.811765 123.000000 -684.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26223" ObjectName="TF-DY_LJ.DY_LJ_2T"/>
    <cge:TPSR_Ref TObjectID="26223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_LJ.DY_LJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37037"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.880000 -0.000000 0.000000 -0.877778 959.000000 -684.000000)" xlink:href="#transformer2:shape85_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.880000 -0.000000 0.000000 -0.877778 959.000000 -684.000000)" xlink:href="#transformer2:shape85_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26222" ObjectName="TF-DY_LJ.DY_LJ_1T"/>
    <cge:TPSR_Ref TObjectID="26222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.000000 -1372.000000)" xlink:href="#transformer2:shape41_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.000000 -1372.000000)" xlink:href="#transformer2:shape41_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3225140">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1281.676442 -329.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b05140">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.000000 -631.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0cb90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 541.000000 -1097.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2d3d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 486.000000 -1107.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b34860">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 883.676442 -208.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31775c0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1092.676442 -210.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_317c370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 -343.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c34430">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 517.676442 -202.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c3e370">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 293.676442 -206.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c47f90">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 51.676442 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c4ff50">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -222.323558 -325.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c50c80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -254.000000 -339.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c562f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 -451.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3545c30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 148.000000 -617.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354bab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -72.000000 -263.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354c9f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.575163 850.000000 -206.617647)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354d410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.575163 18.000000 -204.617647)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354e2f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.575163 260.000000 -205.617647)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354f1d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.575163 484.000000 -206.617647)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35500b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.575163 1059.000000 -209.617647)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_356c090">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 945.459855 -1138.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_357e460">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.000000 -1256.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b5480">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 102.459855 -1178.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-155387" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -979.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155387" ObjectName="DY_LJ:DY_LJ_YGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-155388" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -937.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155388" ObjectName="DY_LJ:DY_LJ_WGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-154930" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1101.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154930" ObjectName="DY_LJ:DY_LJ_3ⅠM_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215179" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -1060.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215179" ObjectName="DY_LJ:DY_LJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215179" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -1015.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215179" ObjectName="DY_LJ:DY_LJ_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154963" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -115.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154963" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26212"/>
     <cge:Term_Ref ObjectID="37015"/>
    <cge:TPSR_Ref TObjectID="26212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154964" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -115.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154964" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26212"/>
     <cge:Term_Ref ObjectID="37015"/>
    <cge:TPSR_Ref TObjectID="26212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -115.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26212"/>
     <cge:Term_Ref ObjectID="37015"/>
    <cge:TPSR_Ref TObjectID="26212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -115.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26212"/>
     <cge:Term_Ref ObjectID="37015"/>
    <cge:TPSR_Ref TObjectID="26212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154961" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -115.000000) translate(0,77)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154961" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26212"/>
     <cge:Term_Ref ObjectID="37015"/>
    <cge:TPSR_Ref TObjectID="26212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154962" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -115.000000) translate(0,93)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26212"/>
     <cge:Term_Ref ObjectID="37015"/>
    <cge:TPSR_Ref TObjectID="26212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154969" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -108.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26209"/>
     <cge:Term_Ref ObjectID="37009"/>
    <cge:TPSR_Ref TObjectID="26209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -108.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154970" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26209"/>
     <cge:Term_Ref ObjectID="37009"/>
    <cge:TPSR_Ref TObjectID="26209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -108.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26209"/>
     <cge:Term_Ref ObjectID="37009"/>
    <cge:TPSR_Ref TObjectID="26209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -108.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26209"/>
     <cge:Term_Ref ObjectID="37009"/>
    <cge:TPSR_Ref TObjectID="26209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -108.000000) translate(0,77)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26209"/>
     <cge:Term_Ref ObjectID="37009"/>
    <cge:TPSR_Ref TObjectID="26209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -108.000000) translate(0,93)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26209"/>
     <cge:Term_Ref ObjectID="37009"/>
    <cge:TPSR_Ref TObjectID="26209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 515.000000 -105.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26206"/>
     <cge:Term_Ref ObjectID="37003"/>
    <cge:TPSR_Ref TObjectID="26206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 515.000000 -105.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26206"/>
     <cge:Term_Ref ObjectID="37003"/>
    <cge:TPSR_Ref TObjectID="26206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 515.000000 -105.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26206"/>
     <cge:Term_Ref ObjectID="37003"/>
    <cge:TPSR_Ref TObjectID="26206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 515.000000 -105.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26206"/>
     <cge:Term_Ref ObjectID="37003"/>
    <cge:TPSR_Ref TObjectID="26206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 515.000000 -105.000000) translate(0,77)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26206"/>
     <cge:Term_Ref ObjectID="37003"/>
    <cge:TPSR_Ref TObjectID="26206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 515.000000 -105.000000) translate(0,93)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26206"/>
     <cge:Term_Ref ObjectID="37003"/>
    <cge:TPSR_Ref TObjectID="26206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -125.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26203"/>
     <cge:Term_Ref ObjectID="36997"/>
    <cge:TPSR_Ref TObjectID="26203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -125.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26203"/>
     <cge:Term_Ref ObjectID="36997"/>
    <cge:TPSR_Ref TObjectID="26203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -125.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26203"/>
     <cge:Term_Ref ObjectID="36997"/>
    <cge:TPSR_Ref TObjectID="26203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -125.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26203"/>
     <cge:Term_Ref ObjectID="36997"/>
    <cge:TPSR_Ref TObjectID="26203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154949" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -125.000000) translate(0,77)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26203"/>
     <cge:Term_Ref ObjectID="36997"/>
    <cge:TPSR_Ref TObjectID="26203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -125.000000) translate(0,93)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26203"/>
     <cge:Term_Ref ObjectID="36997"/>
    <cge:TPSR_Ref TObjectID="26203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26200"/>
     <cge:Term_Ref ObjectID="36991"/>
    <cge:TPSR_Ref TObjectID="26200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26200"/>
     <cge:Term_Ref ObjectID="36991"/>
    <cge:TPSR_Ref TObjectID="26200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26200"/>
     <cge:Term_Ref ObjectID="36991"/>
    <cge:TPSR_Ref TObjectID="26200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -120.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26200"/>
     <cge:Term_Ref ObjectID="36991"/>
    <cge:TPSR_Ref TObjectID="26200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154955" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -120.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154955" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26200"/>
     <cge:Term_Ref ObjectID="36991"/>
    <cge:TPSR_Ref TObjectID="26200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -120.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26200"/>
     <cge:Term_Ref ObjectID="36991"/>
    <cge:TPSR_Ref TObjectID="26200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -657.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26193"/>
     <cge:Term_Ref ObjectID="36977"/>
    <cge:TPSR_Ref TObjectID="26193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -657.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26193"/>
     <cge:Term_Ref ObjectID="36977"/>
    <cge:TPSR_Ref TObjectID="26193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -657.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26193"/>
     <cge:Term_Ref ObjectID="36977"/>
    <cge:TPSR_Ref TObjectID="26193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -657.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26193"/>
     <cge:Term_Ref ObjectID="36977"/>
    <cge:TPSR_Ref TObjectID="26193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -657.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26193"/>
     <cge:Term_Ref ObjectID="36977"/>
    <cge:TPSR_Ref TObjectID="26193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -657.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26193"/>
     <cge:Term_Ref ObjectID="36977"/>
    <cge:TPSR_Ref TObjectID="26193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -878.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26190"/>
     <cge:Term_Ref ObjectID="36971"/>
    <cge:TPSR_Ref TObjectID="26190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -878.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26190"/>
     <cge:Term_Ref ObjectID="36971"/>
    <cge:TPSR_Ref TObjectID="26190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -878.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26190"/>
     <cge:Term_Ref ObjectID="36971"/>
    <cge:TPSR_Ref TObjectID="26190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -878.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26190"/>
     <cge:Term_Ref ObjectID="36971"/>
    <cge:TPSR_Ref TObjectID="26190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -878.000000) translate(0,77)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26190"/>
     <cge:Term_Ref ObjectID="36971"/>
    <cge:TPSR_Ref TObjectID="26190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -878.000000) translate(0,93)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26190"/>
     <cge:Term_Ref ObjectID="36971"/>
    <cge:TPSR_Ref TObjectID="26190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -680.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26198"/>
     <cge:Term_Ref ObjectID="36987"/>
    <cge:TPSR_Ref TObjectID="26198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -680.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26198"/>
     <cge:Term_Ref ObjectID="36987"/>
    <cge:TPSR_Ref TObjectID="26198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -680.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26198"/>
     <cge:Term_Ref ObjectID="36987"/>
    <cge:TPSR_Ref TObjectID="26198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -680.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26198"/>
     <cge:Term_Ref ObjectID="36987"/>
    <cge:TPSR_Ref TObjectID="26198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -680.000000) translate(0,77)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26198"/>
     <cge:Term_Ref ObjectID="36987"/>
    <cge:TPSR_Ref TObjectID="26198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -680.000000) translate(0,93)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26198"/>
     <cge:Term_Ref ObjectID="36987"/>
    <cge:TPSR_Ref TObjectID="26198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 100.000000 -873.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26195"/>
     <cge:Term_Ref ObjectID="36981"/>
    <cge:TPSR_Ref TObjectID="26195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 100.000000 -873.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26195"/>
     <cge:Term_Ref ObjectID="36981"/>
    <cge:TPSR_Ref TObjectID="26195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 100.000000 -873.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26195"/>
     <cge:Term_Ref ObjectID="36981"/>
    <cge:TPSR_Ref TObjectID="26195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 100.000000 -873.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26195"/>
     <cge:Term_Ref ObjectID="36981"/>
    <cge:TPSR_Ref TObjectID="26195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 100.000000 -873.000000) translate(0,77)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26195"/>
     <cge:Term_Ref ObjectID="36981"/>
    <cge:TPSR_Ref TObjectID="26195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 100.000000 -873.000000) translate(0,93)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26195"/>
     <cge:Term_Ref ObjectID="36981"/>
    <cge:TPSR_Ref TObjectID="26195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1437.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26188"/>
     <cge:Term_Ref ObjectID="36967"/>
    <cge:TPSR_Ref TObjectID="26188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1437.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26188"/>
     <cge:Term_Ref ObjectID="36967"/>
    <cge:TPSR_Ref TObjectID="26188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1437.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26188"/>
     <cge:Term_Ref ObjectID="36967"/>
    <cge:TPSR_Ref TObjectID="26188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154890" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1437.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26188"/>
     <cge:Term_Ref ObjectID="36967"/>
    <cge:TPSR_Ref TObjectID="26188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1437.000000) translate(0,77)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26188"/>
     <cge:Term_Ref ObjectID="36967"/>
    <cge:TPSR_Ref TObjectID="26188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1437.000000) translate(0,93)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26188"/>
     <cge:Term_Ref ObjectID="36967"/>
    <cge:TPSR_Ref TObjectID="26188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-154923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -1063.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26184"/>
     <cge:Term_Ref ObjectID="36962"/>
    <cge:TPSR_Ref TObjectID="26184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-154924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -1063.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26184"/>
     <cge:Term_Ref ObjectID="36962"/>
    <cge:TPSR_Ref TObjectID="26184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-154925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -1063.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26184"/>
     <cge:Term_Ref ObjectID="36962"/>
    <cge:TPSR_Ref TObjectID="26184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-154929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -1063.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26184"/>
     <cge:Term_Ref ObjectID="36962"/>
    <cge:TPSR_Ref TObjectID="26184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-154926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -1063.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26184"/>
     <cge:Term_Ref ObjectID="36962"/>
    <cge:TPSR_Ref TObjectID="26184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-154927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -1063.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26184"/>
     <cge:Term_Ref ObjectID="36962"/>
    <cge:TPSR_Ref TObjectID="26184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-154928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -1063.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26184"/>
     <cge:Term_Ref ObjectID="36962"/>
    <cge:TPSR_Ref TObjectID="26184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-154930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -215.000000 -1063.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26184"/>
     <cge:Term_Ref ObjectID="36962"/>
    <cge:TPSR_Ref TObjectID="26184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-154939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -660.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26186"/>
     <cge:Term_Ref ObjectID="36964"/>
    <cge:TPSR_Ref TObjectID="26186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-154940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -660.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26186"/>
     <cge:Term_Ref ObjectID="36964"/>
    <cge:TPSR_Ref TObjectID="26186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-154941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -660.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26186"/>
     <cge:Term_Ref ObjectID="36964"/>
    <cge:TPSR_Ref TObjectID="26186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-154945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -660.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26186"/>
     <cge:Term_Ref ObjectID="36964"/>
    <cge:TPSR_Ref TObjectID="26186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-154942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -660.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26186"/>
     <cge:Term_Ref ObjectID="36964"/>
    <cge:TPSR_Ref TObjectID="26186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-154943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -660.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26186"/>
     <cge:Term_Ref ObjectID="36964"/>
    <cge:TPSR_Ref TObjectID="26186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-154944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -660.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26186"/>
     <cge:Term_Ref ObjectID="36964"/>
    <cge:TPSR_Ref TObjectID="26186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-154946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -660.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26186"/>
     <cge:Term_Ref ObjectID="36964"/>
    <cge:TPSR_Ref TObjectID="26186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-154931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -664.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26185"/>
     <cge:Term_Ref ObjectID="36963"/>
    <cge:TPSR_Ref TObjectID="26185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-154932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -664.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26185"/>
     <cge:Term_Ref ObjectID="36963"/>
    <cge:TPSR_Ref TObjectID="26185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-154933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -664.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26185"/>
     <cge:Term_Ref ObjectID="36963"/>
    <cge:TPSR_Ref TObjectID="26185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-154937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -664.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26185"/>
     <cge:Term_Ref ObjectID="36963"/>
    <cge:TPSR_Ref TObjectID="26185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-154934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -664.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26185"/>
     <cge:Term_Ref ObjectID="36963"/>
    <cge:TPSR_Ref TObjectID="26185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-154935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -664.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26185"/>
     <cge:Term_Ref ObjectID="36963"/>
    <cge:TPSR_Ref TObjectID="26185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-154936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -664.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26185"/>
     <cge:Term_Ref ObjectID="36963"/>
    <cge:TPSR_Ref TObjectID="26185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-154938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -664.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26185"/>
     <cge:Term_Ref ObjectID="36963"/>
    <cge:TPSR_Ref TObjectID="26185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-154921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -724.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26223"/>
     <cge:Term_Ref ObjectID="37042"/>
    <cge:TPSR_Ref TObjectID="26223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-154922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 264.000000 -753.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26223"/>
     <cge:Term_Ref ObjectID="37042"/>
    <cge:TPSR_Ref TObjectID="26223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-154908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1081.000000 -760.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26222"/>
     <cge:Term_Ref ObjectID="37035"/>
    <cge:TPSR_Ref TObjectID="26222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-154907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1082.000000 -730.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26222"/>
     <cge:Term_Ref ObjectID="37038"/>
    <cge:TPSR_Ref TObjectID="26222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154977" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -380.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26219"/>
     <cge:Term_Ref ObjectID="37029"/>
    <cge:TPSR_Ref TObjectID="26219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -380.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26219"/>
     <cge:Term_Ref ObjectID="37029"/>
    <cge:TPSR_Ref TObjectID="26219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -380.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26219"/>
     <cge:Term_Ref ObjectID="37029"/>
    <cge:TPSR_Ref TObjectID="26219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-269745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -1187.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="269745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43605"/>
     <cge:Term_Ref ObjectID="19783"/>
    <cge:TPSR_Ref TObjectID="43605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-269746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -1187.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="269746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43605"/>
     <cge:Term_Ref ObjectID="19783"/>
    <cge:TPSR_Ref TObjectID="43605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-269741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -1187.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="269741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43605"/>
     <cge:Term_Ref ObjectID="19783"/>
    <cge:TPSR_Ref TObjectID="43605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-269742" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -1187.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="269742" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43605"/>
     <cge:Term_Ref ObjectID="19783"/>
    <cge:TPSR_Ref TObjectID="43605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-269743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -1187.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="269743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43605"/>
     <cge:Term_Ref ObjectID="19783"/>
    <cge:TPSR_Ref TObjectID="43605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-269744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -1187.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="269744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43605"/>
     <cge:Term_Ref ObjectID="19783"/>
    <cge:TPSR_Ref TObjectID="43605"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-606" y="-1252"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="31" y="-386"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="31" y="-386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="277" y="-389"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="277" y="-389"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="500" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="500" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="866" y="-390"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="866" y="-390"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1075" y="-392"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1075" y="-392"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="-667" y="-796"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="-667" y="-796"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="689" y="-420"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="689" y="-420"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-458" y="-1233"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-458" y="-1233"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-458" y="-1268"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-458" y="-1268"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="876" y="-740"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="876" y="-740"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-363" y="-1184"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-363" y="-1184"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="51" y="-746"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="51" y="-746"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="990" y="-1042"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="990" y="-1042"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="147" y="-1082"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="147" y="-1082"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV龙街变DY_LJ_063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="31" y="-386"/></g>
   <g href="35kV龙街变DY_LJ_064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="277" y="-389"/></g>
   <g href="大姚35kV龙街变DY_LJ_065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="500" y="-385"/></g>
   <g href="35kV龙街变DY_LJ_081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="866" y="-390"/></g>
   <g href="大姚35kV龙街变DY_LJ_082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1075" y="-392"/></g>
   <g href="大姚35kV龙街变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="-667" y="-796"/></g>
   <g href="35kV龙街变DY_LJ_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="689" y="-420"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-458" y="-1233"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-458" y="-1268"/></g>
   <g href="35kV龙街变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="876" y="-740"/></g>
   <g href="AVC龙街站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-363" y="-1184"/></g>
   <g href="35kV龙街变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="51" y="-746"/></g>
   <g href="35kV龙街变DY_LJ_362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="990" y="-1042"/></g>
   <g href="35kV龙街变北龙线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="147" y="-1082"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-363" y="-1183"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_LJ.DY_LJ_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1290,-914 -293,-914 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26184" ObjectName="BS-DY_LJ.DY_LJ_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="26184"/></metadata>
   <polyline fill="none" opacity="0" points="1290,-914 -293,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_LJ.DY_LJ_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-511 1338,-511 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26185" ObjectName="BS-DY_LJ.DY_LJ_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="26185"/></metadata>
   <polyline fill="none" opacity="0" points="744,-511 1338,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_LJ.DY_LJ_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="650,-509 -296,-509 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26186" ObjectName="BS-DY_LJ.DY_LJ_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="26186"/></metadata>
   <polyline fill="none" opacity="0" points="650,-509 -296,-509 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a83ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 233.241796 -835.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32babe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1059.650861 -834.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0aef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 640.881701 -1066.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3565400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.544703 -983.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_356b3a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.544703 -1167.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3574230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1040.544703 -1079.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_358c6a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 199.544703 -1023.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3592640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 200.544703 -1207.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34bd260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 197.544703 -1119.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="26184" cx="981" cy="-914" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26185" cx="1257" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26185" cx="980" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26185" cx="855" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26185" cx="1064" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26186" cx="636" cy="-509" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26186" cx="489" cy="-509" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26186" cx="265" cy="-509" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26186" cx="23" cy="-509" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26186" cx="-247" cy="-509" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26185" cx="760" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26186" cx="153" cy="-509" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26184" cx="548" cy="-914" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26184" cx="981" cy="-914" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26184" cx="155" cy="-914" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26184" cx="138" cy="-914" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_2bf0310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">龙街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2975760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：0878-6148349</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bc620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -123.903475 -472.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3225e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -269.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2ea70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 94.000000 -1410.000000) translate(0,12)">35kV北龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -1210.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3171b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.761290 -390.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317b700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.761290 -392.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c38570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.761290 -385.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c424b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 277.761290 -389.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4c0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.761290 -386.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c52530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -292.000000 -266.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5d3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -903.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5d9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 324.000000 -530.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5dc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.000000 -531.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5de70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 35.000000 -457.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5e0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 34.000000 -314.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5e2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -459.000000) translate(0,12)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5e530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 277.000000 -316.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5e770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -439.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5e9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -439.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5ebf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 689.000000 -420.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5ee30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -461.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5f070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 864.000000 -463.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5f2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 864.000000 -321.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5f4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -464.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5f730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -322.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5f970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 499.000000 -455.000000) translate(0,12)">0652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5fbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 496.000000 -314.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5fdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1269.000000 -464.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 987.000000 -563.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 -614.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c604b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -866.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c606f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 -888.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 -813.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 160.000000 -554.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 163.000000 -601.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 190.000000 -866.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c61230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 164.000000 -890.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c61470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 168.000000 -812.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c616b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 599.000000 -1096.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c618f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 558.000000 -1019.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353e820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -17.000000 -722.000000) translate(0,12)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353e820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -17.000000 -722.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353e820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -17.000000 -722.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353e820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -17.000000 -722.000000) translate(0,57)">7.0%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35403f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -667.000000 -796.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -380.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -367.000000) translate(0,12)">Ib(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -350.000000) translate(0,12)">Ic(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3541e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -447.000000 -1225.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3542dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -447.000000 -1260.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3554fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3.000000 -146.000000) translate(0,12)">苍屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3555890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 236.000000 -145.000000) translate(0,12)">大龙箐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3556130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -143.000000) translate(0,12)">塔底线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3556970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -153.000000) translate(0,12)">干海资线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3557520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1044.000000 -150.000000) translate(0,12)">龙街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3557b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -753.000000 -261.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_35590e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -599.000000 -271.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_35590e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -599.000000 -271.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_355a940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 51.000000 -746.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_355b340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -716.000000) translate(0,12)">S7-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_355b340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -716.000000) translate(0,27)">35±3×5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_355b340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -716.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_355b340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -716.000000) translate(0,57)">6.9%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_355c390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -740.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_355cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -346.500000 -1172.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3570fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -1069.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3579680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 988.000000 -1140.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3579900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 -1042.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3579b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 988.000000 -957.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3579d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -1015.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3579fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1006.000000 -1111.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357a200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -1199.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_357ca80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -1409.000000) translate(0,15)">35kV胜龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3582340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -1335.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ba300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 35.000000 -1109.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bed90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -1180.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bf3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 147.000000 -1082.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bf600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -997.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bf840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 165.000000 -1055.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bfa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 163.000000 -1151.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bfcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 165.000000 -1239.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3068040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1036.000000 -1257.000000) translate(0,12)">现场上送电流系数错误,</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3068040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1036.000000 -1257.000000) translate(0,27)">主站临时增加系数处理,现场有工作时处理</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c61c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 219.000000 648.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c62f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 219.000000 635.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c63490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 219.000000 618.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c639f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 217.500000 602.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c64590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 205.000000 678.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.500000 663.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6b8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 221.000000 755.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6c140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 191.500000 724.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6cdd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1039.000000 761.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6d070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1009.500000 730.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6e8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -276.000000 1023.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6ef00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -270.500000 1007.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6f180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -276.000000 1060.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6f3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -284.000000 970.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6f600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -284.000000 953.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6f840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -268.000000 936.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c70400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -276.000000 1041.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c70680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -284.000000 989.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c709b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -283.000000 618.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c70c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -277.500000 602.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c70e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -283.000000 655.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c710c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -291.000000 565.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c71300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -291.000000 548.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c71540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -275.000000 531.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c71780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -283.000000 636.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c719c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -291.000000 584.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c71cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1234.000000 625.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35331a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.500000 609.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35333b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1234.000000 662.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35335f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 572.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3533830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 555.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3533a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1242.000000 538.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3533cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1234.000000 643.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3533ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 591.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3534220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.000000 840.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35344a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.000000 825.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35346e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.000000 809.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3534920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 56.500000 794.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3534b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 44.000000 872.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3534da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 32.500000 855.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35350d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -11.000000 1404.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -11.000000 1389.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -11.000000 1373.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35357d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -12.500000 1358.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -25.000000 1436.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -36.500000 1419.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 880.000000 844.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3536200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 880.000000 829.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3536440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 880.000000 813.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3536680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 878.500000 798.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35368c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 876.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3536b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.500000 859.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3536e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1044.000000 629.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35370b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1044.000000 614.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35372f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1044.000000 598.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3537530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.500000 583.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3537770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.000000 661.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35379b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1018.500000 644.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3539c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -5.000000 84.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353a220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -5.000000 71.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353a460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -5.000000 54.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353a6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -6.500000 38.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353a8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -19.000000 114.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353ab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -30.500000 99.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353ae50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 238.000000 78.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353b0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 238.000000 65.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353b310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 238.000000 48.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353b550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 236.500000 32.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353b790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 224.000000 108.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353b9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.500000 93.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353bd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 469.000000 76.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353bf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 469.000000 63.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353c1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 469.000000 46.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 467.500000 30.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353c640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 455.000000 106.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353c880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 443.500000 91.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353cbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.000000 93.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353ce30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.000000 80.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353d070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.000000 63.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353d2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 843.500000 47.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353d4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 123.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353d730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 819.500000 108.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353da60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.000000 91.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353dce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.000000 78.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353df20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.000000 61.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353e160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1045.500000 45.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353e3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 121.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353e5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1021.500000 106.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35760f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 1159.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3576720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 1146.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3576960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 1129.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3576ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.500000 1113.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3576de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1134.000000 1189.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3577020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1122.500000 1174.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34c5510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 948.000000 1370.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="955" cy="1363" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_LJ.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.761290 -165.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33991" ObjectName="EC-DY_LJ.081Ld"/>
    <cge:TPSR_Ref TObjectID="33991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_LJ.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.761290 -167.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33992" ObjectName="EC-DY_LJ.082Ld"/>
    <cge:TPSR_Ref TObjectID="33992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_LJ.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 479.761290 -162.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33990" ObjectName="EC-DY_LJ.065Ld"/>
    <cge:TPSR_Ref TObjectID="33990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_LJ.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 255.761290 -164.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33989" ObjectName="EC-DY_LJ.064Ld"/>
    <cge:TPSR_Ref TObjectID="33989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_LJ.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.761290 -162.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33988" ObjectName="EC-DY_LJ.063Ld"/>
    <cge:TPSR_Ref TObjectID="33988"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153545" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -444.000000 -1165.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26035" ObjectName="DYN-DY_LJ"/>
     <cge:Meas_Ref ObjectId="153545"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-155115">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 146.241796 -858.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26196" ObjectName="SW-DY_LJ.DY_LJ_3021SW"/>
     <cge:Meas_Ref ObjectId="155115"/>
    <cge:TPSR_Ref TObjectID="26196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 144.241796 -524.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26199" ObjectName="SW-DY_LJ.DY_LJ_0022SW"/>
     <cge:Meas_Ref ObjectId="155124"/>
    <cge:TPSR_Ref TObjectID="26199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155116">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 183.241796 -836.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26197" ObjectName="SW-DY_LJ.DY_LJ_30217SW"/>
     <cge:Meas_Ref ObjectId="155116"/>
    <cge:TPSR_Ref TObjectID="26197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155038">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.650861 -856.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26191" ObjectName="SW-DY_LJ.DY_LJ_3011SW"/>
     <cge:Meas_Ref ObjectId="155038"/>
    <cge:TPSR_Ref TObjectID="26191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155047">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.650861 -533.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26194" ObjectName="SW-DY_LJ.DY_LJ_0011SW"/>
     <cge:Meas_Ref ObjectId="155047"/>
    <cge:TPSR_Ref TObjectID="26194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155039">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.650861 -835.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26192" ObjectName="SW-DY_LJ.DY_LJ_30117SW"/>
     <cge:Meas_Ref ObjectId="155039"/>
    <cge:TPSR_Ref TObjectID="26192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.226115 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26221" ObjectName="SW-DY_LJ.DY_LJ_0122SW"/>
     <cge:Meas_Ref ObjectId="155337"/>
    <cge:TPSR_Ref TObjectID="26221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155336">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.226115 -404.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26220" ObjectName="SW-DY_LJ.DY_LJ_0121SW"/>
     <cge:Meas_Ref ObjectId="155336"/>
    <cge:TPSR_Ref TObjectID="26220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.226115 -435.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26217" ObjectName="SW-DY_LJ.DY_LJ_0901SW"/>
     <cge:Meas_Ref ObjectId="155323"/>
    <cge:TPSR_Ref TObjectID="26217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155321">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 538.881701 -991.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26215" ObjectName="SW-DY_LJ.DY_LJ_3901SW"/>
     <cge:Meas_Ref ObjectId="155321"/>
    <cge:TPSR_Ref TObjectID="26215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155322">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 589.881701 -1067.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26216" ObjectName="SW-DY_LJ.DY_LJ_39017SW"/>
     <cge:Meas_Ref ObjectId="155322"/>
    <cge:TPSR_Ref TObjectID="26216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155218">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.761290 -432.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26204" ObjectName="SW-DY_LJ.DY_LJ_0811SW"/>
     <cge:Meas_Ref ObjectId="155218"/>
    <cge:TPSR_Ref TObjectID="26204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155219">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.761290 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26205" ObjectName="SW-DY_LJ.DY_LJ_0816SW"/>
     <cge:Meas_Ref ObjectId="155219"/>
    <cge:TPSR_Ref TObjectID="26205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.761290 -434.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26201" ObjectName="SW-DY_LJ.DY_LJ_0821SW"/>
     <cge:Meas_Ref ObjectId="155192"/>
    <cge:TPSR_Ref TObjectID="26201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.761290 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26202" ObjectName="SW-DY_LJ.DY_LJ_0826SW"/>
     <cge:Meas_Ref ObjectId="155193"/>
    <cge:TPSR_Ref TObjectID="26202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155244">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 479.761290 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26207" ObjectName="SW-DY_LJ.DY_LJ_0652SW"/>
     <cge:Meas_Ref ObjectId="155244"/>
    <cge:TPSR_Ref TObjectID="26207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 479.761290 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26208" ObjectName="SW-DY_LJ.DY_LJ_0656SW"/>
     <cge:Meas_Ref ObjectId="155245"/>
    <cge:TPSR_Ref TObjectID="26208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155270">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 255.761290 -430.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26210" ObjectName="SW-DY_LJ.DY_LJ_0642SW"/>
     <cge:Meas_Ref ObjectId="155270"/>
    <cge:TPSR_Ref TObjectID="26210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155271">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 255.761290 -288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26211" ObjectName="SW-DY_LJ.DY_LJ_0646SW"/>
     <cge:Meas_Ref ObjectId="155271"/>
    <cge:TPSR_Ref TObjectID="26211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155296">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.761290 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26213" ObjectName="SW-DY_LJ.DY_LJ_0632SW"/>
     <cge:Meas_Ref ObjectId="155296"/>
    <cge:TPSR_Ref TObjectID="26213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155297">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.761290 -285.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26214" ObjectName="SW-DY_LJ.DY_LJ_0636SW"/>
     <cge:Meas_Ref ObjectId="155297"/>
    <cge:TPSR_Ref TObjectID="26214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -255.773885 -431.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26218" ObjectName="SW-DY_LJ.DY_LJ_0902SW"/>
     <cge:Meas_Ref ObjectId="155324"/>
    <cge:TPSR_Ref TObjectID="26218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -78.796969 -279.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-269655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.544703 -927.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43606" ObjectName="SW-DY_LJ.DY_LJ_3621SW"/>
     <cge:Meas_Ref ObjectId="269655"/>
    <cge:TPSR_Ref TObjectID="43606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-269657">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 992.544703 -984.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43608" ObjectName="SW-DY_LJ.DY_LJ_36217SW"/>
     <cge:Meas_Ref ObjectId="269657"/>
    <cge:TPSR_Ref TObjectID="43608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-269656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.544703 -1110.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43607" ObjectName="SW-DY_LJ.DY_LJ_3626SW"/>
     <cge:Meas_Ref ObjectId="269656"/>
    <cge:TPSR_Ref TObjectID="43607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-269659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 993.544703 -1168.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43610" ObjectName="SW-DY_LJ.DY_LJ_36267SW"/>
     <cge:Meas_Ref ObjectId="269659"/>
    <cge:TPSR_Ref TObjectID="43610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.544703 -1108.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-269658">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 991.544703 -1080.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43609" ObjectName="SW-DY_LJ.DY_LJ_36260SW"/>
     <cge:Meas_Ref ObjectId="269658"/>
    <cge:TPSR_Ref TObjectID="43609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 204.203031 -1300.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155013">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 128.544703 -963.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26188" ObjectName="SW-DY_LJ.DY_LJ_3611SW"/>
     <cge:Meas_Ref ObjectId="155013"/>
    <cge:TPSR_Ref TObjectID="26188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155014">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 149.544703 -1024.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26189" ObjectName="SW-DY_LJ.DY_LJ_36117SW"/>
     <cge:Meas_Ref ObjectId="155014"/>
    <cge:TPSR_Ref TObjectID="26189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-269722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 128.544703 -1150.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43612" ObjectName="SW-DY_LJ.DY_LJ_3616SW"/>
     <cge:Meas_Ref ObjectId="269722"/>
    <cge:TPSR_Ref TObjectID="43612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-269724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 150.544703 -1208.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43614" ObjectName="SW-DY_LJ.DY_LJ_36167SW"/>
     <cge:Meas_Ref ObjectId="269724"/>
    <cge:TPSR_Ref TObjectID="43614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.544703 -1148.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-269723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 148.544703 -1120.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43613" ObjectName="SW-DY_LJ.DY_LJ_36160SW"/>
     <cge:Meas_Ref ObjectId="269723"/>
    <cge:TPSR_Ref TObjectID="43613"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2b911f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="153,-580 153,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26198@0" ObjectIDZND0="26199@1" Pin0InfoVect0LinkObjId="SW-155124_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="153,-580 153,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b91450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="153,-529 153,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26199@0" ObjectIDZND0="26186@0" Pin0InfoVect0LinkObjId="g_2c35160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155124_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="153,-529 153,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b916b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="155,-793 155,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26195@0" ObjectIDZND0="26223@1" Pin0InfoVect0LinkObjId="g_3546e80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155114_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="155,-793 155,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b91910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="155,-841 188,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26195@x" ObjectIDND1="26196@x" ObjectIDZND0="26197@1" Pin0InfoVect0LinkObjId="SW-155116_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155114_0" Pin1InfoVect1LinkObjId="SW-155115_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="155,-841 188,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a84910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="224,-841 238,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26197@0" ObjectIDZND0="g_2a83ee0@0" Pin0InfoVect0LinkObjId="g_2a83ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155116_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="224,-841 238,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a84b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="155,-841 155,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26197@x" ObjectIDND1="26196@x" ObjectIDZND0="26195@1" Pin0InfoVect0LinkObjId="SW-155114_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155116_0" Pin1InfoVect1LinkObjId="SW-155115_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="155,-841 155,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231bc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-792 982,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26190@0" ObjectIDZND0="26222@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155037_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-792 982,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231be70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-840 1014,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26191@x" ObjectIDND1="26190@x" ObjectIDZND0="26192@1" Pin0InfoVect0LinkObjId="SW-155039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155038_0" Pin1InfoVect1LinkObjId="SW-155037_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-840 1014,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32bb670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-840 1064,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26192@0" ObjectIDZND0="g_32babe0@0" Pin0InfoVect0LinkObjId="g_32babe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155039_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-840 1064,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32bb8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-861 981,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26191@0" ObjectIDZND0="26192@x" ObjectIDZND1="26190@x" Pin0InfoVect0LinkObjId="SW-155039_0" Pin0InfoVect1LinkObjId="SW-155037_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155038_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="981,-861 981,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32bbb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-840 981,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26192@x" ObjectIDND1="26191@x" ObjectIDZND0="26190@1" Pin0InfoVect0LinkObjId="SW-155037_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155039_0" Pin1InfoVect1LinkObjId="SW-155038_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-840 981,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32be350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-897 981,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26191@1" ObjectIDZND0="26184@0" Pin0InfoVect0LinkObjId="g_357aaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155038_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-897 981,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32be540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-509 636,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26186@0" ObjectIDZND0="26221@1" Pin0InfoVect0LinkObjId="SW-155337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b91450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-509 636,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3222480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1257,-511 1257,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26185@0" ObjectIDZND0="26217@1" Pin0InfoVect0LinkObjId="SW-155323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2e810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1257,-511 1257,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3224ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1257,-347 1257,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_317c370@0" ObjectIDZND0="g_3551450@0" Pin0InfoVect0LinkObjId="g_3551450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_317c370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1257,-347 1257,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0b980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-1072 645,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26216@0" ObjectIDZND0="g_2b0aef0@0" Pin0InfoVect0LinkObjId="g_2b0aef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-1072 645,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0bbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="595,-1072 547,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26216@1" ObjectIDZND0="26215@x" ObjectIDZND1="g_2b2d3d0@0" ObjectIDZND2="g_2b0cb90@0" Pin0InfoVect0LinkObjId="SW-155321_0" Pin0InfoVect1LinkObjId="g_2b2d3d0_0" Pin0InfoVect2LinkObjId="g_2b0cb90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155322_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="595,-1072 547,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0c6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-1032 548,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26215@1" ObjectIDZND0="26216@x" ObjectIDZND1="g_2b2d3d0@0" ObjectIDZND2="g_2b0cb90@0" Pin0InfoVect0LinkObjId="SW-155322_0" Pin0InfoVect1LinkObjId="g_2b2d3d0_0" Pin0InfoVect2LinkObjId="g_2b0cb90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155321_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="548,-1032 548,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0c930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="549,-1162 549,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_35436e0@0" ObjectIDZND0="g_2b0cb90@1" Pin0InfoVect0LinkObjId="g_2b0cb90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35436e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="549,-1162 549,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-1086 493,-1086 493,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26216@x" ObjectIDND1="26215@x" ObjectIDND2="g_2b0cb90@0" ObjectIDZND0="g_2b2d3d0@0" Pin0InfoVect0LinkObjId="g_2b2d3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155322_0" Pin1InfoVect1LinkObjId="SW-155321_0" Pin1InfoVect2LinkObjId="g_2b0cb90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="548,-1086 493,-1086 493,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b2cf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-1072 548,-1086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26216@x" ObjectIDND1="26215@x" ObjectIDZND0="g_2b2d3d0@0" ObjectIDZND1="g_2b0cb90@0" Pin0InfoVect0LinkObjId="g_2b2d3d0_0" Pin0InfoVect1LinkObjId="g_2b0cb90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155322_0" Pin1InfoVect1LinkObjId="SW-155321_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="548,-1072 548,-1086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b2d170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-1086 548,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b2d3d0@0" ObjectIDND1="26216@x" ObjectIDND2="26215@x" ObjectIDZND0="g_2b0cb90@0" Pin0InfoVect0LinkObjId="g_2b0cb90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b2d3d0_0" Pin1InfoVect1LinkObjId="SW-155322_0" Pin1InfoVect2LinkObjId="SW-155321_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="548,-1086 548,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2e0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-688 980,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="26222@0" ObjectIDZND0="g_2b05140@1" Pin0InfoVect0LinkObjId="g_2b05140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_231bc10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="980,-688 980,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2e350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-636 980,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2b05140@0" ObjectIDZND0="26193@1" Pin0InfoVect0LinkObjId="SW-155046_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b05140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="980,-636 980,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-594 980,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26193@0" ObjectIDZND0="26194@1" Pin0InfoVect0LinkObjId="SW-155047_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155046_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="980,-594 980,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-538 980,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26194@0" ObjectIDZND0="26185@0" Pin0InfoVect0LinkObjId="g_2b35590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155047_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="980,-538 980,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b35590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="855,-473 855,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26204@1" ObjectIDZND0="26185@0" Pin0InfoVect0LinkObjId="g_2b2e810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="855,-473 855,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b357f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="855,-437 855,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26204@0" ObjectIDZND0="26203@1" Pin0InfoVect0LinkObjId="SW-155217_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155218_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="855,-437 855,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b35a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="855,-370 855,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26203@0" ObjectIDZND0="26205@1" Pin0InfoVect0LinkObjId="SW-155219_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155217_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="855,-370 855,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31716d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="891,-262 891,-277 855,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b34860@0" ObjectIDZND0="26205@x" ObjectIDZND1="g_354c9f0@0" Pin0InfoVect0LinkObjId="SW-155219_0" Pin0InfoVect1LinkObjId="g_354c9f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b34860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="891,-262 891,-277 855,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3171930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="855,-295 855,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26205@0" ObjectIDZND0="g_2b34860@0" ObjectIDZND1="g_354c9f0@0" Pin0InfoVect0LinkObjId="g_2b34860_0" Pin0InfoVect1LinkObjId="g_354c9f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155219_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="855,-295 855,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31782f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1064,-475 1064,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26201@1" ObjectIDZND0="26185@0" Pin0InfoVect0LinkObjId="g_2b2e810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155192_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1064,-475 1064,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3178550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1064,-439 1064,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26201@0" ObjectIDZND0="26200@1" Pin0InfoVect0LinkObjId="SW-155191_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1064,-439 1064,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31787b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1064,-372 1064,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26200@0" ObjectIDZND0="26202@1" Pin0InfoVect0LinkObjId="SW-155193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1064,-372 1064,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317b240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1100,-264 1100,-279 1064,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_31775c0@0" ObjectIDZND0="26202@x" ObjectIDZND1="g_35500b0@0" Pin0InfoVect0LinkObjId="SW-155193_0" Pin0InfoVect1LinkObjId="g_35500b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31775c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1100,-264 1100,-279 1064,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317b4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1064,-297 1064,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26202@0" ObjectIDZND0="g_31775c0@0" ObjectIDZND1="g_35500b0@0" Pin0InfoVect0LinkObjId="g_31775c0_0" Pin0InfoVect1LinkObjId="g_35500b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1064,-297 1064,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317cad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-383 1289,-414 1257,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3225140@0" ObjectIDZND0="26217@x" ObjectIDZND1="g_317c370@0" Pin0InfoVect0LinkObjId="SW-155323_0" Pin0InfoVect1LinkObjId="g_317c370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3225140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-383 1289,-414 1257,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317d5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1257,-440 1257,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26217@0" ObjectIDZND0="g_3225140@0" ObjectIDZND1="g_317c370@0" Pin0InfoVect0LinkObjId="g_3225140_0" Pin0InfoVect1LinkObjId="g_317c370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1257,-440 1257,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317d820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1257,-414 1257,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3225140@0" ObjectIDND1="26217@x" ObjectIDZND0="g_317c370@1" Pin0InfoVect0LinkObjId="g_317c370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3225140_0" Pin1InfoVect1LinkObjId="SW-155323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1257,-414 1257,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c35160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-467 489,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26207@1" ObjectIDZND0="26186@0" Pin0InfoVect0LinkObjId="g_2b91450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155244_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-467 489,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c353c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-431 489,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26207@0" ObjectIDZND0="26206@1" Pin0InfoVect0LinkObjId="SW-155243_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-431 489,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c35620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-364 489,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26206@0" ObjectIDZND0="26208@1" Pin0InfoVect0LinkObjId="SW-155245_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155243_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-364 489,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c380b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="525,-256 525,-271 489,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2c34430@0" ObjectIDZND0="26208@x" ObjectIDZND1="g_354f1d0@0" Pin0InfoVect0LinkObjId="SW-155245_0" Pin0InfoVect1LinkObjId="g_354f1d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c34430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="525,-256 525,-271 489,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c38310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-289 489,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26208@0" ObjectIDZND0="g_2c34430@0" ObjectIDZND1="g_354f1d0@0" Pin0InfoVect0LinkObjId="g_2c34430_0" Pin0InfoVect1LinkObjId="g_354f1d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155245_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="489,-289 489,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3f0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="265,-471 265,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26210@1" ObjectIDZND0="26186@0" Pin0InfoVect0LinkObjId="g_2b91450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="265,-471 265,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3f300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="265,-435 265,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26210@0" ObjectIDZND0="26209@1" Pin0InfoVect0LinkObjId="SW-155269_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="265,-435 265,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3f560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="265,-368 265,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26209@0" ObjectIDZND0="26211@1" Pin0InfoVect0LinkObjId="SW-155271_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="265,-368 265,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c41ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="301,-260 301,-275 265,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2c3e370@0" ObjectIDZND0="26211@x" ObjectIDZND1="g_354e2f0@0" Pin0InfoVect0LinkObjId="SW-155271_0" Pin0InfoVect1LinkObjId="g_354e2f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c3e370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="301,-260 301,-275 265,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c42250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="265,-293 265,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26211@0" ObjectIDZND0="g_2c3e370@0" ObjectIDZND1="g_354e2f0@0" Pin0InfoVect0LinkObjId="g_2c3e370_0" Pin0InfoVect1LinkObjId="g_354e2f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="265,-293 265,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c48cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,-468 23,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26213@1" ObjectIDZND0="26186@0" Pin0InfoVect0LinkObjId="g_2b91450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155296_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="23,-468 23,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c48f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,-432 23,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26213@0" ObjectIDZND0="26212@1" Pin0InfoVect0LinkObjId="SW-155295_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="23,-432 23,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c49180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,-365 23,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26212@0" ObjectIDZND0="26214@1" Pin0InfoVect0LinkObjId="SW-155297_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="23,-365 23,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4bc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="59,-257 59,-272 23,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2c47f90@0" ObjectIDZND0="26214@x" ObjectIDZND1="g_354bab0@0" ObjectIDZND2="g_354d410@0" Pin0InfoVect0LinkObjId="SW-155297_0" Pin0InfoVect1LinkObjId="g_354bab0_0" Pin0InfoVect2LinkObjId="g_354d410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c47f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="59,-257 59,-272 23,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4be70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,-290 23,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26214@0" ObjectIDZND0="g_2c47f90@0" ObjectIDZND1="g_354bab0@0" ObjectIDZND2="g_354d410@0" Pin0InfoVect0LinkObjId="g_2c47f90_0" Pin0InfoVect1LinkObjId="g_354bab0_0" Pin0InfoVect2LinkObjId="g_354d410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155297_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="23,-290 23,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4d3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-247,-509 -247,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26186@0" ObjectIDZND0="26218@1" Pin0InfoVect0LinkObjId="SW-155324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b91450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-247,-509 -247,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-247,-343 -247,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2c50c80@0" ObjectIDZND0="g_35470e0@0" Pin0InfoVect0LinkObjId="g_35470e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c50c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-247,-343 -247,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c51580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-215,-379 -215,-410 -247,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2c4ff50@0" ObjectIDZND0="26218@x" ObjectIDZND1="g_2c50c80@0" Pin0InfoVect0LinkObjId="SW-155324_0" Pin0InfoVect1LinkObjId="g_2c50c80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c4ff50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-215,-379 -215,-410 -247,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c517e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-247,-436 -247,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26218@0" ObjectIDZND0="g_2c4ff50@0" ObjectIDZND1="g_2c50c80@0" Pin0InfoVect0LinkObjId="g_2c4ff50_0" Pin0InfoVect1LinkObjId="g_2c50c80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-247,-436 -247,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c51a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-247,-410 -247,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c4ff50@0" ObjectIDND1="26218@x" ObjectIDZND0="g_2c50c80@1" Pin0InfoVect0LinkObjId="g_2c50c80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c4ff50_0" Pin1InfoVect1LinkObjId="SW-155324_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-247,-410 -247,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c52f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-74,-358 -74,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_35436e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35436e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-74,-358 -74,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c56090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-511 760,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="26185@0" ObjectIDZND0="g_2c562f0@1" Pin0InfoVect0LinkObjId="g_2c562f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2e810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-511 760,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c56f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-456 760,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c562f0@0" ObjectIDZND0="26220@1" Pin0InfoVect0LinkObjId="SW-155336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c562f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-456 760,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c57170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-409 760,-397 714,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26220@0" ObjectIDZND0="26219@0" Pin0InfoVect0LinkObjId="SW-155334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-409 760,-397 714,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c573d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-414 636,-397 687,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26221@0" ObjectIDZND0="26219@1" Pin0InfoVect0LinkObjId="SW-155334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-414 636,-397 687,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3546c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="153,-622 153,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3545c30@0" ObjectIDZND0="26198@1" Pin0InfoVect0LinkObjId="SW-155123_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3545c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="153,-622 153,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3546e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="153,-675 153,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3545c30@1" ObjectIDZND0="26223@0" Pin0InfoVect0LinkObjId="g_2b916b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3545c30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="153,-675 153,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354c530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,-272 12,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c47f90@0" ObjectIDND1="26214@x" ObjectIDND2="g_354d410@0" ObjectIDZND0="g_354bab0@0" Pin0InfoVect0LinkObjId="g_354bab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c47f90_0" Pin1InfoVect1LinkObjId="SW-155297_0" Pin1InfoVect2LinkObjId="g_354d410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="23,-272 12,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354c790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-67,-272 -74,-272 -74,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_354bab0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_35436e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354bab0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-67,-272 -74,-272 -74,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354de30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,-189 23,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="33988@0" ObjectIDZND0="g_354d410@0" Pin0InfoVect0LinkObjId="g_354d410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_LJ.063Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="23,-189 23,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354e090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,-253 23,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_354d410@1" ObjectIDZND0="g_2c47f90@0" ObjectIDZND1="26214@x" ObjectIDZND2="g_354bab0@0" Pin0InfoVect0LinkObjId="g_2c47f90_0" Pin0InfoVect1LinkObjId="SW-155297_0" Pin0InfoVect2LinkObjId="g_354bab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354d410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="23,-253 23,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354ed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="265,-275 265,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c3e370@0" ObjectIDND1="26211@x" ObjectIDZND0="g_354e2f0@1" Pin0InfoVect0LinkObjId="g_354e2f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c3e370_0" Pin1InfoVect1LinkObjId="SW-155271_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="265,-275 265,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354ef70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="265,-208 265,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_354e2f0@0" ObjectIDZND0="33989@0" Pin0InfoVect0LinkObjId="EC-DY_LJ.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354e2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="265,-208 265,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354fbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-271 489,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c34430@0" ObjectIDND1="26208@x" ObjectIDZND0="g_354f1d0@1" Pin0InfoVect0LinkObjId="g_354f1d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c34430_0" Pin1InfoVect1LinkObjId="SW-155245_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-271 489,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354fe50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="489,-209 489,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_354f1d0@0" ObjectIDZND0="33990@0" Pin0InfoVect0LinkObjId="EC-DY_LJ.065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354f1d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-209 489,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3550ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1064,-279 1064,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_31775c0@0" ObjectIDND1="26202@x" ObjectIDZND0="g_35500b0@1" Pin0InfoVect0LinkObjId="g_35500b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31775c0_0" Pin1InfoVect1LinkObjId="SW-155193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1064,-279 1064,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3550d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1064,-212 1064,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_35500b0@0" ObjectIDZND0="33992@0" Pin0InfoVect0LinkObjId="EC-DY_LJ.082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35500b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1064,-212 1064,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3550f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="855,-277 855,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b34860@0" ObjectIDND1="26205@x" ObjectIDZND0="g_354c9f0@1" Pin0InfoVect0LinkObjId="g_354c9f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b34860_0" Pin1InfoVect1LinkObjId="SW-155219_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="855,-277 855,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35511f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="855,-209 855,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_354c9f0@0" ObjectIDZND0="33991@0" Pin0InfoVect0LinkObjId="EC-DY_LJ.081Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354c9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="855,-209 855,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3562230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-989 998,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43605@x" ObjectIDND1="43606@x" ObjectIDZND0="43608@1" Pin0InfoVect0LinkObjId="SW-269657_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-269654_0" Pin1InfoVect1LinkObjId="SW-269655_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-989 998,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3562490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1021 981,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43605@0" ObjectIDZND0="43608@x" ObjectIDZND1="43606@x" Pin0InfoVect0LinkObjId="SW-269657_0" Pin0InfoVect1LinkObjId="SW-269655_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1021 981,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35626f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-989 981,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="43608@x" ObjectIDND1="43605@x" ObjectIDZND0="43606@1" Pin0InfoVect0LinkObjId="SW-269655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-269657_0" Pin1InfoVect1LinkObjId="SW-269654_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-989 981,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3565e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-989 1047,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43608@0" ObjectIDZND0="g_3565400@0" Pin0InfoVect0LinkObjId="g_3565400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-989 1047,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_356be30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1035,-1173 1048,-1173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43610@0" ObjectIDZND0="g_356b3a0@0" Pin0InfoVect0LinkObjId="g_356b3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1035,-1173 1048,-1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_356cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="903,-1113 903,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_356d020@0" Pin0InfoVect0LinkObjId="g_356d020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35436e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="903,-1113 903,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3571950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1085 997,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43605@x" ObjectIDND1="43607@x" ObjectIDZND0="43609@1" Pin0InfoVect0LinkObjId="SW-269658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-269654_0" Pin1InfoVect1LinkObjId="SW-269656_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1085 997,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3574cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-1085 1045,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43609@0" ObjectIDZND0="g_3574230@0" Pin0InfoVect0LinkObjId="g_3574230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-1085 1045,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3574f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1048 981,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43605@1" ObjectIDZND0="43609@x" ObjectIDZND1="43607@x" Pin0InfoVect0LinkObjId="SW-269658_0" Pin0InfoVect1LinkObjId="SW-269656_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1048 981,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3575180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1085 981,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="43609@x" ObjectIDND1="43605@x" ObjectIDZND0="43607@0" Pin0InfoVect0LinkObjId="SW-269656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-269658_0" Pin1InfoVect1LinkObjId="SW-269654_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1085 981,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35753e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-1173 981,-1173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43610@1" ObjectIDZND0="43607@x" ObjectIDZND1="g_356c090@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-269656_0" Pin0InfoVect1LinkObjId="g_356c090_0" Pin0InfoVect2LinkObjId="g_35436e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269659_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="999,-1173 981,-1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3575640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1151 981,-1173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43607@1" ObjectIDZND0="43610@x" ObjectIDZND1="g_356c090@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-269659_0" Pin0InfoVect1LinkObjId="g_356c090_0" Pin0InfoVect2LinkObjId="g_35436e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1151 981,-1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35758a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-1192 981,-1192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_356c090@0" ObjectIDZND0="43610@x" ObjectIDZND1="43607@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-269659_0" Pin0InfoVect1LinkObjId="SW-269656_0" Pin0InfoVect2LinkObjId="g_35436e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_356c090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="952,-1192 981,-1192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3575b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1173 981,-1192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43610@x" ObjectIDND1="43607@x" ObjectIDZND0="g_356c090@0" ObjectIDZND1="0@x" ObjectIDZND2="g_357e460@0" Pin0InfoVect0LinkObjId="g_356c090_0" Pin0InfoVect1LinkObjId="g_35436e0_0" Pin0InfoVect2LinkObjId="g_357e460_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-269659_0" Pin1InfoVect1LinkObjId="SW-269656_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1173 981,-1192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3575d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1212 981,-1319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_356c090@0" ObjectIDND1="43610@x" ObjectIDND2="43607@x" ObjectIDZND0="g_357e460@0" ObjectIDZND1="43719@1" Pin0InfoVect0LinkObjId="g_357e460_0" Pin0InfoVect1LinkObjId="g_35850c0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_356c090_0" Pin1InfoVect1LinkObjId="SW-269659_0" Pin1InfoVect2LinkObjId="SW-269656_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1212 981,-1319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357aaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-996 548,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26215@0" ObjectIDZND0="26184@0" Pin0InfoVect0LinkObjId="g_32be350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="548,-996 548,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357b2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-932 981,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43606@0" ObjectIDZND0="26184@0" Pin0InfoVect0LinkObjId="g_32be350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-932 981,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357c2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1192 981,-1212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_356c090@0" ObjectIDND1="43610@x" ObjectIDND2="43607@x" ObjectIDZND0="0@x" ObjectIDZND1="g_357e460@0" ObjectIDZND2="43719@1" Pin0InfoVect0LinkObjId="g_35436e0_0" Pin0InfoVect1LinkObjId="g_357e460_0" Pin0InfoVect2LinkObjId="g_35850c0_1" Pin0Num="3" Pin1InfoVect0LinkObjId="g_356c090_0" Pin1InfoVect1LinkObjId="SW-269659_0" Pin1InfoVect2LinkObjId="SW-269656_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1192 981,-1212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1212 903,-1212 903,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_356c090@0" ObjectIDND1="43610@x" ObjectIDND2="43607@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_35436e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_356c090_0" Pin1InfoVect1LinkObjId="SW-269659_0" Pin1InfoVect2LinkObjId="SW-269656_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1212 903,-1212 903,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35820e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="209,-1350 209,-1377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_35436e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35436e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="209,-1350 209,-1377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3584e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1300 981,-1319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_357e460@1" ObjectIDZND0="g_356c090@0" ObjectIDZND1="43610@x" ObjectIDZND2="43607@x" Pin0InfoVect0LinkObjId="g_356c090_0" Pin0InfoVect1LinkObjId="SW-269659_0" Pin0InfoVect2LinkObjId="SW-269656_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_357e460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1300 981,-1319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35850c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1319 981,-1330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_356c090@0" ObjectIDND1="43610@x" ObjectIDND2="43607@x" ObjectIDZND0="43719@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_356c090_0" Pin1InfoVect1LinkObjId="SW-269659_0" Pin1InfoVect2LinkObjId="SW-269656_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1319 981,-1330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_358d130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-1029 204,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26189@0" ObjectIDZND0="g_358c6a0@0" Pin0InfoVect0LinkObjId="g_358c6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-1029 204,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35930d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="192,-1213 205,-1213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43614@0" ObjectIDZND0="g_3592640@0" Pin0InfoVect0LinkObjId="g_3592640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="192,-1213 205,-1213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34b6150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="60,-1153 60,-1142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_34b6380@0" Pin0InfoVect0LinkObjId="g_34b6380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35436e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="60,-1153 60,-1142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ba930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1125 154,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43611@x" ObjectIDND1="43612@x" ObjectIDZND0="43613@1" Pin0InfoVect0LinkObjId="SW-269723_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-283807_0" Pin1InfoVect1LinkObjId="SW-269722_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1125 154,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34bdcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="190,-1125 202,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43613@0" ObjectIDZND0="g_34bd260@0" Pin0InfoVect0LinkObjId="g_34bd260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269723_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="190,-1125 202,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34bdf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1088 138,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43611@1" ObjectIDZND0="43613@x" ObjectIDZND1="43612@x" Pin0InfoVect0LinkObjId="SW-269723_0" Pin0InfoVect1LinkObjId="SW-269722_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-283807_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1088 138,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34be1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1125 138,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43611@x" ObjectIDND1="43613@x" ObjectIDZND0="43612@0" Pin0InfoVect0LinkObjId="SW-269722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-283807_0" Pin1InfoVect1LinkObjId="SW-269723_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1125 138,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34be410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="156,-1213 138,-1213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43614@1" ObjectIDZND0="43612@x" ObjectIDZND1="g_34b5480@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-269722_0" Pin0InfoVect1LinkObjId="g_34b5480_0" Pin0InfoVect2LinkObjId="g_35436e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="156,-1213 138,-1213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34be670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1191 138,-1213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43612@1" ObjectIDZND0="43614@x" ObjectIDZND1="g_34b5480@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-269724_0" Pin0InfoVect1LinkObjId="g_34b5480_0" Pin0InfoVect2LinkObjId="g_35436e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-269722_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1191 138,-1213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34be8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="109,-1232 138,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_34b5480@0" ObjectIDZND0="43612@x" ObjectIDZND1="43614@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-269722_0" Pin0InfoVect1LinkObjId="SW-269724_0" Pin0InfoVect2LinkObjId="g_35436e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b5480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="109,-1232 138,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34beb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1213 138,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43612@x" ObjectIDND1="43614@x" ObjectIDZND0="g_34b5480@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_34b5480_0" Pin0InfoVect1LinkObjId="g_35436e0_0" Pin0InfoVect2LinkObjId="g_35436e0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-269722_0" Pin1InfoVect1LinkObjId="SW-269724_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1213 138,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34bff00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1232 138,-1252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43612@x" ObjectIDND1="43614@x" ObjectIDND2="g_34b5480@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_35436e0_0" Pin0InfoVect1LinkObjId="g_35436e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-269722_0" Pin1InfoVect1LinkObjId="SW-269724_0" Pin1InfoVect2LinkObjId="g_34b5480_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1232 138,-1252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c00f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1252 60,-1252 60,-1198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43612@x" ObjectIDND1="43614@x" ObjectIDND2="g_34b5480@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_35436e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-269722_0" Pin1InfoVect1LinkObjId="SW-269724_0" Pin1InfoVect2LinkObjId="g_34b5480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1252 60,-1252 60,-1198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c24f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="155,-914 155,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26184@0" ObjectIDZND0="26196@1" Pin0InfoVect0LinkObjId="SW-155115_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32be350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="155,-914 155,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c2770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="155,-863 155,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26196@0" ObjectIDZND0="26197@x" ObjectIDZND1="26195@x" Pin0InfoVect0LinkObjId="SW-155116_0" Pin0InfoVect1LinkObjId="SW-155114_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155115_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="155,-863 155,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c37c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1061 138,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43611@0" ObjectIDZND0="26189@x" ObjectIDZND1="26188@x" Pin0InfoVect0LinkObjId="SW-155014_0" Pin0InfoVect1LinkObjId="SW-155013_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-283807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1061 138,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c3a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1029 155,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43611@x" ObjectIDND1="26188@x" ObjectIDZND0="26189@1" Pin0InfoVect0LinkObjId="SW-155014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-283807_0" Pin1InfoVect1LinkObjId="SW-155013_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1029 155,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c4230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1029 138,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="43611@x" ObjectIDND1="26189@x" ObjectIDZND0="26188@1" Pin0InfoVect0LinkObjId="SW-155013_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-283807_0" Pin1InfoVect1LinkObjId="SW-155014_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1029 138,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34c4490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-968 138,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26188@0" ObjectIDZND0="26184@0" Pin0InfoVect0LinkObjId="g_32be350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155013_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="138,-968 138,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30b9a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1292 138,-1355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="43612@x" ObjectIDND2="43614@x" ObjectIDZND0="38075@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35436e0_0" Pin1InfoVect1LinkObjId="SW-269722_0" Pin1InfoVect2LinkObjId="SW-269724_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1292 138,-1355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eade60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="209,-1305 209,-1292 138,-1292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43612@x" ObjectIDZND1="43614@x" ObjectIDZND2="g_34b5480@0" Pin0InfoVect0LinkObjId="SW-269722_0" Pin0InfoVect1LinkObjId="SW-269724_0" Pin0InfoVect2LinkObjId="g_34b5480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35436e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="209,-1305 209,-1292 138,-1292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ba120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="138,-1292 138,-1252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="38075@1" ObjectIDZND0="43612@x" ObjectIDZND1="43614@x" ObjectIDZND2="g_34b5480@0" Pin0InfoVect0LinkObjId="SW-269722_0" Pin0InfoVect1LinkObjId="SW-269724_0" Pin0InfoVect2LinkObjId="g_34b5480_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35436e0_0" Pin1InfoVect1LinkObjId="g_30b9a90_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="138,-1292 138,-1252 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_LJ"/>
</svg>