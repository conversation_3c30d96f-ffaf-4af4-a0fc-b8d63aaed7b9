<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-132" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="27 -1266 1940 1290">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_1d01860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="21" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="44" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="50" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="59" x2="59" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="62" x2="62" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="65" x2="65" y1="6" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="voltageTransformer:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="35" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="18" x2="29" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.22074" x1="18" x2="29" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="41" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="34" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="21" x2="25" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="20" x2="27" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="18" x2="29" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="41" x2="41" y1="40" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.239135" x1="34" x2="34" y1="40" y2="51"/>
    <circle cx="24" cy="26" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="24" cy="10" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="10" cy="18" fillStyle="0" r="9" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198e740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_198fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19902c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19912d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1991ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1992a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19934c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1993d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_19975e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19991b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1999da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_199ab60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199c520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199d410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19a0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a0e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a4100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19a4d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19b3590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19b3dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a8560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1300" width="1950" x="22" y="-1271"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d10190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 930.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_187d110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 945.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c91f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 960.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_173e6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.000000 915.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1533f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 386.000000 900.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cf40e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 308.000000 48.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5d9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 33.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1556e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 18.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d8c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1722.000000 504.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cffc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1722.000000 519.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18a5350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1722.000000 534.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f5a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1714.000000 489.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcd090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 474.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a53fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 504.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efcd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 519.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16cf520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 534.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f9c7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 282.000000 489.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1738d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 298.000000 474.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7a880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1255.000000 823.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cf8540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1244.000000 808.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190d420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1269.000000 793.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1209890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1225.000000 551.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b33800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.000000 536.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2213760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.000000 521.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa3120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 776.000000 554.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d71c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 539.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f6cd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 524.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca5590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 804.000000 820.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_174b0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 805.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c8e0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.000000 790.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae2360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 678.000000 1004.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 667.000000 989.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0f9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 974.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1738,-142 1729,-130 1720,-142 1738,-142 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="28" y="-1230"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(255,255,255)" stroke-width="1" width="12" x="645" y="-667"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(255,255,255)" stroke-width="1" width="12" x="1091" y="-667"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-103331">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 612.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20526" ObjectName="SW-CX_HTP.CX_HTP_2311SW"/>
     <cge:Meas_Ref ObjectId="103331"/>
    <cge:TPSR_Ref TObjectID="20526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103332">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 612.000000 -1022.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20527" ObjectName="SW-CX_HTP.CX_HTP_2316SW"/>
     <cge:Meas_Ref ObjectId="103332"/>
    <cge:TPSR_Ref TObjectID="20527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103334">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 570.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20529" ObjectName="SW-CX_HTP.CX_HTP_23160SW"/>
     <cge:Meas_Ref ObjectId="103334"/>
    <cge:TPSR_Ref TObjectID="20529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103335">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.000000 -1072.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20530" ObjectName="SW-CX_HTP.CX_HTP_23167SW"/>
     <cge:Meas_Ref ObjectId="103335"/>
    <cge:TPSR_Ref TObjectID="20530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103354">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 901.000000 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20549" ObjectName="SW-CX_HTP.CX_HTP_2901SW"/>
     <cge:Meas_Ref ObjectId="103354"/>
    <cge:TPSR_Ref TObjectID="20549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103355">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 -1003.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20550" ObjectName="SW-CX_HTP.CX_HTP_29017SW"/>
     <cge:Meas_Ref ObjectId="103355"/>
    <cge:TPSR_Ref TObjectID="20550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103343">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.565184 -827.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20538" ObjectName="SW-CX_HTP.CX_HTP_2011SW"/>
     <cge:Meas_Ref ObjectId="103343"/>
    <cge:TPSR_Ref TObjectID="20538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103344">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.565184 -723.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20539" ObjectName="SW-CX_HTP.CX_HTP_2016SW"/>
     <cge:Meas_Ref ObjectId="103344"/>
    <cge:TPSR_Ref TObjectID="20539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103345">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.565184 -818.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20540" ObjectName="SW-CX_HTP.CX_HTP_20117SW"/>
     <cge:Meas_Ref ObjectId="103345"/>
    <cge:TPSR_Ref TObjectID="20540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103346">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.565184 -770.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20541" ObjectName="SW-CX_HTP.CX_HTP_20160SW"/>
     <cge:Meas_Ref ObjectId="103346"/>
    <cge:TPSR_Ref TObjectID="20541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103347">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.565184 -712.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20542" ObjectName="SW-CX_HTP.CX_HTP_20167SW"/>
     <cge:Meas_Ref ObjectId="103347"/>
    <cge:TPSR_Ref TObjectID="20542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103349">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 -828.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20544" ObjectName="SW-CX_HTP.CX_HTP_2021SW"/>
     <cge:Meas_Ref ObjectId="103349"/>
    <cge:TPSR_Ref TObjectID="20544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103350">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 -724.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20545" ObjectName="SW-CX_HTP.CX_HTP_2026SW"/>
     <cge:Meas_Ref ObjectId="103350"/>
    <cge:TPSR_Ref TObjectID="20545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103351">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 -819.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20546" ObjectName="SW-CX_HTP.CX_HTP_20217SW"/>
     <cge:Meas_Ref ObjectId="103351"/>
    <cge:TPSR_Ref TObjectID="20546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103352">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 -771.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20547" ObjectName="SW-CX_HTP.CX_HTP_20260SW"/>
     <cge:Meas_Ref ObjectId="103352"/>
    <cge:TPSR_Ref TObjectID="20547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103353">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 -713.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20548" ObjectName="SW-CX_HTP.CX_HTP_20267SW"/>
     <cge:Meas_Ref ObjectId="103353"/>
    <cge:TPSR_Ref TObjectID="20548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103362">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 395.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20557" ObjectName="SW-CX_HTP.CX_HTP_33167SW"/>
     <cge:Meas_Ref ObjectId="103362"/>
    <cge:TPSR_Ref TObjectID="20557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103337">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20532" ObjectName="SW-CX_HTP.CX_HTP_2321SW"/>
     <cge:Meas_Ref ObjectId="103337"/>
    <cge:TPSR_Ref TObjectID="20532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103338">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.000000 -1025.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20533" ObjectName="SW-CX_HTP.CX_HTP_2326SW"/>
     <cge:Meas_Ref ObjectId="103338"/>
    <cge:TPSR_Ref TObjectID="20533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103339">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1074.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20534" ObjectName="SW-CX_HTP.CX_HTP_23217SW"/>
     <cge:Meas_Ref ObjectId="103339"/>
    <cge:TPSR_Ref TObjectID="20534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103340">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1074.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20535" ObjectName="SW-CX_HTP.CX_HTP_23260SW"/>
     <cge:Meas_Ref ObjectId="103340"/>
    <cge:TPSR_Ref TObjectID="20535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103341">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.000000 -1072.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20536" ObjectName="SW-CX_HTP.CX_HTP_23267SW"/>
     <cge:Meas_Ref ObjectId="103341"/>
    <cge:TPSR_Ref TObjectID="20536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103356">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 -913.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20551" ObjectName="SW-CX_HTP.CX_HTP_29010SW"/>
     <cge:Meas_Ref ObjectId="103356"/>
    <cge:TPSR_Ref TObjectID="20551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103333">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 570.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20528" ObjectName="SW-CX_HTP.CX_HTP_23117SW"/>
     <cge:Meas_Ref ObjectId="103333"/>
    <cge:TPSR_Ref TObjectID="20528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103358">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20553" ObjectName="SW-CX_HTP.CX_HTP_2010SW"/>
     <cge:Meas_Ref ObjectId="103358"/>
    <cge:TPSR_Ref TObjectID="20553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103360">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 -633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20555" ObjectName="SW-CX_HTP.CX_HTP_2020SW"/>
     <cge:Meas_Ref ObjectId="103360"/>
    <cge:TPSR_Ref TObjectID="20555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104513">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 430.000000 -474.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20590" ObjectName="SW-CX_HTP.CX_HTP_3901XC"/>
     <cge:Meas_Ref ObjectId="104513"/>
    <cge:TPSR_Ref TObjectID="20590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104514">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1562.000000 -474.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20591" ObjectName="SW-CX_HTP.CX_HTP_3902XC"/>
     <cge:Meas_Ref ObjectId="104514"/>
    <cge:TPSR_Ref TObjectID="20591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103364">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 506.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20559" ObjectName="SW-CX_HTP.CX_HTP_33267SW"/>
     <cge:Meas_Ref ObjectId="103364"/>
    <cge:TPSR_Ref TObjectID="20559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103366">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 601.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20561" ObjectName="SW-CX_HTP.CX_HTP_33367SW"/>
     <cge:Meas_Ref ObjectId="103366"/>
    <cge:TPSR_Ref TObjectID="20561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103368">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20563" ObjectName="SW-CX_HTP.CX_HTP_33567SW"/>
     <cge:Meas_Ref ObjectId="103368"/>
    <cge:TPSR_Ref TObjectID="20563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 802.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20584" ObjectName="SW-CX_HTP.CX_HTP_33667SW"/>
     <cge:Meas_Ref ObjectId="104044"/>
    <cge:TPSR_Ref TObjectID="20584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103371">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 912.477768 -232.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20566" ObjectName="SW-CX_HTP.CX_HTP_33767SW"/>
     <cge:Meas_Ref ObjectId="103371"/>
    <cge:TPSR_Ref TObjectID="20566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103373">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1136.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20568" ObjectName="SW-CX_HTP.CX_HTP_34167SW"/>
     <cge:Meas_Ref ObjectId="103373"/>
    <cge:TPSR_Ref TObjectID="20568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103375">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1244.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20570" ObjectName="SW-CX_HTP.CX_HTP_34267SW"/>
     <cge:Meas_Ref ObjectId="103375"/>
    <cge:TPSR_Ref TObjectID="20570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103377">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1352.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20572" ObjectName="SW-CX_HTP.CX_HTP_34367SW"/>
     <cge:Meas_Ref ObjectId="103377"/>
    <cge:TPSR_Ref TObjectID="20572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103379">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1449.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20574" ObjectName="SW-CX_HTP.CX_HTP_34467SW"/>
     <cge:Meas_Ref ObjectId="103379"/>
    <cge:TPSR_Ref TObjectID="20574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1552.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20576" ObjectName="SW-CX_HTP.CX_HTP_34567SW"/>
     <cge:Meas_Ref ObjectId="103381"/>
    <cge:TPSR_Ref TObjectID="20576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103383">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20578" ObjectName="SW-CX_HTP.CX_HTP_34667SW"/>
     <cge:Meas_Ref ObjectId="103383"/>
    <cge:TPSR_Ref TObjectID="20578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1749.477768 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20580" ObjectName="SW-CX_HTP.CX_HTP_34767SW"/>
     <cge:Meas_Ref ObjectId="103385"/>
    <cge:TPSR_Ref TObjectID="20580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 953.000000 -349.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20592" ObjectName="SW-CX_HTP.CX_HTP_3121XC"/>
     <cge:Meas_Ref ObjectId="104515"/>
    <cge:TPSR_Ref TObjectID="20592"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTP.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 481.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43371" ObjectName="SM-CX_HTP.P1"/>
    <cge:TPSR_Ref TObjectID="43371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTP.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 576.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43372" ObjectName="SM-CX_HTP.P2"/>
    <cge:TPSR_Ref TObjectID="43372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTP.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 671.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43373" ObjectName="SM-CX_HTP.P3"/>
    <cge:TPSR_Ref TObjectID="43373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTP.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43374" ObjectName="SM-CX_HTP.P4"/>
    <cge:TPSR_Ref TObjectID="43374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTP.P5">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43375" ObjectName="SM-CX_HTP.P5"/>
    <cge:TPSR_Ref TObjectID="43375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTP.P6">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1327.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43376" ObjectName="SM-CX_HTP.P6"/>
    <cge:TPSR_Ref TObjectID="43376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTP.P7">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1424.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43377" ObjectName="SM-CX_HTP.P7"/>
    <cge:TPSR_Ref TObjectID="43377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTP.P8">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.000000 -85.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43378" ObjectName="SM-CX_HTP.P8"/>
    <cge:TPSR_Ref TObjectID="43378"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_ZX" endPointId="0" endStationName="CX_HTP" flowDrawDirect="1" flowShape="0" id="AC-220kV.hongzi_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="621,-1232 621,-1199 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29091" ObjectName="AC-220kV.hongzi_line"/>
    <cge:TPSR_Ref TObjectID="29091_SS-132"/></metadata>
   <polyline fill="none" opacity="0" points="621,-1232 621,-1199 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HTP" endPointId="0" endStationName="CX_WJ" flowDrawDirect="1" flowShape="0" id="AC-220kV.hongwu_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1125,-1192 1125,-1227 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34025" ObjectName="AC-220kV.hongwu_line"/>
    <cge:TPSR_Ref TObjectID="34025_SS-132"/></metadata>
   <polyline fill="none" opacity="0" points="1125,-1192 1125,-1227 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_220bcf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 -1071.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f441f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 -1006.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_fc3ef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 548.000000 -943.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18827f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 -912.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fef680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 -1002.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f6f90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.565184 -817.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d0da40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.565184 -769.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11e57f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.565184 -711.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d80da0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -818.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cf76f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -770.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a3f00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -712.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1897770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 398.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a42e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1053.000000 -1071.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1870570" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1053.000000 -1006.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1935130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.000000 -943.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d94630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -608.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_fd2680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -608.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19359b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 509.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1574620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a998c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_fc5e70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 805.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_156ed30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 915.477768 -209.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b37100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2115960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_226b3f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1355.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2176d20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1452.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2489710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1555.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21747e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1646.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a01a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1752.477768 -254.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_18c58f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-949 566,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20528@0" ObjectIDZND0="g_fc3ef0@0" Pin0InfoVect0LinkObjId="g_fc3ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,-949 566,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a7b2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-1012 567,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20529@0" ObjectIDZND0="g_f441f0@0" Pin0InfoVect0LinkObjId="g_f441f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,-1012 567,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1925a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-1077 611,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20527@x" ObjectIDND1="g_14ec300@0" ObjectIDND2="g_ec8d10@0" ObjectIDZND0="20530@1" Pin0InfoVect0LinkObjId="SW-103335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103332_0" Pin1InfoVect1LinkObjId="g_14ec300_0" Pin1InfoVect2LinkObjId="g_ec8d10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="621,-1077 611,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a5f5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-1077 567,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20530@0" ObjectIDZND0="g_220bcf0@0" Pin0InfoVect0LinkObjId="g_220bcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,-1077 567,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ad0040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-1077 621,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20530@x" ObjectIDND1="g_14ec300@0" ObjectIDND2="g_ec8d10@0" ObjectIDZND0="20527@1" Pin0InfoVect0LinkObjId="SW-103332_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103335_0" Pin1InfoVect1LinkObjId="g_14ec300_0" Pin1InfoVect2LinkObjId="g_ec8d10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="621,-1077 621,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a82ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-1012 611,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20527@x" ObjectIDND1="20525@x" ObjectIDZND0="20529@1" Pin0InfoVect0LinkObjId="SW-103334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103332_0" Pin1InfoVect1LinkObjId="SW-103330_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="621,-1012 611,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1cadb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="611,-1163 621,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_14ec300@0" ObjectIDZND0="20527@x" ObjectIDZND1="20530@x" ObjectIDZND2="g_ec8d10@0" Pin0InfoVect0LinkObjId="SW-103332_0" Pin0InfoVect1LinkObjId="SW-103335_0" Pin0InfoVect2LinkObjId="g_ec8d10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14ec300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="611,-1163 621,-1163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_f42300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-1163 621,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_14ec300@0" ObjectIDND1="g_ec8d10@0" ObjectIDND2="29091@1" ObjectIDZND0="20527@x" ObjectIDZND1="20530@x" Pin0InfoVect0LinkObjId="SW-103332_0" Pin0InfoVect1LinkObjId="SW-103335_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14ec300_0" Pin1InfoVect1LinkObjId="g_ec8d10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="621,-1163 621,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_150d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-918 910,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="20549@x" ObjectIDND1="20551@x" ObjectIDZND0="20521@0" Pin0InfoVect0LinkObjId="g_1c97cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103354_0" Pin1InfoVect1LinkObjId="SW-103356_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="910,-918 910,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1c97cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-946 910,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="20549@0" ObjectIDZND0="20521@0" ObjectIDZND1="20551@x" Pin0InfoVect0LinkObjId="g_150d230_0" Pin0InfoVect1LinkObjId="SW-103356_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="910,-946 910,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_b8ae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="862,-1008 854,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20550@0" ObjectIDZND0="g_1fef680@0" Pin0InfoVect0LinkObjId="g_1fef680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="862,-1008 854,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ac2550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-879 719,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20521@0" ObjectIDZND0="20538@1" Pin0InfoVect0LinkObjId="SW-103343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_150d230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-879 719,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fed2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-823 767,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_14f6f90@0" ObjectIDZND0="20540@1" Pin0InfoVect0LinkObjId="SW-103345_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f6f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-823 767,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a82c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-775 767,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d0da40@0" ObjectIDZND0="20541@1" Pin0InfoVect0LinkObjId="SW-103346_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d0da40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-775 767,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21796d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-775 719,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20541@0" ObjectIDZND0="20537@x" ObjectIDZND1="20539@x" Pin0InfoVect0LinkObjId="SW-103342_0" Pin0InfoVect1LinkObjId="SW-103344_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103346_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="731,-775 719,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1cf9cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-717 767,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11e57f0@0" ObjectIDZND0="20542@1" Pin0InfoVect0LinkObjId="SW-103347_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11e57f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-717 767,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_14ddf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-832 719,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20538@0" ObjectIDZND0="20537@x" ObjectIDZND1="20540@x" Pin0InfoVect0LinkObjId="SW-103342_0" Pin0InfoVect1LinkObjId="SW-103345_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-832 719,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fdc890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-823 731,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20537@x" ObjectIDND1="20538@x" ObjectIDZND0="20540@0" Pin0InfoVect0LinkObjId="SW-103345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103342_0" Pin1InfoVect1LinkObjId="SW-103343_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-823 731,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1c59730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-823 719,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20540@x" ObjectIDND1="20538@x" ObjectIDZND0="20537@1" Pin0InfoVect0LinkObjId="SW-103342_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103345_0" Pin1InfoVect1LinkObjId="SW-103343_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-823 719,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2029c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-785 719,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20537@0" ObjectIDZND0="20541@x" ObjectIDZND1="20539@x" Pin0InfoVect0LinkObjId="SW-103346_0" Pin0InfoVect1LinkObjId="SW-103344_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-785 719,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_196cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-764 719,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20539@1" ObjectIDZND0="20537@x" ObjectIDZND1="20541@x" Pin0InfoVect0LinkObjId="SW-103342_0" Pin0InfoVect1LinkObjId="SW-103346_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-764 719,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_156cea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-879 1165,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20521@0" ObjectIDZND0="20544@1" Pin0InfoVect0LinkObjId="SW-103349_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_150d230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-879 1165,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_102cc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-824 1213,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d80da0@0" ObjectIDZND0="20546@1" Pin0InfoVect0LinkObjId="SW-103351_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d80da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-824 1213,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_19256e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-776 1213,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1cf76f0@0" ObjectIDZND0="20547@1" Pin0InfoVect0LinkObjId="SW-103352_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cf76f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-776 1213,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1cbb2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1177,-776 1165,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20547@0" ObjectIDZND0="20543@x" ObjectIDZND1="20545@x" Pin0InfoVect0LinkObjId="SW-103348_0" Pin0InfoVect1LinkObjId="SW-103350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1177,-776 1165,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_f1d5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-718 1213,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20a3f00@0" ObjectIDZND0="20548@1" Pin0InfoVect0LinkObjId="SW-103353_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a3f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-718 1213,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_18a7260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1177,-718 1165,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="20548@0" ObjectIDZND0="20545@x" ObjectIDZND1="20583@x" Pin0InfoVect0LinkObjId="SW-103350_0" Pin0InfoVect1LinkObjId="g_1a7e9b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103353_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1177,-718 1165,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_16bc820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-833 1165,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20544@0" ObjectIDZND0="20546@x" ObjectIDZND1="20543@x" Pin0InfoVect0LinkObjId="SW-103351_0" Pin0InfoVect1LinkObjId="SW-103348_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-833 1165,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ff1510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-824 1177,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20544@x" ObjectIDND1="20543@x" ObjectIDZND0="20546@0" Pin0InfoVect0LinkObjId="SW-103351_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103349_0" Pin1InfoVect1LinkObjId="SW-103348_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-824 1177,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_108d470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-824 1165,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20544@x" ObjectIDND1="20546@x" ObjectIDZND0="20543@1" Pin0InfoVect0LinkObjId="SW-103348_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103349_0" Pin1InfoVect1LinkObjId="SW-103351_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-824 1165,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1d2ab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-786 1165,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20543@0" ObjectIDZND0="20547@x" ObjectIDZND1="20545@x" Pin0InfoVect0LinkObjId="SW-103352_0" Pin0InfoVect1LinkObjId="SW-103350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103348_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-786 1165,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_f40320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-729 1165,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="20545@0" ObjectIDZND0="20548@x" ObjectIDZND1="20583@x" Pin0InfoVect0LinkObjId="SW-103353_0" Pin0InfoVect1LinkObjId="g_1a7e9b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-729 1165,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ff24a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-765 1165,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20545@1" ObjectIDZND0="20543@x" ObjectIDZND1="20547@x" Pin0InfoVect0LinkObjId="SW-103348_0" Pin0InfoVect1LinkObjId="SW-103352_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-765 1165,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_200ba90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-898 621,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20526@0" ObjectIDZND0="20521@0" Pin0InfoVect0LinkObjId="g_150d230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="621,-898 621,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1920250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="611,-949 621,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20528@1" ObjectIDZND0="20525@x" ObjectIDZND1="20526@x" Pin0InfoVect0LinkObjId="SW-103330_0" Pin0InfoVect1LinkObjId="SW-103331_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103333_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="611,-949 621,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2211bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-970 621,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20525@0" ObjectIDZND0="20526@x" ObjectIDZND1="20528@x" Pin0InfoVect0LinkObjId="SW-103331_0" Pin0InfoVect1LinkObjId="SW-103333_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="621,-970 621,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_16cee80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-949 621,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20525@x" ObjectIDND1="20528@x" ObjectIDZND0="20526@1" Pin0InfoVect0LinkObjId="SW-103331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103330_0" Pin1InfoVect1LinkObjId="SW-103333_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="621,-949 621,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_18b6e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-1030 621,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20527@0" ObjectIDZND0="20529@x" ObjectIDZND1="20525@x" Pin0InfoVect0LinkObjId="SW-103334_0" Pin0InfoVect1LinkObjId="SW-103330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="621,-1030 621,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1057720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-1012 621,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20529@x" ObjectIDND1="20527@x" ObjectIDZND0="20525@1" Pin0InfoVect0LinkObjId="SW-103330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103334_0" Pin1InfoVect1LinkObjId="SW-103332_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="621,-1012 621,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_17465d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-1008 910,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="20550@1" ObjectIDZND0="20549@x" ObjectIDZND1="g_1909a90@0" ObjectIDZND2="g_1d01d90@0" Pin0InfoVect0LinkObjId="SW-103354_0" Pin0InfoVect1LinkObjId="g_1909a90_0" Pin0InfoVect2LinkObjId="g_1d01d90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103355_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="898,-1008 910,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_eb6a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-982 910,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="20549@1" ObjectIDZND0="20550@x" ObjectIDZND1="g_1909a90@0" ObjectIDZND2="g_1d01d90@0" Pin0InfoVect0LinkObjId="SW-103355_0" Pin0InfoVect1LinkObjId="g_1909a90_0" Pin0InfoVect2LinkObjId="g_1d01d90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103354_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="910,-982 910,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_19833a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-1008 910,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="20549@x" ObjectIDND1="20550@x" ObjectIDND2="g_1909a90@0" ObjectIDZND0="g_1d01d90@0" Pin0InfoVect0LinkObjId="g_1d01d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103354_0" Pin1InfoVect1LinkObjId="SW-103355_0" Pin1InfoVect2LinkObjId="g_1909a90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="910,-1008 910,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1737270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-1008 945,-1008 945,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="20549@x" ObjectIDND1="20550@x" ObjectIDND2="g_1d01d90@0" ObjectIDZND0="g_1909a90@0" Pin0InfoVect0LinkObjId="g_1909a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103354_0" Pin1InfoVect1LinkObjId="SW-103355_0" Pin1InfoVect2LinkObjId="g_1d01d90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="910,-1008 945,-1008 945,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22322c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="404,-282 404,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20557@0" ObjectIDZND0="g_1897770@0" Pin0InfoVect0LinkObjId="g_1897770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103362_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="404,-282 404,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8ab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="404,-318 404,-327 375,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20557@1" ObjectIDZND0="20556@x" ObjectIDZND1="g_185f5e0@0" ObjectIDZND2="g_220f3b0@0" Pin0InfoVect0LinkObjId="SW-103361_0" Pin0InfoVect1LinkObjId="g_185f5e0_0" Pin0InfoVect2LinkObjId="g_220f3b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="404,-318 404,-327 375,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19032b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-576 440,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1a86110@0" ObjectIDZND0="g_1d72fa0@0" Pin0InfoVect0LinkObjId="g_1d72fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a86110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-576 440,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f1ff00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="412,-525 440,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_21a3590@0" ObjectIDZND0="g_1a86110@0" ObjectIDZND1="20590@x" Pin0InfoVect0LinkObjId="g_1a86110_0" Pin0InfoVect1LinkObjId="SW-104513_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a3590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="412,-525 440,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18bae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-525 440,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_21a3590@0" ObjectIDND1="20590@x" ObjectIDZND0="g_1a86110@1" Pin0InfoVect0LinkObjId="g_1a86110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21a3590_0" Pin1InfoVect1LinkObjId="SW-104513_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-525 440,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0fe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1572,-577 1572,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_14f20a0@0" ObjectIDZND0="g_1952ff0@0" Pin0InfoVect0LinkObjId="g_1952ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f20a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1572,-577 1572,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1544,-526 1572,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_18d2b00@0" ObjectIDZND0="g_14f20a0@0" ObjectIDZND1="20591@x" Pin0InfoVect0LinkObjId="g_14f20a0_0" Pin0InfoVect1LinkObjId="SW-104514_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18d2b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1544,-526 1572,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1500110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1572,-526 1572,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_18d2b00@0" ObjectIDND1="20591@x" ObjectIDZND0="g_14f20a0@1" Pin0InfoVect0LinkObjId="g_14f20a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18d2b00_0" Pin1InfoVect1LinkObjId="SW-104514_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1572,-526 1572,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2417700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-728 719,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="20539@0" ObjectIDZND0="20542@x" ObjectIDZND1="20582@x" Pin0InfoVect0LinkObjId="SW-103347_0" Pin0InfoVect1LinkObjId="g_194e840_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103344_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-728 719,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1bcf490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-717 731,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="20539@x" ObjectIDND1="20582@x" ObjectIDZND0="20542@0" Pin0InfoVect0LinkObjId="SW-103347_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103344_0" Pin1InfoVect1LinkObjId="g_194e840_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-717 731,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21a9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-949 1070,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20534@0" ObjectIDZND0="g_1935130@0" Pin0InfoVect0LinkObjId="g_1935130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103339_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-949 1070,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_ef9010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-1012 1071,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20535@0" ObjectIDZND0="g_1870570@0" Pin0InfoVect0LinkObjId="g_1870570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-1012 1071,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_19ee460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-1077 1115,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20533@x" ObjectIDND1="g_152e190@0" ObjectIDND2="g_1d03910@0" ObjectIDZND0="20536@1" Pin0InfoVect0LinkObjId="SW-103341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103338_0" Pin1InfoVect1LinkObjId="g_152e190_0" Pin1InfoVect2LinkObjId="g_1d03910_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-1077 1115,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1cea280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-1077 1071,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20536@0" ObjectIDZND0="g_21a42e0@0" Pin0InfoVect0LinkObjId="g_21a42e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-1077 1071,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ae2f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-1077 1125,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20536@x" ObjectIDND1="g_152e190@0" ObjectIDND2="g_1d03910@0" ObjectIDZND0="20533@1" Pin0InfoVect0LinkObjId="SW-103338_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103341_0" Pin1InfoVect1LinkObjId="g_152e190_0" Pin1InfoVect2LinkObjId="g_1d03910_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-1077 1125,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_f6f4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-1012 1115,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20533@x" ObjectIDND1="20531@x" ObjectIDZND0="20535@1" Pin0InfoVect0LinkObjId="SW-103340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103338_0" Pin1InfoVect1LinkObjId="SW-103336_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-1012 1115,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fa3500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-1163 1125,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_152e190@0" ObjectIDZND0="20533@x" ObjectIDZND1="20536@x" ObjectIDZND2="g_1d03910@0" Pin0InfoVect0LinkObjId="SW-103338_0" Pin0InfoVect1LinkObjId="SW-103341_0" Pin0InfoVect2LinkObjId="g_1d03910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_152e190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-1163 1125,-1163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1d19a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1155,-1170 1155,-1181 1125,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1d03910@0" ObjectIDZND0="20533@x" ObjectIDZND1="20536@x" ObjectIDZND2="g_152e190@0" Pin0InfoVect0LinkObjId="SW-103338_0" Pin0InfoVect1LinkObjId="SW-103341_0" Pin0InfoVect2LinkObjId="g_152e190_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d03910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1155,-1170 1155,-1181 1125,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_11fa210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-1163 1125,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_152e190@0" ObjectIDND1="g_1d03910@0" ObjectIDND2="34025@1" ObjectIDZND0="20533@x" ObjectIDZND1="20536@x" Pin0InfoVect0LinkObjId="SW-103338_0" Pin0InfoVect1LinkObjId="SW-103341_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_152e190_0" Pin1InfoVect1LinkObjId="g_1d03910_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-1163 1125,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_169b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-949 1125,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20534@1" ObjectIDZND0="20531@x" ObjectIDZND1="20532@x" Pin0InfoVect0LinkObjId="SW-103336_0" Pin0InfoVect1LinkObjId="SW-103337_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103339_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-949 1125,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fe9560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-970 1125,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20531@0" ObjectIDZND0="20534@x" ObjectIDZND1="20532@x" Pin0InfoVect0LinkObjId="SW-103339_0" Pin0InfoVect1LinkObjId="SW-103337_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-970 1125,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_18b7cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-949 1125,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20531@x" ObjectIDND1="20534@x" ObjectIDZND0="20532@1" Pin0InfoVect0LinkObjId="SW-103337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103336_0" Pin1InfoVect1LinkObjId="SW-103339_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-949 1125,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_150f730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-1030 1125,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20533@0" ObjectIDZND0="20535@x" ObjectIDZND1="20531@x" Pin0InfoVect0LinkObjId="SW-103340_0" Pin0InfoVect1LinkObjId="SW-103336_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-1030 1125,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2295d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-1012 1125,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20533@x" ObjectIDND1="20535@x" ObjectIDZND0="20531@1" Pin0InfoVect0LinkObjId="SW-103336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103338_0" Pin1InfoVect1LinkObjId="SW-103340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-1012 1125,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_157d790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-1163 1125,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="20533@x" ObjectIDND1="20536@x" ObjectIDND2="g_152e190@0" ObjectIDZND0="g_1d03910@0" ObjectIDZND1="34025@1" Pin0InfoVect0LinkObjId="g_1d03910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103338_0" Pin1InfoVect1LinkObjId="SW-103341_0" Pin1InfoVect2LinkObjId="g_152e190_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-1163 1125,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1c5a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-1195 1125,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="34025@1" ObjectIDZND0="20533@x" ObjectIDZND1="20536@x" ObjectIDZND2="g_152e190@0" Pin0InfoVect0LinkObjId="SW-103338_0" Pin0InfoVect1LinkObjId="SW-103341_0" Pin0InfoVect2LinkObjId="g_152e190_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-1195 1125,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_152f860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-898 1125,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20532@0" ObjectIDZND0="20521@0" Pin0InfoVect0LinkObjId="g_150d230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-898 1125,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a62d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-918 899,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="20549@x" ObjectIDND1="20521@0" ObjectIDZND0="20551@0" Pin0InfoVect0LinkObjId="SW-103356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103354_0" Pin1InfoVect1LinkObjId="g_150d230_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="910,-918 899,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1592500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-918 854,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20551@1" ObjectIDZND0="g_18827f0@0" Pin0InfoVect0LinkObjId="g_18827f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-918 854,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_194e840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-674 631,-681 719,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="20553@1" ObjectIDZND0="20582@x" Pin0InfoVect0LinkObjId="g_fef240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103358_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-674 631,-681 719,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1aa0830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-626 631,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d94630@0" ObjectIDZND0="20553@0" Pin0InfoVect0LinkObjId="SW-103358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d94630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-626 631,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_189e8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1077,-626 1077,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_fd2680@0" ObjectIDZND0="20555@0" Pin0InfoVect0LinkObjId="SW-103360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_fd2680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1077,-626 1077,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a7e9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1077,-674 1077,-681 1165,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="20555@1" ObjectIDZND0="20583@x" Pin0InfoVect0LinkObjId="g_1512110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1077,-674 1077,-681 1165,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_fef240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-716 719,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="20542@x" ObjectIDND1="20539@x" ObjectIDZND0="20582@0" Pin0InfoVect0LinkObjId="g_194e840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103347_0" Pin1InfoVect1LinkObjId="SW-103344_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-716 719,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1512110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-718 1165,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="20545@x" ObjectIDND1="20548@x" ObjectIDZND0="20583@0" Pin0InfoVect0LinkObjId="g_1a7e9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-103350_0" Pin1InfoVect1LinkObjId="SW-103353_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-718 1165,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ac9e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="688,-584 688,-595 719,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="g_188a5a0@0" ObjectIDZND0="20582@x" ObjectIDZND1="20552@x" Pin0InfoVect0LinkObjId="g_194e840_0" Pin0InfoVect1LinkObjId="SW-103357_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_188a5a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="688,-584 688,-595 719,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f6d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,-584 1134,-595 1165,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="g_f6c520@0" ObjectIDZND0="20583@x" ObjectIDZND1="20554@x" Pin0InfoVect0LinkObjId="g_1a7e9b0_0" Pin0InfoVect1LinkObjId="SW-103359_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f6c520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1134,-584 1134,-595 1165,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2226ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-481 719,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20552@1" ObjectIDZND0="20522@0" Pin0InfoVect0LinkObjId="g_1212ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103357_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-481 719,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f14a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-481 1165,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20554@1" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_1c04530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103359_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-481 1165,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a3410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-620 719,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="20582@1" ObjectIDZND0="g_188a5a0@0" ObjectIDZND1="20552@x" Pin0InfoVect0LinkObjId="g_188a5a0_0" Pin0InfoVect1LinkObjId="SW-103357_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_194e840_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-620 719,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16c6790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-573 719,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="20552@0" ObjectIDZND0="g_188a5a0@0" ObjectIDZND1="20582@x" Pin0InfoVect0LinkObjId="g_188a5a0_0" Pin0InfoVect1LinkObjId="g_194e840_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103357_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-573 719,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f7320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-620 1165,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="20583@1" ObjectIDZND0="g_f6c520@0" ObjectIDZND1="20554@x" Pin0InfoVect0LinkObjId="g_f6c520_0" Pin0InfoVect1LinkObjId="SW-103359_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a7e9b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-620 1165,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f2680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-595 1165,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="g_f6c520@0" ObjectIDND1="20583@x" ObjectIDZND0="20554@0" Pin0InfoVect0LinkObjId="SW-103359_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_f6c520_0" Pin1InfoVect1LinkObjId="g_1a7e9b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-595 1165,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1906c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-525 440,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_21a3590@0" ObjectIDND1="g_1a86110@0" ObjectIDZND0="20590@1" Pin0InfoVect0LinkObjId="SW-104513_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21a3590_0" Pin1InfoVect1LinkObjId="g_1a86110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-525 440,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1212ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-481 440,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20590@0" ObjectIDZND0="20522@0" Pin0InfoVect0LinkObjId="g_2226ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104513_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-481 440,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d65bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1572,-526 1572,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_18d2b00@0" ObjectIDND1="g_14f20a0@0" ObjectIDZND0="20591@1" Pin0InfoVect0LinkObjId="SW-104514_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18d2b00_0" Pin1InfoVect1LinkObjId="g_14f20a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1572,-526 1572,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c04530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1572,-481 1572,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20591@0" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_19f14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1572,-481 1572,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfb230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-164 375,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_185f5e0@0" ObjectIDZND0="g_1c43f50@0" Pin0InfoVect0LinkObjId="g_1c43f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_185f5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,-164 375,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf9010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-433 375,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20556@0" ObjectIDZND0="20522@0" Pin0InfoVect0LinkObjId="g_2226ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,-433 375,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c01d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-327 375,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="20557@x" ObjectIDND1="g_185f5e0@0" ObjectIDND2="g_220f3b0@0" ObjectIDZND0="20556@1" Pin0InfoVect0LinkObjId="SW-103361_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103362_0" Pin1InfoVect1LinkObjId="g_185f5e0_0" Pin1InfoVect2LinkObjId="g_220f3b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,-327 375,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfac80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-234 375,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_220f3b0@0" ObjectIDZND0="g_185f5e0@0" ObjectIDZND1="20557@x" ObjectIDZND2="20556@x" Pin0InfoVect0LinkObjId="g_185f5e0_0" Pin0InfoVect1LinkObjId="SW-103362_0" Pin0InfoVect2LinkObjId="SW-103361_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220f3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="382,-234 375,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c00990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-198 375,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_185f5e0@1" ObjectIDZND0="20557@x" ObjectIDZND1="20556@x" ObjectIDZND2="g_220f3b0@0" Pin0InfoVect0LinkObjId="SW-103362_0" Pin0InfoVect1LinkObjId="SW-103361_0" Pin0InfoVect2LinkObjId="g_220f3b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_185f5e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="375,-198 375,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_228f8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-234 375,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_185f5e0@0" ObjectIDND1="g_220f3b0@0" ObjectIDZND0="20557@x" ObjectIDZND1="20556@x" Pin0InfoVect0LinkObjId="SW-103362_0" Pin0InfoVect1LinkObjId="SW-103361_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_185f5e0_0" Pin1InfoVect1LinkObjId="g_220f3b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="375,-234 375,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2290160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-282 515,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20559@0" ObjectIDZND0="g_19359b0@0" Pin0InfoVect0LinkObjId="g_19359b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103364_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-282 515,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_228f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-318 515,-327 486,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20559@1" ObjectIDZND0="20558@x" ObjectIDZND1="g_1d2b3b0@0" ObjectIDZND2="43371@x" Pin0InfoVect0LinkObjId="SW-103363_0" Pin0InfoVect1LinkObjId="g_1d2b3b0_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103364_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="515,-318 515,-327 486,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2320f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-433 486,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20558@0" ObjectIDZND0="20522@0" Pin0InfoVect0LinkObjId="g_2226ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="486,-433 486,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-327 486,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20559@x" ObjectIDND1="g_1d2b3b0@0" ObjectIDND2="43371@x" ObjectIDZND0="20558@1" Pin0InfoVect0LinkObjId="SW-103363_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103364_0" Pin1InfoVect1LinkObjId="g_1d2b3b0_0" Pin1InfoVect2LinkObjId="SM-CX_HTP.P1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="486,-327 486,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c03f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="493,-234 486,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_1d2b3b0@0" ObjectIDZND0="20558@x" ObjectIDZND1="20559@x" ObjectIDZND2="43371@x" Pin0InfoVect0LinkObjId="SW-103363_0" Pin0InfoVect1LinkObjId="SW-103364_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d2b3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="493,-234 486,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfb7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-106 486,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="43371@0" ObjectIDZND0="g_1d2b3b0@0" ObjectIDZND1="20558@x" ObjectIDZND2="20559@x" Pin0InfoVect0LinkObjId="g_1d2b3b0_0" Pin0InfoVect1LinkObjId="SW-103363_0" Pin0InfoVect2LinkObjId="SW-103364_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTP.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="486,-106 486,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2291ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-234 486,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1d2b3b0@0" ObjectIDND1="43371@x" ObjectIDZND0="20558@x" ObjectIDZND1="20559@x" Pin0InfoVect0LinkObjId="SW-103363_0" Pin0InfoVect1LinkObjId="SW-103364_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d2b3b0_0" Pin1InfoVect1LinkObjId="SM-CX_HTP.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="486,-234 486,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21145d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="610,-282 610,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20561@0" ObjectIDZND0="g_1574620@0" Pin0InfoVect0LinkObjId="g_1574620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103366_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="610,-282 610,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2258c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="610,-318 610,-327 581,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20561@1" ObjectIDZND0="20560@x" ObjectIDZND1="g_1c8d3e0@0" ObjectIDZND2="43372@x" Pin0InfoVect0LinkObjId="SW-103365_0" Pin0InfoVect1LinkObjId="g_1c8d3e0_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103366_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="610,-318 610,-327 581,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24203e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-434 581,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20560@0" ObjectIDZND0="20522@0" Pin0InfoVect0LinkObjId="g_2226ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103365_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-434 581,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2111ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-327 581,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20561@x" ObjectIDND1="g_1c8d3e0@0" ObjectIDND2="43372@x" ObjectIDZND0="20560@1" Pin0InfoVect0LinkObjId="SW-103365_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103366_0" Pin1InfoVect1LinkObjId="g_1c8d3e0_0" Pin1InfoVect2LinkObjId="SM-CX_HTP.P2_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-327 581,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf95c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="588,-234 581,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_1c8d3e0@0" ObjectIDZND0="20560@x" ObjectIDZND1="20561@x" ObjectIDZND2="43372@x" Pin0InfoVect0LinkObjId="SW-103365_0" Pin0InfoVect1LinkObjId="SW-103366_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c8d3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="588,-234 581,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2003ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-106 581,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="43372@0" ObjectIDZND0="g_1c8d3e0@0" ObjectIDZND1="20560@x" ObjectIDZND2="20561@x" Pin0InfoVect0LinkObjId="g_1c8d3e0_0" Pin0InfoVect1LinkObjId="SW-103365_0" Pin0InfoVect2LinkObjId="SW-103366_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTP.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="581,-106 581,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2113d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-234 581,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1c8d3e0@0" ObjectIDND1="43372@x" ObjectIDZND0="20560@x" ObjectIDZND1="20561@x" Pin0InfoVect0LinkObjId="SW-103365_0" Pin0InfoVect1LinkObjId="SW-103366_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c8d3e0_0" Pin1InfoVect1LinkObjId="SM-CX_HTP.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="581,-234 581,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c017b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-282 705,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20563@0" ObjectIDZND0="g_1a998c0@0" Pin0InfoVect0LinkObjId="g_1a998c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103368_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-282 705,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf73d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-318 705,-327 676,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20563@1" ObjectIDZND0="20562@x" ObjectIDZND1="g_11e68b0@0" ObjectIDZND2="43373@x" Pin0InfoVect0LinkObjId="SW-103367_0" Pin0InfoVect1LinkObjId="g_11e68b0_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103368_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="705,-318 705,-327 676,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2116790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-433 676,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20562@0" ObjectIDZND0="20522@0" Pin0InfoVect0LinkObjId="g_2226ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,-433 676,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf84b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-327 676,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20563@x" ObjectIDND1="g_11e68b0@0" ObjectIDND2="43373@x" ObjectIDZND0="20562@1" Pin0InfoVect0LinkObjId="SW-103367_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103368_0" Pin1InfoVect1LinkObjId="g_11e68b0_0" Pin1InfoVect2LinkObjId="SM-CX_HTP.P3_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,-327 676,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_241a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="683,-234 676,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_11e68b0@0" ObjectIDZND0="20562@x" ObjectIDZND1="20563@x" ObjectIDZND2="43373@x" Pin0InfoVect0LinkObjId="SW-103367_0" Pin0InfoVect1LinkObjId="SW-103368_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11e68b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="683,-234 676,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21115f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-106 676,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="43373@0" ObjectIDZND0="g_11e68b0@0" ObjectIDZND1="20562@x" ObjectIDZND2="20563@x" Pin0InfoVect0LinkObjId="g_11e68b0_0" Pin0InfoVect1LinkObjId="SW-103367_0" Pin0InfoVect2LinkObjId="SW-103368_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTP.P3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="676,-106 676,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfa6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-234 676,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_11e68b0@0" ObjectIDND1="43373@x" ObjectIDZND0="20562@x" ObjectIDZND1="20563@x" Pin0InfoVect0LinkObjId="SW-103367_0" Pin0InfoVect1LinkObjId="SW-103368_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11e68b0_0" Pin1InfoVect1LinkObjId="SM-CX_HTP.P3_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="676,-234 676,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23219d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="811,-282 811,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20584@0" ObjectIDZND0="g_fc5e70@0" Pin0InfoVect0LinkObjId="g_fc5e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="811,-282 811,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c028c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="811,-318 811,-327 782,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20584@1" ObjectIDZND0="20564@x" ObjectIDZND1="g_17422d0@0" ObjectIDZND2="43374@x" Pin0InfoVect0LinkObjId="SW-103369_0" Pin0InfoVect1LinkObjId="g_17422d0_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="811,-318 811,-327 782,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22909d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="782,-433 782,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20564@0" ObjectIDZND0="20522@0" Pin0InfoVect0LinkObjId="g_2226ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="782,-433 782,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2291240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="782,-327 782,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20584@x" ObjectIDND1="g_17422d0@0" ObjectIDND2="43374@x" ObjectIDZND0="20564@1" Pin0InfoVect0LinkObjId="SW-103369_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104044_0" Pin1InfoVect1LinkObjId="g_17422d0_0" Pin1InfoVect2LinkObjId="SM-CX_HTP.P4_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="782,-327 782,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_270a6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="789,-234 782,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_17422d0@0" ObjectIDZND0="20564@x" ObjectIDZND1="20584@x" ObjectIDZND2="43374@x" Pin0InfoVect0LinkObjId="SW-103369_0" Pin0InfoVect1LinkObjId="SW-104044_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17422d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="789,-234 782,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfa120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="782,-106 782,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="43374@0" ObjectIDZND0="g_17422d0@0" ObjectIDZND1="20564@x" ObjectIDZND2="20584@x" Pin0InfoVect0LinkObjId="g_17422d0_0" Pin0InfoVect1LinkObjId="SW-103369_0" Pin0InfoVect2LinkObjId="SW-104044_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTP.P4_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="782,-106 782,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_217b830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="782,-234 782,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_17422d0@0" ObjectIDND1="43374@x" ObjectIDZND0="20564@x" ObjectIDZND1="20584@x" Pin0InfoVect0LinkObjId="SW-103369_0" Pin0InfoVect1LinkObjId="SW-104044_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17422d0_0" Pin1InfoVect1LinkObjId="SM-CX_HTP.P4_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="782,-234 782,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fefa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="921,-237 921,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20566@0" ObjectIDZND0="g_156ed30@0" Pin0InfoVect0LinkObjId="g_156ed30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103371_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="921,-237 921,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bda160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="892,-433 892,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20565@0" ObjectIDZND0="20522@0" Pin0InfoVect0LinkObjId="g_2226ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-433 892,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f3f170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="974,-275 974,-282 921,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_1856e20@0" ObjectIDZND0="20566@x" ObjectIDZND1="20565@x" ObjectIDZND2="g_1c8cc50@0" Pin0InfoVect0LinkObjId="SW-103371_0" Pin0InfoVect1LinkObjId="SW-103370_0" Pin0InfoVect2LinkObjId="g_1c8cc50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1856e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="974,-275 974,-282 921,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2130780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="921,-273 921,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="20566@1" ObjectIDZND0="g_1856e20@0" ObjectIDZND1="20565@x" ObjectIDZND2="g_1c8cc50@0" Pin0InfoVect0LinkObjId="g_1856e20_0" Pin0InfoVect1LinkObjId="SW-103370_0" Pin0InfoVect2LinkObjId="g_1c8cc50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103371_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="921,-273 921,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b1290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="921,-282 892,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1856e20@0" ObjectIDND1="20566@x" ObjectIDZND0="20565@x" ObjectIDZND1="g_1c8cc50@0" Pin0InfoVect0LinkObjId="SW-103370_0" Pin0InfoVect1LinkObjId="g_1c8cc50_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1856e20_0" Pin1InfoVect1LinkObjId="SW-103371_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="921,-282 892,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_244b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="892,-165 892,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_18d0de0@0" ObjectIDZND0="g_1c8cc50@0" Pin0InfoVect0LinkObjId="g_1c8cc50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18d0de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-165 892,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1547320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="861,-138 861,-149 893,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_220bee0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220bee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="861,-138 861,-149 893,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cb7360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="892,-341 892,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="20565@1" ObjectIDZND0="g_1856e20@0" ObjectIDZND1="20566@x" ObjectIDZND2="g_1c8cc50@0" Pin0InfoVect0LinkObjId="g_1856e20_0" Pin0InfoVect1LinkObjId="SW-103371_0" Pin0InfoVect2LinkObjId="g_1c8cc50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="892,-341 892,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f65f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="892,-225 892,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_1c8cc50@1" ObjectIDZND0="g_1856e20@0" ObjectIDZND1="20566@x" ObjectIDZND2="20565@x" Pin0InfoVect0LinkObjId="g_1856e20_0" Pin0InfoVect1LinkObjId="SW-103371_0" Pin0InfoVect2LinkObjId="SW-103370_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c8cc50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="892,-225 892,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa2dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1145,-282 1145,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20568@0" ObjectIDZND0="g_1b37100@0" Pin0InfoVect0LinkObjId="g_1b37100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1145,-282 1145,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2290c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1145,-318 1145,-327 1116,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20568@1" ObjectIDZND0="20567@x" ObjectIDZND1="g_2321c80@0" ObjectIDZND2="g_21126c0@0" Pin0InfoVect0LinkObjId="SW-103372_0" Pin0InfoVect1LinkObjId="g_2321c80_0" Pin0InfoVect2LinkObjId="g_21126c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103373_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1145,-318 1145,-327 1116,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2291630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,-433 1116,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20567@0" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_19f14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-433 1116,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d332f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,-327 1116,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="20568@x" ObjectIDND1="g_2321c80@0" ObjectIDND2="g_21126c0@0" ObjectIDZND0="20567@1" Pin0InfoVect0LinkObjId="SW-103372_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103373_0" Pin1InfoVect1LinkObjId="g_2321c80_0" Pin1InfoVect2LinkObjId="g_21126c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-327 1116,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1123,-242 1116,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2321c80@0" ObjectIDZND0="20567@x" ObjectIDZND1="20568@x" ObjectIDZND2="g_21126c0@0" Pin0InfoVect0LinkObjId="SW-103372_0" Pin0InfoVect1LinkObjId="SW-103373_0" Pin0InfoVect2LinkObjId="g_21126c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2321c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1123,-242 1116,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_168c850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,-165 1116,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_168cab0@0" ObjectIDZND0="g_21126c0@0" Pin0InfoVect0LinkObjId="g_21126c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_168cab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-165 1116,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2116320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1085,-138 1085,-149 1117,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2112f30@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2112f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1085,-138 1085,-149 1117,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2118390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,-242 1116,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2321c80@0" ObjectIDND1="g_21126c0@0" ObjectIDZND0="20567@x" ObjectIDZND1="20568@x" Pin0InfoVect0LinkObjId="SW-103372_0" Pin0InfoVect1LinkObjId="SW-103373_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2321c80_0" Pin1InfoVect1LinkObjId="g_21126c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-242 1116,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2118580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,-226 1116,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_21126c0@1" ObjectIDZND0="g_2321c80@0" ObjectIDZND1="20567@x" ObjectIDZND2="20568@x" Pin0InfoVect0LinkObjId="g_2321c80_0" Pin0InfoVect1LinkObjId="SW-103372_0" Pin0InfoVect2LinkObjId="SW-103373_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21126c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-226 1116,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22740d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1253,-282 1253,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20570@0" ObjectIDZND0="g_2115960@0" Pin0InfoVect0LinkObjId="g_2115960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103375_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1253,-282 1253,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2115190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1253,-318 1253,-327 1224,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20570@1" ObjectIDZND0="20569@x" ObjectIDZND1="g_2258710@0" ObjectIDZND2="43375@x" Pin0InfoVect0LinkObjId="SW-103374_0" Pin0InfoVect1LinkObjId="g_2258710_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P5_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103375_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1253,-318 1253,-327 1224,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a50670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-433 1224,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20569@0" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_19f14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-433 1224,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a508d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-327 1224,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20570@x" ObjectIDND1="g_2258710@0" ObjectIDND2="43375@x" ObjectIDZND0="20569@1" Pin0InfoVect0LinkObjId="SW-103374_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103375_0" Pin1InfoVect1LinkObjId="g_2258710_0" Pin1InfoVect2LinkObjId="SM-CX_HTP.P5_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-327 1224,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_244a4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1231,-234 1224,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2258710@0" ObjectIDZND0="20570@x" ObjectIDZND1="20569@x" ObjectIDZND2="43375@x" Pin0InfoVect0LinkObjId="SW-103375_0" Pin0InfoVect1LinkObjId="SW-103374_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P5_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2258710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1231,-234 1224,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_244a6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-106 1224,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43375@0" ObjectIDZND0="g_2258710@0" ObjectIDZND1="20570@x" ObjectIDZND2="20569@x" Pin0InfoVect0LinkObjId="g_2258710_0" Pin0InfoVect1LinkObjId="SW-103375_0" Pin0InfoVect2LinkObjId="SW-103374_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTP.P5_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-106 1224,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22584b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-234 1224,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2258710@0" ObjectIDND1="43375@x" ObjectIDZND0="20570@x" ObjectIDZND1="20569@x" Pin0InfoVect0LinkObjId="SW-103375_0" Pin0InfoVect1LinkObjId="SW-103374_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2258710_0" Pin1InfoVect1LinkObjId="SM-CX_HTP.P5_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-234 1224,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2268ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1361,-282 1361,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20572@0" ObjectIDZND0="g_226b3f0@0" Pin0InfoVect0LinkObjId="g_226b3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103377_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1361,-282 1361,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2914700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1361,-318 1361,-327 1332,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20572@1" ObjectIDZND0="20571@x" ObjectIDZND1="g_1c0bd60@0" ObjectIDZND2="43376@x" Pin0InfoVect0LinkObjId="SW-103376_0" Pin0InfoVect1LinkObjId="g_1c0bd60_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P6_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1361,-318 1361,-327 1332,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1332,-433 1332,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20571@0" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_19f14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1332,-433 1332,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0b3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1332,-327 1332,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20572@x" ObjectIDND1="g_1c0bd60@0" ObjectIDND2="43376@x" ObjectIDZND0="20571@1" Pin0InfoVect0LinkObjId="SW-103376_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103377_0" Pin1InfoVect1LinkObjId="g_1c0bd60_0" Pin1InfoVect2LinkObjId="SM-CX_HTP.P6_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1332,-327 1332,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0b640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-234 1332,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_1c0bd60@0" ObjectIDZND0="20572@x" ObjectIDZND1="20571@x" ObjectIDZND2="43376@x" Pin0InfoVect0LinkObjId="SW-103377_0" Pin0InfoVect1LinkObjId="SW-103376_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P6_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c0bd60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-234 1332,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0b8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1332,-106 1332,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43376@0" ObjectIDZND0="g_1c0bd60@0" ObjectIDZND1="20572@x" ObjectIDZND2="20571@x" Pin0InfoVect0LinkObjId="g_1c0bd60_0" Pin0InfoVect1LinkObjId="SW-103377_0" Pin0InfoVect2LinkObjId="SW-103376_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTP.P6_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1332,-106 1332,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0bb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1332,-234 1332,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1c0bd60@0" ObjectIDND1="43376@x" ObjectIDZND0="20572@x" ObjectIDZND1="20571@x" Pin0InfoVect0LinkObjId="SW-103377_0" Pin0InfoVect1LinkObjId="SW-103376_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c0bd60_0" Pin1InfoVect1LinkObjId="SM-CX_HTP.P6_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1332,-234 1332,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21777b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1458,-282 1458,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20574@0" ObjectIDZND0="g_2176d20@0" Pin0InfoVect0LinkObjId="g_2176d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1458,-282 1458,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2177f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1458,-318 1458,-327 1429,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20574@1" ObjectIDZND0="20573@x" ObjectIDZND1="g_2485f40@0" ObjectIDZND2="43377@x" Pin0InfoVect0LinkObjId="SW-103378_0" Pin0InfoVect1LinkObjId="g_2485f40_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P7_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103379_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1458,-318 1458,-327 1429,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225dab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1429,-433 1429,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20573@0" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_19f14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1429,-433 1429,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225dd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1429,-327 1429,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20574@x" ObjectIDND1="g_2485f40@0" ObjectIDND2="43377@x" ObjectIDZND0="20573@1" Pin0InfoVect0LinkObjId="SW-103378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103379_0" Pin1InfoVect1LinkObjId="g_2485f40_0" Pin1InfoVect2LinkObjId="SM-CX_HTP.P7_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1429,-327 1429,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225df70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1436,-234 1429,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2485f40@0" ObjectIDZND0="20574@x" ObjectIDZND1="20573@x" ObjectIDZND2="43377@x" Pin0InfoVect0LinkObjId="SW-103379_0" Pin0InfoVect1LinkObjId="SW-103378_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P7_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2485f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1436,-234 1429,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1429,-106 1429,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43377@0" ObjectIDZND0="g_2485f40@0" ObjectIDZND1="20574@x" ObjectIDZND2="20573@x" Pin0InfoVect0LinkObjId="g_2485f40_0" Pin0InfoVect1LinkObjId="SW-103379_0" Pin0InfoVect2LinkObjId="SW-103378_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTP.P7_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1429,-106 1429,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1429,-234 1429,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2485f40@0" ObjectIDND1="43377@x" ObjectIDZND0="20574@x" ObjectIDZND1="20573@x" Pin0InfoVect0LinkObjId="SW-103379_0" Pin0InfoVect1LinkObjId="SW-103378_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2485f40_0" Pin1InfoVect1LinkObjId="SM-CX_HTP.P7_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1429,-234 1429,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248a1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1561,-282 1561,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20576@0" ObjectIDZND0="g_2489710@0" Pin0InfoVect0LinkObjId="g_2489710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1561,-282 1561,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248a8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1561,-318 1561,-327 1532,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20576@1" ObjectIDZND0="20575@x" ObjectIDZND1="g_22969a0@0" ObjectIDZND2="43378@x" Pin0InfoVect0LinkObjId="SW-103380_0" Pin0InfoVect1LinkObjId="g_22969a0_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P8_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1561,-318 1561,-327 1532,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11a3860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1532,-433 1532,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20575@0" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_19f14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1532,-433 1532,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11a3ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1532,-327 1532,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20576@x" ObjectIDND1="g_22969a0@0" ObjectIDND2="43378@x" ObjectIDZND0="20575@1" Pin0InfoVect0LinkObjId="SW-103380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103381_0" Pin1InfoVect1LinkObjId="g_22969a0_0" Pin1InfoVect2LinkObjId="SM-CX_HTP.P8_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1532,-327 1532,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11a3d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-234 1532,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_22969a0@0" ObjectIDZND0="20576@x" ObjectIDZND1="20575@x" ObjectIDZND2="43378@x" Pin0InfoVect0LinkObjId="SW-103381_0" Pin0InfoVect1LinkObjId="SW-103380_0" Pin0InfoVect2LinkObjId="SM-CX_HTP.P8_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22969a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-234 1532,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11a3f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1532,-106 1532,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43378@0" ObjectIDZND0="g_22969a0@0" ObjectIDZND1="20576@x" ObjectIDZND2="20575@x" Pin0InfoVect0LinkObjId="g_22969a0_0" Pin0InfoVect1LinkObjId="SW-103381_0" Pin0InfoVect2LinkObjId="SW-103380_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTP.P8_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1532,-106 1532,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11a41e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1532,-234 1532,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_22969a0@0" ObjectIDND1="43378@x" ObjectIDZND0="20576@x" ObjectIDZND1="20575@x" Pin0InfoVect0LinkObjId="SW-103381_0" Pin0InfoVect1LinkObjId="SW-103380_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22969a0_0" Pin1InfoVect1LinkObjId="SM-CX_HTP.P8_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1532,-234 1532,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2175270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-282 1652,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20578@0" ObjectIDZND0="g_21747e0@0" Pin0InfoVect0LinkObjId="g_21747e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-282 1652,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21763b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-318 1652,-327 1623,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20578@1" ObjectIDZND0="20577@x" ObjectIDZND1="g_24affd0@0" ObjectIDZND2="g_249b930@0" Pin0InfoVect0LinkObjId="SW-103382_0" Pin0InfoVect1LinkObjId="g_24affd0_0" Pin0InfoVect2LinkObjId="g_249b930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103383_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-318 1652,-327 1623,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b07a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-163 1623,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_24affd0@0" ObjectIDZND0="g_21754d0@0" Pin0InfoVect0LinkObjId="g_21754d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24affd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-163 1623,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b5550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-327 1623,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="20578@x" ObjectIDND1="g_24affd0@0" ObjectIDND2="g_249b930@0" ObjectIDZND0="20577@1" Pin0InfoVect0LinkObjId="SW-103382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103383_0" Pin1InfoVect1LinkObjId="g_24affd0_0" Pin1InfoVect2LinkObjId="g_249b930_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-327 1623,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_249b210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1630,-234 1623,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_249b930@0" ObjectIDZND0="g_24affd0@0" ObjectIDZND1="20577@x" ObjectIDZND2="20578@x" Pin0InfoVect0LinkObjId="g_24affd0_0" Pin0InfoVect1LinkObjId="SW-103382_0" Pin0InfoVect2LinkObjId="SW-103383_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_249b930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1630,-234 1623,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_249b470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-198 1623,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_24affd0@1" ObjectIDZND0="g_249b930@0" ObjectIDZND1="20577@x" ObjectIDZND2="20578@x" Pin0InfoVect0LinkObjId="g_249b930_0" Pin0InfoVect1LinkObjId="SW-103382_0" Pin0InfoVect2LinkObjId="SW-103383_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24affd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-198 1623,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_249b6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-234 1623,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_24affd0@0" ObjectIDND1="g_249b930@0" ObjectIDZND0="20577@x" ObjectIDZND1="20578@x" Pin0InfoVect0LinkObjId="SW-103382_0" Pin0InfoVect1LinkObjId="SW-103383_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24affd0_0" Pin1InfoVect1LinkObjId="g_249b930_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-234 1623,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_249c660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-433 1623,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20577@0" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_19f14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-433 1623,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24a0c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1758,-282 1758,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20580@0" ObjectIDZND0="g_24a01a0@0" Pin0InfoVect0LinkObjId="g_24a01a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1758,-282 1758,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0ca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1758,-318 1758,-327 1729,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20580@1" ObjectIDZND0="20579@x" ObjectIDZND1="g_1c0d130@0" ObjectIDZND2="g_224ab20@0" Pin0InfoVect0LinkObjId="SW-103384_0" Pin0InfoVect1LinkObjId="g_1c0d130_0" Pin0InfoVect2LinkObjId="g_224ab20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1758,-318 1758,-327 1729,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1729,-170 1729,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c0d130@0" ObjectIDZND0="g_224ca10@0" Pin0InfoVect0LinkObjId="g_224ca10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c0d130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1729,-170 1729,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c126b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1729,-327 1729,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="20580@x" ObjectIDND1="g_1c0d130@0" ObjectIDND2="g_224ab20@0" ObjectIDZND0="20579@1" Pin0InfoVect0LinkObjId="SW-103384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-103385_0" Pin1InfoVect1LinkObjId="g_1c0d130_0" Pin1InfoVect2LinkObjId="g_224ab20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1729,-327 1729,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c12910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1736,-234 1729,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_224ab20@0" ObjectIDZND0="g_1c0d130@0" ObjectIDZND1="20579@x" ObjectIDZND2="20580@x" Pin0InfoVect0LinkObjId="g_1c0d130_0" Pin0InfoVect1LinkObjId="SW-103384_0" Pin0InfoVect2LinkObjId="SW-103385_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_224ab20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1736,-234 1729,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c12b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1729,-204 1729,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_1c0d130@1" ObjectIDZND0="g_224ab20@0" ObjectIDZND1="20579@x" ObjectIDZND2="20580@x" Pin0InfoVect0LinkObjId="g_224ab20_0" Pin0InfoVect1LinkObjId="SW-103384_0" Pin0InfoVect2LinkObjId="SW-103385_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c0d130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1729,-204 1729,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224a8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1729,-234 1729,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1c0d130@0" ObjectIDND1="g_224ab20@0" ObjectIDZND0="20579@x" ObjectIDZND1="20580@x" Pin0InfoVect0LinkObjId="SW-103384_0" Pin0InfoVect1LinkObjId="SW-103385_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c0d130_0" Pin1InfoVect1LinkObjId="g_224ab20_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1729,-234 1729,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224b690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1729,-433 1729,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20579@0" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_19f14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1729,-433 1729,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225f390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="963,-453 963,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20522@0" ObjectIDZND0="20592@1" Pin0InfoVect0LinkObjId="SW-104515_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2226ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="963,-453 963,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225f5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,-433 1045,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20581@0" ObjectIDZND0="20523@0" Pin0InfoVect0LinkObjId="g_19f14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-103386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1045,-433 1045,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225f850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="970,-330 963,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_225fab0@0" ObjectIDZND0="20592@x" ObjectIDZND1="20581@x" Pin0InfoVect0LinkObjId="SW-104515_0" Pin0InfoVect1LinkObjId="SW-103386_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_225fab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="970,-330 963,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2264cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="963,-354 963,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="20592@0" ObjectIDZND0="20581@x" ObjectIDZND1="g_225fab0@0" Pin0InfoVect0LinkObjId="SW-103386_0" Pin0InfoVect1LinkObjId="g_225fab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104515_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="963,-354 963,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2264f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="963,-330 963,-309 1045,-309 1045,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="20592@x" ObjectIDND1="g_225fab0@0" ObjectIDZND0="20581@1" Pin0InfoVect0LinkObjId="SW-103386_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104515_0" Pin1InfoVect1LinkObjId="g_225fab0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="963,-330 963,-309 1045,-309 1045,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24a5ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-1138 1653,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_220bcf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-1138 1653,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24a5f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-888 1653,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220bcf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-888 1653,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24acf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-1138 1866,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_220bcf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1866,-1138 1866,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24ad1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-888 1866,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220bcf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1866,-888 1866,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24f75a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1813,-888 1813,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220bcf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1813,-888 1813,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24f7800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-992 1813,-992 1813,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_220bcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_220bcf0_0" Pin1InfoVect1LinkObjId="g_220bcf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-992 1813,-992 1813,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24f82f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-1004 1653,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_220bcf0_0" Pin0InfoVect1LinkObjId="g_220bcf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220bcf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-1004 1653,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24f8550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-992 1653,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_220bcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_220bcf0_0" Pin1InfoVect1LinkObjId="g_220bcf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-992 1653,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fd740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1708,-743 1708,-725 1950,-725 1950,-992 1866,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_220bcf0_0" Pin0InfoVect1LinkObjId="g_220bcf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220bcf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1708,-743 1708,-725 1950,-725 1950,-992 1866,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fd9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1708,-853 1708,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_220bcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1708,-853 1708,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fe630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-1004 1866,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_220bcf0_0" Pin0InfoVect1LinkObjId="g_220bcf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220bcf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1866,-1004 1866,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fe890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-992 1866,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_220bcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_220bcf0_0" Pin1InfoVect1LinkObjId="g_220bcf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1866,-992 1866,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_248f830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-1200 621,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29091@1" ObjectIDZND0="g_ec8d10@0" ObjectIDZND1="20527@x" ObjectIDZND2="20530@x" Pin0InfoVect0LinkObjId="g_ec8d10_0" Pin0InfoVect1LinkObjId="SW-103332_0" Pin0InfoVect2LinkObjId="SW-103335_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="621,-1200 621,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2490210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-1170 651,-1181 621,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_ec8d10@0" ObjectIDZND0="20527@x" ObjectIDZND1="20530@x" ObjectIDZND2="g_14ec300@0" Pin0InfoVect0LinkObjId="SW-103332_0" Pin0InfoVect1LinkObjId="SW-103335_0" Pin0InfoVect2LinkObjId="g_14ec300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_ec8d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="651,-1170 651,-1181 621,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2490440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-1181 621,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_ec8d10@0" ObjectIDND1="29091@1" ObjectIDZND0="20527@x" ObjectIDZND1="20530@x" ObjectIDZND2="g_14ec300@0" Pin0InfoVect0LinkObjId="SW-103332_0" Pin0InfoVect1LinkObjId="SW-103335_0" Pin0InfoVect2LinkObjId="g_14ec300_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_ec8d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="621,-1181 621,-1163 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="20521" cx="910" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20521" cx="719" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20521" cx="1165" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20521" cx="621" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20522" cx="719" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20522" cx="440" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20522" cx="375" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20522" cx="486" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20522" cx="581" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20522" cx="676" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20522" cx="782" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20522" cx="892" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20522" cx="963" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1165" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1572" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1116" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1224" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1332" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1429" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1532" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1623" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1729" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20523" cx="1045" cy="-453" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-93715" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 262.000000 -1118.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19736" ObjectName="DYN-CX_HTP"/>
     <cge:Meas_Ref ObjectId="93715"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_159a6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -874.000000) translate(0,16)">220kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,17)">危险点说明</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fd69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,374)">联系方式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d5c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,17)">频率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d5c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d5c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,59)">全站有功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d5c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d5c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,101)">风机出力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d5c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d5c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,143)">全站无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d5c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d5c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,185)">并网联络点的电压和交换功率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_ec01e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 115.000000 -1198.500000) translate(0,16)">红土坡风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2152120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -684.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15674b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 563.000000 -975.000000) translate(0,12)">23117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a79e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 625.000000 -920.000000) translate(0,12)">2311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_18b0c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -1112.500000) translate(0,16)">220kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_18b0c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -1112.500000) translate(0,35)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_150cb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -943.000000) translate(0,12)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b30620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 917.000000 -971.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6b5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -1034.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a5460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 631.000000 -992.000000) translate(0,12)">231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1690db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -1034.000000) translate(0,12)">23160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1745af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -1099.000000) translate(0,12)">23167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d93220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 628.000000 -1052.000000) translate(0,12)">2316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_20df6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -1265.500000) translate(0,16)">红紫线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1747df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 -806.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_194d350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -856.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_222fa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -750.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_eb61f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -847.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f433b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -798.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1c280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -740.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff82b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -856.000000) translate(0,12)">2021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18af520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1125.000000 -806.000000) translate(0,12)">202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1559850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -747.000000) translate(0,12)">2026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.000000 -740.000000) translate(0,12)">20267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_effd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.000000 -798.000000) translate(0,12)">20260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.000000 -847.000000) translate(0,12)">20217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d5bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -661.000000) translate(0,12)">2020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_11ef390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -447.000000) translate(0,16)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_18c1080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 -445.000000) translate(0,16)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18b7610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 408.257519 -306.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190ce50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 -497.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1692630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 335.000000 -99.000000) translate(0,12)">1号动态无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1692630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 335.000000 -99.000000) translate(0,27)">补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1849ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 341.000000 -67.000000) translate(0,12)">±28MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14edee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -168.000000) translate(0,12)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14edee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -168.000000) translate(0,27)">土</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14edee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -168.000000) translate(0,42)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14edee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -168.000000) translate(0,57)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14edee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -168.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14edee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_185dcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 532.000000 -49.000000) translate(0,12)">(9~17号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f54b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -168.000000) translate(0,12)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f54b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -168.000000) translate(0,27)">土</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f54b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -168.000000) translate(0,42)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f54b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -168.000000) translate(0,57)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f54b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -168.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f54b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b4210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.257519 -533.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fc15a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.257519 -531.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce4960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1609.000000 -1172.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d70b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1620.500000 -1156.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1868c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1603.500000 -849.000000) translate(0,12)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1500bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1805.500000 -849.000000) translate(0,12)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b162e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -805.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b162e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -805.000000) translate(0,27)">SZ11-80000/220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b162e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -805.000000) translate(0,42)">230±8×1.25%/37</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b162e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -805.000000) translate(0,57)">80000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b162e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -805.000000) translate(0,72)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b162e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -805.000000) translate(0,87)">Ud%=14</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f09d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1584.000000 -497.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1513600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 383.000000 -659.500000) translate(0,16)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1513600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 383.000000 -659.500000) translate(0,35)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_14e2cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1523.000000 -658.500000) translate(0,16)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_14e2cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1523.000000 -658.500000) translate(0,35)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1884100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1236.000000 -655.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aab130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -973.000000) translate(0,12)">23217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f91be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1129.000000 -920.000000) translate(0,12)">2321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1548750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 -992.000000) translate(0,12)">232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2239d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -1034.000000) translate(0,12)">23260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fe460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -1099.000000) translate(0,12)">23267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16e1970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1132.000000 -1052.000000) translate(0,12)">2326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_11e1bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1097.000000 -1265.500000) translate(0,16)">红五线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1206190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -805.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1206190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -805.000000) translate(0,27)">SZ11-80000/220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1206190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -805.000000) translate(0,42)">230±8×1.25%/37</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1206190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -805.000000) translate(0,57)">80000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1206190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -805.000000) translate(0,72)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1206190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -805.000000) translate(0,87)">Ud%=14</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cd68e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.000000 -655.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155f870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -661.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c78460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1217.000000 -684.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1744a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.257519 -393.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18b7b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 519.257519 -306.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1007760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 498.257519 -393.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6f310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 614.257519 -306.000000) translate(0,12)">33367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1217640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.257519 -393.000000) translate(0,12)">333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1c40ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 439.000000 -49.000000) translate(0,12)">(1~8号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_192d870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -168.000000) translate(0,12)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_192d870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -168.000000) translate(0,27)">土</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_192d870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -168.000000) translate(0,42)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_192d870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -168.000000) translate(0,57)">Ⅲ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_192d870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -168.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_192d870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_fa0270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 -49.000000) translate(0,12)">(18~27号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16db020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.257519 -306.000000) translate(0,12)">33567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a1190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.257519 -393.000000) translate(0,12)">335</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1739810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -168.000000) translate(0,12)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1739810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -168.000000) translate(0,27)">土</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1739810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -168.000000) translate(0,42)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1739810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -168.000000) translate(0,57)">Ⅳ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1739810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -168.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1739810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e3c630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.257519 -306.000000) translate(0,12)">33667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1048740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.257519 -393.000000) translate(0,12)">336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_f5a910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 738.000000 -49.000000) translate(0,12)">(28~36号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1744530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.257519 -261.000000) translate(0,12)">33767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fb0800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.257519 -393.000000) translate(0,12)">337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7a0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 844.000000 -48.000000) translate(0,12)">35kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1692c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1128.257519 -393.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226bc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1149.257519 -306.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16806f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1063.000000 -51.000000) translate(0,12)">35kV2号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2114880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 -146.000000) translate(0,12)">800kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2291fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 -145.000000) translate(0,12)">800kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21137a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1316.000000 -168.000000) translate(0,12)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21137a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1316.000000 -168.000000) translate(0,27)">土</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21137a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1316.000000 -168.000000) translate(0,42)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21137a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1316.000000 -168.000000) translate(0,57)">Ⅵ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21137a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1316.000000 -168.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21137a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1316.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2113950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 -52.000000) translate(0,12)">(47~56号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226ff80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 -168.000000) translate(0,12)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226ff80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 -168.000000) translate(0,27)">土</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226ff80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 -168.000000) translate(0,42)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226ff80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 -168.000000) translate(0,57)">Ⅴ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226ff80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 -168.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226ff80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1937ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1257.257519 -306.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2115380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1236.257519 -393.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2269250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1363.257519 -306.000000) translate(0,12)">34367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29148f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1344.257519 -393.000000) translate(0,12)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_226d090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -52.000000) translate(0,12)">(37~46号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -168.000000) translate(0,12)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -168.000000) translate(0,27)">土</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -168.000000) translate(0,42)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -168.000000) translate(0,57)">Ⅶ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -168.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226d630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_226d840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 -52.000000) translate(0,12)">(57~63号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2177a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1462.257519 -306.000000) translate(0,12)">34467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21780f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1441.257519 -393.000000) translate(0,12)">344</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1515.000000 -168.000000) translate(0,12)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1515.000000 -168.000000) translate(0,27)">土</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1515.000000 -168.000000) translate(0,42)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1515.000000 -168.000000) translate(0,57)">Ⅷ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1515.000000 -168.000000) translate(0,72)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1515.000000 -168.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248a400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1565.257519 -306.000000) translate(0,12)">34567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248aae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.257519 -393.000000) translate(0,12)">345</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_22976d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -52.000000) translate(0,12)">(64~70号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2176170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1656.257519 -306.000000) translate(0,12)">34667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21765a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1592.000000 -99.000000) translate(0,12)">2号动态无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21765a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1592.000000 -99.000000) translate(0,27)">补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2176850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1603.000000 -69.000000) translate(0,12)">±28MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b0a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1635.257519 -393.000000) translate(0,12)">346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a0e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1762.257519 -306.000000) translate(0,12)">34767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c0cc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1696.000000 -95.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c0cef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -134.000000) translate(0,12)">250kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c0db60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1741.257519 -393.000000) translate(0,12)">347</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a483f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.257519 -393.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22607a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 970.257519 -396.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a6480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1819.000000 -1160.000000) translate(0,12)">10kV红土坡线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a6a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1891.500000 -1088.000000) translate(0,12)">备用所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a6a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1891.500000 -1088.000000) translate(0,27)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fec80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1569.500000 -1081.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_248de90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 -264.500000) translate(0,16)">4799</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.423034" x1="651" x2="651" y1="-681" y2="-667"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="651" x2="651" y1="-626" y2="-658"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="671" x2="671" y1="-626" y2="-653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="671" x2="671" y1="-681" y2="-657"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="631" x2="671" y1="-626" y2="-626"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.423034" x1="1097" x2="1097" y1="-681" y2="-667"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1097" x2="1097" y1="-626" y2="-658"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1117" x2="1117" y1="-626" y2="-653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1117" x2="1117" y1="-681" y2="-657"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1077" x2="1117" y1="-626" y2="-626"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="1721" x2="1729" y1="-120" y2="-112"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="1729" x2="1737" y1="-112" y2="-120"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="1729" x2="1729" y1="-104" y2="-112"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="1636" x2="1721" y1="-853" y2="-853"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="1798" x2="1904" y1="-853" y2="-853"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-103330">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 612.000000 -962.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20525" ObjectName="SW-CX_HTP.CX_HTP_231BK"/>
     <cge:Meas_Ref ObjectId="103330"/>
    <cge:TPSR_Ref TObjectID="20525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103342">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.565184 -777.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20537" ObjectName="SW-CX_HTP.CX_HTP_201BK"/>
     <cge:Meas_Ref ObjectId="103342"/>
    <cge:TPSR_Ref TObjectID="20537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103348">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 -778.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20543" ObjectName="SW-CX_HTP.CX_HTP_202BK"/>
     <cge:Meas_Ref ObjectId="103348"/>
    <cge:TPSR_Ref TObjectID="20543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103336">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.000000 -962.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20531" ObjectName="SW-CX_HTP.CX_HTP_232BK"/>
     <cge:Meas_Ref ObjectId="103336"/>
    <cge:TPSR_Ref TObjectID="20531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103357">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -474.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20552" ObjectName="SW-CX_HTP.CX_HTP_301BK"/>
     <cge:Meas_Ref ObjectId="103357"/>
    <cge:TPSR_Ref TObjectID="20552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103359">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 -474.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20554" ObjectName="SW-CX_HTP.CX_HTP_302BK"/>
     <cge:Meas_Ref ObjectId="103359"/>
    <cge:TPSR_Ref TObjectID="20554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103361">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20556" ObjectName="SW-CX_HTP.CX_HTP_331BK"/>
     <cge:Meas_Ref ObjectId="103361"/>
    <cge:TPSR_Ref TObjectID="20556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103363">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 476.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20558" ObjectName="SW-CX_HTP.CX_HTP_332BK"/>
     <cge:Meas_Ref ObjectId="103363"/>
    <cge:TPSR_Ref TObjectID="20558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103365">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20560" ObjectName="SW-CX_HTP.CX_HTP_333BK"/>
     <cge:Meas_Ref ObjectId="103365"/>
    <cge:TPSR_Ref TObjectID="20560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103367">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 666.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20562" ObjectName="SW-CX_HTP.CX_HTP_335BK"/>
     <cge:Meas_Ref ObjectId="103367"/>
    <cge:TPSR_Ref TObjectID="20562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103369">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20564" ObjectName="SW-CX_HTP.CX_HTP_336BK"/>
     <cge:Meas_Ref ObjectId="103369"/>
    <cge:TPSR_Ref TObjectID="20564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103370">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 882.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20565" ObjectName="SW-CX_HTP.CX_HTP_337BK"/>
     <cge:Meas_Ref ObjectId="103370"/>
    <cge:TPSR_Ref TObjectID="20565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103372">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20567" ObjectName="SW-CX_HTP.CX_HTP_341BK"/>
     <cge:Meas_Ref ObjectId="103372"/>
    <cge:TPSR_Ref TObjectID="20567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103374">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20569" ObjectName="SW-CX_HTP.CX_HTP_342BK"/>
     <cge:Meas_Ref ObjectId="103374"/>
    <cge:TPSR_Ref TObjectID="20569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103376">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20571" ObjectName="SW-CX_HTP.CX_HTP_343BK"/>
     <cge:Meas_Ref ObjectId="103376"/>
    <cge:TPSR_Ref TObjectID="20571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103378">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1419.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20573" ObjectName="SW-CX_HTP.CX_HTP_344BK"/>
     <cge:Meas_Ref ObjectId="103378"/>
    <cge:TPSR_Ref TObjectID="20573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1522.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20575" ObjectName="SW-CX_HTP.CX_HTP_345BK"/>
     <cge:Meas_Ref ObjectId="103380"/>
    <cge:TPSR_Ref TObjectID="20575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1613.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20577" ObjectName="SW-CX_HTP.CX_HTP_346BK"/>
     <cge:Meas_Ref ObjectId="103382"/>
    <cge:TPSR_Ref TObjectID="20577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1719.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20579" ObjectName="SW-CX_HTP.CX_HTP_347BK"/>
     <cge:Meas_Ref ObjectId="103384"/>
    <cge:TPSR_Ref TObjectID="20579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-103386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.000000 -334.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20581" ObjectName="SW-CX_HTP.CX_HTP_312BK"/>
     <cge:Meas_Ref ObjectId="103386"/>
    <cge:TPSR_Ref TObjectID="20581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 -881.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1856.000000 -881.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1803.000000 -881.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1698.000000 -736.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HTP.CX_HTP_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-453 1800,-453 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20523" ObjectName="BS-CX_HTP.CX_HTP_3IIM"/>
    <cge:TPSR_Ref TObjectID="20523"/></metadata>
   <polyline fill="none" opacity="0" points="1023,-453 1800,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HTP.CX_HTP_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="366,-879 1440,-879 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20521" ObjectName="BS-CX_HTP.CX_HTP_2IM"/>
    <cge:TPSR_Ref TObjectID="20521"/></metadata>
   <polyline fill="none" opacity="0" points="366,-879 1440,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HTP.CX_HTP_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-453 984,-453 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20522" ObjectName="BS-CX_HTP.CX_HTP_3IM"/>
    <cge:TPSR_Ref TObjectID="20522"/></metadata>
   <polyline fill="none" opacity="0" points="320,-453 984,-453 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1909a90">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 938.000000 -1009.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c43f50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 358.477768 -102.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a86110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -540.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a3590">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 405.000000 -521.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f20a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1563.000000 -541.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18d2b00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.000000 -522.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_185f5e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.000000 -159.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_ec8d10">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 644.000000 -1116.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d03910">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 -1112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_188a5a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 -526.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f6c520">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1127.000000 -526.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_220f3b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 377.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d2b3b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 488.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c8d3e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 583.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11e68b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 678.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17422d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 784.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1856e20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.477768 -217.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c8cc50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 882.000000 -186.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18d0de0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 -132.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_220bee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 -80.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2321c80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 -234.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21126c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -187.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_168cab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -132.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2112f30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1078.000000 -80.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2258710">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0bd60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1332.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2485f40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22969a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1534.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21754d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1606.477768 -102.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24affd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1613.000000 -159.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_249b930">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1625.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0d130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1719.000000 -165.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_224ab20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1731.000000 -226.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_224ca10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1715.000000 -101.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_225fab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 965.000000 -322.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102692" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 735.000000 -1005.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20525"/>
     <cge:Term_Ref ObjectID="10012"/>
    <cge:TPSR_Ref TObjectID="20525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102693" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 735.000000 -1005.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20525"/>
     <cge:Term_Ref ObjectID="10012"/>
    <cge:TPSR_Ref TObjectID="20525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102691" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 735.000000 -1005.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20525"/>
     <cge:Term_Ref ObjectID="10012"/>
    <cge:TPSR_Ref TObjectID="20525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102696" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -1005.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20531"/>
     <cge:Term_Ref ObjectID="10024"/>
    <cge:TPSR_Ref TObjectID="20531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102697" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -1005.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20531"/>
     <cge:Term_Ref ObjectID="10024"/>
    <cge:TPSR_Ref TObjectID="20531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102695" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -1005.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20531"/>
     <cge:Term_Ref ObjectID="10024"/>
    <cge:TPSR_Ref TObjectID="20531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 862.000000 -820.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20537"/>
     <cge:Term_Ref ObjectID="10036"/>
    <cge:TPSR_Ref TObjectID="20537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 862.000000 -820.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20537"/>
     <cge:Term_Ref ObjectID="10036"/>
    <cge:TPSR_Ref TObjectID="20537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102699" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 862.000000 -820.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20537"/>
     <cge:Term_Ref ObjectID="10036"/>
    <cge:TPSR_Ref TObjectID="20537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 -824.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20543"/>
     <cge:Term_Ref ObjectID="10048"/>
    <cge:TPSR_Ref TObjectID="20543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102718" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 -824.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20543"/>
     <cge:Term_Ref ObjectID="10048"/>
    <cge:TPSR_Ref TObjectID="20543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102716" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 -824.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102716" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20543"/>
     <cge:Term_Ref ObjectID="10048"/>
    <cge:TPSR_Ref TObjectID="20543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 833.000000 -553.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20552"/>
     <cge:Term_Ref ObjectID="10066"/>
    <cge:TPSR_Ref TObjectID="20552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 833.000000 -553.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20552"/>
     <cge:Term_Ref ObjectID="10066"/>
    <cge:TPSR_Ref TObjectID="20552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 833.000000 -553.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20552"/>
     <cge:Term_Ref ObjectID="10066"/>
    <cge:TPSR_Ref TObjectID="20552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102726" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -553.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20554"/>
     <cge:Term_Ref ObjectID="28120"/>
    <cge:TPSR_Ref TObjectID="20554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102727" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -553.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20554"/>
     <cge:Term_Ref ObjectID="28120"/>
    <cge:TPSR_Ref TObjectID="20554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102725" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -553.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102725" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20554"/>
     <cge:Term_Ref ObjectID="28120"/>
    <cge:TPSR_Ref TObjectID="20554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20556"/>
     <cge:Term_Ref ObjectID="28128"/>
    <cge:TPSR_Ref TObjectID="20556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20556"/>
     <cge:Term_Ref ObjectID="28128"/>
    <cge:TPSR_Ref TObjectID="20556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20556"/>
     <cge:Term_Ref ObjectID="28128"/>
    <cge:TPSR_Ref TObjectID="20556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 478.000000 -21.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20558"/>
     <cge:Term_Ref ObjectID="28535"/>
    <cge:TPSR_Ref TObjectID="20558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 478.000000 -21.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20558"/>
     <cge:Term_Ref ObjectID="28535"/>
    <cge:TPSR_Ref TObjectID="20558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102768" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 478.000000 -21.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102768" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20558"/>
     <cge:Term_Ref ObjectID="28535"/>
    <cge:TPSR_Ref TObjectID="20558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -21.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20560"/>
     <cge:Term_Ref ObjectID="28539"/>
    <cge:TPSR_Ref TObjectID="20560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -21.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20560"/>
     <cge:Term_Ref ObjectID="28539"/>
    <cge:TPSR_Ref TObjectID="20560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102772" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -21.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20560"/>
     <cge:Term_Ref ObjectID="28539"/>
    <cge:TPSR_Ref TObjectID="20560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 -21.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20562"/>
     <cge:Term_Ref ObjectID="28543"/>
    <cge:TPSR_Ref TObjectID="20562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 -21.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20562"/>
     <cge:Term_Ref ObjectID="28543"/>
    <cge:TPSR_Ref TObjectID="20562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 -21.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20562"/>
     <cge:Term_Ref ObjectID="28543"/>
    <cge:TPSR_Ref TObjectID="20562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -21.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20564"/>
     <cge:Term_Ref ObjectID="28547"/>
    <cge:TPSR_Ref TObjectID="20564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -21.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20564"/>
     <cge:Term_Ref ObjectID="28547"/>
    <cge:TPSR_Ref TObjectID="20564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102780" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -21.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102780" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20564"/>
     <cge:Term_Ref ObjectID="28547"/>
    <cge:TPSR_Ref TObjectID="20564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102761" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -21.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102761" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20565"/>
     <cge:Term_Ref ObjectID="28549"/>
    <cge:TPSR_Ref TObjectID="20565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102762" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -21.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20565"/>
     <cge:Term_Ref ObjectID="28549"/>
    <cge:TPSR_Ref TObjectID="20565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102760" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -21.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102760" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20565"/>
     <cge:Term_Ref ObjectID="28549"/>
    <cge:TPSR_Ref TObjectID="20565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 -301.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20581"/>
     <cge:Term_Ref ObjectID="28581"/>
    <cge:TPSR_Ref TObjectID="20581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 -301.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20581"/>
     <cge:Term_Ref ObjectID="28581"/>
    <cge:TPSR_Ref TObjectID="20581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102800" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 -301.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20581"/>
     <cge:Term_Ref ObjectID="28581"/>
    <cge:TPSR_Ref TObjectID="20581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1100.000000 -24.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20567"/>
     <cge:Term_Ref ObjectID="28553"/>
    <cge:TPSR_Ref TObjectID="20567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1100.000000 -24.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20567"/>
     <cge:Term_Ref ObjectID="28553"/>
    <cge:TPSR_Ref TObjectID="20567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1100.000000 -24.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20567"/>
     <cge:Term_Ref ObjectID="28553"/>
    <cge:TPSR_Ref TObjectID="20567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1213.000000 -24.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20569"/>
     <cge:Term_Ref ObjectID="28557"/>
    <cge:TPSR_Ref TObjectID="20569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1213.000000 -24.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20569"/>
     <cge:Term_Ref ObjectID="28557"/>
    <cge:TPSR_Ref TObjectID="20569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1213.000000 -24.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20569"/>
     <cge:Term_Ref ObjectID="28557"/>
    <cge:TPSR_Ref TObjectID="20569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -24.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20571"/>
     <cge:Term_Ref ObjectID="28561"/>
    <cge:TPSR_Ref TObjectID="20571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -24.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20571"/>
     <cge:Term_Ref ObjectID="28561"/>
    <cge:TPSR_Ref TObjectID="20571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -24.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20571"/>
     <cge:Term_Ref ObjectID="28561"/>
    <cge:TPSR_Ref TObjectID="20571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102793" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1422.000000 -24.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102793" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20573"/>
     <cge:Term_Ref ObjectID="28565"/>
    <cge:TPSR_Ref TObjectID="20573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102794" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1422.000000 -24.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20573"/>
     <cge:Term_Ref ObjectID="28565"/>
    <cge:TPSR_Ref TObjectID="20573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102792" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1422.000000 -24.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102792" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20573"/>
     <cge:Term_Ref ObjectID="28565"/>
    <cge:TPSR_Ref TObjectID="20573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102797" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -24.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20575"/>
     <cge:Term_Ref ObjectID="28569"/>
    <cge:TPSR_Ref TObjectID="20575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -24.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20575"/>
     <cge:Term_Ref ObjectID="28569"/>
    <cge:TPSR_Ref TObjectID="20575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102796" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -24.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20575"/>
     <cge:Term_Ref ObjectID="28569"/>
    <cge:TPSR_Ref TObjectID="20575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20577"/>
     <cge:Term_Ref ObjectID="28573"/>
    <cge:TPSR_Ref TObjectID="20577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20577"/>
     <cge:Term_Ref ObjectID="28573"/>
    <cge:TPSR_Ref TObjectID="20577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102752" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20577"/>
     <cge:Term_Ref ObjectID="28573"/>
    <cge:TPSR_Ref TObjectID="20577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-102757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1718.000000 -47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20579"/>
     <cge:Term_Ref ObjectID="28577"/>
    <cge:TPSR_Ref TObjectID="20579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-102758" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1718.000000 -47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102758" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20579"/>
     <cge:Term_Ref ObjectID="28577"/>
    <cge:TPSR_Ref TObjectID="20579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-102756" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1718.000000 -47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20579"/>
     <cge:Term_Ref ObjectID="28577"/>
    <cge:TPSR_Ref TObjectID="20579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-102734" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -960.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20521"/>
     <cge:Term_Ref ObjectID="10005"/>
    <cge:TPSR_Ref TObjectID="20521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-102735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -960.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20521"/>
     <cge:Term_Ref ObjectID="10005"/>
    <cge:TPSR_Ref TObjectID="20521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-102736" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -960.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20521"/>
     <cge:Term_Ref ObjectID="10005"/>
    <cge:TPSR_Ref TObjectID="20521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-102737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -960.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20521"/>
     <cge:Term_Ref ObjectID="10005"/>
    <cge:TPSR_Ref TObjectID="20521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-102733" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -960.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102733" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20521"/>
     <cge:Term_Ref ObjectID="10005"/>
    <cge:TPSR_Ref TObjectID="20521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-102739" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -535.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20522"/>
     <cge:Term_Ref ObjectID="10006"/>
    <cge:TPSR_Ref TObjectID="20522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-102740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -535.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20522"/>
     <cge:Term_Ref ObjectID="10006"/>
    <cge:TPSR_Ref TObjectID="20522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-102741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -535.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20522"/>
     <cge:Term_Ref ObjectID="10006"/>
    <cge:TPSR_Ref TObjectID="20522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-102742" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -535.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102742" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20522"/>
     <cge:Term_Ref ObjectID="10006"/>
    <cge:TPSR_Ref TObjectID="20522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-102738" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -535.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102738" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20522"/>
     <cge:Term_Ref ObjectID="10006"/>
    <cge:TPSR_Ref TObjectID="20522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-102744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1778.000000 -535.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20523"/>
     <cge:Term_Ref ObjectID="10007"/>
    <cge:TPSR_Ref TObjectID="20523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-102745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1778.000000 -535.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20523"/>
     <cge:Term_Ref ObjectID="10007"/>
    <cge:TPSR_Ref TObjectID="20523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-102746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1778.000000 -535.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20523"/>
     <cge:Term_Ref ObjectID="10007"/>
    <cge:TPSR_Ref TObjectID="20523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-102747" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1778.000000 -535.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102747" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20523"/>
     <cge:Term_Ref ObjectID="10007"/>
    <cge:TPSR_Ref TObjectID="20523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-102743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1778.000000 -535.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20523"/>
     <cge:Term_Ref ObjectID="10007"/>
    <cge:TPSR_Ref TObjectID="20523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-102707" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.000000 -655.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102707" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20582"/>
     <cge:Term_Ref ObjectID="28583"/>
    <cge:TPSR_Ref TObjectID="20582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-102724" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.500000 -655.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102724" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20583"/>
     <cge:Term_Ref ObjectID="28590"/>
    <cge:TPSR_Ref TObjectID="20583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="0" MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="OT4  " rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 452.000000 -1151.000000) translate(0,12)">OT4   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/></g>
   <g href="cx_索引_接线图_省地共调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1653" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1653" cy="-992" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1813" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1866" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1708" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1866" cy="-992" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_14ec300">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -1118.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d72fa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 405.000000 -583.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d01d90">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 -1033.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1952ff0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.000000 -584.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_152e190">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -1118.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 78.000000 -1150.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116478" ratioFlag="0">
    <text fill="rgb(127,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 148.000000 -1037.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116478" ObjectName="CX_HTP:CX_HTP_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116479" ratioFlag="0">
    <text fill="rgb(127,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 147.000000 -955.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116479" ObjectName="CX_HTP:CX_HTP_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-1113 1648,-1124 1658,-1124 1653,-1113 1653,-1114 1653,-1113 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-1113 1861,-1124 1871,-1124 1866,-1113 1866,-1114 1866,-1113 " stroke="rgb(60,120,255)"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="151" x="90" y="-1209"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="41" y="-1226"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HTP.CX_HTP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28585"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 -615.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 -615.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20582" ObjectName="TF-CX_HTP.CX_HTP_1T"/>
    <cge:TPSR_Ref TObjectID="20582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_HTP.CX_HTP_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28589"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1127.000000 -615.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1127.000000 -615.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20583" ObjectName="TF-CX_HTP.CX_HTP_2T"/>
    <cge:TPSR_Ref TObjectID="20583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1637.000000 -999.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1637.000000 -999.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1850.000000 -999.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1850.000000 -999.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HTP"/>
</svg>