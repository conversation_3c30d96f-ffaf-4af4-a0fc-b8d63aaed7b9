<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-70" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1198 2146 1279">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="42" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="47" x2="28" y1="60" y2="60"/>
    <polyline points="29,89 31,89 33,88 34,88 36,87 37,86 39,85 40,83 41,81 41,80 42,78 42,76 42,74 41,72 41,71 40,69 39,68 37,66 36,65 34,64 33,64 31,63 29,63 27,63 25,64 24,64 22,65 21,66 19,68 18,69 17,71 17,72 16,74 16,76 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="16" x2="28" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="29" x2="29" y1="89" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="55" y2="47"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="76" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="29"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="28" x2="11" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="48" x2="45" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="60" y2="35"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape178">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="131" y2="123"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="107"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="9,120 31,98 31,86 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="28" y1="122" y2="122"/>
    <circle cx="30" cy="71" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="71" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="27" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="77" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="43" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="18" y2="18"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,49 6,49 6,20 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,6 26,19 39,19 32,6 32,7 32,6 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="34" y2="0"/>
    <circle cx="30" cy="49" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="29" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="54"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape147">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape40_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape47_0">
    <polyline points="5,24 12,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="5" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.875" x1="12" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="28" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="28" y1="12" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="29" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="29" y1="24" y2="24"/>
    <polyline points="23,28 23,7 " stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape47_1">
    <polyline points="23,24 23,4 " stroke-width="1"/>
    <polyline points="5,24 12,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="5" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.875" x1="12" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="28" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="28" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="29" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="29" y1="24" y2="24"/>
   </symbol>
   <symbol id="switch2:shape47-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="29" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="29" y1="24" y2="24"/>
    <polyline points="23,28 23,7 " stroke-width="1"/>
    <polyline points="5,24 12,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="5" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.875" x1="12" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="28" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="28" y1="11" y2="4"/>
   </symbol>
   <symbol id="switch2:shape47-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="29" y1="24" y2="24"/>
    <polyline points="23,24 23,4 " stroke-width="1"/>
    <polyline points="5,24 12,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="5" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.875" x1="12" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="28" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="28" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="29" y1="24" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape104">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="41" y2="31"/>
    <rect height="13" stroke-width="1" width="5" x="37" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="12" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="39" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="44" y1="19" y2="28"/>
    <ellipse cx="19" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <ellipse cx="19" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <ellipse cx="8" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="37" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="38" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="36" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
   </symbol>
   <symbol id="voltageTransformer:shape57">
    <circle cx="18" cy="16" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.374294" x1="45" x2="39" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="45" x2="39" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="39" x2="39" y1="33" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="11" y1="14" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="21" x2="16" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="16" y1="9" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="12" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="22" x2="17" y1="45" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="17" y1="34" y2="40"/>
    <ellipse cx="38" cy="28" fillStyle="0" rx="16.5" ry="16" stroke-width="0.340267"/>
    <circle cx="17" cy="37" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="14"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_555f140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_555fd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_5560660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_5561300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_5562240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_5562b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5563280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_5563d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_5564f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_5564f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5566940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5566940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5567ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5567ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_5568860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5569e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_556a810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_556b6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_556bfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_556d790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_556e2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_556ea30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_556f1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_55702d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5570c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5571740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_5572100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_55735a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_5574110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_5575020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_5575a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_5584220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_55770d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_5577d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_5578d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1289" width="2156" x="3112" y="-1203"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3678" x2="3736" y1="-201" y2="-201"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3736" x2="3736" y1="-199" y2="-199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3736" x2="3736" y1="-202" y2="-198"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.637931" x1="4770" x2="4807" y1="-201" y2="-201"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4807" x2="4807" y1="-199" y2="-199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4807" x2="4807" y1="-203" y2="-199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5261" x2="5261" y1="-303" y2="-289"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4175" x2="4186" y1="79" y2="79"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4175" x2="4175" y1="62" y2="62"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4178" x2="4390" y1="66" y2="66"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4143" x2="4143" y1="71" y2="71"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4175" x2="4405" y1="71" y2="71"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4140" x2="4328" y1="60" y2="60"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4072" x2="4072" y1="58" y2="58"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4121" x2="4463" y1="58" y2="58"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4463" x2="4463" y1="63" y2="63"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(21,40,56)" stroke-width="1" x1="4258" x2="4316" y1="69" y2="69"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-597"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(255,255,0)" stroke-width="0.386474" width="24" x="4493" y="-1059"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(255,255,0)" stroke-width="0.386474" width="24" x="4027" y="-1059"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(255,255,0)" stroke-width="0.386474" width="24" x="4782" y="-1039"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-45563">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.000000 -761.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7789" ObjectName="SW-CX_ZW.CX_ZW_3021SW"/>
     <cge:Meas_Ref ObjectId="45563"/>
    <cge:TPSR_Ref TObjectID="7789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45549">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4008.000000 -761.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7784" ObjectName="SW-CX_ZW.CX_ZW_3011SW"/>
     <cge:Meas_Ref ObjectId="45549"/>
    <cge:TPSR_Ref TObjectID="7784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -855.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7795" ObjectName="SW-CX_ZW.CX_ZW_3901SW"/>
     <cge:Meas_Ref ObjectId="45577"/>
    <cge:TPSR_Ref TObjectID="7795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126592">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.000000 -834.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23134" ObjectName="SW-CX_ZW.CX_ZW_34417SW"/>
     <cge:Meas_Ref ObjectId="126592"/>
    <cge:TPSR_Ref TObjectID="23134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126617">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23063" ObjectName="SW-CX_ZW.CX_ZW_3421SW"/>
     <cge:Meas_Ref ObjectId="126617"/>
    <cge:TPSR_Ref TObjectID="23063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126618">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -925.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23066" ObjectName="SW-CX_ZW.CX_ZW_3426SW"/>
     <cge:Meas_Ref ObjectId="126618"/>
    <cge:TPSR_Ref TObjectID="23066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126622">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4445.000000 -973.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23067" ObjectName="SW-CX_ZW.CX_ZW_34267SW"/>
     <cge:Meas_Ref ObjectId="126622"/>
    <cge:TPSR_Ref TObjectID="23067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126621">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4445.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23065" ObjectName="SW-CX_ZW.CX_ZW_34260SW"/>
     <cge:Meas_Ref ObjectId="126621"/>
    <cge:TPSR_Ref TObjectID="23065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126620">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4445.000000 -867.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23064" ObjectName="SW-CX_ZW.CX_ZW_34217SW"/>
     <cge:Meas_Ref ObjectId="126620"/>
    <cge:TPSR_Ref TObjectID="23064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126619">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -1048.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23069" ObjectName="SW-CX_ZW.CX_ZW_3423SW"/>
     <cge:Meas_Ref ObjectId="126619"/>
    <cge:TPSR_Ref TObjectID="23069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45308">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -818.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7758" ObjectName="SW-CX_ZW.CX_ZW_3411SW"/>
     <cge:Meas_Ref ObjectId="45308"/>
    <cge:TPSR_Ref TObjectID="7758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45309">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7759" ObjectName="SW-CX_ZW.CX_ZW_3416SW"/>
     <cge:Meas_Ref ObjectId="45309"/>
    <cge:TPSR_Ref TObjectID="7759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 -972.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7760" ObjectName="SW-CX_ZW.CX_ZW_34167SW"/>
     <cge:Meas_Ref ObjectId="45310"/>
    <cge:TPSR_Ref TObjectID="7760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126605">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 -915.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23070" ObjectName="SW-CX_ZW.CX_ZW_34160SW"/>
     <cge:Meas_Ref ObjectId="126605"/>
    <cge:TPSR_Ref TObjectID="23070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 -866.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23071" ObjectName="SW-CX_ZW.CX_ZW_34117SW"/>
     <cge:Meas_Ref ObjectId="126604"/>
    <cge:TPSR_Ref TObjectID="23071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45311">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -1048.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7761" ObjectName="SW-CX_ZW.CX_ZW_3413SW"/>
     <cge:Meas_Ref ObjectId="45311"/>
    <cge:TPSR_Ref TObjectID="7761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45551">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 -448.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7786" ObjectName="SW-CX_ZW.CX_ZW_0011SW"/>
     <cge:Meas_Ref ObjectId="45551"/>
    <cge:TPSR_Ref TObjectID="7786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126748">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 -444.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23135" ObjectName="SW-CX_ZW.CX_ZW_0022SW"/>
     <cge:Meas_Ref ObjectId="126748"/>
    <cge:TPSR_Ref TObjectID="23135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7763" ObjectName="SW-CX_ZW.CX_ZW_3431SW"/>
     <cge:Meas_Ref ObjectId="45331"/>
    <cge:TPSR_Ref TObjectID="7763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45332">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -924.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7764" ObjectName="SW-CX_ZW.CX_ZW_3436SW"/>
     <cge:Meas_Ref ObjectId="45332"/>
    <cge:TPSR_Ref TObjectID="7764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45333">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4734.000000 -973.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7765" ObjectName="SW-CX_ZW.CX_ZW_34367SW"/>
     <cge:Meas_Ref ObjectId="45333"/>
    <cge:TPSR_Ref TObjectID="7765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126781">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4734.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23061" ObjectName="SW-CX_ZW.CX_ZW_34360SW"/>
     <cge:Meas_Ref ObjectId="126781"/>
    <cge:TPSR_Ref TObjectID="23061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126780">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4734.000000 -867.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7766" ObjectName="SW-CX_ZW.CX_ZW_34317SW"/>
     <cge:Meas_Ref ObjectId="126780"/>
    <cge:TPSR_Ref TObjectID="7766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 -1028.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23062" ObjectName="SW-CX_ZW.CX_ZW_3433SW"/>
     <cge:Meas_Ref ObjectId="45334"/>
    <cge:TPSR_Ref TObjectID="23062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.000000 -911.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7796" ObjectName="SW-CX_ZW.CX_ZW_39017SW"/>
     <cge:Meas_Ref ObjectId="45578"/>
    <cge:TPSR_Ref TObjectID="7796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126528">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4025.000000 -750.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23073" ObjectName="SW-CX_ZW.CX_ZW_30117SW"/>
     <cge:Meas_Ref ObjectId="126528"/>
    <cge:TPSR_Ref TObjectID="23073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126555">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -749.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23072" ObjectName="SW-CX_ZW.CX_ZW_30217SW"/>
     <cge:Meas_Ref ObjectId="126555"/>
    <cge:TPSR_Ref TObjectID="23072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45579">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -437.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7797" ObjectName="SW-CX_ZW.CX_ZW_0901SW"/>
     <cge:Meas_Ref ObjectId="45579"/>
    <cge:TPSR_Ref TObjectID="7797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45580">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4702.000000 -437.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7798" ObjectName="SW-CX_ZW.CX_ZW_0902SW"/>
     <cge:Meas_Ref ObjectId="45580"/>
    <cge:TPSR_Ref TObjectID="7798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45354">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3565.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7768" ObjectName="SW-CX_ZW.CX_ZW_0411SW"/>
     <cge:Meas_Ref ObjectId="45354"/>
    <cge:TPSR_Ref TObjectID="7768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3565.000000 -269.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23076" ObjectName="SW-CX_ZW.CX_ZW_0416SW"/>
     <cge:Meas_Ref ObjectId="126323"/>
    <cge:TPSR_Ref TObjectID="23076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45393">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7772" ObjectName="SW-CX_ZW.CX_ZW_0441SW"/>
     <cge:Meas_Ref ObjectId="45393"/>
    <cge:TPSR_Ref TObjectID="7772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126366">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -269.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23081" ObjectName="SW-CX_ZW.CX_ZW_0446SW"/>
     <cge:Meas_Ref ObjectId="126366"/>
    <cge:TPSR_Ref TObjectID="23081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7770" ObjectName="SW-CX_ZW.CX_ZW_0431SW"/>
     <cge:Meas_Ref ObjectId="45374"/>
    <cge:TPSR_Ref TObjectID="7770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126344">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 -269.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23080" ObjectName="SW-CX_ZW.CX_ZW_0436SW"/>
     <cge:Meas_Ref ObjectId="126344"/>
    <cge:TPSR_Ref TObjectID="23080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.000000 -155.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7776" ObjectName="SW-CX_ZW.CX_ZW_0421SW"/>
     <cge:Meas_Ref ObjectId="45431"/>
    <cge:TPSR_Ref TObjectID="7776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 -269.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23079" ObjectName="SW-CX_ZW.CX_ZW_0423SW"/>
     <cge:Meas_Ref ObjectId="126406"/>
    <cge:TPSR_Ref TObjectID="23079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3726.000000 -178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23078" ObjectName="SW-CX_ZW.CX_ZW_0426SW"/>
     <cge:Meas_Ref ObjectId="126408"/>
    <cge:TPSR_Ref TObjectID="23078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126693">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4234.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23089" ObjectName="SW-CX_ZW.CX_ZW_0121SW"/>
     <cge:Meas_Ref ObjectId="126693"/>
    <cge:TPSR_Ref TObjectID="23089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23090" ObjectName="SW-CX_ZW.CX_ZW_0122SW"/>
     <cge:Meas_Ref ObjectId="126687"/>
    <cge:TPSR_Ref TObjectID="23090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80960">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4473.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17904" ObjectName="SW-CX_ZW.CX_ZW_0452SW"/>
     <cge:Meas_Ref ObjectId="80960"/>
    <cge:TPSR_Ref TObjectID="17904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126598">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4473.000000 -269.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23082" ObjectName="SW-CX_ZW.CX_ZW_0456SW"/>
     <cge:Meas_Ref ObjectId="126598"/>
    <cge:TPSR_Ref TObjectID="23082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7782" ObjectName="SW-CX_ZW.CX_ZW_0462SW"/>
     <cge:Meas_Ref ObjectId="45474"/>
    <cge:TPSR_Ref TObjectID="7782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126466">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -269.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23083" ObjectName="SW-CX_ZW.CX_ZW_0466SW"/>
     <cge:Meas_Ref ObjectId="126466"/>
    <cge:TPSR_Ref TObjectID="23083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7778" ObjectName="SW-CX_ZW.CX_ZW_0472SW"/>
     <cge:Meas_Ref ObjectId="45443"/>
    <cge:TPSR_Ref TObjectID="7778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126425">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 -269.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23084" ObjectName="SW-CX_ZW.CX_ZW_0473SW"/>
     <cge:Meas_Ref ObjectId="126425"/>
    <cge:TPSR_Ref TObjectID="23084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126427">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 -178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23085" ObjectName="SW-CX_ZW.CX_ZW_0476SW"/>
     <cge:Meas_Ref ObjectId="126427"/>
    <cge:TPSR_Ref TObjectID="23085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7774" ObjectName="SW-CX_ZW.CX_ZW_0482SW"/>
     <cge:Meas_Ref ObjectId="45412"/>
    <cge:TPSR_Ref TObjectID="7774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126388">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 -269.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23087" ObjectName="SW-CX_ZW.CX_ZW_0486SW"/>
     <cge:Meas_Ref ObjectId="126388"/>
    <cge:TPSR_Ref TObjectID="23087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45455">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5059.000000 -363.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7780" ObjectName="SW-CX_ZW.CX_ZW_0492SW"/>
     <cge:Meas_Ref ObjectId="45455"/>
    <cge:TPSR_Ref TObjectID="7780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126445">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5059.000000 -269.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23088" ObjectName="SW-CX_ZW.CX_ZW_0496SW"/>
     <cge:Meas_Ref ObjectId="126445"/>
    <cge:TPSR_Ref TObjectID="23088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243843">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.000000 -362.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40982" ObjectName="SW-CX_ZW.CX_ZW_0511SW"/>
     <cge:Meas_Ref ObjectId="243843"/>
    <cge:TPSR_Ref TObjectID="40982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243844">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.000000 -227.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40983" ObjectName="SW-CX_ZW.CX_ZW_0516SW"/>
     <cge:Meas_Ref ObjectId="243844"/>
    <cge:TPSR_Ref TObjectID="40983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243845">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -337.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40980" ObjectName="SW-CX_ZW.CX_ZW_05117SW"/>
     <cge:Meas_Ref ObjectId="243845"/>
    <cge:TPSR_Ref TObjectID="40980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243847">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -196.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40984" ObjectName="SW-CX_ZW.CX_ZW_05167SW"/>
     <cge:Meas_Ref ObjectId="243847"/>
    <cge:TPSR_Ref TObjectID="40984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126428">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4777.000000 -178.000000)" xlink:href="#switch2:shape47_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23086" ObjectName="SW-CX_ZW.CX_ZW_04767SW"/>
     <cge:Meas_Ref ObjectId="126428"/>
    <cge:TPSR_Ref TObjectID="23086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126409">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3706.000000 -178.000000)" xlink:href="#switch2:shape47_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23133" ObjectName="SW-CX_ZW.CX_ZW_04267SW"/>
     <cge:Meas_Ref ObjectId="126409"/>
    <cge:TPSR_Ref TObjectID="23133"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZW.CX_ZW_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3540,-421 4307,-421 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7756" ObjectName="BS-CX_ZW.CX_ZW_9IM"/>
    <cge:TPSR_Ref TObjectID="7756"/></metadata>
   <polyline fill="none" opacity="0" points="3540,-421 4307,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZW.CX_ZW_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-815 4807,-815 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7755" ObjectName="BS-CX_ZW.CX_ZW_3IM"/>
    <cge:TPSR_Ref TObjectID="7755"/></metadata>
   <polyline fill="none" opacity="0" points="3852,-815 4807,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZW.CX_ZW_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-421 5153,-421 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23137" ObjectName="BS-CX_ZW.CX_ZW_9IIM"/>
    <cge:TPSR_Ref TObjectID="23137"/></metadata>
   <polyline fill="none" opacity="0" points="4370,-421 5153,-421 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_ZW.CX_ZW_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -71.000000)" xlink:href="#capacitor:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41470" ObjectName="CB-CX_ZW.CX_ZW_Cb1"/>
    <cge:TPSR_Ref TObjectID="41470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_ZW.CX_ZW_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4777.000000 -73.000000)" xlink:href="#capacitor:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41471" ObjectName="CB-CX_ZW.CX_ZW_Cb2"/>
    <cge:TPSR_Ref TObjectID="41471"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_ZW.CX_ZW_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10898"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -614.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -614.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7799" ObjectName="TF-CX_ZW.CX_ZW_1T"/>
    <cge:TPSR_Ref TObjectID="7799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_ZW.CX_ZW_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28431"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 -614.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 -614.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7800" ObjectName="TF-CX_ZW.CX_ZW_2T"/>
    <cge:TPSR_Ref TObjectID="7800"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_6a2a9d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -140.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a48430">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -994.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a73210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -555.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a73ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 -555.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a74bd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -1006.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a773a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3847.000000 -883.000000)" xlink:href="#lightningRod:shape178"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a91b80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.000000 -993.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a97d20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 -920.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6aaa330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.000000 -498.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6ab3bb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 -498.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6ad3090">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 -140.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6ade160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3587.000000 -217.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6ae1170">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -211.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6ae1e50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4008.000000 -217.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6ae8a40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 -91.000000)" xlink:href="#lightningRod:shape147"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6aeaca0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -152.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_541e8d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3725.000000 -224.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_5422690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -66.000000)" xlink:href="#lightningRod:shape178"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_5432cb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4461.000000 -140.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_543dd80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -217.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_5442320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4598.000000 -140.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_544d660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -217.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_545ace0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 -224.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_5461260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 -140.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_546c330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4944.000000 -217.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_546fce0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.000000 -140.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_547b020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 -217.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_548b020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -536.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_548bae0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.000000 -536.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_54b33a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -140.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-45235" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -638.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45235" ObjectName="CX_ZW:CX_ZW_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3228.500000 -1117.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-59631" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4648.000000 -638.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59631" ObjectName="CX_ZW:CX_ZW_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-126852" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -660.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126852" ObjectName="CX_ZW:CX_ZW_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-126853" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4648.000000 -658.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126853" ObjectName="CX_ZW:CX_ZW_2T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200701" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3249.538462 -983.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200701" ObjectName="CX_ZW:CX_ZW_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200702" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3249.538462 -940.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200702" ObjectName="CX_ZW:CX_ZW_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-45244" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4467.000000 -771.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45244" ObjectName="CX_ZW:CX_ZW_302BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-45245" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -751.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45245" ObjectName="CX_ZW:CX_ZW_302BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-45236" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -733.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45236" ObjectName="CX_ZW:CX_ZW_302BK_Ia"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45230" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -543.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7785"/>
     <cge:Term_Ref ObjectID="10862"/>
    <cge:TPSR_Ref TObjectID="7785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45231" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -543.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7785"/>
     <cge:Term_Ref ObjectID="10862"/>
    <cge:TPSR_Ref TObjectID="7785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45222" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -543.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45222" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7785"/>
     <cge:Term_Ref ObjectID="10862"/>
    <cge:TPSR_Ref TObjectID="7785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-45285" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -850.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7755"/>
     <cge:Term_Ref ObjectID="6323"/>
    <cge:TPSR_Ref TObjectID="7755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-45286" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -850.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7755"/>
     <cge:Term_Ref ObjectID="6323"/>
    <cge:TPSR_Ref TObjectID="7755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-45287" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -850.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7755"/>
     <cge:Term_Ref ObjectID="6323"/>
    <cge:TPSR_Ref TObjectID="7755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-45289" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -850.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45289" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7755"/>
     <cge:Term_Ref ObjectID="6323"/>
    <cge:TPSR_Ref TObjectID="7755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-45288" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -850.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7755"/>
     <cge:Term_Ref ObjectID="6323"/>
    <cge:TPSR_Ref TObjectID="7755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3909.000000 -1097.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7757"/>
     <cge:Term_Ref ObjectID="8867"/>
    <cge:TPSR_Ref TObjectID="7757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3909.000000 -1097.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7757"/>
     <cge:Term_Ref ObjectID="8867"/>
    <cge:TPSR_Ref TObjectID="7757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3909.000000 -1097.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7757"/>
     <cge:Term_Ref ObjectID="8867"/>
    <cge:TPSR_Ref TObjectID="7757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45168" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -1097.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7762"/>
     <cge:Term_Ref ObjectID="10816"/>
    <cge:TPSR_Ref TObjectID="7762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -1097.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7762"/>
     <cge:Term_Ref ObjectID="10816"/>
    <cge:TPSR_Ref TObjectID="7762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -1097.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7762"/>
     <cge:Term_Ref ObjectID="10816"/>
    <cge:TPSR_Ref TObjectID="7762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45173" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.000000 -32.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45173" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7767"/>
     <cge:Term_Ref ObjectID="10826"/>
    <cge:TPSR_Ref TObjectID="7767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45174" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.000000 -32.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7767"/>
     <cge:Term_Ref ObjectID="10826"/>
    <cge:TPSR_Ref TObjectID="7767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.000000 -32.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7767"/>
     <cge:Term_Ref ObjectID="10826"/>
    <cge:TPSR_Ref TObjectID="7767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45194" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -33.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7775"/>
     <cge:Term_Ref ObjectID="10842"/>
    <cge:TPSR_Ref TObjectID="7775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45190" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -33.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7775"/>
     <cge:Term_Ref ObjectID="10842"/>
    <cge:TPSR_Ref TObjectID="7775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45178" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -32.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7769"/>
     <cge:Term_Ref ObjectID="10830"/>
    <cge:TPSR_Ref TObjectID="7769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -32.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7769"/>
     <cge:Term_Ref ObjectID="10830"/>
    <cge:TPSR_Ref TObjectID="7769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45175" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -32.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7769"/>
     <cge:Term_Ref ObjectID="10830"/>
    <cge:TPSR_Ref TObjectID="7769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45183" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4053.000000 -32.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7771"/>
     <cge:Term_Ref ObjectID="10834"/>
    <cge:TPSR_Ref TObjectID="7771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45184" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4053.000000 -32.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45184" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7771"/>
     <cge:Term_Ref ObjectID="10834"/>
    <cge:TPSR_Ref TObjectID="7771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45180" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4053.000000 -32.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45180" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7771"/>
     <cge:Term_Ref ObjectID="10834"/>
    <cge:TPSR_Ref TObjectID="7771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-80956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4492.000000 -33.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17903"/>
     <cge:Term_Ref ObjectID="24726"/>
    <cge:TPSR_Ref TObjectID="17903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4492.000000 -33.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17903"/>
     <cge:Term_Ref ObjectID="24726"/>
    <cge:TPSR_Ref TObjectID="17903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4492.000000 -33.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17903"/>
     <cge:Term_Ref ObjectID="24726"/>
    <cge:TPSR_Ref TObjectID="17903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45207" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -33.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45207" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7781"/>
     <cge:Term_Ref ObjectID="10854"/>
    <cge:TPSR_Ref TObjectID="7781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45208" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -33.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7781"/>
     <cge:Term_Ref ObjectID="10854"/>
    <cge:TPSR_Ref TObjectID="7781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -33.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7781"/>
     <cge:Term_Ref ObjectID="10854"/>
    <cge:TPSR_Ref TObjectID="7781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4803.000000 -33.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7777"/>
     <cge:Term_Ref ObjectID="10846"/>
    <cge:TPSR_Ref TObjectID="7777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45195" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4803.000000 -33.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45195" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7777"/>
     <cge:Term_Ref ObjectID="10846"/>
    <cge:TPSR_Ref TObjectID="7777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4936.000000 -33.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7773"/>
     <cge:Term_Ref ObjectID="10838"/>
    <cge:TPSR_Ref TObjectID="7773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4936.000000 -33.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7773"/>
     <cge:Term_Ref ObjectID="10838"/>
    <cge:TPSR_Ref TObjectID="7773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4936.000000 -33.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7773"/>
     <cge:Term_Ref ObjectID="10838"/>
    <cge:TPSR_Ref TObjectID="7773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45202" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5073.000000 -33.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45202" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7779"/>
     <cge:Term_Ref ObjectID="10850"/>
    <cge:TPSR_Ref TObjectID="7779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45203" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5073.000000 -33.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45203" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7779"/>
     <cge:Term_Ref ObjectID="10850"/>
    <cge:TPSR_Ref TObjectID="7779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45199" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5073.000000 -33.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45199" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7779"/>
     <cge:Term_Ref ObjectID="10850"/>
    <cge:TPSR_Ref TObjectID="7779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-126142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -328.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23075"/>
     <cge:Term_Ref ObjectID="32511"/>
    <cge:TPSR_Ref TObjectID="23075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-126143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -328.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23075"/>
     <cge:Term_Ref ObjectID="32511"/>
    <cge:TPSR_Ref TObjectID="23075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-126139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -328.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23075"/>
     <cge:Term_Ref ObjectID="32511"/>
    <cge:TPSR_Ref TObjectID="23075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45217" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -773.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45217" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7783"/>
     <cge:Term_Ref ObjectID="10858"/>
    <cge:TPSR_Ref TObjectID="7783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45218" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -773.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7783"/>
     <cge:Term_Ref ObjectID="10858"/>
    <cge:TPSR_Ref TObjectID="7783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -773.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7783"/>
     <cge:Term_Ref ObjectID="10858"/>
    <cge:TPSR_Ref TObjectID="7783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-243916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -35.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40981"/>
     <cge:Term_Ref ObjectID="62080"/>
    <cge:TPSR_Ref TObjectID="40981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -35.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40981"/>
     <cge:Term_Ref ObjectID="62080"/>
    <cge:TPSR_Ref TObjectID="40981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -35.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40981"/>
     <cge:Term_Ref ObjectID="62080"/>
    <cge:TPSR_Ref TObjectID="40981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-45292" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -552.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7756"/>
     <cge:Term_Ref ObjectID="8866"/>
    <cge:TPSR_Ref TObjectID="7756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-45293" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -552.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7756"/>
     <cge:Term_Ref ObjectID="8866"/>
    <cge:TPSR_Ref TObjectID="7756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-45294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -552.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7756"/>
     <cge:Term_Ref ObjectID="8866"/>
    <cge:TPSR_Ref TObjectID="7756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-198896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -552.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7756"/>
     <cge:Term_Ref ObjectID="8866"/>
    <cge:TPSR_Ref TObjectID="7756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-45296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -552.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7756"/>
     <cge:Term_Ref ObjectID="8866"/>
    <cge:TPSR_Ref TObjectID="7756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-45297" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -552.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7756"/>
     <cge:Term_Ref ObjectID="8866"/>
    <cge:TPSR_Ref TObjectID="7756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-45298" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -552.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7756"/>
     <cge:Term_Ref ObjectID="8866"/>
    <cge:TPSR_Ref TObjectID="7756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-45295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -552.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7756"/>
     <cge:Term_Ref ObjectID="8866"/>
    <cge:TPSR_Ref TObjectID="7756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-126811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -550.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23137"/>
     <cge:Term_Ref ObjectID="32516"/>
    <cge:TPSR_Ref TObjectID="23137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-126812" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -550.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23137"/>
     <cge:Term_Ref ObjectID="32516"/>
    <cge:TPSR_Ref TObjectID="23137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-126813" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -550.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126813" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23137"/>
     <cge:Term_Ref ObjectID="32516"/>
    <cge:TPSR_Ref TObjectID="23137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-198897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -550.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23137"/>
     <cge:Term_Ref ObjectID="32516"/>
    <cge:TPSR_Ref TObjectID="23137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-126815" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -550.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23137"/>
     <cge:Term_Ref ObjectID="32516"/>
    <cge:TPSR_Ref TObjectID="23137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-126816" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -550.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126816" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23137"/>
     <cge:Term_Ref ObjectID="32516"/>
    <cge:TPSR_Ref TObjectID="23137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-126817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -550.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23137"/>
     <cge:Term_Ref ObjectID="32516"/>
    <cge:TPSR_Ref TObjectID="23137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-126814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -550.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="126814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23137"/>
     <cge:Term_Ref ObjectID="32516"/>
    <cge:TPSR_Ref TObjectID="23137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-45257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -537.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7790"/>
     <cge:Term_Ref ObjectID="10872"/>
    <cge:TPSR_Ref TObjectID="7790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-45258" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -537.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45258" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7790"/>
     <cge:Term_Ref ObjectID="10872"/>
    <cge:TPSR_Ref TObjectID="7790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-45249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -537.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="45249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7790"/>
     <cge:Term_Ref ObjectID="10872"/>
    <cge:TPSR_Ref TObjectID="7790"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3240" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3240" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3192" y="-1193"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3192" y="-1193"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3928" y="-901"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3928" y="-901"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4398" y="-903"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4398" y="-903"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="4683" y="-903"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="4683" y="-903"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3611" y="-344"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3611" y="-344"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3749" y="-344"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3749" y="-344"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3855" y="-344"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3855" y="-344"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4027" y="-344"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4027" y="-344"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4324" y="-365"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4324" y="-365"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4519" y="-344"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4519" y="-344"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4656" y="-344"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4656" y="-344"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4820" y="-344"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4820" y="-344"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4968" y="-344"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4968" y="-344"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5105" y="-344"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5105" y="-344"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4056" y="-699"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4056" y="-699"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="4580" y="-692"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="4580" y="-692"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3485" y="-1152"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3485" y="-1152"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3485" y="-1187"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3485" y="-1187"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="84" x="3203" y="-769"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="84" x="3203" y="-769"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3406,-1182 3403,-1185 3403,-1132 3406,-1135 3406,-1182" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3406,-1182 3403,-1185 3454,-1185 3451,-1182 3406,-1182" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3406,-1135 3403,-1132 3454,-1132 3451,-1135 3406,-1135" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3451,-1182 3454,-1185 3454,-1132 3451,-1135 3451,-1182" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="3406" y="-1182"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3406" y="-1182"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4176" y="-314"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4176" y="-314"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="26" qtmmishow="hidden" width="76" x="3207" y="-720"/>
    </a>
   <metadata/><rect fill="white" height="26" opacity="0" stroke="white" transform="" width="76" x="3207" y="-720"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3240" y="-1176"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3192" y="-1193"/></g>
   <g href="35kV子午变35kV白子东线341断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3928" y="-901"/></g>
   <g href="35kV子午变35kV备用出线342断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4398" y="-903"/></g>
   <g href="35kV子午变35kV子地线343断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="4683" y="-903"/></g>
   <g href="35kV子午变10kV云龙线041断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3611" y="-344"/></g>
   <g href="35kV子午变10kV1号电容器组042断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3749" y="-344"/></g>
   <g href="35kV子午变10kV子午Ⅰ回线043断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3855" y="-344"/></g>
   <g href="35kV子午变10kV以口线044断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4027" y="-344"/></g>
   <g href="35kV子午变10kV备用电源自投装置012断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4324" y="-365"/></g>
   <g href="35kV子午变10kV爱尔法专线045断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4519" y="-344"/></g>
   <g href="35kV子午变10kV中本线046断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4656" y="-344"/></g>
   <g href="35kV子午变10kV2号电容器组047断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4820" y="-344"/></g>
   <g href="35kV子午变10kV红卫线048断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4968" y="-344"/></g>
   <g href="35kV子午变10kV子宜线049断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5105" y="-344"/></g>
   <g href="35kV子午变35kV1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4056" y="-699"/></g>
   <g href="35kV子午变35kV2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="4580" y="-692"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3485" y="-1152"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3485" y="-1187"/></g>
   <g href="35kV子午变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="84" x="3203" y="-769"/></g>
   <g href="AVC子午站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3406" y="-1182"/></g>
   <g href="35kV子午变10kV山嘴子线051断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4176" y="-314"/></g>
   <g href="35kV子午变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="26" qtmmishow="hidden" width="76" x="3207" y="-720"/></g>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3865,-268 3879,-261 3865,-254 3865,-268 " stroke="rgb(50,205,50)" stroke-width="0.8"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-219 3928,-233 3935,-219 3921,-219 " stroke="rgb(50,205,50)" stroke-width="0.8"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_6a491e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4521.000000 -1044.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a61190">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 -1044.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a88c70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4810.000000 -1024.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a943d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -955.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6aa7730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 -533.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6ab13a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 -533.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6ae7170">
    <use class="BV-0KV" transform="matrix(-0.000000 0.571429 0.534483 0.000000 3877.000000 -139.000000)" xlink:href="#voltageTransformer:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZW.CX_ZW_041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3588.000000 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34119" ObjectName="EC-CX_ZW.CX_ZW_041Ld"/>
    <cge:TPSR_Ref TObjectID="34119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZW.CX_ZW_044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34121" ObjectName="EC-CX_ZW.CX_ZW_044Ld"/>
    <cge:TPSR_Ref TObjectID="34121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZW.CX_ZW_043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34120" ObjectName="EC-CX_ZW.CX_ZW_043Ld"/>
    <cge:TPSR_Ref TObjectID="34120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZW.CX_ZW_045Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.000000 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34123" ObjectName="EC-CX_ZW.CX_ZW_045Ld"/>
    <cge:TPSR_Ref TObjectID="34123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZW.CX_ZW_046Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34122" ObjectName="EC-CX_ZW.CX_ZW_046Ld"/>
    <cge:TPSR_Ref TObjectID="34122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZW.CX_ZW_048Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4945.000000 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34124" ObjectName="EC-CX_ZW.CX_ZW_048Ld"/>
    <cge:TPSR_Ref TObjectID="34124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZW.CX_ZW_049Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5082.000000 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34125" ObjectName="EC-CX_ZW.CX_ZW_049Ld"/>
    <cge:TPSR_Ref TObjectID="34125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_ZW.CX_ZW_051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 -54.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41554" ObjectName="EC-CX_ZW.CX_ZW_051Ld"/>
    <cge:TPSR_Ref TObjectID="41554"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_6a278e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-802 4539,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7789@1" ObjectIDZND0="7755@0" Pin0InfoVect0LinkObjId="g_6a27b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45563_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-802 4539,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a27b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-802 4017,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7784@1" ObjectIDZND0="7755@0" Pin0InfoVect0LinkObjId="g_6a278e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45549_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-802 4017,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a27da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-699 4017,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="7799@1" ObjectIDZND0="7783@0" Pin0InfoVect0LinkObjId="SW-45548_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a73c90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-699 4017,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a28000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-699 4539,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="7800@1" ObjectIDZND0="7788@0" Pin0InfoVect0LinkObjId="SW-45562_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a74970_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-699 4539,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6a28260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-485 4017,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7786@1" ObjectIDZND0="7785@0" Pin0InfoVect0LinkObjId="SW-45550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45551_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-485 4017,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6a284c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-481 4539,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23135@1" ObjectIDZND0="7790@0" Pin0InfoVect0LinkObjId="SW-45564_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126748_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-481 4539,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6a2a770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3597,-207 3560,-207 3560,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="34119@x" ObjectIDND1="g_6ade160@0" ObjectIDZND0="g_6a2a9d0@0" Pin0InfoVect0LinkObjId="g_6a2a9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_041Ld_0" Pin1InfoVect1LinkObjId="g_6ade160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3597,-207 3560,-207 3560,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a481d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-824 4433,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23063@0" ObjectIDZND0="7755@0" Pin0InfoVect0LinkObjId="g_6a278e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126617_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-824 4433,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a4c2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-1053 4479,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_6a491e0@0" ObjectIDZND0="23069@1" Pin0InfoVect0LinkObjId="SW-126619_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a491e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-1053 4479,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a4c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-1053 4433,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23069@0" ObjectIDZND0="g_6a48430@0" ObjectIDZND1="23066@x" ObjectIDZND2="23067@x" Pin0InfoVect0LinkObjId="g_6a48430_0" Pin0InfoVect1LinkObjId="SW-126618_0" Pin0InfoVect2LinkObjId="SW-126622_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126619_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-1053 4433,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a4c7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-1001 4433,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_6a48430@0" ObjectIDZND0="23066@x" ObjectIDZND1="23067@x" ObjectIDZND2="23069@x" Pin0InfoVect0LinkObjId="SW-126618_0" Pin0InfoVect1LinkObjId="SW-126622_0" Pin0InfoVect2LinkObjId="SW-126619_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a48430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-1001 4433,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a4fad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-966 4433,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23066@1" ObjectIDZND0="g_6a48430@0" ObjectIDZND1="23069@x" ObjectIDZND2="23067@x" Pin0InfoVect0LinkObjId="g_6a48430_0" Pin0InfoVect1LinkObjId="SW-126619_0" Pin0InfoVect2LinkObjId="SW-126622_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126618_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-966 4433,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a4fd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-978 4433,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23066@x" ObjectIDND1="23067@x" ObjectIDZND0="g_6a48430@0" ObjectIDZND1="23069@x" Pin0InfoVect0LinkObjId="g_6a48430_0" Pin0InfoVect1LinkObjId="SW-126619_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126618_0" Pin1InfoVect1LinkObjId="SW-126622_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-978 4433,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a4ff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-978 4486,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a4d290@0" ObjectIDZND0="23067@1" Pin0InfoVect0LinkObjId="SW-126622_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a4d290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-978 4486,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a501f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-978 4433,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23067@0" ObjectIDZND0="23066@x" ObjectIDZND1="g_6a48430@0" ObjectIDZND2="23069@x" Pin0InfoVect0LinkObjId="SW-126618_0" Pin0InfoVect1LinkObjId="g_6a48430_0" Pin0InfoVect2LinkObjId="SW-126619_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126622_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-978 4433,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a50ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-929 4433,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23066@0" ObjectIDZND0="23068@x" ObjectIDZND1="23065@x" Pin0InfoVect0LinkObjId="SW-126293_0" Pin0InfoVect1LinkObjId="SW-126621_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126618_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-929 4433,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a50f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-921 4433,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23066@x" ObjectIDND1="23065@x" ObjectIDZND0="23068@1" Pin0InfoVect0LinkObjId="SW-126293_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126618_0" Pin1InfoVect1LinkObjId="SW-126621_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-921 4433,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a511a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-921 4486,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a4dd20@0" ObjectIDZND0="23065@1" Pin0InfoVect0LinkObjId="SW-126621_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a4dd20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-921 4486,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a51400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-921 4433,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23065@0" ObjectIDZND0="23066@x" ObjectIDZND1="23068@x" Pin0InfoVect0LinkObjId="SW-126618_0" Pin0InfoVect1LinkObjId="SW-126293_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126621_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-921 4433,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a51ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-882 4433,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23068@0" ObjectIDZND0="23063@x" ObjectIDZND1="23064@x" Pin0InfoVect0LinkObjId="SW-126617_0" Pin0InfoVect1LinkObjId="SW-126620_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-882 4433,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a52150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-872 4433,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23068@x" ObjectIDND1="23064@x" ObjectIDZND0="23063@1" Pin0InfoVect0LinkObjId="SW-126617_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126293_0" Pin1InfoVect1LinkObjId="SW-126620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-872 4433,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a523b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-872 4486,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a4e7b0@0" ObjectIDZND0="23064@1" Pin0InfoVect0LinkObjId="SW-126620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a4e7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-872 4486,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a52610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-872 4433,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23064@0" ObjectIDZND0="23068@x" ObjectIDZND1="23063@x" Pin0InfoVect0LinkObjId="SW-126293_0" Pin0InfoVect1LinkObjId="SW-126617_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-872 4433,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a64290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-1053 4013,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_6a61190@0" ObjectIDZND0="7761@1" Pin0InfoVect0LinkObjId="SW-45311_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a61190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4060,-1053 4013,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a644f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-1053 3967,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="7761@0" ObjectIDZND0="g_6a74bd0@0" ObjectIDZND1="7759@x" ObjectIDZND2="7760@x" Pin0InfoVect0LinkObjId="g_6a74bd0_0" Pin0InfoVect1LinkObjId="SW-45309_0" Pin0InfoVect2LinkObjId="SW-45310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45311_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-1053 3967,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a64750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-1014 3967,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_6a74bd0@0" ObjectIDZND0="7759@x" ObjectIDZND1="7760@1" ObjectIDZND2="7761@x" Pin0InfoVect0LinkObjId="SW-45309_0" Pin0InfoVect1LinkObjId="SW-45310_1" Pin0InfoVect2LinkObjId="SW-45311_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a74bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-1014 3967,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a66960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-965 3967,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="7759@1" ObjectIDZND0="7760@x" ObjectIDZND1="g_6a74bd0@0" ObjectIDZND2="7761@x" Pin0InfoVect0LinkObjId="SW-45310_0" Pin0InfoVect1LinkObjId="g_6a74bd0_0" Pin0InfoVect2LinkObjId="SW-45311_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45309_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-965 3967,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a66bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4031,-977 4020,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a649b0@0" ObjectIDZND0="7760@1" Pin0InfoVect0LinkObjId="SW-45310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a649b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4031,-977 4020,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a66e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-977 3967,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="7760@0" ObjectIDZND0="7759@x" ObjectIDZND1="g_6a74bd0@0" ObjectIDZND2="7761@x" Pin0InfoVect0LinkObjId="SW-45309_0" Pin0InfoVect1LinkObjId="g_6a74bd0_0" Pin0InfoVect2LinkObjId="SW-45311_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-977 3967,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a67080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-928 3967,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7759@0" ObjectIDZND0="7757@x" ObjectIDZND1="23070@x" Pin0InfoVect0LinkObjId="SW-45307_0" Pin0InfoVect1LinkObjId="SW-126605_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-928 3967,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a672e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-920 3967,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7759@0" ObjectIDND1="23070@x" ObjectIDZND0="7757@1" Pin0InfoVect0LinkObjId="SW-45307_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-45309_0" Pin1InfoVect1LinkObjId="SW-126605_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-920 3967,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a67540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4031,-920 4020,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a65440@0" ObjectIDZND0="23070@1" Pin0InfoVect0LinkObjId="SW-126605_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a65440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4031,-920 4020,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a677a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-920 3967,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23070@0" ObjectIDZND0="7757@x" ObjectIDZND1="7759@0" Pin0InfoVect0LinkObjId="SW-45307_0" Pin0InfoVect1LinkObjId="SW-45309_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-920 3967,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a67a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-881 3967,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7757@0" ObjectIDZND0="7758@x" ObjectIDZND1="23071@x" Pin0InfoVect0LinkObjId="SW-45308_0" Pin0InfoVect1LinkObjId="SW-126604_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-881 3967,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a67c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-871 3967,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7757@0" ObjectIDND1="23071@x" ObjectIDZND0="7758@1" Pin0InfoVect0LinkObjId="SW-45308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-45307_0" Pin1InfoVect1LinkObjId="SW-126604_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-871 3967,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a67ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4031,-871 4020,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a65ed0@0" ObjectIDZND0="23071@1" Pin0InfoVect0LinkObjId="SW-126604_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a65ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4031,-871 4020,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a68120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-871 3967,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23071@0" ObjectIDZND0="7758@x" ObjectIDZND1="7757@0" Pin0InfoVect0LinkObjId="SW-45308_0" Pin0InfoVect1LinkObjId="SW-45307_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-871 3967,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6a73c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-594 4017,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_6a73210@1" ObjectIDZND0="7799@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a73210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-594 4017,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6a74970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-594 4539,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_6a73ef0@1" ObjectIDZND0="7800@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a73ef0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-594 4539,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a763f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-977 3967,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="7759@x" ObjectIDND1="7760@x" ObjectIDZND0="g_6a74bd0@0" ObjectIDZND1="7761@x" ObjectIDZND2="37791@1" Pin0InfoVect0LinkObjId="g_6a74bd0_0" Pin0InfoVect1LinkObjId="SW-45311_0" Pin0InfoVect2LinkObjId="g_6a76ee0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-45309_0" Pin1InfoVect1LinkObjId="SW-45310_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-977 3967,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a76ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-1053 3967,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="7761@x" ObjectIDND1="g_6a74bd0@0" ObjectIDND2="7759@x" ObjectIDZND0="37791@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-45311_0" Pin1InfoVect1LinkObjId="g_6a74bd0_0" Pin1InfoVect2LinkObjId="SW-45309_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-1053 3967,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a77140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-1014 3967,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_6a74bd0@0" ObjectIDND1="7759@x" ObjectIDND2="7760@1" ObjectIDZND0="7761@x" ObjectIDZND1="37791@1" Pin0InfoVect0LinkObjId="SW-45311_0" Pin0InfoVect1LinkObjId="g_6a76ee0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_6a74bd0_0" Pin1InfoVect1LinkObjId="SW-45309_0" Pin1InfoVect2LinkObjId="SW-45310_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-1014 3967,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a795e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-1014 3967,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_6a773a0@0" ObjectIDZND0="g_6a74bd0@0" ObjectIDZND1="7759@x" ObjectIDZND2="7760@1" Pin0InfoVect0LinkObjId="g_6a74bd0_0" Pin0InfoVect1LinkObjId="SW-45309_0" Pin0InfoVect2LinkObjId="SW-45310_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a773a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-1014 3967,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a88a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-824 4722,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7763@0" ObjectIDZND0="7755@0" Pin0InfoVect0LinkObjId="g_6a278e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-824 4722,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8bd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4815,-1033 4768,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_6a88c70@0" ObjectIDZND0="23062@1" Pin0InfoVect0LinkObjId="SW-45334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a88c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4815,-1033 4768,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8bfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4732,-1033 4722,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23062@0" ObjectIDZND0="g_6a91b80@0" ObjectIDZND1="7765@0" ObjectIDZND2="7764@x" Pin0InfoVect0LinkObjId="g_6a91b80_0" Pin0InfoVect1LinkObjId="SW-45333_0" Pin0InfoVect2LinkObjId="SW-45332_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4732,-1033 4722,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-966 4722,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="7764@1" ObjectIDZND0="7765@x" ObjectIDZND1="g_6a91b80@0" ObjectIDZND2="23062@x" Pin0InfoVect0LinkObjId="SW-45333_0" Pin0InfoVect1LinkObjId="g_6a91b80_0" Pin0InfoVect2LinkObjId="SW-45334_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-966 4722,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8e440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-978 4775,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a8c230@0" ObjectIDZND0="7765@1" Pin0InfoVect0LinkObjId="SW-45333_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a8c230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-978 4775,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8e6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4739,-978 4722,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="7765@0" ObjectIDZND0="7764@x" ObjectIDZND1="g_6a91b80@0" ObjectIDZND2="23062@x" Pin0InfoVect0LinkObjId="SW-45332_0" Pin0InfoVect1LinkObjId="g_6a91b80_0" Pin0InfoVect2LinkObjId="SW-45334_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4739,-978 4722,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8e900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-929 4722,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7764@0" ObjectIDZND0="7762@x" ObjectIDZND1="23061@x" Pin0InfoVect0LinkObjId="SW-45330_0" Pin0InfoVect1LinkObjId="SW-126781_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-929 4722,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8eb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-921 4722,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7764@x" ObjectIDND1="23061@x" ObjectIDZND0="7762@1" Pin0InfoVect0LinkObjId="SW-45330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-45332_0" Pin1InfoVect1LinkObjId="SW-126781_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-921 4722,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8edc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-921 4775,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a8ccc0@0" ObjectIDZND0="23061@1" Pin0InfoVect0LinkObjId="SW-126781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a8ccc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-921 4775,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8f020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4739,-921 4722,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23061@0" ObjectIDZND0="7762@x" ObjectIDZND1="7764@x" Pin0InfoVect0LinkObjId="SW-45330_0" Pin0InfoVect1LinkObjId="SW-45332_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4739,-921 4722,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8f280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-882 4722,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7762@0" ObjectIDZND0="7763@x" ObjectIDZND1="7766@x" Pin0InfoVect0LinkObjId="SW-45331_0" Pin0InfoVect1LinkObjId="SW-126780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-882 4722,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8f4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-872 4722,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7762@x" ObjectIDND1="7766@x" ObjectIDZND0="7763@1" Pin0InfoVect0LinkObjId="SW-45331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-45330_0" Pin1InfoVect1LinkObjId="SW-126780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-872 4722,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8f740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-872 4775,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a8d750@0" ObjectIDZND0="7766@1" Pin0InfoVect0LinkObjId="SW-126780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a8d750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-872 4775,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a8f9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4739,-872 4722,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7766@0" ObjectIDZND0="7763@x" ObjectIDZND1="7762@x" Pin0InfoVect0LinkObjId="SW-45331_0" Pin0InfoVect1LinkObjId="SW-45330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4739,-872 4722,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a92930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4710,-1000 4722,-1000 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_6a91b80@0" ObjectIDZND0="7765@x" ObjectIDZND1="7764@1" ObjectIDZND2="23062@x" Pin0InfoVect0LinkObjId="SW-45333_0" Pin0InfoVect1LinkObjId="SW-45332_1" Pin0InfoVect2LinkObjId="SW-45334_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a91b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4710,-1000 4722,-1000 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a93420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-978 4722,-1000 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="7765@x" ObjectIDND1="7764@x" ObjectIDZND0="g_6a91b80@0" ObjectIDZND1="23062@x" ObjectIDZND2="34588@1" Pin0InfoVect0LinkObjId="g_6a91b80_0" Pin0InfoVect1LinkObjId="SW-45334_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-45333_0" Pin1InfoVect1LinkObjId="SW-45332_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-978 4722,-1000 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a93f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-1053 4433,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="23069@x" ObjectIDND1="g_6a48430@0" ObjectIDND2="23066@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126619_0" Pin1InfoVect1LinkObjId="g_6a48430_0" Pin1InfoVect2LinkObjId="SW-126618_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-1053 4433,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a94170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-1001 4433,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_6a48430@0" ObjectIDND1="23066@x" ObjectIDND2="23067@x" ObjectIDZND0="23069@x" Pin0InfoVect0LinkObjId="SW-126619_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_6a48430_0" Pin1InfoVect1LinkObjId="SW-126618_0" Pin1InfoVect2LinkObjId="SW-126622_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-1001 4433,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a97860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-1063 4722,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34588@1" ObjectIDZND0="23062@x" ObjectIDZND1="g_6a91b80@0" ObjectIDZND2="7765@0" Pin0InfoVect0LinkObjId="SW-45334_0" Pin0InfoVect1LinkObjId="g_6a91b80_0" Pin0InfoVect2LinkObjId="SW-45333_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-1063 4722,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a97ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-1000 4722,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_6a91b80@0" ObjectIDND1="7765@x" ObjectIDND2="7764@1" ObjectIDZND0="23062@x" ObjectIDZND1="34588@1" Pin0InfoVect0LinkObjId="SW-45334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_6a91b80_0" Pin1InfoVect1LinkObjId="SW-45333_0" Pin1InfoVect2LinkObjId="SW-45332_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-1000 4722,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a98ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-924 4207,-916 4238,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_6a97d20@0" ObjectIDZND0="g_6a943d0@0" ObjectIDZND1="7795@x" ObjectIDZND2="7796@x" Pin0InfoVect0LinkObjId="g_6a943d0_0" Pin0InfoVect1LinkObjId="SW-45577_0" Pin0InfoVect2LinkObjId="SW-45578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a97d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-924 4207,-916 4238,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a997e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-896 4238,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="7795@1" ObjectIDZND0="g_6a97d20@0" ObjectIDZND1="g_6a943d0@0" ObjectIDZND2="7796@x" Pin0InfoVect0LinkObjId="g_6a97d20_0" Pin0InfoVect1LinkObjId="g_6a943d0_0" Pin0InfoVect2LinkObjId="SW-45578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45577_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-896 4238,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a99a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-916 4238,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_6a97d20@0" ObjectIDND1="7795@x" ObjectIDND2="7796@x" ObjectIDZND0="g_6a943d0@0" Pin0InfoVect0LinkObjId="g_6a943d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_6a97d20_0" Pin1InfoVect1LinkObjId="SW-45577_0" Pin1InfoVect2LinkObjId="SW-45578_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-916 4238,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a9b590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-815 4238,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7755@0" ObjectIDZND0="7795@x" ObjectIDZND1="23134@x" Pin0InfoVect0LinkObjId="SW-45577_0" Pin0InfoVect1LinkObjId="SW-126592_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a278e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-815 4238,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a9b7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-839 4238,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7755@0" ObjectIDND1="23134@x" ObjectIDZND0="7795@0" Pin0InfoVect0LinkObjId="SW-45577_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_6a278e0_0" Pin1InfoVect1LinkObjId="SW-126592_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-839 4238,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a9ba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4296,-839 4287,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a9a270@0" ObjectIDZND0="23134@1" Pin0InfoVect0LinkObjId="SW-126592_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a9a270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4296,-839 4287,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a9bcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-839 4238,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="23134@0" ObjectIDZND0="7755@0" ObjectIDZND1="7795@x" Pin0InfoVect0LinkObjId="g_6a278e0_0" Pin0InfoVect1LinkObjId="SW-45577_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126592_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-839 4238,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a9eed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4296,-916 4287,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6a9e440@0" ObjectIDZND0="7796@1" Pin0InfoVect0LinkObjId="SW-45578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a9e440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4296,-916 4287,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6a9f130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4251,-916 4238,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="7796@0" ObjectIDZND0="g_6a97d20@0" ObjectIDZND1="g_6a943d0@0" ObjectIDZND2="7795@x" Pin0InfoVect0LinkObjId="g_6a97d20_0" Pin0InfoVect1LinkObjId="g_6a943d0_0" Pin0InfoVect2LinkObjId="SW-45577_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4251,-916 4238,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6aa2350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4075,-755 4066,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6aa18c0@0" ObjectIDZND0="23073@1" Pin0InfoVect0LinkObjId="SW-126528_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6aa18c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4075,-755 4066,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6aa25b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-755 4017,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23073@0" ObjectIDZND0="7783@x" ObjectIDZND1="7784@x" Pin0InfoVect0LinkObjId="SW-45548_0" Pin0InfoVect1LinkObjId="SW-45549_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126528_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-755 4017,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6aa30a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-745 4017,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7783@1" ObjectIDZND0="23073@x" ObjectIDZND1="7784@x" Pin0InfoVect0LinkObjId="SW-126528_0" Pin0InfoVect1LinkObjId="SW-45549_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45548_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-745 4017,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6aa3300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-755 4017,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23073@x" ObjectIDND1="7783@x" ObjectIDZND0="7784@0" Pin0InfoVect0LinkObjId="SW-45549_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126528_0" Pin1InfoVect1LinkObjId="SW-45548_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-755 4017,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6aa3ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-754 4588,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_6aa3560@0" ObjectIDZND0="23072@1" Pin0InfoVect0LinkObjId="SW-126555_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6aa3560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-754 4588,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6aa4250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4552,-754 4539,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23072@0" ObjectIDZND0="7788@x" ObjectIDZND1="7789@x" Pin0InfoVect0LinkObjId="SW-45562_0" Pin0InfoVect1LinkObjId="SW-45563_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4552,-754 4539,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6aa7270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-745 4539,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7788@1" ObjectIDZND0="23072@x" ObjectIDZND1="7789@x" Pin0InfoVect0LinkObjId="SW-126555_0" Pin0InfoVect1LinkObjId="SW-45563_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-745 4539,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_6aa74d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-754 4539,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23072@x" ObjectIDND1="7788@x" ObjectIDZND0="7789@0" Pin0InfoVect0LinkObjId="SW-45563_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126555_0" Pin1InfoVect1LinkObjId="SW-45562_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-754 4539,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6aab0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4183,-502 4183,-494 4214,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_6aaa330@0" ObjectIDZND0="g_6aa7730@0" ObjectIDZND1="7797@x" Pin0InfoVect0LinkObjId="g_6aa7730_0" Pin0InfoVect1LinkObjId="SW-45579_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6aaa330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4183,-502 4183,-494 4214,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6aab340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4214,-474 4214,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="7797@1" ObjectIDZND0="g_6aaa330@0" ObjectIDZND1="g_6aa7730@0" Pin0InfoVect0LinkObjId="g_6aaa330_0" Pin0InfoVect1LinkObjId="g_6aa7730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4214,-474 4214,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6aab5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4214,-494 4214,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_6aaa330@0" ObjectIDND1="7797@x" ObjectIDZND0="g_6aa7730@0" Pin0InfoVect0LinkObjId="g_6aa7730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_6aaa330_0" Pin1InfoVect1LinkObjId="SW-45579_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4214,-494 4214,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ab0b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4214,-442 4214,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7797@0" ObjectIDZND0="7756@0" Pin0InfoVect0LinkObjId="g_6abf2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45579_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4214,-442 4214,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ab4960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-502 4703,-494 4734,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_6ab3bb0@0" ObjectIDZND0="7798@x" ObjectIDZND1="g_6ab13a0@0" Pin0InfoVect0LinkObjId="SW-45580_0" Pin0InfoVect1LinkObjId="g_6ab13a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6ab3bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-502 4703,-494 4734,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ab4bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-474 4734,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="7798@1" ObjectIDZND0="g_6ab3bb0@0" ObjectIDZND1="g_6ab13a0@0" Pin0InfoVect0LinkObjId="g_6ab3bb0_0" Pin0InfoVect1LinkObjId="g_6ab13a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-474 4734,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ab4e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-494 4734,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="7798@x" ObjectIDND1="g_6ab3bb0@0" ObjectIDZND0="g_6ab13a0@0" Pin0InfoVect0LinkObjId="g_6ab13a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-45580_0" Pin1InfoVect1LinkObjId="g_6ab3bb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-494 4734,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ab9af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-442 4734,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7798@0" ObjectIDZND0="23137@0" Pin0InfoVect0LinkObjId="g_542e270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-442 4734,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6abf050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3597,-350 3597,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7767@1" ObjectIDZND0="7768@0" Pin0InfoVect0LinkObjId="SW-45354_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45353_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3597,-350 3597,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6abf2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3597,-400 3597,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7768@1" ObjectIDZND0="7756@0" Pin0InfoVect0LinkObjId="g_6ab0b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45354_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3597,-400 3597,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ac3f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3597,-306 3597,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23076@1" ObjectIDZND0="7767@0" Pin0InfoVect0LinkObjId="SW-45353_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126323_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3597,-306 3597,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6acbc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-350 4018,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7771@1" ObjectIDZND0="7772@0" Pin0InfoVect0LinkObjId="SW-45393_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-350 4018,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6acbeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-400 4018,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7772@1" ObjectIDZND0="7756@0" Pin0InfoVect0LinkObjId="g_6ab0b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-400 4018,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ad0b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-306 4018,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23081@1" ObjectIDZND0="7771@0" Pin0InfoVect0LinkObjId="SW-45392_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126366_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-306 4018,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ad2e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-207 3803,-207 3803,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="34120@x" ObjectIDND1="g_6ae1170@0" ObjectIDZND0="g_6ad3090@0" Pin0InfoVect0LinkObjId="g_6ad3090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_043Ld_0" Pin1InfoVect1LinkObjId="g_6ae1170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-207 3803,-207 3803,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ad8fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-350 3840,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7769@1" ObjectIDZND0="7770@0" Pin0InfoVect0LinkObjId="SW-45374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45373_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-350 3840,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ad9230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-400 3840,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7770@1" ObjectIDZND0="7756@0" Pin0InfoVect0LinkObjId="g_6ab0b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-400 3840,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6addf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-306 3840,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23080@1" ObjectIDZND0="7769@0" Pin0InfoVect0LinkObjId="SW-45373_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-306 3840,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6adf470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-207 4018,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_6ae1e50@0" ObjectIDND1="g_5422690@0" ObjectIDZND0="34121@0" Pin0InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_044Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_6ae1e50_0" Pin1InfoVect1LinkObjId="g_5422690_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-207 4018,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6adff60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3597,-207 3597,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_6a2a9d0@0" ObjectIDND1="g_6ade160@0" ObjectIDZND0="34119@0" Pin0InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_041Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_6a2a9d0_0" Pin1InfoVect1LinkObjId="g_6ade160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3597,-207 3597,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ae0a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-207 3840,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_6ad3090@0" ObjectIDND1="g_6ae1170@0" ObjectIDZND0="34120@0" Pin0InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_6ad3090_0" Pin1InfoVect1LinkObjId="g_6ae1170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-207 3840,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ae0cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3597,-274 3597,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23076@0" ObjectIDZND0="g_6ade160@1" Pin0InfoVect0LinkObjId="g_6ade160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3597,-274 3597,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ae0f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3597,-222 3597,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_6ade160@0" ObjectIDZND0="g_6a2a9d0@0" ObjectIDZND1="34119@x" Pin0InfoVect0LinkObjId="g_6a2a9d0_0" Pin0InfoVect1LinkObjId="EC-CX_ZW.CX_ZW_041Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6ade160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3597,-222 3597,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ae1bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-216 3840,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_6ae1170@0" ObjectIDZND0="g_6ad3090@0" ObjectIDZND1="34120@x" Pin0InfoVect0LinkObjId="g_6ad3090_0" Pin0InfoVect1LinkObjId="EC-CX_ZW.CX_ZW_043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6ae1170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-216 3840,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ae28d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-274 4018,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23081@0" ObjectIDZND0="g_6ae1e50@1" Pin0InfoVect0LinkObjId="g_6ae1e50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126366_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-274 4018,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ae2b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-222 4018,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_6ae1e50@0" ObjectIDZND0="34121@x" ObjectIDZND1="g_5422690@0" Pin0InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_044Ld_0" Pin0InfoVect1LinkObjId="g_5422690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6ae1e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-222 4018,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ae37b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-274 3840,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23080@0" ObjectIDZND0="g_6ae1170@0" ObjectIDZND1="g_6aeaca0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_6ae1170_0" Pin0InfoVect1LinkObjId="g_6aeaca0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126344_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-274 3840,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6ae3a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-261 3840,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23080@x" ObjectIDND1="g_6aeaca0@0" ObjectIDND2="0@x" ObjectIDZND0="g_6ae1170@1" Pin0InfoVect0LinkObjId="g_6ae1170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126344_0" Pin1InfoVect1LinkObjId="g_6aeaca0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-261 3840,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_6ae91e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-121 3928,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_6ae8a40@0" ObjectIDZND0="g_6ae7170@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_6ae7170_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6ae8a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-121 3928,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_6ae9cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3905,-128 3928,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_6ae7170@0" ObjectIDZND0="g_6ae8a40@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_6ae8a40_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6ae7170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3905,-128 3928,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_6ae9f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-128 3928,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_6ae8a40@0" ObjectIDND1="g_6ae7170@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_6ae8a40_0" Pin1InfoVect1LinkObjId="g_6ae7170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-128 3928,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6aeb880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3907,-210 3907,-214 3928,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_6aeaca0@0" ObjectIDZND0="23080@x" ObjectIDZND1="g_6ae1170@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-126344_0" Pin0InfoVect1LinkObjId="g_6ae1170_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6aeaca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3907,-210 3907,-214 3928,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6aec370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-261 3928,-261 3928,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23080@x" ObjectIDND1="g_6ae1170@0" ObjectIDZND0="g_6aeaca0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_6aeaca0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126344_0" Pin1InfoVect1LinkObjId="g_6ae1170_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-261 3928,-261 3928,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_6aec5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-214 3928,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_6aeaca0@0" ObjectIDND1="23080@x" ObjectIDND2="g_6ae1170@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_6aeaca0_0" Pin1InfoVect1LinkObjId="SW-126344_0" Pin1InfoVect2LinkObjId="g_6ae1170_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-214 3928,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5419740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3735,-350 3735,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7775@1" ObjectIDZND0="7776@0" Pin0InfoVect0LinkObjId="SW-45431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3735,-350 3735,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54199a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3735,-400 3735,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7776@1" ObjectIDZND0="7756@0" Pin0InfoVect0LinkObjId="g_6ab0b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3735,-400 3735,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_541e670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3735,-306 3735,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23079@1" ObjectIDZND0="7775@0" Pin0InfoVect0LinkObjId="SW-45430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3735,-306 3735,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_541f350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3735,-274 3735,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23079@0" ObjectIDZND0="g_541e8d0@1" Pin0InfoVect0LinkObjId="g_541e8d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3735,-274 3735,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5422000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3735,-229 3735,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_541e8d0@0" ObjectIDZND0="23078@1" Pin0InfoVect0LinkObjId="SW-126408_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_541e8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3735,-229 3735,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54248d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4069,-197 4069,-207 4018,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_5422690@0" ObjectIDZND0="34121@x" ObjectIDZND1="g_6ae1e50@0" Pin0InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_044Ld_0" Pin0InfoVect1LinkObjId="g_6ae1e50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_5422690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4069,-197 4069,-207 4018,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54295a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-421 4266,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7756@0" ObjectIDZND0="23089@1" Pin0InfoVect0LinkObjId="SW-126693_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6ab0b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-421 4266,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_542e270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-400 4406,-418 4407,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23090@1" ObjectIDZND0="23137@0" Pin0InfoVect0LinkObjId="g_6ab9af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126687_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4406,-400 4406,-418 4407,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54302e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-368 4266,-341 4323,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23089@0" ObjectIDZND0="23075@1" Pin0InfoVect0LinkObjId="SW-126692_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126693_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-368 4266,-341 4323,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5430540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-341 4406,-341 4406,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23075@0" ObjectIDZND0="23090@0" Pin0InfoVect0LinkObjId="SW-126687_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126692_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-341 4406,-341 4406,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5432a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-207 4468,-207 4468,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="34123@x" ObjectIDND1="g_543dd80@0" ObjectIDZND0="g_5432cb0@0" Pin0InfoVect0LinkObjId="g_5432cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_045Ld_0" Pin1InfoVect1LinkObjId="g_543dd80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-207 4468,-207 4468,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5438bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-350 4505,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17903@1" ObjectIDZND0="17904@0" Pin0InfoVect0LinkObjId="SW-80960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80959_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-350 4505,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5438e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-400 4505,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17904@1" ObjectIDZND0="23137@0" Pin0InfoVect0LinkObjId="g_6ab9af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-400 4505,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_543db20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-306 4505,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23082@1" ObjectIDZND0="17903@0" Pin0InfoVect0LinkObjId="SW-80959_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-306 4505,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_543e800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-207 4505,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_5432cb0@0" ObjectIDND1="g_543dd80@0" ObjectIDZND0="34123@0" Pin0InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_045Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_5432cb0_0" Pin1InfoVect1LinkObjId="g_543dd80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-207 4505,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_543ea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-274 4505,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23082@0" ObjectIDZND0="g_543dd80@1" Pin0InfoVect0LinkObjId="g_543dd80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-274 4505,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_543ecc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-222 4505,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_543dd80@0" ObjectIDZND0="g_5432cb0@0" ObjectIDZND1="34123@x" Pin0InfoVect0LinkObjId="g_5432cb0_0" Pin0InfoVect1LinkObjId="EC-CX_ZW.CX_ZW_045Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_543dd80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-222 4505,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54420c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-207 4605,-207 4605,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="34122@x" ObjectIDND1="g_544d660@0" ObjectIDZND0="g_5442320@0" Pin0InfoVect0LinkObjId="g_5442320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_046Ld_0" Pin1InfoVect1LinkObjId="g_544d660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-207 4605,-207 4605,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54484d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-350 4642,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7781@1" ObjectIDZND0="7782@0" Pin0InfoVect0LinkObjId="SW-45474_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45473_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-350 4642,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5448730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-400 4642,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7782@1" ObjectIDZND0="23137@0" Pin0InfoVect0LinkObjId="g_6ab9af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-400 4642,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_544d400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-306 4642,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23083@1" ObjectIDZND0="7781@0" Pin0InfoVect0LinkObjId="SW-45473_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126466_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-306 4642,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_544e0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-207 4642,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_5442320@0" ObjectIDND1="g_544d660@0" ObjectIDZND0="34122@0" Pin0InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_046Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_5442320_0" Pin1InfoVect1LinkObjId="g_544d660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-207 4642,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_544e340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-274 4642,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23083@0" ObjectIDZND0="g_544d660@1" Pin0InfoVect0LinkObjId="g_544d660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126466_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-274 4642,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_544e5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-222 4642,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_544d660@0" ObjectIDZND0="g_5442320@0" ObjectIDZND1="34122@x" Pin0InfoVect0LinkObjId="g_5442320_0" Pin0InfoVect1LinkObjId="EC-CX_ZW.CX_ZW_046Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_544d660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-222 4642,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5455b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-350 4806,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7777@1" ObjectIDZND0="7778@0" Pin0InfoVect0LinkObjId="SW-45443_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45442_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-350 4806,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5455db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-400 4806,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7778@1" ObjectIDZND0="23137@0" Pin0InfoVect0LinkObjId="g_6ab9af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45443_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-400 4806,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_545aa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-306 4806,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23084@1" ObjectIDZND0="7777@0" Pin0InfoVect0LinkObjId="SW-45442_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126425_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-306 4806,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_545b760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-274 4806,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23084@0" ObjectIDZND0="g_545ace0@1" Pin0InfoVect0LinkObjId="g_545ace0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-274 4806,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_545e410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-229 4806,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_545ace0@0" ObjectIDZND0="23085@1" Pin0InfoVect0LinkObjId="SW-126427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_545ace0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-229 4806,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_545e670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-183 4753,-64 4805,-64 4805,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="23086@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-183 4753,-64 4805,-64 4805,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5461000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4954,-207 4917,-207 4917,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="34124@x" ObjectIDND1="g_546c330@0" ObjectIDZND0="g_5461260@0" Pin0InfoVect0LinkObjId="g_5461260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_048Ld_0" Pin1InfoVect1LinkObjId="g_546c330_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4954,-207 4917,-207 4917,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54671a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4954,-350 4954,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7773@1" ObjectIDZND0="7774@0" Pin0InfoVect0LinkObjId="SW-45412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45411_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4954,-350 4954,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5467400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4954,-400 4954,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7774@1" ObjectIDZND0="23137@0" Pin0InfoVect0LinkObjId="g_6ab9af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4954,-400 4954,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_546c0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4954,-306 4954,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23087@1" ObjectIDZND0="7773@0" Pin0InfoVect0LinkObjId="SW-45411_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4954,-306 4954,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_546cdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4954,-207 4954,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_5461260@0" ObjectIDND1="g_546c330@0" ObjectIDZND0="34124@0" Pin0InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_048Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_5461260_0" Pin1InfoVect1LinkObjId="g_546c330_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4954,-207 4954,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_546d010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4954,-274 4954,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23087@0" ObjectIDZND0="g_546c330@1" Pin0InfoVect0LinkObjId="g_546c330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4954,-274 4954,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_546d270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4954,-222 4954,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_546c330@0" ObjectIDZND0="g_5461260@0" ObjectIDZND1="34124@x" Pin0InfoVect0LinkObjId="g_5461260_0" Pin0InfoVect1LinkObjId="EC-CX_ZW.CX_ZW_048Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_546c330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4954,-222 4954,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_546fa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-207 5054,-207 5054,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="34125@x" ObjectIDND1="g_547b020@0" ObjectIDZND0="g_546fce0@0" Pin0InfoVect0LinkObjId="g_546fce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_049Ld_0" Pin1InfoVect1LinkObjId="g_547b020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-207 5054,-207 5054,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5475e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-350 5091,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7779@1" ObjectIDZND0="7780@0" Pin0InfoVect0LinkObjId="SW-45455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-350 5091,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54760f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-400 5091,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7780@1" ObjectIDZND0="23137@0" Pin0InfoVect0LinkObjId="g_6ab9af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45455_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-400 5091,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_547adc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-306 5091,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23088@1" ObjectIDZND0="7779@0" Pin0InfoVect0LinkObjId="SW-45454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126445_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-306 5091,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_547baa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-207 5091,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_546fce0@0" ObjectIDND1="g_547b020@0" ObjectIDZND0="34125@0" Pin0InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_049Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_546fce0_0" Pin1InfoVect1LinkObjId="g_547b020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-207 5091,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_547bd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-274 5091,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23088@0" ObjectIDZND0="g_547b020@1" Pin0InfoVect0LinkObjId="g_547b020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126445_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-274 5091,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_547bf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5091,-222 5091,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_547b020@0" ObjectIDZND0="g_546fce0@0" ObjectIDZND1="34125@x" Pin0InfoVect0LinkObjId="g_546fce0_0" Pin0InfoVect1LinkObjId="EC-CX_ZW.CX_ZW_049Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_547b020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5091,-222 5091,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_547fec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-453 4017,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7786@0" ObjectIDZND0="7756@0" Pin0InfoVect0LinkObjId="g_6ab0b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45551_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-453 4017,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_5480120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-449 4539,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23135@0" ObjectIDZND0="23137@0" Pin0InfoVect0LinkObjId="g_6ab9af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-449 4539,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5488f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-815 3967,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7755@0" ObjectIDZND0="7758@0" Pin0InfoVect0LinkObjId="SW-45308_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_6a278e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-815 3967,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_548c730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4031,-544 4017,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_548b020@0" ObjectIDZND0="7785@x" ObjectIDZND1="g_6a73210@0" Pin0InfoVect0LinkObjId="SW-45550_0" Pin0InfoVect1LinkObjId="g_6a73210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_548b020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4031,-544 4017,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_548d200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-531 4017,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7785@1" ObjectIDZND0="g_548b020@0" ObjectIDZND1="g_6a73210@0" Pin0InfoVect0LinkObjId="g_548b020_0" Pin0InfoVect1LinkObjId="g_6a73210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45550_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-531 4017,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_548d460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-544 4017,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_548b020@0" ObjectIDND1="7785@x" ObjectIDZND0="g_6a73210@0" Pin0InfoVect0LinkObjId="g_6a73210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_548b020_0" Pin1InfoVect1LinkObjId="SW-45550_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-544 4017,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_548d6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-544 4539,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_548bae0@0" ObjectIDZND0="7790@x" ObjectIDZND1="g_6a73ef0@0" Pin0InfoVect0LinkObjId="SW-45564_0" Pin0InfoVect1LinkObjId="g_6a73ef0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_548bae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-544 4539,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_548e190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-531 4539,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7790@1" ObjectIDZND0="g_548bae0@0" ObjectIDZND1="g_6a73ef0@0" Pin0InfoVect0LinkObjId="g_548bae0_0" Pin0InfoVect1LinkObjId="g_6a73ef0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-45564_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-531 4539,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_548e3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-544 4539,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_548bae0@0" ObjectIDND1="7790@x" ObjectIDZND0="g_6a73ef0@0" Pin0InfoVect0LinkObjId="g_6a73ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_548bae0_0" Pin1InfoVect1LinkObjId="SW-45564_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-544 4539,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54a6fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3682,-183 3682,-64 3734,-64 3734,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="23133@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3682,-183 3682,-64 3734,-64 3734,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54ae6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-399 4162,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40982@1" ObjectIDZND0="7756@0" Pin0InfoVect0LinkObjId="g_6ab0b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243843_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-399 4162,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54b4690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4179,-342 4162,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="40980@0" ObjectIDZND0="40981@x" ObjectIDZND1="40982@x" Pin0InfoVect0LinkObjId="SW-243837_0" Pin0InfoVect1LinkObjId="SW-243843_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243845_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4179,-342 4162,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54b74f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4226,-342 4215,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_54b6a60@0" ObjectIDZND0="40980@1" Pin0InfoVect0LinkObjId="SW-243845_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_54b6a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4226,-342 4215,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54b8500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-320 4162,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40981@1" ObjectIDZND0="40980@x" ObjectIDZND1="40982@x" Pin0InfoVect0LinkObjId="SW-243845_0" Pin0InfoVect1LinkObjId="SW-243843_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243837_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-320 4162,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54b86f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-342 4162,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="40980@x" ObjectIDND1="40981@x" ObjectIDZND0="40982@0" Pin0InfoVect0LinkObjId="SW-243843_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-243845_0" Pin1InfoVect1LinkObjId="SW-243837_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-342 4162,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54b88e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4179,-201 4162,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="40984@0" ObjectIDZND0="40983@x" ObjectIDZND1="g_54b33a0@0" Pin0InfoVect0LinkObjId="SW-243844_0" Pin0InfoVect1LinkObjId="g_54b33a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243847_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4179,-201 4162,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54bb910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4226,-201 4215,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_54bae80@0" ObjectIDZND0="40984@1" Pin0InfoVect0LinkObjId="SW-243847_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_54bae80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4226,-201 4215,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54bc920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-228 4162,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="40983@0" ObjectIDZND0="40984@x" ObjectIDZND1="g_54b33a0@0" Pin0InfoVect0LinkObjId="SW-243847_0" Pin0InfoVect1LinkObjId="g_54b33a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243844_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-228 4162,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54bcb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-201 4162,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="40984@x" ObjectIDND1="40983@x" ObjectIDZND0="g_54b33a0@1" Pin0InfoVect0LinkObjId="g_54b33a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-243847_0" Pin1InfoVect1LinkObjId="SW-243844_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-201 4162,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54c08e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-293 4162,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40981@0" ObjectIDZND0="40983@1" Pin0InfoVect0LinkObjId="SW-243844_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243837_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-293 4162,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54c2690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-81 4162,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="41554@0" ObjectIDZND0="g_54b33a0@0" Pin0InfoVect0LinkObjId="g_54b33a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_ZW.CX_ZW_051Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-81 4162,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54c8670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-183 4806,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="23085@0" ObjectIDZND0="41471@0" Pin0InfoVect0LinkObjId="CB-CX_ZW.CX_ZW_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-183 4806,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_54ce180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3735,-168 3735,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="41470@0" ObjectIDZND0="23078@0" Pin0InfoVect0LinkObjId="SW-126408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_ZW.CX_ZW_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3735,-168 3735,-183 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37339" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3410.000000 -1085.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5921" ObjectName="DYN-CX_ZW"/>
     <cge:Meas_Ref ObjectId="37339"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a33930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.000000 1097.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a33c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 1082.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a33e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 1067.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a34230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3519.000000 32.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a344f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3508.000000 17.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a34730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3533.000000 2.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -450.000000 356.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a34b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 389.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a34e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 374.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 347.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a352b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4252.000000 380.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a355f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 365.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5493340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 835.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5493cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 851.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5493f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 804.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5494180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 821.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54943c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3721.000000 789.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54947e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3866.000000 541.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5494aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 526.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5494ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 511.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549e320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4378.000000 538.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549e930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4367.000000 523.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549eb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 508.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549eea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3891.000000 741.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549f100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3877.000000 771.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549f340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3866.000000 756.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549f670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 737.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549f8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.000000 767.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549fb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 752.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a0bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 1097.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a0e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 1082.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a10c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 1067.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a14e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 32.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a17a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.000000 17.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a19e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.000000 2.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a1e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 32.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a20c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 17.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a2300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 2.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a2720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4431.000000 32.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a29e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 17.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a2c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4445.000000 2.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a3040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4576.000000 33.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a3300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4565.000000 18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a3540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 3.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a3960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4881.000000 33.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a3c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4870.000000 18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a3e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4895.000000 3.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a4280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 33.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a4540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a4780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5031.000000 3.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54c1670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 36.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54c1930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54c1b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 6.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_54d82e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 1117.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="4039" cy="1110" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_54d89a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 1095.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="4749" cy="1088" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.914695 -0.000000 0.000000 -0.933333 2012.045161 444.733333)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_ZW" flowDrawDirect="1" flowShape="0" id="AC-35kV.baizidongTzw_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3967,-1069 3967,-1100 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37791" ObjectName="AC-35kV.baizidongTzw_line"/>
    <cge:TPSR_Ref TObjectID="37791_SS-70"/></metadata>
   <polyline fill="none" opacity="0" points="3967,-1069 3967,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_ZW" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_zidiTzw" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4722,-1060 4722,-1091 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34588" ObjectName="AC-35kV.LN_zidiTzw"/>
    <cge:TPSR_Ref TObjectID="34588_SS-70"/></metadata>
   <polyline fill="none" opacity="0" points="4722,-1060 4722,-1091 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="7755" cx="4539" cy="-815" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7755" cx="4017" cy="-815" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7755" cx="4433" cy="-815" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7755" cx="4722" cy="-815" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7755" cx="4238" cy="-815" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23137" cx="4734" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23137" cx="4407" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23137" cx="4505" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23137" cx="4642" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23137" cx="4806" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23137" cx="4954" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23137" cx="5091" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7755" cx="3967" cy="-815" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23137" cx="4539" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7756" cx="4214" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7756" cx="3597" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7756" cx="4018" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7756" cx="3840" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7756" cx="3735" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7756" cx="4266" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7756" cx="4017" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7756" cx="4162" cy="-421" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-45564">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.000000 -496.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7790" ObjectName="SW-CX_ZW.CX_ZW_002BK"/>
     <cge:Meas_Ref ObjectId="45564"/>
    <cge:TPSR_Ref TObjectID="7790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.000000 -710.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7788" ObjectName="SW-CX_ZW.CX_ZW_302BK"/>
     <cge:Meas_Ref ObjectId="45562"/>
    <cge:TPSR_Ref TObjectID="7788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45550">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4008.000000 -496.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7785" ObjectName="SW-CX_ZW.CX_ZW_001BK"/>
     <cge:Meas_Ref ObjectId="45550"/>
    <cge:TPSR_Ref TObjectID="7785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45548">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4008.000000 -710.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7783" ObjectName="SW-CX_ZW.CX_ZW_301BK"/>
     <cge:Meas_Ref ObjectId="45548"/>
    <cge:TPSR_Ref TObjectID="7783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45353">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3588.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7767" ObjectName="SW-CX_ZW.CX_ZW_041BK"/>
     <cge:Meas_Ref ObjectId="45353"/>
    <cge:TPSR_Ref TObjectID="7767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -874.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23068" ObjectName="SW-CX_ZW.CX_ZW_342BK"/>
     <cge:Meas_Ref ObjectId="126293"/>
    <cge:TPSR_Ref TObjectID="23068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45307">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -873.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7757" ObjectName="SW-CX_ZW.CX_ZW_341BK"/>
     <cge:Meas_Ref ObjectId="45307"/>
    <cge:TPSR_Ref TObjectID="7757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45330">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -874.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7762" ObjectName="SW-CX_ZW.CX_ZW_343BK"/>
     <cge:Meas_Ref ObjectId="45330"/>
    <cge:TPSR_Ref TObjectID="7762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45392">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7771" ObjectName="SW-CX_ZW.CX_ZW_044BK"/>
     <cge:Meas_Ref ObjectId="45392"/>
    <cge:TPSR_Ref TObjectID="7771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45373">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7769" ObjectName="SW-CX_ZW.CX_ZW_043BK"/>
     <cge:Meas_Ref ObjectId="45373"/>
    <cge:TPSR_Ref TObjectID="7769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45430">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3726.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7775" ObjectName="SW-CX_ZW.CX_ZW_042BK"/>
     <cge:Meas_Ref ObjectId="45430"/>
    <cge:TPSR_Ref TObjectID="7775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-126692">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4314.000000 -331.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23075" ObjectName="SW-CX_ZW.CX_ZW_012BK"/>
     <cge:Meas_Ref ObjectId="126692"/>
    <cge:TPSR_Ref TObjectID="23075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17903" ObjectName="SW-CX_ZW.CX_ZW_045BK"/>
     <cge:Meas_Ref ObjectId="80959"/>
    <cge:TPSR_Ref TObjectID="17903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45473">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7781" ObjectName="SW-CX_ZW.CX_ZW_046BK"/>
     <cge:Meas_Ref ObjectId="45473"/>
    <cge:TPSR_Ref TObjectID="7781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45442">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7777" ObjectName="SW-CX_ZW.CX_ZW_047BK"/>
     <cge:Meas_Ref ObjectId="45442"/>
    <cge:TPSR_Ref TObjectID="7777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45411">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4945.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7773" ObjectName="SW-CX_ZW.CX_ZW_048BK"/>
     <cge:Meas_Ref ObjectId="45411"/>
    <cge:TPSR_Ref TObjectID="7773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-45454">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5082.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7779" ObjectName="SW-CX_ZW.CX_ZW_049BK"/>
     <cge:Meas_Ref ObjectId="45454"/>
    <cge:TPSR_Ref TObjectID="7779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243837">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 -285.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40981" ObjectName="SW-CX_ZW.CX_ZW_051BK"/>
     <cge:Meas_Ref ObjectId="243837"/>
    <cge:TPSR_Ref TObjectID="40981"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2bea0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3569.000000 -160.000000) translate(0,15)">云</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2bea0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3569.000000 -160.000000) translate(0,33)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2bea0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3569.000000 -160.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2cb60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3815.000000 -196.000000) translate(0,15)">子</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2cb60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3815.000000 -196.000000) translate(0,33)">午</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2cb60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3815.000000 -196.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2cb60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3815.000000 -196.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2cb60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3815.000000 -196.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2d110" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -160.000000) translate(0,15)">以</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2d110" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -160.000000) translate(0,33)">口</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2d110" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -160.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3669.000000 -58.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2e150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 -58.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2e730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4480.000000 -157.000000) translate(0,15)">爱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2e730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4480.000000 -157.000000) translate(0,33)">尔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2e730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4480.000000 -157.000000) translate(0,51)">法</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2e730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4480.000000 -157.000000) translate(0,69)">专</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2e730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4480.000000 -157.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a2f610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4120.000000 -601.000000) translate(0,15)">10kVⅠ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a301b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -1021.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a30480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -1119.000000) translate(0,15)">35kV白子东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a30c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.000000 -957.000000) translate(0,15)">35kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a30c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.000000 -957.000000) translate(0,33)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a31440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4029.000000 -739.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a31890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -793.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a31ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -887.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a31d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4251.000000 -861.000000) translate(0,12)">34417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a31f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4551.000000 -739.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a32190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4543.000000 -793.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a323d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -811.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a32610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4917.000000 -449.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a32850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4026.000000 -525.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a32a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -525.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_6a35d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.500000 -1165.500000) translate(0,16)">子午变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a36d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a37030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a37030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a37030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a37030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a37030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a37030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_6a37030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a38d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -636.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a3a140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4579.000000 -639.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_6a3a380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -344.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6a52870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -1119.000000) translate(0,15)">35kV备用出线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6ab0d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -596.000000) translate(0,15)">10kVⅡ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6ac41e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4930.000000 -160.000000) translate(0,15)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6ac41e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4930.000000 -160.000000) translate(0,33)">卫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_6ac41e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4930.000000 -160.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_6aea190" transform="matrix(1.000000 0.000000 0.000000 1.000000 3865.000000 -119.000000) translate(0,10)">PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_6aec830" transform="matrix(1.000000 0.000000 0.000000 1.000000 3939.000000 -244.000000) translate(0,10)">10kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_6aec830" transform="matrix(1.000000 0.000000 0.000000 1.000000 3939.000000 -244.000000) translate(0,22)">跌落</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_6aec830" transform="matrix(1.000000 0.000000 0.000000 1.000000 3939.000000 -244.000000) translate(0,34)">式熔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_6aec830" transform="matrix(1.000000 0.000000 0.000000 1.000000 3939.000000 -244.000000) translate(0,46)">断器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_5410d00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3891.000000 -88.000000) translate(0,10)">10kV线路接地</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_5410d00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3891.000000 -88.000000) translate(0,22)">故障信号发生源</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_541f5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.000000 -344.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_54307a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3680.000000 -450.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_543f7b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4619.000000 -160.000000) translate(0,15)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_543f7b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4619.000000 -160.000000) translate(0,33)">本</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_543f7b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4619.000000 -160.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54437f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -344.000000) translate(0,12)">046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_545b9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -344.000000) translate(0,12)">047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_545ed50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4519.000000 -344.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_546d4d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5068.000000 -160.000000) translate(0,15)">子</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_546d4d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5068.000000 -160.000000) translate(0,33)">宜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_546d4d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5068.000000 -160.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54711b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5105.000000 -344.000000) translate(0,12)">049</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_547c1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4968.000000 -344.000000) translate(0,12)">048</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5480380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 -677.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5480380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 -677.000000) translate(0,27)">SZ11-5000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5480380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 -677.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5480380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 -677.000000) translate(0,57)">Y，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5480380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 -677.000000) translate(0,72)">Ud=7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5481330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -677.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5481330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -677.000000) translate(0,27)">SZ11-5000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5481330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -677.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5481330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -677.000000) translate(0,57)">Y，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5481330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -677.000000) translate(0,72)">Ud=7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54815d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3928.000000 -901.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5481820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -953.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5481a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.500000 -1000.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5481c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.500000 -946.000000) translate(0,12)">34160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5481eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -1079.000000) translate(0,12)">3413</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54820f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3920.000000 -847.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5482330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 -897.000000) translate(0,12)">34117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5482570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -942.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54827b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -898.000000) translate(0,12)">34217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54829f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -849.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5482c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -1079.000000) translate(0,12)">3423</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5482e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -947.000000) translate(0,12)">34260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54830b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -1004.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54832f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4388.500000 -955.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5483530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -898.000000) translate(0,12)">34317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5483770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4679.500000 -849.000000) translate(0,12)">3431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54839b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4745.000000 -1059.000000) translate(0,12)">3433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5483bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -947.000000) translate(0,12)">34360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5483e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1004.000000) translate(0,12)">34367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5484070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4679.000000 -954.000000) translate(0,12)">3436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54842b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.500000 -903.000000) translate(0,12)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54844f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4034.000000 -776.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5484730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -776.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5484970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -475.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5484bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.000000 -471.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5484df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4755.000000 -464.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5485030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4229.000000 -464.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5485270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3612.000000 -297.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54854b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3612.500000 -391.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54856f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -208.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5485930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3624.000000 -207.000000) translate(0,12)">04267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5485b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -299.000000) translate(0,12)">0423</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5485db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3751.000000 -393.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5485ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 -344.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5486230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 -299.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5486470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -393.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54866b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -344.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54868f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -297.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5486b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -391.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5486d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -390.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5486fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4324.000000 -365.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54871f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.000000 -390.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5487430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -296.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5487670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -390.000000) translate(0,12)">0452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54878b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.000000 -297.000000) translate(0,12)">0466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5487af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.000000 -391.000000) translate(0,12)">0462</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5487d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -208.000000) translate(0,12)">0476</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5487f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4694.000000 -208.000000) translate(0,12)">04767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54881b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4822.000000 -297.000000) translate(0,12)">0473</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54883f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4822.000000 -391.000000) translate(0,12)">0472</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5488630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4971.000000 -297.000000) translate(0,12)">0486</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5488870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4971.000000 -391.000000) translate(0,12)">0482</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5488ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5108.000000 -297.000000) translate(0,12)">0496</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5488cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5108.000000 -391.000000) translate(0,12)">0492</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5489610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4398.000000 -903.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_54899a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -69.000000) translate(0,15)">10kV2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_54899a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -69.000000) translate(0,33)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_548a010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4051.000000 -659.000000) translate(0,12)">档 位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_548ade0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -657.000000) translate(0,12)">档 位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54983d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -697.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_549a0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -691.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_549b2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3496.000000 -1144.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_549b7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3496.000000 -1179.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_549ba90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3203.000000 -769.000000) translate(0,17)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_549ca90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4318.000000 -279.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_54a49c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3127.000000 -192.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_54a49c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3127.000000 -192.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_54a4da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -171.500000) translate(0,16)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_54a6870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -226.000000) translate(0,16)">3805047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_54a72e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3410.000000 -1168.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54a99f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4176.000000 -314.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54b3e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -255.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54b4450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -390.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54b7750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -368.000000) translate(0,12)">05117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54bbb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -227.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54c1db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -52.000000) translate(0,12)">山嘴子线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d3c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3529.000000 -523.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d42c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3529.000000 -538.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d4500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3529.000000 -554.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d4740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3535.000000 -506.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d4980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -446.000000) translate(0,12)">F(Hz):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d4bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3521.000000 -491.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d4e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3521.000000 -475.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d5040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3521.000000 -460.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5084.000000 -521.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d54c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5084.000000 -536.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d5700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5084.000000 -552.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d5940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5090.000000 -504.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d5b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 -489.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d5dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 -473.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d6000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 -458.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_54d6f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5092.000000 -442.000000) translate(0,12)">F(Hz):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_54d71d0" transform="matrix(0.791667 -0.000000 -0.000000 1.098039 3208.583333 -719.254902) translate(0,20)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_54d9870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -1131.000000) translate(0,15)">35kV子地线</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_6a4d290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 -972.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a4dd20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 -915.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a4e7b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 -866.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a649b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -971.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a65440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -914.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a65ed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -865.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a8c230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -972.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a8ccc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -915.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a8d750" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -866.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a9a270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 -833.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6a9e440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 -910.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6aa18c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -749.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_6aa3560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -748.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_54b6a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 -336.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_54bae80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 -195.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_ZW"/>
</svg>