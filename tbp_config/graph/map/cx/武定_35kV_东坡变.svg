<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-174" aopId="3949070" id="thSvg" product="E8000V2" version="1.0" viewBox="2454 -1623 3789 2108">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="67" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <polyline arcFlag="1" points="44,21 45,21 46,21 46,21 47,22 47,22 48,23 48,23 49,24 49,24 49,25 49,26 50,27 50,28 50,28 49,29 49,30 49,31 49,31 48,32 48,32 47,33 47,33 46,34 46,34 45,34 44,34 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,34 45,34 46,34 46,34 47,35 47,35 48,36 48,36 49,37 49,37 49,38 49,39 50,40 50,41 50,41 49,42 49,43 49,44 49,44 48,45 48,45 47,46 47,46 46,47 46,47 45,47 44,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,47 45,47 46,47 46,47 47,48 47,48 48,49 48,49 49,50 49,50 49,51 49,52 50,53 50,54 50,54 49,55 49,56 49,57 49,57 48,58 48,58 47,59 47,59 46,60 46,60 45,60 44,60 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="13" y2="13"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="32"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="21" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="21" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="29" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="88" y2="88"/>
    <polyline points="25,101 27,101 29,100 30,100 32,99 33,98 35,97 36,95 37,93 37,92 38,90 38,88 38,86 37,84 37,83 36,81 35,80 33,78 32,77 30,76 29,76 27,75 25,75 23,75 21,76 20,76 18,77 17,78 15,80 14,81 13,83 13,84 12,86 12,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="67" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="68" y2="68"/>
   </symbol>
   <symbol id="capacitor:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="19" y2="19"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape14">
    <polyline points="9,9 3,12 1,13 1,14 1,14 3,16 6,17 10,19 11,20 11,20 11,21 10,22 6,23 3,25 2,25 2,26 2,27 3,28 6,29 10,31 11,31 11,32 11,33 10,33 6,35 3,36 2,37 2,38 2,39 3,39 6,41 10,42 11,43 11,44 11,44 10,45 6,47 3,48 1,50 1,50 1,51 3,52 9,55 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="54" y2="61"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape99">
    <polyline DF8003:Layer="PUBLIC" points="25,29 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="8" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="41" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="46" y2="46"/>
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.5"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="nonConstantLoad:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape94_0">
    <circle cx="13" cy="62" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="13" y1="38" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="40" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="46" x2="28" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="42" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="31" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="9" y1="0" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="13" y1="7" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="12,70 8,61 18,61 12,70 "/>
   </symbol>
   <symbol id="transformer2:shape94_1">
    <ellipse cx="13" cy="43" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="42" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="38" y2="42"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_278b2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278cdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278de10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278f070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278fc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27906f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27911b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2403810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2403810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27941f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27941f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2795f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2795f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2796fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2798ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2799790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_279a550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_279ae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279c540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279d130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_279e170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279f250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279fbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a06c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a1080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a24e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a3000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27a3fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27a4ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27b34a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a62a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27a74d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27a8ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="2118" width="3799" x="2449" y="-1628"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4384,62 4383,62 4383,62 4382,62 4381,62 4381,63 4380,63 4379,64 4379,64 4379,65 4378,66 4378,66 4378,67 4378,68 4378,69 4378,69 4379,70 4379,71 4379,71 4380,72 4381,72 4381,73 4382,73 4383,73 4383,73 4384,73 " stroke="rgb(50,205,50)" stroke-width="0.815047"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4384,51 4383,51 4383,51 4382,51 4381,51 4381,52 4380,52 4379,53 4379,53 4379,54 4378,55 4378,55 4378,56 4378,57 4378,58 4378,58 4379,59 4379,60 4379,60 4380,61 4381,61 4381,62 4382,62 4383,62 4383,62 4384,62 " stroke="rgb(50,205,50)" stroke-width="0.815047"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4384,24 4383,24 4383,24 4382,24 4381,24 4381,25 4380,25 4379,26 4379,26 4379,27 4378,28 4378,28 4378,29 4378,30 4378,31 4378,31 4379,32 4379,33 4379,33 4380,34 4381,34 4381,35 4382,35 4383,35 4383,35 4384,35 " stroke="rgb(50,205,50)" stroke-width="0.815047"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4384,13 4383,13 4383,13 4382,13 4381,13 4381,14 4380,14 4379,15 4379,15 4379,16 4378,17 4378,17 4378,18 4378,19 4378,20 4378,20 4379,21 4379,22 4379,22 4380,23 4381,23 4381,24 4382,24 4383,24 4383,24 4384,24 " stroke="rgb(50,205,50)" stroke-width="0.815047"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5285" x2="5294" y1="-372" y2="-372"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4372" x2="4372" y1="69" y2="47"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4372" x2="4372" y1="35" y2="13"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4384" x2="4401" y1="51" y2="51"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4384" x2="4401" y1="73" y2="73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4384" x2="4401" y1="13" y2="13"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4384" x2="4402" y1="35" y2="35"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4400" x2="4428" y1="51" y2="51"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4402" x2="4450" y1="73" y2="73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4468" x2="4468" y1="80" y2="51"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4450" x2="4468" y1="80" y2="80"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4450" x2="4450" y1="80" y2="73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4441" x2="4468" y1="51" y2="51"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4419" x2="4415" y1="-55" y2="-50"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4419" x2="4424" y1="-55" y2="-50"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4419" x2="4419" y1="-55" y2="-60"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4423" x2="4415" y1="-83" y2="-83"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4415" x2="4423" y1="-75" y2="-75"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4415" x2="4423" y1="-75" y2="-83"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="6241" x2="6241" y1="-598" y2="-320"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="4242" x2="4534" y1="-1621" y2="-1621"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="4352" x2="4630" y1="483" y2="483"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,79 4427,73 4420,73 4424,79 4424,79 4424,79 " stroke="rgb(60,120,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-260795">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -914.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42733" ObjectName="SW-WD_DP.WD_DP_364BK"/>
     <cge:Meas_Ref ObjectId="260795"/>
    <cge:TPSR_Ref TObjectID="42733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260739">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -912.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42727" ObjectName="SW-WD_DP.WD_DP_363BK"/>
     <cge:Meas_Ref ObjectId="260739"/>
    <cge:TPSR_Ref TObjectID="42727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261187">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.373913 -278.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42787" ObjectName="SW-WD_DP.WD_DP_064BK"/>
     <cge:Meas_Ref ObjectId="261187"/>
    <cge:TPSR_Ref TObjectID="42787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261210">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.104348 -276.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42792" ObjectName="SW-WD_DP.WD_DP_065BK"/>
     <cge:Meas_Ref ObjectId="261210"/>
    <cge:TPSR_Ref TObjectID="42792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261241">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.373913 -273.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42797" ObjectName="SW-WD_DP.WD_DP_012BK"/>
     <cge:Meas_Ref ObjectId="261241"/>
    <cge:TPSR_Ref TObjectID="42797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260878">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.937500 -0.000000 0.000000 -1.000000 4121.562500 -674.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42746" ObjectName="SW-WD_DP.WD_DP_301BK"/>
     <cge:Meas_Ref ObjectId="260878"/>
    <cge:TPSR_Ref TObjectID="42746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260881">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.937500 -0.000000 0.000000 -1.000000 4121.562500 -425.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42749" ObjectName="SW-WD_DP.WD_DP_001BK"/>
     <cge:Meas_Ref ObjectId="260881"/>
    <cge:TPSR_Ref TObjectID="42749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260963">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3473.126582 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42752" ObjectName="SW-WD_DP.WD_DP_061BK"/>
     <cge:Meas_Ref ObjectId="260963"/>
    <cge:TPSR_Ref TObjectID="42752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.126582 -254.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42767" ObjectName="SW-WD_DP.WD_DP_066BK"/>
     <cge:Meas_Ref ObjectId="261059"/>
    <cge:TPSR_Ref TObjectID="42767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261091">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.126582 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42772" ObjectName="SW-WD_DP.WD_DP_067BK"/>
     <cge:Meas_Ref ObjectId="261091"/>
    <cge:TPSR_Ref TObjectID="42772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5289.126582 -254.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42777" ObjectName="SW-WD_DP.WD_DP_068BK"/>
     <cge:Meas_Ref ObjectId="261123"/>
    <cge:TPSR_Ref TObjectID="42777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261155">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5499.126582 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42782" ObjectName="SW-WD_DP.WD_DP_069BK"/>
     <cge:Meas_Ref ObjectId="261155"/>
    <cge:TPSR_Ref TObjectID="42782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260995">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.126582 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42757" ObjectName="SW-WD_DP.WD_DP_062BK"/>
     <cge:Meas_Ref ObjectId="260995"/>
    <cge:TPSR_Ref TObjectID="42757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261027">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.126582 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42762" ObjectName="SW-WD_DP.WD_DP_063BK"/>
     <cge:Meas_Ref ObjectId="261027"/>
    <cge:TPSR_Ref TObjectID="42762"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2711400">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4200.500000 -1094.500000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28413d0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4768.500000 -1092.500000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc4be0">
    <use class="BV-35KV" transform="matrix(-1.431818 -0.000000 0.000000 -1.489362 4732.000000 -493.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_322b990">
    <use class="BV-10KV" transform="matrix(1.431818 0.000000 -0.000000 1.489362 5301.000000 -708.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29ed830">
    <use class="BV-10KV" transform="matrix(1.431818 0.000000 -0.000000 1.489362 4313.000000 -707.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_JY" endPointId="0" endStationName="WD_DP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dongji" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4129,-1209 4129,-1159 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42840" ObjectName="AC-35kV.LN_dongji"/>
    <cge:TPSR_Ref TObjectID="42840_SS-174"/></metadata>
   <polyline fill="none" opacity="0" points="4129,-1209 4129,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_TX" endPointId="0" endStationName="WD_DP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_tiandong" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4697,-1201 4697,-1134 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42839" ObjectName="AC-35kV.LN_tiandong"/>
    <cge:TPSR_Ref TObjectID="42839_SS-174"/></metadata>
   <polyline fill="none" opacity="0" points="4697,-1201 4697,-1134 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-WD_DP.WD_DP_061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3477.000000 82.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42832" ObjectName="EC-WD_DP.WD_DP_061Ld"/>
    <cge:TPSR_Ref TObjectID="42832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_DP.WD_DP_066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4854.000000 71.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42835" ObjectName="EC-WD_DP.WD_DP_066Ld"/>
    <cge:TPSR_Ref TObjectID="42835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_DP.WD_DP_067Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.000000 69.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42836" ObjectName="EC-WD_DP.WD_DP_067Ld"/>
    <cge:TPSR_Ref TObjectID="42836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_DP.WD_DP_068Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5294.000000 71.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42837" ObjectName="EC-WD_DP.WD_DP_068Ld"/>
    <cge:TPSR_Ref TObjectID="42837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_DP.WD_DP_069Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5503.000000 63.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42838" ObjectName="EC-WD_DP.WD_DP_069Ld"/>
    <cge:TPSR_Ref TObjectID="42838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_DP.WD_DP_062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 79.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42833" ObjectName="EC-WD_DP.WD_DP_062Ld"/>
    <cge:TPSR_Ref TObjectID="42833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_DP.WD_DP_063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 81.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42834" ObjectName="EC-WD_DP.WD_DP_063Ld"/>
    <cge:TPSR_Ref TObjectID="42834"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_26126e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -725.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2293900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -993.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f8670" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -916.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c78780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -832.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39c0870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 -991.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_329f780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 -914.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be4340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 -645.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3caff80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 -565.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca62f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 -839.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247c0b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4961.000000 -846.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0d880" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3325.500000 -716.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ce410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.373913 -145.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2907ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4157.373913 -163.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2730380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4382.104348 -143.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_264ed40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5721.104348 -153.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227a890" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4397.000000 -621.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32d1ef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 130.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c024a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.104348 82.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3302cc0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.937500 -0.000000 0.000000 1.000000 4089.687500 -804.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_273dd10" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5394.000000 -622.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_28af550">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4087.000000 -1140.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_281fab0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4655.000000 -1138.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2621c10">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4649.000000 -564.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c59ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -587.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d37bd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 -1090.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1edb820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.373913 -144.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2654490">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.373913 -107.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28c6000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.104348 -142.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ee39f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.104348 -105.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28e0af0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5748.104348 -114.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3239f40">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5345.000000 -614.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b5fd60">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4304.000000 -623.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3520770">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4357.000000 -613.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef1bd0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4681.500000 -167.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2792980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5749.000000 -284.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_323d8f0">
    <use class="BV-10KV" transform="matrix(-1.376518 -0.000000 0.000000 -1.437500 4371.000000 94.437500)" xlink:href="#lightningRod:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5f6f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4372.104348 -17.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ce030">
    <use class="BV-10KV" transform="matrix(-0.937500 0.000000 -0.000000 -1.000000 4091.562500 -464.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3221d10">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -0.937500 0.000000 4228.906250 -515.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b595e0">
    <use class="BV-10KV" transform="matrix(0.937500 -0.000000 0.000000 -1.000000 4120.625000 -511.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3be4020">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3515.676442 -109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c3e730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3477.000000 -93.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27593e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4900.676442 -120.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2900430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4854.000000 -100.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28a2fa0">
    <use class="BV-0KV" transform="matrix(0.163265 -0.000000 0.000000 -0.172414 4387.000000 -584.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b00e00">
    <use class="BV-35KV" transform="matrix(0.163265 -0.000000 0.000000 -0.172414 4732.000000 -594.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3364e20">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5282.000000 -624.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28bc560">
    <use class="BV-0KV" transform="matrix(0.163265 -0.000000 0.000000 -0.172414 5384.000000 -585.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cb19b0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5110.676442 -122.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2753220">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5064.000000 -103.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_226dc00">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5339.676442 -120.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_321f090">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5293.000000 -101.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24fed30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5549.676442 -122.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2471da0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5503.000000 -102.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb16a0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4883.500000 38.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32cd360">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5093.500000 38.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32ce7b0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5321.500000 41.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_275f5e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5531.500000 38.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36caab0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3505.500000 48.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1beb4b0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3743.676442 -111.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_333b320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -96.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36c2b30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3733.500000 46.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33509d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3973.676442 -109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32ca120">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -94.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3342630">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3963.500000 48.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 2701.000000 -1147.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-261558" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3484.000000 193.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261558" ObjectName="WD_DP:WD_DP_061BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-261565" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 192.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261565" ObjectName="WD_DP:WD_DP_062BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-261572" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.000000 195.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261572" ObjectName="WD_DP:WD_DP_063BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-261579" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 184.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261579" ObjectName="WD_DP:WD_DP_066BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-261586" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 180.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261586" ObjectName="WD_DP:WD_DP_067BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-261593" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5294.000000 181.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261593" ObjectName="WD_DP:WD_DP_068BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-261600" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5504.000000 180.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261600" ObjectName="WD_DP:WD_DP_069BK_Cos"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="42810" cx="5009" cy="-822" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42810" cx="4697" cy="-822" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42810" cx="4129" cy="-822" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42810" cx="4697" cy="-822" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42810" cx="3442" cy="-822" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42810" cx="4130" cy="-822" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42812" cx="4266" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42812" cx="4419" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42812" cx="4526" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42812" cx="4348" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42812" cx="4130" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42813" cx="4659" cy="-376" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42813" cx="5336" cy="-376" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42813" cx="4859" cy="-376" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42813" cx="5069" cy="-376" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42813" cx="5298" cy="-376" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42813" cx="5758" cy="-376" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42813" cx="5508" cy="-376" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42812" cx="3482" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42812" cx="3710" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42812" cx="3940" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-119710" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2890.500000 -1123.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22307" ObjectName="DYN-WD_DP"/>
     <cge:Meas_Ref ObjectId="119710"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,16)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,76)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,96)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,116)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,136)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,156)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,196)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,256)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c39f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -1119.000000) translate(0,276)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,16)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,96)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,136)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,196)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,256)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,276)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,296)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,316)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_371b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -674.000000) translate(0,336)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="40" graphid="g_26ffa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2705.000000 -1222.500000) translate(0,32)">东坡变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_283dd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -489.000000) translate(0,16)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_322c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4642.000000 -410.000000) translate(0,16)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39a6ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -857.000000) translate(0,16)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_266d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4145.500000 -1027.000000) translate(0,16)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_272fda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.500000 -867.000000) translate(0,16)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_322c220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4145.000000 -951.000000) translate(0,16)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1c477d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -759.000000) translate(0,16)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_322d170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4584.000000 -783.000000) translate(0,16)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3232830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -1050.000000) translate(0,16)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_28b0510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2987.000000 -1197.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2762b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2987.000000 -1232.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3ca1230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2536.000000 -307.000000) translate(0,16)">武定巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22f4a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2673.000000 -322.500000) translate(0,16)">18787878990</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22f4a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2673.000000 -322.500000) translate(0,36)">18787842893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22f4a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2673.000000 -322.500000) translate(0,56)">13987880311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3bfd860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2673.000000 -367.500000) translate(0,16)">8865018</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3bfd860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2673.000000 -367.500000) translate(0,36)">13508784637</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f276f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4008.000000 -969.000000) translate(0,16)">36460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2650bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4010.000000 -886.000000) translate(0,16)">36417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b62ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.500000 -1025.000000) translate(0,16)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2872f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 -949.000000) translate(0,16)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_29a8e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -1048.000000) translate(0,16)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_274dec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -967.000000) translate(0,16)">36360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26ac270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -703.000000) translate(0,16)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_283be90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.500000 -865.000000) translate(0,16)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2287940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -893.000000) translate(0,16)">36317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2615520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5035.500000 -878.000000) translate(0,16)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ee7e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4886.000000 -900.000000) translate(0,16)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_22a26c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4935.000000 -1105.000000) translate(0,16)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_274c920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3381.000000 -792.000000) translate(0,16)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26219b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3340.000000 -695.000000) translate(0,16)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37203b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4165.000000 -206.000000) translate(0,16)">06460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_27590d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4277.000000 -91.000000) translate(0,16)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_321d010" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4382.142857 113.200000) translate(0,16)">10kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_321d010" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4382.142857 113.200000) translate(0,36)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335a7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4356.000000 -142.000000) translate(0,16)">06560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26500a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4437.104348 -306.000000) translate(0,16)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_39e23f0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5696.142857 134.200000) translate(0,16)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32d3a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5658.000000 -207.000000) translate(0,16)">05110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32d3c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5776.104348 -316.000000) translate(0,16)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22ec2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3362.000000 -410.000000) translate(0,16)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32ba4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5416.000000 -688.000000) translate(0,16)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32ba4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5416.000000 -688.000000) translate(0,36)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_29d3600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4407.000000 -602.000000) translate(0,16)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_29d3600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4407.000000 -602.000000) translate(0,36)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_28ffe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -306.000000) translate(0,20)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_322a180" transform="matrix(0.937500 -0.000000 -0.000000 1.000000 4152.031250 -454.000000) translate(0,16)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_28c4b00" transform="matrix(0.937500 -0.000000 -0.000000 1.000000 4144.062500 -792.000000) translate(0,16)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_28c4cd0" transform="matrix(0.937500 -0.000000 -0.000000 1.000000 4152.500000 -708.000000) translate(0,16)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_323d280" transform="matrix(0.937500 -0.000000 -0.000000 1.000000 4094.375000 -765.000000) translate(0,10)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_27517a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -679.000000) translate(0,16)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_27517a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -679.000000) translate(0,36)">SZ11-8000/35 YN,D11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_27517a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -679.000000) translate(0,56)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_27517a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -679.000000) translate(0,76)">Uk=7.43%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26d9730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4539.000000 -313.000000) translate(0,16)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_29caf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4141.000000 158.000000) translate(0,16)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2421280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -423.000000) translate(0,16)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b01920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5348.000000 -424.000000) translate(0,16)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_32d77a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2904.000000 -1213.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2721290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2593.000000 -771.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3c45a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4186.000000 -675.000000) translate(0,20)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26dd440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.000000 -60.000000) translate(0,16)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2859520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.000000 -1232.000000) translate(0,16)">35kV田东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2767240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -1232.000000) translate(0,16)">35kV东己线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fd690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3385.000000 -150.000000) translate(0,12)">06160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2731430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4750.000000 -160.000000) translate(0,12)">06660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2415420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4087.000000 -115.000000) translate(0,16)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c630e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4873.000000 -68.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2408d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4960.000000 -162.000000) translate(0,12)">06760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2820e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5083.000000 -70.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c504c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -160.000000) translate(0,12)">06860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26cfd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5312.000000 -68.000000) translate(0,12)">0686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e95d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5399.000000 -162.000000) translate(0,12)">06960</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ea6e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5522.000000 -70.000000) translate(0,12)">0696</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22be660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4876.000000 -283.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bea80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5088.000000 -285.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22becc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5311.000000 -283.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bef00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5523.000000 -285.000000) translate(0,12)">069</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_242f740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.373913 -308.000000) translate(0,16)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2634870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3496.000000 -276.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2634ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.000000 -68.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26350e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3725.000000 -278.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2635320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 -70.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2635560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -156.000000) translate(0,12)">06260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26357a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3953.000000 -276.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26359e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -68.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2635c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3841.000000 -151.000000) translate(0,12)">06360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d2ee70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4832.000000 79.000000) translate(0,12)">河东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a37b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5042.000000 78.000000) translate(0,12)">以赤叨线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d2f520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5276.000000 82.000000) translate(0,12)">东万线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d2fa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5475.000000 78.000000) translate(0,12)">白马口线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33867e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3459.000000 95.000000) translate(0,12)">环州线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3387060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3684.000000 97.000000) translate(0,12)">东环线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33872e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 97.000000) translate(0,12)">东坡线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3388520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2592.000000 -725.000000) translate(0,16)">隔刀远控</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.022222 -0.000000 0.000000 -1.212121 181.288889 157.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32d91d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 579.000000) translate(0,15)">油温(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b5bbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 594.000000) translate(0,15)">档位(档)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_28658d0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4026.500000 1274.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_275b280" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4038.344262 1296.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_324c890" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4051.327869 1251.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3caf300" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4209.500000 775.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2886480" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4221.344262 797.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_28866c0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4234.327869 752.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_28b5cd0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3980.500000 434.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_28b5f30" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3992.344262 456.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_28b6170" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4005.327869 411.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_26ff410" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4531.500000 104.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_26ff680" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4543.344262 126.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2767eb0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4556.327869 81.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_27681e0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4148.500000 -221.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2613e40" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4160.344262 -199.739130) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2614080" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4173.327869 -244.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -397.000000 6.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3669a50" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3577.508772 502.450904) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb96a0" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3577.508772 521.107143) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dde40" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3589.017544 462.010179) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26de0c0" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3567.000000 440.571990) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28b80d0" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3616.500000 417.627813) translate(0,12)">HZ:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28b8580" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3579.508772 482.462078) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2287.000000 4.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2750a00" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3577.508772 502.450904) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efa930" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3577.508772 521.107143) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efab70" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3589.017544 462.010179) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efadb0" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3567.000000 440.571990) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2e730" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3616.500000 417.627813) translate(0,12)">HZ:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2e970" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3579.508772 482.462078) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1567.000000 -416.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eff2d0" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3577.508772 502.450904) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eff520" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3577.508772 521.107143) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eff760" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3589.017544 462.010179) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef7dd0" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3567.000000 440.571990) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef8010" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3616.500000 417.627813) translate(0,12)">HZ:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef8250" transform="matrix(1.438596 -0.000000 0.000000 -1.332588 3579.508772 482.462078) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27317a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -195.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_272f200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3892.000000 -148.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c9530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.500000 -163.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_336bde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 -179.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_274fa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3448.000000 -192.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_274fcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3428.000000 -145.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_274ff10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3417.500000 -160.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2750150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3442.000000 -176.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c40fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -182.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c411f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 -135.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c41430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.500000 -150.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c41670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 -166.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26f93c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3695.000000 -190.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26f9630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.000000 -143.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26f9870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.500000 -158.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26f9ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -174.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1b83920" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4385.500000 -189.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1b83b30" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4397.344262 -167.739130) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1b83d70" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4410.327869 -212.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2409480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5027.000000 -180.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24096f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5007.000000 -133.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2409930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.500000 -148.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2409b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5021.000000 -164.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c50be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5256.000000 -182.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c50e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5236.000000 -135.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c51090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5225.500000 -150.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c512d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5250.000000 -166.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e9cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5466.000000 -180.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e9f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5446.000000 -133.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22ea1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5435.500000 -148.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c488d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5460.000000 -164.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33878f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4772.000000 1229.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="4779" cy="1222" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3387f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 1231.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="4207" cy="1224" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="2455" y="-1291"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="90" stroke="rgb(0,255,0)" stroke-width="1" width="54" x="4347" y="1"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="46" stroke="rgb(0,255,0)" stroke-width="1" width="68" x="4416" y="42"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="4" stroke="rgb(0,255,0)" stroke-width="1" width="13" x="4428" y="49"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_DP.WD_DP_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3243,-822 5106,-822 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42810" ObjectName="BS-WD_DP.WD_DP_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="42810"/></metadata>
   <polyline fill="none" opacity="0" points="3243,-822 5106,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_DP.WD_DP_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3339,-373 4559,-373 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42812" ObjectName="BS-WD_DP.WD_DP_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="42812"/></metadata>
   <polyline fill="none" opacity="0" points="3339,-373 4559,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_DP.WD_DP_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4635,-376 5956,-376 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42813" ObjectName="BS-WD_DP.WD_DP_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="42813"/></metadata>
   <polyline fill="none" opacity="0" points="4635,-376 5956,-376 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 5024.000000 -977.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 5024.000000 -977.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5745.000000 48.000000)" xlink:href="#transformer2:shape94_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5745.000000 48.000000)" xlink:href="#transformer2:shape94_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_DP.WD_DP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="18676"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.000000 -570.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.000000 -570.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="42808" ObjectName="TF-WD_DP.WD_DP_1T"/>
    <cge:TPSR_Ref TObjectID="42808"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3caf7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-752 4656,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42740@0" ObjectIDZND0="g_26126e0@0" Pin0InfoVect0LinkObjId="g_26126e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260852_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-752 4656,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c53560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4082,-1020 4082,-1011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42738@0" ObjectIDZND0="g_2293900@0" Pin0InfoVect0LinkObjId="g_2293900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4082,-1020 4082,-1011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be5650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-1086 4080,-1075 4192,-1075 4192,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_28af550@0" ObjectIDZND0="g_2711400@0" Pin0InfoVect0LinkObjId="g_2711400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28af550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-1086 4080,-1075 4192,-1075 4192,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c21a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4082,-943 4082,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42737@0" ObjectIDZND0="g_36f8670@0" Pin0InfoVect0LinkObjId="g_36f8670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260799_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4082,-943 4082,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2822d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4081,-858 4081,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42736@0" ObjectIDZND0="g_3c78780@0" Pin0InfoVect0LinkObjId="g_3c78780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260798_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4081,-858 4081,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d400d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-1018 4650,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42732@0" ObjectIDZND0="g_39c0870@0" Pin0InfoVect0LinkObjId="g_39c0870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-1018 4650,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c21f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1084 4648,-1073 4760,-1073 4760,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_281fab0@0" ObjectIDZND0="g_28413d0@0" Pin0InfoVect0LinkObjId="g_28413d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_281fab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1084 4648,-1073 4760,-1073 4760,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f3cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-941 4650,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42731@0" ObjectIDZND0="g_329f780@0" Pin0InfoVect0LinkObjId="g_329f780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260743_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-941 4650,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3445f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-663 4742,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_1be4340@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1be4340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-663 4742,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2708c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-674 4742,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="42741@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260853_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-674 4742,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a010d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-592 4697,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3c59ea0@1" ObjectIDZND0="g_1cc4be0@0" Pin0InfoVect0LinkObjId="g_1cc4be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c59ea0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-592 4697,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a7420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-866 4649,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42730@0" ObjectIDZND0="g_1ca62f0@0" Pin0InfoVect0LinkObjId="g_1ca62f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-866 4649,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ee84d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-873 4967,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42745@0" ObjectIDZND0="g_247c0b0@0" Pin0InfoVect0LinkObjId="g_247c0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260877_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-873 4967,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f3890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-853 5009,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42744@0" ObjectIDZND0="42810@0" Pin0InfoVect0LinkObjId="g_271d600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260876_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-853 5009,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_271d600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-846 4697,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42728@0" ObjectIDZND0="42810@0" Pin0InfoVect0LinkObjId="g_29f3890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-846 4697,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5e3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-848 4129,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42734@0" ObjectIDZND0="42810@0" Pin0InfoVect0LinkObjId="g_29f3890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260796_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-848 4129,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28beb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4082,-1056 4082,-1065 4129,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="42738@1" ObjectIDZND0="42735@x" ObjectIDZND1="g_3d37bd0@0" Pin0InfoVect0LinkObjId="SW-260797_0" Pin0InfoVect1LinkObjId="g_3d37bd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260800_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4082,-1056 4082,-1065 4129,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a68e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-1065 4129,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="42738@x" ObjectIDND1="g_3d37bd0@0" ObjectIDZND0="42735@1" Pin0InfoVect0LinkObjId="SW-260797_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260800_0" Pin1InfoVect1LinkObjId="g_3d37bd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-1065 4129,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39c0e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4082,-979 4082,-985 4129,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="42737@1" ObjectIDZND0="42733@x" ObjectIDZND1="42735@x" Pin0InfoVect0LinkObjId="SW-260795_0" Pin0InfoVect1LinkObjId="SW-260797_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4082,-979 4082,-985 4129,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a5920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-949 4129,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42733@1" ObjectIDZND0="42737@x" ObjectIDZND1="42735@x" Pin0InfoVect0LinkObjId="SW-260799_0" Pin0InfoVect1LinkObjId="SW-260797_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260795_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-949 4129,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-985 4129,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42737@x" ObjectIDND1="42733@x" ObjectIDZND0="42735@0" Pin0InfoVect0LinkObjId="SW-260797_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260799_0" Pin1InfoVect1LinkObjId="SW-260795_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-985 4129,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f4150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-1054 4650,-1062 4697,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="42732@1" ObjectIDZND0="42729@x" ObjectIDZND1="42839@1" Pin0InfoVect0LinkObjId="SW-260741_0" Pin0InfoVect1LinkObjId="g_29f3f10_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260744_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-1054 4650,-1062 4697,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5e5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-1031 4697,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="42729@1" ObjectIDZND0="42732@x" ObjectIDZND1="42839@1" Pin0InfoVect0LinkObjId="SW-260744_0" Pin0InfoVect1LinkObjId="g_29f3f10_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-1031 4697,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f3f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-1062 4697,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="42732@x" ObjectIDND1="42729@x" ObjectIDZND0="42839@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260744_0" Pin1InfoVect1LinkObjId="SW-260741_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-1062 4697,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_321d380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-977 4650,-985 4697,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="42731@1" ObjectIDZND0="42727@x" ObjectIDZND1="42729@x" Pin0InfoVect0LinkObjId="SW-260739_0" Pin0InfoVect1LinkObjId="SW-260741_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260743_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-977 4650,-985 4697,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b6270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-947 4697,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42727@1" ObjectIDZND0="42731@x" ObjectIDZND1="42729@x" Pin0InfoVect0LinkObjId="SW-260743_0" Pin0InfoVect1LinkObjId="SW-260741_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260739_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-947 4697,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c69f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-985 4697,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42731@x" ObjectIDND1="42727@x" ObjectIDZND0="42729@0" Pin0InfoVect0LinkObjId="SW-260741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260743_0" Pin1InfoVect1LinkObjId="SW-260739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-985 4697,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1edf500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-902 4649,-910 4697,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="42730@1" ObjectIDZND0="42727@x" ObjectIDZND1="42728@x" Pin0InfoVect0LinkObjId="SW-260739_0" Pin0InfoVect1LinkObjId="SW-260740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260742_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-902 4649,-910 4697,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_329d250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-920 4697,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42727@0" ObjectIDZND0="42730@x" ObjectIDZND1="42728@x" Pin0InfoVect0LinkObjId="SW-260742_0" Pin0InfoVect1LinkObjId="SW-260740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-920 4697,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d3530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-910 4697,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42730@x" ObjectIDND1="42727@x" ObjectIDZND0="42728@1" Pin0InfoVect0LinkObjId="SW-260740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260742_0" Pin1InfoVect1LinkObjId="SW-260739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-910 4697,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d2b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-788 4656,-800 4697,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="42740@1" ObjectIDZND0="42739@x" ObjectIDZND1="42810@0" Pin0InfoVect0LinkObjId="SW-260851_0" Pin0InfoVect1LinkObjId="g_29f3890_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260852_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-788 4656,-800 4697,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb0ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-769 4697,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="42739@1" ObjectIDZND0="42740@x" ObjectIDZND1="42810@0" Pin0InfoVect0LinkObjId="SW-260852_0" Pin0InfoVect1LinkObjId="g_29f3890_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260851_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-769 4697,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c51f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-800 4697,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="42740@x" ObjectIDND1="42739@x" ObjectIDZND0="42810@0" Pin0InfoVect0LinkObjId="g_29f3890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260852_0" Pin1InfoVect1LinkObjId="SW-260851_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-800 4697,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c50e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-710 4742,-720 4697,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="42741@1" ObjectIDZND0="42739@x" ObjectIDZND1="g_3c59ea0@0" Pin0InfoVect0LinkObjId="SW-260851_0" Pin0InfoVect1LinkObjId="g_3c59ea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260853_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-710 4742,-720 4697,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_329e320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-733 4697,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="42739@0" ObjectIDZND0="42741@x" ObjectIDZND1="g_3c59ea0@0" Pin0InfoVect0LinkObjId="SW-260853_0" Pin0InfoVect1LinkObjId="g_3c59ea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260851_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-733 4697,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b5fa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-909 4967,-919 5009,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="42745@1" ObjectIDZND0="42744@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-260876_0" Pin0InfoVect1LinkObjId="g_2711400_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260877_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-909 4967,-919 5009,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_324daa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-889 5009,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="42744@1" ObjectIDZND0="42745@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-260877_0" Pin0InfoVect1LinkObjId="g_2711400_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260876_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-889 5009,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28b58b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-919 5009,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="42745@x" ObjectIDND1="42744@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2711400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260877_0" Pin1InfoVect1LinkObjId="SW-260876_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-919 5009,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_227d000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-1159 4129,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" ObjectIDND0="42840@1" ObjectIDZND0="g_3d37bd0@1" Pin0InfoVect0LinkObjId="g_3d37bd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-1159 4129,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32bf150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-1095 4129,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3d37bd0@0" ObjectIDZND0="42738@x" ObjectIDZND1="42735@x" Pin0InfoVect0LinkObjId="SW-260800_0" Pin0InfoVect1LinkObjId="SW-260797_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d37bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-1095 4129,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2622bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3352,-710 3343,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42743@0" ObjectIDZND0="g_2a0d880@0" Pin0InfoVect0LinkObjId="g_2a0d880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3352,-710 3343,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26222b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3442,-803 3442,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42742@0" ObjectIDZND0="42810@0" Pin0InfoVect0LinkObjId="g_29f3890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260874_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3442,-803 3442,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ee7530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3388,-710 3398,-710 3398,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="42743@1" ObjectIDZND0="42742@x" Pin0InfoVect0LinkObjId="SW-260874_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260875_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3388,-710 3398,-710 3398,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f6310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4081,-894 4081,-906 4129,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="42736@1" ObjectIDZND0="42733@x" ObjectIDZND1="42734@x" Pin0InfoVect0LinkObjId="SW-260795_0" Pin0InfoVect1LinkObjId="SW-260796_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260798_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4081,-894 4081,-906 4129,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1babad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-922 4129,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42733@0" ObjectIDZND0="42736@x" ObjectIDZND1="42734@x" Pin0InfoVect0LinkObjId="SW-260798_0" Pin0InfoVect1LinkObjId="SW-260796_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260795_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-922 4129,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3337e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-906 4129,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42736@x" ObjectIDND1="42733@x" ObjectIDZND0="42734@1" Pin0InfoVect0LinkObjId="SW-260796_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260798_0" Pin1InfoVect1LinkObjId="SW-260795_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-906 4129,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32182b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-349 4266,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42788@0" ObjectIDZND0="42812@0" Pin0InfoVect0LinkObjId="g_3359810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-349 4266,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c58e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-313 4266,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42787@1" ObjectIDZND0="42788@1" Pin0InfoVect0LinkObjId="SW-261188_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261187_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-313 4266,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c39b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-264 4266,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42789@1" ObjectIDZND0="42787@0" Pin0InfoVect0LinkObjId="SW-261187_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261188_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-264 4266,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35475f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4235,-181 4235,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42790@0" ObjectIDZND0="g_26ce410@0" Pin0InfoVect0LinkObjId="g_26ce410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261189_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4235,-181 4235,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a00b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-232 4298,-232 4298,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42789@x" ObjectIDND1="42790@x" ObjectIDND2="g_2654490@0" ObjectIDZND0="g_1edb820@0" Pin0InfoVect0LinkObjId="g_1edb820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-261188_0" Pin1InfoVect1LinkObjId="SW-261189_0" Pin1InfoVect2LinkObjId="g_2654490_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-232 4298,-232 4298,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_227b5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-247 4266,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42789@0" ObjectIDZND0="g_1edb820@0" ObjectIDZND1="42790@x" ObjectIDZND2="g_2654490@0" Pin0InfoVect0LinkObjId="g_1edb820_0" Pin0InfoVect1LinkObjId="SW-261189_0" Pin0InfoVect2LinkObjId="g_2654490_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-247 4266,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d0240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4236,-217 4266,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42790@1" ObjectIDZND0="g_1edb820@0" ObjectIDZND1="42789@x" ObjectIDZND2="g_2654490@0" Pin0InfoVect0LinkObjId="g_1edb820_0" Pin0InfoVect1LinkObjId="SW-261188_0" Pin0InfoVect2LinkObjId="g_2654490_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261189_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4236,-217 4266,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb0700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-232 4266,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1edb820@0" ObjectIDND1="42789@x" ObjectIDZND0="42790@x" ObjectIDZND1="g_2654490@0" Pin0InfoVect0LinkObjId="SW-261189_0" Pin0InfoVect1LinkObjId="g_2654490_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1edb820_0" Pin1InfoVect1LinkObjId="SW-261188_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-232 4266,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_323bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-217 4266,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="42790@x" ObjectIDND1="g_1edb820@0" ObjectIDND2="42789@x" ObjectIDZND0="g_2654490@1" Pin0InfoVect0LinkObjId="g_2654490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-261189_0" Pin1InfoVect1LinkObjId="g_1edb820_0" Pin1InfoVect2LinkObjId="SW-261188_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-217 4266,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3359810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-347 4419,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42793@0" ObjectIDZND0="42812@0" Pin0InfoVect0LinkObjId="g_32182b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261211_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-347 4419,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29204c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-311 4419,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42792@1" ObjectIDZND0="42793@1" Pin0InfoVect0LinkObjId="SW-261211_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-311 4419,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2700630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-262 4419,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42794@1" ObjectIDZND0="42792@0" Pin0InfoVect0LinkObjId="SW-261210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261211_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-262 4419,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c48a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4388,-179 4388,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42795@0" ObjectIDZND0="g_2730380@0" Pin0InfoVect0LinkObjId="g_2730380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261212_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4388,-179 4388,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d46b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-230 4451,-230 4451,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42794@x" ObjectIDND1="42795@x" ObjectIDND2="g_1ee39f0@0" ObjectIDZND0="g_28c6000@0" Pin0InfoVect0LinkObjId="g_28c6000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-261211_0" Pin1InfoVect1LinkObjId="SW-261212_0" Pin1InfoVect2LinkObjId="g_1ee39f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-230 4451,-230 4451,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a0c160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-245 4419,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42794@0" ObjectIDZND0="g_28c6000@0" ObjectIDZND1="42795@x" ObjectIDZND2="g_1ee39f0@0" Pin0InfoVect0LinkObjId="g_28c6000_0" Pin0InfoVect1LinkObjId="SW-261212_0" Pin0InfoVect2LinkObjId="g_1ee39f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261211_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-245 4419,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2692130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4389,-215 4419,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42795@1" ObjectIDZND0="g_28c6000@0" ObjectIDZND1="42794@x" ObjectIDZND2="g_1ee39f0@0" Pin0InfoVect0LinkObjId="g_28c6000_0" Pin0InfoVect1LinkObjId="SW-261211_0" Pin0InfoVect2LinkObjId="g_1ee39f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261212_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4389,-215 4419,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d9900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-230 4419,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_28c6000@0" ObjectIDND1="42794@x" ObjectIDZND0="42795@x" ObjectIDZND1="g_1ee39f0@0" Pin0InfoVect0LinkObjId="SW-261212_0" Pin0InfoVect1LinkObjId="g_1ee39f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28c6000_0" Pin1InfoVect1LinkObjId="SW-261211_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-230 4419,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_323b270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-215 4419,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="42795@x" ObjectIDND1="g_28c6000@0" ObjectIDND2="42794@x" ObjectIDZND0="g_1ee39f0@1" Pin0InfoVect0LinkObjId="g_1ee39f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-261212_0" Pin1InfoVect1LinkObjId="g_28c6000_0" Pin1InfoVect2LinkObjId="SW-261211_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-215 4419,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3338f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-373 4526,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="42812@0" ObjectIDZND0="42800@0" Pin0InfoVect0LinkObjId="SW-261243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32182b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-373 4526,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c97540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-259 4659,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42799@1" ObjectIDZND0="42797@0" Pin0InfoVect0LinkObjId="SW-261241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-259 4659,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_324ad10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-308 4659,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42797@1" ObjectIDZND0="42798@1" Pin0InfoVect0LinkObjId="SW-261242_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-308 4659,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_261c750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-344 4659,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42798@0" ObjectIDZND0="42813@0" Pin0InfoVect0LinkObjId="g_3238c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-344 4659,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3238c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5758,-357 5758,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42805@0" ObjectIDZND0="42813@0" Pin0InfoVect0LinkObjId="g_261c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5758,-357 5758,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e0890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5727,-189 5727,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42807@0" ObjectIDZND0="g_264ed40@0" Pin0InfoVect0LinkObjId="g_264ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5727,-189 5727,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29df260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5728,-225 5758,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42807@1" ObjectIDZND0="g_28e0af0@0" ObjectIDZND1="42806@x" Pin0InfoVect0LinkObjId="g_28e0af0_0" Pin0InfoVect1LinkObjId="SW-261291_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5728,-225 5758,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29e0070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5758,-225 5758,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="42807@x" ObjectIDND1="42806@x" ObjectIDZND0="g_28e0af0@1" Pin0InfoVect0LinkObjId="g_28e0af0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261292_0" Pin1InfoVect1LinkObjId="SW-261291_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5758,-225 5758,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29dba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5758,83 5758,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="nonConstantLoad" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2711400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2711400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5758,83 5758,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29dbc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5758,-27 5758,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28e0af0@0" Pin0InfoVect0LinkObjId="g_28e0af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2711400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5758,-27 5758,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335e680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5336,-609 5336,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3239f40@1" ObjectIDZND0="g_322b990@0" Pin0InfoVect0LinkObjId="g_322b990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3239f40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5336,-609 5336,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29ed5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5336,-408 5336,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42803@0" ObjectIDZND0="42813@0" Pin0InfoVect0LinkObjId="g_261c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5336,-408 5336,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_227a630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-608 4348,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3520770@1" ObjectIDZND0="g_29ed830@0" Pin0InfoVect0LinkObjId="g_29ed830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3520770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-608 4348,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26beab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-407 4348,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42801@0" ObjectIDZND0="42812@0" Pin0InfoVect0LinkObjId="g_32182b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-407 4348,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a0eaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4688,-221 4658,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ef1bd0@0" ObjectIDZND0="42800@x" ObjectIDZND1="42799@x" Pin0InfoVect0LinkObjId="SW-261243_0" Pin0InfoVect1LinkObjId="SW-261242_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ef1bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4688,-221 4658,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_323e210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5758,-272 5758,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="42806@1" ObjectIDZND0="g_2792980@1" Pin0InfoVect0LinkObjId="g_2792980_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5758,-272 5758,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cfa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5758,-321 5758,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2792980@0" ObjectIDZND0="42805@1" Pin0InfoVect0LinkObjId="SW-261291_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2792980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5758,-321 5758,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32d44b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,88 4359,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_323d8f0@0" ObjectIDZND0="g_32d1ef0@0" Pin0InfoVect0LinkObjId="g_32d1ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_323d8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,88 4359,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b96d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-71 4379,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b5f6f0@0" ObjectIDZND0="42796@x" ObjectIDZND1="g_1ee39f0@0" Pin0InfoVect0LinkObjId="SW-261213_0" Pin0InfoVect1LinkObjId="g_1ee39f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5f6f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-71 4379,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c4830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,8 4359,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_323d8f0@1" ObjectIDZND0="0@x" ObjectIDZND1="42796@x" Pin0InfoVect0LinkObjId="g_2711400_0" Pin0InfoVect1LinkObjId="SW-261213_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_323d8f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4359,8 4359,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_274e7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,64 4514,38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c024a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2711400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c024a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4514,64 4514,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c02240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,12 4514,-11 4359,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_323d8f0@0" ObjectIDZND1="42796@x" Pin0InfoVect0LinkObjId="g_323d8f0_0" Pin0InfoVect1LinkObjId="SW-261213_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2711400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4514,12 4514,-11 4359,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c3b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-777 4084,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42748@0" ObjectIDZND0="g_3302cc0@0" Pin0InfoVect0LinkObjId="g_3302cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-777 4084,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be3ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-741 4084,-727 4130,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="42748@1" ObjectIDZND0="42746@x" ObjectIDZND1="42747@x" Pin0InfoVect0LinkObjId="SW-260878_0" Pin0InfoVect1LinkObjId="SW-260879_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-741 4084,-727 4130,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be3d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-709 4130,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42746@1" ObjectIDZND0="42748@x" ObjectIDZND1="42747@x" Pin0InfoVect0LinkObjId="SW-260880_0" Pin0InfoVect1LinkObjId="SW-260879_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260878_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-709 4130,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1baac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-727 4130,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42748@x" ObjectIDND1="42746@x" ObjectIDZND0="42747@0" Pin0InfoVect0LinkObjId="SW-260879_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260880_0" Pin1InfoVect1LinkObjId="SW-260878_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-727 4130,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1baae80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-460 4130,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42749@1" ObjectIDZND0="42751@1" Pin0InfoVect0LinkObjId="SW-260882_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260881_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-460 4130,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-433 4130,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42749@0" ObjectIDZND0="42750@1" Pin0InfoVect0LinkObjId="SW-260882_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260881_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-433 4130,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_274ce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-508 4130,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3221d10@0" ObjectIDZND0="42751@x" ObjectIDZND1="g_2b595e0@0" Pin0InfoVect0LinkObjId="SW-260882_0" Pin0InfoVect1LinkObjId="g_2b595e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3221d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-508 4130,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b4bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-491 4130,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="42751@0" ObjectIDZND0="g_3221d10@0" ObjectIDZND1="g_2b595e0@0" Pin0InfoVect0LinkObjId="g_3221d10_0" Pin0InfoVect1LinkObjId="g_2b595e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-491 4130,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b4e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-508 4130,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3221d10@0" ObjectIDND1="42751@x" ObjectIDZND0="g_2b595e0@0" Pin0InfoVect0LinkObjId="g_2b595e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3221d10_0" Pin1InfoVect1LinkObjId="SW-260882_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-508 4130,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2871c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4085,-518 4085,-562 4130,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_26ce030@0" ObjectIDZND0="42808@x" ObjectIDZND1="g_2b595e0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="g_2b595e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26ce030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4085,-518 4085,-562 4130,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2871ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-575 4130,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="42808@0" ObjectIDZND0="g_26ce030@0" ObjectIDZND1="g_2b595e0@0" Pin0InfoVect0LinkObjId="g_26ce030_0" Pin0InfoVect1LinkObjId="g_2b595e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2871c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-575 4130,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_323d020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-562 4130,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42808@x" ObjectIDND1="g_26ce030@0" ObjectIDZND0="g_2b595e0@1" Pin0InfoVect0LinkObjId="g_2b595e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2871c90_0" Pin1InfoVect1LinkObjId="g_26ce030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-562 4130,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5be20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-801 4130,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42747@1" ObjectIDZND0="42810@0" Pin0InfoVect0LinkObjId="g_29f3890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260879_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-801 4130,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb9ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-395 4130,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42750@0" ObjectIDZND0="42812@0" Pin0InfoVect0LinkObjId="g_32182b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-395 4130,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c45880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-667 4130,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="42808@1" ObjectIDZND0="42746@0" Pin0InfoVect0LinkObjId="SW-260878_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2871c90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-667 4130,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23eb950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-720 4697,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="42741@x" ObjectIDND1="42739@x" ObjectIDZND0="g_3c59ea0@0" Pin0InfoVect0LinkObjId="g_3c59ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260853_0" Pin1InfoVect1LinkObjId="SW-260851_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-720 4697,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26dcf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-11 4359,-27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_323d8f0@0" ObjectIDND1="0@x" ObjectIDZND0="42796@0" Pin0InfoVect0LinkObjId="SW-261213_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_323d8f0_0" Pin1InfoVect1LinkObjId="g_2711400_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-11 4359,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26dd1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-63 4359,-80 4379,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="42796@1" ObjectIDZND0="g_2b5f6f0@0" ObjectIDZND1="g_1ee39f0@0" Pin0InfoVect0LinkObjId="g_2b5f6f0_0" Pin0InfoVect1LinkObjId="g_1ee39f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261213_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-63 4359,-80 4379,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2291f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-291 3482,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42753@1" ObjectIDZND0="42752@1" Pin0InfoVect0LinkObjId="SW-260963_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3482,-291 3482,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22921c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-255 3482,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42752@0" ObjectIDZND0="42754@1" Pin0InfoVect0LinkObjId="SW-260964_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260963_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3482,-255 3482,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c3f280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-309 3482,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42753@0" ObjectIDZND0="42812@0" Pin0InfoVect0LinkObjId="g_32182b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260964_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3482,-309 3482,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bea790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-298 4859,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42768@1" ObjectIDZND0="42767@1" Pin0InfoVect0LinkObjId="SW-261059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-298 4859,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bea9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-262 4859,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42767@0" ObjectIDZND0="42769@1" Pin0InfoVect0LinkObjId="SW-261060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-262 4859,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a8b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-315 4859,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42768@0" ObjectIDZND0="42813@0" Pin0InfoVect0LinkObjId="g_261c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-315 4859,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e1230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-576 4348,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3520770@0" ObjectIDZND0="42801@1" Pin0InfoVect0LinkObjId="SW-261279_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3520770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-576 4348,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28e1490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4391,-593 4391,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_28a2fa0@0" ObjectIDZND0="g_227a890@0" Pin0InfoVect0LinkObjId="g_227a890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a2fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4391,-593 4391,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ca4940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4391,-585 4391,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_28a2fa0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2711400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a2fa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4391,-585 4391,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca4ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4311,-569 4311,-552 4391,-552 4391,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1b5fd60@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2711400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b5fd60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4311,-569 4311,-552 4391,-552 4391,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca4e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4736,-595 4736,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2b00e00@1" ObjectIDZND0="g_3caff80@0" Pin0InfoVect0LinkObjId="g_3caff80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b00e00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4736,-595 4736,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cb8c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-618 4642,-639 4736,-639 4736,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_2621c10@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2711400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2621c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-618 4642,-639 4736,-639 4736,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cb8f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4736,-609 4736,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b00e00@0" Pin0InfoVect0LinkObjId="g_2b00e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2711400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4736,-609 4736,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273dad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5336,-577 5336,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3239f40@0" ObjectIDZND0="42803@1" Pin0InfoVect0LinkObjId="SW-261281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3239f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5336,-577 5336,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3caa8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5388,-594 5388,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_28bc560@0" ObjectIDZND0="g_273dd10@0" Pin0InfoVect0LinkObjId="g_273dd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28bc560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5388,-594 5388,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c04ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5388,-586 5388,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_28bc560@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2711400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28bc560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5388,-586 5388,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c04f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5289,-570 5289,-546 5388,-546 5388,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_3364e20@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2711400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3364e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5289,-570 5289,-546 5388,-546 5388,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c05170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3440,-164 3440,-181 3482,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42755@0" ObjectIDZND0="g_3c3e730@0" ObjectIDZND1="g_3be4020@0" ObjectIDZND2="42754@x" Pin0InfoVect0LinkObjId="g_3c3e730_0" Pin0InfoVect1LinkObjId="g_3be4020_0" Pin0InfoVect2LinkObjId="SW-260964_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260965_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3440,-164 3440,-181 3482,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c5e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-181 3482,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="42755@x" ObjectIDND1="g_3be4020@0" ObjectIDND2="42754@x" ObjectIDZND0="g_3c3e730@1" Pin0InfoVect0LinkObjId="g_3c3e730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-260965_0" Pin1InfoVect1LinkObjId="g_3be4020_0" Pin1InfoVect2LinkObjId="SW-260964_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3482,-181 3482,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c6060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3523,-163 3523,-201 3482,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3be4020@0" ObjectIDZND0="g_3c3e730@0" ObjectIDZND1="42755@x" ObjectIDZND2="42754@x" Pin0InfoVect0LinkObjId="g_3c3e730_0" Pin0InfoVect1LinkObjId="SW-260965_0" Pin0InfoVect2LinkObjId="SW-260964_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3be4020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3523,-163 3523,-201 3482,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24086e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-232 3482,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42754@0" ObjectIDZND0="g_3c3e730@0" ObjectIDZND1="42755@x" ObjectIDZND2="g_3be4020@0" Pin0InfoVect0LinkObjId="g_3c3e730_0" Pin0InfoVect1LinkObjId="SW-260965_0" Pin0InfoVect2LinkObjId="g_3be4020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260964_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3482,-232 3482,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2408940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-201 3482,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3be4020@0" ObjectIDND1="42754@x" ObjectIDZND0="g_3c3e730@0" ObjectIDZND1="42755@x" Pin0InfoVect0LinkObjId="g_3c3e730_0" Pin0InfoVect1LinkObjId="SW-260965_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3be4020_0" Pin1InfoVect1LinkObjId="SW-260964_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3482,-201 3482,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c533d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4908,-174 4908,-213 4859,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_27593e0@0" ObjectIDZND0="42769@x" ObjectIDZND1="g_2900430@0" ObjectIDZND2="42770@x" Pin0InfoVect0LinkObjId="SW-261060_0" Pin0InfoVect1LinkObjId="g_2900430_0" Pin0InfoVect2LinkObjId="SW-261061_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27593e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4908,-174 4908,-213 4859,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2414ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-239 4859,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42769@0" ObjectIDZND0="g_27593e0@0" ObjectIDZND1="g_2900430@0" ObjectIDZND2="42770@x" Pin0InfoVect0LinkObjId="g_27593e0_0" Pin0InfoVect1LinkObjId="g_2900430_0" Pin0InfoVect2LinkObjId="SW-261061_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-239 4859,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2414f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-213 4859,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_27593e0@0" ObjectIDND1="42769@x" ObjectIDZND0="g_2900430@0" ObjectIDZND1="42770@x" Pin0InfoVect0LinkObjId="g_2900430_0" Pin0InfoVect1LinkObjId="SW-261061_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27593e0_0" Pin1InfoVect1LinkObjId="SW-261060_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-213 4859,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b83fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5758,-255 5758,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="42806@0" ObjectIDZND0="42807@x" ObjectIDZND1="g_28e0af0@0" Pin0InfoVect0LinkObjId="SW-261292_0" Pin0InfoVect1LinkObjId="g_28e0af0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5758,-255 5758,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3235820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3341,-747 3398,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="42743@x" ObjectIDZND1="42742@x" Pin0InfoVect0LinkObjId="SW-260875_0" Pin0InfoVect1LinkObjId="SW-260874_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3341,-747 3398,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3235a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3398,-747 3442,-747 3442,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="42743@x" ObjectIDZND0="42742@1" Pin0InfoVect0LinkObjId="SW-260874_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3398,-747 3442,-747 3442,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39e6570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-110 4419,-85 4418,-80 4379,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1ee39f0@0" ObjectIDZND0="g_2b5f6f0@0" ObjectIDZND1="42796@x" Pin0InfoVect0LinkObjId="g_2b5f6f0_0" Pin0InfoVect1LinkObjId="SW-261213_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ee39f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-110 4419,-85 4418,-80 4379,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_263c290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-124 4151,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42815@0" ObjectIDZND0="g_2907ca0@0" Pin0InfoVect0LinkObjId="g_2907ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-124 4151,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2876fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-97 4266,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="42791@1" ObjectIDZND0="g_2654490@0" Pin0InfoVect0LinkObjId="g_2654490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-97 4266,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2877200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-40 4266,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="42815@x" ObjectIDZND0="42814@x" ObjectIDZND1="42791@x" Pin0InfoVect0LinkObjId="CB-WD_DP.WD_DP_1Cb_0" Pin0InfoVect1LinkObjId="SW-261190_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-40 4266,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_240a090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-88 4151,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="42815@1" ObjectIDZND0="42814@x" ObjectIDZND1="42791@x" Pin0InfoVect0LinkObjId="CB-WD_DP.WD_DP_1Cb_0" Pin0InfoVect1LinkObjId="SW-261190_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261191_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-88 4151,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_240a2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-40 4151,132 4267,132 4267,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="42814@x" ObjectIDND1="42791@x" ObjectIDND2="42815@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="CB-WD_DP.WD_DP_1Cb_0" Pin1InfoVect1LinkObjId="SW-261190_0" Pin1InfoVect2LinkObjId="SW-261191_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-40 4151,132 4267,132 4267,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_240ac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-25 4266,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42814@0" ObjectIDZND0="42815@x" ObjectIDZND1="42791@x" Pin0InfoVect0LinkObjId="SW-261191_0" Pin0InfoVect1LinkObjId="SW-261190_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-WD_DP.WD_DP_1Cb_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-25 4266,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3627ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-40 4266,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="42815@x" ObjectIDND1="42814@x" ObjectIDZND0="42791@0" Pin0InfoVect0LinkObjId="SW-261190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261191_0" Pin1InfoVect1LinkObjId="CB-WD_DP.WD_DP_1Cb_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-40 4266,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36289b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-267 4526,-174 4659,-174 4659,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42800@1" ObjectIDZND0="g_1ef1bd0@0" ObjectIDZND1="42799@x" Pin0InfoVect0LinkObjId="g_1ef1bd0_0" Pin0InfoVect1LinkObjId="SW-261242_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-267 4526,-174 4659,-174 4659,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ff200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-221 4659,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1ef1bd0@0" ObjectIDND1="42800@x" ObjectIDZND0="42799@0" Pin0InfoVect0LinkObjId="SW-261242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ef1bd0_0" Pin1InfoVect1LinkObjId="SW-261243_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-221 4659,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c53100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-106 4859,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2900430@0" ObjectIDZND0="42771@1" Pin0InfoVect0LinkObjId="SW-261062_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2900430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-106 4859,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2618180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-300 5069,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42773@1" ObjectIDZND0="42772@1" Pin0InfoVect0LinkObjId="SW-261091_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261092_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-300 5069,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26183e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-264 5069,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42772@0" ObjectIDZND0="42774@1" Pin0InfoVect0LinkObjId="SW-261092_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261091_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-264 5069,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e4c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-317 5069,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42773@0" ObjectIDZND0="42813@0" Pin0InfoVect0LinkObjId="g_261c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-317 5069,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2409db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5118,-176 5118,-215 5069,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3cb19b0@0" ObjectIDZND0="42774@x" ObjectIDZND1="g_2753220@0" ObjectIDZND2="42775@x" Pin0InfoVect0LinkObjId="SW-261092_0" Pin0InfoVect1LinkObjId="g_2753220_0" Pin0InfoVect2LinkObjId="SW-261093_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cb19b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5118,-176 5118,-215 5069,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2760e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-241 5069,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42774@0" ObjectIDZND0="g_3cb19b0@0" ObjectIDZND1="g_2753220@0" ObjectIDZND2="42775@x" Pin0InfoVect0LinkObjId="g_3cb19b0_0" Pin0InfoVect1LinkObjId="g_2753220_0" Pin0InfoVect2LinkObjId="SW-261093_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-241 5069,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2761020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-215 5069,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42774@x" ObjectIDND1="g_3cb19b0@0" ObjectIDZND0="g_2753220@0" ObjectIDZND1="42775@x" Pin0InfoVect0LinkObjId="g_2753220_0" Pin0InfoVect1LinkObjId="SW-261093_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261092_0" Pin1InfoVect1LinkObjId="g_3cb19b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-215 5069,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2820bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-108 5069,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2753220@0" ObjectIDZND0="42776@1" Pin0InfoVect0LinkObjId="SW-261094_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2753220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-108 5069,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bc010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-298 5298,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42778@1" ObjectIDZND0="42777@1" Pin0InfoVect0LinkObjId="SW-261123_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261124_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-298 5298,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bc270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-262 5298,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42777@0" ObjectIDZND0="42779@1" Pin0InfoVect0LinkObjId="SW-261124_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-262 5298,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321fab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-315 5298,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42778@0" ObjectIDZND0="42813@0" Pin0InfoVect0LinkObjId="g_261c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261124_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-315 5298,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c51510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5347,-174 5347,-213 5298,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_226dc00@0" ObjectIDZND0="42779@x" ObjectIDZND1="g_321f090@0" ObjectIDZND2="42780@x" Pin0InfoVect0LinkObjId="SW-261124_0" Pin0InfoVect1LinkObjId="g_321f090_0" Pin0InfoVect2LinkObjId="SW-261125_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_226dc00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5347,-174 5347,-213 5298,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2754fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-239 5298,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42779@0" ObjectIDZND0="g_226dc00@0" ObjectIDZND1="g_321f090@0" ObjectIDZND2="42780@x" Pin0InfoVect0LinkObjId="g_226dc00_0" Pin0InfoVect1LinkObjId="g_321f090_0" Pin0InfoVect2LinkObjId="SW-261125_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261124_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-239 5298,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27551c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-213 5298,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42779@x" ObjectIDND1="g_226dc00@0" ObjectIDZND0="g_321f090@0" ObjectIDZND1="42780@x" Pin0InfoVect0LinkObjId="g_321f090_0" Pin0InfoVect1LinkObjId="SW-261125_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261124_0" Pin1InfoVect1LinkObjId="g_226dc00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-213 5298,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cfb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-106 5298,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_321f090@0" ObjectIDZND0="42781@1" Pin0InfoVect0LinkObjId="SW-261126_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_321f090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-106 5298,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_399ce60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-300 5508,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42783@1" ObjectIDZND0="42782@1" Pin0InfoVect0LinkObjId="SW-261155_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261156_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-300 5508,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_399d0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-264 5508,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42782@0" ObjectIDZND0="42784@1" Pin0InfoVect0LinkObjId="SW-261156_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261155_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-264 5508,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24727c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-317 5508,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42783@0" ObjectIDZND0="42813@0" Pin0InfoVect0LinkObjId="g_261c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261156_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-317 5508,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c48ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5557,-176 5557,-215 5508,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24fed30@0" ObjectIDZND0="g_2471da0@0" ObjectIDZND1="42785@x" ObjectIDZND2="42784@x" Pin0InfoVect0LinkObjId="g_2471da0_0" Pin0InfoVect1LinkObjId="SW-261157_0" Pin0InfoVect2LinkObjId="SW-261156_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24fed30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5557,-176 5557,-215 5508,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c48cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-241 5508,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42784@0" ObjectIDZND0="g_2471da0@0" ObjectIDZND1="42785@x" ObjectIDZND2="g_24fed30@0" Pin0InfoVect0LinkObjId="g_2471da0_0" Pin0InfoVect1LinkObjId="SW-261157_0" Pin0InfoVect2LinkObjId="g_24fed30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261156_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-241 5508,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c48ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-215 5508,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42784@x" ObjectIDND1="g_24fed30@0" ObjectIDZND0="g_2471da0@0" ObjectIDZND1="42785@x" Pin0InfoVect0LinkObjId="g_2471da0_0" Pin0InfoVect1LinkObjId="SW-261157_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261156_0" Pin1InfoVect1LinkObjId="g_24fed30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-215 5508,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea6c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-108 5508,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2471da0@0" ObjectIDZND0="42786@1" Pin0InfoVect0LinkObjId="SW-261158_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2471da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-108 5508,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22bf8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-158 4859,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2900430@1" ObjectIDZND0="g_27593e0@0" ObjectIDZND1="42769@x" ObjectIDZND2="42770@x" Pin0InfoVect0LinkObjId="g_27593e0_0" Pin0InfoVect1LinkObjId="SW-261060_0" Pin0InfoVect2LinkObjId="SW-261061_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2900430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-158 4859,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22bfab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-192 4811,-192 4811,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_27593e0@0" ObjectIDND1="42769@x" ObjectIDND2="g_2900430@0" ObjectIDZND0="42770@0" Pin0InfoVect0LinkObjId="SW-261061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27593e0_0" Pin1InfoVect1LinkObjId="SW-261060_0" Pin1InfoVect2LinkObjId="g_2900430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-192 4811,-192 4811,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c38420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-160 5069,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2753220@1" ObjectIDZND0="42774@x" ObjectIDZND1="g_3cb19b0@0" ObjectIDZND2="42775@x" Pin0InfoVect0LinkObjId="SW-261092_0" Pin0InfoVect1LinkObjId="g_3cb19b0_0" Pin0InfoVect2LinkObjId="SW-261093_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2753220_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-160 5069,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c38680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-194 5021,-194 5021,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="42774@x" ObjectIDND1="g_3cb19b0@0" ObjectIDND2="g_2753220@0" ObjectIDZND0="42775@0" Pin0InfoVect0LinkObjId="SW-261093_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-261092_0" Pin1InfoVect1LinkObjId="g_3cb19b0_0" Pin1InfoVect2LinkObjId="g_2753220_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-194 5021,-194 5021,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c39150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-158 5298,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_321f090@1" ObjectIDZND0="42779@x" ObjectIDZND1="g_226dc00@0" ObjectIDZND2="42780@x" Pin0InfoVect0LinkObjId="SW-261124_0" Pin0InfoVect1LinkObjId="g_226dc00_0" Pin0InfoVect2LinkObjId="SW-261125_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_321f090_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-158 5298,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c393b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-192 5250,-192 5250,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="42779@x" ObjectIDND1="g_226dc00@0" ObjectIDND2="g_321f090@0" ObjectIDZND0="42780@0" Pin0InfoVect0LinkObjId="SW-261125_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-261124_0" Pin1InfoVect1LinkObjId="g_226dc00_0" Pin1InfoVect2LinkObjId="g_321f090_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-192 5250,-192 5250,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2901060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-160 5508,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2471da0@1" ObjectIDZND0="42784@x" ObjectIDZND1="g_24fed30@0" ObjectIDZND2="42785@x" Pin0InfoVect0LinkObjId="SW-261156_0" Pin0InfoVect1LinkObjId="g_24fed30_0" Pin0InfoVect2LinkObjId="SW-261157_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2471da0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-160 5508,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29012c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-194 5460,-194 5460,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="42784@x" ObjectIDND1="g_24fed30@0" ObjectIDND2="g_2471da0@0" ObjectIDZND0="42785@0" Pin0InfoVect0LinkObjId="SW-261157_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-261156_0" Pin1InfoVect1LinkObjId="g_24fed30_0" Pin1InfoVect2LinkObjId="g_2471da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-194 5460,-194 5460,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb2260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-15 4860,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1bb16a0@0" ObjectIDZND0="42771@x" ObjectIDZND1="42835@x" Pin0InfoVect0LinkObjId="SW-261062_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_066Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bb16a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-15 4860,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ccea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-46 4859,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="42771@0" ObjectIDZND0="g_1bb16a0@0" ObjectIDZND1="42835@x" Pin0InfoVect0LinkObjId="g_1bb16a0_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_066Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261062_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-46 4859,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cd100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-15 4859,53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1bb16a0@0" ObjectIDND1="42771@x" ObjectIDZND0="42835@0" Pin0InfoVect0LinkObjId="EC-WD_DP.WD_DP_066Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bb16a0_0" Pin1InfoVect1LinkObjId="SW-261062_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-15 4859,53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cdee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5100,-15 5070,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_32cd360@0" ObjectIDZND0="42776@x" ObjectIDZND1="42836@x" Pin0InfoVect0LinkObjId="SW-261094_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_067Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32cd360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5100,-15 5070,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275def0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-48 5069,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="42776@0" ObjectIDZND0="g_32cd360@0" ObjectIDZND1="42836@x" Pin0InfoVect0LinkObjId="g_32cd360_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_067Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261094_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-48 5069,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275e150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-15 5069,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_32cd360@0" ObjectIDND1="42776@x" ObjectIDZND0="42836@0" Pin0InfoVect0LinkObjId="EC-WD_DP.WD_DP_067Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32cd360_0" Pin1InfoVect1LinkObjId="SW-261094_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-15 5069,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5328,-12 5298,-12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_32ce7b0@0" ObjectIDZND0="42781@x" ObjectIDZND1="42837@x" Pin0InfoVect0LinkObjId="SW-261126_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_068Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ce7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5328,-12 5298,-12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2946150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-46 5298,-12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="42781@0" ObjectIDZND0="g_32ce7b0@0" ObjectIDZND1="42837@x" Pin0InfoVect0LinkObjId="g_32ce7b0_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_068Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261126_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-46 5298,-12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29463b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-12 5298,49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_32ce7b0@0" ObjectIDND1="42781@x" ObjectIDZND0="42837@0" Pin0InfoVect0LinkObjId="EC-WD_DP.WD_DP_068Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32ce7b0_0" Pin1InfoVect1LinkObjId="SW-261126_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-12 5298,49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2947190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-15 5508,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_275f5e0@0" ObjectIDZND0="42786@x" ObjectIDZND1="42838@x" Pin0InfoVect0LinkObjId="SW-261158_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_069Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_275f5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-15 5508,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-48 5508,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="42786@0" ObjectIDZND0="g_275f5e0@0" ObjectIDZND1="42838@x" Pin0InfoVect0LinkObjId="g_275f5e0_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_069Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-48 5508,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242edb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-15 5508,42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_275f5e0@0" ObjectIDND1="42786@x" ObjectIDZND0="42838@0" Pin0InfoVect0LinkObjId="EC-WD_DP.WD_DP_069Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_275f5e0_0" Pin1InfoVect1LinkObjId="SW-261158_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-15 5508,42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36ca2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-99 3482,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3c3e730@0" ObjectIDZND0="42756@1" Pin0InfoVect0LinkObjId="SW-260966_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3e730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3482,-99 3482,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36cb790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3512,-5 3482,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_36caab0@0" ObjectIDZND0="42756@x" ObjectIDZND1="42832@x" Pin0InfoVect0LinkObjId="SW-260966_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36caab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3512,-5 3482,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f10d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-43 3482,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="42756@0" ObjectIDZND0="g_36caab0@0" ObjectIDZND1="42832@x" Pin0InfoVect0LinkObjId="g_36caab0_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260966_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3482,-43 3482,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f10f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-5 3482,60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_36caab0@0" ObjectIDND1="42756@x" ObjectIDZND0="42832@0" Pin0InfoVect0LinkObjId="EC-WD_DP.WD_DP_061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36caab0_0" Pin1InfoVect1LinkObjId="SW-260966_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3482,-5 3482,60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bbf6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-293 3710,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42758@1" ObjectIDZND0="42757@1" Pin0InfoVect0LinkObjId="SW-260995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-293 3710,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bbf930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-257 3710,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42757@0" ObjectIDZND0="42759@1" Pin0InfoVect0LinkObjId="SW-260996_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-257 3710,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2b530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-311 3710,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42758@0" ObjectIDZND0="42812@0" Pin0InfoVect0LinkObjId="g_32182b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-311 3710,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2c400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-166 3668,-183 3710,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42760@0" ObjectIDZND0="g_333b320@0" ObjectIDZND1="42759@x" ObjectIDZND2="g_1beb4b0@0" Pin0InfoVect0LinkObjId="g_333b320_0" Pin0InfoVect1LinkObjId="SW-260996_0" Pin0InfoVect2LinkObjId="g_1beb4b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-166 3668,-183 3710,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2c660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-183 3710,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42760@x" ObjectIDND1="42759@x" ObjectIDND2="g_1beb4b0@0" ObjectIDZND0="g_333b320@1" Pin0InfoVect0LinkObjId="g_333b320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-260997_0" Pin1InfoVect1LinkObjId="SW-260996_0" Pin1InfoVect2LinkObjId="g_1beb4b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-183 3710,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2c8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-165 3751,-203 3710,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1beb4b0@0" ObjectIDZND0="42759@x" ObjectIDZND1="g_333b320@0" ObjectIDZND2="42760@x" Pin0InfoVect0LinkObjId="SW-260996_0" Pin0InfoVect1LinkObjId="g_333b320_0" Pin0InfoVect2LinkObjId="SW-260997_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1beb4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-165 3751,-203 3710,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2cb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-234 3710,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42759@0" ObjectIDZND0="g_1beb4b0@0" ObjectIDZND1="g_333b320@0" ObjectIDZND2="42760@x" Pin0InfoVect0LinkObjId="g_1beb4b0_0" Pin0InfoVect1LinkObjId="g_333b320_0" Pin0InfoVect2LinkObjId="SW-260997_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-234 3710,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c2cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-203 3710,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42759@x" ObjectIDND1="g_1beb4b0@0" ObjectIDZND0="g_333b320@0" ObjectIDZND1="42760@x" Pin0InfoVect0LinkObjId="g_333b320_0" Pin0InfoVect1LinkObjId="SW-260997_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260996_0" Pin1InfoVect1LinkObjId="g_1beb4b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-203 3710,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c28d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-101 3710,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_333b320@0" ObjectIDZND0="42761@1" Pin0InfoVect0LinkObjId="SW-260998_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_333b320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-101 3710,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c3860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-7 3710,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_36c2b30@0" ObjectIDZND0="42761@x" ObjectIDZND1="42833@x" Pin0InfoVect0LinkObjId="SW-260998_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36c2b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-7 3710,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c3ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-45 3710,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="42761@0" ObjectIDZND0="g_36c2b30@0" ObjectIDZND1="42833@x" Pin0InfoVect0LinkObjId="g_36c2b30_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-45 3710,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36c3d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-7 3710,58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="42761@x" ObjectIDND1="g_36c2b30@0" ObjectIDZND0="42833@0" Pin0InfoVect0LinkObjId="EC-WD_DP.WD_DP_062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260998_0" Pin1InfoVect1LinkObjId="g_36c2b30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-7 3710,58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e8830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-291 3940,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42763@1" ObjectIDZND0="42762@1" Pin0InfoVect0LinkObjId="SW-261027_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261028_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-291 3940,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e8a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-255 3940,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42762@0" ObjectIDZND0="42764@1" Pin0InfoVect0LinkObjId="SW-261028_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261027_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-255 3940,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cab40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-309 3940,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42763@0" ObjectIDZND0="42812@0" Pin0InfoVect0LinkObjId="g_32182b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261028_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-309 3940,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3898,-164 3898,-181 3940,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42765@0" ObjectIDZND0="g_32ca120@0" ObjectIDZND1="42764@x" ObjectIDZND2="g_33509d0@0" Pin0InfoVect0LinkObjId="g_32ca120_0" Pin0InfoVect1LinkObjId="SW-261028_0" Pin0InfoVect2LinkObjId="g_33509d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261029_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3898,-164 3898,-181 3940,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333f250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-181 3940,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42765@x" ObjectIDND1="42764@x" ObjectIDND2="g_33509d0@0" ObjectIDZND0="g_32ca120@1" Pin0InfoVect0LinkObjId="g_32ca120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-261029_0" Pin1InfoVect1LinkObjId="SW-261028_0" Pin1InfoVect2LinkObjId="g_33509d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-181 3940,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333f4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-163 3981,-201 3940,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_33509d0@0" ObjectIDZND0="42764@x" ObjectIDZND1="g_32ca120@0" ObjectIDZND2="42765@x" Pin0InfoVect0LinkObjId="SW-261028_0" Pin0InfoVect1LinkObjId="g_32ca120_0" Pin0InfoVect2LinkObjId="SW-261029_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33509d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-163 3981,-201 3940,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-232 3940,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="42764@0" ObjectIDZND0="g_33509d0@0" ObjectIDZND1="g_32ca120@0" ObjectIDZND2="42765@x" Pin0InfoVect0LinkObjId="g_33509d0_0" Pin0InfoVect1LinkObjId="g_32ca120_0" Pin0InfoVect2LinkObjId="SW-261029_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261028_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-232 3940,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333f970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-201 3940,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42764@x" ObjectIDND1="g_33509d0@0" ObjectIDZND0="g_32ca120@0" ObjectIDZND1="42765@x" Pin0InfoVect0LinkObjId="g_32ca120_0" Pin0InfoVect1LinkObjId="SW-261029_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261028_0" Pin1InfoVect1LinkObjId="g_33509d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-201 3940,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33423d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-99 3940,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_32ca120@0" ObjectIDZND0="42766@1" Pin0InfoVect0LinkObjId="SW-261030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ca120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-99 3940,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2632280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3970,-5 3940,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3342630@0" ObjectIDZND0="42766@x" ObjectIDZND1="42834@x" Pin0InfoVect0LinkObjId="SW-261030_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3342630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3970,-5 3940,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26324e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-43 3940,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="42766@0" ObjectIDZND0="g_3342630@0" ObjectIDZND1="42834@x" Pin0InfoVect0LinkObjId="g_3342630_0" Pin0InfoVect1LinkObjId="EC-WD_DP.WD_DP_063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-43 3940,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2632740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-5 3940,60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="42766@x" ObjectIDND1="g_3342630@0" ObjectIDZND0="42834@0" Pin0InfoVect0LinkObjId="EC-WD_DP.WD_DP_063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261030_0" Pin1InfoVect1LinkObjId="g_3342630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-5 3940,60 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4614.000000 -126.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42797"/>
     <cge:Term_Ref ObjectID="18652"/>
    <cge:TPSR_Ref TObjectID="42797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4614.000000 -126.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42797"/>
     <cge:Term_Ref ObjectID="18652"/>
    <cge:TPSR_Ref TObjectID="42797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4614.000000 -126.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42797"/>
     <cge:Term_Ref ObjectID="18652"/>
    <cge:TPSR_Ref TObjectID="42797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 168.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42792"/>
     <cge:Term_Ref ObjectID="18642"/>
    <cge:TPSR_Ref TObjectID="42792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 168.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42792"/>
     <cge:Term_Ref ObjectID="18642"/>
    <cge:TPSR_Ref TObjectID="42792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 168.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42792"/>
     <cge:Term_Ref ObjectID="18642"/>
    <cge:TPSR_Ref TObjectID="42792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4226.000000 201.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42787"/>
     <cge:Term_Ref ObjectID="18632"/>
    <cge:TPSR_Ref TObjectID="42787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4226.000000 201.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42787"/>
     <cge:Term_Ref ObjectID="18632"/>
    <cge:TPSR_Ref TObjectID="42787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261601" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4226.000000 201.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42787"/>
     <cge:Term_Ref ObjectID="18632"/>
    <cge:TPSR_Ref TObjectID="42787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4054.000000 -456.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42749"/>
     <cge:Term_Ref ObjectID="18556"/>
    <cge:TPSR_Ref TObjectID="42749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4054.000000 -456.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42749"/>
     <cge:Term_Ref ObjectID="18556"/>
    <cge:TPSR_Ref TObjectID="42749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4054.000000 -456.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42749"/>
     <cge:Term_Ref ObjectID="18556"/>
    <cge:TPSR_Ref TObjectID="42749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261535" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -797.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42746"/>
     <cge:Term_Ref ObjectID="18550"/>
    <cge:TPSR_Ref TObjectID="42746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261536" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -797.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42746"/>
     <cge:Term_Ref ObjectID="18550"/>
    <cge:TPSR_Ref TObjectID="42746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -797.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42746"/>
     <cge:Term_Ref ObjectID="18550"/>
    <cge:TPSR_Ref TObjectID="42746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -1296.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42733"/>
     <cge:Term_Ref ObjectID="18524"/>
    <cge:TPSR_Ref TObjectID="42733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261507" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -1296.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42733"/>
     <cge:Term_Ref ObjectID="18524"/>
    <cge:TPSR_Ref TObjectID="42733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -1296.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42733"/>
     <cge:Term_Ref ObjectID="18524"/>
    <cge:TPSR_Ref TObjectID="42733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261493" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4678.000000 -1296.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42727"/>
     <cge:Term_Ref ObjectID="18512"/>
    <cge:TPSR_Ref TObjectID="42727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4678.000000 -1296.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42727"/>
     <cge:Term_Ref ObjectID="18512"/>
    <cge:TPSR_Ref TObjectID="42727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4678.000000 -1296.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42727"/>
     <cge:Term_Ref ObjectID="18512"/>
    <cge:TPSR_Ref TObjectID="42727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-261510" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5229.000000 -939.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261510" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42810"/>
     <cge:Term_Ref ObjectID="18678"/>
    <cge:TPSR_Ref TObjectID="42810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-261511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5229.000000 -939.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42810"/>
     <cge:Term_Ref ObjectID="18678"/>
    <cge:TPSR_Ref TObjectID="42810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-261512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5229.000000 -939.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42810"/>
     <cge:Term_Ref ObjectID="18678"/>
    <cge:TPSR_Ref TObjectID="42810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-261516" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5229.000000 -939.000000) translate(0,80)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42810"/>
     <cge:Term_Ref ObjectID="18678"/>
    <cge:TPSR_Ref TObjectID="42810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-261513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5229.000000 -939.000000) translate(0,101)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42810"/>
     <cge:Term_Ref ObjectID="18678"/>
    <cge:TPSR_Ref TObjectID="42810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-261517" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5229.000000 -939.000000) translate(0,122)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42810"/>
     <cge:Term_Ref ObjectID="18678"/>
    <cge:TPSR_Ref TObjectID="42810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-261630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5952.000000 -518.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42813"/>
     <cge:Term_Ref ObjectID="18681"/>
    <cge:TPSR_Ref TObjectID="42813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-261631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5952.000000 -518.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42813"/>
     <cge:Term_Ref ObjectID="18681"/>
    <cge:TPSR_Ref TObjectID="42813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-261632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5952.000000 -518.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42813"/>
     <cge:Term_Ref ObjectID="18681"/>
    <cge:TPSR_Ref TObjectID="42813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-261636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5952.000000 -518.000000) translate(0,80)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42813"/>
     <cge:Term_Ref ObjectID="18681"/>
    <cge:TPSR_Ref TObjectID="42813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-261633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5952.000000 -518.000000) translate(0,101)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42813"/>
     <cge:Term_Ref ObjectID="18681"/>
    <cge:TPSR_Ref TObjectID="42813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-261637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5952.000000 -518.000000) translate(0,122)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42813"/>
     <cge:Term_Ref ObjectID="18681"/>
    <cge:TPSR_Ref TObjectID="42813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-261622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -516.000000) translate(0,17)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42812"/>
     <cge:Term_Ref ObjectID="18680"/>
    <cge:TPSR_Ref TObjectID="42812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-261623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -516.000000) translate(0,38)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42812"/>
     <cge:Term_Ref ObjectID="18680"/>
    <cge:TPSR_Ref TObjectID="42812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-261624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -516.000000) translate(0,59)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42812"/>
     <cge:Term_Ref ObjectID="18680"/>
    <cge:TPSR_Ref TObjectID="42812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-261628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -516.000000) translate(0,80)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42812"/>
     <cge:Term_Ref ObjectID="18680"/>
    <cge:TPSR_Ref TObjectID="42812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-261625" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -516.000000) translate(0,101)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42812"/>
     <cge:Term_Ref ObjectID="18680"/>
    <cge:TPSR_Ref TObjectID="42812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-261629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -516.000000) translate(0,122)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42812"/>
     <cge:Term_Ref ObjectID="18680"/>
    <cge:TPSR_Ref TObjectID="42812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-261551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -555.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42808"/>
     <cge:Term_Ref ObjectID="18674"/>
    <cge:TPSR_Ref TObjectID="42808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-261550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -555.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42808"/>
     <cge:Term_Ref ObjectID="18674"/>
    <cge:TPSR_Ref TObjectID="42808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3483.000000 145.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42752"/>
     <cge:Term_Ref ObjectID="18562"/>
    <cge:TPSR_Ref TObjectID="42752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3483.000000 145.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42752"/>
     <cge:Term_Ref ObjectID="18562"/>
    <cge:TPSR_Ref TObjectID="42752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3483.000000 145.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42752"/>
     <cge:Term_Ref ObjectID="18562"/>
    <cge:TPSR_Ref TObjectID="42752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261562" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 144.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42757"/>
     <cge:Term_Ref ObjectID="18572"/>
    <cge:TPSR_Ref TObjectID="42757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 144.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42757"/>
     <cge:Term_Ref ObjectID="18572"/>
    <cge:TPSR_Ref TObjectID="42757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 144.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42757"/>
     <cge:Term_Ref ObjectID="18572"/>
    <cge:TPSR_Ref TObjectID="42757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 146.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42762"/>
     <cge:Term_Ref ObjectID="18582"/>
    <cge:TPSR_Ref TObjectID="42762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 146.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42762"/>
     <cge:Term_Ref ObjectID="18582"/>
    <cge:TPSR_Ref TObjectID="42762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 146.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42762"/>
     <cge:Term_Ref ObjectID="18582"/>
    <cge:TPSR_Ref TObjectID="42762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4857.000000 136.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42767"/>
     <cge:Term_Ref ObjectID="18592"/>
    <cge:TPSR_Ref TObjectID="42767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4857.000000 136.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42767"/>
     <cge:Term_Ref ObjectID="18592"/>
    <cge:TPSR_Ref TObjectID="42767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4857.000000 136.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42767"/>
     <cge:Term_Ref ObjectID="18592"/>
    <cge:TPSR_Ref TObjectID="42767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5064.000000 132.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42772"/>
     <cge:Term_Ref ObjectID="18602"/>
    <cge:TPSR_Ref TObjectID="42772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5064.000000 132.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42772"/>
     <cge:Term_Ref ObjectID="18602"/>
    <cge:TPSR_Ref TObjectID="42772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5064.000000 132.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42772"/>
     <cge:Term_Ref ObjectID="18602"/>
    <cge:TPSR_Ref TObjectID="42772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5293.000000 134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42777"/>
     <cge:Term_Ref ObjectID="18612"/>
    <cge:TPSR_Ref TObjectID="42777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261591" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5293.000000 134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42777"/>
     <cge:Term_Ref ObjectID="18612"/>
    <cge:TPSR_Ref TObjectID="42777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5293.000000 134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42777"/>
     <cge:Term_Ref ObjectID="18612"/>
    <cge:TPSR_Ref TObjectID="42777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-261597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5505.000000 133.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42782"/>
     <cge:Term_Ref ObjectID="18622"/>
    <cge:TPSR_Ref TObjectID="42782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-261598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5505.000000 133.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42782"/>
     <cge:Term_Ref ObjectID="18622"/>
    <cge:TPSR_Ref TObjectID="42782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-261594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5505.000000 133.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="261594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42782"/>
     <cge:Term_Ref ObjectID="18622"/>
    <cge:TPSR_Ref TObjectID="42782"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="67" qtmmishow="hidden" width="202" x="2664" y="-1235"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="100" qtmmishow="hidden" width="116" x="2591" y="-1255"/></g>
   <g href="cx_配调_配网接线图35_武定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="2976" y="-1205"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="2976" y="-1240"/></g>
   <g href="35kV东坡变WD_DP_364间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="49" x="4141" y="-953"/></g>
   <g href="35kV东坡变WD_DP_363间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="49" x="4709" y="-950"/></g>
   <g href="35kV东坡变WD_DP_065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4437" y="-306"/></g>
   <g href="35kV东坡变WD_DP_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="40" x="4675" y="-306"/></g>
   <g href="AVC东坡站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="2901" y="-1228"/></g>
   <g href="35kV东坡变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="91" x="2585" y="-776"/></g>
   <g href="35kV东坡变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="83" x="4188" y="-678"/></g>
   <g href="35kV东坡变WD_DP_066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="36" x="4871" y="-287"/></g>
   <g href="35kV东坡变WD_DP_067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="36" x="5082" y="-288"/></g>
   <g href="35kV东坡变WD_DP_068间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="36" x="5308" y="-286"/></g>
   <g href="35kV东坡变WD_DP_069间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="36" x="5521" y="-287"/></g>
   <g href="35kV东坡变WD_DP_064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="36" x="4284" y="-308"/></g>
   <g href="35kV东坡变WD_DP_061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3494" y="-277"/></g>
   <g href="35kV东坡变WD_DP_062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3724" y="-279"/></g>
   <g href="35kV东坡变WD_DP_063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3952" y="-277"/></g>
   <g href="35kV东坡变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="91" x="2585" y="-729"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4419" cy="-79" fill="none" fillStyle="0" r="16" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4419" cy="-55" fill="none" fillStyle="0" r="16" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-260851">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -728.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42739" ObjectName="SW-WD_DP.WD_DP_3901SW"/>
     <cge:Meas_Ref ObjectId="260851"/>
    <cge:TPSR_Ref TObjectID="42739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260796">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -843.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42734" ObjectName="SW-WD_DP.WD_DP_3641SW"/>
     <cge:Meas_Ref ObjectId="260796"/>
    <cge:TPSR_Ref TObjectID="42734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260852">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4647.000000 -747.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42740" ObjectName="SW-WD_DP.WD_DP_39017SW"/>
     <cge:Meas_Ref ObjectId="260852"/>
    <cge:TPSR_Ref TObjectID="42740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260800">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4073.000000 -1015.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42738" ObjectName="SW-WD_DP.WD_DP_36467SW"/>
     <cge:Meas_Ref ObjectId="260800"/>
    <cge:TPSR_Ref TObjectID="42738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260797">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -992.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42735" ObjectName="SW-WD_DP.WD_DP_3646SW"/>
     <cge:Meas_Ref ObjectId="260797"/>
    <cge:TPSR_Ref TObjectID="42735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260799">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4073.000000 -938.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42737" ObjectName="SW-WD_DP.WD_DP_36460SW"/>
     <cge:Meas_Ref ObjectId="260799"/>
    <cge:TPSR_Ref TObjectID="42737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260798">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 -853.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42736" ObjectName="SW-WD_DP.WD_DP_36417SW"/>
     <cge:Meas_Ref ObjectId="260798"/>
    <cge:TPSR_Ref TObjectID="42736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260744">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -1013.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42732" ObjectName="SW-WD_DP.WD_DP_36367SW"/>
     <cge:Meas_Ref ObjectId="260744"/>
    <cge:TPSR_Ref TObjectID="42732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -990.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42729" ObjectName="SW-WD_DP.WD_DP_3636SW"/>
     <cge:Meas_Ref ObjectId="260741"/>
    <cge:TPSR_Ref TObjectID="42729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -936.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42731" ObjectName="SW-WD_DP.WD_DP_36360SW"/>
     <cge:Meas_Ref ObjectId="260743"/>
    <cge:TPSR_Ref TObjectID="42731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260853">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4733.000000 -669.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42741" ObjectName="SW-WD_DP.WD_DP_39010SW"/>
     <cge:Meas_Ref ObjectId="260853"/>
    <cge:TPSR_Ref TObjectID="42741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260740">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -841.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42728" ObjectName="SW-WD_DP.WD_DP_3631SW"/>
     <cge:Meas_Ref ObjectId="260740"/>
    <cge:TPSR_Ref TObjectID="42728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260742">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4640.000000 -861.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42730" ObjectName="SW-WD_DP.WD_DP_36317SW"/>
     <cge:Meas_Ref ObjectId="260742"/>
    <cge:TPSR_Ref TObjectID="42730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5000.000000 -848.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42744" ObjectName="SW-WD_DP.WD_DP_3621SW"/>
     <cge:Meas_Ref ObjectId="260876"/>
    <cge:TPSR_Ref TObjectID="42744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260877">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4958.000000 -868.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42745" ObjectName="SW-WD_DP.WD_DP_36217SW"/>
     <cge:Meas_Ref ObjectId="260877"/>
    <cge:TPSR_Ref TObjectID="42745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260874">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3433.000000 -762.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42742" ObjectName="SW-WD_DP.WD_DP_3121SW"/>
     <cge:Meas_Ref ObjectId="260874"/>
    <cge:TPSR_Ref TObjectID="42742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260875">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3347.500000 -719.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42743" ObjectName="SW-WD_DP.WD_DP_31217SW"/>
     <cge:Meas_Ref ObjectId="260875"/>
    <cge:TPSR_Ref TObjectID="42743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261189">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.373913 -176.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42790" ObjectName="SW-WD_DP.WD_DP_06460SW"/>
     <cge:Meas_Ref ObjectId="261189"/>
    <cge:TPSR_Ref TObjectID="42790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261188">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.373913 -325.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42788" ObjectName="SW-WD_DP.WD_DP_064XC"/>
     <cge:Meas_Ref ObjectId="261188"/>
    <cge:TPSR_Ref TObjectID="42788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261188">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.373913 -240.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42789" ObjectName="SW-WD_DP.WD_DP_064XC1"/>
     <cge:Meas_Ref ObjectId="261188"/>
    <cge:TPSR_Ref TObjectID="42789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261190">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.373913 -56.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42791" ObjectName="SW-WD_DP.WD_DP_0646SW"/>
     <cge:Meas_Ref ObjectId="261190"/>
    <cge:TPSR_Ref TObjectID="42791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261212">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.104348 -174.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42795" ObjectName="SW-WD_DP.WD_DP_06560SW"/>
     <cge:Meas_Ref ObjectId="261212"/>
    <cge:TPSR_Ref TObjectID="42795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261211">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.104348 -323.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42793" ObjectName="SW-WD_DP.WD_DP_065XC"/>
     <cge:Meas_Ref ObjectId="261211"/>
    <cge:TPSR_Ref TObjectID="42793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261211">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.104348 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42794" ObjectName="SW-WD_DP.WD_DP_065XC1"/>
     <cge:Meas_Ref ObjectId="261211"/>
    <cge:TPSR_Ref TObjectID="42794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261242">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.373913 -320.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42798" ObjectName="SW-WD_DP.WD_DP_012XC"/>
     <cge:Meas_Ref ObjectId="261242"/>
    <cge:TPSR_Ref TObjectID="42798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261242">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.373913 -235.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42799" ObjectName="SW-WD_DP.WD_DP_012XC1"/>
     <cge:Meas_Ref ObjectId="261242"/>
    <cge:TPSR_Ref TObjectID="42799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261292">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5719.104348 -184.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42807" ObjectName="SW-WD_DP.WD_DP_05110SW"/>
     <cge:Meas_Ref ObjectId="261292"/>
    <cge:TPSR_Ref TObjectID="42807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261291">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5748.104348 -330.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42805" ObjectName="SW-WD_DP.WD_DP_0511XC"/>
     <cge:Meas_Ref ObjectId="261291"/>
    <cge:TPSR_Ref TObjectID="42805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261291">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5748.104348 -248.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42806" ObjectName="SW-WD_DP.WD_DP_0511XC1"/>
     <cge:Meas_Ref ObjectId="261291"/>
    <cge:TPSR_Ref TObjectID="42806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.104348 52.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260879">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.937500 -0.000000 0.000000 -1.000000 4121.562500 -760.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42747" ObjectName="SW-WD_DP.WD_DP_3011SW"/>
     <cge:Meas_Ref ObjectId="260879"/>
    <cge:TPSR_Ref TObjectID="42747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260880">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.937500 -0.000000 0.000000 1.000000 4092.500000 -782.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42748" ObjectName="SW-WD_DP.WD_DP_30117SW"/>
     <cge:Meas_Ref ObjectId="260880"/>
    <cge:TPSR_Ref TObjectID="42748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260882">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.937500 -0.000000 0.000000 -1.000000 4120.625000 -467.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42751" ObjectName="SW-WD_DP.WD_DP_001XC1"/>
     <cge:Meas_Ref ObjectId="260882"/>
    <cge:TPSR_Ref TObjectID="42751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260882">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.937500 -0.000000 0.000000 -1.000000 4120.625000 -388.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42750" ObjectName="SW-WD_DP.WD_DP_001XC"/>
     <cge:Meas_Ref ObjectId="260882"/>
    <cge:TPSR_Ref TObjectID="42750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261243">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4519.000000 -273.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42800" ObjectName="SW-WD_DP.WD_DP_0121SW"/>
     <cge:Meas_Ref ObjectId="261243"/>
    <cge:TPSR_Ref TObjectID="42800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261213">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.373913 -22.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42796" ObjectName="SW-WD_DP.WD_DP_0030SW"/>
     <cge:Meas_Ref ObjectId="261213"/>
    <cge:TPSR_Ref TObjectID="42796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260964">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3472.126582 -225.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42754" ObjectName="SW-WD_DP.WD_DP_061XC1"/>
     <cge:Meas_Ref ObjectId="260964"/>
    <cge:TPSR_Ref TObjectID="42754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260964">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3472.126582 -285.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42753" ObjectName="SW-WD_DP.WD_DP_061XC"/>
     <cge:Meas_Ref ObjectId="260964"/>
    <cge:TPSR_Ref TObjectID="42753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3431.000000 -113.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42755" ObjectName="SW-WD_DP.WD_DP_06160SW"/>
     <cge:Meas_Ref ObjectId="260965"/>
    <cge:TPSR_Ref TObjectID="42755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.126582 -232.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42769" ObjectName="SW-WD_DP.WD_DP_066XC1"/>
     <cge:Meas_Ref ObjectId="261060"/>
    <cge:TPSR_Ref TObjectID="42769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.126582 -291.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42768" ObjectName="SW-WD_DP.WD_DP_066XC"/>
     <cge:Meas_Ref ObjectId="261060"/>
    <cge:TPSR_Ref TObjectID="42768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261061">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.000000 -123.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42770" ObjectName="SW-WD_DP.WD_DP_06660SW"/>
     <cge:Meas_Ref ObjectId="261061"/>
    <cge:TPSR_Ref TObjectID="42770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261279">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.126582 -401.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42801" ObjectName="SW-WD_DP.WD_DP_0901XC"/>
     <cge:Meas_Ref ObjectId="261279"/>
    <cge:TPSR_Ref TObjectID="42801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5326.126582 -401.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42803" ObjectName="SW-WD_DP.WD_DP_0902XC"/>
     <cge:Meas_Ref ObjectId="261281"/>
    <cge:TPSR_Ref TObjectID="42803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261191">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4159.626087 -129.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42815" ObjectName="SW-WD_DP.WD_DP_06467SW"/>
     <cge:Meas_Ref ObjectId="261191"/>
    <cge:TPSR_Ref TObjectID="42815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261062">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.373913 -41.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42771" ObjectName="SW-WD_DP.WD_DP_0666SW"/>
     <cge:Meas_Ref ObjectId="261062"/>
    <cge:TPSR_Ref TObjectID="42771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261092">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5059.126582 -234.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42774" ObjectName="SW-WD_DP.WD_DP_067XC1"/>
     <cge:Meas_Ref ObjectId="261092"/>
    <cge:TPSR_Ref TObjectID="42774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261092">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5059.126582 -293.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42773" ObjectName="SW-WD_DP.WD_DP_067XC"/>
     <cge:Meas_Ref ObjectId="261092"/>
    <cge:TPSR_Ref TObjectID="42773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261093">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5012.000000 -125.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42775" ObjectName="SW-WD_DP.WD_DP_06760SW"/>
     <cge:Meas_Ref ObjectId="261093"/>
    <cge:TPSR_Ref TObjectID="42775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261094">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.373913 -43.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42776" ObjectName="SW-WD_DP.WD_DP_0676SW"/>
     <cge:Meas_Ref ObjectId="261094"/>
    <cge:TPSR_Ref TObjectID="42776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5288.126582 -232.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42779" ObjectName="SW-WD_DP.WD_DP_068XC1"/>
     <cge:Meas_Ref ObjectId="261124"/>
    <cge:TPSR_Ref TObjectID="42779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5288.126582 -291.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42778" ObjectName="SW-WD_DP.WD_DP_068XC"/>
     <cge:Meas_Ref ObjectId="261124"/>
    <cge:TPSR_Ref TObjectID="42778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261125">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5241.000000 -123.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42780" ObjectName="SW-WD_DP.WD_DP_06860SW"/>
     <cge:Meas_Ref ObjectId="261125"/>
    <cge:TPSR_Ref TObjectID="42780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261126">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5289.373913 -41.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42781" ObjectName="SW-WD_DP.WD_DP_0686SW"/>
     <cge:Meas_Ref ObjectId="261126"/>
    <cge:TPSR_Ref TObjectID="42781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261156">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5498.126582 -234.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42784" ObjectName="SW-WD_DP.WD_DP_069XC1"/>
     <cge:Meas_Ref ObjectId="261156"/>
    <cge:TPSR_Ref TObjectID="42784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261156">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5498.126582 -293.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42783" ObjectName="SW-WD_DP.WD_DP_069XC"/>
     <cge:Meas_Ref ObjectId="261156"/>
    <cge:TPSR_Ref TObjectID="42783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261157">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5451.000000 -125.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42785" ObjectName="SW-WD_DP.WD_DP_06960SW"/>
     <cge:Meas_Ref ObjectId="261157"/>
    <cge:TPSR_Ref TObjectID="42785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5499.373913 -43.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42786" ObjectName="SW-WD_DP.WD_DP_0696SW"/>
     <cge:Meas_Ref ObjectId="261158"/>
    <cge:TPSR_Ref TObjectID="42786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260966">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3473.373913 -38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42756" ObjectName="SW-WD_DP.WD_DP_0616SW"/>
     <cge:Meas_Ref ObjectId="260966"/>
    <cge:TPSR_Ref TObjectID="42756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3700.126582 -227.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42759" ObjectName="SW-WD_DP.WD_DP_062XC1"/>
     <cge:Meas_Ref ObjectId="260996"/>
    <cge:TPSR_Ref TObjectID="42759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3700.126582 -287.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42758" ObjectName="SW-WD_DP.WD_DP_062XC"/>
     <cge:Meas_Ref ObjectId="260996"/>
    <cge:TPSR_Ref TObjectID="42758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -115.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42760" ObjectName="SW-WD_DP.WD_DP_06260SW"/>
     <cge:Meas_Ref ObjectId="260997"/>
    <cge:TPSR_Ref TObjectID="42760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260998">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.373913 -40.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42761" ObjectName="SW-WD_DP.WD_DP_0626SW"/>
     <cge:Meas_Ref ObjectId="260998"/>
    <cge:TPSR_Ref TObjectID="42761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261028">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.126582 -225.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42764" ObjectName="SW-WD_DP.WD_DP_063XC1"/>
     <cge:Meas_Ref ObjectId="261028"/>
    <cge:TPSR_Ref TObjectID="42764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261028">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.126582 -285.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42763" ObjectName="SW-WD_DP.WD_DP_063XC"/>
     <cge:Meas_Ref ObjectId="261028"/>
    <cge:TPSR_Ref TObjectID="42763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261029">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 -113.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42765" ObjectName="SW-WD_DP.WD_DP_06360SW"/>
     <cge:Meas_Ref ObjectId="261029"/>
    <cge:TPSR_Ref TObjectID="42765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261030">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.373913 -38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42766" ObjectName="SW-WD_DP.WD_DP_0636SW"/>
     <cge:Meas_Ref ObjectId="261030"/>
    <cge:TPSR_Ref TObjectID="42766"/></metadata>
   </g>
  </g><g id="NonConstantLoad_Layer">
   <g DF8003:Layer="PUBLIC">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5749.104348 110.000000)" xlink:href="#nonConstantLoad:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="67" qtmmishow="hidden" width="202" x="2664" y="-1235"/>
    </a>
   <metadata/><rect fill="white" height="67" opacity="0" stroke="white" transform="" width="202" x="2664" y="-1235"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="100" qtmmishow="hidden" width="116" x="2591" y="-1255"/>
    </a>
   <metadata/><rect fill="white" height="100" opacity="0" stroke="white" transform="" width="116" x="2591" y="-1255"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="2976" y="-1205"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="2976" y="-1205"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="2976" y="-1240"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="2976" y="-1240"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="49" x="4141" y="-953"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="49" x="4141" y="-953"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="49" x="4709" y="-950"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="49" x="4709" y="-950"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4437" y="-306"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4437" y="-306"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="40" x="4675" y="-306"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="40" x="4675" y="-306"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="2901,-1228 2898,-1231 2898,-1178 2901,-1181 2901,-1228" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="2901,-1228 2898,-1231 2949,-1231 2946,-1228 2901,-1228" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="2901,-1181 2898,-1178 2949,-1178 2946,-1181 2901,-1181" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="2946,-1228 2949,-1231 2949,-1178 2946,-1181 2946,-1228" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="2901" y="-1228"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="2901" y="-1228"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="91" x="2585" y="-776"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="91" x="2585" y="-776"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="83" x="4188" y="-678"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="83" x="4188" y="-678"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="36" x="4871" y="-287"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="36" x="4871" y="-287"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="36" x="5082" y="-288"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="36" x="5082" y="-288"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="36" x="5308" y="-286"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="36" x="5308" y="-286"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="36" x="5521" y="-287"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="36" x="5521" y="-287"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="36" x="4284" y="-308"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="36" x="4284" y="-308"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3494" y="-277"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3494" y="-277"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3724" y="-279"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3724" y="-279"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3952" y="-277"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3952" y="-277"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="91" x="2585" y="-729"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="91" x="2585" y="-729"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4251,101 4251,106 4284,106 4284,101 " stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="ConnectPoint_Layer"/><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-WD_DP.WD_DP_1Cb">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4241.000000 84.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42814" ObjectName="CB-WD_DP.WD_DP_1Cb"/>
    <cge:TPSR_Ref TObjectID="42814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.483871 -0.000000 0.000000 -0.541667 4384.000000 -568.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.483871 -0.000000 0.000000 -0.541667 4729.000000 -607.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.483871 -0.000000 0.000000 -0.541667 5381.000000 -569.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_DP"/>
</svg>