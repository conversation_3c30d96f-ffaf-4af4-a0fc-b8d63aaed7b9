<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" LineID="楚雄地区_500kV龙昆甲鲁昆甲龙鲁线.svg" MapType="line" StationID="SS-118" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3517 -1766 2255 1122">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape44_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="29" x2="32" y1="19" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape44_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="43" x2="43" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="10" x2="10" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="6" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="3" x2="3" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="30" x2="30" y1="13" y2="6"/>
   </symbol>
   <symbol id="switch2:shape44-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="43" x2="43" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="10" x2="10" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="6" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="3" x2="3" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="30" x2="30" y1="13" y2="6"/>
   </symbol>
   <symbol id="switch2:shape44-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="43" x2="43" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="10" x2="10" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="6" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="3" x2="3" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="30" x2="30" y1="13" y2="6"/>
   </symbol>
   <symbol id="switch2:shape43_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="27" x2="24" y1="19" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
   </symbol>
   <symbol id="switch2:shape43_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="27" x2="27" y1="13" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape43-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="27" x2="27" y1="13" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape43-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="27" x2="27" y1="13" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3407990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34082d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3408c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3409770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_340a790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_340b430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_340bc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_340c5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_340cec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_340d8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_340e390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_340e9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3410030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3410c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34115f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3411f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34136f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34143f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3414cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34156a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3416880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3417200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3417cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ec9920" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_341a8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_341b250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_342ac40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_341c5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_341e3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3419d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1132" width="2265" x="3512" y="-1771"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-239232">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.860625 -1318.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39984" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_5411BK"/>
     <cge:Meas_Ref ObjectId="239232"/>
    <cge:TPSR_Ref TObjectID="39984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239235">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.860625 -1122.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39987" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_5412BK"/>
     <cge:Meas_Ref ObjectId="239235"/>
    <cge:TPSR_Ref TObjectID="39987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239238">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.860625 -931.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39990" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_5413BK"/>
     <cge:Meas_Ref ObjectId="239238"/>
    <cge:TPSR_Ref TObjectID="39990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239243">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.314402 -1317.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39995" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5421BK"/>
     <cge:Meas_Ref ObjectId="239243"/>
    <cge:TPSR_Ref TObjectID="39995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239248">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.314402 -1121.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40000" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5422BK"/>
     <cge:Meas_Ref ObjectId="239248"/>
    <cge:TPSR_Ref TObjectID="40000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239251">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.314402 -930.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40003" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5423BK"/>
     <cge:Meas_Ref ObjectId="239251"/>
    <cge:TPSR_Ref TObjectID="40003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239254">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.388835 -1315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40006" ObjectName="SW-CX_TASE2JS.YN_LL_5431BK"/>
     <cge:Meas_Ref ObjectId="239254"/>
    <cge:TPSR_Ref TObjectID="40006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239259">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.388835 -1120.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40011" ObjectName="SW-CX_TASE2JS.YN_LL_5432BK"/>
     <cge:Meas_Ref ObjectId="239259"/>
    <cge:TPSR_Ref TObjectID="40011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239262">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.388835 -928.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40014" ObjectName="SW-CX_TASE2JS.YN_LL_5433BK"/>
     <cge:Meas_Ref ObjectId="239262"/>
    <cge:TPSR_Ref TObjectID="40014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239222">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.041961 -1317.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39962" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_5071BK"/>
     <cge:Meas_Ref ObjectId="239222"/>
    <cge:TPSR_Ref TObjectID="39962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239226">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.041961 -1121.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39776" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_5072BK"/>
     <cge:Meas_Ref ObjectId="239226"/>
    <cge:TPSR_Ref TObjectID="39776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239229">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.041961 -930.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39778" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_5073BK"/>
     <cge:Meas_Ref ObjectId="239229"/>
    <cge:TPSR_Ref TObjectID="39778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238033">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.066594 -1318.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39771" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5051BK"/>
     <cge:Meas_Ref ObjectId="238033"/>
    <cge:TPSR_Ref TObjectID="39771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239206">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.066594 -1122.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39966" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5052BK"/>
     <cge:Meas_Ref ObjectId="239206"/>
    <cge:TPSR_Ref TObjectID="39966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239209">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.066594 -931.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39969" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5053BK"/>
     <cge:Meas_Ref ObjectId="239209"/>
    <cge:TPSR_Ref TObjectID="39969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239265">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5332.883140 -1319.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40017" ObjectName="SW-CX_TASE2JS.YN_LL_5321BK"/>
     <cge:Meas_Ref ObjectId="239265"/>
    <cge:TPSR_Ref TObjectID="40017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239271">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5332.883140 -1123.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40022" ObjectName="SW-CX_TASE2JS.YN_LL_5322BK"/>
     <cge:Meas_Ref ObjectId="239271"/>
    <cge:TPSR_Ref TObjectID="40022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239274">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5332.883140 -931.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40025" ObjectName="SW-CX_TASE2JS.YN_LL_5323BK"/>
     <cge:Meas_Ref ObjectId="239274"/>
    <cge:TPSR_Ref TObjectID="40025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239277">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -1317.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40037" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5331BK"/>
     <cge:Meas_Ref ObjectId="239277"/>
    <cge:TPSR_Ref TObjectID="40037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239280">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -1121.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40040" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5332BK"/>
     <cge:Meas_Ref ObjectId="239280"/>
    <cge:TPSR_Ref TObjectID="40040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239283">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -930.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40043" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5333BK"/>
     <cge:Meas_Ref ObjectId="239283"/>
    <cge:TPSR_Ref TObjectID="40043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239212">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.389365 -1313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39972" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5061BK"/>
     <cge:Meas_Ref ObjectId="239212"/>
    <cge:TPSR_Ref TObjectID="39972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239215">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.389365 -1117.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39975" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5062BK"/>
     <cge:Meas_Ref ObjectId="239215"/>
    <cge:TPSR_Ref TObjectID="39975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239218">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.389365 -926.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39978" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5063BK"/>
     <cge:Meas_Ref ObjectId="239218"/>
    <cge:TPSR_Ref TObjectID="39978"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_LDL_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4456,-1438 5049,-1438 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40052" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_LDL_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="40052"/></metadata>
   <polyline fill="none" opacity="0" points="4456,-1438 5049,-1438 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_LDL_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4470,-834 5051,-834 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40053" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_LDL_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="40053"/></metadata>
   <polyline fill="none" opacity="0" points="4470,-834 5051,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_KH_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3663,-1440 4243,-1440 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40050" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_KH_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="40050"/></metadata>
   <polyline fill="none" opacity="0" points="3663,-1440 4243,-1440 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_KH_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-832 4245,-832 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40051" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_KH_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="40051"/></metadata>
   <polyline fill="none" opacity="0" points="3665,-832 4245,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_LKK_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5285,-1441 5659,-1441 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40054" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_LKK_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="40054"/></metadata>
   <polyline fill="none" opacity="0" points="5285,-1441 5659,-1441 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_LKK_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5308,-839 5635,-839 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40055" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_LKK_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="40055"/></metadata>
   <polyline fill="none" opacity="0" points="5308,-839 5635,-839 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-500KV" id="g_2fdb890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1438 4506,-1415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40052@0" ObjectIDZND0="39985@1" Pin0InfoVect0LinkObjId="SW-239233_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1438 4506,-1415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2fe9ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-869 4506,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39992@0" ObjectIDZND0="40053@0" Pin0InfoVect0LinkObjId="g_2f526b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-869 4506,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb1860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1438 4760,-1414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40052@0" ObjectIDZND0="39996@1" Pin0InfoVect0LinkObjId="SW-239244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1438 4760,-1414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f526b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-868 4760,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40005@0" ObjectIDZND0="40053@0" Pin0InfoVect0LinkObjId="g_2fe9ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239253_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-868 4760,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2fd8580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1059 4760,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40002@0" ObjectIDZND0="40004@1" Pin0InfoVect0LinkObjId="SW-239252_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1059 4760,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f16b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-1438 4981,-1412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40052@0" ObjectIDZND0="40008@1" Pin0InfoVect0LinkObjId="SW-239255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-1438 4981,-1412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3006cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-866 4981,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40016@0" ObjectIDZND0="40053@0" Pin0InfoVect0LinkObjId="g_2fe9ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-866 4981,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2a04c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-1440 4161,-1414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40050@0" ObjectIDZND0="39774@1" Pin0InfoVect0LinkObjId="SW-239223_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-1440 4161,-1414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f22210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-868 4161,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39779@0" ObjectIDZND0="40051@0" Pin0InfoVect0LinkObjId="g_2eda090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239231_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-868 4161,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f22470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-1378 4161,-1352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39774@0" ObjectIDZND0="39962@1" Pin0InfoVect0LinkObjId="SW-239222_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239223_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-1378 4161,-1352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f226d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-1325 4161,-1291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39962@0" ObjectIDZND0="39775@1" Pin0InfoVect0LinkObjId="SW-239224_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239222_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-1325 4161,-1291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f12fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-1182 4161,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39982@0" ObjectIDZND0="39776@1" Pin0InfoVect0LinkObjId="SW-239226_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239227_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-1182 4161,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f13220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-1129 4161,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39776@0" ObjectIDZND0="39777@1" Pin0InfoVect0LinkObjId="SW-239228_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239226_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-1129 4161,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f13480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-965 4161,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39778@1" ObjectIDZND0="39983@0" Pin0InfoVect0LinkObjId="SW-239230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239229_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-965 4161,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eee250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-904 4161,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39779@1" ObjectIDZND0="39778@0" Pin0InfoVect0LinkObjId="SW-239229_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239231_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-904 4161,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2e8be30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-1440 3736,-1415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40050@0" ObjectIDZND0="39963@1" Pin0InfoVect0LinkObjId="SW-239203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-1440 3736,-1415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eda090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-869 3736,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39971@0" ObjectIDZND0="40051@0" Pin0InfoVect0LinkObjId="g_2f22210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239211_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-869 3736,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eda2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-1379 3736,-1353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39963@0" ObjectIDZND0="39771@1" Pin0InfoVect0LinkObjId="SW-238033_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-1379 3736,-1353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eda550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-1326 3736,-1292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39771@0" ObjectIDZND0="39964@1" Pin0InfoVect0LinkObjId="SW-239204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238033_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-1326 3736,-1292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eda7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-1183 3736,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39967@0" ObjectIDZND0="39966@1" Pin0InfoVect0LinkObjId="SW-239206_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-1183 3736,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2edaa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-1130 3736,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39966@0" ObjectIDZND0="39968@1" Pin0InfoVect0LinkObjId="SW-239208_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-1130 3736,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2edac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-966 3736,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39969@1" ObjectIDZND0="39970@0" Pin0InfoVect0LinkObjId="SW-239210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-966 3736,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2edaed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-905 3736,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39971@1" ObjectIDZND0="39969@0" Pin0InfoVect0LinkObjId="SW-239209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239211_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-905 3736,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3043ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1326 4506,-1292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39984@0" ObjectIDZND0="39986@1" Pin0InfoVect0LinkObjId="SW-239234_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1326 4506,-1292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_30441e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1353 4506,-1379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39984@1" ObjectIDZND0="39985@0" Pin0InfoVect0LinkObjId="SW-239233_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1353 4506,-1379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_30443d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1157 4506,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39987@1" ObjectIDZND0="39988@0" Pin0InfoVect0LinkObjId="SW-239236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1157 4506,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_30445c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1130 4506,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39987@0" ObjectIDZND0="39989@1" Pin0InfoVect0LinkObjId="SW-239237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1130 4506,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_30447f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-966 4506,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39990@1" ObjectIDZND0="39991@0" Pin0InfoVect0LinkObjId="SW-239239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239238_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-966 4506,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3044a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-939 4506,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39990@0" ObjectIDZND0="39992@1" Pin0InfoVect0LinkObjId="SW-239240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239238_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-939 4506,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3044c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1378 4760,-1352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39996@0" ObjectIDZND0="39995@1" Pin0InfoVect0LinkObjId="SW-239243_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1378 4760,-1352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3044e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1325 4760,-1291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39995@0" ObjectIDZND0="39997@1" Pin0InfoVect0LinkObjId="SW-239245_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239243_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1325 4760,-1291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f40480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1182 4760,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40001@0" ObjectIDZND0="40000@1" Pin0InfoVect0LinkObjId="SW-239248_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239249_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1182 4760,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f406b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1129 4760,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40000@0" ObjectIDZND0="40002@1" Pin0InfoVect0LinkObjId="SW-239250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239248_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1129 4760,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f40910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-965 4760,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40003@1" ObjectIDZND0="40004@0" Pin0InfoVect0LinkObjId="SW-239252_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239251_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-965 4760,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f40b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-904 4760,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40005@1" ObjectIDZND0="40003@0" Pin0InfoVect0LinkObjId="SW-239251_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239253_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-904 4760,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f40dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-1376 4981,-1350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40008@0" ObjectIDZND0="40006@1" Pin0InfoVect0LinkObjId="SW-239254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-1376 4981,-1350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f41030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-1181 4981,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40012@0" ObjectIDZND0="40011@1" Pin0InfoVect0LinkObjId="SW-239259_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-1181 4981,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f41290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-1128 4981,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40011@0" ObjectIDZND0="40013@1" Pin0InfoVect0LinkObjId="SW-239261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239259_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-1128 4981,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f414f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-963 4981,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40014@1" ObjectIDZND0="40015@0" Pin0InfoVect0LinkObjId="SW-239263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-963 4981,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f41750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-902 4981,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40016@1" ObjectIDZND0="40014@0" Pin0InfoVect0LinkObjId="SW-239262_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-902 4981,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_39b2cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-1441 5342,-1416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40054@0" ObjectIDZND0="40018@1" Pin0InfoVect0LinkObjId="SW-239266_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-1441 5342,-1416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f0c2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-869 5342,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40027@0" ObjectIDZND0="40055@0" Pin0InfoVect0LinkObjId="g_326dc90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239276_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-869 5342,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f0c520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-1061 5342,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40024@0" ObjectIDZND0="40026@1" Pin0InfoVect0LinkObjId="SW-239275_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239273_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-1061 5342,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2f0c780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-1441 5556,-1414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40054@0" ObjectIDZND0="40038@1" Pin0InfoVect0LinkObjId="SW-239278_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-1441 5556,-1414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2ec3050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-1255 5556,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40039@0" ObjectIDZND0="40041@1" Pin0InfoVect0LinkObjId="SW-239281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-1255 5556,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_326dc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-868 5556,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40045@0" ObjectIDZND0="40055@0" Pin0InfoVect0LinkObjId="g_2f0c2c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239285_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-868 5556,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3270550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-1380 5342,-1354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40018@0" ObjectIDZND0="40017@1" Pin0InfoVect0LinkObjId="SW-239265_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239266_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-1380 5342,-1354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_32707b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-1327 5342,-1293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40017@0" ObjectIDZND0="40019@1" Pin0InfoVect0LinkObjId="SW-239267_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239265_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-1327 5342,-1293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3270a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-1184 5342,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40023@0" ObjectIDZND0="40022@1" Pin0InfoVect0LinkObjId="SW-239271_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239272_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-1184 5342,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3270c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-1131 5342,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40022@0" ObjectIDZND0="40024@1" Pin0InfoVect0LinkObjId="SW-239273_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-1131 5342,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3270ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-966 5342,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40025@1" ObjectIDZND0="40026@0" Pin0InfoVect0LinkObjId="SW-239275_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239274_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-966 5342,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3271130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-905 5342,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40027@1" ObjectIDZND0="40025@0" Pin0InfoVect0LinkObjId="SW-239274_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239276_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-905 5342,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3271390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-1325 5556,-1291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40037@0" ObjectIDZND0="40039@1" Pin0InfoVect0LinkObjId="SW-239279_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-1325 5556,-1291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_32715f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-1378 5556,-1352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40038@0" ObjectIDZND0="40037@1" Pin0InfoVect0LinkObjId="SW-239277_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239278_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-1378 5556,-1352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3271850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-1182 5556,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40041@0" ObjectIDZND0="40040@1" Pin0InfoVect0LinkObjId="SW-239280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-1182 5556,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3271ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-965 5556,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40043@1" ObjectIDZND0="40044@0" Pin0InfoVect0LinkObjId="SW-239284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-965 5556,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3271d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-904 5556,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40045@1" ObjectIDZND0="40043@0" Pin0InfoVect0LinkObjId="SW-239283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-904 5556,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_330c970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-1289 4981,-1323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40007@1" ObjectIDZND0="40006@0" Pin0InfoVect0LinkObjId="SW-239254_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239256_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-1289 4981,-1323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_330cb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-1109 5556,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40042@1" ObjectIDZND0="40040@0" Pin0InfoVect0LinkObjId="SW-239280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-1109 5556,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2efcd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-1440 3942,-1410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40050@0" ObjectIDZND0="39973@1" Pin0InfoVect0LinkObjId="SW-239213_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-1440 3942,-1410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2ee5f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-864 3942,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39980@0" ObjectIDZND0="40051@0" Pin0InfoVect0LinkObjId="g_2f22210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-864 3942,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2ee61e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-1374 3942,-1348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39973@0" ObjectIDZND0="39972@1" Pin0InfoVect0LinkObjId="SW-239212_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239213_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-1374 3942,-1348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2ee6440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-1321 3942,-1287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39972@0" ObjectIDZND0="39974@1" Pin0InfoVect0LinkObjId="SW-239214_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239212_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-1321 3942,-1287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2ee66a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-1178 3942,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39976@0" ObjectIDZND0="39975@1" Pin0InfoVect0LinkObjId="SW-239215_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239216_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-1178 3942,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2ee6900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-1125 3942,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39975@0" ObjectIDZND0="39977@1" Pin0InfoVect0LinkObjId="SW-239217_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239215_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-1125 3942,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2ee6b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-964 3942,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39978@1" ObjectIDZND0="39979@0" Pin0InfoVect0LinkObjId="SW-239219_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-964 3942,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2ee6dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-900 3942,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39980@1" ObjectIDZND0="39978@0" Pin0InfoVect0LinkObjId="SW-239218_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239220_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-900 3942,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2ee7020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-1251 3942,-1214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39974@0" ObjectIDZND0="39976@1" Pin0InfoVect0LinkObjId="SW-239216_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-1251 3942,-1214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb4880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-1060 3736,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39968@0" ObjectIDZND0="39970@1" Pin0InfoVect0LinkObjId="SW-239210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-1060 3736,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb4a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-1023 3942,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39979@1" ObjectIDZND0="39977@x" ObjectIDZND1="39981@x" ObjectIDZND2="39998@x" Pin0InfoVect0LinkObjId="SW-239217_0" Pin0InfoVect1LinkObjId="SW-239221_0" Pin0InfoVect2LinkObjId="SW-239246_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239219_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-1023 3942,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb4c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-1039 3942,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="39979@x" ObjectIDND1="39981@x" ObjectIDND2="39998@x" ObjectIDZND0="39977@0" Pin0InfoVect0LinkObjId="SW-239217_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239219_0" Pin1InfoVect1LinkObjId="SW-239221_0" Pin1InfoVect2LinkObjId="SW-239246_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-1039 3942,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb6390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-1255 4161,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39775@0" ObjectIDZND0="39982@x" ObjectIDZND1="39780@x" ObjectIDZND2="39994@x" Pin0InfoVect0LinkObjId="SW-239227_0" Pin0InfoVect1LinkObjId="SW-239225_0" Pin0InfoVect2LinkObjId="SW-239242_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239224_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-1255 4161,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb65b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-1237 4161,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="39775@x" ObjectIDND1="39780@x" ObjectIDND2="39994@x" ObjectIDZND0="39982@1" Pin0InfoVect0LinkObjId="SW-239227_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239224_0" Pin1InfoVect1LinkObjId="SW-239225_0" Pin1InfoVect2LinkObjId="SW-239242_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-1237 4161,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb6d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-1073 4161,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39777@0" ObjectIDZND0="39983@1" Pin0InfoVect0LinkObjId="SW-239230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239228_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-1073 4161,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb8ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-1220 5342,-1235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="40023@1" ObjectIDZND0="40019@x" ObjectIDZND1="40021@x" ObjectIDZND2="40020@x" Pin0InfoVect0LinkObjId="SW-239267_0" Pin0InfoVect1LinkObjId="SW-239269_0" Pin0InfoVect2LinkObjId="SW-239268_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239272_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-1220 5342,-1235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb9140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5342,-1235 5342,-1257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="40023@x" ObjectIDND1="40021@x" ObjectIDND2="40020@x" ObjectIDZND0="40019@0" Pin0InfoVect0LinkObjId="SW-239267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239272_0" Pin1InfoVect1LinkObjId="SW-239269_0" Pin1InfoVect2LinkObjId="SW-239268_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5342,-1235 5342,-1257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb9c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-1073 5556,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="40042@0" ObjectIDZND0="40044@x" ObjectIDZND1="40047@x" ObjectIDZND2="40046@x" Pin0InfoVect0LinkObjId="SW-239284_0" Pin0InfoVect1LinkObjId="SW-239287_0" Pin0InfoVect2LinkObjId="SW-239286_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239282_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-1073 5556,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_2eb9e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-1051 5556,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="40042@x" ObjectIDND1="40047@x" ObjectIDND2="40046@x" ObjectIDZND0="40044@1" Pin0InfoVect0LinkObjId="SW-239284_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239282_0" Pin1InfoVect1LinkObjId="SW-239287_0" Pin1InfoVect2LinkObjId="SW-239286_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-1051 5556,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_34768c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5723,-1018 5743,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40047@1" ObjectIDZND0="g_3476b20@0" Pin0InfoVect0LinkObjId="g_3476b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239287_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5723,-1018 5743,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_34775b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5687,-1018 5663,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="40047@0" ObjectIDZND0="40046@x" ObjectIDZND1="40042@x" ObjectIDZND2="40044@x" Pin0InfoVect0LinkObjId="SW-239286_0" Pin0InfoVect1LinkObjId="SW-239282_0" Pin0InfoVect2LinkObjId="SW-239284_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5687,-1018 5663,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_34780a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5663,-985 5663,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="40046@1" ObjectIDZND0="40047@x" ObjectIDZND1="40042@x" ObjectIDZND2="40044@x" Pin0InfoVect0LinkObjId="SW-239287_0" Pin0InfoVect1LinkObjId="SW-239282_0" Pin0InfoVect2LinkObjId="SW-239284_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5663,-985 5663,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3478300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5663,-1018 5663,-1051 5556,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40047@x" ObjectIDND1="40046@x" ObjectIDZND0="40042@x" ObjectIDZND1="40044@x" Pin0InfoVect0LinkObjId="SW-239282_0" Pin0InfoVect1LinkObjId="SW-239284_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239287_0" Pin1InfoVect1LinkObjId="SW-239286_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5663,-1018 5663,-1051 5556,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3478df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-1219 3736,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39967@1" ObjectIDZND0="39964@x" ObjectIDZND1="39965@x" ObjectIDZND2="40046@x" Pin0InfoVect0LinkObjId="SW-239204_0" Pin0InfoVect1LinkObjId="SW-239205_0" Pin0InfoVect2LinkObjId="SW-239286_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239207_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-1219 3736,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3479050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-1238 3736,-1256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="39967@x" ObjectIDND1="39965@x" ObjectIDND2="40046@x" ObjectIDZND0="39964@0" Pin0InfoVect0LinkObjId="SW-239204_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239207_0" Pin1InfoVect1LinkObjId="SW-239205_0" Pin1InfoVect2LinkObjId="SW-239286_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-1238 3736,-1256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_347b7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3597,-1237 3575,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39965@0" ObjectIDZND0="g_347ba50@0" Pin0InfoVect0LinkObjId="g_347ba50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239205_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3597,-1237 3575,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3481d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-1235 4352,-1235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39780@1" ObjectIDZND0="g_34812e0@0" Pin0InfoVect0LinkObjId="g_34812e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239225_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-1235 4352,-1235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_34828a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4297,-1235 4258,-1235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39780@0" ObjectIDZND0="39775@x" ObjectIDZND1="39982@x" ObjectIDZND2="39994@x" Pin0InfoVect0LinkObjId="SW-239224_0" Pin0InfoVect1LinkObjId="SW-239227_0" Pin0InfoVect2LinkObjId="SW-239242_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239225_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4297,-1235 4258,-1235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3482b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4258,-1235 4163,-1235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39780@x" ObjectIDND1="39994@x" ObjectIDND2="39993@x" ObjectIDZND0="39775@x" ObjectIDZND1="39982@x" Pin0InfoVect0LinkObjId="SW-239224_0" Pin0InfoVect1LinkObjId="SW-239227_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239225_0" Pin1InfoVect1LinkObjId="SW-239242_0" Pin1InfoVect2LinkObjId="SW-239241_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4258,-1235 4163,-1235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3483630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1060 4506,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39989@0" ObjectIDZND0="39991@x" ObjectIDZND1="39993@x" Pin0InfoVect0LinkObjId="SW-239239_0" Pin0InfoVect1LinkObjId="SW-239241_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1060 4506,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3483890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1040 4506,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="39989@x" ObjectIDND1="39993@x" ObjectIDZND0="39991@1" Pin0InfoVect0LinkObjId="SW-239239_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239237_0" Pin1InfoVect1LinkObjId="SW-239241_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1040 4506,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33159c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4466,-1040 4506,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39993@1" ObjectIDZND0="39989@x" ObjectIDZND1="39991@x" Pin0InfoVect0LinkObjId="SW-239237_0" Pin0InfoVect1LinkObjId="SW-239239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4466,-1040 4506,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3317f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4323,-1040 4302,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39994@0" ObjectIDZND0="g_33181e0@0" Pin0InfoVect0LinkObjId="g_33181e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4323,-1040 4302,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3318c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-1040 4405,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39994@1" ObjectIDZND0="39780@x" ObjectIDZND1="39775@x" ObjectIDZND2="39982@x" Pin0InfoVect0LinkObjId="SW-239225_0" Pin0InfoVect1LinkObjId="SW-239224_0" Pin0InfoVect2LinkObjId="SW-239227_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-1040 4405,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33196e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4258,-1235 4258,-1534 4405,-1534 4405,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39780@x" ObjectIDND1="39775@x" ObjectIDND2="39982@x" ObjectIDZND0="39994@x" ObjectIDZND1="39993@x" Pin0InfoVect0LinkObjId="SW-239242_0" Pin0InfoVect1LinkObjId="SW-239241_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239225_0" Pin1InfoVect1LinkObjId="SW-239224_0" Pin1InfoVect2LinkObjId="SW-239227_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4258,-1235 4258,-1534 4405,-1534 4405,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3319930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-1040 4430,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="39994@x" ObjectIDND1="39780@x" ObjectIDND2="39775@x" ObjectIDZND0="39993@0" Pin0InfoVect0LinkObjId="SW-239241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239242_0" Pin1InfoVect1LinkObjId="SW-239225_0" Pin1InfoVect2LinkObjId="SW-239224_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-1040 4430,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_331ad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1218 4760,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40001@1" ObjectIDZND0="39997@x" ObjectIDZND1="39998@x" Pin0InfoVect0LinkObjId="SW-239245_0" Pin0InfoVect1LinkObjId="SW-239246_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1218 4760,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_331af70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1238 4760,-1255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="40001@x" ObjectIDND1="39998@x" ObjectIDZND0="39997@0" Pin0InfoVect0LinkObjId="SW-239245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239249_0" Pin1InfoVect1LinkObjId="SW-239246_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1238 4760,-1255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_331d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-1238 4919,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39999@1" ObjectIDZND0="g_331d6a0@0" Pin0InfoVect0LinkObjId="g_331d6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239247_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-1238 4919,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33203f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1238 4785,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="40001@x" ObjectIDND1="39997@x" ObjectIDZND0="39998@0" Pin0InfoVect0LinkObjId="SW-239246_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239249_0" Pin1InfoVect1LinkObjId="SW-239245_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1238 4785,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33212c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-1238 4846,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39998@1" ObjectIDZND0="39999@x" ObjectIDZND1="39981@x" ObjectIDZND2="39977@x" Pin0InfoVect0LinkObjId="SW-239247_0" Pin0InfoVect1LinkObjId="SW-239221_0" Pin0InfoVect2LinkObjId="SW-239217_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239246_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-1238 4846,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33214b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4846,-1238 4863,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="39998@x" ObjectIDND1="39981@x" ObjectIDND2="39977@x" ObjectIDZND0="39999@0" Pin0InfoVect0LinkObjId="SW-239247_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239246_0" Pin1InfoVect1LinkObjId="SW-239221_0" Pin1InfoVect2LinkObjId="SW-239217_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4846,-1238 4863,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3324940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-1039 4113,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39981@1" ObjectIDZND0="g_3323eb0@0" Pin0InfoVect0LinkObjId="g_3323eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239221_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-1039 4113,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3324ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4027,-1039 4058,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="39977@x" ObjectIDND1="39979@x" ObjectIDND2="39998@x" ObjectIDZND0="39981@0" Pin0InfoVect0LinkObjId="SW-239221_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239217_0" Pin1InfoVect1LinkObjId="SW-239219_0" Pin1InfoVect2LinkObjId="SW-239246_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4027,-1039 4058,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33256d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-1039 4027,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39977@x" ObjectIDND1="39979@x" ObjectIDZND0="39981@x" ObjectIDZND1="39998@x" ObjectIDZND2="39999@x" Pin0InfoVect0LinkObjId="SW-239221_0" Pin0InfoVect1LinkObjId="SW-239246_0" Pin0InfoVect2LinkObjId="SW-239247_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239217_0" Pin1InfoVect1LinkObjId="SW-239219_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-1039 4027,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3325930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4027,-1039 4027,-1600 4846,-1600 4846,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39981@x" ObjectIDND1="39977@x" ObjectIDND2="39979@x" ObjectIDZND0="39998@x" ObjectIDZND1="39999@x" Pin0InfoVect0LinkObjId="SW-239246_0" Pin0InfoVect1LinkObjId="SW-239247_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239221_0" Pin1InfoVect1LinkObjId="SW-239217_0" Pin1InfoVect2LinkObjId="SW-239219_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4027,-1039 4027,-1600 4846,-1600 4846,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3325ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-1058 4981,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40013@0" ObjectIDZND0="40015@1" Pin0InfoVect0LinkObjId="SW-239263_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-1058 4981,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3327f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-1253 4981,-1234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40007@0" ObjectIDZND0="40012@x" ObjectIDZND1="40009@x" Pin0InfoVect0LinkObjId="SW-239260_0" Pin0InfoVect1LinkObjId="SW-239257_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-1253 4981,-1234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3328110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-1234 4981,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="40007@x" ObjectIDND1="40009@x" ObjectIDZND0="40012@1" Pin0InfoVect0LinkObjId="SW-239260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239256_0" Pin1InfoVect1LinkObjId="SW-239257_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-1234 4981,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_332a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-1234 5003,-1234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="40007@x" ObjectIDND1="40012@x" ObjectIDZND0="40009@0" Pin0InfoVect0LinkObjId="SW-239257_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239256_0" Pin1InfoVect1LinkObjId="SW-239260_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-1234 5003,-1234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_332d310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5136,-1234 5155,-1234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40010@1" ObjectIDZND0="g_332d570@0" Pin0InfoVect0LinkObjId="g_332d570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239258_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5136,-1234 5155,-1234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_332e000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-1234 5100,-1234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="40009@x" ObjectIDND1="40020@x" ObjectIDND2="40049@x" ObjectIDZND0="40010@0" Pin0InfoVect0LinkObjId="SW-239258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239257_0" Pin1InfoVect1LinkObjId="SW-239268_0" Pin1InfoVect2LinkObjId="SW-239270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-1234 5100,-1234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_332eb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-1234 5072,-1234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="40009@1" ObjectIDZND0="40010@x" ObjectIDZND1="40020@x" ObjectIDZND2="40049@x" Pin0InfoVect0LinkObjId="SW-239258_0" Pin0InfoVect1LinkObjId="SW-239268_0" Pin0InfoVect2LinkObjId="SW-239270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239257_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-1234 5072,-1234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33312d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5228,-1197 5206,-1197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40021@0" ObjectIDZND0="g_3331530@0" Pin0InfoVect0LinkObjId="g_3331530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5228,-1197 5206,-1197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33347d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5264,-1197 5286,-1197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="40021@1" ObjectIDZND0="40020@x" ObjectIDZND1="40023@x" ObjectIDZND2="40019@x" Pin0InfoVect0LinkObjId="SW-239268_0" Pin0InfoVect1LinkObjId="SW-239272_0" Pin0InfoVect2LinkObjId="SW-239267_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5264,-1197 5286,-1197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3335300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5286,-1171 5286,-1197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="40020@1" ObjectIDZND0="40021@x" ObjectIDZND1="40023@x" ObjectIDZND2="40019@x" Pin0InfoVect0LinkObjId="SW-239269_0" Pin0InfoVect1LinkObjId="SW-239272_0" Pin0InfoVect2LinkObjId="SW-239267_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5286,-1171 5286,-1197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3335560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5286,-1197 5286,-1237 5345,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40021@x" ObjectIDND1="40020@x" ObjectIDZND0="40023@x" ObjectIDZND1="40019@x" Pin0InfoVect0LinkObjId="SW-239272_0" Pin0InfoVect1LinkObjId="SW-239267_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239269_0" Pin1InfoVect1LinkObjId="SW-239268_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5286,-1197 5286,-1237 5345,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33357c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5249,-1107 5286,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="40049@0" ObjectIDZND0="40010@x" ObjectIDZND1="40009@x" ObjectIDZND2="40020@x" Pin0InfoVect0LinkObjId="SW-239258_0" Pin0InfoVect1LinkObjId="SW-239257_0" Pin0InfoVect2LinkObjId="SW-239268_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5249,-1107 5286,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_33362f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-1234 5072,-739 5286,-739 5286,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40010@x" ObjectIDND1="40009@x" ObjectIDZND0="40020@x" ObjectIDZND1="40049@x" Pin0InfoVect0LinkObjId="SW-239268_0" Pin0InfoVect1LinkObjId="SW-239270_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239258_0" Pin1InfoVect1LinkObjId="SW-239257_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-1234 5072,-739 5286,-739 5286,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_3336560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5286,-1107 5286,-1135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="40010@x" ObjectIDND1="40009@x" ObjectIDND2="40049@x" ObjectIDZND0="40020@0" Pin0InfoVect0LinkObjId="SW-239268_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239258_0" Pin1InfoVect1LinkObjId="SW-239257_0" Pin1InfoVect2LinkObjId="SW-239270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5286,-1107 5286,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_333a220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1219 4506,-1256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39988@1" ObjectIDZND0="39986@0" Pin0InfoVect0LinkObjId="SW-239234_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1219 4506,-1256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_333a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3651,-1237 3651,-679 5663,-679 5663,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39965@x" ObjectIDND1="39967@x" ObjectIDND2="39964@x" ObjectIDZND0="40046@x" ObjectIDZND1="40048@x" Pin0InfoVect0LinkObjId="SW-239286_0" Pin0InfoVect1LinkObjId="SW-239288_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239205_0" Pin1InfoVect1LinkObjId="SW-239207_0" Pin1InfoVect2LinkObjId="SW-239204_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3651,-1237 3651,-679 5663,-679 5663,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_337b280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5717,-915 5663,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="40048@0" ObjectIDZND0="40046@x" ObjectIDZND1="39965@x" ObjectIDZND2="39967@x" Pin0InfoVect0LinkObjId="SW-239286_0" Pin0InfoVect1LinkObjId="SW-239205_0" Pin0InfoVect2LinkObjId="SW-239207_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5717,-915 5663,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_337b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5663,-915 5663,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="40048@x" ObjectIDND1="39965@x" ObjectIDND2="39967@x" ObjectIDZND0="40046@0" Pin0InfoVect0LinkObjId="SW-239286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-239288_0" Pin1InfoVect1LinkObjId="SW-239205_0" Pin1InfoVect2LinkObjId="SW-239207_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5663,-915 5663,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_337fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-1237 3651,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39965@1" ObjectIDZND0="40046@x" ObjectIDZND1="40048@x" ObjectIDZND2="39967@x" Pin0InfoVect0LinkObjId="SW-239286_0" Pin0InfoVect1LinkObjId="SW-239288_0" Pin0InfoVect2LinkObjId="SW-239207_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239205_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-1237 3651,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_337ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-1237 3651,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39967@x" ObjectIDND1="39964@x" ObjectIDZND0="39965@x" ObjectIDZND1="40046@x" ObjectIDZND2="40048@x" Pin0InfoVect0LinkObjId="SW-239205_0" Pin0InfoVect1LinkObjId="SW-239286_0" Pin0InfoVect2LinkObjId="SW-239288_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239207_0" Pin1InfoVect1LinkObjId="SW-239204_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-1237 3651,-1237 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="40052" cx="4506" cy="-1438" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40052" cx="4760" cy="-1438" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40052" cx="4981" cy="-1438" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40050" cx="4161" cy="-1440" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40050" cx="3736" cy="-1440" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40050" cx="3942" cy="-1440" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40051" cx="4161" cy="-832" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40051" cx="3736" cy="-832" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40051" cx="3942" cy="-832" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40054" cx="5342" cy="-1441" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40054" cx="5556" cy="-1441" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40053" cx="4506" cy="-834" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40053" cx="4760" cy="-834" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40053" cx="4981" cy="-834" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40055" cx="5342" cy="-839" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40055" cx="5556" cy="-839" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2ed2800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -1470.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f26830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4293.000000 -1519.000000) translate(0,16)">鲁昆乙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="38" graphid="g_2edb830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.500000 -1710.500000) translate(0,31)">龙昆甲、鲁昆甲乙、龙鲁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eedce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4521.000000 -1152.000000) translate(0,16)">5412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eedf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4521.000000 -960.000000) translate(0,16)">5413</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f26d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -1348.000000) translate(0,16)">5421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2ebf500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -1150.000000) translate(0,16)">5422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2ebf710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4992.000000 -1345.000000) translate(0,16)">5431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2ed97e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -1149.000000) translate(0,16)">5432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2edb130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4041.000000 -820.000000) translate(0,16)">昆北换流站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3043950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -822.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3043db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -1469.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f419b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4708.000000 -811.000000) translate(0,16)">鲁地拉电厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3271f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5502.000000 -822.000000) translate(0,16)">龙开口电厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3272550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5242.000000 -1468.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330c760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5313.000000 -826.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330cd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.066594 -1404.000000) translate(0,16)">50511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330d070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.066594 -1347.000000) translate(0,16)">5051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330d2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.066594 -1281.000000) translate(0,16)">50512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330d4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.066594 -1151.000000) translate(0,16)">5052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330d730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.066594 -1085.000000) translate(0,16)">50522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330d970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.066594 -1017.000000) translate(0,16)">50531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330dbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.066594 -894.000000) translate(0,16)">50532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330ddf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.066594 -960.000000) translate(0,16)">5053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330e030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.041961 -1346.000000) translate(0,16)">5071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.041961 -1280.000000) translate(0,16)">50712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330e4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.041961 -1403.000000) translate(0,16)">50711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330e730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.041961 -1207.000000) translate(0,16)">50721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330e970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.041961 -1150.000000) translate(0,16)">5072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330ebb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.041961 -1098.000000) translate(0,16)">50722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330edf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.041961 -1006.000000) translate(0,16)">50731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330f030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.041961 -959.000000) translate(0,16)">5073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330f270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.041961 -893.000000) translate(0,16)">50732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330f4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5348.883140 -1405.000000) translate(0,16)">53211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330f6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5350.883140 -1348.000000) translate(0,16)">5321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330f930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5348.883140 -1282.000000) translate(0,16)">53212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330fb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5350.883140 -1152.000000) translate(0,16)">5322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330fdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5348.883140 -1209.000000) translate(0,16)">53221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_330fff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5348.883140 -1086.000000) translate(0,16)">53222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3310230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5348.883140 -1018.000000) translate(0,16)">53231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3310470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5350.883140 -961.000000) translate(0,16)">5323</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33106b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5348.883140 -895.000000) translate(0,16)">53232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33108f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5563.000000 -1403.000000) translate(0,16)">53311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3310b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -1346.000000) translate(0,16)">5331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3310d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5563.000000 -1280.000000) translate(0,16)">53312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3310fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5563.000000 -1207.000000) translate(0,16)">53321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33111f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -1150.000000) translate(0,16)">5332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3311430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5563.000000 -1098.000000) translate(0,16)">53322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3311670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5563.000000 -1016.000000) translate(0,16)">53331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33118b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -959.000000) translate(0,16)">5333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3311af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5563.000000 -893.000000) translate(0,16)">53332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303e950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.860625 -1406.000000) translate(0,16)">54111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303eb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4519.860625 -1349.000000) translate(0,16)">5411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303edd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.860625 -1283.000000) translate(0,16)">54112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303f010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.860625 -1210.000000) translate(0,16)">54121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303f250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.860625 -1087.000000) translate(0,16)">54122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303f490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.860625 -1019.000000) translate(0,16)">54131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303f6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.860625 -896.000000) translate(0,16)">54132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303f910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.314402 -1403.000000) translate(0,16)">54211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303fb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4770.314402 -1295.000000) translate(0,16)">54212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303fd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4775.314402 -1209.000000) translate(0,16)">54221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_303ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.314402 -1085.000000) translate(0,16)">54222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3040210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4775.314402 -1019.000000) translate(0,16)">54231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3040450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4780.314402 -960.000000) translate(0,16)">5423</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3040690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.314402 -894.000000) translate(0,16)">54232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_30408d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4987.388835 -1401.000000) translate(0,16)">54311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3040b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4989.388835 -1293.000000) translate(0,16)">54312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3040d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4998.388835 -1207.000000) translate(0,16)">54321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3040f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4999.388835 -1085.000000) translate(0,16)">54322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_30411d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4995.388835 -1016.000000) translate(0,16)">54331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3041410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4998.388835 -958.000000) translate(0,16)">5433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3041650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4999.388835 -893.000000) translate(0,16)">54332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2ee7280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.389365 -1399.000000) translate(0,16)">50611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2ee7a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.389365 -1342.000000) translate(0,16)">5061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2ee7ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.389365 -1276.000000) translate(0,16)">50612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2ee7f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.389365 -1203.000000) translate(0,16)">50621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb3b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.389365 -1146.000000) translate(0,16)">5062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.389365 -1080.000000) translate(0,16)">50622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb3f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.389365 -1012.000000) translate(0,16)">50631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb41c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.389365 -889.000000) translate(0,16)">50632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb4400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.389365 -955.000000) translate(0,16)">5063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb4640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.000000 -868.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb4e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4302.000000 -1627.000000) translate(0,16)">鲁昆甲线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb67d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -664.000000) translate(0,16)">龙昆甲线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -723.000000) translate(0,16)">龙鲁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3319b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4294.859013 -1272.000000) translate(0,16)">507167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_331a180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4423.677677 -1079.000000) translate(0,16)">54136</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_331a3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.677677 -1075.000000) translate(0,16)">5413617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3320650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4780.134396 -1267.000000) translate(0,16)">54216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33216a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4053.209359 -1073.000000) translate(0,16)">506367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3321b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.134396 -1267.000000) translate(0,16)">5421617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33272b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.000000 -819.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_332a810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5002.134396 -1261.000000) translate(0,16)">54316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_332ae40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5073.134396 -1264.000000) translate(0,16)">5431617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33367c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5215.677677 -1168.000000) translate(0,16)">53216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3336cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5198.677677 -1224.000000) translate(0,16)">532167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3336ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5188.677677 -1133.000000) translate(0,16)">5321617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_337b740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5674.000000 -984.000000) translate(0,16)">53336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_337bc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5689.000000 -1047.000000) translate(0,16)">533367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_337bec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5691.000000 -949.000000) translate(0,16)">5333617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_337f000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3592.066594 -1272.000000) translate(0,16)">505167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3381d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3746.066594 -1210.000000) translate(0,16)">50521</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-239234">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.860625 -1251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39986" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_54112SW"/>
     <cge:Meas_Ref ObjectId="239234"/>
    <cge:TPSR_Ref TObjectID="39986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239236">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.860625 -1178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39988" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_54121SW"/>
     <cge:Meas_Ref ObjectId="239236"/>
    <cge:TPSR_Ref TObjectID="39988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239237">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.860625 -1055.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39989" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_54122SW"/>
     <cge:Meas_Ref ObjectId="239237"/>
    <cge:TPSR_Ref TObjectID="39989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239240">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.860625 -864.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39992" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_54132SW"/>
     <cge:Meas_Ref ObjectId="239240"/>
    <cge:TPSR_Ref TObjectID="39992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239239">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.860625 -987.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39991" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_54131SW"/>
     <cge:Meas_Ref ObjectId="239239"/>
    <cge:TPSR_Ref TObjectID="39991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239233">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.860625 -1374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39985" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_54111SW"/>
     <cge:Meas_Ref ObjectId="239233"/>
    <cge:TPSR_Ref TObjectID="39985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239244">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.314402 -1373.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39996" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_54211SW"/>
     <cge:Meas_Ref ObjectId="239244"/>
    <cge:TPSR_Ref TObjectID="39996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239245">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.314402 -1250.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39997" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_54212SW"/>
     <cge:Meas_Ref ObjectId="239245"/>
    <cge:TPSR_Ref TObjectID="39997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239249">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.314402 -1177.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40001" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_54221SW"/>
     <cge:Meas_Ref ObjectId="239249"/>
    <cge:TPSR_Ref TObjectID="40001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239250">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.314402 -1054.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40002" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_54222SW"/>
     <cge:Meas_Ref ObjectId="239250"/>
    <cge:TPSR_Ref TObjectID="40002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239253">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.314402 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40005" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_54232SW"/>
     <cge:Meas_Ref ObjectId="239253"/>
    <cge:TPSR_Ref TObjectID="40005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239252">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.314402 -986.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40004" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_54231SW"/>
     <cge:Meas_Ref ObjectId="239252"/>
    <cge:TPSR_Ref TObjectID="40004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239256">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.388835 -1248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40007" ObjectName="SW-CX_TASE2JS.YN_LL_54312SW"/>
     <cge:Meas_Ref ObjectId="239256"/>
    <cge:TPSR_Ref TObjectID="40007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239260">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.388835 -1176.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40012" ObjectName="SW-CX_TASE2JS.YN_LL_54321SW"/>
     <cge:Meas_Ref ObjectId="239260"/>
    <cge:TPSR_Ref TObjectID="40012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239261">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.388835 -1053.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40013" ObjectName="SW-CX_TASE2JS.YN_LL_54322SW"/>
     <cge:Meas_Ref ObjectId="239261"/>
    <cge:TPSR_Ref TObjectID="40013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239264">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.388835 -861.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40016" ObjectName="SW-CX_TASE2JS.YN_LL_54332SW"/>
     <cge:Meas_Ref ObjectId="239264"/>
    <cge:TPSR_Ref TObjectID="40016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239263">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.388835 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40015" ObjectName="SW-CX_TASE2JS.YN_LL_54331SW"/>
     <cge:Meas_Ref ObjectId="239263"/>
    <cge:TPSR_Ref TObjectID="40015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239255">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.388835 -1371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40008" ObjectName="SW-CX_TASE2JS.YN_LL_54311SW"/>
     <cge:Meas_Ref ObjectId="239255"/>
    <cge:TPSR_Ref TObjectID="40008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239223">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.041961 -1373.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39774" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_50711SW"/>
     <cge:Meas_Ref ObjectId="239223"/>
    <cge:TPSR_Ref TObjectID="39774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239224">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.041961 -1250.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39775" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_50712SW"/>
     <cge:Meas_Ref ObjectId="239224"/>
    <cge:TPSR_Ref TObjectID="39775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239227">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.041961 -1177.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39982" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_50721SW"/>
     <cge:Meas_Ref ObjectId="239227"/>
    <cge:TPSR_Ref TObjectID="39982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239228">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.041961 -1068.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39777" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_50722SW"/>
     <cge:Meas_Ref ObjectId="239228"/>
    <cge:TPSR_Ref TObjectID="39777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239231">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.041961 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39779" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_50732SW"/>
     <cge:Meas_Ref ObjectId="239231"/>
    <cge:TPSR_Ref TObjectID="39779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239230">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.041961 -976.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39983" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_50731SW"/>
     <cge:Meas_Ref ObjectId="239230"/>
    <cge:TPSR_Ref TObjectID="39983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239203">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.066594 -1374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39963" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50511SW"/>
     <cge:Meas_Ref ObjectId="239203"/>
    <cge:TPSR_Ref TObjectID="39963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239204">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.066594 -1251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39964" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50512SW"/>
     <cge:Meas_Ref ObjectId="239204"/>
    <cge:TPSR_Ref TObjectID="39964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239207">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.066594 -1178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39967" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50521SW"/>
     <cge:Meas_Ref ObjectId="239207"/>
    <cge:TPSR_Ref TObjectID="39967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239208">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.066594 -1055.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39968" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50522SW"/>
     <cge:Meas_Ref ObjectId="239208"/>
    <cge:TPSR_Ref TObjectID="39968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239211">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.066594 -864.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39971" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50532SW"/>
     <cge:Meas_Ref ObjectId="239211"/>
    <cge:TPSR_Ref TObjectID="39971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239210">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.066594 -987.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39970" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50531SW"/>
     <cge:Meas_Ref ObjectId="239210"/>
    <cge:TPSR_Ref TObjectID="39970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239266">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5332.883140 -1375.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40018" ObjectName="SW-CX_TASE2JS.YN_LL_53211SW"/>
     <cge:Meas_Ref ObjectId="239266"/>
    <cge:TPSR_Ref TObjectID="40018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239267">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5332.883140 -1252.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40019" ObjectName="SW-CX_TASE2JS.YN_LL_53212SW"/>
     <cge:Meas_Ref ObjectId="239267"/>
    <cge:TPSR_Ref TObjectID="40019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239272">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5332.883140 -1179.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40023" ObjectName="SW-CX_TASE2JS.YN_LL_53221SW"/>
     <cge:Meas_Ref ObjectId="239272"/>
    <cge:TPSR_Ref TObjectID="40023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239273">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5332.883140 -1056.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40024" ObjectName="SW-CX_TASE2JS.YN_LL_53222SW"/>
     <cge:Meas_Ref ObjectId="239273"/>
    <cge:TPSR_Ref TObjectID="40024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239276">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5332.883140 -864.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40027" ObjectName="SW-CX_TASE2JS.YN_LL_53232SW"/>
     <cge:Meas_Ref ObjectId="239276"/>
    <cge:TPSR_Ref TObjectID="40027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239275">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5332.883140 -987.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40026" ObjectName="SW-CX_TASE2JS.YN_LL_53231SW"/>
     <cge:Meas_Ref ObjectId="239275"/>
    <cge:TPSR_Ref TObjectID="40026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239279">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -1250.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40039" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_53312SW"/>
     <cge:Meas_Ref ObjectId="239279"/>
    <cge:TPSR_Ref TObjectID="40039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239281">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -1177.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40041" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_53321SW"/>
     <cge:Meas_Ref ObjectId="239281"/>
    <cge:TPSR_Ref TObjectID="40041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239282">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -1068.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40042" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_53322SW"/>
     <cge:Meas_Ref ObjectId="239282"/>
    <cge:TPSR_Ref TObjectID="40042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239285">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40045" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_53332SW"/>
     <cge:Meas_Ref ObjectId="239285"/>
    <cge:TPSR_Ref TObjectID="40045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239284">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -986.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40044" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_53331SW"/>
     <cge:Meas_Ref ObjectId="239284"/>
    <cge:TPSR_Ref TObjectID="40044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239278">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -1373.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40038" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_53311SW"/>
     <cge:Meas_Ref ObjectId="239278"/>
    <cge:TPSR_Ref TObjectID="40038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239213">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.389365 -1369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39973" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50611SW"/>
     <cge:Meas_Ref ObjectId="239213"/>
    <cge:TPSR_Ref TObjectID="39973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239214">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.389365 -1246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39974" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50612SW"/>
     <cge:Meas_Ref ObjectId="239214"/>
    <cge:TPSR_Ref TObjectID="39974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239216">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.389365 -1173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39976" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50621SW"/>
     <cge:Meas_Ref ObjectId="239216"/>
    <cge:TPSR_Ref TObjectID="39976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239217">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.389365 -1050.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39977" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50622SW"/>
     <cge:Meas_Ref ObjectId="239217"/>
    <cge:TPSR_Ref TObjectID="39977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239220">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.389365 -859.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39980" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50632SW"/>
     <cge:Meas_Ref ObjectId="239220"/>
    <cge:TPSR_Ref TObjectID="39980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239219">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.389365 -982.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39979" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_50631SW"/>
     <cge:Meas_Ref ObjectId="239219"/>
    <cge:TPSR_Ref TObjectID="39979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239286">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5654.000000 -944.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40046" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_53336SW"/>
     <cge:Meas_Ref ObjectId="239286"/>
    <cge:TPSR_Ref TObjectID="40046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239287">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5682.000000 -1013.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40047" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_533367SW"/>
     <cge:Meas_Ref ObjectId="239287"/>
    <cge:TPSR_Ref TObjectID="40047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239205">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 -1232.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39965" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_505167SW"/>
     <cge:Meas_Ref ObjectId="239205"/>
    <cge:TPSR_Ref TObjectID="39965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239225">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.182949 -1230.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39780" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_507167SW"/>
     <cge:Meas_Ref ObjectId="239225"/>
    <cge:TPSR_Ref TObjectID="39780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239241">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.317345 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39993" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_54136SW"/>
     <cge:Meas_Ref ObjectId="239241"/>
    <cge:TPSR_Ref TObjectID="39993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239242">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.182949 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39994" ObjectName="SW-CX_TASE2JS.YN_LKⅡ_5413617SW"/>
     <cge:Meas_Ref ObjectId="239242"/>
    <cge:TPSR_Ref TObjectID="39994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239247">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4858.180006 -1233.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39999" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5421617SW"/>
     <cge:Meas_Ref ObjectId="239247"/>
    <cge:TPSR_Ref TObjectID="39999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239246">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 4780.180006 -1234.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39998" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_54216SW"/>
     <cge:Meas_Ref ObjectId="239246"/>
    <cge:TPSR_Ref TObjectID="39998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239221">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.180006 -1034.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39981" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_506367SW"/>
     <cge:Meas_Ref ObjectId="239221"/>
    <cge:TPSR_Ref TObjectID="39981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239257">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4998.205464 -1229.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40009" ObjectName="SW-CX_TASE2JS.YN_LL_54316SW"/>
     <cge:Meas_Ref ObjectId="239257"/>
    <cge:TPSR_Ref TObjectID="40009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239258">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5095.205464 -1229.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40010" ObjectName="SW-CX_TASE2JS.YN_LL_5431617SW"/>
     <cge:Meas_Ref ObjectId="239258"/>
    <cge:TPSR_Ref TObjectID="40010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239269">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5223.205464 -1192.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40021" ObjectName="SW-CX_TASE2JS.YN_LL_532167SW"/>
     <cge:Meas_Ref ObjectId="239269"/>
    <cge:TPSR_Ref TObjectID="40021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239268">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5277.388835 -1130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40020" ObjectName="SW-CX_TASE2JS.YN_LL_53216SW"/>
     <cge:Meas_Ref ObjectId="239268"/>
    <cge:TPSR_Ref TObjectID="40020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239270">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5196.205464 -1113.000000)" xlink:href="#switch2:shape44_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40049" ObjectName="SW-CX_TASE2JS.YN_LL_5321617SW"/>
     <cge:Meas_Ref ObjectId="239270"/>
    <cge:TPSR_Ref TObjectID="40049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239288">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5714.000000 -921.000000)" xlink:href="#switch2:shape43_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40048" ObjectName="SW-CX_TASE2JS.YN_LKⅠ_5333617SW"/>
     <cge:Meas_Ref ObjectId="239288"/>
    <cge:TPSR_Ref TObjectID="40048"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3691.500000 -1624.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239295" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -1655.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239295" ObjectName="CX_TASE2JS:YN_LuKunⅠ_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239296" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -1640.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239296" ObjectName="CX_TASE2JS:YN_LuKunⅠ_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239297" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -1625.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239297" ObjectName="CX_TASE2JS:YN_LuKunⅠ_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239298" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4354.000000 -1478.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239298" ObjectName="CX_TASE2JS:YN_LuKunⅡ_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239299" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4354.000000 -1463.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239299" ObjectName="CX_TASE2JS:YN_LuKunⅡ_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239300" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4354.000000 -1448.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239300" ObjectName="CX_TASE2JS:YN_LuKunⅡ_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239292" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5196.000000 -797.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239292" ObjectName="CX_TASE2JS:YN_LongLu_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239293" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5196.000000 -782.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239293" ObjectName="CX_TASE2JS:YN_LongLu_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239294" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5196.000000 -767.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239294" ObjectName="CX_TASE2JS:YN_LongLu_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239290" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4531.000000 -715.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239290" ObjectName="CX_TASE2JS:YN_LongKunⅠ_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239291" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4531.000000 -700.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239291" ObjectName="CX_TASE2JS:YN_LongKunⅠ_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiNoTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239289" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4531.000000 -730.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239289" ObjectName="CX_TASE2JS:YN_LongKunⅠ_P"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_局属变.svg" style="fill-opacity:0"><rect height="84" qtmmishow="hidden" width="526" x="3616" y="-1734"/></g>
   <g href="cx_索引_接线图_局属变.svg" style="fill-opacity:0"><rect height="131" qtmmishow="hidden" width="173" x="3517" y="-1765"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303ba00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 1477.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303bfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 1462.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303ce50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 1447.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303d8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 1655.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303dbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.000000 1640.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303ddf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4426.000000 1625.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303e210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5130.000000 798.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303e4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5119.000000 783.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303e710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5144.000000 768.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3382de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4465.000000 731.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33830a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4454.000000 716.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33832e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4479.000000 701.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="84" qtmmishow="hidden" width="526" x="3616" y="-1734"/>
    </a>
   <metadata/><rect fill="white" height="84" opacity="0" stroke="white" transform="" width="526" x="3616" y="-1734"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="131" qtmmishow="hidden" width="173" x="3517" y="-1765"/>
    </a>
   <metadata/><rect fill="white" height="131" opacity="0" stroke="white" transform="" width="173" x="3517" y="-1765"/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3476b20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5738.000000 -1009.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347ba50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3550.000000 -1229.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34812e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4348.182949 -1226.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33181e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.182949 -1031.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331d6a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.180006 -1229.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3323eb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.180006 -1030.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_332d570" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5151.205464 -1225.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3331530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5181.205464 -1188.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>