<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-220" aopId="4063750" id="thSvg" product="E8000V2" version="1.0" viewBox="-229 -1099 2191 1175">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape28">
    <polyline arcFlag="1" points="19,105 17,105 15,104 14,104 12,103 11,102 9,101 8,99 7,97 7,96 6,94 6,92 6,90 7,88 7,87 8,85 9,84 11,82 12,81 14,80 15,80 17,79 19,79 21,79 23,80 24,80 26,81 27,82 29,84 30,85 31,87 31,88 32,90 32,92 " stroke-width="0.0972"/>
    <polyline arcFlag="1" points="36,30 37,30 38,30 38,30 39,31 39,31 40,31 40,32 41,32 41,33 41,34 41,34 42,35 42,36 42,36 41,37 41,38 41,38 41,39 40,39 40,40 39,40 39,40 38,41 38,41 37,41 36,41 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="60" y2="60"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="92" y2="26"/>
    <polyline arcFlag="1" points="36,41 37,41 38,41 38,42 39,42 39,42 40,43 40,43 41,44 41,44 41,45 41,45 42,46 42,47 42,47 41,48 41,49 41,49 41,50 40,50 40,51 39,51 39,52 38,52 38,52 37,52 36,52 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,19 37,19 38,19 38,19 39,19 39,20 40,20 40,21 41,21 41,22 41,22 41,23 42,24 42,24 42,25 41,26 41,26 41,27 41,27 40,28 40,28 39,29 39,29 38,29 38,30 37,30 36,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="60" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="92" y2="92"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.1087"/>
    <polyline points="58,100 64,100 " stroke-width="1.1087"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.1087"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="27" x2="28" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="22" x2="21" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="28" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
    <circle cx="23" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="7" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="32" y1="33" y2="33"/>
    <ellipse cx="22" cy="14" rx="7.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="30" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="46" y1="32" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="31" y2="35"/>
    <ellipse cx="12" cy="20" rx="7.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="21" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="27" y1="16" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="21" y1="16" y2="12"/>
    <ellipse cx="12" cy="8" rx="7.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="16" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="16" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="29" y1="38" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="32" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="33" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="33" y2="33"/>
    <rect height="5" stroke-width="1" width="13" x="19" y="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2329670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2329fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_232a9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_232b6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_232c8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_232d1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_232d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_232e360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_232ec30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_232f610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_232f610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2331400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2331400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_23323f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2334080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2334c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2335640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2335f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2337540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2337d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2338370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2338d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2339d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_233a580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_233b0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1afb0c0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_233da70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_233e260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_234dee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2345c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23475f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_233d4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1185" width="2201" x="-234" y="-1104"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1474" x2="1459" y1="-776" y2="-761"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1459" x2="1474" y1="-776" y2="-761"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="643" x2="628" y1="-773" y2="-758"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="628" x2="643" y1="-773" y2="-758"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="417" x2="402" y1="-767" y2="-752"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="402" x2="417" y1="-767" y2="-752"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="711" x2="696" y1="-730" y2="-715"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="696" x2="711" y1="-730" y2="-715"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="711" x2="696" y1="-510" y2="-495"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="696" x2="711" y1="-510" y2="-495"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="565" x2="550" y1="-173" y2="-158"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="550" x2="565" y1="-173" y2="-158"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1390" x2="1405" y1="-490" y2="-475"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1405" x2="1390" y1="-490" y2="-475"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1083" x2="1068" y1="-920" y2="-905"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1068" x2="1083" y1="-920" y2="-905"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1398" x2="1803" y1="-502" y2="-502"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1953" x2="1962" y1="-480" y2="-480"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" points="1587,-495 1587,-510 1599,-502 1587,-495 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" points="1641,-495 1641,-510 1629,-502 1641,-495 " stroke="rgb(0,255,0)"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-148334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 -748.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25934" ObjectName="SW-YA_DXC.YA_DXC_3111SW"/>
     <cge:Meas_Ref ObjectId="148334"/>
    <cge:TPSR_Ref TObjectID="25934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148336">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 -828.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25936" ObjectName="SW-YA_DXC.YA_DXC_3112SW"/>
     <cge:Meas_Ref ObjectId="148336"/>
    <cge:TPSR_Ref TObjectID="25936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148347">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1086.000000 -908.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25937" ObjectName="SW-YA_DXC.YA_DXC_3113SW"/>
     <cge:Meas_Ref ObjectId="148347"/>
    <cge:TPSR_Ref TObjectID="25937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148358">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.000000 -782.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25939" ObjectName="SW-YA_DXC.YA_DXC_3121SW"/>
     <cge:Meas_Ref ObjectId="148358"/>
    <cge:TPSR_Ref TObjectID="25939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148360">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.000000 -865.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25941" ObjectName="SW-YA_DXC.YA_DXC_3122SW"/>
     <cge:Meas_Ref ObjectId="148360"/>
    <cge:TPSR_Ref TObjectID="25941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148359">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1426.000000 -891.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25940" ObjectName="SW-YA_DXC.YA_DXC_31227SW"/>
     <cge:Meas_Ref ObjectId="148359"/>
    <cge:TPSR_Ref TObjectID="25940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148371">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1486.000000 -937.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25942" ObjectName="SW-YA_DXC.YA_DXC_3123SW"/>
     <cge:Meas_Ref ObjectId="148371"/>
    <cge:TPSR_Ref TObjectID="25942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -780.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25944" ObjectName="SW-YA_DXC.YA_DXC_3131SW"/>
     <cge:Meas_Ref ObjectId="148382"/>
    <cge:TPSR_Ref TObjectID="25944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -862.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25946" ObjectName="SW-YA_DXC.YA_DXC_3132SW"/>
     <cge:Meas_Ref ObjectId="148384"/>
    <cge:TPSR_Ref TObjectID="25946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148383">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 594.000000 -891.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25945" ObjectName="SW-YA_DXC.YA_DXC_31327SW"/>
     <cge:Meas_Ref ObjectId="148383"/>
    <cge:TPSR_Ref TObjectID="25945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148395">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 654.000000 -937.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25947" ObjectName="SW-YA_DXC.YA_DXC_3133SW"/>
     <cge:Meas_Ref ObjectId="148395"/>
    <cge:TPSR_Ref TObjectID="25947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148479">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 -773.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25952" ObjectName="SW-YA_DXC.YA_DXC_3901SW"/>
     <cge:Meas_Ref ObjectId="148479"/>
    <cge:TPSR_Ref TObjectID="25952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148480">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 356.000000 -784.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25953" ObjectName="SW-YA_DXC.YA_DXC_39017SW"/>
     <cge:Meas_Ref ObjectId="148480"/>
    <cge:TPSR_Ref TObjectID="25953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1010.000000 -862.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25935" ObjectName="SW-YA_DXC.YA_DXC_31127SW"/>
     <cge:Meas_Ref ObjectId="148335"/>
    <cge:TPSR_Ref TObjectID="25935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148406">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 695.000000 -660.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25949" ObjectName="SW-YA_DXC.YA_DXC_3011SW"/>
     <cge:Meas_Ref ObjectId="148406"/>
    <cge:TPSR_Ref TObjectID="25949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 695.000000 -393.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25955" ObjectName="SW-YA_DXC.YA_DXC_4011SW"/>
     <cge:Meas_Ref ObjectId="148485"/>
    <cge:TPSR_Ref TObjectID="25955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148444">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 -683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25951" ObjectName="SW-YA_DXC.YA_DXC_3021SW"/>
     <cge:Meas_Ref ObjectId="148444"/>
    <cge:TPSR_Ref TObjectID="25951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148489">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 -387.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25957" ObjectName="SW-YA_DXC.YA_DXC_4021SW"/>
     <cge:Meas_Ref ObjectId="148489"/>
    <cge:TPSR_Ref TObjectID="25957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -404.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25958" ObjectName="SW-YA_DXC.YA_DXC_4901SW"/>
     <cge:Meas_Ref ObjectId="148491"/>
    <cge:TPSR_Ref TObjectID="25958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148496">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.142857 -308.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25960" ObjectName="SW-YA_DXC.YA_DXC_4121SW"/>
     <cge:Meas_Ref ObjectId="148496"/>
    <cge:TPSR_Ref TObjectID="25960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148497">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.142857 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25961" ObjectName="SW-YA_DXC.YA_DXC_4122SW"/>
     <cge:Meas_Ref ObjectId="148497"/>
    <cge:TPSR_Ref TObjectID="25961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.685714 -309.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25963" ObjectName="SW-YA_DXC.YA_DXC_4131SW"/>
     <cge:Meas_Ref ObjectId="148513"/>
    <cge:TPSR_Ref TObjectID="25963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148514">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.685714 -107.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25964" ObjectName="SW-YA_DXC.YA_DXC_4132SW"/>
     <cge:Meas_Ref ObjectId="148514"/>
    <cge:TPSR_Ref TObjectID="25964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148530">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.828571 -310.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25966" ObjectName="SW-YA_DXC.YA_DXC_4141SW"/>
     <cge:Meas_Ref ObjectId="148530"/>
    <cge:TPSR_Ref TObjectID="25966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148531">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.828571 -110.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25967" ObjectName="SW-YA_DXC.YA_DXC_4142SW"/>
     <cge:Meas_Ref ObjectId="148531"/>
    <cge:TPSR_Ref TObjectID="25967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148547">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.371429 -312.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25969" ObjectName="SW-YA_DXC.YA_DXC_4151SW"/>
     <cge:Meas_Ref ObjectId="148547"/>
    <cge:TPSR_Ref TObjectID="25969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148548">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.371429 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25970" ObjectName="SW-YA_DXC.YA_DXC_4152SW"/>
     <cge:Meas_Ref ObjectId="148548"/>
    <cge:TPSR_Ref TObjectID="25970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1353.514286 -314.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1353.514286 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1554.657143 -314.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1554.657143 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148564">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 379.000000 -305.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25972" ObjectName="SW-YA_DXC.YA_DXC_4111SW"/>
     <cge:Meas_Ref ObjectId="148564"/>
    <cge:TPSR_Ref TObjectID="25972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -920.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_DXC.YA_DXC_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-746 1792,-746 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25930" ObjectName="BS-YA_DXC.YA_DXC_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   <polyline fill="none" opacity="0" points="352,-746 1792,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_DXC.YA_DXC_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,-381 1869,-381 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25931" ObjectName="BS-YA_DXC.YA_DXC_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   <polyline fill="none" opacity="0" points="273,-381 1869,-381 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 -46.000000)" xlink:href="#capacitor:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YA_DXC.YA_DXC_Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36702"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 718.000000 -788.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 718.000000 -788.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25975" ObjectName="TF-YA_DXC.YA_DXC_Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1745.000000 -80.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1745.000000 -80.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 486.000000 67.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 486.000000 67.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_DXC.YA_DXC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36698"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1363.000000 -527.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1363.000000 -527.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25974" ObjectName="TF-YA_DXC.YA_DXC_2T"/>
    <cge:TPSR_Ref TObjectID="25974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_DXC.YA_DXC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36694"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 669.000000 -523.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 669.000000 -523.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25973" ObjectName="TF-YA_DXC.YA_DXC_1T"/>
    <cge:TPSR_Ref TObjectID="25973"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b736c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 -888.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd9d60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 -895.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b59010">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 -845.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b76820">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 440.000000 -848.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b48310">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1057.000000 -512.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c05f70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1077.000000 -450.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad3240">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 553.142857 -174.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf3370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 595.142857 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b2a9f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.685714 -173.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b2b6d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.685714 -45.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b2c400">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 988.828571 -48.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b80c50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 946.828571 -178.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b81910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.371429 -50.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b71930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1157.371429 -178.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b72680">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.514286 -180.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c20950">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1399.514286 -52.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa8bc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1558.657143 -180.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa98e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1600.657143 -52.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aaa610">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1782.000000 -250.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbbd80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.000000 -114.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b9ba90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 -171.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af2d90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1556.000000 -868.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad3920">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 523.000000 -103.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1adb700">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1062.000000 -972.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -69.000000 -994.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217886" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -813.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217886" ObjectName="YA_DXC:YA_DXC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219738" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -772.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219738" ObjectName="YA_DXC:YA_DXC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217886" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -46.000000 -894.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217886" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217886" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -854.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217886" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 -1074.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25943"/>
     <cge:Term_Ref ObjectID="36632"/>
    <cge:TPSR_Ref TObjectID="25943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 -1074.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25943"/>
     <cge:Term_Ref ObjectID="36632"/>
    <cge:TPSR_Ref TObjectID="25943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 -1074.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25943"/>
     <cge:Term_Ref ObjectID="36632"/>
    <cge:TPSR_Ref TObjectID="25943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -1074.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25933"/>
     <cge:Term_Ref ObjectID="36612"/>
    <cge:TPSR_Ref TObjectID="25933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -1074.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25933"/>
     <cge:Term_Ref ObjectID="36612"/>
    <cge:TPSR_Ref TObjectID="25933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -1074.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25933"/>
     <cge:Term_Ref ObjectID="36612"/>
    <cge:TPSR_Ref TObjectID="25933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1457.000000 -1074.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25938"/>
     <cge:Term_Ref ObjectID="36622"/>
    <cge:TPSR_Ref TObjectID="25938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1457.000000 -1074.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25938"/>
     <cge:Term_Ref ObjectID="36622"/>
    <cge:TPSR_Ref TObjectID="25938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1457.000000 -1074.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25938"/>
     <cge:Term_Ref ObjectID="36622"/>
    <cge:TPSR_Ref TObjectID="25938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 657.000000 -659.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25948"/>
     <cge:Term_Ref ObjectID="36642"/>
    <cge:TPSR_Ref TObjectID="25948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 657.000000 -659.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25948"/>
     <cge:Term_Ref ObjectID="36642"/>
    <cge:TPSR_Ref TObjectID="25948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 657.000000 -659.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25948"/>
     <cge:Term_Ref ObjectID="36642"/>
    <cge:TPSR_Ref TObjectID="25948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 659.000000 -479.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25954"/>
     <cge:Term_Ref ObjectID="36654"/>
    <cge:TPSR_Ref TObjectID="25954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 659.000000 -479.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25954"/>
     <cge:Term_Ref ObjectID="36654"/>
    <cge:TPSR_Ref TObjectID="25954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 659.000000 -479.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25954"/>
     <cge:Term_Ref ObjectID="36654"/>
    <cge:TPSR_Ref TObjectID="25954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -670.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25950"/>
     <cge:Term_Ref ObjectID="36646"/>
    <cge:TPSR_Ref TObjectID="25950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -670.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25950"/>
     <cge:Term_Ref ObjectID="36646"/>
    <cge:TPSR_Ref TObjectID="25950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -670.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25950"/>
     <cge:Term_Ref ObjectID="36646"/>
    <cge:TPSR_Ref TObjectID="25950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25956"/>
     <cge:Term_Ref ObjectID="36658"/>
    <cge:TPSR_Ref TObjectID="25956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25956"/>
     <cge:Term_Ref ObjectID="36658"/>
    <cge:TPSR_Ref TObjectID="25956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25956"/>
     <cge:Term_Ref ObjectID="36658"/>
    <cge:TPSR_Ref TObjectID="25956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148311" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 369.000000 -3.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148311" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25971"/>
     <cge:Term_Ref ObjectID="36688"/>
    <cge:TPSR_Ref TObjectID="25971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 369.000000 -3.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25971"/>
     <cge:Term_Ref ObjectID="36688"/>
    <cge:TPSR_Ref TObjectID="25971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 369.000000 -3.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25971"/>
     <cge:Term_Ref ObjectID="36688"/>
    <cge:TPSR_Ref TObjectID="25971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148291" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 1.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25959"/>
     <cge:Term_Ref ObjectID="36664"/>
    <cge:TPSR_Ref TObjectID="25959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148292" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 1.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25959"/>
     <cge:Term_Ref ObjectID="36664"/>
    <cge:TPSR_Ref TObjectID="25959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148289" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 1.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148289" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25959"/>
     <cge:Term_Ref ObjectID="36664"/>
    <cge:TPSR_Ref TObjectID="25959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -3.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25962"/>
     <cge:Term_Ref ObjectID="36670"/>
    <cge:TPSR_Ref TObjectID="25962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148297" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -3.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25962"/>
     <cge:Term_Ref ObjectID="36670"/>
    <cge:TPSR_Ref TObjectID="25962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -3.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25962"/>
     <cge:Term_Ref ObjectID="36670"/>
    <cge:TPSR_Ref TObjectID="25962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148301" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 -3.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25965"/>
     <cge:Term_Ref ObjectID="36676"/>
    <cge:TPSR_Ref TObjectID="25965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148302" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 -3.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148302" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25965"/>
     <cge:Term_Ref ObjectID="36676"/>
    <cge:TPSR_Ref TObjectID="25965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 -3.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25965"/>
     <cge:Term_Ref ObjectID="36676"/>
    <cge:TPSR_Ref TObjectID="25965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1150.000000 -4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25968"/>
     <cge:Term_Ref ObjectID="36682"/>
    <cge:TPSR_Ref TObjectID="25968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1150.000000 -4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25968"/>
     <cge:Term_Ref ObjectID="36682"/>
    <cge:TPSR_Ref TObjectID="25968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1150.000000 -4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25968"/>
     <cge:Term_Ref ObjectID="36682"/>
    <cge:TPSR_Ref TObjectID="25968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-148263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 -735.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-148264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 -735.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-148265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 -735.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-148266" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 -735.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-148284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -448.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-148285" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -448.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-148286" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -448.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-148287" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -448.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-148274" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 837.000000 -583.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148274" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25973"/>
     <cge:Term_Ref ObjectID="36692"/>
    <cge:TPSR_Ref TObjectID="25973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-148275" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 837.000000 -583.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25973"/>
     <cge:Term_Ref ObjectID="36692"/>
    <cge:TPSR_Ref TObjectID="25973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-148282" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1540.000000 -583.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25974"/>
     <cge:Term_Ref ObjectID="36699"/>
    <cge:TPSR_Ref TObjectID="25974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-148283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1540.000000 -583.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25974"/>
     <cge:Term_Ref ObjectID="36699"/>
    <cge:TPSR_Ref TObjectID="25974"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="108" y="-1024"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="108" y="-1024"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="108" y="-1063"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="108" y="-1063"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="645" y="-852"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="645" y="-852"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1061" y="-817"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1061" y="-817"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1477" y="-854"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1477" y="-854"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="397" y="-277"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="397" y="-277"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="568" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="568" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="763" y="-279"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="763" y="-279"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="961" y="-282"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="961" y="-282"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1172" y="-284"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1172" y="-284"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="84" x="-147" y="-631"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="84" x="-147" y="-631"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="538" y="-603"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="538" y="-603"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1254" y="-608"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1254" y="-608"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="108" y="-1024"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="108" y="-1063"/></g>
   <g href="35kV大新仓变35kV姚新线313断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="645" y="-852"/></g>
   <g href="35kV大新仓变35kV西大连线311断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1061" y="-817"/></g>
   <g href="35kV大新仓变35kV大仓南线312断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1477" y="-854"/></g>
   <g href="35kV大新仓变10kV电容器411断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="397" y="-277"/></g>
   <g href="35kV大新仓变10kV左门线412断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="568" y="-280"/></g>
   <g href="35kV大新仓变10kV姚安工业园区线413断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="763" y="-279"/></g>
   <g href="35kV大新仓变10kV东线414断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="961" y="-282"/></g>
   <g href="35kV大新仓变10kV光禄线415断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1172" y="-284"/></g>
   <g href="35kV大新仓变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="84" x="-147" y="-631"/></g>
   <g href="35kV大新仓变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="538" y="-603"/></g>
   <g href="35kV大新仓变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1254" y="-608"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1793,-494 1805,-502 1793,-510 " stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b751b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 418.000000 -928.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b48da0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1060.000000 -518.000000)" xlink:href="#voltageTransformer:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af13a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 -847.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af1fe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1591.000000 -876.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_DXC.412Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 553.142857 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34262" ObjectName="EC-YA_DXC.412Ld"/>
    <cge:TPSR_Ref TObjectID="34262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DXC.413Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.685714 -44.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34263" ObjectName="EC-YA_DXC.413Ld"/>
    <cge:TPSR_Ref TObjectID="34263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DXC.414Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 946.828571 -47.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34264" ObjectName="EC-YA_DXC.414Ld"/>
    <cge:TPSR_Ref TObjectID="34264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DXC.415Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1157.371429 -49.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34265" ObjectName="EC-YA_DXC.415Ld"/>
    <cge:TPSR_Ref TObjectID="34265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.514286 -51.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1558.657143 -51.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1c29620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1051,-746 1051,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25934@0" Pin0InfoVect0LinkObjId="SW-148334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1051,-746 1051,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1baf0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1051,-789 1051,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25934@1" ObjectIDZND0="25933@0" Pin0InfoVect0LinkObjId="SW-148332_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148334_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1051,-789 1051,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcfcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1051,-823 1051,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25933@1" ObjectIDZND0="25936@0" Pin0InfoVect0LinkObjId="SW-148336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1051,-823 1051,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b983d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1467,-746 1467,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25939@0" Pin0InfoVect0LinkObjId="SW-148358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1467,-746 1467,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b985c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1467,-823 1467,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25939@1" ObjectIDZND0="25938@0" Pin0InfoVect0LinkObjId="SW-148356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148358_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1467,-823 1467,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b987b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1467,-860 1467,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25938@1" ObjectIDZND0="25941@0" Pin0InfoVect0LinkObjId="SW-148360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1467,-860 1467,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b74380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-746 636,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25944@0" Pin0InfoVect0LinkObjId="SW-148382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-746 636,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b74570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-821 636,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25944@1" ObjectIDZND0="25943@0" Pin0InfoVect0LinkObjId="SW-148380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-821 636,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b74760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-858 636,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25943@1" ObjectIDZND0="25946@0" Pin0InfoVect0LinkObjId="SW-148384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-858 636,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bec320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,-942 718,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="25947@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148395_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="695,-942 718,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bda350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="748,-900 749,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1bd9d60@1" ObjectIDZND0="25975@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd9d60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="748,-900 749,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bda540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="718,-942 748,-942 748,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1b736c0@0" ObjectIDZND0="g_1bd9d60@0" Pin0InfoVect0LinkObjId="g_1bd9d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b736c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="718,-942 748,-942 748,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b58e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="365,-789 365,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25953@0" ObjectIDZND0="g_1b586b0@0" Pin0InfoVect0LinkObjId="g_1b586b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="365,-789 365,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b59600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-746 409,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25952@0" Pin0InfoVect0LinkObjId="SW-148479_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-746 409,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b597f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-881 409,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1b59010@0" ObjectIDZND0="g_1b751b0@0" Pin0InfoVect0LinkObjId="g_1b751b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b59010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-881 409,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b599e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="365,-825 409,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25953@1" ObjectIDZND0="25952@x" ObjectIDZND1="g_1b76820@0" ObjectIDZND2="g_1b59010@0" Pin0InfoVect0LinkObjId="SW-148479_0" Pin0InfoVect1LinkObjId="g_1b76820_0" Pin0InfoVect2LinkObjId="g_1b59010_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="365,-825 409,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b74fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-814 409,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25952@1" ObjectIDZND0="25953@x" ObjectIDZND1="g_1b76820@0" ObjectIDZND2="g_1b59010@0" Pin0InfoVect0LinkObjId="SW-148480_0" Pin0InfoVect1LinkObjId="g_1b76820_0" Pin0InfoVect2LinkObjId="g_1b59010_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148479_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="409,-814 409,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6ce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-835 448,-835 448,-854 447,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25952@x" ObjectIDND1="25953@x" ObjectIDND2="g_1b59010@0" ObjectIDZND0="g_1b76820@0" Pin0InfoVect0LinkObjId="g_1b76820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148479_0" Pin1InfoVect1LinkObjId="SW-148480_0" Pin1InfoVect2LinkObjId="g_1b59010_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-835 448,-835 448,-854 447,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6d880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-825 409,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25952@x" ObjectIDND1="25953@x" ObjectIDZND0="g_1b76820@0" ObjectIDZND1="g_1b59010@0" Pin0InfoVect0LinkObjId="g_1b76820_0" Pin0InfoVect1LinkObjId="g_1b59010_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-148479_0" Pin1InfoVect1LinkObjId="SW-148480_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="409,-825 409,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6daa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-835 409,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1b76820@0" ObjectIDND1="25952@x" ObjectIDND2="25953@x" ObjectIDZND0="g_1b59010@1" Pin0InfoVect0LinkObjId="g_1b59010_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b76820_0" Pin1InfoVect1LinkObjId="SW-148479_0" Pin1InfoVect2LinkObjId="SW-148480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-835 409,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="603,-942 636,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="25945@0" ObjectIDZND0="25946@x" ObjectIDZND1="25947@x" ObjectIDZND2="34312@1" Pin0InfoVect0LinkObjId="SW-148384_0" Pin0InfoVect1LinkObjId="SW-148395_0" Pin0InfoVect2LinkObjId="g_1b6e100_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="603,-942 636,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6dee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-903 636,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="25946@1" ObjectIDZND0="25945@x" ObjectIDZND1="25947@x" ObjectIDZND2="34312@1" Pin0InfoVect0LinkObjId="SW-148383_0" Pin0InfoVect1LinkObjId="SW-148395_0" Pin0InfoVect2LinkObjId="g_1b6e100_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="636,-903 636,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6e100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-942 636,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="25945@x" ObjectIDND1="25946@x" ObjectIDND2="25947@x" ObjectIDZND0="34312@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148383_0" Pin1InfoVect1LinkObjId="SW-148384_0" Pin1InfoVect2LinkObjId="SW-148395_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-942 636,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6e320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-942 659,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="25945@x" ObjectIDND1="25946@x" ObjectIDND2="34312@1" ObjectIDZND0="25947@0" Pin0InfoVect0LinkObjId="SW-148395_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148383_0" Pin1InfoVect1LinkObjId="SW-148384_0" Pin1InfoVect2LinkObjId="g_1b6e100_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-942 659,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c10c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1435,-942 1467,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="25940@0" ObjectIDZND0="25941@x" ObjectIDZND1="25942@x" ObjectIDZND2="34314@1" Pin0InfoVect0LinkObjId="SW-148360_0" Pin0InfoVect1LinkObjId="SW-148371_0" Pin0InfoVect2LinkObjId="g_1c11a70_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1435,-942 1467,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c11850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1467,-906 1467,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="25941@1" ObjectIDZND0="25940@x" ObjectIDZND1="25942@x" ObjectIDZND2="34314@1" Pin0InfoVect0LinkObjId="SW-148359_0" Pin0InfoVect1LinkObjId="SW-148371_0" Pin0InfoVect2LinkObjId="g_1c11a70_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1467,-906 1467,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c11a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1467,-942 1467,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="25940@x" ObjectIDND1="25941@x" ObjectIDND2="25942@x" ObjectIDZND0="34314@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148359_0" Pin1InfoVect1LinkObjId="SW-148360_0" Pin1InfoVect2LinkObjId="SW-148371_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1467,-942 1467,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c11c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1491,-942 1467,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="25942@0" ObjectIDZND0="25940@x" ObjectIDZND1="25941@x" ObjectIDZND2="34314@1" Pin0InfoVect0LinkObjId="SW-148359_0" Pin0InfoVect1LinkObjId="SW-148360_0" Pin0InfoVect2LinkObjId="g_1c11a70_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148371_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1491,-942 1467,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8a2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,-746 704,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25949@1" Pin0InfoVect0LinkObjId="SW-148406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="704,-746 704,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8a4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,-665 704,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25949@0" ObjectIDZND0="25948@1" Pin0InfoVect0LinkObjId="SW-148404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="704,-665 704,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8a710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,-625 704,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25948@0" ObjectIDZND0="25973@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="704,-625 704,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8a930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,-398 704,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25955@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1bd7a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="704,-398 704,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8ab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,-445 704,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25954@0" ObjectIDZND0="25955@1" Pin0InfoVect0LinkObjId="SW-148485_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148483_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="704,-445 704,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd6d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-746 1398,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25951@1" Pin0InfoVect0LinkObjId="SW-148444_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-746 1398,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd6f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-688 1398,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25951@0" ObjectIDZND0="25950@1" Pin0InfoVect0LinkObjId="SW-148442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-688 1398,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd7160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-637 1398,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25950@0" ObjectIDZND0="25974@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-637 1398,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd7380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-440 1398,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25956@0" ObjectIDZND0="25957@1" Pin0InfoVect0LinkObjId="SW-148489_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148487_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-440 1398,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd75a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-531 1398,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25974@0" ObjectIDZND0="25956@1" Pin0InfoVect0LinkObjId="SW-148487_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd7160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-531 1398,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd77e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,-527 704,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25973@0" ObjectIDZND0="25954@1" Pin0InfoVect0LinkObjId="SW-148483_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b8a710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="704,-527 704,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd7a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-392 1398,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25957@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148489_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-392 1398,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bf21b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-409 1052,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25958@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-409 1052,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c05d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-523 1052,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1b48da0@0" ObjectIDZND0="g_1b48310@0" Pin0InfoVect0LinkObjId="g_1b48310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b48da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-523 1052,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c06c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1084,-454 1052,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1c05f70@0" ObjectIDZND0="g_1b48310@0" ObjectIDZND1="25958@x" Pin0InfoVect0LinkObjId="g_1b48310_0" Pin0InfoVect1LinkObjId="SW-148491_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c05f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1084,-454 1052,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c076a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-462 1052,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1b48310@1" ObjectIDZND0="g_1c05f70@0" ObjectIDZND1="25958@x" Pin0InfoVect0LinkObjId="g_1c05f70_0" Pin0InfoVect1LinkObjId="SW-148491_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b48310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-462 1052,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bf5170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-454 1052,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c05f70@0" ObjectIDND1="g_1b48310@0" ObjectIDZND0="25958@1" Pin0InfoVect0LinkObjId="SW-148491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c05f70_0" Pin1InfoVect1LinkObjId="g_1b48310_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-454 1052,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b95d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-286 558,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25959@1" ObjectIDZND0="25960@0" Pin0InfoVect0LinkObjId="SW-148496_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148494_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-286 558,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b95fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-149 558,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25961@1" ObjectIDZND0="g_1ad3240@1" Pin0InfoVect0LinkObjId="g_1ad3240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148497_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-149 558,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b96210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-232 558,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1ad3240@0" ObjectIDZND0="25959@0" Pin0InfoVect0LinkObjId="SW-148494_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad3240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-232 558,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bf63b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="602,-100 558,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1bf3370@0" ObjectIDZND0="34262@x" ObjectIDZND1="25961@x" ObjectIDZND2="g_1ad3920@0" Pin0InfoVect0LinkObjId="EC-YA_DXC.412Ld_0" Pin0InfoVect1LinkObjId="SW-148497_0" Pin0InfoVect2LinkObjId="g_1ad3920_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf3370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="602,-100 558,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bf6610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-66 558,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="34262@0" ObjectIDZND0="g_1bf3370@0" ObjectIDZND1="25961@x" ObjectIDZND2="g_1ad3920@0" Pin0InfoVect0LinkObjId="g_1bf3370_0" Pin0InfoVect1LinkObjId="SW-148497_0" Pin0InfoVect2LinkObjId="g_1ad3920_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_DXC.412Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="558,-66 558,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1aa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-100 558,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="34262@x" ObjectIDND1="g_1bf3370@0" ObjectIDND2="g_1ad3920@0" ObjectIDZND0="25961@0" Pin0InfoVect0LinkObjId="SW-148497_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YA_DXC.412Ld_0" Pin1InfoVect1LinkObjId="g_1bf3370_0" Pin1InfoVect2LinkObjId="g_1ad3920_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-100 558,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcd500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-349 558,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25960@1" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148496_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-349 558,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b32b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-285 754,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25962@1" ObjectIDZND0="25963@0" Pin0InfoVect0LinkObjId="SW-148513_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-285 754,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b32db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-148 754,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25964@1" ObjectIDZND0="g_1b2a9f0@1" Pin0InfoVect0LinkObjId="g_1b2a9f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-148 754,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b33010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-231 754,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1b2a9f0@0" ObjectIDZND0="25962@0" Pin0InfoVect0LinkObjId="SW-148511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b2a9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-231 754,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b33ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-99 754,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1b2b6d0@0" ObjectIDZND0="34263@x" ObjectIDZND1="25964@x" Pin0InfoVect0LinkObjId="EC-YA_DXC.413Ld_0" Pin0InfoVect1LinkObjId="SW-148514_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b2b6d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="798,-99 754,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b34100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-65 754,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34263@0" ObjectIDZND0="g_1b2b6d0@0" ObjectIDZND1="25964@x" Pin0InfoVect0LinkObjId="g_1b2b6d0_0" Pin0InfoVect1LinkObjId="SW-148514_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_DXC.413Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="754,-65 754,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b34360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-99 754,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34263@x" ObjectIDND1="g_1b2b6d0@0" ObjectIDZND0="25964@0" Pin0InfoVect0LinkObjId="SW-148514_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_DXC.413Ld_0" Pin1InfoVect1LinkObjId="g_1b2b6d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-99 754,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2a340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-350 754,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25963@1" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-350 754,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9edf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-288 952,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25965@1" ObjectIDZND0="25966@0" Pin0InfoVect0LinkObjId="SW-148530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148528_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-288 952,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9f050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-151 952,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25967@1" ObjectIDZND0="g_1b80c50@1" Pin0InfoVect0LinkObjId="g_1b80c50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148531_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-151 952,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9f2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-236 952,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1b80c50@0" ObjectIDZND0="25965@0" Pin0InfoVect0LinkObjId="SW-148528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b80c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-236 952,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba0140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="996,-102 952,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1b2c400@0" ObjectIDZND0="34264@x" ObjectIDZND1="25967@x" Pin0InfoVect0LinkObjId="EC-YA_DXC.414Ld_0" Pin0InfoVect1LinkObjId="SW-148531_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b2c400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="996,-102 952,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba03a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-68 952,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34264@0" ObjectIDZND0="g_1b2c400@0" ObjectIDZND1="25967@x" Pin0InfoVect0LinkObjId="g_1b2c400_0" Pin0InfoVect1LinkObjId="SW-148531_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_DXC.414Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="952,-68 952,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba0600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-102 952,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34264@x" ObjectIDND1="g_1b2c400@0" ObjectIDZND0="25967@0" Pin0InfoVect0LinkObjId="SW-148531_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_DXC.414Ld_0" Pin1InfoVect1LinkObjId="g_1b2c400_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-102 952,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b7dc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-351 952,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25966@1" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-351 952,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b82640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-353 1162,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25969@1" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148547_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-353 1162,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b828a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-290 1162,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25968@1" ObjectIDZND0="25969@0" Pin0InfoVect0LinkObjId="SW-148547_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148545_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-290 1162,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b82b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-153 1162,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25970@1" ObjectIDZND0="g_1b71930@1" Pin0InfoVect0LinkObjId="g_1b71930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148548_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-153 1162,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b82d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-236 1162,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1b71930@0" ObjectIDZND0="25968@0" Pin0InfoVect0LinkObjId="SW-148545_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b71930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-236 1162,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab6ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-104 1162,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1b81910@0" ObjectIDZND0="34265@x" ObjectIDZND1="25970@x" Pin0InfoVect0LinkObjId="EC-YA_DXC.415Ld_0" Pin0InfoVect1LinkObjId="SW-148548_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b81910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1206,-104 1162,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab6d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-70 1162,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34265@0" ObjectIDZND0="g_1b81910@0" ObjectIDZND1="25970@x" Pin0InfoVect0LinkObjId="g_1b81910_0" Pin0InfoVect1LinkObjId="SW-148548_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_DXC.415Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-70 1162,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab6fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-104 1162,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34265@x" ObjectIDND1="g_1b81910@0" ObjectIDZND0="25970@0" Pin0InfoVect0LinkObjId="SW-148548_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_DXC.415Ld_0" Pin1InfoVect1LinkObjId="g_1b81910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-104 1162,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c21680">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1363,-355 1363,-381 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1363,-355 1363,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c218e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1363,-155 1363,-185 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1b72680@1" Pin0InfoVect0LinkObjId="g_1b72680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1363,-155 1363,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c22750">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1407,-106 1363,-106 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1c20950@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c20950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1407,-106 1363,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c229b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1363,-72 1363,-106 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1c20950@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1c20950_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1363,-72 1363,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c22c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1363,-106 1363,-119 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1c20950@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1c20950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1363,-106 1363,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c4c140">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1564,-292 1564,-319 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1564,-292 1564,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c4c3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1564,-155 1564,-185 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1aa8bc0@1" Pin0InfoVect0LinkObjId="g_1aa8bc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1564,-155 1564,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c4c600">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1564,-238 1564,-265 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1aa8bc0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa8bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1564,-238 1564,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c4d470">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1608,-106 1564,-106 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1aa98e0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa98e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-106 1564,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c4d6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1564,-72 1564,-106 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1aa98e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1aa98e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1564,-72 1564,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bc8920">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1564,-106 1564,-119 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1aa98e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1aa98e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1564,-106 1564,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b7acc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1564,-355 1564,-381 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1564,-355 1564,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a834f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,-200 1776,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1aaa610@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aaa610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1777,-200 1776,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a84c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,-245 1777,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_1aaa610@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aaa610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1777,-245 1777,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a84e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-283 388,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25971@1" ObjectIDZND0="25972@0" Pin0InfoVect0LinkObjId="SW-148564_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,-283 388,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a85060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-229 388,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1b9ba90@0" ObjectIDZND0="25971@0" Pin0InfoVect0LinkObjId="SW-148562_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b9ba90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,-229 388,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9a670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-168 341,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1b9ba90@0" ObjectIDZND0="g_1bbbd80@0" Pin0InfoVect0LinkObjId="g_1bbbd80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1b9ba90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,-168 341,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9a8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-162 388,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1bbbd80@0" ObjectIDZND1="g_1b9ba90@0" Pin0InfoVect0LinkObjId="g_1bbbd80_0" Pin0InfoVect1LinkObjId="g_1b9ba90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="388,-162 388,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9ab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-168 388,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1bbbd80@0" ObjectIDZND0="g_1b9ba90@1" Pin0InfoVect0LinkObjId="g_1b9ba90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1bbbd80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,-168 388,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-346 388,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25972@1" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_1b8a930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148564_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,-346 388,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1af3ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1563,-922 1563,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_1af2d90@0" ObjectIDZND0="25942@x" ObjectIDZND1="g_1af1fe0@0" Pin0InfoVect0LinkObjId="SW-148371_0" Pin0InfoVect1LinkObjId="g_1af1fe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1af2d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1563,-922 1563,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1af45b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1527,-942 1563,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="25942@1" ObjectIDZND0="g_1af2d90@0" ObjectIDZND1="g_1af1fe0@0" Pin0InfoVect0LinkObjId="g_1af2d90_0" Pin0InfoVect1LinkObjId="g_1af1fe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148371_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1527,-942 1563,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1af4810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1563,-942 1599,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1af2d90@0" ObjectIDND1="25942@x" ObjectIDZND0="g_1af1fe0@0" Pin0InfoVect0LinkObjId="g_1af1fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1af2d90_0" Pin1InfoVect1LinkObjId="SW-148371_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1563,-942 1599,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1afd280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-53 517,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1ad3920@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad3920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-53 517,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1afd4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-98 557,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1ad3920@0" ObjectIDZND0="34262@x" ObjectIDZND1="g_1bf3370@0" ObjectIDZND2="25961@x" Pin0InfoVect0LinkObjId="EC-YA_DXC.412Ld_0" Pin0InfoVect1LinkObjId="g_1bf3370_0" Pin0InfoVect2LinkObjId="SW-148497_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad3920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="518,-98 557,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1afe1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1363,-319 1363,-238 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1b72680@0" Pin0InfoVect0LinkObjId="g_1b72680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1363,-319 1363,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1adac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,-913 1091,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="25935@0" ObjectIDZND0="25937@0" Pin0InfoVect0LinkObjId="SW-148347_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1019,-913 1091,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1adae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1051,-925 1051,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="25936@1" Pin0InfoVect0LinkObjId="SW-148336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1051,-925 1051,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1adc470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-913 1180,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="25937@1" ObjectIDZND0="g_1af13a0@0" Pin0InfoVect0LinkObjId="g_1af13a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148347_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-913 1180,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1adc6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1066,-980 1051,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_1adb700@0" ObjectIDZND0="0@x" ObjectIDZND1="34317@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1add420_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1adb700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1066,-980 1051,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1add1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1051,-970 1051,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@1" ObjectIDZND0="g_1adb700@0" ObjectIDZND1="34317@1" Pin0InfoVect0LinkObjId="g_1adb700_0" Pin0InfoVect1LinkObjId="g_1add420_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1051,-970 1051,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1add420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1051,-980 1051,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="g_1adb700@0" ObjectIDND1="0@x" ObjectIDZND0="34317@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1adb700_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1051,-980 1051,-997 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-147909" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 109.500000 -926.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25866" ObjectName="DYN-YA_DXC"/>
     <cge:Meas_Ref ObjectId="147909"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba1d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.000000 719.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba2a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.000000 734.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba3100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 275.000000 691.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba3380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.000000 705.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba3a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 561.000000 1073.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba4cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 550.000000 1058.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba5560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 575.000000 1043.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba5ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 566.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba6aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 581.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba7410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 282.000000 432.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba76c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 282.000000 447.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba7900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 274.000000 404.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ba7b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 282.000000 418.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab00c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.000000 6.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab0380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 300.000000 -9.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab05c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 325.000000 -24.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab08f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 568.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab0b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 583.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab1ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 595.000000 660.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab1df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.000000 645.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab2000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 630.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab23f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.000000 478.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab26b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 587.000000 463.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab28f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 612.000000 448.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abe750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 983.000000 1075.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abec20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 972.000000 1060.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abee60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 997.000000 1045.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abf280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 1075.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abf540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1385.000000 1060.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abf780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1410.000000 1045.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abfba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.000000 671.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abfe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 656.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac00a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1301.000000 641.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac04c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1283.000000 478.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac0780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1272.000000 463.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac09c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1297.000000 448.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac0de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 550.000000 1.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac10a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 539.000000 -14.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac12e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 564.000000 -29.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac1700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 685.000000 2.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac19c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 674.000000 -13.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac1c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.000000 -28.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 882.000000 3.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac22e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 871.000000 -12.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac2520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 896.000000 -27.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac2940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1095.000000 5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac2c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1084.000000 -10.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac2e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 -25.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YA" endPointId="0" endStationName="YA_DXC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_YaoXin" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="636,-1019 636,-980 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34312" ObjectName="AC-35kV.LN_YaoXin"/>
    <cge:TPSR_Ref TObjectID="34312_SS-220"/></metadata>
   <polyline fill="none" opacity="0" points="636,-1019 636,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_DXC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xidalianTdxc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1051,-1016 1051,-993 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34317" ObjectName="AC-35kV.LN_xidalianTdxc"/>
    <cge:TPSR_Ref TObjectID="34317_SS-220"/></metadata>
   <polyline fill="none" opacity="0" points="1051,-1016 1051,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_DXC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dacangnanTdxc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1467,-1019 1467,-981 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34314" ObjectName="AC-35kV.LN_dacangnanTdxc"/>
    <cge:TPSR_Ref TObjectID="34314_SS-220"/></metadata>
   <polyline fill="none" opacity="0" points="1467,-1019 1467,-981 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="704" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="1398" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="1052" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="558" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="754" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="952" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="1162" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="1363" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="1564" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="1777" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="388" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="1051" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="1467" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="636" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="409" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="704" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="1398" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-148332">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 -788.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25933" ObjectName="SW-YA_DXC.YA_DXC_311BK"/>
     <cge:Meas_Ref ObjectId="148332"/>
    <cge:TPSR_Ref TObjectID="25933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148356">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.000000 -825.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25938" ObjectName="SW-YA_DXC.YA_DXC_312BK"/>
     <cge:Meas_Ref ObjectId="148356"/>
    <cge:TPSR_Ref TObjectID="25938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -823.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25943" ObjectName="SW-YA_DXC.YA_DXC_313BK"/>
     <cge:Meas_Ref ObjectId="148380"/>
    <cge:TPSR_Ref TObjectID="25943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 695.000000 -617.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25948" ObjectName="SW-YA_DXC.YA_DXC_301BK"/>
     <cge:Meas_Ref ObjectId="148404"/>
    <cge:TPSR_Ref TObjectID="25948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148483">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 695.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25954" ObjectName="SW-YA_DXC.YA_DXC_401BK"/>
     <cge:Meas_Ref ObjectId="148483"/>
    <cge:TPSR_Ref TObjectID="25954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148442">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 -629.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25950" ObjectName="SW-YA_DXC.YA_DXC_302BK"/>
     <cge:Meas_Ref ObjectId="148442"/>
    <cge:TPSR_Ref TObjectID="25950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148487">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25956" ObjectName="SW-YA_DXC.YA_DXC_402BK"/>
     <cge:Meas_Ref ObjectId="148487"/>
    <cge:TPSR_Ref TObjectID="25956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148494">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.142857 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25959" ObjectName="SW-YA_DXC.YA_DXC_412BK"/>
     <cge:Meas_Ref ObjectId="148494"/>
    <cge:TPSR_Ref TObjectID="25959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.685714 -250.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25962" ObjectName="SW-YA_DXC.YA_DXC_413BK"/>
     <cge:Meas_Ref ObjectId="148511"/>
    <cge:TPSR_Ref TObjectID="25962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148528">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.828571 -253.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25965" ObjectName="SW-YA_DXC.YA_DXC_414BK"/>
     <cge:Meas_Ref ObjectId="148528"/>
    <cge:TPSR_Ref TObjectID="25965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148545">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.371429 -255.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25968" ObjectName="SW-YA_DXC.YA_DXC_415BK"/>
     <cge:Meas_Ref ObjectId="148545"/>
    <cge:TPSR_Ref TObjectID="25968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1554.657143 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148562">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 379.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25971" ObjectName="SW-YA_DXC.YA_DXC_411BK"/>
     <cge:Meas_Ref ObjectId="148562"/>
    <cge:TPSR_Ref TObjectID="25971"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1a741f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -16.000000 -1033.500000) translate(0,16)">大新仓变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2ed10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1005.000000 -1097.000000) translate(0,12)">35kV西大连线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b98120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1422.000000 -1099.000000) translate(0,12)">35kV大仓南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b74050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -1099.000000) translate(0,12)">35kV姚新线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf53d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -586.000000) translate(0,12)">SZ9-4000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf53d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -586.000000) translate(0,27)">35±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf53d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -586.000000) translate(0,42)">y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf53d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -586.000000) translate(0,57)">Ud=7.68%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf5a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -587.000000) translate(0,12)">SZ9-4000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf5a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -587.000000) translate(0,27)">35±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf5a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -587.000000) translate(0,42)">y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf5a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -587.000000) translate(0,57)">Ud=7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bf5c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bd4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bd4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bd4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bd4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bd4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bd4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bd4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bd4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bd4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bccf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.142857 -37.400000) translate(0,12)">左门线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c036d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 716.285714 -29.200000) translate(0,12)">姚安工业园区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b7de90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.428571 -30.800000) translate(0,12)">东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b8f250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1138.571429 -26.000000) translate(0,12)">光禄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bc3b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.714286 -28.000000) translate(0,12)">出线五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b7a4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.857143 -27.000000) translate(0,12)">出线六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a83750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1737.000000 -68.600000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a83750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1737.000000 -68.600000) translate(0,27)">SH15-50/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9ad90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 354.000000 -28.000000) translate(0,12)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bac510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 270.000000 -371.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b63a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 395.000000 -335.000000) translate(0,12)">4111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b63c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 397.000000 -277.000000) translate(0,12)">411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b63ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -340.000000) translate(0,12)">4141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b640e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -140.000000) translate(0,12)">4142</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -282.000000) translate(0,12)">414</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -342.000000) translate(0,12)">4151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b647a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1172.000000 -284.000000) translate(0,12)">415</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b649e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -142.000000) translate(0,12)">4152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -434.000000) translate(0,12)">4901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -137.000000) translate(0,12)">4132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b650a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -339.000000) translate(0,12)">4131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b652e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 763.000000 -279.000000) translate(0,12)">413</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b65520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -338.000000) translate(0,12)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b65760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -138.000000) translate(0,12)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b659a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -280.000000) translate(0,12)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b65be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -422.000000) translate(0,12)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b65e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 714.000000 -465.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b66060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -690.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b662a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 714.000000 -646.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b664e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -417.000000) translate(0,12)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b66720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -461.000000) translate(0,12)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b66960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -713.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b66ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -658.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b66de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 278.000000 -754.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b67020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1377.000000 -930.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b67260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -812.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b674a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -895.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b676e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.000000 -968.000000) translate(0,12)">3123</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b67920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1477.000000 -854.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b67b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 306.000000 -813.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b67da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 417.000000 -803.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b67fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -778.000000) translate(0,12)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -899.000000) translate(0,12)">31127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -858.000000) translate(0,12)">3112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b686a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -939.000000) translate(0,12)">3113</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b688e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -817.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -810.000000) translate(0,12)">3131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 545.000000 -928.000000) translate(0,12)">31327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -892.000000) translate(0,12)">3132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b691e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -968.000000) translate(0,12)">3133</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b69420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -852.000000) translate(0,12)">313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b69660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 768.000000 -846.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b69660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 768.000000 -846.000000) translate(0,27)">S9-50/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1aee720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -1017.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1af0a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -1054.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1af63b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -147.000000 -631.000000) translate(0,17)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="24" graphid="g_1af8ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 920.000000 -608.000000) translate(0,20)">1、2号主变档位联调</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1afd740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 478.000000 60.400000) translate(0,12)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1afdd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -345.000000) translate(0,12)">4161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1afdfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -143.000000) translate(0,12)">4162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1afe3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1577.000000 -347.000000) translate(0,12)">4171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1afe690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1577.000000 -290.000000) translate(0,12)">417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1afe8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1568.000000 -142.000000) translate(0,12)">4172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,64,64)" font-family="SimSun" font-size="15" graphid="g_1b02460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -397.000000) translate(0,12)">401、402、413、414、415断路器弹簧未储能信号主站侧已取反，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,64,64)" font-family="SimSun" font-size="15" graphid="g_1b02460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.000000 -397.000000) translate(0,27)">待现场有自动化工作时核实处理。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac4a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -493.000000) translate(0,12)">至10kV左门线站外1+1号杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad4d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1011.000000 -958.000000) translate(0,12)">3101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1ad5180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1123.000000 -834.000000) translate(0,8)">RW5-35/200-30A,31.5kA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1add7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -197.000000 -63.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b958d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -73.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b958d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -73.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b1a440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -103.000000) translate(0,17)">5817032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae4300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 538.000000 -603.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae4a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -608.000000) translate(0,12)">2号主变</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b586b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.000000 -755.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YA_DXC"/>
</svg>