<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-152" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="27 -1266 2038 1283">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_1fe3380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="21" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="44" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="50" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="59" x2="59" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="62" x2="62" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="65" x2="65" y1="6" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="voltageTransformer:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="35" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="18" x2="29" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.22074" x1="18" x2="29" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="41" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="34" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="21" x2="25" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="20" x2="27" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="18" x2="29" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="41" x2="41" y1="40" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.239135" x1="34" x2="34" y1="40" y2="51"/>
    <circle cx="24" cy="26" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="24" cy="10" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="10" cy="18" fillStyle="0" r="9" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198e740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_198fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19902c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19912d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1991ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1992a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19934c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1993d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_19975e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19991b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1999da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_199ab60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199c520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199d410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19a0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a0e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a4100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19a4d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19b3590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19b3dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a8560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1293" width="2048" x="22" y="-1271"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20dab30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 930.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_169e920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 945.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_169ed60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 960.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b6e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.000000 915.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b7060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 386.000000 900.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c7f0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 308.000000 48.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16da120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 33.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c689c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 18.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_eb2bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1787.000000 511.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_eb2e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1787.000000 526.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2180530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1787.000000 541.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2180770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 496.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e7750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1795.000000 481.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e7a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 254.000000 507.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c1020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 254.000000 522.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c1260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 254.000000 537.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c14a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 492.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be2060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 262.000000 477.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f88560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1483.000000 821.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c540b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1472.000000 806.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bf270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1497.000000 791.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bf5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1453.000000 549.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ecef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1442.000000 534.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ecf1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 519.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ecf3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 776.000000 554.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_feeb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 539.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_feed90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 524.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac75c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 804.000000 820.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac7820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 805.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac7a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.000000 790.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14eec90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 737.000000 1005.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14eef00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 990.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14ef110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.000000 975.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="951,-144 942,-132 933,-144 951,-144 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="28" y="-1230"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(255,255,255)" stroke-width="1" width="12" x="645" y="-667"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(255,255,255)" stroke-width="1" width="12" x="1319" y="-665"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-104660">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20626" ObjectName="SW-CX_WJ.CX_WJ_2511SW"/>
     <cge:Meas_Ref ObjectId="104660"/>
    <cge:TPSR_Ref TObjectID="20626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104661">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 -1022.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20627" ObjectName="SW-CX_WJ.CX_WJ_2516SW"/>
     <cge:Meas_Ref ObjectId="104661"/>
    <cge:TPSR_Ref TObjectID="20627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104663">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20629" ObjectName="SW-CX_WJ.CX_WJ_25160SW"/>
     <cge:Meas_Ref ObjectId="104663"/>
    <cge:TPSR_Ref TObjectID="20629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104664">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -1072.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20630" ObjectName="SW-CX_WJ.CX_WJ_25167SW"/>
     <cge:Meas_Ref ObjectId="104664"/>
    <cge:TPSR_Ref TObjectID="20630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104677">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1111.000000 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20643" ObjectName="SW-CX_WJ.CX_WJ_2901SW"/>
     <cge:Meas_Ref ObjectId="104677"/>
    <cge:TPSR_Ref TObjectID="20643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104679">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.000000 -1004.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20645" ObjectName="SW-CX_WJ.CX_WJ_29017SW"/>
     <cge:Meas_Ref ObjectId="104679"/>
    <cge:TPSR_Ref TObjectID="20645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104666">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.565184 -827.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20632" ObjectName="SW-CX_WJ.CX_WJ_2011SW"/>
     <cge:Meas_Ref ObjectId="104666"/>
    <cge:TPSR_Ref TObjectID="20632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104667">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.565184 -723.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20633" ObjectName="SW-CX_WJ.CX_WJ_2016SW"/>
     <cge:Meas_Ref ObjectId="104667"/>
    <cge:TPSR_Ref TObjectID="20633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104668">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.565184 -818.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20634" ObjectName="SW-CX_WJ.CX_WJ_20117SW"/>
     <cge:Meas_Ref ObjectId="104668"/>
    <cge:TPSR_Ref TObjectID="20634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104669">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.565184 -770.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20635" ObjectName="SW-CX_WJ.CX_WJ_20160SW"/>
     <cge:Meas_Ref ObjectId="104669"/>
    <cge:TPSR_Ref TObjectID="20635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104670">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.565184 -712.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20636" ObjectName="SW-CX_WJ.CX_WJ_20167SW"/>
     <cge:Meas_Ref ObjectId="104670"/>
    <cge:TPSR_Ref TObjectID="20636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104672">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1384.000000 -826.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20638" ObjectName="SW-CX_WJ.CX_WJ_2021SW"/>
     <cge:Meas_Ref ObjectId="104672"/>
    <cge:TPSR_Ref TObjectID="20638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104673">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1384.000000 -722.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20639" ObjectName="SW-CX_WJ.CX_WJ_2026SW"/>
     <cge:Meas_Ref ObjectId="104673"/>
    <cge:TPSR_Ref TObjectID="20639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104674">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1400.000000 -817.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20640" ObjectName="SW-CX_WJ.CX_WJ_20217SW"/>
     <cge:Meas_Ref ObjectId="104674"/>
    <cge:TPSR_Ref TObjectID="20640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104675">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1400.000000 -769.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20641" ObjectName="SW-CX_WJ.CX_WJ_20260SW"/>
     <cge:Meas_Ref ObjectId="104675"/>
    <cge:TPSR_Ref TObjectID="20641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104676">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1400.000000 -711.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20642" ObjectName="SW-CX_WJ.CX_WJ_20267SW"/>
     <cge:Meas_Ref ObjectId="104676"/>
    <cge:TPSR_Ref TObjectID="20642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104685">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 403.176563 -280.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20651" ObjectName="SW-CX_WJ.CX_WJ_35167SW"/>
     <cge:Meas_Ref ObjectId="104685"/>
    <cge:TPSR_Ref TObjectID="20651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104678">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 -914.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20644" ObjectName="SW-CX_WJ.CX_WJ_29010SW"/>
     <cge:Meas_Ref ObjectId="104678"/>
    <cge:TPSR_Ref TObjectID="20644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104662">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20628" ObjectName="SW-CX_WJ.CX_WJ_25117SW"/>
     <cge:Meas_Ref ObjectId="104662"/>
    <cge:TPSR_Ref TObjectID="20628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104681">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20647" ObjectName="SW-CX_WJ.CX_WJ_2010SW"/>
     <cge:Meas_Ref ObjectId="104681"/>
    <cge:TPSR_Ref TObjectID="20647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104683">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1296.000000 -631.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20649" ObjectName="SW-CX_WJ.CX_WJ_2020SW"/>
     <cge:Meas_Ref ObjectId="104683"/>
    <cge:TPSR_Ref TObjectID="20649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104711">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 430.000000 -474.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20679" ObjectName="SW-CX_WJ.CX_WJ_3903SW"/>
     <cge:Meas_Ref ObjectId="104711"/>
    <cge:TPSR_Ref TObjectID="20679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104712">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1626.000000 -472.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20681" ObjectName="SW-CX_WJ.CX_WJ_3904SW"/>
     <cge:Meas_Ref ObjectId="104712"/>
    <cge:TPSR_Ref TObjectID="20681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104687">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 517.489816 -280.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20653" ObjectName="SW-CX_WJ.CX_WJ_35267SW"/>
     <cge:Meas_Ref ObjectId="104687"/>
    <cge:TPSR_Ref TObjectID="20653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104689">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.574153 -280.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20655" ObjectName="SW-CX_WJ.CX_WJ_35367SW"/>
     <cge:Meas_Ref ObjectId="104689"/>
    <cge:TPSR_Ref TObjectID="20655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104691">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.393431 -280.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20657" ObjectName="SW-CX_WJ.CX_WJ_35467SW"/>
     <cge:Meas_Ref ObjectId="104691"/>
    <cge:TPSR_Ref TObjectID="20657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104693">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.297045 -280.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20659" ObjectName="SW-CX_WJ.CX_WJ_35567SW"/>
     <cge:Meas_Ref ObjectId="104693"/>
    <cge:TPSR_Ref TObjectID="20659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104697">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.140419 -236.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20663" ObjectName="SW-CX_WJ.CX_WJ_35767SW"/>
     <cge:Meas_Ref ObjectId="104697"/>
    <cge:TPSR_Ref TObjectID="20663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104699">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.477768 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20665" ObjectName="SW-CX_WJ.CX_WJ_36167SW"/>
     <cge:Meas_Ref ObjectId="104699"/>
    <cge:TPSR_Ref TObjectID="20665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104701">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1607.477768 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20667" ObjectName="SW-CX_WJ.CX_WJ_36267SW"/>
     <cge:Meas_Ref ObjectId="104701"/>
    <cge:TPSR_Ref TObjectID="20667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104703">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1715.477768 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20669" ObjectName="SW-CX_WJ.CX_WJ_36367SW"/>
     <cge:Meas_Ref ObjectId="104703"/>
    <cge:TPSR_Ref TObjectID="20669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104705">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1812.477768 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20671" ObjectName="SW-CX_WJ.CX_WJ_36467SW"/>
     <cge:Meas_Ref ObjectId="104705"/>
    <cge:TPSR_Ref TObjectID="20671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104707">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1915.477768 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20673" ObjectName="SW-CX_WJ.CX_WJ_36567SW"/>
     <cge:Meas_Ref ObjectId="104707"/>
    <cge:TPSR_Ref TObjectID="20673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104709">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2006.477768 -276.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20675" ObjectName="SW-CX_WJ.CX_WJ_36667SW"/>
     <cge:Meas_Ref ObjectId="104709"/>
    <cge:TPSR_Ref TObjectID="20675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104695">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.809814 -280.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20661" ObjectName="SW-CX_WJ.CX_WJ_35667SW"/>
     <cge:Meas_Ref ObjectId="104695"/>
    <cge:TPSR_Ref TObjectID="20661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104713">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1333.024096 -338.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20677" ObjectName="SW-CX_WJ.CX_WJ_3344SW"/>
     <cge:Meas_Ref ObjectId="104713"/>
    <cge:TPSR_Ref TObjectID="20677"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_WJ.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 492.000000 -88.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43391" ObjectName="SM-CX_WJ.P1"/>
    <cge:TPSR_Ref TObjectID="43391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_WJ.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 589.000000 -88.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43392" ObjectName="SM-CX_WJ.P2"/>
    <cge:TPSR_Ref TObjectID="43392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_WJ.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 687.000000 -88.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43393" ObjectName="SM-CX_WJ.P3"/>
    <cge:TPSR_Ref TObjectID="43393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_WJ.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 796.000000 -88.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43394" ObjectName="SM-CX_WJ.P4"/>
    <cge:TPSR_Ref TObjectID="43394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_WJ.P5">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1582.000000 -84.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43395" ObjectName="SM-CX_WJ.P5"/>
    <cge:TPSR_Ref TObjectID="43395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_WJ.P6">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1690.000000 -84.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43396" ObjectName="SM-CX_WJ.P6"/>
    <cge:TPSR_Ref TObjectID="43396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_WJ.P7">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1787.000000 -84.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43397" ObjectName="SM-CX_WJ.P7"/>
    <cge:TPSR_Ref TObjectID="43397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_WJ.P8">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1890.000000 -84.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43398" ObjectName="SM-CX_WJ.P8"/>
    <cge:TPSR_Ref TObjectID="43398"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HTP" endPointId="0" endStationName="CX_WJ" flowDrawDirect="1" flowShape="0" id="AC-220kV.hongwu_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="674,-1201 674,-1233 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34025" ObjectName="AC-220kV.hongwu_line"/>
    <cge:TPSR_Ref TObjectID="34025_SS-152"/></metadata>
   <polyline fill="none" opacity="0" points="674,-1201 674,-1233 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_f0cc30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 -1071.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_155f5a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 -1006.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c82850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 601.000000 -943.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21afa90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -913.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cf7c70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -1003.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c4f130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.565184 -817.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_192ca40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.565184 -769.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17428a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.565184 -711.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_eb6380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.000000 -816.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15408b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.000000 -768.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd8930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.000000 -710.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2226410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.176563 -257.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21396f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -608.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f1830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1299.000000 -606.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1870d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.489816 -257.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1685da0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 616.574153 -257.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18d1e60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.393431 -257.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_272ac20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.297045 -257.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2136340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1152.140419 -213.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20dd840" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1502.477768 -253.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ff290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1610.477768 -253.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ba280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1718.477768 -253.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1928010" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1815.477768 -253.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23760a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1918.477768 -253.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242a530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2009.477768 -253.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2462f00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 964.809814 -257.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_192a400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="627,-949 619,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20628@0" ObjectIDZND0="g_1c82850@0" Pin0InfoVect0LinkObjId="g_1c82850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="627,-949 619,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a6ac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="627,-1012 620,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20629@0" ObjectIDZND0="g_155f5a0@0" Pin0InfoVect0LinkObjId="g_155f5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104663_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="627,-1012 620,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b47060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-1077 664,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20627@x" ObjectIDND1="g_11e8560@0" ObjectIDND2="g_217a6b0@0" ObjectIDZND0="20630@1" Pin0InfoVect0LinkObjId="SW-104664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104661_0" Pin1InfoVect1LinkObjId="g_11e8560_0" Pin1InfoVect2LinkObjId="g_217a6b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-1077 664,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_11ee600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="627,-1077 620,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20630@0" ObjectIDZND0="g_f0cc30@0" Pin0InfoVect0LinkObjId="g_f0cc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="627,-1077 620,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_fa3e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-1077 674,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20630@x" ObjectIDND1="g_11e8560@0" ObjectIDND2="g_217a6b0@0" ObjectIDZND0="20627@1" Pin0InfoVect0LinkObjId="SW-104661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104664_0" Pin1InfoVect1LinkObjId="g_11e8560_0" Pin1InfoVect2LinkObjId="g_217a6b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-1077 674,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_18a1f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-1012 664,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20627@x" ObjectIDND1="20625@x" ObjectIDZND0="20629@1" Pin0InfoVect0LinkObjId="SW-104663_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104661_0" Pin1InfoVect1LinkObjId="SW-104659_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-1012 664,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_155e350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="664,-1163 674,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_11e8560@0" ObjectIDZND0="20627@x" ObjectIDZND1="20630@x" ObjectIDZND2="g_217a6b0@0" Pin0InfoVect0LinkObjId="SW-104661_0" Pin0InfoVect1LinkObjId="SW-104664_0" Pin0InfoVect2LinkObjId="g_217a6b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11e8560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="664,-1163 674,-1163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1d73fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,-1170 704,-1181 674,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_217a6b0@0" ObjectIDZND0="20627@x" ObjectIDZND1="20630@x" ObjectIDZND2="g_11e8560@0" Pin0InfoVect0LinkObjId="SW-104661_0" Pin0InfoVect1LinkObjId="SW-104664_0" Pin0InfoVect2LinkObjId="g_11e8560_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_217a6b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="704,-1170 704,-1181 674,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_191a140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-1163 674,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_11e8560@0" ObjectIDND1="g_217a6b0@0" ObjectIDND2="34025@1" ObjectIDZND0="20627@x" ObjectIDZND1="20630@x" Pin0InfoVect0LinkObjId="SW-104661_0" Pin0InfoVect1LinkObjId="SW-104664_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11e8560_0" Pin1InfoVect1LinkObjId="g_217a6b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="674,-1163 674,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f867b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-919 1120,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="20643@x" ObjectIDND1="20644@x" ObjectIDZND0="20686@0" Pin0InfoVect0LinkObjId="g_1736260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104677_0" Pin1InfoVect1LinkObjId="SW-104678_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-919 1120,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1736260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-947 1120,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="20643@0" ObjectIDZND0="20686@0" ObjectIDZND1="20644@x" Pin0InfoVect0LinkObjId="g_1f867b0_0" Pin0InfoVect1LinkObjId="SW-104678_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-947 1120,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1d92ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-1009 1064,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20645@0" ObjectIDZND0="g_1cf7c70@0" Pin0InfoVect0LinkObjId="g_1cf7c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-1009 1064,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f67470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-879 719,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20686@0" ObjectIDZND0="20632@1" Pin0InfoVect0LinkObjId="SW-104666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f867b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-879 719,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1bf1cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-823 767,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c4f130@0" ObjectIDZND0="20634@1" Pin0InfoVect0LinkObjId="SW-104668_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c4f130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-823 767,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_ed05e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-775 767,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_192ca40@0" ObjectIDZND0="20635@1" Pin0InfoVect0LinkObjId="SW-104669_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_192ca40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-775 767,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_11f0fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-775 719,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20635@0" ObjectIDZND0="20633@x" ObjectIDZND1="20631@x" Pin0InfoVect0LinkObjId="SW-104667_0" Pin0InfoVect1LinkObjId="SW-104665_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104669_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="731,-775 719,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1c3f9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-717 767,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_17428a0@0" ObjectIDZND0="20636@1" Pin0InfoVect0LinkObjId="SW-104670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17428a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-717 767,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ac92b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-832 719,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20632@0" ObjectIDZND0="20631@x" ObjectIDZND1="20634@x" Pin0InfoVect0LinkObjId="SW-104665_0" Pin0InfoVect1LinkObjId="SW-104668_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-832 719,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_fc8510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-823 731,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20631@x" ObjectIDND1="20632@x" ObjectIDZND0="20634@0" Pin0InfoVect0LinkObjId="SW-104668_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104665_0" Pin1InfoVect1LinkObjId="SW-104666_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-823 731,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fb6cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-823 719,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20634@x" ObjectIDND1="20632@x" ObjectIDZND0="20631@1" Pin0InfoVect0LinkObjId="SW-104665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104668_0" Pin1InfoVect1LinkObjId="SW-104666_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-823 719,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_f09500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-785 719,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20631@0" ObjectIDZND0="20633@x" ObjectIDZND1="20635@x" Pin0InfoVect0LinkObjId="SW-104667_0" Pin0InfoVect1LinkObjId="SW-104669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-785 719,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_14fe8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-764 719,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20633@1" ObjectIDZND0="20631@x" ObjectIDZND1="20635@x" Pin0InfoVect0LinkObjId="SW-104665_0" Pin0InfoVect1LinkObjId="SW-104669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104667_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-764 719,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ca93d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-879 1393,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20686@0" ObjectIDZND0="20638@1" Pin0InfoVect0LinkObjId="SW-104672_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f867b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-879 1393,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f9d640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1449,-822 1441,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_eb6380@0" ObjectIDZND0="20640@1" Pin0InfoVect0LinkObjId="SW-104674_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_eb6380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1449,-822 1441,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1c82b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1449,-774 1441,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_15408b0@0" ObjectIDZND0="20641@1" Pin0InfoVect0LinkObjId="SW-104675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15408b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1449,-774 1441,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20c0420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1405,-774 1393,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20641@0" ObjectIDZND0="20637@x" ObjectIDZND1="20639@x" Pin0InfoVect0LinkObjId="SW-104671_0" Pin0InfoVect1LinkObjId="SW-104673_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-774 1393,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_fee570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1449,-716 1441,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fd8930@0" ObjectIDZND0="20642@1" Pin0InfoVect0LinkObjId="SW-104676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd8930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1449,-716 1441,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2185d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1405,-716 1393,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="20642@0" ObjectIDZND0="20639@x" ObjectIDZND1="20684@x" Pin0InfoVect0LinkObjId="SW-104673_0" Pin0InfoVect1LinkObjId="g_21793b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-716 1393,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f57ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-831 1393,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20638@0" ObjectIDZND0="20640@x" ObjectIDZND1="20637@x" Pin0InfoVect0LinkObjId="SW-104674_0" Pin0InfoVect1LinkObjId="SW-104671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-831 1393,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1bedfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-822 1405,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20638@x" ObjectIDND1="20637@x" ObjectIDZND0="20640@0" Pin0InfoVect0LinkObjId="SW-104674_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104672_0" Pin1InfoVect1LinkObjId="SW-104671_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-822 1405,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1962870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-822 1393,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20638@x" ObjectIDND1="20640@x" ObjectIDZND0="20637@1" Pin0InfoVect0LinkObjId="SW-104671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104672_0" Pin1InfoVect1LinkObjId="SW-104674_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-822 1393,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1065a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-784 1393,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20637@0" ObjectIDZND0="20641@x" ObjectIDZND1="20639@x" Pin0InfoVect0LinkObjId="SW-104675_0" Pin0InfoVect1LinkObjId="SW-104673_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-784 1393,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_18fcb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-727 1393,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="20639@0" ObjectIDZND0="20642@x" ObjectIDZND1="20684@x" Pin0InfoVect0LinkObjId="SW-104676_0" Pin0InfoVect1LinkObjId="g_21793b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-727 1393,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2120680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-763 1393,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20639@1" ObjectIDZND0="20641@x" ObjectIDZND1="20637@x" Pin0InfoVect0LinkObjId="SW-104675_0" Pin0InfoVect1LinkObjId="SW-104671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104673_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-763 1393,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a54780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-898 674,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20626@0" ObjectIDZND0="20686@0" Pin0InfoVect0LinkObjId="g_1f867b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-898 674,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_22301c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="664,-949 674,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20628@1" ObjectIDZND0="20625@x" ObjectIDZND1="20626@x" Pin0InfoVect0LinkObjId="SW-104659_0" Pin0InfoVect1LinkObjId="SW-104660_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="664,-949 674,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_189ece0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-970 674,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20625@0" ObjectIDZND0="20626@x" ObjectIDZND1="20628@x" Pin0InfoVect0LinkObjId="SW-104660_0" Pin0InfoVect1LinkObjId="SW-104662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="674,-970 674,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_f3f500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-949 674,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20625@x" ObjectIDND1="20628@x" ObjectIDZND0="20626@1" Pin0InfoVect0LinkObjId="SW-104660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104659_0" Pin1InfoVect1LinkObjId="SW-104662_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-949 674,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_ec0fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-1030 674,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20627@0" ObjectIDZND0="20629@x" ObjectIDZND1="20625@x" Pin0InfoVect0LinkObjId="SW-104663_0" Pin0InfoVect1LinkObjId="SW-104659_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="674,-1030 674,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f968e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-1012 674,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20629@x" ObjectIDND1="20627@x" ObjectIDZND0="20625@1" Pin0InfoVect0LinkObjId="SW-104659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104663_0" Pin1InfoVect1LinkObjId="SW-104661_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-1012 674,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_16d4920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-1009 1120,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="20645@1" ObjectIDZND0="20643@x" ObjectIDZND1="g_1d532d0@0" ObjectIDZND2="g_1a78d10@0" Pin0InfoVect0LinkObjId="SW-104677_0" Pin0InfoVect1LinkObjId="g_1d532d0_0" Pin0InfoVect2LinkObjId="g_1a78d10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104679_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-1009 1120,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1035770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-983 1120,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="20643@1" ObjectIDZND0="20645@x" ObjectIDZND1="g_1d532d0@0" ObjectIDZND2="g_1a78d10@0" Pin0InfoVect0LinkObjId="SW-104679_0" Pin0InfoVect1LinkObjId="g_1d532d0_0" Pin0InfoVect2LinkObjId="g_1a78d10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-983 1120,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fdcad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-1009 1120,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="20645@x" ObjectIDND1="20643@x" ObjectIDND2="g_1a78d10@0" ObjectIDZND0="g_1d532d0@0" Pin0InfoVect0LinkObjId="g_1d532d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104679_0" Pin1InfoVect1LinkObjId="SW-104677_0" Pin1InfoVect2LinkObjId="g_1a78d10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-1009 1120,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1012940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-1009 1155,-1009 1155,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="20645@x" ObjectIDND1="20643@x" ObjectIDND2="g_1d532d0@0" ObjectIDZND0="g_1a78d10@0" Pin0InfoVect0LinkObjId="g_1a78d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104679_0" Pin1InfoVect1LinkObjId="SW-104677_0" Pin1InfoVect2LinkObjId="g_1d532d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-1009 1155,-1009 1155,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe3120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="412,-285 412,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20651@0" ObjectIDZND0="g_2226410@0" Pin0InfoVect0LinkObjId="g_2226410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104685_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="412,-285 412,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2133220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="412,-321 412,-330 383,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20651@1" ObjectIDZND0="20650@x" ObjectIDZND1="g_1f58f00@0" ObjectIDZND2="g_169b920@0" Pin0InfoVect0LinkObjId="SW-104684_0" Pin0InfoVect1LinkObjId="g_1f58f00_0" Pin0InfoVect2LinkObjId="g_169b920_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104685_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="412,-321 412,-330 383,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_186f520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-576 440,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_fe4a30@0" ObjectIDZND0="g_186f750@0" Pin0InfoVect0LinkObjId="g_186f750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_fe4a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-576 440,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18ba500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="412,-525 440,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_202e8c0@0" ObjectIDZND0="g_fe4a30@0" ObjectIDZND1="20679@x" Pin0InfoVect0LinkObjId="g_fe4a30_0" Pin0InfoVect1LinkObjId="SW-104711_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_202e8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="412,-525 440,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d0bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-525 440,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_202e8c0@0" ObjectIDND1="20679@x" ObjectIDZND0="g_fe4a30@1" Pin0InfoVect0LinkObjId="g_fe4a30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_202e8c0_0" Pin1InfoVect1LinkObjId="SW-104711_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-525 440,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a39410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1636,-575 1636,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_23de6d0@0" ObjectIDZND0="g_1a39640@0" Pin0InfoVect0LinkObjId="g_1a39640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23de6d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1636,-575 1636,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1689720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,-524 1636,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_11fab30@0" ObjectIDZND0="g_23de6d0@0" ObjectIDZND1="20681@x" Pin0InfoVect0LinkObjId="g_23de6d0_0" Pin0InfoVect1LinkObjId="SW-104712_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11fab30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-524 1636,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1689980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1636,-524 1636,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_11fab30@0" ObjectIDND1="20681@x" ObjectIDZND0="g_23de6d0@1" Pin0InfoVect0LinkObjId="g_23de6d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11fab30_0" Pin1InfoVect1LinkObjId="SW-104712_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1636,-524 1636,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1be2280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-728 719,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="20633@0" ObjectIDZND0="20683@x" ObjectIDZND1="20636@x" Pin0InfoVect0LinkObjId="g_2139490_0" Pin0InfoVect1LinkObjId="SW-104670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-728 719,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1be2470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-717 731,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20683@x" ObjectIDND1="20633@x" ObjectIDZND0="20636@0" Pin0InfoVect0LinkObjId="SW-104670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1be2280_0" Pin1InfoVect1LinkObjId="SW-104667_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-717 731,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1bf40b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-1163 674,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="20627@x" ObjectIDND1="20630@x" ObjectIDND2="g_11e8560@0" ObjectIDZND0="g_217a6b0@0" ObjectIDZND1="34025@1" Pin0InfoVect0LinkObjId="g_217a6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104661_0" Pin1InfoVect1LinkObjId="SW-104664_0" Pin1InfoVect2LinkObjId="g_11e8560_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="674,-1163 674,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1bf4310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-1202 674,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34025@1" ObjectIDZND0="g_217a6b0@0" ObjectIDZND1="20627@x" ObjectIDZND2="20630@x" Pin0InfoVect0LinkObjId="g_217a6b0_0" Pin0InfoVect1LinkObjId="SW-104661_0" Pin0InfoVect2LinkObjId="SW-104664_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="674,-1202 674,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2135360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-919 1109,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20686@0" ObjectIDND1="20643@x" ObjectIDZND0="20644@0" Pin0InfoVect0LinkObjId="SW-104678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f867b0_0" Pin1InfoVect1LinkObjId="SW-104677_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-919 1109,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21355c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1073,-919 1064,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20644@1" ObjectIDZND0="g_21afa90@0" Pin0InfoVect0LinkObjId="g_21afa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1073,-919 1064,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2139490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-674 631,-681 719,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="20647@1" ObjectIDZND0="20683@x" Pin0InfoVect0LinkObjId="g_1be2280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-674 631,-681 719,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_eb3540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-626 631,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21396f0@0" ObjectIDZND0="20647@0" Pin0InfoVect0LinkObjId="SW-104681_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21396f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-626 631,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2179150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-624 1305,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_14f1830@0" ObjectIDZND0="20649@0" Pin0InfoVect0LinkObjId="SW-104683_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f1830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-624 1305,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21793b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-672 1305,-679 1393,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="20649@1" ObjectIDZND0="20684@x" Pin0InfoVect0LinkObjId="g_241a710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104683_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-672 1305,-679 1393,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2140420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-716 719,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="20636@x" ObjectIDND1="20633@x" ObjectIDZND0="20683@0" Pin0InfoVect0LinkObjId="g_1be2280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104670_0" Pin1InfoVect1LinkObjId="SW-104667_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-716 719,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_241a710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-716 1393,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="20642@x" ObjectIDND1="20639@x" ObjectIDZND0="20684@0" Pin0InfoVect0LinkObjId="g_21793b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-104676_0" Pin1InfoVect1LinkObjId="SW-104673_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-716 1393,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f66560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="688,-584 688,-595 719,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="g_241a900@0" ObjectIDZND0="20683@x" ObjectIDZND1="20646@x" Pin0InfoVect0LinkObjId="g_1be2280_0" Pin0InfoVect1LinkObjId="SW-104680_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_241a900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="688,-584 688,-595 719,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16843b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1362,-582 1362,-593 1393,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="g_1f66790@0" ObjectIDZND0="20684@x" ObjectIDZND1="20648@x" Pin0InfoVect0LinkObjId="g_21793b0_0" Pin0InfoVect1LinkObjId="SW-104682_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f66790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1362,-582 1362,-593 1393,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169f600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-481 719,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20646@1" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_1b314f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-481 719,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1faaa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-479 1393,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20648@1" ObjectIDZND0="20688@0" Pin0InfoVect0LinkObjId="g_19142d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104682_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-479 1393,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1faacc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-620 719,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="20683@1" ObjectIDZND0="g_241a900@0" ObjectIDZND1="20646@x" Pin0InfoVect0LinkObjId="g_241a900_0" Pin0InfoVect1LinkObjId="SW-104680_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1be2280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-620 719,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1faaf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-573 719,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="20646@0" ObjectIDZND0="20683@x" ObjectIDZND1="g_241a900@0" Pin0InfoVect0LinkObjId="g_1be2280_0" Pin0InfoVect1LinkObjId="g_241a900_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-573 719,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fab180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-618 1393,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="20684@1" ObjectIDZND0="g_1f66790@0" ObjectIDZND1="20648@x" Pin0InfoVect0LinkObjId="g_1f66790_0" Pin0InfoVect1LinkObjId="SW-104682_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21793b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-618 1393,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21956c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-593 1393,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="g_1f66790@0" ObjectIDND1="20684@x" ObjectIDZND0="20648@0" Pin0InfoVect0LinkObjId="SW-104682_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f66790_0" Pin1InfoVect1LinkObjId="g_21793b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-593 1393,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b31290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-525 440,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_fe4a30@0" ObjectIDND1="g_202e8c0@0" ObjectIDZND0="20679@1" Pin0InfoVect0LinkObjId="SW-104711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_fe4a30_0" Pin1InfoVect1LinkObjId="g_202e8c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-525 440,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b314f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-481 440,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20679@0" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_169f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-481 440,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213aa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1636,-524 1636,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_11fab30@0" ObjectIDND1="g_23de6d0@0" ObjectIDZND0="20681@1" Pin0InfoVect0LinkObjId="SW-104712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11fab30_0" Pin1InfoVect1LinkObjId="g_23de6d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1636,-524 1636,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19142d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1636,-479 1636,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20681@0" ObjectIDZND0="20688@0" Pin0InfoVect0LinkObjId="g_1faaa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1636,-479 1636,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1914530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="383,-167 383,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_169b920@0" ObjectIDZND0="g_219b820@0" Pin0InfoVect0LinkObjId="g_219b820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_169b920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="383,-167 383,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c82fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="383,-436 383,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20650@0" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_169f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="383,-436 383,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c83210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="383,-330 383,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="20651@x" ObjectIDND1="g_1f58f00@0" ObjectIDND2="g_169b920@0" ObjectIDZND0="20650@1" Pin0InfoVect0LinkObjId="SW-104684_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104685_0" Pin1InfoVect1LinkObjId="g_1f58f00_0" Pin1InfoVect2LinkObjId="g_169b920_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="383,-330 383,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c83470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="390,-237 383,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_1f58f00@0" ObjectIDZND0="g_169b920@0" ObjectIDZND1="20651@x" ObjectIDZND2="20650@x" Pin0InfoVect0LinkObjId="g_169b920_0" Pin0InfoVect1LinkObjId="SW-104685_0" Pin0InfoVect2LinkObjId="SW-104684_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f58f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="390,-237 383,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c836d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="383,-201 383,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_169b920@1" ObjectIDZND0="g_1f58f00@0" ObjectIDZND1="20651@x" ObjectIDZND2="20650@x" Pin0InfoVect0LinkObjId="g_1f58f00_0" Pin0InfoVect1LinkObjId="SW-104685_0" Pin0InfoVect2LinkObjId="SW-104684_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_169b920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="383,-201 383,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f58ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="383,-237 383,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1f58f00@0" ObjectIDND1="g_169b920@0" ObjectIDZND0="20651@x" ObjectIDZND1="20650@x" Pin0InfoVect0LinkObjId="SW-104685_0" Pin0InfoVect1LinkObjId="SW-104684_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f58f00_0" Pin1InfoVect1LinkObjId="g_169b920_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="383,-237 383,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a52d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="526,-285 526,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20653@0" ObjectIDZND0="g_1870d50@0" Pin0InfoVect0LinkObjId="g_1870d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104687_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="526,-285 526,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a5ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="526,-321 526,-330 497,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20653@1" ObjectIDZND0="20652@x" ObjectIDZND1="g_293a330@0" ObjectIDZND2="43391@x" Pin0InfoVect0LinkObjId="SW-104686_0" Pin0InfoVect1LinkObjId="g_293a330_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104687_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="526,-321 526,-330 497,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc1f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-436 496,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20652@0" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_169f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104686_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-436 496,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc2190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-330 497,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20653@x" ObjectIDND1="g_293a330@0" ObjectIDND2="43391@x" ObjectIDZND0="20652@1" Pin0InfoVect0LinkObjId="SW-104686_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104687_0" Pin1InfoVect1LinkObjId="g_293a330_0" Pin1InfoVect2LinkObjId="SM-CX_WJ.P1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-330 497,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc23f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="504,-237 497,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_293a330@0" ObjectIDZND0="20653@x" ObjectIDZND1="20652@x" ObjectIDZND2="43391@x" Pin0InfoVect0LinkObjId="SW-104687_0" Pin0InfoVect1LinkObjId="SW-104686_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_293a330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="504,-237 497,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2939ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-109 497,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43391@0" ObjectIDZND0="g_293a330@0" ObjectIDZND1="20653@x" ObjectIDZND2="20652@x" Pin0InfoVect0LinkObjId="g_293a330_0" Pin0InfoVect1LinkObjId="SW-104687_0" Pin0InfoVect2LinkObjId="SW-104686_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_WJ.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="497,-109 497,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_293a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-237 497,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_293a330@0" ObjectIDND1="43391@x" ObjectIDZND0="20653@x" ObjectIDZND1="20652@x" Pin0InfoVect0LinkObjId="SW-104687_0" Pin0InfoVect1LinkObjId="SW-104686_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_293a330_0" Pin1InfoVect1LinkObjId="SM-CX_WJ.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="497,-237 497,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1686790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="623,-285 623,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20655@0" ObjectIDZND0="g_1685da0@0" Pin0InfoVect0LinkObjId="g_1685da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104689_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="623,-285 623,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2700a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="623,-321 623,-330 594,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20655@1" ObjectIDZND0="20654@x" ObjectIDZND1="g_1d7aca0@0" ObjectIDZND2="43392@x" Pin0InfoVect0LinkObjId="SW-104688_0" Pin0InfoVect1LinkObjId="g_1d7aca0_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104689_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="623,-321 623,-330 594,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_ed4710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="594,-437 594,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20654@0" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_169f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104688_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="594,-437 594,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_ed4970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="594,-330 594,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20655@x" ObjectIDND1="g_1d7aca0@0" ObjectIDND2="43392@x" ObjectIDZND0="20654@1" Pin0InfoVect0LinkObjId="SW-104688_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104689_0" Pin1InfoVect1LinkObjId="g_1d7aca0_0" Pin1InfoVect2LinkObjId="SM-CX_WJ.P2_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="594,-330 594,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7a580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="601,-237 594,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_1d7aca0@0" ObjectIDZND0="20655@x" ObjectIDZND1="20654@x" ObjectIDZND2="43392@x" Pin0InfoVect0LinkObjId="SW-104689_0" Pin0InfoVect1LinkObjId="SW-104688_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d7aca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="601,-237 594,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7a7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="594,-109 594,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43392@0" ObjectIDZND0="g_1d7aca0@0" ObjectIDZND1="20655@x" ObjectIDZND2="20654@x" Pin0InfoVect0LinkObjId="g_1d7aca0_0" Pin0InfoVect1LinkObjId="SW-104689_0" Pin0InfoVect2LinkObjId="SW-104688_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_WJ.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="594,-109 594,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7aa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="594,-237 594,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1d7aca0@0" ObjectIDND1="43392@x" ObjectIDZND0="20655@x" ObjectIDZND1="20654@x" Pin0InfoVect0LinkObjId="SW-104689_0" Pin0InfoVect1LinkObjId="SW-104688_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d7aca0_0" Pin1InfoVect1LinkObjId="SM-CX_WJ.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="594,-237 594,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bce930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-285 721,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20657@0" ObjectIDZND0="g_18d1e60@0" Pin0InfoVect0LinkObjId="g_18d1e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104691_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-285 721,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcf080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-321 721,-330 692,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20657@1" ObjectIDZND0="20656@x" ObjectIDZND1="g_169a4d0@0" ObjectIDZND2="43393@x" Pin0InfoVect0LinkObjId="SW-104690_0" Pin0InfoVect1LinkObjId="g_169a4d0_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104691_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="721,-321 721,-330 692,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16852a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-436 692,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20656@0" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_169f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="692,-436 692,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1685500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-330 692,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20657@x" ObjectIDND1="g_169a4d0@0" ObjectIDND2="43393@x" ObjectIDZND0="20656@1" Pin0InfoVect0LinkObjId="SW-104690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104691_0" Pin1InfoVect1LinkObjId="g_169a4d0_0" Pin1InfoVect2LinkObjId="SM-CX_WJ.P3_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="692,-330 692,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1685760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,-237 692,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_169a4d0@0" ObjectIDZND0="20657@x" ObjectIDZND1="20656@x" ObjectIDZND2="43393@x" Pin0InfoVect0LinkObjId="SW-104691_0" Pin0InfoVect1LinkObjId="SW-104690_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_169a4d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="699,-237 692,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169a010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-109 692,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43393@0" ObjectIDZND0="g_169a4d0@0" ObjectIDZND1="20657@x" ObjectIDZND2="20656@x" Pin0InfoVect0LinkObjId="g_169a4d0_0" Pin0InfoVect1LinkObjId="SW-104691_0" Pin0InfoVect2LinkObjId="SW-104690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_WJ.P3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="692,-109 692,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169a270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-237 692,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_169a4d0@0" ObjectIDND1="43393@x" ObjectIDZND0="20657@x" ObjectIDZND1="20656@x" Pin0InfoVect0LinkObjId="SW-104691_0" Pin0InfoVect1LinkObjId="SW-104690_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_169a4d0_0" Pin1InfoVect1LinkObjId="SM-CX_WJ.P3_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="692,-237 692,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2504860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-286 830,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20659@0" ObjectIDZND0="g_272ac20@0" Pin0InfoVect0LinkObjId="g_272ac20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104693_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="830,-286 830,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2504fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="830,-321 830,-330 801,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20659@1" ObjectIDZND0="20658@x" ObjectIDZND1="g_193bb70@0" ObjectIDZND2="43394@x" Pin0InfoVect0LinkObjId="SW-104692_0" Pin0InfoVect1LinkObjId="g_193bb70_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104693_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="830,-321 830,-330 801,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_193af90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-436 801,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20658@0" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_169f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104692_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-436 801,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_193b1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-330 801,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20659@x" ObjectIDND1="g_193bb70@0" ObjectIDND2="43394@x" ObjectIDZND0="20658@1" Pin0InfoVect0LinkObjId="SW-104692_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104693_0" Pin1InfoVect1LinkObjId="g_193bb70_0" Pin1InfoVect2LinkObjId="SM-CX_WJ.P4_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-330 801,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_193b450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="808,-237 801,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_193bb70@0" ObjectIDZND0="20659@x" ObjectIDZND1="20658@x" ObjectIDZND2="43394@x" Pin0InfoVect0LinkObjId="SW-104693_0" Pin0InfoVect1LinkObjId="SW-104692_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_193bb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="808,-237 801,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_193b6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-109 801,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43394@0" ObjectIDZND0="g_193bb70@0" ObjectIDZND1="20659@x" ObjectIDZND2="20658@x" Pin0InfoVect0LinkObjId="g_193bb70_0" Pin0InfoVect1LinkObjId="SW-104693_0" Pin0InfoVect2LinkObjId="SW-104692_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_WJ.P4_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="801,-109 801,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_193b910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-237 801,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_193bb70@0" ObjectIDND1="43394@x" ObjectIDZND0="20659@x" ObjectIDZND1="20658@x" Pin0InfoVect0LinkObjId="SW-104693_0" Pin0InfoVect1LinkObjId="SW-104692_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_193bb70_0" Pin1InfoVect1LinkObjId="SM-CX_WJ.P4_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="801,-237 801,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209d4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-241 1158,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20663@0" ObjectIDZND0="g_2136340@0" Pin0InfoVect0LinkObjId="g_2136340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104697_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-241 1158,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_168a4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1129,-437 1129,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20662@0" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_169f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104696_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1129,-437 1129,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_fe6860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1211,-279 1211,-286 1158,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_1a7f850@0" ObjectIDZND0="20663@x" ObjectIDZND1="20662@x" ObjectIDZND2="g_fe6f20@0" Pin0InfoVect0LinkObjId="SW-104697_0" Pin0InfoVect1LinkObjId="SW-104696_0" Pin0InfoVect2LinkObjId="g_fe6f20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a7f850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1211,-279 1211,-286 1158,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_fe6a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-277 1158,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="20663@1" ObjectIDZND0="g_1a7f850@0" ObjectIDZND1="20662@x" ObjectIDZND2="g_fe6f20@0" Pin0InfoVect0LinkObjId="g_1a7f850_0" Pin0InfoVect1LinkObjId="SW-104696_0" Pin0InfoVect2LinkObjId="g_fe6f20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104697_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-277 1158,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_fe6cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-286 1129,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_1a7f850@0" ObjectIDND1="20663@x" ObjectIDZND0="20662@x" ObjectIDZND1="g_fe6f20@0" Pin0InfoVect0LinkObjId="SW-104696_0" Pin0InfoVect1LinkObjId="g_fe6f20_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a7f850_0" Pin1InfoVect1LinkObjId="SW-104697_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-286 1129,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_eecbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1129,-169 1129,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_eece40@0" ObjectIDZND0="g_fe6f20@0" Pin0InfoVect0LinkObjId="g_fe6f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_eece40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1129,-169 1129,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_fd2bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-142 1078,-153 1110,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_fd2e10@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_fd2e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-142 1078,-153 1110,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f7fcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1129,-345 1129,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="20662@1" ObjectIDZND0="g_1a7f850@0" ObjectIDZND1="20663@x" ObjectIDZND2="g_fe6f20@0" Pin0InfoVect0LinkObjId="g_1a7f850_0" Pin0InfoVect1LinkObjId="SW-104697_0" Pin0InfoVect2LinkObjId="g_fe6f20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104696_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1129,-345 1129,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f7ff30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1129,-229 1129,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_fe6f20@1" ObjectIDZND0="g_1a7f850@0" ObjectIDZND1="20663@x" ObjectIDZND2="20662@x" Pin0InfoVect0LinkObjId="g_1a7f850_0" Pin0InfoVect1LinkObjId="SW-104697_0" Pin0InfoVect2LinkObjId="SW-104696_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_fe6f20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1129,-229 1129,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdfcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-281 1508,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20665@0" ObjectIDZND0="g_20dd840@0" Pin0InfoVect0LinkObjId="g_20dd840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104699_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-281 1508,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1508,-317 1508,-326 1479,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20665@1" ObjectIDZND0="20664@x" ObjectIDZND1="g_1c9a930@0" ObjectIDZND2="g_1911340@0" Pin0InfoVect0LinkObjId="SW-104698_0" Pin0InfoVect1LinkObjId="g_1c9a930_0" Pin0InfoVect2LinkObjId="g_1911340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104699_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1508,-317 1508,-326 1479,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c99c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1479,-432 1479,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20664@0" ObjectIDZND0="20688@0" Pin0InfoVect0LinkObjId="g_1faaa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1479,-432 1479,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c99ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1479,-326 1479,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="20665@x" ObjectIDND1="g_1c9a930@0" ObjectIDND2="g_1911340@0" ObjectIDZND0="20664@1" Pin0InfoVect0LinkObjId="SW-104698_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104699_0" Pin1InfoVect1LinkObjId="g_1c9a930_0" Pin1InfoVect2LinkObjId="g_1911340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1479,-326 1479,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c9a140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1486,-241 1479,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_1c9a930@0" ObjectIDZND0="20665@x" ObjectIDZND1="20664@x" ObjectIDZND2="g_1911340@0" Pin0InfoVect0LinkObjId="SW-104699_0" Pin0InfoVect1LinkObjId="SW-104698_0" Pin0InfoVect2LinkObjId="g_1911340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c9a930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1486,-241 1479,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2136af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1479,-164 1479,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2136d20@0" ObjectIDZND0="g_1911340@0" Pin0InfoVect0LinkObjId="g_1911340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2136d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1479,-164 1479,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ad3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1448,-137 1448,-148 1480,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_21ad650@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ad650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1448,-137 1448,-148 1480,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_efb300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1479,-241 1479,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1c9a930@0" ObjectIDND1="g_1911340@0" ObjectIDZND0="20665@x" ObjectIDZND1="20664@x" Pin0InfoVect0LinkObjId="SW-104699_0" Pin0InfoVect1LinkObjId="SW-104698_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c9a930_0" Pin1InfoVect1LinkObjId="g_1911340_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1479,-241 1479,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_efb4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1479,-225 1479,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_1911340@1" ObjectIDZND0="g_1c9a930@0" ObjectIDZND1="20665@x" ObjectIDZND2="20664@x" Pin0InfoVect0LinkObjId="g_1c9a930_0" Pin0InfoVect1LinkObjId="SW-104699_0" Pin0InfoVect2LinkObjId="SW-104698_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1911340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1479,-225 1479,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26ffd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1616,-281 1616,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20667@0" ObjectIDZND0="g_26ff290@0" Pin0InfoVect0LinkObjId="g_26ff290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1616,-281 1616,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_eb4a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1616,-317 1616,-326 1587,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20667@1" ObjectIDZND0="20666@x" ObjectIDZND1="g_1677fd0@0" ObjectIDZND2="43395@x" Pin0InfoVect0LinkObjId="SW-104700_0" Pin0InfoVect1LinkObjId="g_1677fd0_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P5_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104701_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1616,-317 1616,-326 1587,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213fa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1587,-432 1587,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20666@0" ObjectIDZND0="20688@0" Pin0InfoVect0LinkObjId="g_1faaa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1587,-432 1587,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213fcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1587,-326 1587,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20667@x" ObjectIDND1="g_1677fd0@0" ObjectIDND2="43395@x" ObjectIDZND0="20666@1" Pin0InfoVect0LinkObjId="SW-104700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104701_0" Pin1InfoVect1LinkObjId="g_1677fd0_0" Pin1InfoVect2LinkObjId="SM-CX_WJ.P5_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1587,-326 1587,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16778b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1594,-233 1587,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_1677fd0@0" ObjectIDZND0="20667@x" ObjectIDZND1="20666@x" ObjectIDZND2="43395@x" Pin0InfoVect0LinkObjId="SW-104701_0" Pin0InfoVect1LinkObjId="SW-104700_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P5_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1677fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1594,-233 1587,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1677b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1587,-105 1587,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43395@0" ObjectIDZND0="g_1677fd0@0" ObjectIDZND1="20667@x" ObjectIDZND2="20666@x" Pin0InfoVect0LinkObjId="g_1677fd0_0" Pin0InfoVect1LinkObjId="SW-104701_0" Pin0InfoVect2LinkObjId="SW-104700_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_WJ.P5_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1587,-105 1587,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1677d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1587,-233 1587,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1677fd0@0" ObjectIDND1="43395@x" ObjectIDZND0="20667@x" ObjectIDZND1="20666@x" Pin0InfoVect0LinkObjId="SW-104701_0" Pin0InfoVect1LinkObjId="SW-104700_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1677fd0_0" Pin1InfoVect1LinkObjId="SM-CX_WJ.P5_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1587,-233 1587,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23c73c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1724,-281 1724,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20669@0" ObjectIDZND0="g_21ba280@0" Pin0InfoVect0LinkObjId="g_21ba280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104703_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1724,-281 1724,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23c7b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1724,-317 1724,-326 1695,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20669@1" ObjectIDZND0="20668@x" ObjectIDZND1="g_1c168a0@0" ObjectIDZND2="43396@x" Pin0InfoVect0LinkObjId="SW-104702_0" Pin0InfoVect1LinkObjId="g_1c168a0_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P6_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104703_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1724,-317 1724,-326 1695,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c15cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1695,-432 1695,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20668@0" ObjectIDZND0="20688@0" Pin0InfoVect0LinkObjId="g_1faaa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104702_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1695,-432 1695,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c15f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1695,-326 1695,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20669@x" ObjectIDND1="g_1c168a0@0" ObjectIDND2="43396@x" ObjectIDZND0="20668@1" Pin0InfoVect0LinkObjId="SW-104702_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104703_0" Pin1InfoVect1LinkObjId="g_1c168a0_0" Pin1InfoVect2LinkObjId="SM-CX_WJ.P6_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1695,-326 1695,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c16180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1702,-233 1695,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_1c168a0@0" ObjectIDZND0="20669@x" ObjectIDZND1="20668@x" ObjectIDZND2="43396@x" Pin0InfoVect0LinkObjId="SW-104703_0" Pin0InfoVect1LinkObjId="SW-104702_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P6_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c168a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1702,-233 1695,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c163e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1695,-105 1695,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43396@0" ObjectIDZND0="g_1c168a0@0" ObjectIDZND1="20669@x" ObjectIDZND2="20668@x" Pin0InfoVect0LinkObjId="g_1c168a0_0" Pin0InfoVect1LinkObjId="SW-104703_0" Pin0InfoVect2LinkObjId="SW-104702_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_WJ.P6_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1695,-105 1695,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c16640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1695,-233 1695,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1c168a0@0" ObjectIDND1="43396@x" ObjectIDZND0="20669@x" ObjectIDZND1="20668@x" Pin0InfoVect0LinkObjId="SW-104703_0" Pin0InfoVect1LinkObjId="SW-104702_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c168a0_0" Pin1InfoVect1LinkObjId="SM-CX_WJ.P6_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1695,-233 1695,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1928a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1821,-281 1821,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20671@0" ObjectIDZND0="g_1928010@0" Pin0InfoVect0LinkObjId="g_1928010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1821,-281 1821,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1929170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1821,-317 1821,-326 1792,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20671@1" ObjectIDZND0="20670@x" ObjectIDZND1="g_23b6c30@0" ObjectIDZND2="43397@x" Pin0InfoVect0LinkObjId="SW-104704_0" Pin0InfoVect1LinkObjId="g_23b6c30_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P7_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1821,-317 1821,-326 1792,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272c850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1792,-432 1792,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20670@0" ObjectIDZND0="20688@0" Pin0InfoVect0LinkObjId="g_1faaa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1792,-432 1792,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272cab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1792,-326 1792,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20671@x" ObjectIDND1="g_23b6c30@0" ObjectIDND2="43397@x" ObjectIDZND0="20670@1" Pin0InfoVect0LinkObjId="SW-104704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104705_0" Pin1InfoVect1LinkObjId="g_23b6c30_0" Pin1InfoVect2LinkObjId="SM-CX_WJ.P7_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1792,-326 1792,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272cd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,-233 1792,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_23b6c30@0" ObjectIDZND0="20671@x" ObjectIDZND1="20670@x" ObjectIDZND2="43397@x" Pin0InfoVect0LinkObjId="SW-104705_0" Pin0InfoVect1LinkObjId="SW-104704_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P7_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23b6c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1799,-233 1792,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272cf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1792,-105 1792,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43397@0" ObjectIDZND0="g_23b6c30@0" ObjectIDZND1="20671@x" ObjectIDZND2="20670@x" Pin0InfoVect0LinkObjId="g_23b6c30_0" Pin0InfoVect1LinkObjId="SW-104705_0" Pin0InfoVect2LinkObjId="SW-104704_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_WJ.P7_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1792,-105 1792,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23b69d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1792,-233 1792,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_23b6c30@0" ObjectIDND1="43397@x" ObjectIDZND0="20671@x" ObjectIDZND1="20670@x" Pin0InfoVect0LinkObjId="SW-104705_0" Pin0InfoVect1LinkObjId="SW-104704_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23b6c30_0" Pin1InfoVect1LinkObjId="SM-CX_WJ.P7_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1792,-233 1792,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2376ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1924,-281 1924,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20673@0" ObjectIDZND0="g_23760a0@0" Pin0InfoVect0LinkObjId="g_23760a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104707_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1924,-281 1924,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2377220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1924,-317 1924,-326 1895,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="20673@1" ObjectIDZND0="20672@x" ObjectIDZND1="g_2459720@0" ObjectIDZND2="43398@x" Pin0InfoVect0LinkObjId="SW-104706_0" Pin0InfoVect1LinkObjId="g_2459720_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P8_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104707_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1924,-317 1924,-326 1895,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2458b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1895,-432 1895,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20672@0" ObjectIDZND0="20688@0" Pin0InfoVect0LinkObjId="g_1faaa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104706_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1895,-432 1895,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2458da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1895,-326 1895,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="breaker" ObjectIDND0="20673@x" ObjectIDND1="g_2459720@0" ObjectIDND2="43398@x" ObjectIDZND0="20672@1" Pin0InfoVect0LinkObjId="SW-104706_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104707_0" Pin1InfoVect1LinkObjId="g_2459720_0" Pin1InfoVect2LinkObjId="SM-CX_WJ.P8_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1895,-326 1895,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2459000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-233 1895,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2459720@0" ObjectIDZND0="20673@x" ObjectIDZND1="20672@x" ObjectIDZND2="43398@x" Pin0InfoVect0LinkObjId="SW-104707_0" Pin0InfoVect1LinkObjId="SW-104706_0" Pin0InfoVect2LinkObjId="SM-CX_WJ.P8_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2459720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-233 1895,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2459260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1895,-105 1895,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="43398@0" ObjectIDZND0="g_2459720@0" ObjectIDZND1="20673@x" ObjectIDZND2="20672@x" Pin0InfoVect0LinkObjId="g_2459720_0" Pin0InfoVect1LinkObjId="SW-104707_0" Pin0InfoVect2LinkObjId="SW-104706_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_WJ.P8_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1895,-105 1895,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24594c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1895,-233 1895,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2459720@0" ObjectIDND1="43398@x" ObjectIDZND0="20673@x" ObjectIDZND1="20672@x" Pin0InfoVect0LinkObjId="SW-104707_0" Pin0InfoVect1LinkObjId="SW-104706_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2459720_0" Pin1InfoVect1LinkObjId="SM-CX_WJ.P8_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1895,-233 1895,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242afc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2015,-281 2015,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20675@0" ObjectIDZND0="g_242a530@0" Pin0InfoVect0LinkObjId="g_242a530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2015,-281 2015,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242c100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2015,-317 2015,-326 1986,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20675@1" ObjectIDZND0="20674@x" ObjectIDZND1="g_1a46bc0@0" ObjectIDZND2="g_242c7e0@0" Pin0InfoVect0LinkObjId="SW-104708_0" Pin0InfoVect1LinkObjId="g_1a46bc0_0" Pin0InfoVect2LinkObjId="g_242c7e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2015,-317 2015,-326 1986,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,-162 1986,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_242c7e0@0" ObjectIDZND0="g_242b220@0" Pin0InfoVect0LinkObjId="g_242b220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_242c7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1986,-162 1986,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a46240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,-326 1986,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="20675@x" ObjectIDND1="g_1a46bc0@0" ObjectIDND2="g_242c7e0@0" ObjectIDZND0="20674@1" Pin0InfoVect0LinkObjId="SW-104708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104709_0" Pin1InfoVect1LinkObjId="g_1a46bc0_0" Pin1InfoVect2LinkObjId="g_242c7e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1986,-326 1986,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a464a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-233 1986,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_1a46bc0@0" ObjectIDZND0="g_242c7e0@0" ObjectIDZND1="20675@x" ObjectIDZND2="20674@x" Pin0InfoVect0LinkObjId="g_242c7e0_0" Pin0InfoVect1LinkObjId="SW-104709_0" Pin0InfoVect2LinkObjId="SW-104708_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a46bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-233 1986,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a46700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,-197 1986,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_242c7e0@1" ObjectIDZND0="g_1a46bc0@0" ObjectIDZND1="20675@x" ObjectIDZND2="20674@x" Pin0InfoVect0LinkObjId="g_1a46bc0_0" Pin0InfoVect1LinkObjId="SW-104709_0" Pin0InfoVect2LinkObjId="SW-104708_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_242c7e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1986,-197 1986,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a46960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,-233 1986,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1a46bc0@0" ObjectIDND1="g_242c7e0@0" ObjectIDZND0="20675@x" ObjectIDZND1="20674@x" Pin0InfoVect0LinkObjId="SW-104709_0" Pin0InfoVect1LinkObjId="SW-104708_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a46bc0_0" Pin1InfoVect1LinkObjId="g_242c7e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1986,-233 1986,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2460580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,-432 1986,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20674@0" ObjectIDZND0="20688@0" Pin0InfoVect0LinkObjId="g_1faaa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1986,-432 1986,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2463990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="971,-285 971,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20661@0" ObjectIDZND0="g_2462f00@0" Pin0InfoVect0LinkObjId="g_2462f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="971,-285 971,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2726250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="971,-321 971,-330 942,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20661@1" ObjectIDZND0="20660@x" ObjectIDZND1="g_24351f0@0" ObjectIDZND2="g_2726930@0" Pin0InfoVect0LinkObjId="SW-104694_0" Pin0InfoVect1LinkObjId="g_24351f0_0" Pin0InfoVect2LinkObjId="g_2726930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104695_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="971,-321 971,-330 942,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2727100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-173 942,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2726930@0" ObjectIDZND0="g_245a150@0" Pin0InfoVect0LinkObjId="g_245a150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2726930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,-173 942,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2434870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-330 942,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="20661@x" ObjectIDND1="g_24351f0@0" ObjectIDND2="g_2726930@0" ObjectIDZND0="20660@1" Pin0InfoVect0LinkObjId="SW-104694_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-104695_0" Pin1InfoVect1LinkObjId="g_24351f0_0" Pin1InfoVect2LinkObjId="g_2726930_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,-330 942,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2434ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="949,-237 942,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_24351f0@0" ObjectIDZND0="g_2726930@0" ObjectIDZND1="20661@x" ObjectIDZND2="20660@x" Pin0InfoVect0LinkObjId="g_2726930_0" Pin0InfoVect1LinkObjId="SW-104695_0" Pin0InfoVect2LinkObjId="SW-104694_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24351f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="949,-237 942,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2434d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-207 942,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_2726930@1" ObjectIDZND0="g_24351f0@0" ObjectIDZND1="20661@x" ObjectIDZND2="20660@x" Pin0InfoVect0LinkObjId="g_24351f0_0" Pin0InfoVect1LinkObjId="SW-104695_0" Pin0InfoVect2LinkObjId="SW-104694_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2726930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="942,-207 942,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2434f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-237 942,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_24351f0@0" ObjectIDND1="g_2726930@0" ObjectIDZND0="20661@x" ObjectIDZND1="20660@x" Pin0InfoVect0LinkObjId="SW-104695_0" Pin0InfoVect1LinkObjId="SW-104694_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24351f0_0" Pin1InfoVect1LinkObjId="g_2726930_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="942,-237 942,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2435f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-436 942,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20660@0" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_169f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,-436 942,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a319c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1343,-455 1343,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20688@0" ObjectIDZND0="20677@1" Pin0InfoVect0LinkObjId="SW-104713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1faaa90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1343,-455 1343,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a31c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1252,-431 1252,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20676@0" ObjectIDZND0="20687@0" Pin0InfoVect0LinkObjId="g_169f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1252,-431 1252,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a3e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-1138 1653,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_f0cc30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-1138 1653,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a3ea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-888 1653,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f0cc30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-888 1653,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2370510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-1138 1866,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_f0cc30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1866,-1138 1866,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2370770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-888 1866,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f0cc30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1866,-888 1866,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2375320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1813,-888 1813,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f0cc30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1813,-888 1813,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2375580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-992 1813,-992 1813,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_f0cc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_f0cc30_0" Pin1InfoVect1LinkObjId="g_f0cc30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-992 1813,-992 1813,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2701060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-1004 1653,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_f0cc30_0" Pin0InfoVect1LinkObjId="g_f0cc30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f0cc30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-1004 1653,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27012c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-992 1653,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_f0cc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_f0cc30_0" Pin1InfoVect1LinkObjId="g_f0cc30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-992 1653,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27064b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1708,-743 1708,-725 1950,-725 1950,-992 1866,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_f0cc30_0" Pin0InfoVect1LinkObjId="g_f0cc30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f0cc30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1708,-743 1708,-725 1950,-725 1950,-992 1866,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2706720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1708,-853 1708,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_f0cc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1708,-853 1708,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27073a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-1004 1866,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_f0cc30_0" Pin0InfoVect1LinkObjId="g_f0cc30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f0cc30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1866,-1004 1866,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2707600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-992 1866,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_f0cc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_f0cc30_0" Pin1InfoVect1LinkObjId="g_f0cc30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1866,-992 1866,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24431f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1259,-321 1252,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1a31e80@0" ObjectIDZND0="20676@x" ObjectIDZND1="20677@x" Pin0InfoVect0LinkObjId="SW-104710_0" Pin0InfoVect1LinkObjId="SW-104713_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a31e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1259,-321 1252,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2443450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1252,-321 1252,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1a31e80@0" ObjectIDND1="20677@x" ObjectIDZND0="20676@1" Pin0InfoVect0LinkObjId="SW-104710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a31e80_0" Pin1InfoVect1LinkObjId="SW-104713_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1252,-321 1252,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24436b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1252,-321 1252,-302 1344,-302 1343,-303 1343,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1a31e80@0" ObjectIDND1="20676@x" ObjectIDZND0="20677@0" Pin0InfoVect0LinkObjId="SW-104713_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a31e80_0" Pin1InfoVect1LinkObjId="SW-104710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1252,-321 1252,-302 1344,-302 1343,-303 1343,-343 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="20686" cx="674" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20686" cx="719" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20686" cx="1393" cy="-879" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20688" cx="1479" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20688" cx="1587" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20688" cx="1695" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20688" cx="1792" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20688" cx="1895" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20688" cx="1986" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="383" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="594" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="692" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="801" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="496" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="942" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="1129" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20688" cx="1393" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="719" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="440" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20687" cx="1252" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20688" cx="1343" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20688" cx="1636" cy="-455" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-93920" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 262.000000 -1118.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19753" ObjectName="DYN-CX_WJ"/>
     <cge:Meas_Ref ObjectId="93920"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1ce9090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -874.000000) translate(0,16)">I段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,17)">危险点说明</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1550aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,374)">联系方式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18bee90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,17)">频率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18bee90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18bee90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,59)">全站有功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18bee90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18bee90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,101)">风机出力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18bee90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18bee90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,143)">全站无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18bee90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18bee90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,185)">并网联络点的电压和交换功率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_18becf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 115.000000 -1198.500000) translate(0,16)">五街风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_f10080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -684.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24563f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 616.000000 -975.000000) translate(0,12)">25117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1961f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 -920.000000) translate(0,12)">2511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_eb6860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -1113.500000) translate(0,16)">220kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_eb6860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -1113.500000) translate(0,35)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fd7880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -944.000000) translate(0,12)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19cb730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1127.000000 -972.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fe4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1063.000000 -1035.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cabe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -992.000000) translate(0,12)">251</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1206be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -1034.000000) translate(0,12)">25160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18baa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -1099.000000) translate(0,12)">25167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f62280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 -1052.000000) translate(0,12)">2516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_193be30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 646.000000 -1265.500000) translate(0,16)">红五线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ec3720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 -806.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cb5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 676.000000 -856.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cb5ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -750.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a571c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -847.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a573d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -798.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19743c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -740.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1974600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1349.000000 -854.000000) translate(0,12)">2021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_100afe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -804.000000) translate(0,12)">202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_100b220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1349.000000 -745.000000) translate(0,12)">2026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1098740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -738.000000) translate(0,12)">20267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1098960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -796.000000) translate(0,12)">20260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21225f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -845.000000) translate(0,12)">20217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14ea270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1259.000000 -659.000000) translate(0,12)">2020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1fdcd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -447.000000) translate(0,16)">35kVⅢ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b6aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.257519 -310.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d0e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 -497.000000) translate(0,12)">3903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21832d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 335.000000 -99.000000) translate(0,12)">3号动态无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21832d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 335.000000 -99.000000) translate(0,27)">补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151afa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 341.000000 -67.000000) translate(0,12)">±20MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acc930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 -170.000000) translate(0,12)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acc930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 -170.000000) translate(0,27)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acc930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 -170.000000) translate(0,42)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acc930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 -170.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acc930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 -170.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1faf4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 534.000000 -67.000000) translate(0,12)">(79~86号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2420b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -171.000000) translate(0,12)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2420b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -171.000000) translate(0,27)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2420b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -171.000000) translate(0,42)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2420b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -171.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2420b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -171.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_100d1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.257519 -533.000000) translate(0,12)">303</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a35e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1404.257519 -529.000000) translate(0,12)">304</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a36050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1609.000000 -1172.000000) translate(0,12)">35kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285ff60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1620.500000 -1156.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1865ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1603.500000 -849.000000) translate(0,12)">0.4kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1865fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1805.500000 -849.000000) translate(0,12)">0.4kVⅣ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -803.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -803.000000) translate(0,27)">SZ11-80000/220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -803.000000) translate(0,42)">230±8×1.25%/37</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -803.000000) translate(0,57)">80000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -803.000000) translate(0,72)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -803.000000) translate(0,87)">Ud%=14</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1688d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1655.000000 -494.000000) translate(0,12)">3904</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_169b640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 383.000000 -659.500000) translate(0,16)">35kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_169b640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 383.000000 -659.500000) translate(0,35)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1ac6b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -656.500000) translate(0,16)">35kVⅣ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1ac6b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -656.500000) translate(0,35)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac70b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1456.000000 -653.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd9340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -801.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd9340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -801.000000) translate(0,27)">SZ11-80000/220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd9340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -801.000000) translate(0,42)">230±8×1.25%/37</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd9340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -801.000000) translate(0,57)">80000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd9340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -801.000000) translate(0,72)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd9340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -801.000000) translate(0,87)">Ud%=14</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c51930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.000000 -655.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ed3660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -661.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_213ff30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1437.000000 -682.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1914790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 395.257519 -396.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a5530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.257519 -309.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa5ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.257519 -395.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2700470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.257519 -308.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2700bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.257519 -396.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18e0200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -68.000000) translate(0,12)">(71~78号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_102d290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -171.000000) translate(0,12)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_102d290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -171.000000) translate(0,27)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_102d290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -171.000000) translate(0,42)">Ⅲ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_102d290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -171.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_102d290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -171.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_102d490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 -67.000000) translate(0,12)">(87~96号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bceb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.257519 -307.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcf270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.257519 -396.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1864090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -170.000000) translate(0,12)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1864090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -170.000000) translate(0,27)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1864090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -170.000000) translate(0,42)">Ⅳ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1864090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -170.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1864090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -170.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2504ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.257519 -310.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1598480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.257519 -395.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fdda10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -67.000000) translate(0,12)">(97~105号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209d740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1161.257519 -264.000000) translate(0,12)">35767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209dc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1138.257519 -397.000000) translate(0,12)">357</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168a730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -78.000000) translate(0,12)">35kV3号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be01b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1491.257519 -392.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c9a3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1512.257519 -305.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -73.000000) translate(0,12)">35kV4号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efb0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1150.000000 -163.000000) translate(0,12)">800kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efb6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1498.000000 -144.000000) translate(0,12)">800kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -167.000000) translate(0,12)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -167.000000) translate(0,27)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -167.000000) translate(0,42)">Ⅵ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -167.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -167.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efbf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1641.000000 -56.000000) translate(0,12)">(113~119号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efc1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -167.000000) translate(0,12)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efc1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -167.000000) translate(0,27)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efc1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -167.000000) translate(0,42)">Ⅴ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efc1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -167.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efc1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -167.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fff80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1620.257519 -305.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_eb4c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1599.257519 -392.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c75f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1726.257519 -305.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c7d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1707.257519 -392.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22db410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 -72.000000) translate(0,12)">(106~112号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22db9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.000000 -167.000000) translate(0,12)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22db9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.000000 -167.000000) translate(0,27)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22db9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.000000 -167.000000) translate(0,42)">Ⅶ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22db9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.000000 -167.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22db9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.000000 -167.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22dbed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -71.000000) translate(0,12)">(120~130号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1928c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1825.257519 -305.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a29670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1804.257519 -392.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1878.000000 -167.000000) translate(0,12)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1878.000000 -167.000000) translate(0,27)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1878.000000 -167.000000) translate(0,42)">Ⅷ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1878.000000 -167.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1878.000000 -167.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2376d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1928.257519 -305.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2377410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1906.257519 -392.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2368e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1847.000000 -56.000000) translate(0,12)">(131~141号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_242bec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2019.257519 -305.000000) translate(0,12)">36667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_242c2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1955.000000 -98.000000) translate(0,12)">4号动态无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_242c2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1955.000000 -98.000000) translate(0,27)">补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_242c5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1966.000000 -68.000000) translate(0,12)">±20MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_242d2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1998.257519 -392.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2463bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.257519 -310.000000) translate(0,12)">35667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2726440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 909.000000 -96.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27266f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -136.000000) translate(0,12)">250kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2727360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.257519 -396.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_245f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.257519 -387.000000) translate(0,12)">334</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3eff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 -1172.000000) translate(0,12)">10kV五街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3eff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 -1172.000000) translate(0,27)">硬把分支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a401c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1891.500000 -1088.000000) translate(0,12)">备用所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a401c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1891.500000 -1088.000000) translate(0,27)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27079f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1569.500000 -1081.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2443920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.257519 -383.000000) translate(0,12)">3344</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2443e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1970.000000 -482.000000) translate(0,16)">35kVⅣ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_271c060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -261.000000) translate(0,15)">7321190</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.423034" x1="651" x2="651" y1="-681" y2="-667"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="651" x2="651" y1="-626" y2="-658"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="671" x2="671" y1="-626" y2="-653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="671" x2="671" y1="-681" y2="-657"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="631" x2="671" y1="-626" y2="-626"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.423034" x1="1325" x2="1325" y1="-679" y2="-665"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1325" x2="1325" y1="-624" y2="-656"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1345" x2="1345" y1="-624" y2="-651"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1345" x2="1345" y1="-679" y2="-655"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1305" x2="1345" y1="-624" y2="-624"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="934" x2="942" y1="-124" y2="-116"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="942" x2="950" y1="-116" y2="-124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="942" x2="942" y1="-108" y2="-116"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="1636" x2="1721" y1="-853" y2="-853"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="1798" x2="1904" y1="-853" y2="-853"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-104659">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 -962.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20625" ObjectName="SW-CX_WJ.CX_WJ_251BK"/>
     <cge:Meas_Ref ObjectId="104659"/>
    <cge:TPSR_Ref TObjectID="20625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104665">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.565184 -777.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20631" ObjectName="SW-CX_WJ.CX_WJ_201BK"/>
     <cge:Meas_Ref ObjectId="104665"/>
    <cge:TPSR_Ref TObjectID="20631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104671">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1384.000000 -776.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20637" ObjectName="SW-CX_WJ.CX_WJ_202BK"/>
     <cge:Meas_Ref ObjectId="104671"/>
    <cge:TPSR_Ref TObjectID="20637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104680">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -474.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20646" ObjectName="SW-CX_WJ.CX_WJ_303BK"/>
     <cge:Meas_Ref ObjectId="104680"/>
    <cge:TPSR_Ref TObjectID="20646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104682">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1383.000000 -472.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20648" ObjectName="SW-CX_WJ.CX_WJ_304BK"/>
     <cge:Meas_Ref ObjectId="104682"/>
    <cge:TPSR_Ref TObjectID="20648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104684">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 372.698795 -337.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20650" ObjectName="SW-CX_WJ.CX_WJ_351BK"/>
     <cge:Meas_Ref ObjectId="104684"/>
    <cge:TPSR_Ref TObjectID="20650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104686">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.012048 -337.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20652" ObjectName="SW-CX_WJ.CX_WJ_352BK"/>
     <cge:Meas_Ref ObjectId="104686"/>
    <cge:TPSR_Ref TObjectID="20652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104688">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.096386 -337.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20654" ObjectName="SW-CX_WJ.CX_WJ_353BK"/>
     <cge:Meas_Ref ObjectId="104688"/>
    <cge:TPSR_Ref TObjectID="20654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104690">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.915663 -337.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20656" ObjectName="SW-CX_WJ.CX_WJ_354BK"/>
     <cge:Meas_Ref ObjectId="104690"/>
    <cge:TPSR_Ref TObjectID="20656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104692">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.819277 -337.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20658" ObjectName="SW-CX_WJ.CX_WJ_355BK"/>
     <cge:Meas_Ref ObjectId="104692"/>
    <cge:TPSR_Ref TObjectID="20658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104696">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.662651 -338.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20662" ObjectName="SW-CX_WJ.CX_WJ_357BK"/>
     <cge:Meas_Ref ObjectId="104696"/>
    <cge:TPSR_Ref TObjectID="20662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104698">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 -333.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20664" ObjectName="SW-CX_WJ.CX_WJ_361BK"/>
     <cge:Meas_Ref ObjectId="104698"/>
    <cge:TPSR_Ref TObjectID="20664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104700">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1577.000000 -333.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20666" ObjectName="SW-CX_WJ.CX_WJ_362BK"/>
     <cge:Meas_Ref ObjectId="104700"/>
    <cge:TPSR_Ref TObjectID="20666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104702">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1685.000000 -333.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20668" ObjectName="SW-CX_WJ.CX_WJ_363BK"/>
     <cge:Meas_Ref ObjectId="104702"/>
    <cge:TPSR_Ref TObjectID="20668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1782.000000 -333.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20670" ObjectName="SW-CX_WJ.CX_WJ_364BK"/>
     <cge:Meas_Ref ObjectId="104704"/>
    <cge:TPSR_Ref TObjectID="20670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104706">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1885.000000 -333.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20672" ObjectName="SW-CX_WJ.CX_WJ_365BK"/>
     <cge:Meas_Ref ObjectId="104706"/>
    <cge:TPSR_Ref TObjectID="20672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1976.000000 -333.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20674" ObjectName="SW-CX_WJ.CX_WJ_366BK"/>
     <cge:Meas_Ref ObjectId="104708"/>
    <cge:TPSR_Ref TObjectID="20674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104694">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 932.332046 -337.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20660" ObjectName="SW-CX_WJ.CX_WJ_356BK"/>
     <cge:Meas_Ref ObjectId="104694"/>
    <cge:TPSR_Ref TObjectID="20660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104710">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1242.024096 -332.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20676" ObjectName="SW-CX_WJ.CX_WJ_334BK"/>
     <cge:Meas_Ref ObjectId="104710"/>
    <cge:TPSR_Ref TObjectID="20676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 -881.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1856.000000 -881.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1803.000000 -881.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1698.000000 -736.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_WJ.CX_WJ_3IVM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-455 2029,-455 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20688" ObjectName="BS-CX_WJ.CX_WJ_3IVM"/>
    <cge:TPSR_Ref TObjectID="20688"/></metadata>
   <polyline fill="none" opacity="0" points="1312,-455 2029,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WJ.CX_WJ_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="366,-879 1440,-879 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20686" ObjectName="BS-CX_WJ.CX_WJ_2IM"/>
    <cge:TPSR_Ref TObjectID="20686"/></metadata>
   <polyline fill="none" opacity="0" points="366,-879 1440,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WJ.CX_WJ_3IIIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="354,-455 1271,-455 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20687" ObjectName="BS-CX_WJ.CX_WJ_3IIIM"/>
    <cge:TPSR_Ref TObjectID="20687"/></metadata>
   <polyline fill="none" opacity="0" points="354,-455 1271,-455 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a78d10">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 -1010.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_219b820">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.176563 -105.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_fe4a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -540.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_202e8c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 405.000000 -521.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23de6d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1627.000000 -539.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11fab30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -520.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_169b920">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 372.698795 -162.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_217a6b0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -1116.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_241a900">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 -526.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f66790">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1355.000000 -524.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f58f00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 384.698795 -229.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_293a330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 499.012048 -229.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d7aca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 596.096386 -229.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_169a4d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 693.915663 -229.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_193bb70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 802.819277 -229.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a7f850">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1204.140419 -221.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_fe6f20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.662651 -190.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_eece40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1111.662651 -136.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_fd2e10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -84.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c9a930">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1481.000000 -233.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1911340">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 -186.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2136d20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1462.000000 -131.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ad650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1441.000000 -79.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1677fd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1589.000000 -225.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c168a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1697.000000 -225.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23b6c30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1794.000000 -225.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2459720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1897.000000 -225.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242b220">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1969.477768 -101.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242c7e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1976.000000 -158.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a46bc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1988.000000 -225.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2726930">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 932.332046 -168.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24351f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 944.332046 -229.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_245a150">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 928.332046 -104.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a31e80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1254.024096 -313.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-105420" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -535.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105420" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20687"/>
     <cge:Term_Ref ObjectID="28734"/>
    <cge:TPSR_Ref TObjectID="20687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-105421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -535.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20687"/>
     <cge:Term_Ref ObjectID="28734"/>
    <cge:TPSR_Ref TObjectID="20687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-105422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -535.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20687"/>
     <cge:Term_Ref ObjectID="28734"/>
    <cge:TPSR_Ref TObjectID="20687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-105423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -535.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20687"/>
     <cge:Term_Ref ObjectID="28734"/>
    <cge:TPSR_Ref TObjectID="20687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-105419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -535.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20687"/>
     <cge:Term_Ref ObjectID="28734"/>
    <cge:TPSR_Ref TObjectID="20687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105377" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 -1002.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105377" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20625"/>
     <cge:Term_Ref ObjectID="28609"/>
    <cge:TPSR_Ref TObjectID="20625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105378" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 -1002.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105378" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20625"/>
     <cge:Term_Ref ObjectID="28609"/>
    <cge:TPSR_Ref TObjectID="20625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105376" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 -1002.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20625"/>
     <cge:Term_Ref ObjectID="28609"/>
    <cge:TPSR_Ref TObjectID="20625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105381" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -818.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20631"/>
     <cge:Term_Ref ObjectID="28621"/>
    <cge:TPSR_Ref TObjectID="20631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105382" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -818.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105382" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20631"/>
     <cge:Term_Ref ObjectID="28621"/>
    <cge:TPSR_Ref TObjectID="20631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105380" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -818.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105380" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20631"/>
     <cge:Term_Ref ObjectID="28621"/>
    <cge:TPSR_Ref TObjectID="20631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105390" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -553.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105390" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20646"/>
     <cge:Term_Ref ObjectID="28651"/>
    <cge:TPSR_Ref TObjectID="20646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -553.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20646"/>
     <cge:Term_Ref ObjectID="28651"/>
    <cge:TPSR_Ref TObjectID="20646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105389" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -553.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20646"/>
     <cge:Term_Ref ObjectID="28651"/>
    <cge:TPSR_Ref TObjectID="20646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105398" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 -820.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20637"/>
     <cge:Term_Ref ObjectID="28633"/>
    <cge:TPSR_Ref TObjectID="20637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 -820.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20637"/>
     <cge:Term_Ref ObjectID="28633"/>
    <cge:TPSR_Ref TObjectID="20637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105397" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 -820.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20637"/>
     <cge:Term_Ref ObjectID="28633"/>
    <cge:TPSR_Ref TObjectID="20637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 -547.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20648"/>
     <cge:Term_Ref ObjectID="28655"/>
    <cge:TPSR_Ref TObjectID="20648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 -547.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20648"/>
     <cge:Term_Ref ObjectID="28655"/>
    <cge:TPSR_Ref TObjectID="20648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 -547.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20648"/>
     <cge:Term_Ref ObjectID="28655"/>
    <cge:TPSR_Ref TObjectID="20648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 362.000000 -46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20650"/>
     <cge:Term_Ref ObjectID="28659"/>
    <cge:TPSR_Ref TObjectID="20650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 362.000000 -46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20650"/>
     <cge:Term_Ref ObjectID="28659"/>
    <cge:TPSR_Ref TObjectID="20650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105429" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 362.000000 -46.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105429" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20650"/>
     <cge:Term_Ref ObjectID="28659"/>
    <cge:TPSR_Ref TObjectID="20650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 465.000000 -35.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20652"/>
     <cge:Term_Ref ObjectID="28663"/>
    <cge:TPSR_Ref TObjectID="20652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 465.000000 -35.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20652"/>
     <cge:Term_Ref ObjectID="28663"/>
    <cge:TPSR_Ref TObjectID="20652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 465.000000 -35.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20652"/>
     <cge:Term_Ref ObjectID="28663"/>
    <cge:TPSR_Ref TObjectID="20652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20654"/>
     <cge:Term_Ref ObjectID="28667"/>
    <cge:TPSR_Ref TObjectID="20654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20654"/>
     <cge:Term_Ref ObjectID="28667"/>
    <cge:TPSR_Ref TObjectID="20654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20654"/>
     <cge:Term_Ref ObjectID="28667"/>
    <cge:TPSR_Ref TObjectID="20654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20656"/>
     <cge:Term_Ref ObjectID="28671"/>
    <cge:TPSR_Ref TObjectID="20656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20656"/>
     <cge:Term_Ref ObjectID="28671"/>
    <cge:TPSR_Ref TObjectID="20656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20656"/>
     <cge:Term_Ref ObjectID="28671"/>
    <cge:TPSR_Ref TObjectID="20656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -37.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20658"/>
     <cge:Term_Ref ObjectID="28675"/>
    <cge:TPSR_Ref TObjectID="20658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -37.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20658"/>
     <cge:Term_Ref ObjectID="28675"/>
    <cge:TPSR_Ref TObjectID="20658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -37.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20658"/>
     <cge:Term_Ref ObjectID="28675"/>
    <cge:TPSR_Ref TObjectID="20658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105438" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 -49.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105438" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20660"/>
     <cge:Term_Ref ObjectID="28679"/>
    <cge:TPSR_Ref TObjectID="20660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 -49.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20660"/>
     <cge:Term_Ref ObjectID="28679"/>
    <cge:TPSR_Ref TObjectID="20660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 -49.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20660"/>
     <cge:Term_Ref ObjectID="28679"/>
    <cge:TPSR_Ref TObjectID="20660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.000000 -47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20662"/>
     <cge:Term_Ref ObjectID="28683"/>
    <cge:TPSR_Ref TObjectID="20662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.000000 -47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20662"/>
     <cge:Term_Ref ObjectID="28683"/>
    <cge:TPSR_Ref TObjectID="20662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.000000 -47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20662"/>
     <cge:Term_Ref ObjectID="28683"/>
    <cge:TPSR_Ref TObjectID="20662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -292.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20676"/>
     <cge:Term_Ref ObjectID="28711"/>
    <cge:TPSR_Ref TObjectID="20676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -292.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20676"/>
     <cge:Term_Ref ObjectID="28711"/>
    <cge:TPSR_Ref TObjectID="20676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -292.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20676"/>
     <cge:Term_Ref ObjectID="28711"/>
    <cge:TPSR_Ref TObjectID="20676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1447.000000 -47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20664"/>
     <cge:Term_Ref ObjectID="28687"/>
    <cge:TPSR_Ref TObjectID="20664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1447.000000 -47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20664"/>
     <cge:Term_Ref ObjectID="28687"/>
    <cge:TPSR_Ref TObjectID="20664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1447.000000 -47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20664"/>
     <cge:Term_Ref ObjectID="28687"/>
    <cge:TPSR_Ref TObjectID="20664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 -30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20666"/>
     <cge:Term_Ref ObjectID="28691"/>
    <cge:TPSR_Ref TObjectID="20666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 -30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20666"/>
     <cge:Term_Ref ObjectID="28691"/>
    <cge:TPSR_Ref TObjectID="20666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 -30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20666"/>
     <cge:Term_Ref ObjectID="28691"/>
    <cge:TPSR_Ref TObjectID="20666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1667.000000 -28.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20668"/>
     <cge:Term_Ref ObjectID="28695"/>
    <cge:TPSR_Ref TObjectID="20668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1667.000000 -28.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20668"/>
     <cge:Term_Ref ObjectID="28695"/>
    <cge:TPSR_Ref TObjectID="20668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1667.000000 -28.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20668"/>
     <cge:Term_Ref ObjectID="28695"/>
    <cge:TPSR_Ref TObjectID="20668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 -28.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20670"/>
     <cge:Term_Ref ObjectID="28699"/>
    <cge:TPSR_Ref TObjectID="20670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 -28.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20670"/>
     <cge:Term_Ref ObjectID="28699"/>
    <cge:TPSR_Ref TObjectID="20670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 -28.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20670"/>
     <cge:Term_Ref ObjectID="28699"/>
    <cge:TPSR_Ref TObjectID="20670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 -28.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20672"/>
     <cge:Term_Ref ObjectID="28703"/>
    <cge:TPSR_Ref TObjectID="20672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 -28.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20672"/>
     <cge:Term_Ref ObjectID="28703"/>
    <cge:TPSR_Ref TObjectID="20672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 -28.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20672"/>
     <cge:Term_Ref ObjectID="28703"/>
    <cge:TPSR_Ref TObjectID="20672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-105434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1958.000000 -46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20674"/>
     <cge:Term_Ref ObjectID="28707"/>
    <cge:TPSR_Ref TObjectID="20674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-105435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1958.000000 -46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20674"/>
     <cge:Term_Ref ObjectID="28707"/>
    <cge:TPSR_Ref TObjectID="20674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-105433" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1958.000000 -46.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20674"/>
     <cge:Term_Ref ObjectID="28707"/>
    <cge:TPSR_Ref TObjectID="20674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-105425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1839.000000 -538.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105425" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20688"/>
     <cge:Term_Ref ObjectID="28735"/>
    <cge:TPSR_Ref TObjectID="20688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-105426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1839.000000 -538.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20688"/>
     <cge:Term_Ref ObjectID="28735"/>
    <cge:TPSR_Ref TObjectID="20688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-105427" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1839.000000 -538.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105427" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20688"/>
     <cge:Term_Ref ObjectID="28735"/>
    <cge:TPSR_Ref TObjectID="20688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-105428" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1839.000000 -538.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105428" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20688"/>
     <cge:Term_Ref ObjectID="28735"/>
    <cge:TPSR_Ref TObjectID="20688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-105424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1839.000000 -538.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20688"/>
     <cge:Term_Ref ObjectID="28735"/>
    <cge:TPSR_Ref TObjectID="20688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-105415" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -957.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105415" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20686"/>
     <cge:Term_Ref ObjectID="28733"/>
    <cge:TPSR_Ref TObjectID="20686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-105416" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -957.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105416" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20686"/>
     <cge:Term_Ref ObjectID="28733"/>
    <cge:TPSR_Ref TObjectID="20686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-105417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -957.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20686"/>
     <cge:Term_Ref ObjectID="28733"/>
    <cge:TPSR_Ref TObjectID="20686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-105418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -957.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20686"/>
     <cge:Term_Ref ObjectID="28733"/>
    <cge:TPSR_Ref TObjectID="20686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-105414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -957.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20686"/>
     <cge:Term_Ref ObjectID="28733"/>
    <cge:TPSR_Ref TObjectID="20686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-105388" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -655.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20683"/>
     <cge:Term_Ref ObjectID="28728"/>
    <cge:TPSR_Ref TObjectID="20683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-105405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -653.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20684"/>
     <cge:Term_Ref ObjectID="28732"/>
    <cge:TPSR_Ref TObjectID="20684"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/></g>
   <g href="cx_索引_接线图_省调直调电厂_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1653" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1653" cy="-992" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1813" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1866" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1708" cy="-853" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1866" cy="-992" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_11e8560">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 612.000000 -1118.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_186f750">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 405.000000 -583.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d532d0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1085.000000 -1034.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a39640">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -582.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 78.000000 -1150.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116480" ratioFlag="0">
    <text fill="rgb(127,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -1035.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116480" ObjectName="CX_WJ:CX_WJ_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116481" ratioFlag="0">
    <text fill="rgb(127,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 139.000000 -955.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116481" ObjectName="CX_WJ:CX_WJ_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1653,-1113 1648,-1124 1658,-1124 1653,-1113 1653,-1114 1653,-1113 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1866,-1113 1861,-1124 1871,-1124 1866,-1113 1866,-1114 1866,-1113 " stroke="rgb(60,120,255)"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="151" x="90" y="-1209"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="41" y="-1226"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_WJ.CX_WJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28727"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 -615.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 -615.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20683" ObjectName="TF-CX_WJ.CX_WJ_1T"/>
    <cge:TPSR_Ref TObjectID="20683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_WJ.CX_WJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28731"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1355.000000 -613.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1355.000000 -613.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20684" ObjectName="TF-CX_WJ.CX_WJ_2T"/>
    <cge:TPSR_Ref TObjectID="20684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1637.000000 -999.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1637.000000 -999.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1850.000000 -999.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1850.000000 -999.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_WJ"/>
</svg>