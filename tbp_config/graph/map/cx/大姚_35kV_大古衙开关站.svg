<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-178" aopId="3934726" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -957 2122 1292">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="60" x2="21" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="6" x2="6" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="4" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="6" y2="9"/>
    <rect height="13" stroke-width="0.424575" width="29" x="15" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape187">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="56" y2="47"/>
    <polyline arcFlag="1" points="6,36 5,36 5,36 4,36 3,36 3,37 2,37 1,38 1,38 1,39 0,40 0,40 0,41 0,42 0,43 0,43 1,44 1,45 1,45 2,46 3,46 3,47 4,47 5,47 5,47 6,47 " stroke-width="0.171589"/>
    <polyline arcFlag="1" points="6,25 5,25 5,25 4,25 3,25 3,26 2,26 1,27 1,27 1,28 0,29 0,29 0,30 0,31 0,32 0,32 1,33 1,34 1,34 2,35 3,35 3,36 4,36 5,36 5,36 6,36 " stroke-width="0.171589"/>
    <polyline arcFlag="1" points="6,14 5,14 5,14 4,14 3,14 3,15 2,15 1,16 1,16 1,17 0,18 0,18 0,19 0,20 0,21 0,21 1,22 1,23 1,23 2,24 3,24 3,25 4,25 5,25 5,25 6,25 " stroke-width="0.171589"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape183">
    <polyline arcFlag="1" points="5,29 7,29 8,29 9,29 10,28 11,28 12,27 13,26 14,25 15,24 16,22 16,21 16,19 17,18 17,16 16,15 16,13 16,12 15,10 14,9 13,8 12,7 11,6 10,5 9,5 8,5 7,5 5,5 " stroke-width="0.402481"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_4a240f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape27_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 22,27 10,27 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="10" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape27_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,87 10,75 21,75 15,87 15,86 15,87 "/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape62_0">
    <circle cx="16" cy="58" fillStyle="0" r="16" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,58 41,58 41,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="53" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="16,15 22,28 10,28 16,15 16,16 16,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="58" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="58" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="58" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape62_1">
    <circle cx="16" cy="82" fillStyle="0" r="16" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="78" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="12" y1="86" y2="86"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="voltageTransformer:shape96">
    <circle cx="41" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="2" y1="39" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="25" y2="25"/>
    <polyline DF8003:Layer="PUBLIC" points="13,25 17,26 17,24 13,25 13,25 13,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="17" y1="25" y2="25"/>
    <polyline DF8003:Layer="PUBLIC" points="12,25 8,26 8,24 12,25 12,25 12,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="54" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="59" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="63" x2="57" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="46" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="11" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="42" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="42" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="60,20 61,23 59,23 60,20 60,20 60,20 "/>
    <polyline DF8003:Layer="PUBLIC" points="60,18 61,15 59,15 60,18 60,18 60,18 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="27" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="39" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="2" y1="11" y2="11"/>
    <circle cx="41" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="42" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="42" y1="14" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="44" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="23" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="27" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="42" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="42" y1="29" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="45" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="29" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="32" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="29" y1="14" y2="11"/>
    <circle cx="29" cy="12" r="7.5" stroke-width="1"/>
    <circle cx="29" cy="24" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30be890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30bf2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30bfc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30c0900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30c1b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30c2710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c2f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c3920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c41f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c4bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c56c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c5d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c62e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c6c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c7590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30c7ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30c9690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30ca1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30ca930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30cb320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30cc500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30cce80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30cd970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30cf3a0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30cff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30d0910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30e0580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_30d3240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_30d63c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1302" width="2132" x="-335" y="-962"/>
  </g><g id="Line_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1682,-721 1711,-721 1711,-712 1683,-692 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-632 1436,-620 1448,-620 1442,-632 1442,-631 1442,-632 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1683,-632 1677,-620 1689,-620 1683,-632 1683,-631 1683,-632 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1682,-786 1688,-799 1676,-799 1682,-786 1682,-787 1682,-786 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-787 1448,-800 1436,-800 1442,-787 1442,-788 1442,-787 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-701 718,-714 706,-714 712,-701 712,-702 712,-701 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" points="712,-870 715,-856 708,-856 712,-870 712,-869 712,-870 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="567,65 573,52 561,52 567,65 567,64 567,65 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="567,76 573,89 561,89 567,76 567,77 567,76 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,68 1174,81 1162,81 1168,68 1168,69 1168,68 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,57 1174,44 1162,44 1168,57 1168,56 1168,57 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-603 706,-591 718,-591 712,-603 712,-602 712,-603 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="737,65 743,52 731,52 737,65 737,64 737,65 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="737,76 743,89 731,89 737,76 737,77 737,76 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="918,65 924,52 912,52 918,65 918,64 918,65 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="918,76 924,89 912,89 918,76 918,77 918,76 " stroke="rgb(255,255,0)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127555">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.000000 -50.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23605" ObjectName="SW-CX_DGY.CX_DGY_361BK"/>
     <cge:Meas_Ref ObjectId="127555"/>
    <cge:TPSR_Ref TObjectID="23605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127548">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 -270.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23601" ObjectName="SW-CX_DGY.CX_DGY_351BK"/>
     <cge:Meas_Ref ObjectId="127548"/>
    <cge:TPSR_Ref TObjectID="23601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.050000 -0.000000 0.000000 -0.990196 1432.000000 -486.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.050000 -0.000000 0.000000 -0.990196 1673.000000 -486.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -50.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23609" ObjectName="SW-CX_DGY.CX_DGY_362BK"/>
     <cge:Meas_Ref ObjectId="127562"/>
    <cge:TPSR_Ref TObjectID="23609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127569">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -50.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23613" ObjectName="SW-CX_DGY.CX_DGY_363BK"/>
     <cge:Meas_Ref ObjectId="127569"/>
    <cge:TPSR_Ref TObjectID="23613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 -50.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23619" ObjectName="SW-CX_DGY.CX_DGY_364BK"/>
     <cge:Meas_Ref ObjectId="127577"/>
    <cge:TPSR_Ref TObjectID="23619"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d99640">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -518.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_322c2d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 -301.000000)" xlink:href="#voltageTransformer:shape96"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.000000 129.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 129.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_330e880" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1722.000000 -446.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_46fc2b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 491.000000 27.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a6a060" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 661.000000 27.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_492d2f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 27.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4b7b770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 988.000000 159.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4b7c400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 27.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4b7fa30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1093.000000 241.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4b88990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1367.000000 -611.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a245e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 -443.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_46938e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,-148 567,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23604@1" ObjectIDZND0="23596@0" Pin0InfoVect0LinkObjId="g_31cfa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127554_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="567,-148 567,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_49230a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,153 918,166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="23615@x" ObjectIDND1="23614@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127571_0" Pin1InfoVect1LinkObjId="SW-127570_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,153 918,166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31cf120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,153 918,153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="23615@0" ObjectIDZND0="0@x" ObjectIDZND1="23614@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-127570_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127571_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="946,153 918,153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e955e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,153 918,137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="23615@x" ObjectIDZND0="23614@0" Pin0InfoVect0LinkObjId="SW-127570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-127571_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,153 918,137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d8f730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,218 918,208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_4a23980@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a23980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,218 918,208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31cfa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-146 1168,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23618@1" ObjectIDZND0="23596@0" Pin0InfoVect0LinkObjId="g_46938e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127576_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-146 1168,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4708bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-212 712,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23600@0" ObjectIDZND0="23596@0" Pin0InfoVect0LinkObjId="g_46938e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127547_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-212 712,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32264e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-758 1442,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-758 1442,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_46e3300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1682,-758 1682,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1682,-758 1682,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3513b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-249 712,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23600@1" ObjectIDZND0="23601@0" Pin0InfoVect0LinkObjId="SW-127548_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127547_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-249 712,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_47084f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-306 712,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23601@1" ObjectIDZND0="23599@0" Pin0InfoVect0LinkObjId="SW-127546_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127548_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-306 712,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e432a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-526 712,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2d99640@0" ObjectIDZND0="g_4707640@0" ObjectIDZND1="23599@x" ObjectIDZND2="23598@x" Pin0InfoVect0LinkObjId="g_4707640_0" Pin0InfoVect1LinkObjId="SW-127546_0" Pin0InfoVect2LinkObjId="SW-127545_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d99640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="773,-526 712,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30930d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-172 416,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23596@0" ObjectIDZND0="23621@0" Pin0InfoVect0LinkObjId="SW-127581_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_46938e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-172 416,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30942b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-239 475,-239 475,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_322b6b0@0" ObjectIDND1="23621@x" ObjectIDZND0="g_334fab0@0" Pin0InfoVect0LinkObjId="g_334fab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_322b6b0_0" Pin1InfoVect1LinkObjId="SW-127581_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-239 475,-239 475,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4708930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,153 983,153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4b7b770@0" ObjectIDZND0="23615@1" Pin0InfoVect0LinkObjId="SW-127571_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4b7b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,153 983,153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331ef70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,-86 567,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23605@1" ObjectIDZND0="23604@0" Pin0InfoVect0LinkObjId="SW-127554_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127555_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="567,-86 567,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b78ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-86 1168,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23619@1" ObjectIDZND0="23618@0" Pin0InfoVect0LinkObjId="SW-127576_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127577_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-86 1168,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a83540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1099,223 1099,214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_4b7fa30@0" ObjectIDZND0="g_2df3780@1" Pin0InfoVect0LinkObjId="g_2df3780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4b7fa30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1099,223 1099,214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_46fb690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1728,-440 1728,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_330e880@0" Pin0InfoVect0LinkObjId="g_330e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1728,-440 1728,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3335230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-493 1442,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2e10510@0" Pin0InfoVect0LinkObjId="g_2e10510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-493 1442,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a5230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-428 1442,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2e10510@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e10510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-428 1442,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_322f040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1683,-493 1683,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_4a6bb40@0" Pin0InfoVect0LinkObjId="g_4a6bb40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1683,-493 1683,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a3a730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1683,-428 1683,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_4a6bb40@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a6bb40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1683,-428 1683,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32567e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-598 1489,-575 1489,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-598 1489,-575 1489,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d4780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-584 1442,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-584 1442,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4698a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-702 1442,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="busSection" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-702 1442,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d9f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1683,-598 1636,-575 1636,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1683,-598 1636,-575 1636,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_470ab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1683,-584 1683,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1683,-584 1683,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4709c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1683,-598 1683,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1683,-598 1683,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b8df20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,-59 567,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23605@0" ObjectIDZND0="23603@1" Pin0InfoVect0LinkObjId="SW-127553_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="567,-59 567,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f447c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-36 1168,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23617@1" ObjectIDZND0="23619@0" Pin0InfoVect0LinkObjId="SW-127577_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127575_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-36 1168,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_494a540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="711,-430 691,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d99640@0" ObjectIDND1="g_46987e0@0" ObjectIDND2="23598@x" ObjectIDZND0="g_4707640@0" Pin0InfoVect0LinkObjId="g_4707640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d99640_0" Pin1InfoVect1LinkObjId="g_46987e0_0" Pin1InfoVect2LinkObjId="SW-127545_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="711,-430 691,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_46fe600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-526 712,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2d99640@0" ObjectIDND1="g_46987e0@0" ObjectIDZND0="g_4707640@0" ObjectIDZND1="23599@x" ObjectIDZND2="23598@x" Pin0InfoVect0LinkObjId="g_4707640_0" Pin0InfoVect1LinkObjId="SW-127546_0" Pin0InfoVect2LinkObjId="SW-127545_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d99640_0" Pin1InfoVect1LinkObjId="g_46987e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="712,-526 712,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_46fd260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="782,-449 774,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4a245e0@0" ObjectIDZND0="23598@1" Pin0InfoVect0LinkObjId="SW-127545_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a245e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="782,-449 774,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4697220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="738,-449 712,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23598@0" ObjectIDZND0="g_2d99640@0" ObjectIDZND1="g_46987e0@0" ObjectIDZND2="g_4707640@0" Pin0InfoVect0LinkObjId="g_2d99640_0" Pin0InfoVect1LinkObjId="g_46987e0_0" Pin0InfoVect2LinkObjId="g_4707640_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127545_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="738,-449 712,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31cf3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-449 712,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2d99640@0" ObjectIDND1="g_46987e0@0" ObjectIDND2="23598@x" ObjectIDZND0="g_4707640@0" ObjectIDZND1="23599@x" Pin0InfoVect0LinkObjId="g_4707640_0" Pin0InfoVect1LinkObjId="SW-127546_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d99640_0" Pin1InfoVect1LinkObjId="g_46987e0_0" Pin1InfoVect2LinkObjId="SW-127545_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,-449 712,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a29d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-430 712,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_4707640@0" ObjectIDND1="g_2d99640@0" ObjectIDND2="g_46987e0@0" ObjectIDZND0="23599@1" Pin0InfoVect0LinkObjId="SW-127546_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4707640_0" Pin1InfoVect1LinkObjId="g_2d99640_0" Pin1InfoVect2LinkObjId="g_46987e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-430 712,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4923320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="756,-765 712,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_46987e0@0" ObjectIDZND0="g_2d99640@0" ObjectIDZND1="g_4707640@0" ObjectIDZND2="23599@x" Pin0InfoVect0LinkObjId="g_2d99640_0" Pin0InfoVect1LinkObjId="g_4707640_0" Pin0InfoVect2LinkObjId="SW-127546_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_46987e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="756,-765 712,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_473bca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-856 712,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDZND0="g_46987e0@0" ObjectIDZND1="g_2d99640@0" ObjectIDZND2="g_4707640@0" Pin0InfoVect0LinkObjId="g_46987e0_0" Pin0InfoVect1LinkObjId="g_2d99640_0" Pin0InfoVect2LinkObjId="g_4707640_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="712,-856 712,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_325af60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-765 712,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_46987e0@0" ObjectIDZND0="g_2d99640@0" ObjectIDZND1="g_4707640@0" ObjectIDZND2="23599@x" Pin0InfoVect0LinkObjId="g_2d99640_0" Pin0InfoVect1LinkObjId="g_4707640_0" Pin0InfoVect2LinkObjId="SW-127546_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_46987e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="712,-765 712,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4936eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-286 416,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_322b6b0@0" ObjectIDZND0="g_322c2d0@0" Pin0InfoVect0LinkObjId="g_322c2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_322b6b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-286 416,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33afb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-222 416,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="23621@1" ObjectIDZND0="g_322b6b0@0" ObjectIDZND1="g_334fab0@0" Pin0InfoVect0LinkObjId="g_322b6b0_0" Pin0InfoVect1LinkObjId="g_334fab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127581_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="416,-222 416,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334f850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-239 416,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_334fab0@0" ObjectIDND1="23621@x" ObjectIDZND0="g_322b6b0@1" Pin0InfoVect0LinkObjId="g_322b6b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_334fab0_0" Pin1InfoVect1LinkObjId="SW-127581_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-239 416,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3342fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="594,29 567,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_476b2f0@0" ObjectIDZND0="0@x" ObjectIDZND1="23602@x" ObjectIDZND2="23603@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-127552_0" Pin0InfoVect2LinkObjId="SW-127553_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_476b2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="594,29 567,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_46f01e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,29 567,102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_476b2f0@0" ObjectIDND1="23602@x" ObjectIDND2="23603@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_476b2f0_0" Pin1InfoVect1LinkObjId="SW-127552_0" Pin1InfoVect2LinkObjId="SW-127553_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="567,29 567,102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3341c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,0 567,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="23603@0" ObjectIDZND0="g_476b2f0@0" ObjectIDZND1="0@x" ObjectIDZND2="23602@x" Pin0InfoVect0LinkObjId="g_476b2f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-127552_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127553_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="567,0 567,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3324760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,21 567,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="23602@x" ObjectIDND1="23603@x" ObjectIDZND0="g_476b2f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_476b2f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127552_0" Pin1InfoVect1LinkObjId="SW-127553_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="567,21 567,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_49312f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="509,21 522,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_46fc2b0@0" ObjectIDZND0="23602@0" Pin0InfoVect0LinkObjId="SW-127552_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_46fc2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="509,21 522,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31277d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,21 567,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="23602@1" ObjectIDZND0="g_476b2f0@0" ObjectIDZND1="0@x" ObjectIDZND2="23603@x" Pin0InfoVect0LinkObjId="g_476b2f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-127553_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127552_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="558,21 567,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4ba7820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,-148 737,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23608@1" ObjectIDZND0="23596@0" Pin0InfoVect0LinkObjId="g_46938e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127561_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,-148 737,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_469cb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,-86 737,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23609@1" ObjectIDZND0="23608@0" Pin0InfoVect0LinkObjId="SW-127561_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,-86 737,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ed8b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,-59 737,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23609@0" ObjectIDZND0="23607@1" Pin0InfoVect0LinkObjId="SW-127560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,-59 737,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_322ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="764,29 737,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_325a850@0" ObjectIDZND0="0@x" ObjectIDZND1="23606@x" ObjectIDZND2="23607@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-127559_0" Pin0InfoVect2LinkObjId="SW-127560_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_325a850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="764,29 737,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_322bcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,29 737,102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_325a850@0" ObjectIDND1="23606@x" ObjectIDND2="23607@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_325a850_0" Pin1InfoVect1LinkObjId="SW-127559_0" Pin1InfoVect2LinkObjId="SW-127560_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,29 737,102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f793c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,0 737,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23607@0" ObjectIDZND0="0@x" ObjectIDZND1="g_325a850@0" ObjectIDZND2="23606@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_325a850_0" Pin0InfoVect2LinkObjId="SW-127559_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="737,0 737,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f79620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,21 737,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="23606@x" ObjectIDND1="23607@x" ObjectIDZND0="0@x" ObjectIDZND1="g_325a850@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_325a850_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127559_0" Pin1InfoVect1LinkObjId="SW-127560_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="737,21 737,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f277f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="679,21 692,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4a6a060@0" ObjectIDZND0="23606@0" Pin0InfoVect0LinkObjId="SW-127559_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a6a060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="679,21 692,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f27a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="728,21 737,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23606@1" ObjectIDZND0="0@x" ObjectIDZND1="g_325a850@0" ObjectIDZND2="23607@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_325a850_0" Pin0InfoVect2LinkObjId="SW-127560_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127559_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="728,21 737,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_492a360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-148 918,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23612@1" ObjectIDZND0="23596@0" Pin0InfoVect0LinkObjId="g_46938e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127568_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-148 918,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_492a5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-86 918,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23613@1" ObjectIDZND0="23612@0" Pin0InfoVect0LinkObjId="SW-127568_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127569_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-86 918,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_492ab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-59 918,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23613@0" ObjectIDZND0="23611@1" Pin0InfoVect0LinkObjId="SW-127567_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127569_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-59 918,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b79850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="945,29 918,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_492dd80@0" ObjectIDZND0="23610@x" ObjectIDZND1="23611@x" ObjectIDZND2="23614@x" Pin0InfoVect0LinkObjId="SW-127566_0" Pin0InfoVect1LinkObjId="SW-127567_0" Pin0InfoVect2LinkObjId="SW-127570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_492dd80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="945,29 918,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b79ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,29 918,102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_492dd80@0" ObjectIDND1="23610@x" ObjectIDND2="23611@x" ObjectIDZND0="23614@1" Pin0InfoVect0LinkObjId="SW-127570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_492dd80_0" Pin1InfoVect1LinkObjId="SW-127566_0" Pin1InfoVect2LinkObjId="SW-127567_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,29 918,102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b79d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,0 918,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23611@0" ObjectIDZND0="g_492dd80@0" ObjectIDZND1="23614@x" ObjectIDZND2="23610@x" Pin0InfoVect0LinkObjId="g_492dd80_0" Pin0InfoVect1LinkObjId="SW-127570_0" Pin0InfoVect2LinkObjId="SW-127566_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="918,0 918,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b79f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,21 918,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23610@x" ObjectIDND1="23611@x" ObjectIDZND0="g_492dd80@0" ObjectIDZND1="23614@x" Pin0InfoVect0LinkObjId="g_492dd80_0" Pin0InfoVect1LinkObjId="SW-127570_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127566_0" Pin1InfoVect1LinkObjId="SW-127567_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="918,21 918,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b7a1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="860,21 873,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_492d2f0@0" ObjectIDZND0="23610@0" Pin0InfoVect0LinkObjId="SW-127566_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_492d2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="860,21 873,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b7a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,21 918,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23610@1" ObjectIDZND0="g_492dd80@0" ObjectIDZND1="23614@x" ObjectIDZND2="23611@x" Pin0InfoVect0LinkObjId="g_492dd80_0" Pin0InfoVect1LinkObjId="SW-127570_0" Pin0InfoVect2LinkObjId="SW-127567_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127566_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="909,21 918,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b7c1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1099,153 1099,163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23620@0" ObjectIDZND0="g_2df3780@0" Pin0InfoVect0LinkObjId="g_2df3780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1099,153 1099,163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b7ce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1195,29 1168,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_4b7d550@0" ObjectIDZND0="23616@x" ObjectIDZND1="23617@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-127574_0" Pin0InfoVect1LinkObjId="SW-127575_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4b7d550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1195,29 1168,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b7d090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1110,21 1123,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4b7c400@0" ObjectIDZND0="23616@0" Pin0InfoVect0LinkObjId="SW-127574_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4b7c400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1110,21 1123,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b7d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,21 1168,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="23616@1" ObjectIDZND0="g_4b7d550@0" ObjectIDZND1="0@x" ObjectIDZND2="23617@x" Pin0InfoVect0LinkObjId="g_4b7d550_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-127575_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127574_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1159,21 1168,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b7ea80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,0 1168,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="23617@0" ObjectIDZND0="g_4b7d550@0" ObjectIDZND1="0@x" ObjectIDZND2="23616@x" Pin0InfoVect0LinkObjId="g_4b7d550_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-127574_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127575_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1168,0 1168,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b7f570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,94 1168,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_4b7d550@0" ObjectIDZND1="23616@x" ObjectIDZND2="23617@x" Pin0InfoVect0LinkObjId="g_4b7d550_0" Pin0InfoVect1LinkObjId="SW-127574_0" Pin0InfoVect2LinkObjId="SW-127575_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1168,94 1168,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b7f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,29 1168,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4b7d550@0" ObjectIDND1="0@x" ObjectIDZND0="23616@x" ObjectIDZND1="23617@x" Pin0InfoVect0LinkObjId="SW-127574_0" Pin0InfoVect1LinkObjId="SW-127575_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4b7d550_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1168,29 1168,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b811b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1130,123 1130,110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_4b80480@0" ObjectIDZND0="0@x" ObjectIDZND1="23620@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-127580_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4b80480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1130,123 1130,110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b84670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1099,117 1099,110 1130,110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="23620@1" ObjectIDZND0="g_4b80480@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_4b80480_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1099,117 1099,110 1130,110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4b848d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1130,110 1169,110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_4b80480@0" ObjectIDND1="23620@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4b80480_0" Pin1InfoVect1LinkObjId="SW-127580_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1130,110 1169,110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4b884d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-629 1373,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_4b88990@0" ObjectIDZND0="g_4b877c0@1" Pin0InfoVect0LinkObjId="g_4b877c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4b88990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-629 1373,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4b88730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-699 1373,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_4b877c0@0" Pin0InfoVect0LinkObjId="g_4b877c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-699 1373,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4b893e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1404,-729 1404,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_4b8c060@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4b8c060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1404,-729 1404,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4b8b310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-735 1373,-742 1404,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_4b8c060@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4b8c060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-735 1373,-742 1404,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4b8b570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1404,-742 1443,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_4b8c060@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4b8c060_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1404,-742 1443,-742 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1728" cy="-440" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1442" cy="-402" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1683" cy="-402" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1489" cy="-440" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1636" cy="-440" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23596" cx="567" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23596" cx="1168" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23596" cx="712" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23596" cx="416" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23596" cx="737" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23596" cx="918" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-125237" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -47.000000 -849.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22923" ObjectName="DYN-CX_DGY"/>
     <cge:Meas_Ref ObjectId="125237"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e35970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 835.000000 249.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e35970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 835.000000 249.000000) translate(0,33)">      ±3MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4a1f880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 191.000000 -195.000000) translate(0,15)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_46981f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 135.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3138ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dc3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dc3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dc3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dc3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dc3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dc3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dc3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_47127c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">大古衙开关站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31de970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 155.000000) translate(0,15)">(11-20号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_325b580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.000000 -137.500000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f91350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 519.000000 -31.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4a57a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 135.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dd6090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 504.500000 155.000000) translate(0,15)">(1-10号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b93ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 188.000000) translate(0,12)">3150kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b9f340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1080.500000 244.000000) translate(0,15)">35kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b9f340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1080.500000 244.000000) translate(0,33)">  及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d9d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -235.500000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_470dd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 725.000000 -299.500000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3226b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -359.500000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a23420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 735.000000 -474.500000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3227240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -849.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3227240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -849.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3227240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -849.000000) translate(0,51)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3227240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -849.000000) translate(0,69)">古</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3227240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -849.000000) translate(0,87)">衙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3227240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -849.000000) translate(0,105)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3227240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -849.000000) translate(0,123)">接</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3227240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -849.000000) translate(0,141)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3090610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1524.500000 -394.000000) translate(0,15)">0.4kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4924bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 -869.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4924bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 -869.000000) translate(0,33)">  1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46eade0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 110.000000) translate(0,12)">630kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ed67a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.500000 -394.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ed67a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.500000 -394.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f982e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1332.500000 -786.000000) translate(0,15)">1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f982e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1332.500000 -786.000000) translate(0,33)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d90d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1627.000000 -869.000000) translate(0,15)">10kV碧新线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d90d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1627.000000 -869.000000) translate(0,33)">大古衙分支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d3320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1701.500000 -766.000000) translate(0,15)">备用所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d3320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1701.500000 -766.000000) translate(0,33)"> (施工变)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e89900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 506.000000 -0.500000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4b872d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1332.000000 -723.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a270a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -79.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a27590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -81.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a277d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 874.000000 -138.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a27a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 874.000000 -26.000000) translate(0,12)">3633</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a27c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.000000 1.000000) translate(0,12)">36337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a27e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 111.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a280d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 973.000000 128.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a28310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 695.000000 -82.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a28550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -139.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a28790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -27.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a289d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 1.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e65540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1126.000000 -84.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e65a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.000000 -29.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e65c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1101.000000 -0.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e65eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.000000 -139.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e666a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -217.000000 11.000000) translate(0,12)">4796</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e66890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 132.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e66ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 -211.000000) translate(0,12)">3901</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="922,120 965,120 965,148 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127553">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.000000 5.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23603" ObjectName="SW-CX_DGY.CX_DGY_3616SW"/>
     <cge:Meas_Ref ObjectId="127553"/>
    <cge:TPSR_Ref TObjectID="23603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127554">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.000000 -107.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23604" ObjectName="SW-CX_DGY.CX_DGY_3611SW"/>
     <cge:Meas_Ref ObjectId="127554"/>
    <cge:TPSR_Ref TObjectID="23604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127570">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.978261 910.000000 142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23614" ObjectName="SW-CX_DGY.CX_DGY_3636SW"/>
     <cge:Meas_Ref ObjectId="127570"/>
    <cge:TPSR_Ref TObjectID="23614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127576">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 -105.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23618" ObjectName="SW-CX_DGY.CX_DGY_3641SW"/>
     <cge:Meas_Ref ObjectId="127576"/>
    <cge:TPSR_Ref TObjectID="23618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127575">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 5.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23617" ObjectName="SW-CX_DGY.CX_DGY_3646SW"/>
     <cge:Meas_Ref ObjectId="127575"/>
    <cge:TPSR_Ref TObjectID="23617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127547">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 -208.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23600" ObjectName="SW-CX_DGY.CX_DGY_3511SW"/>
     <cge:Meas_Ref ObjectId="127547"/>
    <cge:TPSR_Ref TObjectID="23600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127546">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 -334.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23599" ObjectName="SW-CX_DGY.CX_DGY_3516SW"/>
     <cge:Meas_Ref ObjectId="127546"/>
    <cge:TPSR_Ref TObjectID="23599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127580">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1090.000000 158.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23620" ObjectName="SW-CX_DGY.CX_DGY_3010SW"/>
     <cge:Meas_Ref ObjectId="127580"/>
    <cge:TPSR_Ref TObjectID="23620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127581">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.000000 -181.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23621" ObjectName="SW-CX_DGY.CX_DGY_3901SW"/>
     <cge:Meas_Ref ObjectId="127581"/>
    <cge:TPSR_Ref TObjectID="23621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127545">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 733.000000 -444.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23598" ObjectName="SW-CX_DGY.CX_DGY_35167SW"/>
     <cge:Meas_Ref ObjectId="127545"/>
    <cge:TPSR_Ref TObjectID="23598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127571">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.000000 158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23615" ObjectName="SW-CX_DGY.CX_DGY_36367SW"/>
     <cge:Meas_Ref ObjectId="127571"/>
    <cge:TPSR_Ref TObjectID="23615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127552">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 517.000000 26.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23602" ObjectName="SW-CX_DGY.CX_DGY_36167SW"/>
     <cge:Meas_Ref ObjectId="127552"/>
    <cge:TPSR_Ref TObjectID="23602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127560">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 5.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23607" ObjectName="SW-CX_DGY.CX_DGY_3626SW"/>
     <cge:Meas_Ref ObjectId="127560"/>
    <cge:TPSR_Ref TObjectID="23607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127561">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -107.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23608" ObjectName="SW-CX_DGY.CX_DGY_3621SW"/>
     <cge:Meas_Ref ObjectId="127561"/>
    <cge:TPSR_Ref TObjectID="23608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127559">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 687.000000 26.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23606" ObjectName="SW-CX_DGY.CX_DGY_36267SW"/>
     <cge:Meas_Ref ObjectId="127559"/>
    <cge:TPSR_Ref TObjectID="23606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127567">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 5.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23611" ObjectName="SW-CX_DGY.CX_DGY_3633SW"/>
     <cge:Meas_Ref ObjectId="127567"/>
    <cge:TPSR_Ref TObjectID="23611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -107.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23612" ObjectName="SW-CX_DGY.CX_DGY_3631SW"/>
     <cge:Meas_Ref ObjectId="127568"/>
    <cge:TPSR_Ref TObjectID="23612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127566">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 868.000000 26.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23610" ObjectName="SW-CX_DGY.CX_DGY_36337SW"/>
     <cge:Meas_Ref ObjectId="127566"/>
    <cge:TPSR_Ref TObjectID="23610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1364.000000 -694.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127574">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 26.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23616" ObjectName="SW-CX_DGY.CX_DGY_36467SW"/>
     <cge:Meas_Ref ObjectId="127574"/>
    <cge:TPSR_Ref TObjectID="23616"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 -664.000000)" xlink:href="#transformer2:shape27_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 -664.000000)" xlink:href="#transformer2:shape27_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 213.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 213.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1152.000000 192.000000)" xlink:href="#transformer2:shape62_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1152.000000 192.000000)" xlink:href="#transformer2:shape62_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1426.000000 -660.000000)" xlink:href="#transformer2:shape62_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1426.000000 -660.000000)" xlink:href="#transformer2:shape62_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127516" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -209.461538 -748.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127516" ObjectName="CX_DGY:CX_DGY_351BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127517" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -210.461538 -707.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127517" ObjectName="CX_DGY:CX_DGY_351BK_Q"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c848a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.000000 -186.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_325cbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 689.000000 -201.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46eeb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -216.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_325d690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 502.000000 -186.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3341530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 491.000000 -201.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3515550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 -216.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2baeda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.000000 -289.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4710030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 853.000000 -304.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46a09f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 878.000000 -319.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d6cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -285.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f41740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1081.000000 -300.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ba5bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -315.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a18640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 318.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dc010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 303.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_334f5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 795.000000 288.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3124ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 229.666667) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33af890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 183.000000 216.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3127520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 191.000000 271.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3125730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 191.000000 243.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4931040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 191.000000 257.333333) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DGY.CX_DGY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,-172 1376,-172 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23596" ObjectName="BS-CX_DGY.CX_DGY_3IM"/>
    <cge:TPSR_Ref TObjectID="23596"/></metadata>
   <polyline fill="none" opacity="0" points="187,-172 1376,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-440 1742,-440 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1399,-440 1742,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-402 1742,-402 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1399,-402 1742,-402 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-127516" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 -317.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23601"/>
     <cge:Term_Ref ObjectID="33361"/>
    <cge:TPSR_Ref TObjectID="23601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-127517" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 -317.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23601"/>
     <cge:Term_Ref ObjectID="33361"/>
    <cge:TPSR_Ref TObjectID="23601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-127513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 -317.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23601"/>
     <cge:Term_Ref ObjectID="33361"/>
    <cge:TPSR_Ref TObjectID="23601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-127523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 555.000000 188.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23605"/>
     <cge:Term_Ref ObjectID="33369"/>
    <cge:TPSR_Ref TObjectID="23605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-127524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 555.000000 188.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23605"/>
     <cge:Term_Ref ObjectID="33369"/>
    <cge:TPSR_Ref TObjectID="23605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-127520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 555.000000 188.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23605"/>
     <cge:Term_Ref ObjectID="33369"/>
    <cge:TPSR_Ref TObjectID="23605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-127529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 756.000000 188.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23609"/>
     <cge:Term_Ref ObjectID="33377"/>
    <cge:TPSR_Ref TObjectID="23609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-127530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 756.000000 188.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23609"/>
     <cge:Term_Ref ObjectID="33377"/>
    <cge:TPSR_Ref TObjectID="23609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-127526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 756.000000 188.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23609"/>
     <cge:Term_Ref ObjectID="33377"/>
    <cge:TPSR_Ref TObjectID="23609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-127535" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 917.000000 290.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23613"/>
     <cge:Term_Ref ObjectID="33385"/>
    <cge:TPSR_Ref TObjectID="23613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-127536" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 917.000000 290.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23613"/>
     <cge:Term_Ref ObjectID="33385"/>
    <cge:TPSR_Ref TObjectID="23613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-127532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 917.000000 290.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23613"/>
     <cge:Term_Ref ObjectID="33385"/>
    <cge:TPSR_Ref TObjectID="23613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-127541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1144.000000 287.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23619"/>
     <cge:Term_Ref ObjectID="33397"/>
    <cge:TPSR_Ref TObjectID="23619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-127542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1144.000000 287.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23619"/>
     <cge:Term_Ref ObjectID="33397"/>
    <cge:TPSR_Ref TObjectID="23619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-127538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1144.000000 287.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23619"/>
     <cge:Term_Ref ObjectID="33397"/>
    <cge:TPSR_Ref TObjectID="23619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-127506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -269.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23596"/>
     <cge:Term_Ref ObjectID="33352"/>
    <cge:TPSR_Ref TObjectID="23596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-127507" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -269.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23596"/>
     <cge:Term_Ref ObjectID="33352"/>
    <cge:TPSR_Ref TObjectID="23596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-127508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -269.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23596"/>
     <cge:Term_Ref ObjectID="33352"/>
    <cge:TPSR_Ref TObjectID="23596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-127509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -269.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23596"/>
     <cge:Term_Ref ObjectID="33352"/>
    <cge:TPSR_Ref TObjectID="23596"/></metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.424575" width="29" x="727" y="-533"/>
  </g><g id="MotifButton_Layer">
   <g href="dy_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/></g>
   <g href="dy_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_4707640">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -422.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df3780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1093.000000 219.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e10510">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1437.000000 -423.000000)" xlink:href="#lightningRod:shape183"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a6bb40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1678.000000 -423.000000)" xlink:href="#lightningRod:shape183"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_46987e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -757.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_322b6b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.000000 -250.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_334fab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 468.000000 -259.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_476b2f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 587.000000 83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_325a850">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_492dd80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 938.000000 83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4b7d550">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4b80480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.000000 177.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4b877c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1367.000000 -633.000000)" xlink:href="#lightningRod:shape187"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4b8c060">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1397.000000 -675.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a23980">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 901.000000 246.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DGY"/>
</svg>