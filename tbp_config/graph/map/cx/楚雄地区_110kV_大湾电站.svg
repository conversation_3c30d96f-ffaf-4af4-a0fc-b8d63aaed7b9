<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-142" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1259 2349 1260">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="64" x2="64" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape122">
    <ellipse cx="12" cy="15" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="39" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="35" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="39" y2="35"/>
    <ellipse cx="12" cy="35" rx="11" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="7" y1="11" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="17" x2="7" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="17" y1="11" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape44_0">
    <circle cx="26" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="27" y1="57" y2="27"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="26,57 2,57 1,57 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="27,14 21,27 34,27 27,14 27,15 27,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="25,87 21,78 31,78 25,87 "/>
   </symbol>
   <symbol id="transformer2:shape44_1">
    <circle cx="25" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="30" y1="57" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="21" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="57" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape45_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape45_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape69">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="35" x2="35" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="24" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="25" x2="25" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="18" x2="18" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="42" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="42" x2="42" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="28" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="4" x2="4" y1="26" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="19" y1="17" y2="15"/>
    <circle cx="29" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="28" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="29" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="28" x2="26" y1="6" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="30" x2="32" y1="6" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="32" x2="26" y1="3" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="24" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="19" y1="6" y2="4"/>
    <circle cx="19" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="18" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="19" x2="16" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="13" x2="16" y1="8" y2="6"/>
    <ellipse cx="15" cy="19" fillStyle="0" rx="6.5" ry="8.5" stroke-width="0.445094"/>
    <ellipse cx="25" cy="15" fillStyle="0" rx="6" ry="8" stroke-width="0.445094"/>
    <ellipse cx="16" cy="8" fillStyle="0" rx="6" ry="8.5" stroke-width="0.445094"/>
    <ellipse cx="7" cy="14" fillStyle="0" rx="6" ry="8" stroke-width="0.445094"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="22" x2="25" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="25" x2="25" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="28" x2="25" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="13" x2="16" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="16" x2="16" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="19" x2="16" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.781768" x1="8" x2="9" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.787381" x1="6" x2="3" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.749893" x1="3" x2="9" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="16" x2="16" y1="6" y2="3"/>
   </symbol>
   <symbol id="voltageTransformer:shape70">
    <circle cx="17" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="5" x2="10" y1="37" y2="37"/>
    <circle cx="29" cy="36" r="7.5" stroke-width="0.804311"/>
    <circle cx="29" cy="25" r="7.5" stroke-width="0.804311"/>
    <polyline points="5,7 5,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="2" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="3" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="0" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="14" x2="16" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="16" x2="16" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="16" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="29" x2="29" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="27" x2="29" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="29" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="32" x2="28" y1="38" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="32" x2="28" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="28" x2="28" y1="34" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="17" y1="37" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="14" x2="17" y1="39" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="20" x2="17" y1="39" y2="37"/>
    <circle cx="17" cy="25" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1270" width="2359" x="3112" y="-1264"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b35ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3710.000000 124.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cb1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3699.000000 109.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4035ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 94.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40c4580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 116.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36280f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 101.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a2060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4302.000000 86.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404fe60" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3569.000000 533.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40500f0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3572.000000 517.500000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4050330" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3569.000000 579.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4050570" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3569.000000 564.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40507b0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3569.000000 549.033333) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4050ae0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4613.000000 519.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4050d50" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4616.000000 503.500000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4050f90" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4613.000000 565.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40511d0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4613.000000 550.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4051410" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4613.000000 535.033333) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3655e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 478.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3656050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4938.000000 463.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3656290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 493.000000) translate(0,12)">P(kW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3657250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5153.000000 483.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36574b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5178.000000 468.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36576f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5164.000000 498.000000) translate(0,12)">P(kW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa1bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5366.000000 485.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa1e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5391.000000 470.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa2060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5377.000000 500.000000) translate(0,12)">P(kW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dff730" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4233.000000 1103.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dffa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 1149.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dffc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 1134.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dffe90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4252.000000 1119.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e001c0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3659.000000 649.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e00430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 695.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e00670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 680.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e008b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3678.000000 665.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e00be0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4466.000000 656.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e00e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4471.000000 702.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e01090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4460.000000 687.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3630c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4485.000000 672.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-88734">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 -885.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19138" ObjectName="SW-CX_DW.CX_DW_154BK"/>
     <cge:Meas_Ref ObjectId="88734"/>
    <cge:TPSR_Ref TObjectID="19138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88735">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 -887.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19139" ObjectName="SW-CX_DW.CX_DW_152BK"/>
     <cge:Meas_Ref ObjectId="88735"/>
    <cge:TPSR_Ref TObjectID="19139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88736">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -793.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19140" ObjectName="SW-CX_DW.CX_DW_153BK"/>
     <cge:Meas_Ref ObjectId="88736"/>
    <cge:TPSR_Ref TObjectID="19140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4881.000000 -445.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5121.000000 -447.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5330.000000 -447.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4969.000000 -356.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5329.000000 -728.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5225.000000 -357.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5091.000000 -308.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88758">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3730.000000 -355.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19162" ObjectName="SW-CX_DW.CX_DW_051BK"/>
     <cge:Meas_Ref ObjectId="88758"/>
    <cge:TPSR_Ref TObjectID="19162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88731">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3896.000000 -355.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19135" ObjectName="SW-CX_DW.CX_DW_052BK"/>
     <cge:Meas_Ref ObjectId="88731"/>
    <cge:TPSR_Ref TObjectID="19135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88732">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3982.000000 -354.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19136" ObjectName="SW-CX_DW.CX_DW_053BK"/>
     <cge:Meas_Ref ObjectId="88732"/>
    <cge:TPSR_Ref TObjectID="19136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4307.000000 -357.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19164" ObjectName="SW-CX_DW.CX_DW_054BK"/>
     <cge:Meas_Ref ObjectId="88760"/>
    <cge:TPSR_Ref TObjectID="19164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88733">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -357.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19137" ObjectName="SW-CX_DW.CX_DW_055BK"/>
     <cge:Meas_Ref ObjectId="88733"/>
    <cge:TPSR_Ref TObjectID="19137"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_40deed0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -1130.000000)" xlink:href="#voltageTransformer:shape69"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4192280">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -699.000000)" xlink:href="#voltageTransformer:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cffb10">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 -701.000000)" xlink:href="#voltageTransformer:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_410ff10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3528.000000 -323.000000)" xlink:href="#voltageTransformer:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2649c80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3656.000000 -165.000000)" xlink:href="#voltageTransformer:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a26dc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3579.000000 -172.000000)" xlink:href="#voltageTransformer:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e2a340">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4105.000000 -325.000000)" xlink:href="#voltageTransformer:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2322da0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 -167.000000)" xlink:href="#voltageTransformer:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_363ac20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -174.000000)" xlink:href="#voltageTransformer:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_ZX" endPointId="0" endStationName="CX_DW" flowDrawDirect="1" flowShape="0" id="AC-110kV.ziwan_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4210,-1191 4210,-1238 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29090" ObjectName="AC-110kV.ziwan_line"/>
    <cge:TPSR_Ref TObjectID="29090_SS-142"/></metadata>
   <polyline fill="none" opacity="0" points="4210,-1191 4210,-1238 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_24ff4b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 4118.000000 -1094.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f803f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 4116.000000 -1019.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fd9880" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -529.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_418a040" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3765.000000 -633.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e2850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3765.000000 -701.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43359d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 -871.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a290a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -938.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cb5710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -531.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40bb660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 -635.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d4ac40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 -703.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35fad00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 -873.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_363b030" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -940.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dfc5e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -720.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3320640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 -720.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25db1f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -342.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_46a23e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -344.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_40cf090">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3785.000000 -750.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fbff20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -713.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4199400">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -536.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40ce620">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -536.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_466f800">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 -1168.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d4fe0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -752.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d410d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -538.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9dae0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4479.000000 -538.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d66a70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 -340.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5ae50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3536.000000 -373.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b34fd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 -215.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e426f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3549.000000 -193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d4330">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3587.000000 -222.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dfb8c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -203.000000)" xlink:href="#lightningRod:shape122"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad1b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 -264.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b09a20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3897.000000 -305.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f14190">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3896.000000 -259.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d59b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -304.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d7730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3982.000000 -258.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4116280">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -342.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f843b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -375.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3374f30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4241.000000 -217.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f2c720">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -195.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3639ff0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -224.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41adb50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -205.000000)" xlink:href="#lightningRod:shape122"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d5e50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -266.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dfdd70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -307.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dfebc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -261.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e04a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.000000 -537.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cec0a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 -715.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_265d550">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -539.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e3580">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5333.000000 -628.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88698" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -494.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88698" ObjectName="CX_DW:CX_DW_GG_P1_27"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88699" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -480.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88699" ObjectName="CX_DW:CX_DW_GG_Q1_28"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88701" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -467.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88701" ObjectName="CX_DW:CX_DW_GG_Ia1_30"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88704" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5222.000000 -499.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88704" ObjectName="CX_DW:CX_DW_GG_P2_33"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88705" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5222.000000 -485.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88705" ObjectName="CX_DW:CX_DW_GG_Q2_34"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88707" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5222.000000 -471.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88707" ObjectName="CX_DW:CX_DW_GG_Ia2_36"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88710" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5435.000000 -501.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88710" ObjectName="CX_DW:CX_DW_GG_P3_39"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88711" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5435.000000 -486.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88711" ObjectName="CX_DW:CX_DW_GG_Q3_40"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88713" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5435.000000 -472.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88713" ObjectName="CX_DW:CX_DW_GG_Ia3_42"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116474" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3231.000000 -1013.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116474" ObjectName="CX_DW:CX_DW_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116475" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3236.000000 -972.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116475" ObjectName="CX_DW:CX_DW_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3766.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19162"/>
     <cge:Term_Ref ObjectID="26633"/>
    <cge:TPSR_Ref TObjectID="19162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88687" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3766.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19162"/>
     <cge:Term_Ref ObjectID="26633"/>
    <cge:TPSR_Ref TObjectID="19162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88689" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3766.000000 -121.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88689" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19162"/>
     <cge:Term_Ref ObjectID="26633"/>
    <cge:TPSR_Ref TObjectID="19162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88716" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88716" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19135"/>
     <cge:Term_Ref ObjectID="26579"/>
    <cge:TPSR_Ref TObjectID="19135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19135"/>
     <cge:Term_Ref ObjectID="26579"/>
    <cge:TPSR_Ref TObjectID="19135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88719" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -121.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19135"/>
     <cge:Term_Ref ObjectID="26579"/>
    <cge:TPSR_Ref TObjectID="19135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19136"/>
     <cge:Term_Ref ObjectID="26581"/>
    <cge:TPSR_Ref TObjectID="19136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19136"/>
     <cge:Term_Ref ObjectID="26581"/>
    <cge:TPSR_Ref TObjectID="19136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88780" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88780" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19136"/>
     <cge:Term_Ref ObjectID="26581"/>
    <cge:TPSR_Ref TObjectID="19136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88692" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -114.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19164"/>
     <cge:Term_Ref ObjectID="26637"/>
    <cge:TPSR_Ref TObjectID="19164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88693" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -114.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19164"/>
     <cge:Term_Ref ObjectID="26637"/>
    <cge:TPSR_Ref TObjectID="19164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88695" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -114.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19164"/>
     <cge:Term_Ref ObjectID="26637"/>
    <cge:TPSR_Ref TObjectID="19164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88722" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -114.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19137"/>
     <cge:Term_Ref ObjectID="26583"/>
    <cge:TPSR_Ref TObjectID="19137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88723" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -114.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19137"/>
     <cge:Term_Ref ObjectID="26583"/>
    <cge:TPSR_Ref TObjectID="19137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88725" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -114.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88725" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19137"/>
     <cge:Term_Ref ObjectID="26583"/>
    <cge:TPSR_Ref TObjectID="19137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-88986" prefix="Ia   " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3910.000000 -912.000000) translate(0,12)">Ia    0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19138"/>
     <cge:Term_Ref ObjectID="26585"/>
    <cge:TPSR_Ref TObjectID="19138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-88783" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3634.000000 -579.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88783" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19168"/>
     <cge:Term_Ref ObjectID="26649"/>
    <cge:TPSR_Ref TObjectID="19168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-88784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3634.000000 -579.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19168"/>
     <cge:Term_Ref ObjectID="26649"/>
    <cge:TPSR_Ref TObjectID="19168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-88785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3634.000000 -579.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19168"/>
     <cge:Term_Ref ObjectID="26649"/>
    <cge:TPSR_Ref TObjectID="19168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-88786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3634.000000 -579.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19168"/>
     <cge:Term_Ref ObjectID="26649"/>
    <cge:TPSR_Ref TObjectID="19168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-88787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3634.000000 -579.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19168"/>
     <cge:Term_Ref ObjectID="26649"/>
    <cge:TPSR_Ref TObjectID="19168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-88790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -565.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19169"/>
     <cge:Term_Ref ObjectID="26650"/>
    <cge:TPSR_Ref TObjectID="19169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-88791" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -565.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88791" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19169"/>
     <cge:Term_Ref ObjectID="26650"/>
    <cge:TPSR_Ref TObjectID="19169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-88792" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -565.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88792" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19169"/>
     <cge:Term_Ref ObjectID="26650"/>
    <cge:TPSR_Ref TObjectID="19169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-88788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -565.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19169"/>
     <cge:Term_Ref ObjectID="26650"/>
    <cge:TPSR_Ref TObjectID="19169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-88789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -565.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19169"/>
     <cge:Term_Ref ObjectID="26650"/>
    <cge:TPSR_Ref TObjectID="19169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-88890" prefix="Ia   " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4447.000000 -912.000000) translate(0,12)">Ia    0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19139"/>
     <cge:Term_Ref ObjectID="26587"/>
    <cge:TPSR_Ref TObjectID="19139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88676" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3719.000000 -694.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88676" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19141"/>
     <cge:Term_Ref ObjectID="26591"/>
    <cge:TPSR_Ref TObjectID="19141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88677" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3719.000000 -694.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19141"/>
     <cge:Term_Ref ObjectID="26591"/>
    <cge:TPSR_Ref TObjectID="19141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88679" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3719.000000 -694.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88679" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19141"/>
     <cge:Term_Ref ObjectID="26591"/>
    <cge:TPSR_Ref TObjectID="19141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-88678" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3719.000000 -694.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88678" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19141"/>
     <cge:Term_Ref ObjectID="26591"/>
    <cge:TPSR_Ref TObjectID="19141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88671" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4295.000000 -1147.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19159"/>
     <cge:Term_Ref ObjectID="26627"/>
    <cge:TPSR_Ref TObjectID="19159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88672" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4295.000000 -1147.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88672" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19159"/>
     <cge:Term_Ref ObjectID="26627"/>
    <cge:TPSR_Ref TObjectID="19159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88674" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4295.000000 -1147.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88674" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19159"/>
     <cge:Term_Ref ObjectID="26627"/>
    <cge:TPSR_Ref TObjectID="19159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-88673" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4295.000000 -1147.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88673" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19159"/>
     <cge:Term_Ref ObjectID="26627"/>
    <cge:TPSR_Ref TObjectID="19159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88985" prefix="Ia   " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4116.000000 -775.000000) translate(0,12)">Ia    0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19140"/>
     <cge:Term_Ref ObjectID="26589"/>
    <cge:TPSR_Ref TObjectID="19140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88681" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -699.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19144"/>
     <cge:Term_Ref ObjectID="26597"/>
    <cge:TPSR_Ref TObjectID="19144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88682" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -699.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19144"/>
     <cge:Term_Ref ObjectID="26597"/>
    <cge:TPSR_Ref TObjectID="19144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88684" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -699.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19144"/>
     <cge:Term_Ref ObjectID="26597"/>
    <cge:TPSR_Ref TObjectID="19144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-88683" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -699.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19144"/>
     <cge:Term_Ref ObjectID="26597"/>
    <cge:TPSR_Ref TObjectID="19144"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="173" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="173" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="173" x="3246" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DW.CX_DW_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3549,-480 4030,-480 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19168" ObjectName="BS-CX_DW.CX_DW_9IM"/>
    <cge:TPSR_Ref TObjectID="19168"/></metadata>
   <polyline fill="none" opacity="0" points="3549,-480 4030,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DW.CX_DW_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4126,-482 4607,-482 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19169" ObjectName="BS-CX_DW.CX_DW_9IIM"/>
    <cge:TPSR_Ref TObjectID="19169"/></metadata>
   <polyline fill="none" opacity="0" points="4126,-482 4607,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4808,-394 4966,-394 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4808,-394 4966,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-396 5206,-396 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5048,-396 5206,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5257,-396 5415,-396 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5257,-396 5415,-396 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_DW.CX_DW_GN1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -178.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43272" ObjectName="SM-CX_DW.CX_DW_GN1"/>
    <cge:TPSR_Ref TObjectID="43272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DW.CX_DW_GN2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 -178.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43273" ObjectName="SM-CX_DW.CX_DW_GN2"/>
    <cge:TPSR_Ref TObjectID="43273"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="19168" cx="3866" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19168" cx="3577" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19168" cx="3740" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19169" cx="4401" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19169" cx="4154" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4890" cy="-394" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5130" cy="-396" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5339" cy="-396" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5175" cy="-396" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5319" cy="-396" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4841" cy="-394" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5378" cy="-396" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4909" cy="-394" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5069" cy="-396" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19168" cx="3906" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19168" cx="3992" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19169" cx="4317" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19169" cx="4512" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2676fa0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3680.000000 -177.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dc0d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4225.000000 -1259.000000) translate(0,15)">紫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dc0d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4225.000000 -1259.000000) translate(0,33)">湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dc0d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4225.000000 -1259.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36266a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3777.000000 -203.000000) translate(0,15)">1号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3dface0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4912.000000 -662.000000) translate(0,15)">1B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d6290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -674.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_41ab320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -624.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d40ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -419.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e3ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -1052.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4224c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -1071.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40d2fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -1126.000000) translate(0,12)">K1517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_447a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e2b7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e2b7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e2b7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e2b7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e2b7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e2b7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e2b7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_41a8700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3292.000000 -1168.500000) translate(0,16)">大湾电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42c43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -717.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42c43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -717.000000) translate(0,33)">SSP10-31500/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42c43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -717.000000) translate(0,51)">31500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42c43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -717.000000) translate(0,69)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42c43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -717.000000) translate(0,87)">121±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42c43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -717.000000) translate(0,105)">Ud=10.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_265df30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3580.000000 -159.000000) translate(0,15)">1号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_265df30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3580.000000 -159.000000) translate(0,33)">SF24.9-24/4920</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_265df30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3580.000000 -159.000000) translate(0,51)">Pe=24.9MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_265df30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3580.000000 -159.000000) translate(0,69)">Ue=10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_265df30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3580.000000 -159.000000) translate(0,87)">cos∮=0.85</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_265df30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3580.000000 -159.000000) translate(0,105)">50Hz</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4255fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -581.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b401a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 -913.000000) translate(0,12)">154</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_469e6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -666.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4197000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -981.000000) translate(0,12)">1546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_418a4c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3944.000000 -511.000000) translate(0,15)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d650e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3915.000000 -416.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cf5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -734.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a280d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3807.000000 -904.000000) translate(0,12)">15417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2610a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -971.000000) translate(0,12)">15467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -842.000000) translate(0,12)">1541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4ebe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -676.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4208b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4429.000000 -626.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4128030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4607.000000 -698.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4128030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4607.000000 -698.000000) translate(0,33)">SSP10-31500/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4128030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4607.000000 -698.000000) translate(0,51)">31500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4128030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4607.000000 -698.000000) translate(0,69)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4128030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4607.000000 -698.000000) translate(0,87)">121±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4128030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4607.000000 -698.000000) translate(0,105)">Ud=10.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a05550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4519.000000 -583.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2674600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -915.000000) translate(0,12)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4e500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -668.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4e740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4413.000000 -983.000000) translate(0,12)">1526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d7d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -736.000000) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cedc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -906.000000) translate(0,12)">15227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40c5a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -973.000000) translate(0,12)">15267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41c0ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -844.000000) translate(0,12)">1522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e97c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -833.000000) translate(0,12)">1531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e980e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -772.000000) translate(0,12)">15317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4335e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4142.000000 -793.000000) translate(0,12)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4336030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -831.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ebc740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4239.000000 -774.000000) translate(0,12)">15327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ebc960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4273.000000 -832.000000) translate(0,12)">1532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d5fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -948.000000) translate(0,15)">1号主变110kV侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_469dd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4440.000000 -946.000000) translate(0,15)">2号主变110kV侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_469e0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -979.000000) translate(0,15)">110kV GIS组合电器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d4fa0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3480.000000 -327.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d4fa0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3480.000000 -327.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad15c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -371.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f9ed0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3832.000000 -262.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35fa3c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3837.000000 -154.000000) translate(0,12)">至0.4kV站用电Ⅰ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d5350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4001.000000 -415.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4b790" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4002.000000 -260.000000) translate(0,12)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fdf4e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3976.000000 -153.000000) translate(0,12)">至坝区用电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d8b100" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4257.000000 -179.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d00770" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4354.000000 -205.000000) translate(0,15)">2号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d009a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4279.000000 -417.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d00be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -161.000000) translate(0,15)">2号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d00be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -161.000000) translate(0,33)">SF24.9-24/4920</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d00be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -161.000000) translate(0,51)">Pe=24.9MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d00be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -161.000000) translate(0,69)">Ue=10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d00be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -161.000000) translate(0,87)">cos∮=0.85</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d00be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -161.000000) translate(0,105)">50Hz</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d00e90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4521.000000 -513.000000) translate(0,15)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d01380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4521.000000 -418.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d5860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -373.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4104130" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4527.000000 -265.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4104620" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4443.000000 -156.000000) translate(0,12)">至0.4kV站用电Ⅱ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f5fc50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4061.000000 -323.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f5fc50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4061.000000 -323.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_410e260" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4767.000000 -419.000000) translate(0,12)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ceb620" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4838.000000 -851.000000) translate(0,12)">至1号主变低压侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ceb620" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4838.000000 -851.000000) translate(0,27)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cebe30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5152.000000 -664.000000) translate(0,15)">2B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f88de0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5080.000000 -387.000000) translate(0,12)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e2b30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5079.000000 -855.000000) translate(0,12)">至2号主变低压侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e2b30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5079.000000 -855.000000) translate(0,27)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34e3270" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5361.000000 -664.000000) translate(0,15)">1B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d3170" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5347.000000 -418.000000) translate(0,12)">0.4kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d3a90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5274.000000 -875.000000) translate(0,12)">厂房柴油发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4125190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5331.000000 -850.000000) translate(0,12)">D1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df05f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5051.000000 -302.000000) translate(0,12)">电站站用电接线示意图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_411d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3251.000000 -219.000000) translate(0,13)">4764</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_29d1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -785.000000) translate(0,15)">全站检修停电前应挂“全站检修”牌，“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_29d1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -785.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_29d1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -785.000000) translate(0,51)">全站检修完工后仅可摘除“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_29d1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -785.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_29d1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -785.000000) translate(0,87)">全站检修复电后才可以摘除“全站检修”牌</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="5338" cy="-842" fill="none" fillStyle="0" r="17" stroke="rgb(30,144,255)" stroke-width="1"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DW.CX_DW_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="26643"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 -526.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 -526.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="19166" ObjectName="TF-CX_DW.CX_DW_1T"/>
    <cge:TPSR_Ref TObjectID="19166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DW.CX_DW_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="26647"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 -528.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 -528.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="19167" ObjectName="TF-CX_DW.CX_DW_2T"/>
    <cge:TPSR_Ref TObjectID="19167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -155.000000)" xlink:href="#transformer2:shape44_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -155.000000)" xlink:href="#transformer2:shape44_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -154.000000)" xlink:href="#transformer2:shape45_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -154.000000)" xlink:href="#transformer2:shape45_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -157.000000)" xlink:href="#transformer2:shape44_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -157.000000)" xlink:href="#transformer2:shape44_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -591.000000)" xlink:href="#transformer2:shape44_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -591.000000)" xlink:href="#transformer2:shape44_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 -593.000000)" xlink:href="#transformer2:shape44_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 -593.000000)" xlink:href="#transformer2:shape44_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-88657" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19121" ObjectName="DYN-CX_DW"/>
     <cge:Meas_Ref ObjectId="88657"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-88756">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -1023.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19160" ObjectName="SW-CX_DW.CX_DW_15160SW"/>
     <cge:Meas_Ref ObjectId="88756"/>
    <cge:TPSR_Ref TObjectID="19160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88757">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.000000 -1098.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19161" ObjectName="SW-CX_DW.CX_DW_K1517SW"/>
     <cge:Meas_Ref ObjectId="88757"/>
    <cge:TPSR_Ref TObjectID="19161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88773">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 -553.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19172" ObjectName="SW-CX_DW.CX_DW_1010SW"/>
     <cge:Meas_Ref ObjectId="88773"/>
    <cge:TPSR_Ref TObjectID="19172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88737">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19141" ObjectName="SW-CX_DW.CX_DW_1016SW"/>
     <cge:Meas_Ref ObjectId="88737"/>
    <cge:TPSR_Ref TObjectID="19141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88739">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -634.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19143" ObjectName="SW-CX_DW.CX_DW_10167SW"/>
     <cge:Meas_Ref ObjectId="88739"/>
    <cge:TPSR_Ref TObjectID="19143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88755">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -1041.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19159" ObjectName="SW-CX_DW.CX_DW_1516SW"/>
     <cge:Meas_Ref ObjectId="88755"/>
    <cge:TPSR_Ref TObjectID="19159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88738">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -702.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19142" ObjectName="SW-CX_DW.CX_DW_10160SW"/>
     <cge:Meas_Ref ObjectId="88738"/>
    <cge:TPSR_Ref TObjectID="19142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88743">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 -817.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19147" ObjectName="SW-CX_DW.CX_DW_1541SW"/>
     <cge:Meas_Ref ObjectId="88743"/>
    <cge:TPSR_Ref TObjectID="19147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88745">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -872.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19149" ObjectName="SW-CX_DW.CX_DW_15417SW"/>
     <cge:Meas_Ref ObjectId="88745"/>
    <cge:TPSR_Ref TObjectID="19149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88746">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -939.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19150" ObjectName="SW-CX_DW.CX_DW_15467SW"/>
     <cge:Meas_Ref ObjectId="88746"/>
    <cge:TPSR_Ref TObjectID="19150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88744">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 -955.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19148" ObjectName="SW-CX_DW.CX_DW_1546SW"/>
     <cge:Meas_Ref ObjectId="88744"/>
    <cge:TPSR_Ref TObjectID="19148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88774">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -555.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19173" ObjectName="SW-CX_DW.CX_DW_1020SW"/>
     <cge:Meas_Ref ObjectId="88774"/>
    <cge:TPSR_Ref TObjectID="19173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88740">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19144" ObjectName="SW-CX_DW.CX_DW_1026SW"/>
     <cge:Meas_Ref ObjectId="88740"/>
    <cge:TPSR_Ref TObjectID="19144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88742">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 -636.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19146" ObjectName="SW-CX_DW.CX_DW_10267SW"/>
     <cge:Meas_Ref ObjectId="88742"/>
    <cge:TPSR_Ref TObjectID="19146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88741">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 -704.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19145" ObjectName="SW-CX_DW.CX_DW_10260SW"/>
     <cge:Meas_Ref ObjectId="88741"/>
    <cge:TPSR_Ref TObjectID="19145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88748">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19152" ObjectName="SW-CX_DW.CX_DW_1522SW"/>
     <cge:Meas_Ref ObjectId="88748"/>
    <cge:TPSR_Ref TObjectID="19152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88750">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -874.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19154" ObjectName="SW-CX_DW.CX_DW_15227SW"/>
     <cge:Meas_Ref ObjectId="88750"/>
    <cge:TPSR_Ref TObjectID="19154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88749">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4337.000000 -941.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19153" ObjectName="SW-CX_DW.CX_DW_15267SW"/>
     <cge:Meas_Ref ObjectId="88749"/>
    <cge:TPSR_Ref TObjectID="19153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88747">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 -957.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19151" ObjectName="SW-CX_DW.CX_DW_1526SW"/>
     <cge:Meas_Ref ObjectId="88747"/>
    <cge:TPSR_Ref TObjectID="19151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88752">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3950.000000 -798.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19156" ObjectName="SW-CX_DW.CX_DW_1531SW"/>
     <cge:Meas_Ref ObjectId="88752"/>
    <cge:TPSR_Ref TObjectID="19156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88754">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 -746.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19158" ObjectName="SW-CX_DW.CX_DW_15317SW"/>
     <cge:Meas_Ref ObjectId="88754"/>
    <cge:TPSR_Ref TObjectID="19158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88753">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4224.000000 -745.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19157" ObjectName="SW-CX_DW.CX_DW_15327SW"/>
     <cge:Meas_Ref ObjectId="88753"/>
    <cge:TPSR_Ref TObjectID="19157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88751">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -798.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19155" ObjectName="SW-CX_DW.CX_DW_1532SW"/>
     <cge:Meas_Ref ObjectId="88751"/>
    <cge:TPSR_Ref TObjectID="19155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3567.000000 -431.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -433.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4240.000000 -276.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -276.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -345.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19165" ObjectName="SW-CX_DW.CX_DW_05467SW"/>
     <cge:Meas_Ref ObjectId="88761"/>
    <cge:TPSR_Ref TObjectID="19165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.000000 -493.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.000000 -412.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -495.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -414.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5329.000000 -495.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5329.000000 -414.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.000000 -357.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5036.000000 -357.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 -358.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5274.000000 -358.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 -309.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5179.000000 -309.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -343.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19163" ObjectName="SW-CX_DW.CX_DW_05167SW"/>
     <cge:Meas_Ref ObjectId="88759"/>
    <cge:TPSR_Ref TObjectID="19163"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_25cdf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4156,-1103 4141,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19161@0" ObjectIDZND0="g_24ff4b0@0" Pin0InfoVect0LinkObjId="g_24ff4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88757_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4156,-1103 4141,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34d0320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4139,-1028 4154,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3f803f0@0" ObjectIDZND0="19160@0" Pin0InfoVect0LinkObjId="SW-88756_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f803f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4139,-1028 4154,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a49910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-1103 4210,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="voltageTransformer" ObjectIDND0="19161@1" ObjectIDZND0="g_466f800@0" ObjectIDZND1="29090@1" ObjectIDZND2="g_40deed0@0" Pin0InfoVect0LinkObjId="g_466f800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_40deed0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88757_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-1103 4210,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a78e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-594 3977,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_4199400@0" ObjectIDZND0="19172@1" Pin0InfoVect0LinkObjId="SW-88773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4199400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-594 3977,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36554b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-547 3977,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3fd9880@0" ObjectIDZND0="19172@0" Pin0InfoVect0LinkObjId="SW-88773_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fd9880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-547 3977,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40c09b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-594 3927,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="19166@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40d5e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-594 3927,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b3e680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-639 3783,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19143@0" ObjectIDZND0="g_418a040@0" Pin0InfoVect0LinkObjId="g_418a040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-639 3783,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40d6bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-1175 4210,-1175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_466f800@0" ObjectIDZND0="g_40deed0@0" ObjectIDZND1="19161@x" ObjectIDZND2="19159@x" Pin0InfoVect0LinkObjId="g_40deed0_0" Pin0InfoVect1LinkObjId="SW-88757_0" Pin0InfoVect2LinkObjId="SW-88755_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_466f800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-1175 4210,-1175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_44785c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-1191 4210,-1175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="29090@1" ObjectIDZND0="g_466f800@0" ObjectIDZND1="g_40deed0@0" ObjectIDZND2="19161@x" Pin0InfoVect0LinkObjId="g_466f800_0" Pin0InfoVect1LinkObjId="g_40deed0_0" Pin0InfoVect2LinkObjId="SW-88757_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-1191 4210,-1175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_46a0ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-1159 4210,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_40deed0@0" ObjectIDZND0="g_466f800@0" ObjectIDZND1="29090@1" ObjectIDZND2="19161@x" Pin0InfoVect0LinkObjId="g_466f800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-88757_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40deed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-1159 4210,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b40f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-1175 4210,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_466f800@0" ObjectIDND1="29090@1" ObjectIDZND0="g_40deed0@0" ObjectIDZND1="19161@x" ObjectIDZND2="19159@x" Pin0InfoVect0LinkObjId="g_40deed0_0" Pin0InfoVect1LinkObjId="SW-88757_0" Pin0InfoVect2LinkObjId="SW-88755_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_466f800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-1175 4210,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40c0510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-531 3866,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="busSection" ObjectIDND0="19166@0" ObjectIDZND0="19168@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40d5e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-531 3866,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40d5e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3846,-639 3867,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="19143@1" ObjectIDZND0="19166@x" ObjectIDZND1="19141@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-88737_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88739_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3846,-639 3867,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4197d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-611 3867,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19166@1" ObjectIDZND0="19143@x" ObjectIDZND1="19141@x" Pin0InfoVect0LinkObjId="SW-88739_0" Pin0InfoVect1LinkObjId="SW-88737_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40d5e50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-611 3867,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_362ad70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-707 3783,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19142@0" ObjectIDZND0="g_40e2850@0" Pin0InfoVect0LinkObjId="g_40e2850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88738_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-707 3783,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34d8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3846,-707 3867,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19142@1" ObjectIDZND0="19141@x" ObjectIDZND1="19156@x" ObjectIDZND2="19147@x" Pin0InfoVect0LinkObjId="SW-88737_0" Pin0InfoVect1LinkObjId="SW-88752_0" Pin0InfoVect2LinkObjId="SW-88743_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88738_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3846,-707 3867,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d06160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-877 3800,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19149@0" ObjectIDZND0="g_43359d0@0" Pin0InfoVect0LinkObjId="g_43359d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-877 3800,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26102c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-944 3799,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19150@0" ObjectIDZND0="g_2a290a0@0" Pin0InfoVect0LinkObjId="g_2a290a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-944 3799,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2bac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3847,-877 3867,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="19149@1" ObjectIDZND0="19147@x" ObjectIDZND1="19138@x" Pin0InfoVect0LinkObjId="SW-88743_0" Pin0InfoVect1LinkObjId="SW-88734_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3847,-877 3867,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2c2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-858 3867,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="19147@1" ObjectIDZND0="19149@x" ObjectIDZND1="19138@x" Pin0InfoVect0LinkObjId="SW-88745_0" Pin0InfoVect1LinkObjId="SW-88734_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88743_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-858 3867,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b32c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-877 3867,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19147@x" ObjectIDND1="19149@x" ObjectIDZND0="19138@0" Pin0InfoVect0LinkObjId="SW-88734_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88743_0" Pin1InfoVect1LinkObjId="SW-88745_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-877 3867,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40e13b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3847,-944 3867,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19150@1" ObjectIDZND0="19138@x" ObjectIDZND1="19148@x" Pin0InfoVect0LinkObjId="SW-88734_0" Pin0InfoVect1LinkObjId="SW-88744_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88746_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3847,-944 3867,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2dea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-920 3867,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19138@1" ObjectIDZND0="19150@x" ObjectIDZND1="19148@x" Pin0InfoVect0LinkObjId="SW-88746_0" Pin0InfoVect1LinkObjId="SW-88744_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88734_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-920 3867,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_326c830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-944 3867,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19138@x" ObjectIDND1="19150@x" ObjectIDZND0="19148@0" Pin0InfoVect0LinkObjId="SW-88744_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88734_0" Pin1InfoVect1LinkObjId="SW-88746_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-944 3867,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_46a3d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4462,-596 4512,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3d410d0@0" ObjectIDZND0="19173@1" Pin0InfoVect0LinkObjId="SW-88774_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d410d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4462,-596 4512,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_43355a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-549 4512,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3cb5710@0" ObjectIDZND0="19173@0" Pin0InfoVect0LinkObjId="SW-88774_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cb5710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-549 4512,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35fc1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-596 4462,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="19167@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_466fb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-596 4462,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34e5a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-641 4318,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19146@0" ObjectIDZND0="g_40bb660@0" Pin0InfoVect0LinkObjId="g_40bb660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-641 4318,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c65900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-533 4401,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="busSection" ObjectIDND0="19167@0" ObjectIDZND0="19169@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_466fb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-533 4401,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_466fb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4381,-641 4402,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="19146@1" ObjectIDZND0="19167@x" ObjectIDZND1="19144@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-88740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88742_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4381,-641 4402,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa5a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-613 4402,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19167@1" ObjectIDZND0="19146@x" ObjectIDZND1="19144@x" Pin0InfoVect0LinkObjId="SW-88742_0" Pin0InfoVect1LinkObjId="SW-88740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_466fb60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-613 4402,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33ee020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-641 4402,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="19146@x" ObjectIDND1="19167@x" ObjectIDZND0="19144@0" Pin0InfoVect0LinkObjId="SW-88740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88742_0" Pin1InfoVect1LinkObjId="g_466fb60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-641 4402,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cb1b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-709 4318,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19145@0" ObjectIDZND0="g_3d4ac40@0" Pin0InfoVect0LinkObjId="g_3d4ac40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-709 4318,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cff920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4381,-709 4402,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19145@1" ObjectIDZND0="19144@x" ObjectIDZND1="g_34d4fe0@0" ObjectIDZND2="19155@x" Pin0InfoVect0LinkObjId="SW-88740_0" Pin0InfoVect1LinkObjId="g_34d4fe0_0" Pin0InfoVect2LinkObjId="SW-88751_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4381,-709 4402,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40c6b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4343,-879 4331,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19154@0" ObjectIDZND0="g_35fad00@0" Pin0InfoVect0LinkObjId="g_35fad00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4343,-879 4331,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cb7960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-946 4330,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19153@0" ObjectIDZND0="g_363b030@0" Pin0InfoVect0LinkObjId="g_363b030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-946 4330,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dfa3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-879 4402,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19154@1" ObjectIDZND0="19139@x" ObjectIDZND1="19152@x" Pin0InfoVect0LinkObjId="SW-88735_0" Pin0InfoVect1LinkObjId="SW-88748_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88750_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-879 4402,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dfa5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-879 4402,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19154@x" ObjectIDND1="19152@x" ObjectIDZND0="19139@0" Pin0InfoVect0LinkObjId="SW-88735_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88750_0" Pin1InfoVect1LinkObjId="SW-88748_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-879 4402,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35d2a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-946 4402,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19153@1" ObjectIDZND0="19139@x" ObjectIDZND1="19151@x" Pin0InfoVect0LinkObjId="SW-88735_0" Pin0InfoVect1LinkObjId="SW-88747_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88749_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-946 4402,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35d2c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-922 4402,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19139@1" ObjectIDZND0="19153@x" ObjectIDZND1="19151@x" Pin0InfoVect0LinkObjId="SW-88749_0" Pin0InfoVect1LinkObjId="SW-88747_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88735_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-922 4402,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40c89c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-946 4402,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="19153@x" ObjectIDND1="19139@x" ObjectIDZND0="19151@0" Pin0InfoVect0LinkObjId="SW-88747_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88749_0" Pin1InfoVect1LinkObjId="SW-88735_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-946 4402,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2a670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-803 3955,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="19147@x" ObjectIDND1="19142@x" ObjectIDND2="19141@x" ObjectIDZND0="19156@0" Pin0InfoVect0LinkObjId="SW-88752_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-88743_0" Pin1InfoVect1LinkObjId="SW-88738_0" Pin1InfoVect2LinkObjId="SW-88737_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-803 3955,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40cc010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3991,-803 4081,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19156@1" ObjectIDZND0="19140@x" ObjectIDZND1="19158@x" Pin0InfoVect0LinkObjId="SW-88736_0" Pin0InfoVect1LinkObjId="SW-88754_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88752_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3991,-803 4081,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40cc240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4081,-803 4141,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19156@x" ObjectIDND1="19158@x" ObjectIDZND0="19140@1" Pin0InfoVect0LinkObjId="SW-88736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88752_0" Pin1InfoVect1LinkObjId="SW-88754_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4081,-803 4141,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4028450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4081,-738 4081,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3dfc5e0@0" ObjectIDZND0="19158@0" Pin0InfoVect0LinkObjId="SW-88754_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dfc5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4081,-738 4081,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40286b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4081,-787 4081,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19158@1" ObjectIDZND0="19140@x" ObjectIDZND1="19156@x" Pin0InfoVect0LinkObjId="SW-88736_0" Pin0InfoVect1LinkObjId="SW-88752_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88754_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4081,-787 4081,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cb5f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4233,-738 4233,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3320640@0" ObjectIDZND0="19157@0" Pin0InfoVect0LinkObjId="SW-88753_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3320640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4233,-738 4233,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cb6130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4233,-786 4233,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19157@1" ObjectIDZND0="19140@x" ObjectIDZND1="19155@x" Pin0InfoVect0LinkObjId="SW-88736_0" Pin0InfoVect1LinkObjId="SW-88751_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88753_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4233,-786 4233,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_418ec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4168,-803 4233,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19140@0" ObjectIDZND0="19157@x" ObjectIDZND1="19155@x" Pin0InfoVect0LinkObjId="SW-88753_0" Pin0InfoVect1LinkObjId="SW-88751_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4168,-803 4233,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2669ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4233,-803 4274,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="19157@x" ObjectIDND1="19140@x" ObjectIDZND0="19155@0" Pin0InfoVect0LinkObjId="SW-88751_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88753_0" Pin1InfoVect1LinkObjId="SW-88736_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4233,-803 4274,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2669f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-803 4402,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19155@1" ObjectIDZND0="19152@x" ObjectIDZND1="g_34d4fe0@0" ObjectIDZND2="19145@x" Pin0InfoVect0LinkObjId="SW-88748_0" Pin0InfoVect1LinkObjId="g_34d4fe0_0" Pin0InfoVect2LinkObjId="SW-88741_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88751_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-803 4402,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ebcba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-1028 4210,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19160@1" ObjectIDZND0="19159@x" ObjectIDZND1="19151@x" ObjectIDZND2="19148@x" Pin0InfoVect0LinkObjId="SW-88755_0" Pin0InfoVect1LinkObjId="SW-88747_0" Pin0InfoVect2LinkObjId="SW-88744_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88756_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-1028 4210,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25d5bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-998 4402,-1011 4210,-1011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19151@1" ObjectIDZND0="19148@x" ObjectIDZND1="19160@x" ObjectIDZND2="19159@x" Pin0InfoVect0LinkObjId="SW-88744_0" Pin0InfoVect1LinkObjId="SW-88756_0" Pin0InfoVect2LinkObjId="SW-88755_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88747_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-998 4402,-1011 4210,-1011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25d5dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-1011 3867,-1011 3867,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="19151@x" ObjectIDND1="19160@x" ObjectIDND2="19159@x" ObjectIDZND0="19148@1" Pin0InfoVect0LinkObjId="SW-88744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-88747_0" Pin1InfoVect1LinkObjId="SW-88756_0" Pin1InfoVect2LinkObjId="SW-88755_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-1011 3867,-1011 3867,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e08c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-480 3577,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19168@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40c0510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-480 3577,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4479250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-438 3577,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3d66a70@0" ObjectIDZND1="g_2b5ae50@0" Pin0InfoVect0LinkObjId="g_3d66a70_0" Pin0InfoVect1LinkObjId="g_2b5ae50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-438 3577,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a88d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-426 3601,-426 3601,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b5ae50@0" ObjectIDZND0="g_3d66a70@0" Pin0InfoVect0LinkObjId="g_3d66a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b5ae50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-426 3601,-426 3601,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_41b20d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3545,-367 3545,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_410ff10@0" ObjectIDZND0="g_2b5ae50@1" Pin0InfoVect0LinkObjId="g_2b5ae50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_410ff10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3545,-367 3545,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_41b2330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3545,-410 3545,-426 3577,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b5ae50@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3d66a70@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3d66a70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5ae50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3545,-410 3545,-426 3577,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4208030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-480 3740,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19168@0" ObjectIDZND0="19162@0" Pin0InfoVect0LinkObjId="SW-88758_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40c0510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-480 3740,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ce640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-323 3740,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="hydroGenerator" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="19162@x" ObjectIDZND0="43272@0" Pin0InfoVect0LinkObjId="SM-CX_DW.CX_DW_GN1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-88758_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-323 3740,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2649a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-209 3673,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2649c80@0" ObjectIDZND0="g_2b34fd0@1" Pin0InfoVect0LinkObjId="g_2b34fd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2649c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-209 3673,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_41b33a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-281 3673,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b34fd0@0" Pin0InfoVect0LinkObjId="g_2b34fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-281 3673,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a26b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-216 3596,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2a26dc0@0" ObjectIDZND0="g_25d4330@1" Pin0InfoVect0LinkObjId="g_25d4330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a26dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-216 3596,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2648a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3556,-251 3556,-270 3596,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3e426f0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_25d4330@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_25d4330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e426f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3556,-251 3556,-270 3596,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35fbaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-281 3596,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3e426f0@0" ObjectIDZND1="g_25d4330@0" Pin0InfoVect0LinkObjId="g_3e426f0_0" Pin0InfoVect1LinkObjId="g_25d4330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-281 3596,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35fbd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-270 3596,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3e426f0@0" ObjectIDND1="0@x" ObjectIDZND0="g_25d4330@0" Pin0InfoVect0LinkObjId="g_25d4330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e426f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-270 3596,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35fbf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-298 3673,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="43272@x" ObjectIDZND2="19162@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SM-CX_DW.CX_DW_GN1_0" Pin0InfoVect2LinkObjId="SW-88758_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-298 3673,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a87a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-298 3596,-323 3673,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="43272@x" ObjectIDZND2="19162@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SM-CX_DW.CX_DW_GN1_0" Pin0InfoVect2LinkObjId="SW-88758_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-298 3596,-323 3673,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dfb660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-323 3740,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="hydroGenerator" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="43272@x" ObjectIDZND1="19162@x" ObjectIDZND2="19163@x" Pin0InfoVect0LinkObjId="SM-CX_DW.CX_DW_GN1_0" Pin0InfoVect1LinkObjId="SW-88758_0" Pin0InfoVect2LinkObjId="SW-88759_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-323 3740,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46636d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-362 3740,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19162@1" ObjectIDZND0="43272@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SM-CX_DW.CX_DW_GN1_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88758_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-362 3740,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4663930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-348 3740,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19162@x" ObjectIDND1="19163@x" ObjectIDZND0="43272@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SM-CX_DW.CX_DW_GN1_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88758_0" Pin1InfoVect1LinkObjId="SW-88759_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-348 3740,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4663b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3664,-348 3679,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25db1f0@0" ObjectIDZND0="19163@0" Pin0InfoVect0LinkObjId="SW-88759_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25db1f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3664,-348 3679,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad1360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3715,-348 3740,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="hydroGenerator" EndDevType2="switch" ObjectIDND0="19163@1" ObjectIDZND0="19162@x" ObjectIDZND1="43272@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-88758_0" Pin0InfoVect1LinkObjId="SM-CX_DW.CX_DW_GN1_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88759_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3715,-348 3740,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40bbea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-250 3819,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3dfb8c0@0" ObjectIDZND0="g_2ad1b50@0" Pin0InfoVect0LinkObjId="g_2ad1b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dfb8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-250 3819,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40bc100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-303 3819,-324 3740,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ad1b50@1" ObjectIDZND0="43272@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SM-CX_DW.CX_DW_GN1_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad1b50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-303 3819,-324 3740,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40bc360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3906,-480 3906,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19168@0" ObjectIDZND0="19135@0" Pin0InfoVect0LinkObjId="SW-88731_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40c0510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3906,-480 3906,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0a280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3906,-361 3906,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="19135@1" ObjectIDZND0="g_2b09a20@0" Pin0InfoVect0LinkObjId="g_2b09a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88731_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3906,-361 3906,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f13f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3906,-310 3906,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b09a20@1" ObjectIDZND0="g_3f14190@1" Pin0InfoVect0LinkObjId="g_3f14190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b09a20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3906,-310 3906,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ef49c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3906,-264 3906,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3f14190@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f14190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3906,-264 3906,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d57c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-480 3992,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19168@0" ObjectIDZND0="19136@0" Pin0InfoVect0LinkObjId="SW-88732_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40c0510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-480 3992,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d7270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-361 3992,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="19136@1" ObjectIDZND0="g_34d59b0@0" Pin0InfoVect0LinkObjId="g_34d59b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88732_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-361 3992,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d74d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-309 3992,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_34d59b0@1" ObjectIDZND0="g_23d7730@1" Pin0InfoVect0LinkObjId="g_23d7730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d59b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-309 3992,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d4b530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-263 3992,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_23d7730@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23d7730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-263 3992,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4115b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-482 4154,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19169@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c65900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-482 4154,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4115dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-440 4154,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_4116280@0" ObjectIDZND1="g_3f843b0@0" Pin0InfoVect0LinkObjId="g_4116280_0" Pin0InfoVect1LinkObjId="g_3f843b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-440 4154,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4116020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-428 4178,-428 4178,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_3f843b0@0" ObjectIDZND0="g_4116280@0" Pin0InfoVect0LinkObjId="g_4116280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3f843b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-428 4178,-428 4178,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e29e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-369 4122,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3e2a340@0" ObjectIDZND0="g_3f843b0@1" Pin0InfoVect0LinkObjId="g_3f843b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e2a340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-369 4122,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e2a0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-412 4122,-428 4154,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3f843b0@0" ObjectIDZND0="g_4116280@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_4116280_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f843b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-412 4122,-428 4154,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d04bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-482 4317,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19169@0" ObjectIDZND0="19164@0" Pin0InfoVect0LinkObjId="SW-88760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c65900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-482 4317,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_436d8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-325 4317,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_23d5e50@0" ObjectIDZND0="43273@0" Pin0InfoVect0LinkObjId="SM-CX_DW.CX_DW_GN2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_23d5e50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-325 4317,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2322b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4250,-211 4250,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2322da0@0" ObjectIDZND0="g_3374f30@1" Pin0InfoVect0LinkObjId="g_3374f30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2322da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4250,-211 4250,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26127e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4250,-283 4250,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3374f30@0" Pin0InfoVect0LinkObjId="g_3374f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4250,-283 4250,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_363a9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-218 4173,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_363ac20@0" ObjectIDZND0="g_3639ff0@1" Pin0InfoVect0LinkObjId="g_3639ff0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_363ac20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-218 4173,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34d7380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-253 4133,-272 4173,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3f2c720@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3639ff0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3639ff0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f2c720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-253 4133,-272 4173,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34d75c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-283 4173,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3f2c720@0" ObjectIDZND1="g_3639ff0@0" Pin0InfoVect0LinkObjId="g_3f2c720_0" Pin0InfoVect1LinkObjId="g_3639ff0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-283 4173,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_41ad1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-272 4173,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_3f2c720@0" ObjectIDZND0="g_3639ff0@0" Pin0InfoVect0LinkObjId="g_3639ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3f2c720_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-272 4173,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ad430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4250,-300 4250,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="43273@x" ObjectIDZND2="g_23d5e50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SM-CX_DW.CX_DW_GN2_0" Pin0InfoVect2LinkObjId="g_23d5e50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4250,-300 4250,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ad690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-300 4173,-325 4250,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="43273@x" ObjectIDZND2="g_23d5e50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SM-CX_DW.CX_DW_GN2_0" Pin0InfoVect2LinkObjId="g_23d5e50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-300 4173,-325 4250,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ad8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4250,-325 4317,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="43273@x" ObjectIDZND1="g_23d5e50@0" ObjectIDZND2="19165@x" Pin0InfoVect0LinkObjId="SM-CX_DW.CX_DW_GN2_0" Pin0InfoVect1LinkObjId="g_23d5e50_0" Pin0InfoVect2LinkObjId="SW-88761_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4250,-325 4317,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f03e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-364 4317,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="hydroGenerator" ObjectIDND0="19164@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="43273@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SM-CX_DW.CX_DW_GN2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88760_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-364 4317,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f040f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-350 4317,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="hydroGenerator" ObjectIDND0="19165@x" ObjectIDND1="19164@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="43273@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SM-CX_DW.CX_DW_GN2_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88761_0" Pin1InfoVect1LinkObjId="SW-88760_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-350 4317,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f04350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4241,-350 4256,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_46a23e0@0" ObjectIDZND0="19165@0" Pin0InfoVect0LinkObjId="SW-88761_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_46a23e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4241,-350 4256,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d5600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4292,-350 4317,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="hydroGenerator" ObjectIDND0="19165@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="43273@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SM-CX_DW.CX_DW_GN2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4292,-350 4317,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d6620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-252 4396,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_41adb50@0" ObjectIDZND0="g_23d5e50@0" Pin0InfoVect0LinkObjId="g_23d5e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41adb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-252 4396,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d6880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-305 4396,-326 4317,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="hydroGenerator" ObjectIDND0="g_23d5e50@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="43273@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SM-CX_DW.CX_DW_GN2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23d5e50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-305 4396,-326 4317,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dfdb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-482 4512,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19169@0" ObjectIDZND0="19137@0" Pin0InfoVect0LinkObjId="SW-88733_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c65900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-482 4512,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dfe700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-364 4512,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="19137@1" ObjectIDZND0="g_3dfdd70@0" Pin0InfoVect0LinkObjId="g_3dfdd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88733_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-364 4512,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dfe960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-312 4512,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3dfdd70@1" ObjectIDZND0="g_3dfebc0@1" Pin0InfoVect0LinkObjId="g_3dfebc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dfdd70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-312 4512,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2acffc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-266 4512,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3dfebc0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dfebc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-266 4512,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f60100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-823 4889,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3fbff20@1" Pin0InfoVect0LinkObjId="g_3fbff20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-823 4889,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f602f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-718 4889,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3fbff20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fbff20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-718 4889,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25e0240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-598 4890,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_25e04a0@1" Pin0InfoVect0LinkObjId="g_25e04a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-598 4890,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f8a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-542 4890,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_25e04a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e04a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-542 4890,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ebce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-500 4890,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-500 4890,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_410d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-453 4890,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-453 4890,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_410d830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-419 4890,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-419 4890,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3cec810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-825 5129,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3cec0a0@1" Pin0InfoVect0LinkObjId="g_3cec0a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-825 5129,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ceca70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-720 5129,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3cec0a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cec0a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-720 5129,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_265d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-600 5130,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_265d550@1" Pin0InfoVect0LinkObjId="g_265d550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-600 5130,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_331edd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-544 5130,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_265d550@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_265d550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-544 5130,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_331f030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-502 5130,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-502 5130,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f88720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-455 5130,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-455 5130,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f88980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-421 5130,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-421 5130,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e271d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5339,-636 5339,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34e3580@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e3580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5339,-636 5339,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e27430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5339,-502 5339,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5339,-502 5339,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29d2ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5339,-455 5339,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5339,-455 5339,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29d2d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5339,-421 5339,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5339,-421 5339,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f02140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5338,-686 5338,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_34e3580@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e3580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5338,-686 5338,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f023a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5338,-763 5338,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5338,-763 5338,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac5c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5175,-396 5175,-367 5190,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5175,-396 5175,-367 5190,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac5e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-367 5234,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-367 5234,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a913b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5261,-367 5278,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5261,-367 5278,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a91610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5295,-367 5319,-367 5319,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5295,-367 5319,-367 5319,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_41b1780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4841,-394 4841,-318 5037,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4841,-394 4841,-318 5037,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3defed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-318 5100,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-318 5100,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3df0130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-318 5183,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-318 5183,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3df0390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5200,-318 5378,-318 5378,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5200,-318 5378,-318 5378,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3df15a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4918,-366 4909,-366 4909,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4918,-366 4909,-366 4909,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_411c990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4935,-366 4978,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4935,-366 4978,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_411cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-366 5040,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-366 5040,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_411cdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5057,-366 5069,-366 5069,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5057,-366 5069,-366 5069,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_411e450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-1159 4210,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_466f800@0" ObjectIDND1="29090@1" ObjectIDND2="g_40deed0@0" ObjectIDZND0="19161@x" ObjectIDZND1="19159@x" Pin0InfoVect0LinkObjId="SW-88757_0" Pin0InfoVect1LinkObjId="SW-88755_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_466f800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_40deed0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-1159 4210,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3de6fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-803 3867,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="19156@x" ObjectIDND1="19142@x" ObjectIDND2="19141@x" ObjectIDZND0="19147@0" Pin0InfoVect0LinkObjId="SW-88743_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-88752_0" Pin1InfoVect1LinkObjId="SW-88738_0" Pin1InfoVect2LinkObjId="SW-88737_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-803 3867,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3de7a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-1028 4210,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="19160@x" ObjectIDND1="19151@x" ObjectIDND2="19148@x" ObjectIDZND0="19159@0" Pin0InfoVect0LinkObjId="SW-88755_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-88756_0" Pin1InfoVect1LinkObjId="SW-88747_0" Pin1InfoVect2LinkObjId="SW-88744_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-1028 4210,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3de7cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-1082 4210,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="19159@1" ObjectIDZND0="19161@x" ObjectIDZND1="g_466f800@0" ObjectIDZND2="29090@1" Pin0InfoVect0LinkObjId="SW-88757_0" Pin0InfoVect1LinkObjId="g_466f800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88755_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-1082 4210,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40088c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-1011 4210,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19151@x" ObjectIDND1="19148@x" ObjectIDZND0="19160@x" ObjectIDZND1="19159@x" Pin0InfoVect0LinkObjId="SW-88756_0" Pin0InfoVect1LinkObjId="SW-88755_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88747_0" Pin1InfoVect1LinkObjId="SW-88744_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-1011 4210,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4008b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-639 3867,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19166@x" ObjectIDND1="19143@x" ObjectIDZND0="19141@0" Pin0InfoVect0LinkObjId="SW-88737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40d5e50_0" Pin1InfoVect1LinkObjId="SW-88739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-639 3867,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4008d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-690 3867,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19141@1" ObjectIDZND0="19142@x" ObjectIDZND1="19156@x" ObjectIDZND2="19147@x" Pin0InfoVect0LinkObjId="SW-88738_0" Pin0InfoVect1LinkObjId="SW-88752_0" Pin0InfoVect2LinkObjId="SW-88743_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-690 3867,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4009850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-691 4402,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19144@1" ObjectIDZND0="19145@x" ObjectIDZND1="g_34d4fe0@0" ObjectIDZND2="19155@x" Pin0InfoVect0LinkObjId="SW-88741_0" Pin0InfoVect1LinkObjId="g_34d4fe0_0" Pin0InfoVect2LinkObjId="SW-88751_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-691 4402,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4009ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-860 4402,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="19152@1" ObjectIDZND0="19154@x" ObjectIDZND1="19139@x" Pin0InfoVect0LinkObjId="SW-88750_0" Pin0InfoVect1LinkObjId="SW-88735_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88748_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-860 4402,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_400a580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-803 4402,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="19155@x" ObjectIDND1="g_34d4fe0@0" ObjectIDND2="19145@x" ObjectIDZND0="19152@0" Pin0InfoVect0LinkObjId="SW-88748_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-88751_0" Pin1InfoVect1LinkObjId="g_34d4fe0_0" Pin1InfoVect2LinkObjId="SW-88741_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-803 4402,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4185380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-756 3867,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_40cf090@0" ObjectIDZND0="19142@x" ObjectIDZND1="19141@x" ObjectIDZND2="19156@x" Pin0InfoVect0LinkObjId="SW-88738_0" Pin0InfoVect1LinkObjId="SW-88737_0" Pin0InfoVect2LinkObjId="SW-88752_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40cf090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-756 3867,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4186070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-707 3867,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="19142@x" ObjectIDND1="19141@x" ObjectIDZND0="19156@x" ObjectIDZND1="19147@x" ObjectIDZND2="g_40cf090@0" Pin0InfoVect0LinkObjId="SW-88752_0" Pin0InfoVect1LinkObjId="SW-88743_0" Pin0InfoVect2LinkObjId="g_40cf090_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88738_0" Pin1InfoVect1LinkObjId="SW-88737_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-707 3867,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_41862d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-756 3867,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19142@x" ObjectIDND1="19141@x" ObjectIDND2="g_40cf090@0" ObjectIDZND0="19156@x" ObjectIDZND1="19147@x" Pin0InfoVect0LinkObjId="SW-88752_0" Pin0InfoVect1LinkObjId="SW-88743_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-88738_0" Pin1InfoVect1LinkObjId="SW-88737_0" Pin1InfoVect2LinkObjId="g_40cf090_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-756 3867,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4186530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3915,-728 3915,-756 3866,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4192280@0" ObjectIDZND0="19142@x" ObjectIDZND1="19141@x" ObjectIDZND2="19156@x" Pin0InfoVect0LinkObjId="SW-88738_0" Pin0InfoVect1LinkObjId="SW-88737_0" Pin0InfoVect2LinkObjId="SW-88752_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4192280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3915,-728 3915,-756 3866,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4186790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-758 4402,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_34d4fe0@0" ObjectIDZND0="19145@x" ObjectIDZND1="19144@x" ObjectIDZND2="19155@x" Pin0InfoVect0LinkObjId="SW-88741_0" Pin0InfoVect1LinkObjId="SW-88740_0" Pin0InfoVect2LinkObjId="SW-88751_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d4fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-758 4402,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da6cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-709 4402,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19145@x" ObjectIDND1="19144@x" ObjectIDZND0="g_34d4fe0@0" ObjectIDZND1="19155@x" ObjectIDZND2="19152@x" Pin0InfoVect0LinkObjId="g_34d4fe0_0" Pin0InfoVect1LinkObjId="SW-88751_0" Pin0InfoVect2LinkObjId="SW-88748_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88741_0" Pin1InfoVect1LinkObjId="SW-88740_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-709 4402,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da6f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-758 4402,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_34d4fe0@0" ObjectIDND1="19145@x" ObjectIDND2="19144@x" ObjectIDZND0="19155@x" ObjectIDZND1="19152@x" Pin0InfoVect0LinkObjId="SW-88751_0" Pin0InfoVect1LinkObjId="SW-88748_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34d4fe0_0" Pin1InfoVect1LinkObjId="SW-88741_0" Pin1InfoVect2LinkObjId="SW-88740_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-758 4402,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da7190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-730 4450,-758 4402,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3cffb10@0" ObjectIDZND0="g_34d4fe0@0" ObjectIDZND1="19145@x" ObjectIDZND2="19144@x" Pin0InfoVect0LinkObjId="g_34d4fe0_0" Pin0InfoVect1LinkObjId="SW-88741_0" Pin0InfoVect2LinkObjId="SW-88740_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cffb10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-730 4450,-758 4402,-758 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DW"/>
</svg>