<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-13" aopId="3941118" id="thSvg" product="E8000V2" version="1.0" viewBox="3086 -1214 2222 1260">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape26">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="48" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="19" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="49" x2="46" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="37" x2="21" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="58" y2="58"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="30"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="91" y2="24"/>
    <polyline arcFlag="1" points="11,39 10,39 9,39 9,40 8,40 8,40 7,41 7,41 6,42 6,42 6,43 6,43 5,44 5,45 5,45 6,46 6,47 6,47 6,48 7,48 7,49 8,49 8,50 9,50 9,50 10,50 11,50 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,28 10,28 9,28 9,28 8,29 8,29 7,29 7,30 6,30 6,31 6,32 6,32 5,33 5,34 5,34 6,35 6,36 6,36 6,37 7,37 7,38 8,38 8,38 9,39 9,39 10,39 11,39 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,18 10,18 9,18 9,18 8,18 8,19 7,19 7,20 6,20 6,21 6,21 6,22 5,23 5,23 5,24 6,25 6,25 6,26 6,26 7,27 7,27 8,28 8,28 9,28 9,29 10,29 11,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="57" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="18" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="103" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="90" y2="90"/>
    <polyline arcFlag="1" points="27,103 25,103 23,102 22,102 20,101 19,100 17,99 16,97 15,95 15,94 14,92 14,90 14,88 15,86 15,85 16,83 17,82 19,80 20,79 22,78 23,78 25,77 27,77 29,77 31,78 32,78 34,79 35,80 37,82 38,83 39,85 39,86 40,88 40,90 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="4" x2="13" y1="40" y2="40"/>
    <circle cx="24" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="34" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="13" x2="13" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="38" x2="20" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="47" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape57">
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.897222" x1="7" x2="7" y1="62" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="3" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="22" x2="36" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="23" x2="36" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="54" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="29" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="23" x2="35" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="23" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="34" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="27" x2="30" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="25" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="23" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="43" y1="31" y2="31"/>
    <circle cx="55" cy="23" fillStyle="0" r="7" stroke-width="1"/>
    <circle cx="49" cy="30" fillStyle="0" r="7" stroke-width="1"/>
    <circle cx="61" cy="30" fillStyle="0" r="7" stroke-width="1"/>
    <circle cx="55" cy="35" fillStyle="0" r="7" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape61">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="14" y2="14"/>
    <circle cx="37" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="30" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="37" cy="15" fillStyle="0" r="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="75" y2="22"/>
    <rect height="27" stroke-width="0.416667" width="14" x="23" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="29" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="65" y2="34"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="20" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,57 6,57 6,28 " stroke-width="1"/>
    <circle cx="31" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="55" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1585fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1578750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_153f3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14f3470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1541b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1543420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1543d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14ecfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14f8930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1550ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1561160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1561a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_154fde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1567330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14e9c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14ea3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15ad3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a1280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a1a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15400a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15410d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_155f620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1570b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15a2e30" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19ef000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19f8380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19f7a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19f4640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1270" width="2232" x="3081" y="-1219"/>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="3863" cy="-1143" fill="rgb(0,0,0)" fillStyle="1" r="10" stroke="rgb(255,255,255)" stroke-width="1.75238"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3878" x2="3878" y1="-88" y2="-60"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4757" x2="4757" y1="-86" y2="-58"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4925" x2="4925" y1="-89" y2="-61"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4297.000000 -435.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28230">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -780.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4563" ObjectName="SW-CX_WM.CX_WM_101BK"/>
     <cge:Meas_Ref ObjectId="28230"/>
    <cge:TPSR_Ref TObjectID="4563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -440.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4572" ObjectName="SW-CX_WM.CX_WM_001BK"/>
     <cge:Meas_Ref ObjectId="28245"/>
    <cge:TPSR_Ref TObjectID="4572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 -426.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4669.000000 -628.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -947.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4569" ObjectName="SW-CX_WM.CX_WM_301BK"/>
     <cge:Meas_Ref ObjectId="28240"/>
    <cge:TPSR_Ref TObjectID="4569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28491">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -957.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4614" ObjectName="SW-CX_WM.CX_WM_191BK"/>
     <cge:Meas_Ref ObjectId="28491"/>
    <cge:TPSR_Ref TObjectID="4614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28393">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 -930.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4598" ObjectName="SW-CX_WM.CX_WM_374BK"/>
     <cge:Meas_Ref ObjectId="28393"/>
    <cge:TPSR_Ref TObjectID="4598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4918.000000 -738.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -782.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28191">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -958.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4557" ObjectName="SW-CX_WM.CX_WM_192BK"/>
     <cge:Meas_Ref ObjectId="28191"/>
    <cge:TPSR_Ref TObjectID="4557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28145">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 -958.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4551" ObjectName="SW-CX_WM.CX_WM_193BK"/>
     <cge:Meas_Ref ObjectId="28145"/>
    <cge:TPSR_Ref TObjectID="4551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55059">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.000000 -959.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4616" ObjectName="SW-CX_WM.CX_WM_194BK"/>
     <cge:Meas_Ref ObjectId="55059"/>
    <cge:TPSR_Ref TObjectID="4616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28373">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -811.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4588" ObjectName="SW-CX_WM.CX_WM_112BK"/>
     <cge:Meas_Ref ObjectId="28373"/>
    <cge:TPSR_Ref TObjectID="4588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4926.000000 -800.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4601" ObjectName="SW-CX_WM.CX_WM_375BK"/>
     <cge:Meas_Ref ObjectId="28405"/>
    <cge:TPSR_Ref TObjectID="4601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4926.000000 -1151.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.000000 -1007.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4595" ObjectName="SW-CX_WM.CX_WM_373BK"/>
     <cge:Meas_Ref ObjectId="28381"/>
    <cge:TPSR_Ref TObjectID="4595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -693.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4694.000000 -259.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4926.000000 -615.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.778846 -541.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.778846 -469.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4933.778846 -394.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4862.000000 -262.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.071429 -0.000000 0.000000 -1.000000 4482.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.071429 -0.000000 0.000000 -1.000000 4557.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3519.000000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20064" ObjectName="SW-CX_WM.CX_WM_071BK"/>
     <cge:Meas_Ref ObjectId="95793"/>
    <cge:TPSR_Ref TObjectID="20064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95808">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.071429 -0.000000 0.000000 -1.000000 3616.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20067" ObjectName="SW-CX_WM.CX_WM_072BK"/>
     <cge:Meas_Ref ObjectId="95808"/>
    <cge:TPSR_Ref TObjectID="20067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4612" ObjectName="SW-CX_WM.CX_WM_074BK"/>
     <cge:Meas_Ref ObjectId="28452"/>
    <cge:TPSR_Ref TObjectID="4612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28417">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -242.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4604" ObjectName="SW-CX_WM.CX_WM_076BK"/>
     <cge:Meas_Ref ObjectId="28417"/>
    <cge:TPSR_Ref TObjectID="4604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4231.000000 -245.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4607" ObjectName="SW-CX_WM.CX_WM_077BK"/>
     <cge:Meas_Ref ObjectId="28431"/>
    <cge:TPSR_Ref TObjectID="4607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95823">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.071429 -0.000000 0.000000 -1.000000 3715.000000 -239.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20070" ObjectName="SW-CX_WM.CX_WM_073BK"/>
     <cge:Meas_Ref ObjectId="95823"/>
    <cge:TPSR_Ref TObjectID="20070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.000000 -1079.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29442" ObjectName="SW-CX_WM.CX_WM_372BK"/>
     <cge:Meas_Ref ObjectId="193562"/>
    <cge:TPSR_Ref TObjectID="29442"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_WM.CX_WM_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="6690"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 -545.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="6692"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 -545.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="6694"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 -545.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4617" ObjectName="TF-CX_WM.CX_WM_1T"/>
    <cge:TPSR_Ref TObjectID="4617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.000000 -591.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.000000 -591.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.000000 -591.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4641,-144 4641,-108 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4641,-144 4641,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4567,-144 4567,-110 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4567,-144 4567,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4492,-144 4492,-106 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4492,-144 4492,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4418,-140 4418,-106 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4418,-140 4418,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongwanTwanma_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4132,-1183 4132,-1139 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="14288" ObjectName="AC-110kV.yongwanTwanma_line"/>
    <cge:TPSR_Ref TObjectID="14288_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="4132,-1183 4132,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DDH" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.duowan_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4442,-1181 4442,-1135 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11712" ObjectName="AC-110kV.duowan_line"/>
    <cge:TPSR_Ref TObjectID="11712_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="4442,-1181 4442,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WM" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongganwanTwanma" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3888,-1181 3888,-1139 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18018" ObjectName="AC-110kV.yongganwanTwanma"/>
    <cge:TPSR_Ref TObjectID="18018_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="3888,-1181 3888,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YIZ" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yiwan_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3610,-1180 3610,-1136 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11713" ObjectName="AC-110kV.yiwan_line"/>
    <cge:TPSR_Ref TObjectID="11713_SS-13"/></metadata>
   <polyline fill="none" opacity="0" points="3610,-1180 3610,-1136 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.LD373">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5103.000000 -1008.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14290" ObjectName="EC-CX_WM.LD373"/>
    <cge:TPSR_Ref TObjectID="14290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.LD374">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5108.000000 -931.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14292" ObjectName="EC-CX_WM.LD374"/>
    <cge:TPSR_Ref TObjectID="14292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.LD375">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5112.000000 -801.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14291" ObjectName="EC-CX_WM.LD375"/>
    <cge:TPSR_Ref TObjectID="14291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.LD076">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -61.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14293" ObjectName="EC-CX_WM.LD076"/>
    <cge:TPSR_Ref TObjectID="14293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.LD077">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4231.000000 -63.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14294" ObjectName="EC-CX_WM.LD077"/>
    <cge:TPSR_Ref TObjectID="14294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.LD071">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3519.000000 -74.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20078" ObjectName="EC-CX_WM.LD071"/>
    <cge:TPSR_Ref TObjectID="20078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.LD072">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3617.000000 -74.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20079" ObjectName="EC-CX_WM.LD072"/>
    <cge:TPSR_Ref TObjectID="20079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_WM.LD073">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 -69.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20080" ObjectName="EC-CX_WM.LD073"/>
    <cge:TPSR_Ref TObjectID="20080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5107.000000 -1080.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2fc02d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.000000 -753.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34fa970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3679.000000 -535.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3628630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 -582.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2642770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3671.000000 -1053.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_362d070" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3671.000000 -996.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3038fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3671.000000 -951.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345a0c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -827.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_282cb10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.000000 -829.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30a7280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -698.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3572630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 -769.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34a9db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.666667 -923.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4e410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3803.666667 -996.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd04a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -1053.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20437b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -996.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2830850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -952.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3142140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -1054.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed1c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -997.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc4590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -952.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3043aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.666667 -922.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d5710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4331.666667 -999.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_326fde0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -1055.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3393ac0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -998.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2712350" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -952.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3088000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -733.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3649700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.000000 -734.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32832b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4761.000000 -132.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25490c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -135.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_308dc20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -134.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb3e90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.000000 -669.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ac8660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 -78.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ec90e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 -51.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e8f4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -76.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e92dc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -49.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28810f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4953.000000 -79.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28848d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4953.000000 -52.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_3548e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-759 3845,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2fc02d0@0" ObjectIDZND0="4568@1" Pin0InfoVect0LinkObjId="SW-28235_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fc02d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-759 3845,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3549020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-759 3790,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4568@0" ObjectIDZND0="4565@x" ObjectIDZND1="4563@x" Pin0InfoVect0LinkObjId="SW-28232_0" Pin0InfoVect1LinkObjId="SW-28230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-759 3790,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3549210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-612 3713,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3443430@0" ObjectIDND1="4617@x" ObjectIDND2="4575@x" ObjectIDZND0="g_33917b0@0" Pin0InfoVect0LinkObjId="g_33917b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3443430_0" Pin1InfoVect1LinkObjId="g_35f26f0_0" Pin1InfoVect2LinkObjId="SW-28250_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-612 3713,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3549400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-612 3736,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="4617@x" ObjectIDND1="g_33917b0@0" ObjectIDND2="4575@x" ObjectIDZND0="g_3443430@0" Pin0InfoVect0LinkObjId="g_3443430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35f26f0_0" Pin1InfoVect1LinkObjId="g_33917b0_0" Pin1InfoVect2LinkObjId="SW-28250_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-612 3736,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3549630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-612 3736,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="4617@x" ObjectIDZND0="g_3443430@0" ObjectIDZND1="g_33917b0@0" ObjectIDZND2="4575@x" Pin0InfoVect0LinkObjId="g_3443430_0" Pin0InfoVect1LinkObjId="g_33917b0_0" Pin0InfoVect2LinkObjId="SW-28250_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f26f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-612 3736,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3549860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3736,-612 3713,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3443430@0" ObjectIDND1="4617@x" ObjectIDZND0="g_33917b0@0" ObjectIDZND1="4575@x" Pin0InfoVect0LinkObjId="g_33917b0_0" Pin0InfoVect1LinkObjId="SW-28250_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3443430_0" Pin1InfoVect1LinkObjId="g_35f26f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3736,-612 3713,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3549a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-552 3685,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34fa970@0" ObjectIDZND0="4575@1" Pin0InfoVect0LinkObjId="SW-28250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fa970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-552 3685,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3549cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-602 3685,-612 3713,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="4575@0" ObjectIDZND0="g_33917b0@0" ObjectIDZND1="g_3443430@0" ObjectIDZND2="4617@x" Pin0InfoVect0LinkObjId="g_33917b0_0" Pin0InfoVect1LinkObjId="g_3443430_0" Pin0InfoVect2LinkObjId="g_35f26f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-602 3685,-612 3713,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3549ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4338,-661 4338,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_303a750@0" ObjectIDND2="0@x" ObjectIDZND0="g_2fb9000@0" Pin0InfoVect0LinkObjId="g_2fb9000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_303a750_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4338,-661 4338,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_354a120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4315,-611 4315,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3628630@0" Pin0InfoVect0LinkObjId="g_3628630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4315,-611 4315,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_354a380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-638 4750,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-638 4750,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_354a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-638 4678,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-638 4678,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_354a840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-546 4412,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2f69ae0@0" Pin0InfoVect0LinkObjId="g_2f69ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-546 4412,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_354aaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-594 4427,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@2" ObjectIDZND0="g_2f69ae0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2f69ae0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-594 4427,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354ad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-895 3610,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDZND0="4615@1" Pin0InfoVect0LinkObjId="SW-28492_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-895 3610,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354af60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-946 3610,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4615@0" ObjectIDZND0="4614@x" ObjectIDZND1="9693@x" Pin0InfoVect0LinkObjId="SW-28491_0" Pin0InfoVect1LinkObjId="SW-54984_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-946 3610,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354b1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-955 3610,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4615@x" ObjectIDND1="9693@x" ObjectIDZND0="4614@0" Pin0InfoVect0LinkObjId="SW-28491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28492_0" Pin1InfoVect1LinkObjId="SW-54984_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-955 3610,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354b420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3663,-1059 3676,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9696@1" ObjectIDZND0="g_2642770@0" Pin0InfoVect0LinkObjId="g_2642770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54987_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3663,-1059 3676,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354b680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-1002 3628,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4614@x" ObjectIDND1="9694@x" ObjectIDZND0="9695@0" Pin0InfoVect0LinkObjId="SW-54986_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28491_0" Pin1InfoVect1LinkObjId="SW-54985_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-1002 3628,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354b8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3663,-1002 3676,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9695@1" ObjectIDZND0="g_362d070@0" Pin0InfoVect0LinkObjId="g_362d070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54986_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3663,-1002 3676,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354bb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-992 3610,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4614@1" ObjectIDZND0="9695@x" ObjectIDZND1="9694@x" Pin0InfoVect0LinkObjId="SW-54986_0" Pin0InfoVect1LinkObjId="SW-54985_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28491_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-992 3610,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354bda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-1002 3610,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4614@x" ObjectIDND1="9695@x" ObjectIDZND0="9694@1" Pin0InfoVect0LinkObjId="SW-54985_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28491_0" Pin1InfoVect1LinkObjId="SW-54986_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-1002 3610,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354c000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-957 3628,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4614@x" ObjectIDND1="4615@x" ObjectIDZND0="9693@0" Pin0InfoVect0LinkObjId="SW-54984_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28491_0" Pin1InfoVect1LinkObjId="SW-28492_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-957 3628,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354c260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3663,-957 3676,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9693@1" ObjectIDZND0="g_3038fc0@0" Pin0InfoVect0LinkObjId="g_3038fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54984_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3663,-957 3676,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354c4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-940 4875,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4596@0" Pin0InfoVect0LinkObjId="SW-28391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a6690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-940 4875,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eee1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-940 4911,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4598@1" ObjectIDZND0="4596@1" Pin0InfoVect0LinkObjId="SW-28391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-940 4911,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eee420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-940 4960,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4597@0" ObjectIDZND0="4598@0" Pin0InfoVect0LinkObjId="SW-28393_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-940 4960,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eee680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-833 4481,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_345a0c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_345a0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-833 4481,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eee8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-833 4428,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-833 4428,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2eeeb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3865,-835 3847,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_282cb10@0" ObjectIDZND0="4566@1" Pin0InfoVect0LinkObjId="SW-28233_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_282cb10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3865,-835 3847,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eeeda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-704 4481,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_30a7280@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30a7280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-704 4481,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eef000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-704 4428,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-704 4428,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eef260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4498,-775 4480,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3572630@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3572630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4498,-775 4480,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2eef4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-882 4428,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="4548@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-882 4428,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eef720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-833 4428,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-833 4428,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eef980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-775 4428,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-775 4428,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eefbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-833 4428,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-833 4428,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eefe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-790 4428,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-790 4428,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ef00a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-775 4428,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-775 4428,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ef0300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-717 4428,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-717 4428,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ef0560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-704 4428,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-704 4428,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef07c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-929 3760,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDND1="4576@x" ObjectIDZND0="4577@0" Pin0InfoVect0LinkObjId="SW-28320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-28319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-929 3760,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef0a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-929 3810,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4577@1" ObjectIDZND0="g_34a9db0@0" Pin0InfoVect0LinkObjId="g_34a9db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-929 3810,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef0c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-895 3743,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4547@0" ObjectIDZND0="4577@x" ObjectIDZND1="4576@x" Pin0InfoVect0LinkObjId="SW-28320_0" Pin0InfoVect1LinkObjId="SW-28319_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-895 3743,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef0ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-1002 3762,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1a4eea0@0" ObjectIDND1="4576@x" ObjectIDZND0="4578@0" Pin0InfoVect0LinkObjId="SW-28321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a4eea0_0" Pin1InfoVect1LinkObjId="SW-28319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-1002 3762,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef1140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3796,-1002 3809,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4578@1" ObjectIDZND0="g_1a4e410@0" Pin0InfoVect0LinkObjId="g_1a4e410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28321_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3796,-1002 3809,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef13a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-895 3888,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDZND0="4558@1" Pin0InfoVect0LinkObjId="SW-28192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-895 3888,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef1600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-947 3888,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4558@0" ObjectIDZND0="4557@x" ObjectIDZND1="4560@x" Pin0InfoVect0LinkObjId="SW-28191_0" Pin0InfoVect1LinkObjId="SW-28194_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-947 3888,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef1860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-956 3888,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4558@x" ObjectIDND1="4560@x" ObjectIDZND0="4557@0" Pin0InfoVect0LinkObjId="SW-28191_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28192_0" Pin1InfoVect1LinkObjId="SW-28194_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-956 3888,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef1ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,-1059 3954,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4561@1" ObjectIDZND0="g_1fd04a0@0" Pin0InfoVect0LinkObjId="g_1fd04a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28195_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3941,-1059 3954,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef1d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-1003 3906,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4557@x" ObjectIDND1="4559@x" ObjectIDZND0="4562@0" Pin0InfoVect0LinkObjId="SW-28196_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28191_0" Pin1InfoVect1LinkObjId="SW-28193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-1003 3906,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef1f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,-1002 3954,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4562@1" ObjectIDZND0="g_20437b0@0" Pin0InfoVect0LinkObjId="g_20437b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28196_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3941,-1002 3954,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef21e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-993 3888,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4557@1" ObjectIDZND0="4562@x" ObjectIDZND1="4559@x" Pin0InfoVect0LinkObjId="SW-28196_0" Pin0InfoVect1LinkObjId="SW-28193_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28191_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-993 3888,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef2440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-1003 3888,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4557@x" ObjectIDND1="4562@x" ObjectIDZND0="4559@1" Pin0InfoVect0LinkObjId="SW-28193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28191_0" Pin1InfoVect1LinkObjId="SW-28196_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-1003 3888,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef26a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-958 3906,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4557@x" ObjectIDND1="4558@x" ObjectIDZND0="4560@0" Pin0InfoVect0LinkObjId="SW-28194_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28191_0" Pin1InfoVect1LinkObjId="SW-28192_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-958 3906,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef2900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,-958 3954,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4560@1" ObjectIDZND0="g_2830850@0" Pin0InfoVect0LinkObjId="g_2830850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28194_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3941,-958 3954,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef2b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-896 4132,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDZND0="4552@1" Pin0InfoVect0LinkObjId="SW-28146_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eef4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-896 4132,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef2dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-947 4132,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4552@0" ObjectIDZND0="4551@x" ObjectIDZND1="4554@x" Pin0InfoVect0LinkObjId="SW-28145_0" Pin0InfoVect1LinkObjId="SW-28148_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28146_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-947 4132,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef3020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-956 4132,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4552@x" ObjectIDND1="4554@x" ObjectIDZND0="4551@0" Pin0InfoVect0LinkObjId="SW-28145_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28146_0" Pin1InfoVect1LinkObjId="SW-28148_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-956 4132,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef3280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4185,-1060 4198,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4555@1" ObjectIDZND0="g_3142140@0" Pin0InfoVect0LinkObjId="g_3142140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28149_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4185,-1060 4198,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef34e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1003 4149,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4551@x" ObjectIDND1="4553@x" ObjectIDZND0="4556@0" Pin0InfoVect0LinkObjId="SW-28150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28145_0" Pin1InfoVect1LinkObjId="SW-28147_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1003 4149,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ef3740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4185,-1003 4198,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4556@1" ObjectIDZND0="g_2ed1c10@0" Pin0InfoVect0LinkObjId="g_2ed1c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28150_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4185,-1003 4198,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35edfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-993 4132,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4551@1" ObjectIDZND0="4556@x" ObjectIDZND1="4553@x" Pin0InfoVect0LinkObjId="SW-28150_0" Pin0InfoVect1LinkObjId="SW-28147_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28145_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-993 4132,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ee210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1003 4132,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4551@x" ObjectIDND1="4556@x" ObjectIDZND0="4553@1" Pin0InfoVect0LinkObjId="SW-28147_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28145_0" Pin1InfoVect1LinkObjId="SW-28150_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1003 4132,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ee470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-958 4149,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4551@x" ObjectIDND1="4552@x" ObjectIDZND0="4554@0" Pin0InfoVect0LinkObjId="SW-28148_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28145_0" Pin1InfoVect1LinkObjId="SW-28146_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-958 4149,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ee6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4185,-958 4198,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4554@1" ObjectIDZND0="g_1fc4590@0" Pin0InfoVect0LinkObjId="g_1fc4590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28148_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4185,-958 4198,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ee930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-928 4289,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDND1="4579@x" ObjectIDZND0="4580@0" Pin0InfoVect0LinkObjId="SW-28324_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2eef4c0_0" Pin1InfoVect1LinkObjId="SW-28323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-928 4289,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35eeb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-928 4338,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4580@1" ObjectIDZND0="g_3043aa0@0" Pin0InfoVect0LinkObjId="g_3043aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28324_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-928 4338,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35eedf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-896 4271,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4548@0" ObjectIDZND0="4580@x" ObjectIDZND1="4579@x" Pin0InfoVect0LinkObjId="SW-28324_0" Pin0InfoVect1LinkObjId="SW-28323_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eef4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-896 4271,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ef050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-1005 4288,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_31d61a0@0" ObjectIDND1="4579@x" ObjectIDZND0="4581@0" Pin0InfoVect0LinkObjId="SW-28325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31d61a0_0" Pin1InfoVect1LinkObjId="SW-28323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-1005 4288,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ef2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-1005 4337,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4581@1" ObjectIDZND0="g_31d5710@0" Pin0InfoVect0LinkObjId="g_31d5710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-1005 4337,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ef510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-896 4442,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDZND0="9697@1" Pin0InfoVect0LinkObjId="SW-55060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eef4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-896 4442,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ef770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-948 4442,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9697@0" ObjectIDZND0="4616@x" ObjectIDZND1="9699@x" Pin0InfoVect0LinkObjId="SW-55059_0" Pin0InfoVect1LinkObjId="SW-55062_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-948 4442,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ef9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-957 4442,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9697@x" ObjectIDND1="9699@x" ObjectIDZND0="4616@0" Pin0InfoVect0LinkObjId="SW-55059_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55060_0" Pin1InfoVect1LinkObjId="SW-55062_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-957 4442,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35efc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-1061 4508,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9700@1" ObjectIDZND0="g_326fde0@0" Pin0InfoVect0LinkObjId="g_326fde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-1061 4508,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35efe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-1004 4459,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4616@x" ObjectIDND1="9698@x" ObjectIDZND0="9701@0" Pin0InfoVect0LinkObjId="SW-55064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55059_0" Pin1InfoVect1LinkObjId="SW-55061_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-1004 4459,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f00f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-1004 4508,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9701@1" ObjectIDZND0="g_3393ac0@0" Pin0InfoVect0LinkObjId="g_3393ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55064_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-1004 4508,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f0350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-994 4442,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4616@1" ObjectIDZND0="9701@x" ObjectIDZND1="9698@x" Pin0InfoVect0LinkObjId="SW-55064_0" Pin0InfoVect1LinkObjId="SW-55061_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55059_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-994 4442,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f05b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-1004 4442,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4616@x" ObjectIDND1="9701@x" ObjectIDZND0="9698@1" Pin0InfoVect0LinkObjId="SW-55061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55059_0" Pin1InfoVect1LinkObjId="SW-55064_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-1004 4442,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f0810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-958 4459,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4616@x" ObjectIDND1="9697@x" ObjectIDZND0="9699@0" Pin0InfoVect0LinkObjId="SW-55062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55059_0" Pin1InfoVect1LinkObjId="SW-55060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-958 4459,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f0a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-958 4508,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9699@1" ObjectIDZND0="g_2712350@0" Pin0InfoVect0LinkObjId="g_2712350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55062_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-958 4508,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f0cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-895 3944,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDZND0="4589@0" Pin0InfoVect0LinkObjId="SW-28374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-895 3944,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f0f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-768 3944,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4591@1" ObjectIDZND0="g_3088000@0" Pin0InfoVect0LinkObjId="g_3088000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-768 3944,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f1190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4072,-896 4072,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDZND0="4590@0" Pin0InfoVect0LinkObjId="SW-28375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eef4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4072,-896 4072,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f13f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4072,-769 4072,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4592@1" ObjectIDZND0="g_3649700@0" Pin0InfoVect0LinkObjId="g_3649700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4072,-769 4072,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f1650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-836 3944,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4589@1" ObjectIDZND0="4591@x" ObjectIDZND1="4588@x" Pin0InfoVect0LinkObjId="SW-28376_0" Pin0InfoVect1LinkObjId="SW-28373_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-836 3944,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f18b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-821 3944,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="4589@x" ObjectIDND1="4588@x" ObjectIDZND0="4591@0" Pin0InfoVect0LinkObjId="SW-28376_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28374_0" Pin1InfoVect1LinkObjId="SW-28373_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-821 3944,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f1b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4072,-821 4072,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4588@x" ObjectIDND1="4590@x" ObjectIDZND0="4592@0" Pin0InfoVect0LinkObjId="SW-28377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28373_0" Pin1InfoVect1LinkObjId="SW-28375_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4072,-821 4072,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35f1d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4338,-661 4315,-661 4315,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="g_2fb9000@0" ObjectIDND1="g_303a750@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2fb9000_0" Pin1InfoVect1LinkObjId="g_303a750_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4338,-661 4315,-661 4315,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f1fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-821 3992,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4591@x" ObjectIDND1="4589@x" ObjectIDZND0="4588@1" Pin0InfoVect0LinkObjId="SW-28373_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28376_0" Pin1InfoVect1LinkObjId="SW-28374_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-821 3992,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f2230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-821 4072,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4588@0" ObjectIDZND0="4592@x" ObjectIDZND1="4590@x" Pin0InfoVect0LinkObjId="SW-28377_0" Pin0InfoVect1LinkObjId="SW-28375_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-821 4072,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35f2490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-536 3805,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4617@x" ObjectIDND1="4574@x" ObjectIDZND0="g_208aa40@0" Pin0InfoVect0LinkObjId="g_208aa40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35f26f0_0" Pin1InfoVect1LinkObjId="SW-28247_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-536 3805,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35f26f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-522 3789,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="4574@0" ObjectIDZND0="4617@x" ObjectIDZND1="g_208aa40@0" Pin0InfoVect0LinkObjId="g_35f2950_0" Pin0InfoVect1LinkObjId="g_208aa40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28247_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-522 3789,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35f2950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-536 3789,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer" ObjectIDND0="4574@x" ObjectIDND1="g_208aa40@0" ObjectIDZND0="4617@2" Pin0InfoVect0LinkObjId="g_35f26f0_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28247_0" Pin1InfoVect1LinkObjId="g_208aa40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-536 3789,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f2bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-810 4877,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4599@0" Pin0InfoVect0LinkObjId="SW-28403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a6690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-810 4877,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f2e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4935,-810 4913,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4601@1" ObjectIDZND0="4599@1" Pin0InfoVect0LinkObjId="SW-28403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4935,-810 4913,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f3070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4983,-810 4962,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4600@0" ObjectIDZND0="4601@0" Pin0InfoVect0LinkObjId="SW-28405_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4983,-810 4962,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f32d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5108,-870 5143,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_37661d0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37661d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5108,-870 5143,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f3530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-1161 4877,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a6690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-1161 4877,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255a510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4935,-1161 4913,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4935,-1161 4913,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255a770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4983,-1161 4962,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4983,-1161 4962,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_255a9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-1017 4872,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4593@0" Pin0InfoVect0LinkObjId="SW-28379_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a6690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-1017 4872,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_255ac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-1017 4908,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4595@1" ObjectIDZND0="4593@1" Pin0InfoVect0LinkObjId="SW-28379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-1017 4908,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_255ae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4978,-1017 4957,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4594@0" ObjectIDZND0="4595@0" Pin0InfoVect0LinkObjId="SW-28381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4978,-1017 4957,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255b0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-703 4876,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-703 4876,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255b350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4934,-703 4912,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4934,-703 4912,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255b5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4982,-703 4961,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4982,-703 4961,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-344 4703,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-344 4703,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-138 4716,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3061820@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3061820_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-138 4716,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255bcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4752,-138 4766,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_32832b0@0" Pin0InfoVect0LinkObjId="g_32832b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4752,-138 4766,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-294 4703,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-294 4703,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255c190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-252 4703,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-252 4703,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-216 4703,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3061820@0" Pin0InfoVect0LinkObjId="g_3061820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-216 4703,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255c650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-151 4703,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3061820@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3061820_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-151 4703,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-138 4703,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3061820@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3061820_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-138 4703,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255cb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-525 4427,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2f69ae0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2f69ae0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-525 4427,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-475 3789,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4572@1" ObjectIDZND0="4574@1" Pin0InfoVect0LinkObjId="SW-28247_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-475 3789,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255cfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-357 3789,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4550@0" ObjectIDZND0="4573@1" Pin0InfoVect0LinkObjId="SW-28246_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a2410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-357 3789,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-435 3789,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4573@0" ObjectIDZND0="4572@0" Pin0InfoVect0LinkObjId="SW-28245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-435 3789,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-625 4877,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-625 4877,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255d6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4935,-625 4913,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4935,-625 4913,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4983,-625 4962,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4983,-625 4962,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255dbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-551 4877,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-551 4877,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4935,-551 4913,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4935,-551 4913,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255e070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4983,-551 4962,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4983,-551 4962,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255e2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-479 4880,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-479 4880,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255e530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-479 4916,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-479 4916,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255e790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4986,-479 4965,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4986,-479 4965,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255e9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-404 4885,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-404 4885,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255ec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-404 4921,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-404 4921,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255eeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4991,-404 4970,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4991,-404 4970,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-347 4871,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-347 4871,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255f370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-141 4884,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2545ea0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2545ea0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-141 4884,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255f5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-141 4934,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_25490c0@0" Pin0InfoVect0LinkObjId="g_25490c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-141 4934,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255f830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-297 4871,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-297 4871,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_255fa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-255 4871,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-255 4871,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a3140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-219 4871,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2545ea0@0" Pin0InfoVect0LinkObjId="g_2545ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-219 4871,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a3370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-156 4871,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2545ea0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2545ea0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-156 4871,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a35d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-141 4871,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2545ea0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2545ea0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-141 4871,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a3830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-339 4418,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-339 4418,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a3a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-240 4418,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2816240@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2816240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-240 4418,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a3cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-176 4418,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2816240@1" Pin0InfoVect0LinkObjId="g_2816240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-176 4418,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a3f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-303 4418,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-303 4418,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a41b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-343 4492,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-343 4492,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a4410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-244 4492,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1a93360@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a93360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-244 4492,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a4670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-180 4492,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1a93360@1" Pin0InfoVect0LinkObjId="g_1a93360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-180 4492,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a48d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-307 4492,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-307 4492,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a4b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4567,-343 4568,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4567,-343 4568,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a4d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4567,-244 4567,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_354e120@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354e120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4567,-244 4567,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a4ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4567,-180 4567,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_354e120@1" Pin0InfoVect0LinkObjId="g_354e120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4567,-180 4567,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a5250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4567,-307 4567,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4567,-307 4567,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a54b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-343 4641,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-343 4641,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a5710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-244 4641,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_320e2f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_320e2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-244 4641,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a5970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-180 4641,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_320e2f0@1" Pin0InfoVect0LinkObjId="g_320e2f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-180 4641,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a5bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-307 4641,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-307 4641,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a5e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-358 5009,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-358 5009,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a6090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-306 5009,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_26276f0@0" Pin0InfoVect0LinkObjId="g_26276f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-306 5009,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a62f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-260 5009,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_26276f0@1" ObjectIDZND0="g_2627f70@0" Pin0InfoVect0LinkObjId="g_2627f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26276f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-260 5009,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a6550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-185 5009,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2627f70@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2627f70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-185 5009,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a67b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-358 5128,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-358 5128,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a6a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-302 5128,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_35c88d0@0" Pin0InfoVect0LinkObjId="g_35c88d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-302 5128,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34a6c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-1090 4827,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4583@1" Pin0InfoVect0LinkObjId="SW-28334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a6690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-1090 4827,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34a6ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1090 4769,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4583@0" ObjectIDZND0="g_338a1f0@0" Pin0InfoVect0LinkObjId="g_338a1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1090 4769,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a7130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-442 4818,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-442 4818,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a7390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-442 4759,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_338ba20@0" Pin0InfoVect0LinkObjId="g_338ba20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-442 4759,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34a75f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-414 4260,-425 4306,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4587@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-414 4260,-425 4306,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a7850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-358 4378,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-358 4378,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a7ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-413 4378,-425 4333,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-413 4378,-425 4333,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34a7d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4072,-837 4072,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4590@1" ObjectIDZND0="4588@x" ObjectIDZND1="4592@x" Pin0InfoVect0LinkObjId="SW-28373_0" Pin0InfoVect1LinkObjId="SW-28377_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28375_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4072,-837 4072,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a7f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-638 4849,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-638 4849,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a81d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-358 4427,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-358 4427,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a8430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-415 4427,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-415 4427,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a8690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-461 4427,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-461 4427,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a1f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-236 3528,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_312c8c0@0" ObjectIDZND0="20064@0" Pin0InfoVect0LinkObjId="SW-95793_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_312c8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-236 3528,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a21b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-172 3528,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20063@0" ObjectIDZND0="g_312c8c0@1" Pin0InfoVect0LinkObjId="g_312c8c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95792_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-172 3528,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a2410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3626,-339 3626,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20065@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_32a3bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3626,-339 3626,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a2670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3626,-240 3626,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_348bb80@0" ObjectIDZND0="20067@0" Pin0InfoVect0LinkObjId="SW-95808_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_348bb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3626,-240 3626,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a28d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3626,-176 3626,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20066@0" ObjectIDZND0="g_348bb80@1" Pin0InfoVect0LinkObjId="g_348bb80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3626,-176 3626,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a2b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3626,-303 3626,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20065@1" ObjectIDZND0="20067@1" Pin0InfoVect0LinkObjId="SW-95808_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3626,-303 3626,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a2d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-357 3528,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4550@0" ObjectIDZND0="20062@0" Pin0InfoVect0LinkObjId="SW-95791_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a2410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-357 3528,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a2ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-299 3528,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20062@1" ObjectIDZND0="20064@1" Pin0InfoVect0LinkObjId="SW-95793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95791_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-299 3528,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a3250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-140 3887,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4613@1" ObjectIDZND0="g_308dc20@0" Pin0InfoVect0LinkObjId="g_308dc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28455_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-140 3887,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a34b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-291 3824,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4612@1" ObjectIDZND0="4608@1" Pin0InfoVect0LinkObjId="SW-28448_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28452_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-291 3824,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a3710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-249 3824,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4611@0" ObjectIDZND0="4612@0" Pin0InfoVect0LinkObjId="SW-28452_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-249 3824,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a3970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-213 3824,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4611@1" ObjectIDZND0="g_37c91d0@1" Pin0InfoVect0LinkObjId="g_37c91d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-213 3824,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a3bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-341 3824,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4608@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_32a2410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-341 3824,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a3e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-357 3963,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4550@0" ObjectIDZND0="4586@0" Pin0InfoVect0LinkObjId="SW-28360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a2410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-357 3963,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a4090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-305 3963,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4586@1" ObjectIDZND0="g_3090d70@0" Pin0InfoVect0LinkObjId="g_3090d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-305 3963,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a42f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-258 3963,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3090d70@1" ObjectIDZND0="g_360dc80@1" Pin0InfoVect0LinkObjId="g_360dc80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3090d70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-258 3963,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a4550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-187 3963,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_360dc80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_360dc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-187 3963,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a47b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-357 4051,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4550@0" ObjectIDZND0="4585@0" Pin0InfoVect0LinkObjId="SW-28358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a2410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-357 4051,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a4a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-302 4051,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4585@1" ObjectIDZND0="g_3610370@0" Pin0InfoVect0LinkObjId="g_3610370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28358_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-302 4051,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a4c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-337 4141,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4602@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_32a2410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-337 4141,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a4ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-301 4141,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4602@1" ObjectIDZND0="4604@1" Pin0InfoVect0LinkObjId="SW-28417_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-301 4141,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a5130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-304 4240,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4605@1" ObjectIDZND0="4607@1" Pin0InfoVect0LinkObjId="SW-28431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-304 4240,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a5390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-340 4240,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4605@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_32a2410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28429_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-340 4240,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32a55f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-661 4428,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="g_303a750@0" ObjectIDND1="0@x" ObjectIDND2="g_2fb9000@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_303a750_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2fb9000_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-661 4428,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32a5850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-645 4361,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_303a750@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2fb9000@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2fb9000_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_303a750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-645 4361,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32a5ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-661 4338,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_303a750@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_2fb9000@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2fb9000_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_303a750_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-661 4338,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32a5d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-638 4590,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4483,-638 4590,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a5f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-592 3975,-592 3975,-532 4646,-532 4646,-957 4665,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4617@0" ObjectIDZND0="4571@0" Pin0InfoVect0LinkObjId="SW-28242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f26f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-592 3975,-592 3975,-532 4646,-532 4646,-957 4665,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a61d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4701,-957 4718,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4571@1" ObjectIDZND0="4569@1" Pin0InfoVect0LinkObjId="SW-28240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4701,-957 4718,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a6430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-957 4762,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4569@0" ObjectIDZND0="4570@0" Pin0InfoVect0LinkObjId="SW-28241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-957 4762,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a6690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4798,-957 4848,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4570@1" ObjectIDZND0="4549@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4798,-957 4848,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a68f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-790 4870,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4584@0" Pin0InfoVect0LinkObjId="SW-28336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a6690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-790 4870,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a6b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4906,-790 4927,-790 4927,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4584@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4906,-790 4927,-790 4927,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32a6db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-726 4871,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-726 4871,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32a7010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-726 4927,-726 4927,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-726 4927,-726 4927,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32a7270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-1028 3743,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1a4eea0@0" ObjectIDZND0="4578@x" ObjectIDZND1="4576@x" Pin0InfoVect0LinkObjId="SW-28321_0" Pin0InfoVect1LinkObjId="SW-28319_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a4eea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-1028 3743,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32a74d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-928 3743,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDND1="4577@x" ObjectIDZND0="4576@1" Pin0InfoVect0LinkObjId="SW-28319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-28320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-928 3743,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32028e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-982 3743,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4576@0" ObjectIDZND0="g_1a4eea0@0" ObjectIDZND1="4578@x" Pin0InfoVect0LinkObjId="g_1a4eea0_0" Pin0InfoVect1LinkObjId="SW-28321_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28319_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-982 3743,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3202b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-1005 4271,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4581@x" ObjectIDND1="4579@x" ObjectIDZND0="g_31d61a0@0" Pin0InfoVect0LinkObjId="g_31d61a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28325_0" Pin1InfoVect1LinkObjId="SW-28323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-1005 4271,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3202da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-928 4271,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4548@0" ObjectIDND1="4580@x" ObjectIDZND0="4579@1" Pin0InfoVect0LinkObjId="SW-28323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2eef4c0_0" Pin1InfoVect1LinkObjId="SW-28324_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-928 4271,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3203000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-986 4271,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4579@0" ObjectIDZND0="g_31d61a0@0" ObjectIDZND1="4581@x" Pin0InfoVect0LinkObjId="g_31d61a0_0" Pin0InfoVect1LinkObjId="SW-28325_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-986 4271,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3203260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-759 3790,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4565@x" ObjectIDND1="4568@x" ObjectIDZND0="4563@0" Pin0InfoVect0LinkObjId="SW-28230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28232_0" Pin1InfoVect1LinkObjId="SW-28235_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-759 3790,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32034c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-759 3790,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4563@x" ObjectIDND1="4568@x" ObjectIDZND0="4565@0" Pin0InfoVect0LinkObjId="SW-28232_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28230_0" Pin1InfoVect1LinkObjId="SW-28235_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-759 3790,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3203720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-675 3845,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2eb3e90@0" ObjectIDZND0="4567@1" Pin0InfoVect0LinkObjId="SW-28234_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eb3e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-675 3845,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3203980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-675 3790,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="4567@0" ObjectIDZND0="4565@x" ObjectIDZND1="4617@x" Pin0InfoVect0LinkObjId="SW-28232_0" Pin0InfoVect1LinkObjId="g_35f26f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-675 3790,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3203be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-701 3790,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="4565@1" ObjectIDZND0="4567@x" ObjectIDZND1="4617@x" Pin0InfoVect0LinkObjId="SW-28234_0" Pin0InfoVect1LinkObjId="g_35f26f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-701 3790,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3203e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-675 3790,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="4565@x" ObjectIDND1="4567@x" ObjectIDZND0="4617@1" Pin0InfoVect0LinkObjId="g_35f26f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28232_0" Pin1InfoVect1LinkObjId="SW-28234_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-675 3790,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32040a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-149 3824,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_37c91d0@0" ObjectIDZND0="4613@x" ObjectIDZND1="4609@x" Pin0InfoVect0LinkObjId="SW-28455_0" Pin0InfoVect1LinkObjId="SW-28449_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c91d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-149 3824,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3204300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-140 3837,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_37c91d0@0" ObjectIDND1="4609@x" ObjectIDZND0="4613@0" Pin0InfoVect0LinkObjId="SW-28455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37c91d0_0" Pin1InfoVect1LinkObjId="SW-28449_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-140 3837,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3204560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-129 3824,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="4609@0" ObjectIDZND0="4613@x" ObjectIDZND1="g_37c91d0@0" Pin0InfoVect0LinkObjId="SW-28455_0" Pin0InfoVect1LinkObjId="g_37c91d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28449_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-129 3824,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32047c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-810 5038,-812 5038,-835 5053,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="14291@x" ObjectIDND1="g_3645850@0" ObjectIDND2="4600@x" ObjectIDZND0="g_36468e0@0" Pin0InfoVect0LinkObjId="g_36468e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_WM.LD375_0" Pin1InfoVect1LinkObjId="g_3645850_0" Pin1InfoVect2LinkObjId="SW-28404_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-810 5038,-812 5038,-835 5053,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3204a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-780 5038,-780 5038,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3645850@0" ObjectIDZND0="14291@x" ObjectIDZND1="g_36468e0@0" ObjectIDZND2="4600@x" Pin0InfoVect0LinkObjId="EC-CX_WM.LD375_0" Pin0InfoVect1LinkObjId="g_36468e0_0" Pin0InfoVect2LinkObjId="SW-28404_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3645850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-780 5038,-780 5038,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3204c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-810 5019,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="14291@x" ObjectIDND1="g_3645850@0" ObjectIDND2="g_36468e0@0" ObjectIDZND0="4600@1" Pin0InfoVect0LinkObjId="SW-28404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_WM.LD375_0" Pin1InfoVect1LinkObjId="g_3645850_0" Pin1InfoVect2LinkObjId="g_36468e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-810 5019,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3204ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-987 5033,-987 5033,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_28554a0@0" ObjectIDZND0="4594@x" ObjectIDZND1="g_2856530@0" ObjectIDZND2="14290@x" Pin0InfoVect0LinkObjId="SW-28380_0" Pin0InfoVect1LinkObjId="g_2856530_0" Pin0InfoVect2LinkObjId="EC-CX_WM.LD373_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28554a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-987 5033,-987 5033,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3205130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-1017 5014,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_28554a0@0" ObjectIDND1="g_2856530@0" ObjectIDND2="14290@x" ObjectIDZND0="4594@1" Pin0InfoVect0LinkObjId="SW-28380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28554a0_0" Pin1InfoVect1LinkObjId="g_2856530_0" Pin1InfoVect2LinkObjId="EC-CX_WM.LD373_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-1017 5014,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3205390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-1131 5038,-1131 5038,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2870210@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2864ba0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2864ba0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2870210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-1131 5038,-1131 5038,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32055f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-1161 5019,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2870210@0" ObjectIDND1="g_2864ba0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2870210_0" Pin1InfoVect1LinkObjId="g_2864ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-1161 5019,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3205850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-1017 5033,-1019 5033,-1042 5048,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="g_28554a0@0" ObjectIDND1="4594@x" ObjectIDND2="14290@x" ObjectIDZND0="g_2856530@0" Pin0InfoVect0LinkObjId="g_2856530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28554a0_0" Pin1InfoVect1LinkObjId="SW-28380_0" Pin1InfoVect2LinkObjId="EC-CX_WM.LD373_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-1017 5033,-1019 5033,-1042 5048,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3205aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-1161 5038,-1163 5038,-1186 5053,-1186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2870210@0" ObjectIDND1="0@x" ObjectIDZND0="g_2864ba0@0" Pin0InfoVect0LinkObjId="g_2864ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2870210_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-1161 5038,-1163 5038,-1186 5053,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3205cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-835 3790,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4566@0" ObjectIDZND0="4563@x" ObjectIDZND1="4564@x" Pin0InfoVect0LinkObjId="SW-28230_0" Pin0InfoVect1LinkObjId="SW-28231_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28233_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-835 3790,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3205f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-835 3790,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4566@x" ObjectIDND1="4564@x" ObjectIDZND0="4563@1" Pin0InfoVect0LinkObjId="SW-28230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28233_0" Pin1InfoVect1LinkObjId="SW-28231_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-835 3790,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32061b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-895 3790,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4547@0" ObjectIDZND0="4564@0" Pin0InfoVect0LinkObjId="SW-28231_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-895 3790,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3206410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-848 3790,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4564@1" ObjectIDZND0="4566@x" ObjectIDZND1="4563@x" Pin0InfoVect0LinkObjId="SW-28233_0" Pin0InfoVect1LinkObjId="SW-28230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28231_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-848 3790,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3206670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5037,-703 5118,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_322ca10@0" ObjectIDND1="0@x" ObjectIDND2="g_280ca30@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_322ca10_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_280ca30_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5037,-703 5118,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32068d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5062,-673 5037,-673 5037,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_322ca10@0" ObjectIDZND0="0@x" ObjectIDZND1="g_280ca30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_280ca30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_322ca10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5062,-673 5037,-673 5037,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3206b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5037,-703 5018,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_322ca10@0" ObjectIDND1="g_280ca30@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_322ca10_0" Pin1InfoVect1LinkObjId="g_280ca30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5037,-703 5018,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3206d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-625 5118,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_2ed91d0@0" ObjectIDND1="0@x" ObjectIDND2="g_2eda260@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ed91d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2eda260_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-625 5118,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3206ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-595 5038,-595 5038,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2ed91d0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2eda260@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2eda260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ed91d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-595 5038,-595 5038,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3207250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-625 5019,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ed91d0@0" ObjectIDND1="g_2eda260@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ed91d0_0" Pin1InfoVect1LinkObjId="g_2eda260_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-625 5019,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32074b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-551 5119,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_28188c0@0" ObjectIDND1="0@x" ObjectIDND2="g_2819950@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28188c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2819950_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-551 5119,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3207710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5063,-521 5038,-521 5038,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_28188c0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2819950@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2819950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28188c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5063,-521 5038,-521 5038,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3207970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-551 5019,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_28188c0@0" ObjectIDND1="g_2819950@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28188c0_0" Pin1InfoVect1LinkObjId="g_2819950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-551 5019,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3207bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-479 5119,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_3240740@0" ObjectIDND1="0@x" ObjectIDND2="g_32417d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3240740_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_32417d0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-479 5119,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3207e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5066,-449 5041,-449 5041,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3240740@0" ObjectIDZND0="0@x" ObjectIDZND1="g_32417d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_32417d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3240740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5066,-449 5041,-449 5041,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e986d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-479 5022,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3240740@0" ObjectIDND1="g_32417d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3240740_0" Pin1InfoVect1LinkObjId="g_32417d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-479 5022,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e98930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-404 5119,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_2fc8b00@0" ObjectIDND1="0@x" ObjectIDND2="g_2fc9b90@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2fc8b00_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2fc9b90_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-404 5119,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e98b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5071,-374 5046,-374 5046,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2fc8b00@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2fc9b90@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2fc9b90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fc8b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5071,-374 5046,-374 5046,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e98df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-404 5027,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2fc8b00@0" ObjectIDND1="g_2fc9b90@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fc8b00_0" Pin1InfoVect1LinkObjId="g_2fc9b90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-404 5027,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e99050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-810 5085,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_3645850@0" ObjectIDND1="g_36468e0@0" ObjectIDND2="4600@x" ObjectIDZND0="14291@0" Pin0InfoVect0LinkObjId="EC-CX_WM.LD375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3645850_0" Pin1InfoVect1LinkObjId="g_36468e0_0" Pin1InfoVect2LinkObjId="SW-28404_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-810 5085,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e992b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-1017 5076,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_28554a0@0" ObjectIDND1="4594@x" ObjectIDND2="g_2856530@0" ObjectIDZND0="14290@0" Pin0InfoVect0LinkObjId="EC-CX_WM.LD373_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28554a0_0" Pin1InfoVect1LinkObjId="SW-28380_0" Pin1InfoVect2LinkObjId="g_2856530_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-1017 5076,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e99510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-1161 5116,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_2870210@0" ObjectIDND1="0@x" ObjectIDND2="g_2864ba0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2870210_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2864ba0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-1161 5116,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e99770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3587,-1102 3610,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ed43f0@0" ObjectIDZND0="g_2087790@0" ObjectIDZND1="9694@x" ObjectIDZND2="9696@x" Pin0InfoVect0LinkObjId="g_2087790_0" Pin0InfoVect1LinkObjId="SW-54985_0" Pin0InfoVect2LinkObjId="SW-54987_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ed43f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3587,-1102 3610,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e999d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-1102 3610,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2ed43f0@0" ObjectIDND1="g_2087790@0" ObjectIDND2="9694@x" ObjectIDZND0="11713@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ed43f0_0" Pin1InfoVect1LinkObjId="g_2087790_0" Pin1InfoVect2LinkObjId="SW-54985_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-1102 3610,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e99c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3634,-1081 3610,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_2087790@0" ObjectIDZND0="g_2ed43f0@0" ObjectIDZND1="11713@1" ObjectIDZND2="9694@x" Pin0InfoVect0LinkObjId="g_2ed43f0_0" Pin0InfoVect1LinkObjId="g_2e999d0_1" Pin0InfoVect2LinkObjId="SW-54985_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2087790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3634,-1081 3610,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e99e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-1081 3610,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_2087790@0" ObjectIDND1="9694@x" ObjectIDND2="9696@x" ObjectIDZND0="g_2ed43f0@0" ObjectIDZND1="11713@1" Pin0InfoVect0LinkObjId="g_2ed43f0_0" Pin0InfoVect1LinkObjId="g_2e999d0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2087790_0" Pin1InfoVect1LinkObjId="SW-54985_0" Pin1InfoVect2LinkObjId="SW-54987_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-1081 3610,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9a0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-1059 3610,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="9696@0" ObjectIDZND0="9694@x" ObjectIDZND1="g_2ed43f0@0" ObjectIDZND2="11713@1" Pin0InfoVect0LinkObjId="SW-54985_0" Pin0InfoVect1LinkObjId="g_2ed43f0_0" Pin0InfoVect2LinkObjId="g_2e999d0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54987_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-1059 3610,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9a350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-1050 3610,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="9694@0" ObjectIDZND0="9696@x" ObjectIDZND1="g_2ed43f0@0" ObjectIDZND2="11713@1" Pin0InfoVect0LinkObjId="SW-54987_0" Pin0InfoVect1LinkObjId="g_2ed43f0_0" Pin0InfoVect2LinkObjId="g_2e999d0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54985_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-1050 3610,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-1059 3610,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="9694@x" ObjectIDND1="9696@x" ObjectIDZND0="g_2ed43f0@0" ObjectIDZND1="11713@1" ObjectIDZND2="g_2087790@0" Pin0InfoVect0LinkObjId="g_2ed43f0_0" Pin0InfoVect1LinkObjId="g_2e999d0_1" Pin0InfoVect2LinkObjId="g_2087790_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54985_0" Pin1InfoVect1LinkObjId="SW-54987_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-1059 3610,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9a810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3865,-1103 3888,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2e94f30@0" ObjectIDZND0="g_2088220@0" ObjectIDZND1="4559@x" ObjectIDZND2="4561@x" Pin0InfoVect0LinkObjId="g_2088220_0" Pin0InfoVect1LinkObjId="SW-28193_0" Pin0InfoVect2LinkObjId="SW-28195_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e94f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3865,-1103 3888,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9aa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-1103 3888,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2e94f30@0" ObjectIDND1="g_2088220@0" ObjectIDND2="4559@x" ObjectIDZND0="18018@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e94f30_0" Pin1InfoVect1LinkObjId="g_2088220_0" Pin1InfoVect2LinkObjId="SW-28193_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-1103 3888,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9acd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-1082 3888,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_2088220@0" ObjectIDZND0="g_2e94f30@0" ObjectIDZND1="18018@1" ObjectIDZND2="4559@x" Pin0InfoVect0LinkObjId="g_2e94f30_0" Pin0InfoVect1LinkObjId="g_2e9aa70_1" Pin0InfoVect2LinkObjId="SW-28193_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2088220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-1082 3888,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9af30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-1082 3888,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_2088220@0" ObjectIDND1="4559@x" ObjectIDND2="4561@x" ObjectIDZND0="g_2e94f30@0" ObjectIDZND1="18018@1" Pin0InfoVect0LinkObjId="g_2e94f30_0" Pin0InfoVect1LinkObjId="g_2e9aa70_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2088220_0" Pin1InfoVect1LinkObjId="SW-28193_0" Pin1InfoVect2LinkObjId="SW-28195_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-1082 3888,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9b190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3905,-1059 3888,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4561@0" ObjectIDZND0="4559@x" ObjectIDZND1="g_2e94f30@0" ObjectIDZND2="18018@1" Pin0InfoVect0LinkObjId="SW-28193_0" Pin0InfoVect1LinkObjId="g_2e94f30_0" Pin0InfoVect2LinkObjId="g_2e9aa70_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28195_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3905,-1059 3888,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9b3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-1051 3888,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4559@0" ObjectIDZND0="4561@x" ObjectIDZND1="g_2e94f30@0" ObjectIDZND2="18018@1" Pin0InfoVect0LinkObjId="SW-28195_0" Pin0InfoVect1LinkObjId="g_2e94f30_0" Pin0InfoVect2LinkObjId="g_2e9aa70_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-1051 3888,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9b650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-1060 3888,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="4559@x" ObjectIDND1="4561@x" ObjectIDZND0="g_2e94f30@0" ObjectIDZND1="18018@1" ObjectIDZND2="g_2088220@0" Pin0InfoVect0LinkObjId="g_2e94f30_0" Pin0InfoVect1LinkObjId="g_2e9aa70_1" Pin0InfoVect2LinkObjId="g_2088220_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28193_0" Pin1InfoVect1LinkObjId="SW-28195_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-1060 3888,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e9b8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-941 5033,-871 5063,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="4597@x" ObjectIDND1="14292@x" ObjectIDND2="g_2833ee0@0" ObjectIDZND0="g_37661d0@0" Pin0InfoVect0LinkObjId="g_37661d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-28392_0" Pin1InfoVect1LinkObjId="EC-CX_WM.LD374_0" Pin1InfoVect2LinkObjId="g_2833ee0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-941 5033,-871 5063,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9bb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4156,-1082 4132,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2088f60@0" ObjectIDZND0="4553@x" ObjectIDZND1="4555@x" ObjectIDZND2="g_3250e00@0" Pin0InfoVect0LinkObjId="SW-28147_0" Pin0InfoVect1LinkObjId="SW-28149_0" Pin0InfoVect2LinkObjId="g_3250e00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2088f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4156,-1082 4132,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9bd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4149,-1060 4132,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4555@0" ObjectIDZND0="4553@x" ObjectIDZND1="g_2088f60@0" ObjectIDZND2="g_3250e00@0" Pin0InfoVect0LinkObjId="SW-28147_0" Pin0InfoVect1LinkObjId="g_2088f60_0" Pin0InfoVect2LinkObjId="g_3250e00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28149_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4149,-1060 4132,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9bfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1051 4132,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4553@0" ObjectIDZND0="4555@x" ObjectIDZND1="g_2088f60@0" ObjectIDZND2="g_3250e00@0" Pin0InfoVect0LinkObjId="SW-28149_0" Pin0InfoVect1LinkObjId="g_2088f60_0" Pin0InfoVect2LinkObjId="g_3250e00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1051 4132,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9c230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1060 4132,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4553@x" ObjectIDND1="4555@x" ObjectIDZND0="g_2088f60@0" ObjectIDZND1="g_3250e00@0" ObjectIDZND2="14288@1" Pin0InfoVect0LinkObjId="g_2088f60_0" Pin0InfoVect1LinkObjId="g_3250e00_0" Pin0InfoVect2LinkObjId="g_2e9c950_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28147_0" Pin1InfoVect1LinkObjId="SW-28149_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1060 4132,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9c490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-1103 4132,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3250e00@0" ObjectIDZND0="4553@x" ObjectIDZND1="4555@x" ObjectIDZND2="g_2088f60@0" Pin0InfoVect0LinkObjId="SW-28147_0" Pin0InfoVect1LinkObjId="SW-28149_0" Pin0InfoVect2LinkObjId="g_2088f60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3250e00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-1103 4132,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9c6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1082 4132,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="4553@x" ObjectIDND1="4555@x" ObjectIDND2="g_2088f60@0" ObjectIDZND0="g_3250e00@0" ObjectIDZND1="14288@1" Pin0InfoVect0LinkObjId="g_3250e00_0" Pin0InfoVect1LinkObjId="g_2e9c950_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-28147_0" Pin1InfoVect1LinkObjId="SW-28149_0" Pin1InfoVect2LinkObjId="g_2088f60_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1082 4132,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9c950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1103 4132,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4553@x" ObjectIDND1="4555@x" ObjectIDND2="g_2088f60@0" ObjectIDZND0="14288@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-28147_0" Pin1InfoVect1LinkObjId="SW-28149_0" Pin1InfoVect2LinkObjId="g_2088f60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1103 4132,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9cbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-1104 4442,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2fcd7e0@0" ObjectIDZND0="g_2089cd0@0" ObjectIDZND1="9698@x" ObjectIDZND2="9700@x" Pin0InfoVect0LinkObjId="g_2089cd0_0" Pin0InfoVect1LinkObjId="SW-55061_0" Pin0InfoVect2LinkObjId="SW-55063_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fcd7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-1104 4442,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9ce10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-1104 4442,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2fcd7e0@0" ObjectIDND1="g_2089cd0@0" ObjectIDND2="9698@x" ObjectIDZND0="11712@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2fcd7e0_0" Pin1InfoVect1LinkObjId="g_2089cd0_0" Pin1InfoVect2LinkObjId="SW-55061_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-1104 4442,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9d070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4466,-1083 4442,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_2089cd0@0" ObjectIDZND0="g_2fcd7e0@0" ObjectIDZND1="11712@1" ObjectIDZND2="9698@x" Pin0InfoVect0LinkObjId="g_2fcd7e0_0" Pin0InfoVect1LinkObjId="g_2e9ce10_1" Pin0InfoVect2LinkObjId="SW-55061_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2089cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4466,-1083 4442,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9d2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-1083 4442,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_2089cd0@0" ObjectIDND1="9698@x" ObjectIDND2="9700@x" ObjectIDZND0="g_2fcd7e0@0" ObjectIDZND1="11712@1" Pin0InfoVect0LinkObjId="g_2fcd7e0_0" Pin0InfoVect1LinkObjId="g_2e9ce10_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2089cd0_0" Pin1InfoVect1LinkObjId="SW-55061_0" Pin1InfoVect2LinkObjId="SW-55063_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-1083 4442,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9d530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4459,-1061 4442,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="9700@0" ObjectIDZND0="9698@x" ObjectIDZND1="g_2fcd7e0@0" ObjectIDZND2="11712@1" Pin0InfoVect0LinkObjId="SW-55061_0" Pin0InfoVect1LinkObjId="g_2fcd7e0_0" Pin0InfoVect2LinkObjId="g_2e9ce10_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55063_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4459,-1061 4442,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9d790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-1052 4442,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="9698@0" ObjectIDZND0="9700@x" ObjectIDZND1="g_2fcd7e0@0" ObjectIDZND2="11712@1" Pin0InfoVect0LinkObjId="SW-55063_0" Pin0InfoVect1LinkObjId="g_2fcd7e0_0" Pin0InfoVect2LinkObjId="g_2e9ce10_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-1052 4442,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e9d9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-1061 4442,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="9698@x" ObjectIDND1="9700@x" ObjectIDZND0="g_2fcd7e0@0" ObjectIDZND1="11712@1" ObjectIDZND2="g_2089cd0@0" Pin0InfoVect0LinkObjId="g_2fcd7e0_0" Pin0InfoVect1LinkObjId="g_2e9ce10_1" Pin0InfoVect2LinkObjId="g_2089cd0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55061_0" Pin1InfoVect1LinkObjId="SW-55063_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-1061 4442,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e9dc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4167,-120 4167,-131 4141,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_34b6bd0@0" ObjectIDZND0="14293@x" ObjectIDZND1="4603@x" Pin0InfoVect0LinkObjId="EC-CX_WM.LD076_0" Pin0InfoVect1LinkObjId="SW-28416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b6bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4167,-120 4167,-131 4141,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ee390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-88 4141,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14293@0" ObjectIDZND0="g_34b6bd0@0" ObjectIDZND1="4603@x" Pin0InfoVect0LinkObjId="g_34b6bd0_0" Pin0InfoVect1LinkObjId="SW-28416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_WM.LD076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-88 4141,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ee5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-131 4141,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14293@x" ObjectIDND1="g_34b6bd0@0" ObjectIDZND0="4603@1" Pin0InfoVect0LinkObjId="SW-28416_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_WM.LD076_0" Pin1InfoVect1LinkObjId="g_34b6bd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-131 4141,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ee850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-122 4265,-133 4240,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_34b7940@0" ObjectIDZND0="14294@x" ObjectIDZND1="4606@x" Pin0InfoVect0LinkObjId="EC-CX_WM.LD077_0" Pin0InfoVect1LinkObjId="SW-28430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b7940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-122 4265,-133 4240,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33eeab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-90 4240,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14294@0" ObjectIDZND0="g_34b7940@0" ObjectIDZND1="4606@x" Pin0InfoVect0LinkObjId="g_34b7940_0" Pin0InfoVect1LinkObjId="SW-28430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_WM.LD077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-90 4240,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33eed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-133 4240,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14294@x" ObjectIDND1="g_34b7940@0" ObjectIDZND0="4606@1" Pin0InfoVect0LinkObjId="SW-28430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_WM.LD077_0" Pin1InfoVect1LinkObjId="g_34b7940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-133 4240,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33eef70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-250 4141,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="4604@0" ObjectIDZND0="g_3547110@1" Pin0InfoVect0LinkObjId="g_3547110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-250 4141,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ef1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-194 4141,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3547110@0" ObjectIDZND0="4603@0" Pin0InfoVect0LinkObjId="SW-28416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3547110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-194 4141,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ef430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-177 4240,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4606@0" ObjectIDZND0="g_3547980@0" Pin0InfoVect0LinkObjId="g_3547980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-177 4240,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ef690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-233 4240,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3547980@1" ObjectIDZND0="4607@0" Pin0InfoVect0LinkObjId="SW-28431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3547980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-233 4240,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3820ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5037,-703 5037,-728 5052,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_322ca10@0" ObjectIDND1="0@x" ObjectIDZND0="g_280ca30@0" Pin0InfoVect0LinkObjId="g_280ca30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_322ca10_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5037,-703 5037,-728 5052,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3821130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-625 5038,-649 5053,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2ed91d0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2eda260@0" Pin0InfoVect0LinkObjId="g_2eda260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ed91d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-625 5038,-649 5053,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3821390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-551 5038,-575 5054,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_28188c0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2819950@0" Pin0InfoVect0LinkObjId="g_2819950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28188c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-551 5038,-575 5054,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38215f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-479 5041,-503 5057,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3240740@0" ObjectIDND1="0@x" ObjectIDZND0="g_32417d0@0" Pin0InfoVect0LinkObjId="g_32417d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3240740_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-479 5041,-503 5057,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3821850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-404 5046,-428 5062,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2fc8b00@0" ObjectIDND1="0@x" ObjectIDZND0="g_2fc9b90@0" Pin0InfoVect0LinkObjId="g_2fc9b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fc8b00_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-404 5046,-428 5062,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac90f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-84 3911,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4610@1" ObjectIDZND0="g_1ac8660@0" Pin0InfoVect0LinkObjId="g_1ac8660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-84 3911,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac9350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-84 3861,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="4609@x" ObjectIDND1="12211@x" ObjectIDZND0="4610@0" Pin0InfoVect0LinkObjId="SW-28450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28449_0" Pin1InfoVect1LinkObjId="CB-CX_WM.CX_WM_1C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-84 3861,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac9e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-93 3824,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="4609@1" ObjectIDZND0="4610@x" ObjectIDZND1="12211@x" Pin0InfoVect0LinkObjId="SW-28450_0" Pin0InfoVect1LinkObjId="CB-CX_WM.CX_WM_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28449_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-93 3824,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aca0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-84 3824,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="4610@x" ObjectIDND1="4609@x" ObjectIDZND0="12211@0" Pin0InfoVect0LinkObjId="CB-CX_WM.CX_WM_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-28450_0" Pin1InfoVect1LinkObjId="SW-28449_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-84 3824,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ec9b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3898,-57 3911,-57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2ec90e0@0" Pin0InfoVect0LinkObjId="g_2ec90e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3898,-57 3911,-57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec9dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3862,-57 3856,-57 3856,43 3825,43 3825,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="12211@1" Pin0InfoVect0LinkObjId="CB-CX_WM.CX_WM_1C_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3862,-57 3856,-57 3856,43 3825,43 3825,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e8ff70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-82 4790,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2e8f4e0@0" Pin0InfoVect0LinkObjId="g_2e8f4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-82 4790,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e901d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-82 4740,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-82 4740,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e90430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-91 4703,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-91 4703,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e90690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-82 4703,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-82 4703,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20bf5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4777,-55 4790,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2e92dc0@0" Pin0InfoVect0LinkObjId="g_2e92dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4777,-55 4790,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20bf830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-55 4735,-55 4735,45 4704,45 4704,39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-55 4735,-55 4735,45 4704,45 4704,39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2881b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-85 4958,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_28810f0@0" Pin0InfoVect0LinkObjId="g_28810f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-85 4958,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2881d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-85 4908,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-85 4908,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2881fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-94 4871,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-94 4871,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2882240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-85 4871,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-85 4871,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2885360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4945,-58 4958,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_28848d0@0" Pin0InfoVect0LinkObjId="g_28848d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4945,-58 4958,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28855c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-58 4903,-58 4903,42 4872,42 4872,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-58 4903,-58 4903,42 4872,42 4872,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28480d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3725,-334 3725,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20068@0" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_32a2410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3725,-334 3725,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28482c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3725,-235 3725,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2846ba0@0" ObjectIDZND0="20070@0" Pin0InfoVect0LinkObjId="SW-95823_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2846ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3725,-235 3725,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28484b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3725,-171 3725,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20069@0" ObjectIDZND0="g_2846ba0@1" Pin0InfoVect0LinkObjId="g_2846ba0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95822_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3725,-171 3725,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28486a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3725,-298 3725,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20068@1" ObjectIDZND0="20070@1" Pin0InfoVect0LinkObjId="SW-95823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95821_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3725,-298 3725,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_284b600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-115 3751,-126 3725,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2848e10@0" ObjectIDZND0="20069@x" ObjectIDZND1="20080@x" Pin0InfoVect0LinkObjId="SW-95822_0" Pin0InfoVect1LinkObjId="EC-CX_WM.LD073_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2848e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-115 3751,-126 3725,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fae000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-119 3652,-133 3626,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2849b20@0" ObjectIDZND0="20066@x" Pin0InfoVect0LinkObjId="SW-95807_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2849b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-119 3652,-133 3626,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fae260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3554,-115 3554,-127 3528,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_284a890@0" ObjectIDZND0="20063@x" ObjectIDZND1="20078@x" Pin0InfoVect0LinkObjId="SW-95792_0" Pin0InfoVect1LinkObjId="EC-CX_WM.LD071_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_284a890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3554,-115 3554,-127 3528,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb0210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-136 3528,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="20063@1" ObjectIDZND0="g_284a890@0" ObjectIDZND1="20078@x" Pin0InfoVect0LinkObjId="g_284a890_0" Pin0InfoVect1LinkObjId="EC-CX_WM.LD071_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-136 3528,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb0470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-127 3528,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_284a890@0" ObjectIDND1="20063@x" ObjectIDZND0="20078@0" Pin0InfoVect0LinkObjId="EC-CX_WM.LD071_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_284a890_0" Pin1InfoVect1LinkObjId="SW-95792_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-127 3528,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb0f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3626,-140 3626,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20066@1" ObjectIDZND0="g_2849b20@0" Pin0InfoVect0LinkObjId="g_2849b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95807_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3626,-140 3626,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb11c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3626,-133 3626,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_2849b20@0" ObjectIDND1="20066@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2849b20_0" Pin1InfoVect1LinkObjId="SW-95807_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3626,-133 3626,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb1cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3725,-135 3725,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="20069@1" ObjectIDZND0="g_2848e10@0" ObjectIDZND1="20080@x" Pin0InfoVect0LinkObjId="g_2848e10_0" Pin0InfoVect1LinkObjId="EC-CX_WM.LD073_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-95822_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3725,-135 3725,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb1f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3725,-126 3725,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2848e10@0" ObjectIDND1="20069@x" ObjectIDZND0="20080@0" Pin0InfoVect0LinkObjId="EC-CX_WM.LD073_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2848e10_0" Pin1InfoVect1LinkObjId="SW-95822_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3725,-126 3725,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebd090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-378 4260,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4587@1" ObjectIDZND0="4550@0" Pin0InfoVect0LinkObjId="g_32a2410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28361_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-378 4260,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2834f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-905 5040,-905 5040,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2833ee0@0" ObjectIDZND0="14292@x" ObjectIDZND1="g_1f83f20@0" ObjectIDZND2="g_37661d0@0" Pin0InfoVect0LinkObjId="EC-CX_WM.LD374_0" Pin0InfoVect1LinkObjId="g_1f83f20_0" Pin0InfoVect2LinkObjId="g_37661d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2833ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-905 5040,-905 5040,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2835c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-940 5081,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_2833ee0@0" ObjectIDND1="g_1f83f20@0" ObjectIDND2="g_37661d0@0" ObjectIDZND0="14292@0" Pin0InfoVect0LinkObjId="EC-CX_WM.LD374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2833ee0_0" Pin1InfoVect1LinkObjId="g_1f83f20_0" Pin1InfoVect2LinkObjId="g_37661d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-940 5081,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2835ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-940 5040,-963 5051,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="14292@x" ObjectIDND1="g_2833ee0@0" ObjectIDND2="g_37661d0@0" ObjectIDZND0="g_1f83f20@0" Pin0InfoVect0LinkObjId="g_1f83f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_WM.LD374_0" Pin1InfoVect1LinkObjId="g_2833ee0_0" Pin1InfoVect2LinkObjId="g_37661d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-940 5040,-963 5051,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2837dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-940 5033,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="4597@1" ObjectIDZND0="g_37661d0@0" ObjectIDZND1="14292@x" ObjectIDZND2="g_2833ee0@0" Pin0InfoVect0LinkObjId="g_37661d0_0" Pin0InfoVect1LinkObjId="EC-CX_WM.LD374_0" Pin0InfoVect2LinkObjId="g_2833ee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-940 5033,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2837fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-940 5040,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_37661d0@0" ObjectIDND1="4597@x" ObjectIDZND0="14292@x" ObjectIDZND1="g_2833ee0@0" ObjectIDZND2="g_1f83f20@0" Pin0InfoVect0LinkObjId="EC-CX_WM.LD374_0" Pin0InfoVect1LinkObjId="g_2833ee0_0" Pin0InfoVect2LinkObjId="g_1f83f20_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37661d0_0" Pin1InfoVect1LinkObjId="SW-28392_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-940 5040,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3068970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-1089 4872,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="29440@0" Pin0InfoVect0LinkObjId="SW-193559_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a6690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-1089 4872,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3068ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-1089 4908,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29442@1" ObjectIDZND0="29440@1" Pin0InfoVect0LinkObjId="SW-193559_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-1089 4908,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3068dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4978,-1089 4957,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29441@0" ObjectIDZND0="29442@0" Pin0InfoVect0LinkObjId="SW-193562_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4978,-1089 4957,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3069000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-1089 5033,-1091 5033,-1114 5048,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="g_2e14700@0" ObjectIDND1="29441@x" ObjectIDND2="0@x" ObjectIDZND0="g_2e15790@0" Pin0InfoVect0LinkObjId="g_2e15790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e14700_0" Pin1InfoVect1LinkObjId="SW-193560_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-1089 5033,-1091 5033,-1114 5048,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3069240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-1059 5033,-1059 5033,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2e14700@0" ObjectIDZND0="g_2e15790@0" ObjectIDZND1="29441@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2e15790_0" Pin0InfoVect1LinkObjId="SW-193560_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e14700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-1059 5033,-1059 5033,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3069470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-1089 5014,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2e15790@0" ObjectIDND1="g_2e14700@0" ObjectIDND2="0@x" ObjectIDZND0="29441@1" Pin0InfoVect0LinkObjId="SW-193560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e15790_0" Pin1InfoVect1LinkObjId="g_2e14700_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-1089 5014,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30696d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-1089 5080,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2e15790@0" ObjectIDND1="g_2e14700@0" ObjectIDND2="29441@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e15790_0" Pin1InfoVect1LinkObjId="g_2e14700_0" Pin1InfoVect2LinkObjId="SW-193560_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-1089 5080,-1089 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="3610" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4428" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="3743" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="3888" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4132" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4271" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4442" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="3944" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4548" cx="4072" cy="-896" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4703" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4871" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4418" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4492" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4568" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4641" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5009" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5128" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4378" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4427" cy="-358" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3626" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3528" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3824" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="4141" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3726" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3963" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="4051" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="4240" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="4260" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4547" cx="3790" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4550" cx="3789" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4848" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4848" cy="-810" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4848" cy="-1161" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4848" cy="-1017" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4848" cy="-1090" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4848" cy="-957" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4848" cy="-790" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4849" cy="-703" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4849" cy="-625" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4849" cy="-551" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4849" cy="-479" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4849" cy="-404" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4849" cy="-442" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4849" cy="-638" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4849" cy="-726" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4549" cx="4848" cy="-1089" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-13" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3420.000000 -1087.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="13" ObjectName="DYN-CX_WM"/>
     <cge:Meas_Ref ObjectId="13"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35a4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35a4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35a4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35a4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35a4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35a4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35a4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cbf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3589.000000 -1184.000000) translate(0,15)">迤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cbf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3589.000000 -1184.000000) translate(0,33)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cbf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3589.000000 -1184.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cc640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -1184.000000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cc640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -1184.000000) translate(0,33)">干</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cc640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -1184.000000) translate(0,51)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cc640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -1184.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cc8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -1184.000000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cc8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -1184.000000) translate(0,33)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cc8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -1184.000000) translate(0,51)">的</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cc8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -1184.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ccb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -1184.000000) translate(0,15)">多</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ccb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -1184.000000) translate(0,33)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ccb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -1184.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cd020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3498.000000 -137.000000) translate(0,15)">立</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cd020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3498.000000 -137.000000) translate(0,33)">溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cd020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3498.000000 -137.000000) translate(0,51)">冬</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cd020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3498.000000 -137.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cdbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3596.000000 -133.000000) translate(0,15)">丙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cdbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3596.000000 -133.000000) translate(0,33)">和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cdbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3596.000000 -133.000000) translate(0,51)">良</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cdbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3596.000000 -133.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3798.000000 -178.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3798.000000 -178.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3798.000000 -178.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3798.000000 -178.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3798.000000 -178.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3798.000000 -178.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -173.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -173.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -173.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -173.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ce9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -173.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cec30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -292.000000) translate(0,15)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cec30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -292.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cec30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -292.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cec30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -292.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cec30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -292.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cee70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4116.000000 -114.000000) translate(0,15)">团</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cee70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4116.000000 -114.000000) translate(0,33)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cee70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4116.000000 -114.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cf380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -111.000000) translate(0,15)">昔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cf380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -111.000000) translate(0,33)">丙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cf380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -111.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cf8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4467.000000 -138.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cf8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4467.000000 -138.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f27e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4390.000000 -139.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f27e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4390.000000 -139.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f2a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4543.000000 -139.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f2a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4543.000000 -139.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f2c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -138.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f2c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -138.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f2ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4829.000000 -54.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f2ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4829.000000 -54.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f30e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -46.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f30e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -46.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f3320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4971.000000 -175.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f3320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4971.000000 -175.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f3560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5077.000000 -256.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f3560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5077.000000 -256.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f37a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5121.000000 -723.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f39e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5121.000000 -644.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f3c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -570.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f3e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5121.000000 -499.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f40a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5124.000000 -424.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f42e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5125.000000 -829.000000) translate(0,15)">万中线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f4520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5109.000000 -947.000000) translate(0,15)">万他马红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f4760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5111.000000 -1027.000000) translate(0,15)">马湾线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f4c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5228.000000 -866.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5123.000000 -1185.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f5140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3559.000000 -890.000000) translate(0,15)">110kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f5380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -918.000000) translate(0,15)">110kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f55c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3486.000000 -385.000000) translate(0,15)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f5800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4853.000000 -1197.000000) translate(0,15)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f5a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -425.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f5c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.000000 -1076.000000) translate(0,15)">I段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f5ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3573.000000 -986.000000) translate(0,12)">191</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f6100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -935.000000) translate(0,12)">1911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2635c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -971.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2636140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3759.000000 -954.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2636380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3758.000000 -1028.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26365c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3853.000000 -987.000000) translate(0,12)">192</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2636800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3895.000000 -936.000000) translate(0,12)">1921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2636a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3903.000000 -981.000000) translate(0,12)">19217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2636c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3903.000000 -1025.000000) translate(0,12)">19260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2636ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3903.000000 -1054.000000) translate(0,12)">19267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2637100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3895.000000 -1040.000000) translate(0,12)">1926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eafb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -987.000000) translate(0,12)">193</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eafdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -936.000000) translate(0,12)">1932</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -1040.000000) translate(0,12)">1936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4147.000000 -982.000000) translate(0,12)">19327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4147.000000 -1025.000000) translate(0,12)">19360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb06c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4147.000000 -1054.000000) translate(0,12)">19367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4278.000000 -975.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4287.000000 -954.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4286.000000 -1031.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -989.000000) translate(0,12)">194</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3803.000000 -809.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb1440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -726.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb1680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3795.000000 -872.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb18c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -830.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb1b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3807.000000 -785.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb4920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3807.000000 -701.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc6760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3994.000000 -845.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -861.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc6be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -862.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc6e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -793.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc7060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -794.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc72a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3637.000000 -591.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc74e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -401.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc7720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -469.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -424.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc7ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -511.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fca1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -330.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fca6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -327.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8d220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4936.000000 -834.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8d710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4879.000000 -836.000000) translate(0,12)">3751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4985.000000 -836.000000) translate(0,12)">3756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8db90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4877.000000 -966.000000) translate(0,12)">3741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8ddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4983.000000 -966.000000) translate(0,12)">3746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8e010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4931.000000 -1041.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8e250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -1043.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8e490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4874.000000 -1043.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d3450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -785.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20d3690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -395.000000) translate(0,15)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20d38d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5154.000000 -379.000000) translate(0,15)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d3b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -981.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d3d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4667.000000 -983.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d3f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4764.000000 -983.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d41d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4793.000000 -1116.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d9030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3852.000000 -581.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_31dce50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -1167.500000) translate(0,16)">万马变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_280dd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -1041.000000) translate(0,12)">1946</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2085ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -937.000000) translate(0,12)">1942</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2085d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -1058.000000) translate(0,12)">19467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2085f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -1026.000000) translate(0,12)">19460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20861a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -980.000000) translate(0,12)">19427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20863e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -1039.000000) translate(0,12)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2086620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3633.000000 -1056.000000) translate(0,12)">19167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2086860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3634.000000 -1021.000000) translate(0,12)">19160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2086aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3631.000000 -975.000000) translate(0,12)">19117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bb520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -50.000000) translate(0,12)">07400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3548950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3421.000000 -1169.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1ac5bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3264.000000 -253.000000) translate(0,17)">4707</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28478f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3698.000000 -118.000000) translate(0,15)">进</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28478f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3698.000000 -118.000000) translate(0,33)">化</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28478f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3698.000000 -118.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebd860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4306.000000 -455.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2836ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4934.000000 -964.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2838340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3734.000000 -268.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28388b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3833.000000 -285.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2838af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -271.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2838d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 -274.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2838f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -269.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28391b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3635.000000 -273.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34da810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3633.000000 -328.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3633.000000 -165.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dad40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3535.000000 -324.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34daf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3535.000000 -161.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34db1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 -323.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34db400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 -160.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34db640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -330.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34db880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -238.000000) translate(0,12)">0743</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dbac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -118.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dbd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -107.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dbf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -166.000000) translate(0,12)">07460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dc180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4148.000000 -326.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dc3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4148.000000 -163.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dc600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.000000 -329.000000) translate(0,12)">0771</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34dc840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.000000 -166.000000) translate(0,12)">0776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_34dd190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3166.000000 -803.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34de490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -522.000000) translate(0,15)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34de490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -522.000000) translate(0,33)">SFSZ11-40000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34de490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -522.000000) translate(0,51)">110±8×1.25%/38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34de490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -522.000000) translate(0,69)">40/40/40MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34de490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -522.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34de490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -522.000000) translate(0,105)">Ud1-2=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34de490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -522.000000) translate(0,123)">Ud1-3=17.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34de490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -522.000000) translate(0,141)">Ud2-3=6.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3067f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5111.000000 -1098.000000) translate(0,15)">万兴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306a3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4874.000000 -1111.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306a8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -1111.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306ab10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4931.000000 -1113.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_306ad50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5231.000000 -640.000000) translate(0,15)">调试区</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d9460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 1149.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d9720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3628.000000 1134.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d9960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 1119.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d9d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4703.000000 942.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31da040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 927.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31da280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4717.000000 912.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31da6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3841.000000 482.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31da960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 467.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31daba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 452.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31dafc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3666.000000 823.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31db280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3655.000000 808.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31db4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3680.000000 793.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31db8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 732.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31dbba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3955.000000 717.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31dbde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3980.000000 702.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31dc200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 0.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31dc500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3896.000000 -15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2086dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 616.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2087030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 631.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb23b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3458.000000 48.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb28c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3447.000000 33.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb2b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3472.000000 18.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebde00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3450.000000 897.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebe080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3443.000000 914.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebe2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3443.000000 942.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebe500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3443.000000 929.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebe740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3436.000000 881.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebe980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3449.000000 864.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebecb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 894.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebef30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 911.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebf170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 939.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebf3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 926.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebf5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 878.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebf830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 861.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebfb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3463.000000 470.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebfde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3463.000000 454.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 438.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3454.000000 423.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec04a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3463.000000 485.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec06e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 407.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 1199.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 1183.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 1167.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec1110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4673.000000 1152.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec1350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 1214.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec1590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 1136.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-28232">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -696.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4565" ObjectName="SW-CX_WM.CX_WM_1016SW"/>
     <cge:Meas_Ref ObjectId="28232"/>
    <cge:TPSR_Ref TObjectID="4565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28235">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 -754.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4568" ObjectName="SW-CX_WM.CX_WM_10160SW"/>
     <cge:Meas_Ref ObjectId="28235"/>
    <cge:TPSR_Ref TObjectID="4568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28250">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -561.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4575" ObjectName="SW-CX_WM.CX_WM_1010SW"/>
     <cge:Meas_Ref ObjectId="28250"/>
    <cge:TPSR_Ref TObjectID="4575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28247">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4574" ObjectName="SW-CX_WM.CX_WM_0016SW"/>
     <cge:Meas_Ref ObjectId="28247"/>
    <cge:TPSR_Ref TObjectID="4574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28246">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -394.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4573" ObjectName="SW-CX_WM.CX_WM_0011SW"/>
     <cge:Meas_Ref ObjectId="28246"/>
    <cge:TPSR_Ref TObjectID="4573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 -606.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 -484.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -633.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -633.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4571" ObjectName="SW-CX_WM.CX_WM_3016SW"/>
     <cge:Meas_Ref ObjectId="28242"/>
    <cge:TPSR_Ref TObjectID="4571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4757.000000 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4570" ObjectName="SW-CX_WM.CX_WM_3011SW"/>
     <cge:Meas_Ref ObjectId="28241"/>
    <cge:TPSR_Ref TObjectID="4570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28492">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -905.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4615" ObjectName="SW-CX_WM.CX_WM_1911SW"/>
     <cge:Meas_Ref ObjectId="28492"/>
    <cge:TPSR_Ref TObjectID="4615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54985">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -1009.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9694" ObjectName="SW-CX_WM.CX_WM_1916SW"/>
     <cge:Meas_Ref ObjectId="54985"/>
    <cge:TPSR_Ref TObjectID="9694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54987">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -1054.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9696" ObjectName="SW-CX_WM.CX_WM_19167SW"/>
     <cge:Meas_Ref ObjectId="54987"/>
    <cge:TPSR_Ref TObjectID="9696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54986">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -997.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9695" ObjectName="SW-CX_WM.CX_WM_19160SW"/>
     <cge:Meas_Ref ObjectId="54986"/>
    <cge:TPSR_Ref TObjectID="9695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54984">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9693" ObjectName="SW-CX_WM.CX_WM_19117SW"/>
     <cge:Meas_Ref ObjectId="54984"/>
    <cge:TPSR_Ref TObjectID="9693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28391">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4870.000000 -935.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4596" ObjectName="SW-CX_WM.CX_WM_3741SW"/>
     <cge:Meas_Ref ObjectId="28391"/>
    <cge:TPSR_Ref TObjectID="4596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28392">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4976.000000 -935.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4597" ObjectName="SW-CX_WM.CX_WM_3746SW"/>
     <cge:Meas_Ref ObjectId="28392"/>
    <cge:TPSR_Ref TObjectID="4597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28336">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.000000 -785.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4584" ObjectName="SW-CX_WM.CX_WM_3121SW"/>
     <cge:Meas_Ref ObjectId="28336"/>
    <cge:TPSR_Ref TObjectID="4584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4866.000000 -721.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -841.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -828.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -712.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28233">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -830.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4566" ObjectName="SW-CX_WM.CX_WM_10117SW"/>
     <cge:Meas_Ref ObjectId="28233"/>
    <cge:TPSR_Ref TObjectID="4566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -699.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -770.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28320">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3755.000000 -924.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4577" ObjectName="SW-CX_WM.CX_WM_19010SW"/>
     <cge:Meas_Ref ObjectId="28320"/>
    <cge:TPSR_Ref TObjectID="4577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28321">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -997.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4578" ObjectName="SW-CX_WM.CX_WM_19017SW"/>
     <cge:Meas_Ref ObjectId="28321"/>
    <cge:TPSR_Ref TObjectID="4578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28192">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -906.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4558" ObjectName="SW-CX_WM.CX_WM_1921SW"/>
     <cge:Meas_Ref ObjectId="28192"/>
    <cge:TPSR_Ref TObjectID="4558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28193">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -1010.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4559" ObjectName="SW-CX_WM.CX_WM_1926SW"/>
     <cge:Meas_Ref ObjectId="28193"/>
    <cge:TPSR_Ref TObjectID="4559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28195">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -1054.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4561" ObjectName="SW-CX_WM.CX_WM_19267SW"/>
     <cge:Meas_Ref ObjectId="28195"/>
    <cge:TPSR_Ref TObjectID="4561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28196">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -998.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4562" ObjectName="SW-CX_WM.CX_WM_19260SW"/>
     <cge:Meas_Ref ObjectId="28196"/>
    <cge:TPSR_Ref TObjectID="4562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28194">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -953.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4560" ObjectName="SW-CX_WM.CX_WM_19217SW"/>
     <cge:Meas_Ref ObjectId="28194"/>
    <cge:TPSR_Ref TObjectID="4560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28146">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 -906.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4552" ObjectName="SW-CX_WM.CX_WM_1932SW"/>
     <cge:Meas_Ref ObjectId="28146"/>
    <cge:TPSR_Ref TObjectID="4552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28147">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 -1010.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4553" ObjectName="SW-CX_WM.CX_WM_1936SW"/>
     <cge:Meas_Ref ObjectId="28147"/>
    <cge:TPSR_Ref TObjectID="4553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28149">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -1055.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4555" ObjectName="SW-CX_WM.CX_WM_19367SW"/>
     <cge:Meas_Ref ObjectId="28149"/>
    <cge:TPSR_Ref TObjectID="4555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28150">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -998.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4556" ObjectName="SW-CX_WM.CX_WM_19360SW"/>
     <cge:Meas_Ref ObjectId="28150"/>
    <cge:TPSR_Ref TObjectID="4556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28148">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -953.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4554" ObjectName="SW-CX_WM.CX_WM_19327SW"/>
     <cge:Meas_Ref ObjectId="28148"/>
    <cge:TPSR_Ref TObjectID="4554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28324">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.000000 -923.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4580" ObjectName="SW-CX_WM.CX_WM_19020SW"/>
     <cge:Meas_Ref ObjectId="28324"/>
    <cge:TPSR_Ref TObjectID="4580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28325">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4283.000000 -1000.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4581" ObjectName="SW-CX_WM.CX_WM_19027SW"/>
     <cge:Meas_Ref ObjectId="28325"/>
    <cge:TPSR_Ref TObjectID="4581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55060">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.000000 -907.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9697" ObjectName="SW-CX_WM.CX_WM_1942SW"/>
     <cge:Meas_Ref ObjectId="55060"/>
    <cge:TPSR_Ref TObjectID="9697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55061">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.000000 -1011.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9698" ObjectName="SW-CX_WM.CX_WM_1946SW"/>
     <cge:Meas_Ref ObjectId="55061"/>
    <cge:TPSR_Ref TObjectID="9698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55063">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4454.000000 -1056.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9700" ObjectName="SW-CX_WM.CX_WM_19467SW"/>
     <cge:Meas_Ref ObjectId="55063"/>
    <cge:TPSR_Ref TObjectID="9700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55064">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4454.000000 -999.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9701" ObjectName="SW-CX_WM.CX_WM_19460SW"/>
     <cge:Meas_Ref ObjectId="55064"/>
    <cge:TPSR_Ref TObjectID="9701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55062">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4454.000000 -953.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9699" ObjectName="SW-CX_WM.CX_WM_19427SW"/>
     <cge:Meas_Ref ObjectId="55062"/>
    <cge:TPSR_Ref TObjectID="9699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28374">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -831.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4589" ObjectName="SW-CX_WM.CX_WM_1121SW"/>
     <cge:Meas_Ref ObjectId="28374"/>
    <cge:TPSR_Ref TObjectID="4589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28376">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -763.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4591" ObjectName="SW-CX_WM.CX_WM_11217SW"/>
     <cge:Meas_Ref ObjectId="28376"/>
    <cge:TPSR_Ref TObjectID="4591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28375">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -832.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4590" ObjectName="SW-CX_WM.CX_WM_1122SW"/>
     <cge:Meas_Ref ObjectId="28375"/>
    <cge:TPSR_Ref TObjectID="4590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28377">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -764.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4592" ObjectName="SW-CX_WM.CX_WM_11227SW"/>
     <cge:Meas_Ref ObjectId="28377"/>
    <cge:TPSR_Ref TObjectID="4592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -805.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4599" ObjectName="SW-CX_WM.CX_WM_3751SW"/>
     <cge:Meas_Ref ObjectId="28403"/>
    <cge:TPSR_Ref TObjectID="4599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -805.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4600" ObjectName="SW-CX_WM.CX_WM_3756SW"/>
     <cge:Meas_Ref ObjectId="28404"/>
    <cge:TPSR_Ref TObjectID="4600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -1156.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -1156.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4973.000000 -1012.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4594" ObjectName="SW-CX_WM.CX_WM_3736SW"/>
     <cge:Meas_Ref ObjectId="28380"/>
    <cge:TPSR_Ref TObjectID="4594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28379">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 -1012.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4593" ObjectName="SW-CX_WM.CX_WM_3731SW"/>
     <cge:Meas_Ref ObjectId="28379"/>
    <cge:TPSR_Ref TObjectID="4593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -698.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 -698.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4694.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4694.000000 -211.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 -133.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -620.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -620.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -474.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4981.000000 -474.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.000000 -399.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -399.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4862.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4862.000000 -214.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 -136.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -298.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -135.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4484.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4484.000000 -139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4559.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4559.000000 -139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5000.000000 -301.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5119.000000 -297.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28361">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 -373.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4587" ObjectName="SW-CX_WM.CX_WM_0121SW"/>
     <cge:Meas_Ref ObjectId="28361"/>
    <cge:TPSR_Ref TObjectID="4587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -372.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3519.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20062" ObjectName="SW-CX_WM.CX_WM_0711SW"/>
     <cge:Meas_Ref ObjectId="95791"/>
    <cge:TPSR_Ref TObjectID="20062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3519.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20063" ObjectName="SW-CX_WM.CX_WM_0716SW"/>
     <cge:Meas_Ref ObjectId="95792"/>
    <cge:TPSR_Ref TObjectID="20063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3618.000000 -298.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20065" ObjectName="SW-CX_WM.CX_WM_0721SW"/>
     <cge:Meas_Ref ObjectId="95806"/>
    <cge:TPSR_Ref TObjectID="20065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95807">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3618.000000 -135.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20066" ObjectName="SW-CX_WM.CX_WM_0726SW"/>
     <cge:Meas_Ref ObjectId="95807"/>
    <cge:TPSR_Ref TObjectID="20066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 -300.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4608" ObjectName="SW-CX_WM.CX_WM_0741SW"/>
     <cge:Meas_Ref ObjectId="28448"/>
    <cge:TPSR_Ref TObjectID="4608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 -208.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4611" ObjectName="SW-CX_WM.CX_WM_0743SW"/>
     <cge:Meas_Ref ObjectId="28451"/>
    <cge:TPSR_Ref TObjectID="4611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28455">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3832.000000 -135.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4613" ObjectName="SW-CX_WM.CX_WM_07460SW"/>
     <cge:Meas_Ref ObjectId="28455"/>
    <cge:TPSR_Ref TObjectID="4613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -300.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4586" ObjectName="SW-CX_WM.CX_WM_0751SW"/>
     <cge:Meas_Ref ObjectId="28360"/>
    <cge:TPSR_Ref TObjectID="4586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28358">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 -297.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4585" ObjectName="SW-CX_WM.CX_WM_0901SW"/>
     <cge:Meas_Ref ObjectId="28358"/>
    <cge:TPSR_Ref TObjectID="4585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4602" ObjectName="SW-CX_WM.CX_WM_0761SW"/>
     <cge:Meas_Ref ObjectId="28415"/>
    <cge:TPSR_Ref TObjectID="4602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -133.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4603" ObjectName="SW-CX_WM.CX_WM_0766SW"/>
     <cge:Meas_Ref ObjectId="28416"/>
    <cge:TPSR_Ref TObjectID="4603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4231.000000 -299.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4605" ObjectName="SW-CX_WM.CX_WM_0771SW"/>
     <cge:Meas_Ref ObjectId="28429"/>
    <cge:TPSR_Ref TObjectID="4605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28430">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4231.000000 -136.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4606" ObjectName="SW-CX_WM.CX_WM_0776SW"/>
     <cge:Meas_Ref ObjectId="28430"/>
    <cge:TPSR_Ref TObjectID="4606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28319">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3734.000000 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4576" ObjectName="SW-CX_WM.CX_WM_1901SW"/>
     <cge:Meas_Ref ObjectId="28319"/>
    <cge:TPSR_Ref TObjectID="4576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28323">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -945.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4579" ObjectName="SW-CX_WM.CX_WM_1902SW"/>
     <cge:Meas_Ref ObjectId="28323"/>
    <cge:TPSR_Ref TObjectID="4579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28234">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 -670.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4567" ObjectName="SW-CX_WM.CX_WM_10167SW"/>
     <cge:Meas_Ref ObjectId="28234"/>
    <cge:TPSR_Ref TObjectID="4567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28449">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 -88.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4609" ObjectName="SW-CX_WM.CX_WM_0746SW"/>
     <cge:Meas_Ref ObjectId="28449"/>
    <cge:TPSR_Ref TObjectID="4609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4694.000000 -86.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4862.000000 -89.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28231">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -843.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4564" ObjectName="SW-CX_WM.CX_WM_1011SW"/>
     <cge:Meas_Ref ObjectId="28231"/>
    <cge:TPSR_Ref TObjectID="4564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -437.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 -1085.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4583" ObjectName="SW-CX_WM.CX_WM_3901SW"/>
     <cge:Meas_Ref ObjectId="28334"/>
    <cge:TPSR_Ref TObjectID="4583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3856.000000 -79.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4610" ObjectName="SW-CX_WM.CX_WM_07467SW"/>
     <cge:Meas_Ref ObjectId="28450"/>
    <cge:TPSR_Ref TObjectID="4610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -52.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -77.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 -50.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.000000 -80.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 -53.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3717.000000 -293.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20068" ObjectName="SW-CX_WM.CX_WM_0731SW"/>
     <cge:Meas_Ref ObjectId="95821"/>
    <cge:TPSR_Ref TObjectID="20068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-95822">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3717.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20069" ObjectName="SW-CX_WM.CX_WM_0736SW"/>
     <cge:Meas_Ref ObjectId="95822"/>
    <cge:TPSR_Ref TObjectID="20069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193559">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 -1084.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29440" ObjectName="SW-CX_WM.CX_WM_3721SW"/>
     <cge:Meas_Ref ObjectId="193559"/>
    <cge:TPSR_Ref TObjectID="29440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193560">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4973.000000 -1084.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29441" ObjectName="SW-CX_WM.CX_WM_3726SW"/>
     <cge:Meas_Ref ObjectId="193560"/>
    <cge:TPSR_Ref TObjectID="29441"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_WM.CX_WM_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3797.000000 42.000000)" xlink:href="#capacitor:shape26"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12211" ObjectName="CB-CX_WM.CX_WM_1C"/>
    <cge:TPSR_Ref TObjectID="12211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4676.000000 44.000000)" xlink:href="#capacitor:shape26"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4844.000000 41.000000)" xlink:href="#capacitor:shape26"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_33917b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.000000 -542.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3443430">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -546.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fb9000">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -588.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_303a750">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 -591.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f69ae0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 -540.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed43f0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3592.000000 -1062.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4eea0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3736.000000 -1090.000000)" xlink:href="#lightningRod:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37661d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5113.000000 -865.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e94f30">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3870.000000 -1063.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3250e00">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4114.000000 -1063.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d61a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4264.000000 -1089.000000)" xlink:href="#lightningRod:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fcd7e0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4424.000000 -1064.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f83f20">
    <use class="BV-35KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5047.000000 -956.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3645850">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5135.000000 -770.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36468e0">
    <use class="BV-35KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5049.000000 -827.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2870210">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5135.000000 -1121.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2864ba0">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5049.000000 -1178.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28554a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5130.000000 -977.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2856530">
    <use class="BV-35KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5044.000000 -1034.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_322ca10">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5134.000000 -663.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3061820">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 -146.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed91d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5135.000000 -585.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eda260">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5049.000000 -641.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28188c0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5135.110577 -511.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2819950">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5050.000000 -567.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3240740">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5138.110577 -439.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32417d0">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5053.000000 -495.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc8b00">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5143.110577 -364.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc9b90">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5058.000000 -420.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2545ea0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4866.000000 -151.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2816240">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -182.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a93360">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.333333 -186.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354e120">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.166667 -186.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_320e2f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4636.000000 -186.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26276f0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5018.000000 -296.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2627f70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5004.000000 -180.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35c88d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5098.000000 -205.000000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_338a1f0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4694.500000 -1120.500000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_338ba20">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4684.500000 -471.500000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_312c8c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3523.000000 -178.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_348bb80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.333333 -182.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37c91d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3819.000000 -144.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3090d70">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3972.000000 -294.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_360dc80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -182.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3610370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.000000 -205.000000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_280ca30">
    <use class="BV-0KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5048.000000 -720.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2087790">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3630.000000 -1073.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2088220">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 -1074.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2088f60">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -1074.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2089cd0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4462.000000 -1075.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_208aa40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 -528.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b6bd0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4158.500000 -124.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b7940">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4256.500000 -126.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3547110">
    <use class="BV-10KV" transform="matrix(0.500000 -0.000000 0.000000 -1.000000 4136.000000 -189.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3547980">
    <use class="BV-10KV" transform="matrix(0.500000 -0.000000 0.000000 -1.000000 4235.000000 -189.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2846ba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.333333 -177.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2848e10">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3742.500000 -119.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2849b20">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3643.500000 -123.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_284a890">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3545.500000 -119.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2833ee0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5146.000000 -895.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e14700">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5130.000000 -1049.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e15790">
    <use class="BV-35KV" transform="matrix(0.984375 -0.000000 0.000000 -0.933333 5044.000000 -1106.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28131" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3697.000000 -1149.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4614"/>
     <cge:Term_Ref ObjectID="6683"/>
    <cge:TPSR_Ref TObjectID="4614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28132" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3697.000000 -1149.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4614"/>
     <cge:Term_Ref ObjectID="6683"/>
    <cge:TPSR_Ref TObjectID="4614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28126" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3697.000000 -1149.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4614"/>
     <cge:Term_Ref ObjectID="6683"/>
    <cge:TPSR_Ref TObjectID="4614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27975" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3723.000000 -823.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4563"/>
     <cge:Term_Ref ObjectID="6581"/>
    <cge:TPSR_Ref TObjectID="4563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27976" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3723.000000 -823.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4563"/>
     <cge:Term_Ref ObjectID="6581"/>
    <cge:TPSR_Ref TObjectID="4563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27971" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3723.000000 -823.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4563"/>
     <cge:Term_Ref ObjectID="6581"/>
    <cge:TPSR_Ref TObjectID="4563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27990" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -482.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4572"/>
     <cge:Term_Ref ObjectID="6599"/>
    <cge:TPSR_Ref TObjectID="4572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27991" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -482.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4572"/>
     <cge:Term_Ref ObjectID="6599"/>
    <cge:TPSR_Ref TObjectID="4572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27986" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -482.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4572"/>
     <cge:Term_Ref ObjectID="6599"/>
    <cge:TPSR_Ref TObjectID="4572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27982" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.000000 -942.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4569"/>
     <cge:Term_Ref ObjectID="6593"/>
    <cge:TPSR_Ref TObjectID="4569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27983" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.000000 -942.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4569"/>
     <cge:Term_Ref ObjectID="6593"/>
    <cge:TPSR_Ref TObjectID="4569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27978" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.000000 -942.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4569"/>
     <cge:Term_Ref ObjectID="6593"/>
    <cge:TPSR_Ref TObjectID="4569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28060" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5203.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4598"/>
     <cge:Term_Ref ObjectID="6651"/>
    <cge:TPSR_Ref TObjectID="4598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28061" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5203.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4598"/>
     <cge:Term_Ref ObjectID="6651"/>
    <cge:TPSR_Ref TObjectID="4598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-54958" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5203.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4598"/>
     <cge:Term_Ref ObjectID="6651"/>
    <cge:TPSR_Ref TObjectID="4598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27967" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -1149.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4557"/>
     <cge:Term_Ref ObjectID="6569"/>
    <cge:TPSR_Ref TObjectID="4557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27968" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -1149.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4557"/>
     <cge:Term_Ref ObjectID="6569"/>
    <cge:TPSR_Ref TObjectID="4557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27962" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -1149.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4557"/>
     <cge:Term_Ref ObjectID="6569"/>
    <cge:TPSR_Ref TObjectID="4557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27958" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4159.000000 -1149.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4551"/>
     <cge:Term_Ref ObjectID="6557"/>
    <cge:TPSR_Ref TObjectID="4551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27959" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4159.000000 -1149.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4551"/>
     <cge:Term_Ref ObjectID="6557"/>
    <cge:TPSR_Ref TObjectID="4551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27953" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4159.000000 -1149.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4551"/>
     <cge:Term_Ref ObjectID="6557"/>
    <cge:TPSR_Ref TObjectID="4551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28140" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -1149.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4616"/>
     <cge:Term_Ref ObjectID="6687"/>
    <cge:TPSR_Ref TObjectID="4616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28141" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -1149.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4616"/>
     <cge:Term_Ref ObjectID="6687"/>
    <cge:TPSR_Ref TObjectID="4616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28135" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -1149.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4616"/>
     <cge:Term_Ref ObjectID="6687"/>
    <cge:TPSR_Ref TObjectID="4616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28050" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -732.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28050" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4588"/>
     <cge:Term_Ref ObjectID="6631"/>
    <cge:TPSR_Ref TObjectID="4588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28051" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -732.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4588"/>
     <cge:Term_Ref ObjectID="6631"/>
    <cge:TPSR_Ref TObjectID="4588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28046" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -732.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4588"/>
     <cge:Term_Ref ObjectID="6631"/>
    <cge:TPSR_Ref TObjectID="4588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28067" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5192.500000 -832.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4601"/>
     <cge:Term_Ref ObjectID="6657"/>
    <cge:TPSR_Ref TObjectID="4601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28068" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5192.500000 -832.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4601"/>
     <cge:Term_Ref ObjectID="6657"/>
    <cge:TPSR_Ref TObjectID="4601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28063" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5192.500000 -832.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28063" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4601"/>
     <cge:Term_Ref ObjectID="6657"/>
    <cge:TPSR_Ref TObjectID="4601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28057" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5181.000000 -1033.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4595"/>
     <cge:Term_Ref ObjectID="6645"/>
    <cge:TPSR_Ref TObjectID="4595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28058" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5181.000000 -1033.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28058" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4595"/>
     <cge:Term_Ref ObjectID="6645"/>
    <cge:TPSR_Ref TObjectID="4595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28053" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5181.000000 -1033.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28053" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4595"/>
     <cge:Term_Ref ObjectID="6645"/>
    <cge:TPSR_Ref TObjectID="4595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28074" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4604"/>
     <cge:Term_Ref ObjectID="6663"/>
    <cge:TPSR_Ref TObjectID="4604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28075" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4604"/>
     <cge:Term_Ref ObjectID="6663"/>
    <cge:TPSR_Ref TObjectID="4604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28070" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4604"/>
     <cge:Term_Ref ObjectID="6663"/>
    <cge:TPSR_Ref TObjectID="4604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28081" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4607"/>
     <cge:Term_Ref ObjectID="6669"/>
    <cge:TPSR_Ref TObjectID="4607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28082" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4607"/>
     <cge:Term_Ref ObjectID="6669"/>
    <cge:TPSR_Ref TObjectID="4607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28077" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4607"/>
     <cge:Term_Ref ObjectID="6669"/>
    <cge:TPSR_Ref TObjectID="4607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28088" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -0.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4612"/>
     <cge:Term_Ref ObjectID="6679"/>
    <cge:TPSR_Ref TObjectID="4612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28084" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -0.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4612"/>
     <cge:Term_Ref ObjectID="6679"/>
    <cge:TPSR_Ref TObjectID="4612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-28020" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.000000 -481.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28020" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-28021" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.000000 -481.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-28022" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.000000 -481.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-28026" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.000000 -481.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-28023" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.000000 -481.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28023" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28027" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.000000 -481.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28027" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4550"/>
     <cge:Term_Ref ObjectID="6556"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-27996" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -939.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27996" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-27997" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -939.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-27998" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -939.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-28002" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -939.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-27999" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -939.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28003" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -939.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4547"/>
     <cge:Term_Ref ObjectID="6553"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-28004" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -936.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28004" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-28005" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -936.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28005" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-28006" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -936.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-28010" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -936.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-28007" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -936.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28011" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -936.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28011" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4548"/>
     <cge:Term_Ref ObjectID="6554"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-27995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -632.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4617"/>
     <cge:Term_Ref ObjectID="6691"/>
    <cge:TPSR_Ref TObjectID="4617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-27994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -632.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4617"/>
     <cge:Term_Ref ObjectID="6691"/>
    <cge:TPSR_Ref TObjectID="4617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-28012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1210.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-28013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1210.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-28014" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1210.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-28018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1210.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-28015" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1210.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28019" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1210.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4549"/>
     <cge:Term_Ref ObjectID="6555"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3518.000000 -47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20064"/>
     <cge:Term_Ref ObjectID="28016"/>
    <cge:TPSR_Ref TObjectID="20064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3518.000000 -47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20064"/>
     <cge:Term_Ref ObjectID="28016"/>
    <cge:TPSR_Ref TObjectID="20064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3518.000000 -47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20064"/>
     <cge:Term_Ref ObjectID="28016"/>
    <cge:TPSR_Ref TObjectID="20064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20067"/>
     <cge:Term_Ref ObjectID="28022"/>
    <cge:TPSR_Ref TObjectID="20067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20067"/>
     <cge:Term_Ref ObjectID="28022"/>
    <cge:TPSR_Ref TObjectID="20067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20067"/>
     <cge:Term_Ref ObjectID="28022"/>
    <cge:TPSR_Ref TObjectID="20067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20070"/>
     <cge:Term_Ref ObjectID="28028"/>
    <cge:TPSR_Ref TObjectID="20070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20070"/>
     <cge:Term_Ref ObjectID="28028"/>
    <cge:TPSR_Ref TObjectID="20070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -46.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20070"/>
     <cge:Term_Ref ObjectID="28028"/>
    <cge:TPSR_Ref TObjectID="20070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-193586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -1107.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29442"/>
     <cge:Term_Ref ObjectID="41936"/>
    <cge:TPSR_Ref TObjectID="29442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-193587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -1107.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29442"/>
     <cge:Term_Ref ObjectID="41936"/>
    <cge:TPSR_Ref TObjectID="29442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-193583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -1107.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29442"/>
     <cge:Term_Ref ObjectID="41936"/>
    <cge:TPSR_Ref TObjectID="29442"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="yr_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/></g>
   <g href="yr_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/></g>
   <g href="AVC万马站.svg" style="fill-opacity:0"><rect height="46" qtmmishow="hidden" width="92" x="3390" y="-1182"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="22" x="3852" y="-1154"/></g>
   <g href="110kV万马变110kV迤万线191断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3573" y="-986"/></g>
   <g href="110kV万马变110kV永干万线192断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3853" y="-987"/></g>
   <g href="110kV万马变110kV分段112断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="24" x="3994" y="-845"/></g>
   <g href="110kV万马变110kV永万的线193断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4096" y="-987"/></g>
   <g href="110kV万马变110kV多万线194断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4410" y="-989"/></g>
   <g href="110kV万马变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="3852" y="-581"/></g>
   <g href="110kV万马变35kV马湾线373断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4931" y="-1041"/></g>
   <g href="110kV万马变35kV万他马红线374断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4934" y="-964"/></g>
   <g href="110kV万马变35kV万中线375断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4936" y="-834"/></g>
   <g href="110kV万马变10kV立溪冬线071断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3537" y="-269"/></g>
   <g href="110kV万马变10kV丙和良线072断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3635" y="-273"/></g>
   <g href="110kV万马变10kV进化线073断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3734" y="-268"/></g>
   <g href="110kV万马变10kV1号电容器074断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3833" y="-285"/></g>
   <g href="110kV万马变10kV团山线076断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4150" y="-271"/></g>
   <g href="110kV万马变10kV昔丙线077断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4249" y="-274"/></g>
   <g href="110kV万马变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3166" y="-803"/></g>
   <g href="110kV万马变35kV万兴线372断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4931" y="-1113"/></g>
  </g><g id="IosButton_Layer">
   <g DF8003:Layer="PUBLIC">
    <polygon fill="rgb(255,255,255)" points="3390,-1183 3387,-1186 3387,-1131 3390,-1134 3390,-1183" stroke="rgb(255,255,255)"/>
    <polygon fill="rgb(255,255,255)" points="3390,-1183 3387,-1186 3485,-1186 3482,-1183 3390,-1183" stroke="rgb(255,255,255)"/>
    <polygon fill="rgb(127,127,127)" points="3390,-1134 3387,-1131 3485,-1131 3482,-1134 3390,-1134" stroke="rgb(127,127,127)"/>
    <polygon fill="rgb(127,127,127)" points="3482,-1183 3485,-1186 3485,-1131 3482,-1134 3482,-1183" stroke="rgb(127,127,127)"/>
    <rect fill="rgb(255,255,255)" height="49" stroke="rgb(255,255,255)" width="92" x="3390" y="-1183"/>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="92" x="3390" y="-1183"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_WM.CX_WM_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3486,-358 4297,-358 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4550" ObjectName="BS-CX_WM.CX_WM_9IM"/>
    <cge:TPSR_Ref TObjectID="4550"/></metadata>
   <polyline fill="none" opacity="0" points="3486,-358 4297,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WM.CX_WM_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3558,-896 3964,-896 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4547" ObjectName="BS-CX_WM.CX_WM_1IM"/>
    <cge:TPSR_Ref TObjectID="4547"/></metadata>
   <polyline fill="none" opacity="0" points="3558,-896 3964,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WM.CX_WM_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-1190 4849,-779 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4549" ObjectName="BS-CX_WM.CX_WM_3IM"/>
    <cge:TPSR_Ref TObjectID="4549"/></metadata>
   <polyline fill="none" opacity="0" points="4849,-1190 4849,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4850,-743 4850,-385 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4850,-743 4850,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WM.CX_WM_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-897 4555,-897 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4548" ObjectName="BS-CX_WM.CX_WM_1IIM"/>
    <cge:TPSR_Ref TObjectID="4548"/></metadata>
   <polyline fill="none" opacity="0" points="4044,-897 4555,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-359 5181,-359 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4359,-359 5181,-359 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3226.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62638" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3274.538462 -1000.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62638" ObjectName="CX_WM:CX_WM_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-61618" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3274.538462 -959.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="61618" ObjectName="CX_WM:CX_WM_sumQ"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3087" y="-1059"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3238" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3189" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="46" qtmmishow="hidden" width="92" x="3390" y="-1182"/>
    </a>
   <metadata/><rect fill="white" height="46" opacity="0" stroke="white" transform="" width="92" x="3390" y="-1182"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="22" x="3852" y="-1154"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="22" x="3852" y="-1154"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3573" y="-986"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3573" y="-986"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3853" y="-987"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3853" y="-987"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="24" x="3994" y="-845"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="24" x="3994" y="-845"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4096" y="-987"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4096" y="-987"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4410" y="-989"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4410" y="-989"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="3852" y="-581"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="3852" y="-581"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4931" y="-1041"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4931" y="-1041"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4934" y="-964"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4934" y="-964"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4936" y="-834"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4936" y="-834"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3537" y="-269"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3537" y="-269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3635" y="-273"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3635" y="-273"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3734" y="-268"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3734" y="-268"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3833" y="-285"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3833" y="-285"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4150" y="-271"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4150" y="-271"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4249" y="-274"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4249" y="-274"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3166" y="-803"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3166" y="-803"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4931" y="-1113"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4931" y="-1113"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5236.000000 -839.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5236.000000 -839.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4978.500000 -75.500000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4978.500000 -75.500000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3932.500000 -74.500000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3932.500000 -74.500000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_WM"/>
</svg>