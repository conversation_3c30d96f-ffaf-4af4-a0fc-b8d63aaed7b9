<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-100" aopId="0" id="thSvg" viewBox="3116 -1198 2152 1202">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
    <line stroke-width="0.5" x1="3" x2="16" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line stroke-width="0.5" x1="15" x2="3" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
    <line stroke-width="0.5" x1="9" x2="34" y1="16" y2="5"/>
    <line stroke-width="0.5" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line stroke-width="0.5" x1="34" x2="10" y1="15" y2="4"/>
    <line stroke-width="0.5" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="earth:shape3">
    <line stroke-width="0.185606" x1="29" x2="29" y1="7" y2="11"/>
    <line stroke-width="0.226608" x1="4" x2="22" y1="9" y2="9"/>
    <line stroke-width="0.226608" x1="22" x2="22" y1="0" y2="18"/>
    <line stroke-width="0.226608" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line stroke-width="0.185606" x1="7" x2="11" y1="2" y2="2"/>
    <line stroke-width="0.226608" x1="9" x2="9" y1="27" y2="9"/>
    <line stroke-width="0.226608" x1="0" x2="18" y1="9" y2="9"/>
    <line stroke-width="0.226608" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line stroke-width="0.5" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="0.5" width="26" x="18" y="1"/>
    <line stroke-width="0.125" x1="1" x2="1" y1="5" y2="8"/>
    <line stroke-width="0.196875" x1="5" x2="5" y1="3" y2="10"/>
    <line stroke-width="0.125874" x1="9" x2="17" y1="7" y2="7"/>
    <line stroke-width="0.305732" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.25" width="16" x="1" y="5"/>
    <line stroke-width="0.5" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line stroke-width="0.5" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="0.5" width="12" x="1" y="20"/>
    <line stroke-width="0.125" x1="9" x2="6" y1="63" y2="63"/>
    <line stroke-width="0.196875" x1="11" x2="4" y1="60" y2="60"/>
    <line stroke-width="0.125874" x1="7" x2="7" y1="55" y2="47"/>
    <line stroke-width="0.305732" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line stroke-width="0.5" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5"/>
    <line stroke-width="0.0778547" x1="25" x2="20" y1="8" y2="10"/>
    <line stroke-width="0.0778547" x1="25" x2="20" y1="8" y2="5"/>
    <line stroke-width="0.0778547" x1="20" x2="20" y1="10" y2="5"/>
    <line stroke-width="0.0778547" x1="8" x2="5" y1="7" y2="9"/>
    <line stroke-width="0.0778547" x1="10" x2="8" y1="9" y2="7"/>
    <line stroke-width="0.0778547" x1="8" x2="8" y1="4" y2="7"/>
    <line stroke-width="0.0778547" x1="14" x2="11" y1="18" y2="20"/>
    <line stroke-width="0.0778547" x1="16" x2="14" y1="20" y2="18"/>
    <line stroke-width="0.0778547" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5"/>
    <ellipse cx="14" cy="16" rx="9" ry="7.5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line stroke-width="0.305732" x1="2" x2="13" y1="8" y2="8"/>
    <line stroke-width="0.125874" x1="7" x2="7" y1="9" y2="17"/>
    <line stroke-width="0.196875" x1="11" x2="4" y1="4" y2="4"/>
    <line stroke-width="0.125" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="0.5" width="12" x="1" y="18"/>
    <line stroke-width="0.5" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape33">
    <ellipse cx="7" cy="23" rx="6.5" ry="6"/>
    <line stroke-width="0.285714" x1="8" x2="8" y1="29" y2="43"/>
    <line stroke-width="0.285714" x1="29" x2="28" y1="20" y2="22"/>
    <line stroke-width="0.285714" x1="30" x2="29" y1="22" y2="20"/>
    <line stroke-width="0.285714" x1="29" x2="29" y1="34" y2="20"/>
    <line stroke-width="0.285714" x1="29" x2="8" y1="34" y2="34"/>
    <line stroke-width="0.12949" x1="29" x2="29" y1="16" y2="6"/>
    <line stroke-width="0.12949" x1="34" x2="24" y1="6" y2="6"/>
    <line stroke-width="0.12949" x1="31" x2="27" y1="4" y2="4"/>
    <line stroke-width="0.106061" x1="30" x2="28" y1="2" y2="2"/>
    <rect height="14" stroke-width="0.285714" width="6" x="26" y="16"/>
    <circle cx="14" cy="17" r="6.5"/>
    <circle cx="7" cy="13" r="6.5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line stroke-width="0.5" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line stroke-width="0.5" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line stroke-width="0.5" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line stroke-width="0.5" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line stroke-width="0.5" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line stroke-width="0.5" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line stroke-width="0.5" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line stroke-width="0.5" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24"/>
    <line stroke-width="0.255102" x1="26" x2="17" y1="34" y2="18"/>
    <line stroke-width="0.255102" x1="26" x2="34" y1="34" y2="18"/>
    <line stroke-width="0.255102" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5"/>
    <line stroke-width="0.255102" x1="24" x2="24" y1="58" y2="66"/>
    <line stroke-width="0.255102" x1="24" x2="32" y1="66" y2="74"/>
    <line stroke-width="0.255102" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="40" y2="36"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="36" y2="40"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="20" y2="16"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="16" y2="20"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 19.000000) translate(0,12)">禁止刷新</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(127,127,127);fill:none}
.BKBV-0KV { stroke:rgb(127,127,127);fill:rgb(127,127,127)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(0,72,216);fill:none}
.BKBV-10KV { stroke:rgb(0,72,216);fill:rgb(0,72,216)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(0,255,0);fill:none}
.BKBV-20KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(213,0,0);fill:none}
.BKBV-110KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-220KV { stroke:rgb(255,0,255);fill:none}
.BKBV-220KV { stroke:rgb(255,0,255);fill:rgb(255,0,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(255,0,0);fill:none}
.BKBV-500KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-750KV { stroke:rgb(255,0,0);fill:none}
.BKBV-750KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1212" width="2162" x="3111" y="-1203"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 759.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 729.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4240.000000 744.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 594.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 564.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4240.000000 579.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3568.000000 69.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3582.000000 39.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3557.000000 54.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.000000 69.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3761.000000 39.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3736.000000 54.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 69.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3955.000000 39.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 54.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 69.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 39.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 54.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 342.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 312.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 327.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 69.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 39.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4539.000000 54.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4716.000000 69.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 39.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4705.000000 54.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3866.000000 859.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3508.000000 520.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5002.000000 520.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.673667 -892.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53220">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -715.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9299" ObjectName="SW-CX_CJ.CX_CJ_301BK"/>
     <cge:Meas_Ref ObjectId="53220"/>
    <cge:TPSR_Ref TObjectID="9299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53226">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -551.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9301" ObjectName="SW-CX_CJ.CX_CJ_401BK"/>
     <cge:Meas_Ref ObjectId="53226"/>
    <cge:TPSR_Ref TObjectID="9301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53209">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 -388.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9296" ObjectName="SW-CX_CJ.CX_CJ_412BK"/>
     <cge:Meas_Ref ObjectId="53209"/>
    <cge:TPSR_Ref TObjectID="9296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53140">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3609.000000 -353.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9280" ObjectName="SW-CX_CJ.CX_CJ_431BK"/>
     <cge:Meas_Ref ObjectId="53140"/>
    <cge:TPSR_Ref TObjectID="9280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53184">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.666667 -353.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9292" ObjectName="SW-CX_CJ.CX_CJ_432BK"/>
     <cge:Meas_Ref ObjectId="53184"/>
    <cge:TPSR_Ref TObjectID="9292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.333333 -353.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9286" ObjectName="SW-CX_CJ.CX_CJ_433BK"/>
     <cge:Meas_Ref ObjectId="53162"/>
    <cge:TPSR_Ref TObjectID="9286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4146.000000 -353.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9283" ObjectName="SW-CX_CJ.CX_CJ_434BK"/>
     <cge:Meas_Ref ObjectId="53151"/>
    <cge:TPSR_Ref TObjectID="9283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53129">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.333333 -353.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9277" ObjectName="SW-CX_CJ.CX_CJ_435BK"/>
     <cge:Meas_Ref ObjectId="53129"/>
    <cge:TPSR_Ref TObjectID="9277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53173">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -353.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9289" ObjectName="SW-CX_CJ.CX_CJ_436BK"/>
     <cge:Meas_Ref ObjectId="53173"/>
    <cge:TPSR_Ref TObjectID="9289"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-CX_CJ.CX_CJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-829 4565,-829 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9274" ObjectName="BS-CX_CJ.CX_CJ_3IM"/>
    <cge:TPSR_Ref TObjectID="9274"/></metadata>
   <polyline fill="none" opacity="0" points="3976,-829 4565,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CJ.CX_CJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3546,-477 4400,-477 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9275" ObjectName="BS-CX_CJ.CX_CJ_9IM"/>
    <cge:TPSR_Ref TObjectID="9275"/></metadata>
   <polyline fill="none" opacity="0" points="3546,-477 4400,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CJ.CX_CJ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-477 5170,-477 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9276" ObjectName="BS-CX_CJ.CX_CJ_9IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4453,-477 5170,-477 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="16fcd80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.673667 -987.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="397eff0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.000000 -835.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="37de730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-829 4309,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9274@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3946460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-829 4309,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="38c8f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-885 4309,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-885 4309,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3983630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-927 4309,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-927 4309,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3981f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-981 4309,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="35aaa10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="35aaa10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-981 4309,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="38e7400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-997 4325,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="35aaa10@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="35aaa10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-997 4325,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="16fcb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-997 4374,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="16fcd80@0" Pin0InfoVect0LinkObjId="16fcd80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-997 4374,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="35ad810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-1021 4293,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="35aaa10@0" Pin0InfoVect0LinkObjId="35aaa10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-1021 4293,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39580d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-997 4309,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="35aaa10@0" Pin0InfoVect0LinkObjId="35aaa10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-997 4309,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="35aa7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-1021 4309,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="35aaa10@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="35aaa10_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-1021 4309,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="35a7e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-829 4106,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9274@0" ObjectIDZND0="9303@0" Pin0InfoVect0LinkObjId="SW-53242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3946460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-829 4106,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="35a8080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-895 4106,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="9303@1" ObjectIDZND0="9304@x" ObjectIDZND1="38c7f70@0" ObjectIDZND2="38c5760@0" Pin0InfoVect0LinkObjId="SW-53243_0" Pin0InfoVect1LinkObjId="38c7f70_0" Pin0InfoVect2LinkObjId="38c5760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-895 4106,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="39edab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-981 4106,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38c5760@1" ObjectIDZND0="395cae0@0" Pin0InfoVect0LinkObjId="395cae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="38c5760_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-981 4106,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="38c5a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-939 4152,-939 4152,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9303@x" ObjectIDND1="9304@x" ObjectIDND2="38c5760@0" ObjectIDZND0="38c7f70@0" Pin0InfoVect0LinkObjId="38c7f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-53242_0" Pin1InfoVect1LinkObjId="SW-53243_0" Pin1InfoVect2LinkObjId="38c5760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-939 4152,-939 4152,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="38c7ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-921 4106,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9303@x" ObjectIDND1="9304@x" ObjectIDZND0="38c7f70@0" ObjectIDZND1="38c5760@0" Pin0InfoVect0LinkObjId="38c7f70_0" Pin0InfoVect1LinkObjId="38c5760_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-53242_0" Pin1InfoVect1LinkObjId="SW-53243_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-921 4106,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="38c7d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-939 4106,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38c7f70@0" ObjectIDND1="9303@x" ObjectIDND2="9304@x" ObjectIDZND0="38c5760@0" Pin0InfoVect0LinkObjId="38c5760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="38c7f70_0" Pin1InfoVect1LinkObjId="SW-53242_0" Pin1InfoVect2LinkObjId="SW-53243_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-939 4106,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="3946200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-750 4190,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9299@1" ObjectIDZND0="9300@0" Pin0InfoVect0LinkObjId="SW-53223_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53220_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-750 4190,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="3946460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-812 4190,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9300@1" ObjectIDZND0="9274@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53223_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-812 4190,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39594a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-586 4190,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="9301@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53226_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-586 4190,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="3959700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-698 4190,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="9299@0" Pin0InfoVect0LinkObjId="SW-53220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-698 4190,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3959960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-477 4190,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="9302@0" Pin0InfoVect0LinkObjId="SW-53229_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-477 4190,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3959bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-534 4190,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9302@1" ObjectIDZND0="9301@0" Pin0InfoVect0LinkObjId="SW-53226_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53229_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-534 4190,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3984760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-477 4294,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-477 4294,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39849c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-413 4294,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-413 4294,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="387e040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-477 4368,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="9298@1" Pin0InfoVect0LinkObjId="SW-53212_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-477 4368,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="395b8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-458 4482,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9297@1" ObjectIDZND0="9276@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53211_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-458 4482,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37f2390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-422 4368,-398 4411,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9298@0" ObjectIDZND0="9296@1" Pin0InfoVect0LinkObjId="SW-53209_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53212_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-422 4368,-398 4411,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37f25f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-398 4482,-398 4482,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9296@0" ObjectIDZND0="9297@0" Pin0InfoVect0LinkObjId="SW-53211_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53209_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-398 4482,-398 4482,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39eef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-477 4928,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9276@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="395b8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-477 4928,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39ef190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-393 4977,-393 4977,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="39ef3f0@0" Pin0InfoVect0LinkObjId="39ef3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-393 4977,-393 4977,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="397e650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-393 4928,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="39ef3f0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="39ef3f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-393 4928,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="387aa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4927,-286 4927,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="387af00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="387af00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4927,-286 4927,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="387aca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-354 4928,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="39ef3f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="39ef3f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-354 4928,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38802e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3618,-477 3618,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="9281@1" Pin0InfoVect0LinkObjId="SW-53142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3618,-477 3618,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37b7900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3618,-417 3618,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9281@0" ObjectIDZND0="9280@1" Pin0InfoVect0LinkObjId="SW-53140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3618,-417 3618,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38d0c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3618,-361 3618,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9280@0" ObjectIDZND0="9282@1" Pin0InfoVect0LinkObjId="SW-53143_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3618,-361 3618,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38d0e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3618,-275 3666,-275 3666,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9282@x" ObjectIDZND0="38d1590@0" Pin0InfoVect0LinkObjId="38d1590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53143_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3618,-275 3666,-275 3666,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38d10d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3618,-300 3618,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9282@0" ObjectIDZND0="38d1590@0" Pin0InfoVect0LinkObjId="38d1590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53143_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3618,-300 3618,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38d1330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3618,-275 3618,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9282@x" ObjectIDND1="38d1590@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-53143_0" Pin1InfoVect1LinkObjId="38d1590_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3618,-275 3618,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="394bdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-477 3797,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="9293@1" Pin0InfoVect0LinkObjId="SW-53186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-477 3797,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38ec2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-417 3797,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9293@0" ObjectIDZND0="9292@1" Pin0InfoVect0LinkObjId="SW-53184_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53186_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-417 3797,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39df220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-361 3797,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9292@0" ObjectIDZND0="9294@1" Pin0InfoVect0LinkObjId="SW-53187_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-361 3797,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39df480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-275 3845,-275 3845,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9294@x" ObjectIDZND0="39dfba0@0" Pin0InfoVect0LinkObjId="39dfba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53187_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-275 3845,-275 3845,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39df6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-300 3797,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9294@0" ObjectIDZND0="39dfba0@0" Pin0InfoVect0LinkObjId="39dfba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53187_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-300 3797,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39df940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-275 3797,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9294@x" ObjectIDND1="39dfba0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-53187_0" Pin1InfoVect1LinkObjId="39dfba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-275 3797,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37b9db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-477 3976,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="9287@1" Pin0InfoVect0LinkObjId="SW-53164_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-477 3976,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38cd180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-417 3976,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9287@0" ObjectIDZND0="9286@1" Pin0InfoVect0LinkObjId="SW-53162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53164_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-417 3976,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3883d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-361 3976,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9286@0" ObjectIDZND0="9288@1" Pin0InfoVect0LinkObjId="SW-53165_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-361 3976,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3883f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-275 4024,-275 4024,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9288@x" ObjectIDZND0="38846a0@0" Pin0InfoVect0LinkObjId="38846a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53165_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-275 4024,-275 4024,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38841e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-300 3976,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9288@0" ObjectIDZND0="38846a0@0" Pin0InfoVect0LinkObjId="38846a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53165_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-300 3976,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3884440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-275 3976,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9288@x" ObjectIDND1="38846a0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-53165_0" Pin1InfoVect1LinkObjId="38846a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-275 3976,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38fbd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-477 4155,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="9284@1" Pin0InfoVect0LinkObjId="SW-53153_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-477 4155,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39427b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-417 4155,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9284@0" ObjectIDZND0="9283@1" Pin0InfoVect0LinkObjId="SW-53151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-417 4155,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39452c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-361 4155,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9283@0" ObjectIDZND0="9285@1" Pin0InfoVect0LinkObjId="SW-53154_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-361 4155,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="396c090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-275 4203,-275 4203,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9285@x" ObjectIDZND0="396c7b0@0" Pin0InfoVect0LinkObjId="396c7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53154_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-275 4203,-275 4203,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="396c2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-300 4155,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9285@0" ObjectIDZND0="396c7b0@0" Pin0InfoVect0LinkObjId="396c7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53154_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-300 4155,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="396c550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-275 4155,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9285@x" ObjectIDND1="396c7b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-53154_0" Pin1InfoVect1LinkObjId="396c7b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-275 4155,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39f0220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-477 4576,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9276@0" ObjectIDZND0="9278@1" Pin0InfoVect0LinkObjId="SW-53131_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="395b8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-477 4576,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39f2550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-417 4576,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9278@0" ObjectIDZND0="9277@1" Pin0InfoVect0LinkObjId="SW-53129_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53131_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-417 4576,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3953b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-361 4576,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9277@0" ObjectIDZND0="9279@1" Pin0InfoVect0LinkObjId="SW-53132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53129_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-361 4576,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3953df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-275 4624,-275 4624,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9279@x" ObjectIDZND0="3954510@0" Pin0InfoVect0LinkObjId="3954510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-275 4624,-275 4624,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3954050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-300 4576,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9279@0" ObjectIDZND0="3954510@0" Pin0InfoVect0LinkObjId="3954510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-300 4576,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39542b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-275 4576,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9279@x" ObjectIDND1="3954510@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-53132_0" Pin1InfoVect1LinkObjId="3954510_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-275 4576,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37e1950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-477 4755,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9276@0" ObjectIDZND0="9290@1" Pin0InfoVect0LinkObjId="SW-53175_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="395b8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-477 4755,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37e3c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-417 4755,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9290@0" ObjectIDZND0="9289@1" Pin0InfoVect0LinkObjId="SW-53173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53175_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-417 4755,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3970cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-361 4755,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9289@0" ObjectIDZND0="9291@1" Pin0InfoVect0LinkObjId="SW-53176_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-361 4755,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3970f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-275 4803,-275 4803,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9291@x" ObjectIDZND0="3971640@0" Pin0InfoVect0LinkObjId="3971640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-275 4803,-275 4803,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3971180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-300 4755,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9291@0" ObjectIDZND0="3971640@0" Pin0InfoVect0LinkObjId="3971640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-300 4755,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39713e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-275 4755,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9291@x" ObjectIDND1="3971640@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-53176_0" Pin1InfoVect1LinkObjId="3971640_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-275 4755,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="3bf7d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-861 4151,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="397eff0@0" ObjectIDZND0="9304@0" Pin0InfoVect0LinkObjId="SW-53243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="397eff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-861 4151,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="3a63dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-908 4151,-921 4106,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="9304@1" ObjectIDZND0="9303@x" ObjectIDZND1="38c7f70@0" ObjectIDZND2="38c5760@0" Pin0InfoVect0LinkObjId="SW-53242_0" Pin0InfoVect1LinkObjId="38c7f70_0" Pin0InfoVect2LinkObjId="38c5760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-908 4151,-921 4106,-921 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">川街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4252.000000 -1091.000000) translate(0,18)">35kV川街支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4070.000000 -1058.000000) translate(0,18)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4320.000000 -927.000000) translate(0,15)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4273.000000 -327.000000) translate(0,18)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4893.000000 -234.000000) translate(0,18)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3583.000000 -121.000000) translate(0,18)">恐龙谷专线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.666667 -121.000000) translate(0,18)">备用2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3941.333333 -121.000000) translate(0,18)">大村线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4120.000000 -121.000000) translate(0,18)">街区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4541.333333 -121.000000) translate(0,18)">长田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4720.000000 -121.000000) translate(0,18)">九渡线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4053.000000 -884.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4158.000000 -901.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4197.000000 -803.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3900.000000 -840.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4199.000000 -582.000000) translate(0,15)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4197.000000 -525.000000) translate(0,15)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4199.000000 -746.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3546.000000 -500.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5049.000000 -500.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3627.000000 -384.000000) translate(0,15)">431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3625.000000 -444.000000) translate(0,15)">4311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3625.000000 -327.000000) translate(0,15)">4316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3806.000000 -384.000000) translate(0,15)">432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3805.000000 -444.000000) translate(0,15)">4321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3805.000000 -327.000000) translate(0,15)">4326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3985.000000 -384.000000) translate(0,15)">433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3984.000000 -327.000000) translate(0,15)">4336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3984.000000 -444.000000) translate(0,15)">4331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4164.000000 -384.000000) translate(0,15)">434</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4162.000000 -327.000000) translate(0,15)">4346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4162.000000 -444.000000) translate(0,15)">4341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4411.000000 -377.000000) translate(0,15)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4375.000000 -448.000000) translate(0,15)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4489.000000 -449.000000) translate(0,15)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4585.000000 -384.000000) translate(0,15)">435</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4584.000000 -327.000000) translate(0,15)">4356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4584.000000 -444.000000) translate(0,15)">4351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4764.000000 -384.000000) translate(0,15)">436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -444.000000) translate(0,15)">4361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -327.000000) translate(0,15)">4366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4875.000000 -447.000000) translate(0,15)">4902</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="1200" stroke="rgb(213,0,0)" stroke-width="0.5" width="2150" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-597"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -613.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -613.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.000000 -339.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.000000 -339.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" stroke="rgb(255,0,0)" width="138" x="3248" y="-1177"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" stroke="rgb(255,0,0)" width="77" x="3199" y="-1194"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="35aaa10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4234.673667 -1015.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="38c5760">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4097.000000 -945.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="38c7f70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4145.000000 -947.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="395cae0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4120.000000 -1028.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="39ef3f0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4985.000000 -463.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="387af00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4919.000000 -244.000000)" xlink:href="#lightningRod:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="38d1590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="39dfba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3837.666667 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="38846a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.333333 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="396c7b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3954510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4617.333333 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3971640">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.673667 -827.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.673667 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4333.673667 -971.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.000000 -837.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9303" ObjectName="SW-CX_CJ.CX_CJ_3901SW"/>
     <cge:Meas_Ref ObjectId="53242"/>
    <cge:TPSR_Ref TObjectID="9303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4136.000000 -850.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9304" ObjectName="SW-CX_CJ.CX_CJ_39017SW"/>
     <cge:Meas_Ref ObjectId="53243"/>
    <cge:TPSR_Ref TObjectID="9304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53223">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -754.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9300" ObjectName="SW-CX_CJ.CX_CJ_3011SW"/>
     <cge:Meas_Ref ObjectId="53223"/>
    <cge:TPSR_Ref TObjectID="9300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53229">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -476.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9302" ObjectName="SW-CX_CJ.CX_CJ_4011SW"/>
     <cge:Meas_Ref ObjectId="53229"/>
    <cge:TPSR_Ref TObjectID="9302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 -408.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53212">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 -399.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9298" ObjectName="SW-CX_CJ.CX_CJ_4121SW"/>
     <cge:Meas_Ref ObjectId="53212"/>
    <cge:TPSR_Ref TObjectID="9298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53211">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4467.000000 -400.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9297" ObjectName="SW-CX_CJ.CX_CJ_4122SW"/>
     <cge:Meas_Ref ObjectId="53211"/>
    <cge:TPSR_Ref TObjectID="9297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 -304.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9281" ObjectName="SW-CX_CJ.CX_CJ_4311SW"/>
     <cge:Meas_Ref ObjectId="53142"/>
    <cge:TPSR_Ref TObjectID="9281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53143">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9282" ObjectName="SW-CX_CJ.CX_CJ_4316SW"/>
     <cge:Meas_Ref ObjectId="53143"/>
    <cge:TPSR_Ref TObjectID="9282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53186">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.666667 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9293" ObjectName="SW-CX_CJ.CX_CJ_4321SW"/>
     <cge:Meas_Ref ObjectId="53186"/>
    <cge:TPSR_Ref TObjectID="9293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53187">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.666667 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9294" ObjectName="SW-CX_CJ.CX_CJ_4326SW"/>
     <cge:Meas_Ref ObjectId="53187"/>
    <cge:TPSR_Ref TObjectID="9294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53164">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.333333 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9287" ObjectName="SW-CX_CJ.CX_CJ_4331SW"/>
     <cge:Meas_Ref ObjectId="53164"/>
    <cge:TPSR_Ref TObjectID="9287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.333333 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9288" ObjectName="SW-CX_CJ.CX_CJ_4336SW"/>
     <cge:Meas_Ref ObjectId="53165"/>
    <cge:TPSR_Ref TObjectID="9288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53153">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4140.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9284" ObjectName="SW-CX_CJ.CX_CJ_4341SW"/>
     <cge:Meas_Ref ObjectId="53153"/>
    <cge:TPSR_Ref TObjectID="9284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53154">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4140.000000 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9285" ObjectName="SW-CX_CJ.CX_CJ_4346SW"/>
     <cge:Meas_Ref ObjectId="53154"/>
    <cge:TPSR_Ref TObjectID="9285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53131">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4561.333333 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9278" ObjectName="SW-CX_CJ.CX_CJ_4351SW"/>
     <cge:Meas_Ref ObjectId="53131"/>
    <cge:TPSR_Ref TObjectID="9278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4561.333333 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9279" ObjectName="SW-CX_CJ.CX_CJ_4356SW"/>
     <cge:Meas_Ref ObjectId="53132"/>
    <cge:TPSR_Ref TObjectID="9279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53175">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4740.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9290" ObjectName="SW-CX_CJ.CX_CJ_4361SW"/>
     <cge:Meas_Ref ObjectId="53175"/>
    <cge:TPSR_Ref TObjectID="9290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53176">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4740.000000 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9291" ObjectName="SW-CX_CJ.CX_CJ_4366SW"/>
     <cge:Meas_Ref ObjectId="53176"/>
    <cge:TPSR_Ref TObjectID="9291"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53100" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 -759.000000) translate(0,12)">53100.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53100" ObjectName="CX_CJ.CX_CJ_301BK:F"/>
     <cge:PSR_Ref ObjectID="9299"/>
     <cge:Term_Ref ObjectID="13399"/>
    <cge:TPSR_Ref TObjectID="9299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53101" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 -759.000000) translate(0,27)">53101.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53101" ObjectName="CX_CJ.CX_CJ_301BK:F"/>
     <cge:PSR_Ref ObjectID="9299"/>
     <cge:Term_Ref ObjectID="13399"/>
    <cge:TPSR_Ref TObjectID="9299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53097" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 -759.000000) translate(0,42)">53097.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53097" ObjectName="CX_CJ.CX_CJ_301BK:F"/>
     <cge:PSR_Ref ObjectID="9299"/>
     <cge:Term_Ref ObjectID="13399"/>
    <cge:TPSR_Ref TObjectID="9299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53106" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 -594.000000) translate(0,12)">53106.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53106" ObjectName="CX_CJ.CX_CJ_401BK:F"/>
     <cge:PSR_Ref ObjectID="9301"/>
     <cge:Term_Ref ObjectID="13403"/>
    <cge:TPSR_Ref TObjectID="9301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53107" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 -594.000000) translate(0,27)">53107.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53107" ObjectName="CX_CJ.CX_CJ_401BK:F"/>
     <cge:PSR_Ref ObjectID="9301"/>
     <cge:Term_Ref ObjectID="13403"/>
    <cge:TPSR_Ref TObjectID="9301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53103" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 -594.000000) translate(0,42)">53103.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53103" ObjectName="CX_CJ.CX_CJ_401BK:F"/>
     <cge:PSR_Ref ObjectID="9301"/>
     <cge:Term_Ref ObjectID="13403"/>
    <cge:TPSR_Ref TObjectID="9301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53064" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3621.000000 -69.000000) translate(0,12)">53064.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53064" ObjectName="CX_CJ.CX_CJ_431BK:F"/>
     <cge:PSR_Ref ObjectID="9280"/>
     <cge:Term_Ref ObjectID="13361"/>
    <cge:TPSR_Ref TObjectID="9280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53065" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3621.000000 -69.000000) translate(0,27)">53065.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53065" ObjectName="CX_CJ.CX_CJ_431BK:F"/>
     <cge:PSR_Ref ObjectID="9280"/>
     <cge:Term_Ref ObjectID="13361"/>
    <cge:TPSR_Ref TObjectID="9280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53062" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3621.000000 -69.000000) translate(0,42)">53062.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53062" ObjectName="CX_CJ.CX_CJ_431BK:F"/>
     <cge:PSR_Ref ObjectID="9280"/>
     <cge:Term_Ref ObjectID="13361"/>
    <cge:TPSR_Ref TObjectID="9280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53084" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3802.000000 -69.000000) translate(0,12)">53084.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53084" ObjectName="CX_CJ.CX_CJ_432BK:F"/>
     <cge:PSR_Ref ObjectID="9292"/>
     <cge:Term_Ref ObjectID="13385"/>
    <cge:TPSR_Ref TObjectID="9292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53085" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3802.000000 -69.000000) translate(0,27)">53085.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53085" ObjectName="CX_CJ.CX_CJ_432BK:F"/>
     <cge:PSR_Ref ObjectID="9292"/>
     <cge:Term_Ref ObjectID="13385"/>
    <cge:TPSR_Ref TObjectID="9292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53082" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3802.000000 -69.000000) translate(0,42)">53082.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53082" ObjectName="CX_CJ.CX_CJ_432BK:F"/>
     <cge:PSR_Ref ObjectID="9292"/>
     <cge:Term_Ref ObjectID="13385"/>
    <cge:TPSR_Ref TObjectID="9292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53074" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4001.000000 -69.000000) translate(0,12)">53074.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53074" ObjectName="CX_CJ.CX_CJ_433BK:F"/>
     <cge:PSR_Ref ObjectID="9286"/>
     <cge:Term_Ref ObjectID="13373"/>
    <cge:TPSR_Ref TObjectID="9286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53075" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4001.000000 -69.000000) translate(0,27)">53075.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53075" ObjectName="CX_CJ.CX_CJ_433BK:F"/>
     <cge:PSR_Ref ObjectID="9286"/>
     <cge:Term_Ref ObjectID="13373"/>
    <cge:TPSR_Ref TObjectID="9286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53072" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4001.000000 -69.000000) translate(0,42)">53072.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53072" ObjectName="CX_CJ.CX_CJ_433BK:F"/>
     <cge:PSR_Ref ObjectID="9286"/>
     <cge:Term_Ref ObjectID="13373"/>
    <cge:TPSR_Ref TObjectID="9286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53069" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4168.000000 -69.000000) translate(0,12)">53069.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53069" ObjectName="CX_CJ.CX_CJ_434BK:F"/>
     <cge:PSR_Ref ObjectID="9283"/>
     <cge:Term_Ref ObjectID="13367"/>
    <cge:TPSR_Ref TObjectID="9283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53070" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4168.000000 -69.000000) translate(0,27)">53070.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53070" ObjectName="CX_CJ.CX_CJ_434BK:F"/>
     <cge:PSR_Ref ObjectID="9283"/>
     <cge:Term_Ref ObjectID="13367"/>
    <cge:TPSR_Ref TObjectID="9283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53067" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4168.000000 -69.000000) translate(0,42)">53067.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53067" ObjectName="CX_CJ.CX_CJ_434BK:F"/>
     <cge:PSR_Ref ObjectID="9283"/>
     <cge:Term_Ref ObjectID="13367"/>
    <cge:TPSR_Ref TObjectID="9283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53094" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4432.000000 -342.000000) translate(0,12)">53094.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53094" ObjectName="CX_CJ.CX_CJ_412BK:F"/>
     <cge:PSR_Ref ObjectID="9296"/>
     <cge:Term_Ref ObjectID="13393"/>
    <cge:TPSR_Ref TObjectID="9296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53095" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4432.000000 -342.000000) translate(0,27)">53095.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53095" ObjectName="CX_CJ.CX_CJ_412BK:F"/>
     <cge:PSR_Ref ObjectID="9296"/>
     <cge:Term_Ref ObjectID="13393"/>
    <cge:TPSR_Ref TObjectID="9296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53092" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4432.000000 -342.000000) translate(0,42)">53092.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53092" ObjectName="CX_CJ.CX_CJ_412BK:F"/>
     <cge:PSR_Ref ObjectID="9296"/>
     <cge:Term_Ref ObjectID="13393"/>
    <cge:TPSR_Ref TObjectID="9296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53059" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4602.000000 -69.000000) translate(0,12)">53059.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53059" ObjectName="CX_CJ.CX_CJ_435BK:F"/>
     <cge:PSR_Ref ObjectID="9277"/>
     <cge:Term_Ref ObjectID="13355"/>
    <cge:TPSR_Ref TObjectID="9277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53060" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4602.000000 -69.000000) translate(0,27)">53060.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53060" ObjectName="CX_CJ.CX_CJ_435BK:F"/>
     <cge:PSR_Ref ObjectID="9277"/>
     <cge:Term_Ref ObjectID="13355"/>
    <cge:TPSR_Ref TObjectID="9277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53057" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4602.000000 -69.000000) translate(0,42)">53057.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53057" ObjectName="CX_CJ.CX_CJ_435BK:F"/>
     <cge:PSR_Ref ObjectID="9277"/>
     <cge:Term_Ref ObjectID="13355"/>
    <cge:TPSR_Ref TObjectID="9277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53079" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4771.000000 -69.000000) translate(0,12)">53079.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53079" ObjectName="CX_CJ.CX_CJ_436BK:F"/>
     <cge:PSR_Ref ObjectID="9289"/>
     <cge:Term_Ref ObjectID="13379"/>
    <cge:TPSR_Ref TObjectID="9289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53080" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4771.000000 -69.000000) translate(0,27)">53080.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53080" ObjectName="CX_CJ.CX_CJ_436BK:F"/>
     <cge:PSR_Ref ObjectID="9289"/>
     <cge:Term_Ref ObjectID="13379"/>
    <cge:TPSR_Ref TObjectID="9289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53077" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4771.000000 -69.000000) translate(0,42)">53077.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53077" ObjectName="CX_CJ.CX_CJ_436BK:F"/>
     <cge:PSR_Ref ObjectID="9289"/>
     <cge:Term_Ref ObjectID="13379"/>
    <cge:TPSR_Ref TObjectID="9289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-53111" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3958.000000 -859.000000) translate(0,12)">53111.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53111" ObjectName="CX_CJ.CX_CJ_3IM:F"/>
     <cge:PSR_Ref ObjectID="9274"/>
     <cge:Term_Ref ObjectID="13352"/>
    <cge:TPSR_Ref TObjectID="9274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-53115" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3598.000000 -520.000000) translate(0,12)">53115.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53115" ObjectName="CX_CJ.CX_CJ_9IM:F"/>
     <cge:PSR_Ref ObjectID="9275"/>
     <cge:Term_Ref ObjectID="13353"/>
    <cge:TPSR_Ref TObjectID="9275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-53119" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5101.000000 -520.000000) translate(0,12)">53119.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53119" ObjectName="CX_CJ.CX_CJ_9IIM:F"/>
     <cge:PSR_Ref ObjectID="9276"/>
     <cge:Term_Ref ObjectID="13354"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4"/>
</svg>