<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-246" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-721 -1271 2049 1187">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape36">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="63" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="48" x2="45" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="28" x2="11" y1="58" y2="58"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="30"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="79" y2="25"/>
    <polyline arcFlag="1" points="11,39 10,39 9,39 9,40 8,40 8,40 7,41 7,41 6,42 6,42 6,43 6,43 5,44 5,45 5,45 6,46 6,47 6,47 6,48 7,48 7,49 8,49 8,50 9,50 9,50 10,50 11,50 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,28 10,28 9,28 9,28 8,29 8,29 7,29 7,30 6,30 6,31 6,32 6,32 5,33 5,34 5,34 6,35 6,36 6,36 6,37 7,37 7,38 8,38 8,38 9,39 9,39 10,39 11,39 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,18 10,18 9,18 9,18 8,18 8,19 7,19 7,20 6,20 6,21 6,21 6,22 5,23 5,23 5,24 6,25 6,25 6,26 6,26 7,27 7,27 8,28 8,28 9,28 9,29 10,29 11,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="58" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="18" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="29" x2="29" y1="92" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="16" x2="28" y1="79" y2="79"/>
    <polyline points="29,92 31,92 33,91 34,91 36,90 37,89 39,88 40,86 41,84 41,83 42,81 42,79 42,77 41,75 41,74 40,72 39,71 37,69 36,68 34,67 33,67 31,66 29,66 27,66 25,67 24,67 22,68 21,69 19,71 18,72 17,74 17,75 16,77 16,79 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="47" x2="28" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="11" y1="11" y2="11"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape15">
    <ellipse cx="23" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="2" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="4" y="14"/>
    <ellipse cx="34" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="23" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="34" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="24,36 8,36 8,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="3" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="21" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="24" x2="24" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="35" x2="35" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="32" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="37" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="34" x2="32" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="35" x2="37" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="37" x2="32" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="23" x2="23" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="20" x2="23" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="26" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34736a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34740d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3474a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34756c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3476920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3477540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3477fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3478a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34792f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3479be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_347a890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_347b170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_347cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_347d8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_347e1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_347eaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34801c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3480ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3481780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3482170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3483350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3483cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34847c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3489b40" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_348a890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34864d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3487a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3488490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_34975a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_348c2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1197" width="2059" x="-726" y="-1276"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="866" x2="874" y1="-894" y2="-894"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="56" x2="64" y1="-1008" y2="-1008"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-186630">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 -1194.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28265" ObjectName="SW-YM_TG.YM_TG_3516SW"/>
     <cge:Meas_Ref ObjectId="186630"/>
    <cge:TPSR_Ref TObjectID="28265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186688">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 232.000000 -309.125000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28279" ObjectName="SW-YM_TG.YM_TG_0626SW"/>
     <cge:Meas_Ref ObjectId="186688"/>
    <cge:TPSR_Ref TObjectID="28279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186686">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 281.819337 -429.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28277" ObjectName="SW-YM_TG.YM_TG_06260SW"/>
     <cge:Meas_Ref ObjectId="186686"/>
    <cge:TPSR_Ref TObjectID="28277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1.000000 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28299" ObjectName="SW-YM_TG.YM_TG_0616SW"/>
     <cge:Meas_Ref ObjectId="186713"/>
    <cge:TPSR_Ref TObjectID="28299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 42.819337 -430.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28297" ObjectName="SW-YM_TG.YM_TG_06160SW"/>
     <cge:Meas_Ref ObjectId="186711"/>
    <cge:TPSR_Ref TObjectID="28297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45.819337 -308.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.037037 860.000000 -910.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28268" ObjectName="SW-YM_TG.YM_TG_3501XC"/>
     <cge:Meas_Ref ObjectId="186634"/>
    <cge:TPSR_Ref TObjectID="28268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.037037 860.000000 -809.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28269" ObjectName="SW-YM_TG.YM_TG_3501XC1"/>
     <cge:Meas_Ref ObjectId="186634"/>
    <cge:TPSR_Ref TObjectID="28269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.037037 50.000000 -1020.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28266" ObjectName="SW-CX_TG.CX_TG_3901XC"/>
     <cge:Meas_Ref ObjectId="186631"/>
    <cge:TPSR_Ref TObjectID="28266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.037037 50.000000 -919.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28267" ObjectName="SW-CX_TG.CX_TG_3901XC1"/>
     <cge:Meas_Ref ObjectId="186631"/>
    <cge:TPSR_Ref TObjectID="28267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186644">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -724.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28273" ObjectName="SW-CX_TG.CX_TG_0011SW"/>
     <cge:Meas_Ref ObjectId="186644"/>
    <cge:TPSR_Ref TObjectID="28273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186645">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 433.000000 -617.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28274" ObjectName="SW-CX_TG.CX_TG_0016SW"/>
     <cge:Meas_Ref ObjectId="186645"/>
    <cge:TPSR_Ref TObjectID="28274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186710">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2.000000 -522.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28296" ObjectName="SW-YM_TG.YM_TG_0611SW"/>
     <cge:Meas_Ref ObjectId="186710"/>
    <cge:TPSR_Ref TObjectID="28296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186685">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 231.000000 -508.750000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28276" ObjectName="SW-YM_TG.YM_TG_0621SW"/>
     <cge:Meas_Ref ObjectId="186685"/>
    <cge:TPSR_Ref TObjectID="28276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -216.000000 -346.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186715">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -186.180663 -430.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28301" ObjectName="SW-YM_TG.YM_TG_09017SW"/>
     <cge:Meas_Ref ObjectId="186715"/>
    <cge:TPSR_Ref TObjectID="28301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186714">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 -220.000000 -480.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28300" ObjectName="SW-YM_TG.YM_TG_0901SW"/>
     <cge:Meas_Ref ObjectId="186714"/>
    <cge:TPSR_Ref TObjectID="28300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186693">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 493.000000 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28283" ObjectName="SW-YM_TG.YM_TG_0636SW"/>
     <cge:Meas_Ref ObjectId="186693"/>
    <cge:TPSR_Ref TObjectID="28283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186691">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 542.819337 -427.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28281" ObjectName="SW-YM_TG.YM_TG_06360SW"/>
     <cge:Meas_Ref ObjectId="186691"/>
    <cge:TPSR_Ref TObjectID="28281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186690">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 492.000000 -507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28280" ObjectName="SW-YM_TG.YM_TG_0631SW"/>
     <cge:Meas_Ref ObjectId="186690"/>
    <cge:TPSR_Ref TObjectID="28280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186698">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.000000 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28287" ObjectName="SW-YM_TG.YM_TG_0646SW"/>
     <cge:Meas_Ref ObjectId="186698"/>
    <cge:TPSR_Ref TObjectID="28287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186696">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 784.819337 -427.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28285" ObjectName="SW-YM_TG.YM_TG_06460SW"/>
     <cge:Meas_Ref ObjectId="186696"/>
    <cge:TPSR_Ref TObjectID="28285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 -507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28284" ObjectName="SW-YM_TG.YM_TG_0641SW"/>
     <cge:Meas_Ref ObjectId="186695"/>
    <cge:TPSR_Ref TObjectID="28284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186703">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 975.000000 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28291" ObjectName="SW-YM_TG.YM_TG_0656SW"/>
     <cge:Meas_Ref ObjectId="186703"/>
    <cge:TPSR_Ref TObjectID="28291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186701">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.819337 -427.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28289" ObjectName="SW-YM_TG.YM_TG_06560SW"/>
     <cge:Meas_Ref ObjectId="186701"/>
    <cge:TPSR_Ref TObjectID="28289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186700">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 975.000000 -508.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28288" ObjectName="SW-YM_TG.YM_TG_0651SW"/>
     <cge:Meas_Ref ObjectId="186700"/>
    <cge:TPSR_Ref TObjectID="28288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186708">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1206.000000 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28295" ObjectName="SW-YM_TG.YM_TG_0666SW"/>
     <cge:Meas_Ref ObjectId="186708"/>
    <cge:TPSR_Ref TObjectID="28295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186706">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1255.819337 -427.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28293" ObjectName="SW-YM_TG.YM_TG_06660SW"/>
     <cge:Meas_Ref ObjectId="186706"/>
    <cge:TPSR_Ref TObjectID="28293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1206.000000 -508.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28292" ObjectName="SW-YM_TG.YM_TG_0661SW"/>
     <cge:Meas_Ref ObjectId="186705"/>
    <cge:TPSR_Ref TObjectID="28292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186641">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -1034.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28271" ObjectName="SW-YM_TG.YM_TG_301XC"/>
     <cge:Meas_Ref ObjectId="186641"/>
    <cge:TPSR_Ref TObjectID="28271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186641">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -969.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28272" ObjectName="SW-YM_TG.YM_TG_301XC1"/>
     <cge:Meas_Ref ObjectId="186641"/>
    <cge:TPSR_Ref TObjectID="28272"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YM_TG.YM_TG_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-286,-589 1287,-589 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28263" ObjectName="BS-YM_TG.YM_TG_9IM"/>
    <cge:TPSR_Ref TObjectID="28263"/></metadata>
   <polyline fill="none" opacity="0" points="-286,-589 1287,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_TG.YM_TG_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-92,-1098 993,-1098 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28262" ObjectName="BS-YM_TG.YM_TG_3IM"/>
    <cge:TPSR_Ref TObjectID="28262"/></metadata>
   <polyline fill="none" opacity="0" points="-92,-1098 993,-1098 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 231.643930 -221.125000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 492.643930 -219.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.643930 -219.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 974.643930 -219.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1205.643930 -219.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3a98010" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.819337 -428.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3766630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 93.819337 -429.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a8ef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 96.819337 -307.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c24d40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -135.180663 -429.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3662a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 593.819337 -426.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34439e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 835.819337 -426.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3674c20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1075.819337 -426.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3763400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1306.819337 -426.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_33a4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-368 241,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34e20c0@0" ObjectIDZND0="28279@1" Pin0InfoVect0LinkObjId="SW-186688_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e20c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-368 241,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3500a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="310,-278 241,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_33502c0@0" ObjectIDZND0="28279@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-186688_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33502c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="310,-278 241,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3514190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-314 241,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28279@0" ObjectIDZND0="g_33502c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_33502c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186688_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="241,-314 241,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35036e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-278 241,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_33502c0@0" ObjectIDND1="28279@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33502c0_0" Pin1InfoVect1LinkObjId="SW-186688_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-278 241,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aba360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="323,-434 337,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28277@0" ObjectIDZND0="g_3a98010@0" Pin0InfoVect0LinkObjId="g_3a98010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186686_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="323,-434 337,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3352a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-434 241,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28277@1" ObjectIDZND0="28278@x" ObjectIDZND1="g_34e20c0@0" ObjectIDZND2="g_3684990@0" Pin0InfoVect0LinkObjId="SW-186687_0" Pin0InfoVect1LinkObjId="g_34e20c0_0" Pin0InfoVect2LinkObjId="g_3684990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186686_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="287,-434 241,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a99850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-458 241,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28278@0" ObjectIDZND0="28277@x" ObjectIDZND1="g_34e20c0@0" ObjectIDZND2="g_3684990@0" Pin0InfoVect0LinkObjId="SW-186686_0" Pin0InfoVect1LinkObjId="g_34e20c0_0" Pin0InfoVect2LinkObjId="g_3684990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186687_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="241,-458 241,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_334fac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-434 241,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28277@x" ObjectIDND1="28278@x" ObjectIDND2="g_3684990@0" ObjectIDZND0="g_34e20c0@1" Pin0InfoVect0LinkObjId="g_34e20c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186686_0" Pin1InfoVect1LinkObjId="SW-186687_0" Pin1InfoVect2LinkObjId="g_3684990_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-434 241,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34fea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-382 10,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_366bf40@0" ObjectIDZND0="28299@1" Pin0InfoVect0LinkObjId="SW-186713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_366bf40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="10,-382 10,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a96860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="84,-435 98,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28297@0" ObjectIDZND0="g_3766630@0" Pin0InfoVect0LinkObjId="g_3766630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="84,-435 98,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c208d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="87,-313 101,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_33a8ef0@0" Pin0InfoVect0LinkObjId="g_33a8ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="87,-313 101,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c20b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="51,-313 10,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="28299@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-186713_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="51,-313 10,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c20d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-323 10,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="28299@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="10,-323 10,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c20ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-313 10,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="28299@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-186713_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="10,-313 10,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36898f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-816 870,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="28269@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186634_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-816 870,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3750200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="60,-926 60,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="28267@0" ObjectIDZND0="g_368a5d0@0" Pin0InfoVect0LinkObjId="g_368a5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="60,-926 60,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3751bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-882 441,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3751190@0" ObjectIDZND0="28302@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3751190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-882 441,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36318d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="511,-947 441,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3751e10@0" ObjectIDZND0="g_3751190@0" ObjectIDZND1="28272@x" Pin0InfoVect0LinkObjId="g_3751190_0" Pin0InfoVect1LinkObjId="SW-186641_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3751e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="511,-947 441,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36323a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-947 441,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3751e10@0" ObjectIDND1="28272@x" ObjectIDZND0="g_3751190@1" Pin0InfoVect0LinkObjId="g_3751190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3751e10_0" Pin1InfoVect1LinkObjId="SW-186641_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-947 441,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3668450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-784 440,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="28302@0" ObjectIDZND0="28273@1" Pin0InfoVect0LinkObjId="SW-186644_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3751bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-784 440,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36686b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-729 440,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28273@0" ObjectIDZND0="28275@1" Pin0InfoVect0LinkObjId="SW-186646_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186644_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-729 440,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_367d3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-658 440,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28274@1" ObjectIDZND0="28275@0" Pin0InfoVect0LinkObjId="SW-186646_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186645_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-658 440,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3684270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-527 10,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28296@0" ObjectIDZND0="28298@1" Pin0InfoVect0LinkObjId="SW-186712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="10,-527 10,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36844d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-435 48,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_366bf40@1" ObjectIDZND0="28297@1" Pin0InfoVect0LinkObjId="SW-186711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_366bf40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="10,-435 48,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3684730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-473 10,-435 -42,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="28298@0" ObjectIDZND0="g_367fe50@0" Pin0InfoVect0LinkObjId="g_367fe50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="10,-473 10,-435 -42,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3755740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="184,-434 242,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_3684990@0" ObjectIDZND0="28277@x" ObjectIDZND1="28278@x" ObjectIDZND2="g_34e20c0@0" Pin0InfoVect0LinkObjId="SW-186686_0" Pin0InfoVect1LinkObjId="SW-186687_0" Pin0InfoVect2LinkObjId="g_34e20c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3684990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="184,-434 242,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37559a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="240,-514 240,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28276@0" ObjectIDZND0="28278@1" Pin0InfoVect0LinkObjId="SW-186687_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186685_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="240,-514 240,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c257d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-145,-435 -131,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28301@0" ObjectIDZND0="g_3c24d40@0" Pin0InfoVect0LinkObjId="g_3c24d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186715_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-145,-435 -131,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3650970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-181,-435 -212,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28301@1" ObjectIDZND0="28300@x" ObjectIDZND1="g_3c25a30@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-186714_0" Pin0InfoVect1LinkObjId="g_3c25a30_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186715_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-181,-435 -212,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3651680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-211,-485 -211,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28300@0" ObjectIDZND0="28301@x" ObjectIDZND1="g_3c25a30@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-186715_0" Pin0InfoVect1LinkObjId="g_3c25a30_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186714_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-211,-485 -211,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36518e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-211,-435 -211,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28301@x" ObjectIDND1="28300@x" ObjectIDND2="g_3c25a30@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186715_0" Pin1InfoVect1LinkObjId="SW-186714_0" Pin1InfoVect2LinkObjId="g_3c25a30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-211,-435 -211,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3651b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-211,-435 -253,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28301@x" ObjectIDND1="28300@x" ObjectIDND2="0@x" ObjectIDZND0="g_3c25a30@0" Pin0InfoVect0LinkObjId="g_3c25a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186715_0" Pin1InfoVect1LinkObjId="SW-186714_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-211,-435 -253,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3651da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-211,-347 -211,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3756490@0" Pin0InfoVect0LinkObjId="g_3756490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-211,-347 -211,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-367 502,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3652000@0" ObjectIDZND0="28283@1" Pin0InfoVect0LinkObjId="SW-186693_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3652000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="502,-367 502,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365f190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-277 502,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3652a20@0" ObjectIDZND0="28283@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-186693_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3652a20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="571,-277 502,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-312 502,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28283@0" ObjectIDZND0="g_3652a20@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3652a20_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186693_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="502,-312 502,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365f650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-277 502,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3652a20@0" ObjectIDND1="28283@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3652a20_0" Pin1InfoVect1LinkObjId="SW-186693_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="502,-277 502,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3745ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-432 598,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28281@0" ObjectIDZND0="g_3662a60@0" Pin0InfoVect0LinkObjId="g_3662a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186691_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-432 598,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3745d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-432 502,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28281@1" ObjectIDZND0="28282@x" ObjectIDZND1="g_3652000@0" ObjectIDZND2="g_3746450@0" Pin0InfoVect0LinkObjId="SW-186692_0" Pin0InfoVect1LinkObjId="g_3652000_0" Pin0InfoVect2LinkObjId="g_3746450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186691_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="548,-432 502,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3745f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-457 502,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28282@0" ObjectIDZND0="28281@x" ObjectIDZND1="g_3652000@0" ObjectIDZND2="g_3746450@0" Pin0InfoVect0LinkObjId="SW-186691_0" Pin0InfoVect1LinkObjId="g_3652000_0" Pin0InfoVect2LinkObjId="g_3746450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186692_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="502,-457 502,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37461f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-432 502,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28281@x" ObjectIDND1="28282@x" ObjectIDND2="g_3746450@0" ObjectIDZND0="g_3652000@1" Pin0InfoVect0LinkObjId="g_3652000_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186691_0" Pin1InfoVect1LinkObjId="SW-186692_0" Pin1InfoVect2LinkObjId="g_3746450_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="502,-432 502,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_374ba10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="445,-432 503,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_3746450@0" ObjectIDZND0="28281@x" ObjectIDZND1="28282@x" ObjectIDZND2="g_3652000@0" Pin0InfoVect0LinkObjId="SW-186691_0" Pin0InfoVect1LinkObjId="SW-186692_0" Pin0InfoVect2LinkObjId="g_3652000_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3746450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="445,-432 503,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_374bc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="501,-512 501,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28280@0" ObjectIDZND0="28282@1" Pin0InfoVect0LinkObjId="SW-186692_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="501,-512 501,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343feb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-367 744,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_374bed0@0" ObjectIDZND0="28287@1" Pin0InfoVect0LinkObjId="SW-186698_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_374bed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-367 744,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3440110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-277 744,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_374c8f0@0" ObjectIDZND0="28287@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-186698_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_374c8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="813,-277 744,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3440370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-312 744,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28287@0" ObjectIDZND0="g_374c8f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_374c8f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="744,-312 744,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34405d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-277 744,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_374c8f0@0" ObjectIDND1="28287@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_374c8f0_0" Pin1InfoVect1LinkObjId="SW-186698_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-277 744,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3444470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-432 840,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28285@0" ObjectIDZND0="g_34439e0@0" Pin0InfoVect0LinkObjId="g_34439e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186696_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,-432 840,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34446d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-432 744,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28285@1" ObjectIDZND0="28286@x" ObjectIDZND1="g_374bed0@0" ObjectIDZND2="g_3654a00@0" Pin0InfoVect0LinkObjId="SW-186697_0" Pin0InfoVect1LinkObjId="g_374bed0_0" Pin0InfoVect2LinkObjId="g_3654a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186696_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="790,-432 744,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3444930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-457 744,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28286@0" ObjectIDZND0="28285@x" ObjectIDZND1="g_374bed0@0" ObjectIDZND2="g_3654a00@0" Pin0InfoVect0LinkObjId="SW-186696_0" Pin0InfoVect1LinkObjId="g_374bed0_0" Pin0InfoVect2LinkObjId="g_3654a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186697_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="744,-457 744,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3444b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-432 744,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28285@x" ObjectIDND1="28286@x" ObjectIDND2="g_3654a00@0" ObjectIDZND0="g_374bed0@1" Pin0InfoVect0LinkObjId="g_374bed0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186696_0" Pin1InfoVect1LinkObjId="SW-186697_0" Pin1InfoVect2LinkObjId="g_3654a00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-432 744,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365a000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="687,-432 745,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_3654a00@0" ObjectIDZND0="28285@x" ObjectIDZND1="28286@x" ObjectIDZND2="g_374bed0@0" Pin0InfoVect0LinkObjId="SW-186696_0" Pin0InfoVect1LinkObjId="SW-186697_0" Pin0InfoVect2LinkObjId="g_374bed0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3654a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="687,-432 745,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365a260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-512 743,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28284@0" ObjectIDZND0="28286@1" Pin0InfoVect0LinkObjId="SW-186697_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-512 743,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36710f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-367 984,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_365a4c0@0" ObjectIDZND0="28291@1" Pin0InfoVect0LinkObjId="SW-186703_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_365a4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-367 984,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3671350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1053,-277 984,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_365aee0@0" ObjectIDZND0="28291@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-186703_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_365aee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1053,-277 984,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36715b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-312 984,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28291@0" ObjectIDZND0="g_365aee0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_365aee0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186703_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="984,-312 984,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3671810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-277 984,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_365aee0@0" ObjectIDND1="28291@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_365aee0_0" Pin1InfoVect1LinkObjId="SW-186703_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-277 984,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36756b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1066,-432 1080,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28289@0" ObjectIDZND0="g_3674c20@0" Pin0InfoVect0LinkObjId="g_3674c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1066,-432 1080,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3675910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1030,-432 984,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28289@1" ObjectIDZND0="28290@x" ObjectIDZND1="g_365a4c0@0" ObjectIDZND2="g_3676030@0" Pin0InfoVect0LinkObjId="SW-186702_0" Pin0InfoVect1LinkObjId="g_365a4c0_0" Pin0InfoVect2LinkObjId="g_3676030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186701_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1030,-432 984,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3675b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-457 984,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28290@0" ObjectIDZND0="28289@x" ObjectIDZND1="g_365a4c0@0" ObjectIDZND2="g_3676030@0" Pin0InfoVect0LinkObjId="SW-186701_0" Pin0InfoVect1LinkObjId="g_365a4c0_0" Pin0InfoVect2LinkObjId="g_3676030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186702_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="984,-457 984,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3675dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-432 984,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28289@x" ObjectIDND1="28290@x" ObjectIDND2="g_3676030@0" ObjectIDZND0="g_365a4c0@1" Pin0InfoVect0LinkObjId="g_365a4c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186701_0" Pin1InfoVect1LinkObjId="SW-186702_0" Pin1InfoVect2LinkObjId="g_3676030_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-432 984,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_377ad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="927,-432 984,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_3676030@0" ObjectIDZND0="28289@x" ObjectIDZND1="28290@x" ObjectIDZND2="g_365a4c0@0" Pin0InfoVect0LinkObjId="SW-186701_0" Pin0InfoVect1LinkObjId="SW-186702_0" Pin0InfoVect2LinkObjId="g_365a4c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3676030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="927,-432 984,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_377af60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-513 984,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28288@0" ObjectIDZND0="28290@1" Pin0InfoVect0LinkObjId="SW-186702_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-513 984,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_375f8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1215,-367 1215,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_377fec0@0" ObjectIDZND0="28295@1" Pin0InfoVect0LinkObjId="SW-186708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_377fec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1215,-367 1215,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_375fb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-277 1215,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_37808e0@0" ObjectIDZND0="28295@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-186708_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37808e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-277 1215,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_375fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1215,-312 1215,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28295@0" ObjectIDZND0="g_37808e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_37808e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1215,-312 1215,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_375fff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1215,-277 1215,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="28295@x" ObjectIDND1="g_37808e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186708_0" Pin1InfoVect1LinkObjId="g_37808e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1215,-277 1215,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3763e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1297,-432 1311,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28293@0" ObjectIDZND0="g_3763400@0" Pin0InfoVect0LinkObjId="g_3763400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186706_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1297,-432 1311,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37640f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1261,-432 1215,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28293@1" ObjectIDZND0="28294@x" ObjectIDZND1="g_3764810@0" ObjectIDZND2="g_377fec0@0" Pin0InfoVect0LinkObjId="SW-186707_0" Pin0InfoVect1LinkObjId="g_3764810_0" Pin0InfoVect2LinkObjId="g_377fec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186706_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1261,-432 1215,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3764350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1215,-457 1215,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28294@0" ObjectIDZND0="28293@x" ObjectIDZND1="g_3764810@0" ObjectIDZND2="g_377fec0@0" Pin0InfoVect0LinkObjId="SW-186706_0" Pin0InfoVect1LinkObjId="g_3764810_0" Pin0InfoVect2LinkObjId="g_377fec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186707_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1215,-457 1215,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37645b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1215,-432 1215,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28294@x" ObjectIDND1="28293@x" ObjectIDND2="g_3764810@0" ObjectIDZND0="g_377fec0@1" Pin0InfoVect0LinkObjId="g_377fec0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-186707_0" Pin1InfoVect1LinkObjId="SW-186706_0" Pin1InfoVect2LinkObjId="g_3764810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1215,-432 1215,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_368e930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-432 1215,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3764810@0" ObjectIDZND0="28294@x" ObjectIDZND1="28293@x" ObjectIDZND2="g_377fec0@0" Pin0InfoVect0LinkObjId="SW-186707_0" Pin0InfoVect1LinkObjId="SW-186706_0" Pin0InfoVect2LinkObjId="g_377fec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3764810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-432 1215,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_368eb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1215,-513 1215,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28292@0" ObjectIDZND0="28294@1" Pin0InfoVect0LinkObjId="SW-186707_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1215,-513 1215,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3690130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-211,-521 -211,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28300@1" ObjectIDZND0="28263@0" Pin0InfoVect0LinkObjId="g_3690390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186714_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-211,-521 -211,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3690390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-563 10,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28296@1" ObjectIDZND0="28263@0" Pin0InfoVect0LinkObjId="g_3690130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="10,-563 10,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36905f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="240,-550 240,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28276@1" ObjectIDZND0="28263@0" Pin0InfoVect0LinkObjId="g_3690130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186685_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="240,-550 240,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3690850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="501,-548 501,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28280@1" ObjectIDZND0="28263@0" Pin0InfoVect0LinkObjId="g_3690130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="501,-548 501,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3690ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-548 743,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28284@1" ObjectIDZND0="28263@0" Pin0InfoVect0LinkObjId="g_3690130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186695_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-548 743,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3690d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-549 984,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28288@1" ObjectIDZND0="28263@0" Pin0InfoVect0LinkObjId="g_3690130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-549 984,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3690f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1215,-549 1215,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28292@1" ObjectIDZND0="28263@0" Pin0InfoVect0LinkObjId="g_3690130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1215,-549 1215,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36959f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-622 441,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28274@0" ObjectIDZND0="28263@0" Pin0InfoVect0LinkObjId="g_3690130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186645_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-622 441,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36960d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="442,-1184 379,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3a92370@0" ObjectIDZND0="28265@x" ObjectIDZND1="g_375ae60@0" Pin0InfoVect0LinkObjId="SW-186630_0" Pin0InfoVect1LinkObjId="g_375ae60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a92370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="442,-1184 379,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36962c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-1235 378,-1256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="28265@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="378,-1235 378,-1256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369e270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1030 441,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28270@1" ObjectIDZND0="28271@1" Pin0InfoVect0LinkObjId="SW-186641_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186639_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1030 441,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369e4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-947 441,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3751e10@0" ObjectIDND1="g_3751190@0" ObjectIDZND0="28272@0" Pin0InfoVect0LinkObjId="SW-186641_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3751e10_0" Pin1InfoVect1LinkObjId="g_3751190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-947 441,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369e730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-993 441,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28272@1" ObjectIDZND0="28270@0" Pin0InfoVect0LinkObjId="SW-186639_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186641_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-993 441,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a02f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="60,-951 60,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3750460@0" ObjectIDND1="g_36a1280@0" ObjectIDZND0="28267@1" Pin0InfoVect0LinkObjId="SW-186631_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3750460_0" Pin1InfoVect1LinkObjId="g_36a1280_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="60,-951 60,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a04e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="26,-946 26,-951 60,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3750460@0" ObjectIDZND0="28267@x" ObjectIDZND1="g_36a1280@0" Pin0InfoVect0LinkObjId="SW-186631_0" Pin0InfoVect1LinkObjId="g_36a1280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3750460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="26,-946 26,-951 60,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a0dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-917 870,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28268@1" ObjectIDZND0="g_36a06d0@0" Pin0InfoVect0LinkObjId="g_36a06d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186634_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-917 870,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a1020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-856 870,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_36a06d0@1" ObjectIDZND0="28269@1" Pin0InfoVect0LinkObjId="SW-186634_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36a06d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-856 870,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a1c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="60,-1027 60,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28266@1" ObjectIDZND0="g_36a1280@0" Pin0InfoVect0LinkObjId="g_36a1280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186631_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="60,-1027 60,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a1eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="60,-970 60,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_36a1280@1" ObjectIDZND0="28267@x" ObjectIDZND1="g_3750460@0" Pin0InfoVect0LinkObjId="SW-186631_0" Pin0InfoVect1LinkObjId="g_3750460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36a1280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="60,-970 60,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36af590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="60,-1045 60,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28266@0" ObjectIDZND0="28262@0" Pin0InfoVect0LinkObjId="g_36af7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="60,-1045 60,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36af7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-1042 870,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_3689b50@1" ObjectIDZND0="28262@0" Pin0InfoVect0LinkObjId="g_36af590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3689b50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-1042 870,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36af9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1058 441,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28271@0" ObjectIDZND0="28262@0" Pin0InfoVect0LinkObjId="g_36af590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186641_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1058 441,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b0c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-1117 378,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_375ae60@0" ObjectIDZND0="28262@0" Pin0InfoVect0LinkObjId="g_36af590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_375ae60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-1117 378,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b0ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-963 870,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3689b50@0" ObjectIDZND0="28268@0" Pin0InfoVect0LinkObjId="SW-186634_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3689b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-963 870,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b2710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-1199 378,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28265@0" ObjectIDZND0="g_3a92370@0" ObjectIDZND1="g_375ae60@0" Pin0InfoVect0LinkObjId="g_3a92370_0" Pin0InfoVect1LinkObjId="g_375ae60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="378,-1199 378,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b2900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-1184 378,-1170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3a92370@0" ObjectIDND1="28265@x" ObjectIDZND0="g_375ae60@1" Pin0InfoVect0LinkObjId="g_375ae60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a92370_0" Pin1InfoVect1LinkObjId="SW-186630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-1184 378,-1170 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="28263" cx="-211" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28263" cx="10" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28263" cx="240" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28263" cx="501" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28263" cx="743" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28263" cx="984" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28263" cx="1215" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28263" cx="441" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28262" cx="60" cy="-1098" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28262" cx="870" cy="-1098" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28262" cx="441" cy="-1098" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28262" cx="378" cy="-1098" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-186455" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -436.000000 -1127.000000)" xlink:href="#dynamicPoint:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28239" ObjectName="DYN-CX_TG"/>
     <cge:Meas_Ref ObjectId="186455"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_3abe9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -574.000000 -1223.500000) translate(0,16)">提灌变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34e2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34e2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34e2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34e2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34e2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34e2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34e2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34e2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34e2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33afaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a94c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 186.000000 -161.000000) translate(0,12)">10kV1、2号泵站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3679210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -255.000000 -161.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3679d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -264.000000 -628.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a7690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 203.000000 -1163.000000) translate(0,12)">35kV永物T线提灌支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36911d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -27.000000 -159.000000) translate(0,12)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3694640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1166.000000 -163.000000) translate(0,12)">10kV3、4号泵站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3694c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 476.000000 -162.000000) translate(0,12)">备用1线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3695560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.000000 -164.000000) translate(0,12)">备用2线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36957b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 962.000000 -162.000000) translate(0,12)">备用3线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_369e990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 -1024.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_369efc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 384.000000 -1223.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_369f370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 72.000000 -995.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a2110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 883.000000 -876.000000) translate(0,12)">3501</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a2740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 449.000000 -701.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a2980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 447.000000 -754.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a2bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -647.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a2e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -205.000000 -510.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a3040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -183.000000 -461.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a33d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 19.000000 -494.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a38c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 57.000000 -461.000000) translate(0,12)">06160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a3b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -552.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -348.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a3f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -539.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a41c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 249.000000 -479.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a4400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 296.000000 -460.000000) translate(0,12)">06260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a4640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 248.000000 -339.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a4880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 508.000000 -537.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a4ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 510.000000 -477.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a4d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 557.000000 -458.000000) translate(0,12)">06360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a4f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.000000 -337.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a5180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 752.000000 -477.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a53c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 750.000000 -537.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a5600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 799.000000 -458.000000) translate(0,12)">06460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a5840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -337.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a5a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -538.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a5cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 993.000000 -478.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a5f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1039.000000 -458.000000) translate(0,12)">06560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a6140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -337.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a6380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -538.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1224.000000 -478.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a6800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1270.000000 -458.000000) translate(0,12)">06660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a6a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -337.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36acaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 272.000000 -808.000000) translate(0,12)">SZ11-5000/35GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36acaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 272.000000 -808.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36acaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 272.000000 -808.000000) translate(0,42)">Uk=7%    Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b1700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -67.000000 -1129.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b1bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 842.000000 -689.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b2b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -830.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_36b3e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -699.000000 -837.000000) translate(0,14)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_36b53a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -386.000000 -1202.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_36b6270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -386.000000 -1239.000000) translate(0,16)">主网返回</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-186646">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -672.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28275" ObjectName="SW-CX_TG.CX_TG_001BK"/>
     <cge:Meas_Ref ObjectId="186646"/>
    <cge:TPSR_Ref TObjectID="28275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186712">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1.000000 -465.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28298" ObjectName="SW-YM_TG.YM_TG_061BK"/>
     <cge:Meas_Ref ObjectId="186712"/>
    <cge:TPSR_Ref TObjectID="28298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 231.000000 -449.913793)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28278" ObjectName="SW-YM_TG.YM_TG_062BK"/>
     <cge:Meas_Ref ObjectId="186687"/>
    <cge:TPSR_Ref TObjectID="28278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186692">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 492.000000 -447.913793)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28282" ObjectName="SW-YM_TG.YM_TG_063BK"/>
     <cge:Meas_Ref ObjectId="186692"/>
    <cge:TPSR_Ref TObjectID="28282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186697">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 -447.913793)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28286" ObjectName="SW-YM_TG.YM_TG_064BK"/>
     <cge:Meas_Ref ObjectId="186697"/>
    <cge:TPSR_Ref TObjectID="28286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186702">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 975.000000 -448.913793)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28290" ObjectName="SW-YM_TG.YM_TG_065BK"/>
     <cge:Meas_Ref ObjectId="186702"/>
    <cge:TPSR_Ref TObjectID="28290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186707">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1206.000000 -448.913793)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28294" ObjectName="SW-YM_TG.YM_TG_066BK"/>
     <cge:Meas_Ref ObjectId="186707"/>
    <cge:TPSR_Ref TObjectID="28294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186639">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 -995.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28270" ObjectName="SW-YM_TG.YM_TG_301BK"/>
     <cge:Meas_Ref ObjectId="186639"/>
    <cge:TPSR_Ref TObjectID="28270"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.981481 -0.000000 0.000000 -0.885714 -18.000000 -213.000000)" xlink:href="#capacitor:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3a92370">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 435.398535 -1130.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e20c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 236.000000 -364.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33502c0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 302.559082 -224.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_366bf40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 -377.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_375ae60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -1112.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3689b50">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 861.000000 -1047.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3750460">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 19.398535 -892.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3751190">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 436.000000 -877.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3751e10">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 504.398535 -893.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_367fe50">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -48.601465 -381.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3684990">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 177.398535 -380.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c25a30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -259.601465 -381.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3652000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 497.000000 -362.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3652a20">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 563.559082 -223.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3746450">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 438.398535 -378.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_374bed0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 -362.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_374c8f0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 805.559082 -223.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3654a00">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 680.398535 -378.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_365a4c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 979.000000 -362.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_365aee0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1045.559082 -223.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3676030">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 920.398535 -378.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_377fec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1210.000000 -362.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37808e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1276.559082 -223.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3764810">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1151.398535 -378.086207)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a06d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.000000 -851.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a1280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 51.000000 -965.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-186554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -1041.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28270"/>
     <cge:Term_Ref ObjectID="40120"/>
    <cge:TPSR_Ref TObjectID="28270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -1041.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28270"/>
     <cge:Term_Ref ObjectID="40120"/>
    <cge:TPSR_Ref TObjectID="28270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -1041.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28270"/>
     <cge:Term_Ref ObjectID="40120"/>
    <cge:TPSR_Ref TObjectID="28270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 579.000000 -711.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28275"/>
     <cge:Term_Ref ObjectID="40130"/>
    <cge:TPSR_Ref TObjectID="28275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 579.000000 -711.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28275"/>
     <cge:Term_Ref ObjectID="40130"/>
    <cge:TPSR_Ref TObjectID="28275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 579.000000 -711.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28275"/>
     <cge:Term_Ref ObjectID="40130"/>
    <cge:TPSR_Ref TObjectID="28275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6.000000 -130.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28298"/>
     <cge:Term_Ref ObjectID="40176"/>
    <cge:TPSR_Ref TObjectID="28298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-186597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6.000000 -130.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28298"/>
     <cge:Term_Ref ObjectID="40176"/>
    <cge:TPSR_Ref TObjectID="28298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6.000000 -130.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28298"/>
     <cge:Term_Ref ObjectID="40176"/>
    <cge:TPSR_Ref TObjectID="28298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 221.000000 -131.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28278"/>
     <cge:Term_Ref ObjectID="40136"/>
    <cge:TPSR_Ref TObjectID="28278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 221.000000 -131.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28278"/>
     <cge:Term_Ref ObjectID="40136"/>
    <cge:TPSR_Ref TObjectID="28278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 221.000000 -131.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28278"/>
     <cge:Term_Ref ObjectID="40136"/>
    <cge:TPSR_Ref TObjectID="28278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28282"/>
     <cge:Term_Ref ObjectID="40144"/>
    <cge:TPSR_Ref TObjectID="28282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28282"/>
     <cge:Term_Ref ObjectID="40144"/>
    <cge:TPSR_Ref TObjectID="28282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28282"/>
     <cge:Term_Ref ObjectID="40144"/>
    <cge:TPSR_Ref TObjectID="28282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 -131.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28286"/>
     <cge:Term_Ref ObjectID="40152"/>
    <cge:TPSR_Ref TObjectID="28286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 -131.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28286"/>
     <cge:Term_Ref ObjectID="40152"/>
    <cge:TPSR_Ref TObjectID="28286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 -131.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28286"/>
     <cge:Term_Ref ObjectID="40152"/>
    <cge:TPSR_Ref TObjectID="28286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 -130.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28290"/>
     <cge:Term_Ref ObjectID="40160"/>
    <cge:TPSR_Ref TObjectID="28290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 -130.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28290"/>
     <cge:Term_Ref ObjectID="40160"/>
    <cge:TPSR_Ref TObjectID="28290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 -130.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28290"/>
     <cge:Term_Ref ObjectID="40160"/>
    <cge:TPSR_Ref TObjectID="28290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186591" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -131.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28294"/>
     <cge:Term_Ref ObjectID="40168"/>
    <cge:TPSR_Ref TObjectID="28294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -131.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28294"/>
     <cge:Term_Ref ObjectID="40168"/>
    <cge:TPSR_Ref TObjectID="28294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -131.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28294"/>
     <cge:Term_Ref ObjectID="40168"/>
    <cge:TPSR_Ref TObjectID="28294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-186606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -113.000000 -677.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28263"/>
     <cge:Term_Ref ObjectID="40107"/>
    <cge:TPSR_Ref TObjectID="28263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-186607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -113.000000 -677.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28263"/>
     <cge:Term_Ref ObjectID="40107"/>
    <cge:TPSR_Ref TObjectID="28263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-186608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -113.000000 -677.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28263"/>
     <cge:Term_Ref ObjectID="40107"/>
    <cge:TPSR_Ref TObjectID="28263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-186609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -113.000000 -677.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28263"/>
     <cge:Term_Ref ObjectID="40107"/>
    <cge:TPSR_Ref TObjectID="28263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-186610" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -113.000000 -677.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28263"/>
     <cge:Term_Ref ObjectID="40107"/>
    <cge:TPSR_Ref TObjectID="28263"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-615" y="-1232"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="112" qtmmishow="hidden" width="123" x="-720" y="-1270"/></g>
   <g href="35kV提灌变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="340" y="-830"/></g>
   <g href="35kV提灌变10kV1号电容器061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="19" y="-494"/></g>
   <g href="35kV提灌变10kV1、2号泵站线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="249" y="-479"/></g>
   <g href="35kV提灌变10kV备用1线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="510" y="-477"/></g>
   <g href="35kV提灌变10kV备用2线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="752" y="-477"/></g>
   <g href="35kV提灌变10kV备用3线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="993" y="-478"/></g>
   <g href="35kV提灌变10kV3、4号泵站线066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1224" y="-478"/></g>
   <g href="35kV提灌变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="68" x="-700" y="-838"/></g>
   <g href="cx_配调_配网接线图35_元谋.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-397" y="-1209"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-397" y="-1248"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34fd3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -365.000000 130.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34edbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -376.000000 115.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3503170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -351.000000 100.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a6e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 1040.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a7120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.000000 1025.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a7360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.000000 1010.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a7780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 518.000000 710.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a7a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 507.000000 695.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a7c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 532.000000 680.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ab4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -177.000000 616.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36abde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.000000 646.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ac3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.000000 661.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ac620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.000000 677.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ac860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -170.000000 631.250000) translate(0,12)">U0(kV):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YM_TG.YM_TG_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="40186"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.000000 -779.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.000000 -779.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28302" ObjectName="TF-YM_TG.YM_TG_1T"/>
    <cge:TPSR_Ref TObjectID="28302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 -705.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 -705.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-615" y="-1232"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-615" y="-1232"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="112" qtmmishow="hidden" width="123" x="-720" y="-1270"/>
    </a>
   <metadata/><rect fill="white" height="112" opacity="0" stroke="white" transform="" width="123" x="-720" y="-1270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="340" y="-830"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="340" y="-830"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="19" y="-494"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="19" y="-494"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="249" y="-479"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="249" y="-479"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="510" y="-477"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="510" y="-477"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="752" y="-477"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="752" y="-477"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="993" y="-478"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="993" y="-478"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1224" y="-478"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1224" y="-478"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="68" x="-700" y="-838"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="68" x="-700" y="-838"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-397" y="-1209"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-397" y="-1209"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-397" y="-1248"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-397" y="-1248"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_368a5d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 37.000000 -865.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3756490">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -236.000000 -269.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -625.000000 -1158.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-186554" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -591.000000 -1099.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186554" ObjectName="YM_TG:YM_TG_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-186554" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -1060.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186554" ObjectName="YM_TG:YM_TG_301BK_P"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YM_TG"/>
</svg>