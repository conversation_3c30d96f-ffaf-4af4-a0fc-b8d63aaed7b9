<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-331" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -957 2110 1295">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape99">
    <polyline DF8003:Layer="PUBLIC" points="25,29 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="8" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="41" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="46" y2="46"/>
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.5"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="voltageTransformer:shape135">
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="52"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d1e5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b83970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d20e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d21a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d22c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d23760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d24320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d24e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a1def0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a1def0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d28860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d28860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d2a5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d2a5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d2b520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d2d150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d2dde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d2ec50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d2f3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d30c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d31880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d32140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d32900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d339e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d34360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d34e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d35810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d36e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d37850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d389f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d39680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d47a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d3fbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d41360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d3b870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1305" width="2120" x="-335" y="-962"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="562" x2="698" y1="336" y2="336"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-307002">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 343.000000 -242.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47591" ObjectName="SW-CX_WSC.CX_WSC_361BK"/>
     <cge:Meas_Ref ObjectId="307002"/>
    <cge:TPSR_Ref TObjectID="47591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307012">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 620.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47599" ObjectName="SW-CX_WSC.CX_WSC_362BK"/>
     <cge:Meas_Ref ObjectId="307012"/>
    <cge:TPSR_Ref TObjectID="47599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307007">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 906.000000 -245.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47595" ObjectName="SW-CX_WSC.CX_WSC_363BK"/>
     <cge:Meas_Ref ObjectId="307007"/>
    <cge:TPSR_Ref TObjectID="47595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307017">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1220.000000 -244.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47603" ObjectName="SW-CX_WSC.CX_WSC_364BK"/>
     <cge:Meas_Ref ObjectId="307017"/>
    <cge:TPSR_Ref TObjectID="47603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306988">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 -388.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47583" ObjectName="SW-CX_WSC.CX_WSC_301BK"/>
     <cge:Meas_Ref ObjectId="306988"/>
    <cge:TPSR_Ref TObjectID="47583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1536.000000 -243.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47587" ObjectName="SW-CX_WSC.CX_WSC_365BK"/>
     <cge:Meas_Ref ObjectId="306997"/>
    <cge:TPSR_Ref TObjectID="47587"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b80b60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 716.611650 -568.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b778c0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1361.000000 -582.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_WSC.CX_WSC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,-330 1780,-330 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47581" ObjectName="BS-CX_WSC.CX_WSC_3IM"/>
    <cge:TPSR_Ref TObjectID="47581"/></metadata>
   <polyline fill="none" opacity="0" points="187,-330 1780,-330 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.000000 -33.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -32.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 -36.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1225.000000 -35.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29519b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.611650 -492.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27f2f20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 388.000000 -109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a8ad20">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 689.611650 -545.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a5c050">
    <use class="BV-35KV" transform="matrix(0.326531 -0.000000 0.000000 -0.310345 772.611650 -520.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b29710">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 -108.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ba13d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 -112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aabce0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1265.000000 -111.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b294b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 -582.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b04460">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1367.000000 -494.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5a310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1358.000000 -744.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9af00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1581.000000 -110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-306930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -433.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47581"/>
     <cge:Term_Ref ObjectID="45210"/>
    <cge:TPSR_Ref TObjectID="47581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-306931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -433.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47581"/>
     <cge:Term_Ref ObjectID="45210"/>
    <cge:TPSR_Ref TObjectID="47581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-306932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -433.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47581"/>
     <cge:Term_Ref ObjectID="45210"/>
    <cge:TPSR_Ref TObjectID="47581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-306933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -433.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47581"/>
     <cge:Term_Ref ObjectID="45210"/>
    <cge:TPSR_Ref TObjectID="47581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-306936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -433.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47581"/>
     <cge:Term_Ref ObjectID="45210"/>
    <cge:TPSR_Ref TObjectID="47581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -458.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47583"/>
     <cge:Term_Ref ObjectID="45213"/>
    <cge:TPSR_Ref TObjectID="47583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -458.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47583"/>
     <cge:Term_Ref ObjectID="45213"/>
    <cge:TPSR_Ref TObjectID="47583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -458.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47583"/>
     <cge:Term_Ref ObjectID="45213"/>
    <cge:TPSR_Ref TObjectID="47583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 28.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47591"/>
     <cge:Term_Ref ObjectID="45229"/>
    <cge:TPSR_Ref TObjectID="47591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 28.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47591"/>
     <cge:Term_Ref ObjectID="45229"/>
    <cge:TPSR_Ref TObjectID="47591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 28.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47591"/>
     <cge:Term_Ref ObjectID="45229"/>
    <cge:TPSR_Ref TObjectID="47591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306961" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 27.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306961" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47599"/>
     <cge:Term_Ref ObjectID="45245"/>
    <cge:TPSR_Ref TObjectID="47599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306962" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 27.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47599"/>
     <cge:Term_Ref ObjectID="45245"/>
    <cge:TPSR_Ref TObjectID="47599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306963" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 27.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306963" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47599"/>
     <cge:Term_Ref ObjectID="45245"/>
    <cge:TPSR_Ref TObjectID="47599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306949" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 32.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47595"/>
     <cge:Term_Ref ObjectID="45237"/>
    <cge:TPSR_Ref TObjectID="47595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 32.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47595"/>
     <cge:Term_Ref ObjectID="45237"/>
    <cge:TPSR_Ref TObjectID="47595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 32.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47595"/>
     <cge:Term_Ref ObjectID="45237"/>
    <cge:TPSR_Ref TObjectID="47595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1224.000000 41.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47603"/>
     <cge:Term_Ref ObjectID="45253"/>
    <cge:TPSR_Ref TObjectID="47603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1224.000000 41.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47603"/>
     <cge:Term_Ref ObjectID="45253"/>
    <cge:TPSR_Ref TObjectID="47603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1224.000000 41.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47603"/>
     <cge:Term_Ref ObjectID="45253"/>
    <cge:TPSR_Ref TObjectID="47603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1566.000000 38.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47587"/>
     <cge:Term_Ref ObjectID="45221"/>
    <cge:TPSR_Ref TObjectID="47587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1566.000000 38.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47587"/>
     <cge:Term_Ref ObjectID="45221"/>
    <cge:TPSR_Ref TObjectID="47587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1566.000000 38.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47587"/>
     <cge:Term_Ref ObjectID="45221"/>
    <cge:TPSR_Ref TObjectID="47587"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="-227" y="-940"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-122 359,-135 347,-135 353,-122 353,-123 353,-122 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-98 359,-85 347,-85 353,-98 353,-97 353,-98 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="630,-121 636,-134 624,-134 630,-121 630,-122 630,-121 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="630,-97 636,-84 624,-84 630,-97 630,-96 630,-97 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-125 922,-138 910,-138 916,-125 916,-126 916,-125 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-101 922,-88 910,-88 916,-101 916,-100 916,-101 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-124 1236,-137 1224,-137 1230,-124 1230,-125 1230,-124 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-100 1236,-87 1224,-87 1230,-100 1230,-99 1230,-100 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-123 1552,-136 1540,-136 1546,-123 1546,-124 1546,-123 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-99 1552,-86 1540,-86 1546,-99 1546,-98 1546,-99 " stroke="rgb(255,255,0)"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_WSC.CX_WSC_P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1541.000000 -34.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48152" ObjectName="SM-CX_WSC.CX_WSC_P1"/>
    <cge:TPSR_Ref TObjectID="48152"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b7f630" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 436.500000 -105.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a5cb10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 774.611650 -545.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b00c00" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 713.500000 -104.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b91490" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 999.500000 -108.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b3ce10" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1313.500000 -107.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ab12c0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1629.500000 -106.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="47581" cx="736" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47581" cx="352" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47581" cx="629" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47581" cx="915" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47581" cx="1229" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47581" cx="1334" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47581" cx="1545" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bbd9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：13508789288</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28d2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28d2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28d2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28d2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28d2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28d2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28d2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2bb8590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">王三冲开关站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aaabb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 778.500000 -607.000000) translate(0,15)">35kV母线PT柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b4b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -22.000000) translate(0,15)">储能进线柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b95dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -268.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a5bea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -350.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b2a0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -21.000000) translate(0,15)">35kV站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a51d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 586.000000 -272.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 898.000000 -27.000000) translate(0,15)">SVG进线柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba1d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 898.000000 -27.000000) translate(0,33)">3Mvar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b92640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -272.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aac850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -24.000000) translate(0,15)">接地变柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3dfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -270.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa4c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -414.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b5bd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.500000 -887.000000) translate(0,15)">35kV马三线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b774f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.500000 -855.000000) translate(0,15)">C  B  A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b79270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 748.000000 -411.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b85c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1341.000000 -715.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9a2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 442.000000 -161.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9a480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -160.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9a840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1005.000000 -164.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9aa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1319.000000 -163.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a9ba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.000000 -23.000000) translate(0,15)">    1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a9ba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.000000 -23.000000) translate(0,33)">(1-7号方阵 23.08MWp)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab2600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 -269.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab5530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1635.000000 -162.000000) translate(0,12)">36567</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab5e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1426.000000 459.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab61c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1415.000000 444.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab68f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1440.000000 429.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab7350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 300.000000 -24.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab7650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 289.000000 -39.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab7890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.000000 -54.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab7cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -24.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab7f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -39.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab81b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 596.000000 -54.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab85d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 877.000000 -33.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab8890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 -48.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a83ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 -63.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a83ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1164.000000 -42.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a84190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.000000 -57.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a843d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1178.000000 -72.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a847f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1500.000000 -36.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a84ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.000000 -51.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a84cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1514.000000 -66.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a85020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 313.000000 375.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a85bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.000000 406.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a86430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.000000 421.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a86990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.000000 435.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a86c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 390.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_MSP" endPointId="0" endStationName="CX_WSC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_MaSan" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1334,-770 1334,-826 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47735" ObjectName="AC-35kV.LN_MaSan"/>
    <cge:TPSR_Ref TObjectID="47735_SS-331"/></metadata>
   <polyline fill="none" opacity="0" points="1334,-770 1334,-826 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-306896" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -25.000000 -837.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47572" ObjectName="DYN-CX_WSC"/>
     <cge:Meas_Ref ObjectId="306896"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-307003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 342.000000 -286.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47592" ObjectName="SW-CX_WSC.CX_WSC_361XC"/>
     <cge:Meas_Ref ObjectId="307003"/>
    <cge:TPSR_Ref TObjectID="47592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 426.000000 -131.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47594" ObjectName="SW-CX_WSC.CX_WSC_36167SW"/>
     <cge:Meas_Ref ObjectId="307004"/>
    <cge:TPSR_Ref TObjectID="47594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307025">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.611650 -364.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47608" ObjectName="SW-CX_WSC.CX_WSC_3901XC1"/>
     <cge:Meas_Ref ObjectId="307025"/>
    <cge:TPSR_Ref TObjectID="47608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 342.772152 -201.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47593" ObjectName="SW-CX_WSC.CX_WSC_361XC1"/>
     <cge:Meas_Ref ObjectId="307003"/>
    <cge:TPSR_Ref TObjectID="47593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307013">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 619.000000 -286.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47600" ObjectName="SW-CX_WSC.CX_WSC_362XC"/>
     <cge:Meas_Ref ObjectId="307013"/>
    <cge:TPSR_Ref TObjectID="47600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307014">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 703.000000 -130.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47602" ObjectName="SW-CX_WSC.CX_WSC_36267SW"/>
     <cge:Meas_Ref ObjectId="307014"/>
    <cge:TPSR_Ref TObjectID="47602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307013">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 619.772152 -200.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47601" ObjectName="SW-CX_WSC.CX_WSC_362XC1"/>
     <cge:Meas_Ref ObjectId="307013"/>
    <cge:TPSR_Ref TObjectID="47601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307008">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 -290.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47596" ObjectName="SW-CX_WSC.CX_WSC_363XC"/>
     <cge:Meas_Ref ObjectId="307008"/>
    <cge:TPSR_Ref TObjectID="47596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307009">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 989.000000 -134.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47598" ObjectName="SW-CX_WSC.CX_WSC_36367SW"/>
     <cge:Meas_Ref ObjectId="307009"/>
    <cge:TPSR_Ref TObjectID="47598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307008">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.772152 -204.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47597" ObjectName="SW-CX_WSC.CX_WSC_363XC1"/>
     <cge:Meas_Ref ObjectId="307008"/>
    <cge:TPSR_Ref TObjectID="47597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307018">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 -289.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47604" ObjectName="SW-CX_WSC.CX_WSC_364XC"/>
     <cge:Meas_Ref ObjectId="307018"/>
    <cge:TPSR_Ref TObjectID="47604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307019">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1303.000000 -133.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47606" ObjectName="SW-CX_WSC.CX_WSC_36467SW"/>
     <cge:Meas_Ref ObjectId="307019"/>
    <cge:TPSR_Ref TObjectID="47606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307018">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.772152 -203.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47605" ObjectName="SW-CX_WSC.CX_WSC_364XC1"/>
     <cge:Meas_Ref ObjectId="307018"/>
    <cge:TPSR_Ref TObjectID="47605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306989">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1324.000000 -433.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47585" ObjectName="SW-CX_WSC.CX_WSC_301XC1"/>
     <cge:Meas_Ref ObjectId="306989"/>
    <cge:TPSR_Ref TObjectID="47585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306989">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1323.772152 -344.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47584" ObjectName="SW-CX_WSC.CX_WSC_301XC"/>
     <cge:Meas_Ref ObjectId="306989"/>
    <cge:TPSR_Ref TObjectID="47584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306990">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 -685.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47586" ObjectName="SW-CX_WSC.CX_WSC_3016SW"/>
     <cge:Meas_Ref ObjectId="306990"/>
    <cge:TPSR_Ref TObjectID="47586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1535.000000 -288.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47588" ObjectName="SW-CX_WSC.CX_WSC_365XC"/>
     <cge:Meas_Ref ObjectId="306998"/>
    <cge:TPSR_Ref TObjectID="47588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306999">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1619.000000 -132.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47590" ObjectName="SW-CX_WSC.CX_WSC_36567SW"/>
     <cge:Meas_Ref ObjectId="306999"/>
    <cge:TPSR_Ref TObjectID="47590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1535.772152 -202.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47589" ObjectName="SW-CX_WSC.CX_WSC_365XC1"/>
     <cge:Meas_Ref ObjectId="306998"/>
    <cge:TPSR_Ref TObjectID="47589"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2a8ab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-250 352,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47591@0" ObjectIDZND0="47593@1" Pin0InfoVect0LinkObjId="SW-307003_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="352,-250 352,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b84120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-208 353,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="47593@0" ObjectIDZND0="47594@x" ObjectIDZND1="g_27f2f20@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-307004_0" Pin0InfoVect1LinkObjId="g_27f2f20_0" Pin0InfoVect2LinkObjId="g_2b80b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="353,-208 353,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b84310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-185 353,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="47594@x" ObjectIDND1="g_27f2f20@0" ObjectIDND2="47593@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2b80b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307004_0" Pin1InfoVect1LinkObjId="g_27f2f20_0" Pin1InfoVect2LinkObjId="SW-307003_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="353,-185 353,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b7fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,-185 431,-185 431,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_27f2f20@0" ObjectIDND1="47593@x" ObjectIDND2="0@x" ObjectIDZND0="47594@1" Pin0InfoVect0LinkObjId="SW-307004_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27f2f20_0" Pin1InfoVect1LinkObjId="SW-307003_0" Pin1InfoVect2LinkObjId="g_2b80b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,-185 431,-185 431,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b804b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,-163 395,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_27f2f20@0" ObjectIDZND0="47594@x" ObjectIDZND1="47593@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-307004_0" Pin0InfoVect1LinkObjId="SW-307003_0" Pin0InfoVect2LinkObjId="g_2b80b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27f2f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="395,-163 395,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b806a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,-185 352,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="47594@x" ObjectIDND1="g_27f2f20@0" ObjectIDZND0="47593@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307003_0" Pin0InfoVect1LinkObjId="g_2b80b60_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307004_0" Pin1InfoVect1LinkObjId="g_27f2f20_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="395,-185 352,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b80890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-136 430,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="47594@0" ObjectIDZND0="g_2b7f630@0" Pin0InfoVect0LinkObjId="g_2b7f630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="431,-136 430,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b95be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-528 736,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_29519b0@0" ObjectIDZND0="g_2b80b60@0" Pin0InfoVect0LinkObjId="g_2b80b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29519b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-528 736,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a5ae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="683,-491 683,-464 736,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2a8ad20@0" ObjectIDZND0="47608@x" ObjectIDZND1="g_29519b0@0" ObjectIDZND2="g_2a5c050@0" Pin0InfoVect0LinkObjId="SW-307025_0" Pin0InfoVect1LinkObjId="g_29519b0_0" Pin0InfoVect2LinkObjId="g_2a5c050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a8ad20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="683,-491 683,-464 736,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a5b8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-438 736,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47608@1" ObjectIDZND0="g_2a8ad20@0" ObjectIDZND1="g_29519b0@0" ObjectIDZND2="g_2a5c050@0" Pin0InfoVect0LinkObjId="g_2a8ad20_0" Pin0InfoVect1LinkObjId="g_29519b0_0" Pin0InfoVect2LinkObjId="g_2a5c050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307025_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="736,-438 736,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a5bac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-464 736,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2a8ad20@0" ObjectIDND1="47608@x" ObjectIDND2="g_2a5c050@0" ObjectIDZND0="g_29519b0@1" Pin0InfoVect0LinkObjId="g_29519b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a8ad20_0" Pin1InfoVect1LinkObjId="SW-307025_0" Pin1InfoVect2LinkObjId="g_2a5c050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-464 736,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a5bcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-464 781,-464 781,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2a8ad20@0" ObjectIDND1="47608@x" ObjectIDND2="g_29519b0@0" ObjectIDZND0="g_2a5c050@1" Pin0InfoVect0LinkObjId="g_2a5c050_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a8ad20_0" Pin1InfoVect1LinkObjId="SW-307025_0" Pin1InfoVect2LinkObjId="g_29519b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-464 781,-464 781,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a5d2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="781,-550 781,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_2a5cb10@0" ObjectIDZND0="g_2a5c050@0" Pin0InfoVect0LinkObjId="g_2a5c050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a5cb10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="781,-550 781,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b10440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-294 352,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47592@1" ObjectIDZND0="47591@1" Pin0InfoVect0LinkObjId="SW-307002_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="352,-294 352,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b12810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-369 736,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47608@0" ObjectIDZND0="47581@0" Pin0InfoVect0LinkObjId="g_2b617a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-369 736,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afed30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="629,-249 629,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47599@0" ObjectIDZND0="47601@1" Pin0InfoVect0LinkObjId="SW-307013_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307012_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="629,-249 629,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afef20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="630,-207 630,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="47601@0" ObjectIDZND0="g_2b29710@0" ObjectIDZND1="47602@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b29710_0" Pin0InfoVect1LinkObjId="SW-307014_0" Pin0InfoVect2LinkObjId="g_2b80b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307013_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="630,-207 630,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aff110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="630,-184 630,-53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="47601@x" ObjectIDND1="g_2b29710@0" ObjectIDND2="47602@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2b80b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307013_0" Pin1InfoVect1LinkObjId="g_2b29710_0" Pin1InfoVect2LinkObjId="SW-307014_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="630,-184 630,-53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b013b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="672,-184 708,-184 708,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2b29710@0" ObjectIDND1="47601@x" ObjectIDND2="0@x" ObjectIDZND0="47602@1" Pin0InfoVect0LinkObjId="SW-307014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b29710_0" Pin1InfoVect1LinkObjId="SW-307013_0" Pin1InfoVect2LinkObjId="g_2b80b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="672,-184 708,-184 708,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a51750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="672,-162 672,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2b29710@0" ObjectIDZND0="47602@x" ObjectIDZND1="47601@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-307014_0" Pin0InfoVect1LinkObjId="SW-307013_0" Pin0InfoVect2LinkObjId="g_2b80b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b29710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="672,-162 672,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a51940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="672,-184 629,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b29710@0" ObjectIDND1="47602@x" ObjectIDZND0="47601@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307013_0" Pin0InfoVect1LinkObjId="g_2b80b60_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b29710_0" Pin1InfoVect1LinkObjId="SW-307014_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="672,-184 629,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a51b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="708,-135 707,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="47602@0" ObjectIDZND0="g_2b00c00@0" Pin0InfoVect0LinkObjId="g_2b00c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="708,-135 707,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a52050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="629,-293 629,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47600@1" ObjectIDZND0="47599@1" Pin0InfoVect0LinkObjId="SW-307012_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307013_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="629,-293 629,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aafbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-254 915,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47595@0" ObjectIDZND0="47597@1" Pin0InfoVect0LinkObjId="SW-307008_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307007_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-254 915,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aafda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-211 916,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="47597@0" ObjectIDZND0="g_2ba13d0@0" ObjectIDZND1="47598@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2ba13d0_0" Pin0InfoVect1LinkObjId="SW-307009_0" Pin0InfoVect2LinkObjId="g_2b80b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="916,-211 916,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aaff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-188 916,-57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="47597@x" ObjectIDND1="g_2ba13d0@0" ObjectIDND2="47598@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2b80b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307008_0" Pin1InfoVect1LinkObjId="g_2ba13d0_0" Pin1InfoVect2LinkObjId="SW-307009_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-188 916,-57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b91dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="958,-188 994,-188 994,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2ba13d0@0" ObjectIDND1="47597@x" ObjectIDND2="0@x" ObjectIDZND0="47598@1" Pin0InfoVect0LinkObjId="SW-307009_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ba13d0_0" Pin1InfoVect1LinkObjId="SW-307008_0" Pin1InfoVect2LinkObjId="g_2b80b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="958,-188 994,-188 994,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b91fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="958,-166 958,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2ba13d0@0" ObjectIDZND0="47598@x" ObjectIDZND1="47597@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-307009_0" Pin0InfoVect1LinkObjId="SW-307008_0" Pin0InfoVect2LinkObjId="g_2b80b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ba13d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="958,-166 958,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b92200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="958,-188 915,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2ba13d0@0" ObjectIDND1="47598@x" ObjectIDZND0="47597@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307008_0" Pin0InfoVect1LinkObjId="g_2b80b60_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ba13d0_0" Pin1InfoVect1LinkObjId="SW-307009_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="958,-188 915,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b92420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="994,-139 993,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="47598@0" ObjectIDZND0="g_2b91490@0" Pin0InfoVect0LinkObjId="g_2b91490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307009_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="994,-139 993,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b92b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-297 915,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47596@1" ObjectIDZND0="47595@1" Pin0InfoVect0LinkObjId="SW-307007_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307008_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-297 915,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abb280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1229,-252 1229,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47603@0" ObjectIDZND0="47605@1" Pin0InfoVect0LinkObjId="SW-307018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307017_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1229,-252 1229,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abb4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-210 1230,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="47605@0" ObjectIDZND0="g_2aabce0@0" ObjectIDZND1="47606@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2aabce0_0" Pin0InfoVect1LinkObjId="SW-307019_0" Pin0InfoVect2LinkObjId="g_2b80b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-210 1230,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abb6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-187 1230,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="47605@x" ObjectIDND1="g_2aabce0@0" ObjectIDND2="47606@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2b80b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307018_0" Pin1InfoVect1LinkObjId="g_2aabce0_0" Pin1InfoVect2LinkObjId="SW-307019_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-187 1230,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3d740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1272,-187 1308,-187 1308,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2aabce0@0" ObjectIDND1="47605@x" ObjectIDND2="0@x" ObjectIDZND0="47606@1" Pin0InfoVect0LinkObjId="SW-307019_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2aabce0_0" Pin1InfoVect1LinkObjId="SW-307018_0" Pin1InfoVect2LinkObjId="g_2b80b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1272,-187 1308,-187 1308,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3d960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1272,-165 1272,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2aabce0@0" ObjectIDZND0="47606@x" ObjectIDZND1="47605@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-307019_0" Pin0InfoVect1LinkObjId="SW-307018_0" Pin0InfoVect2LinkObjId="g_2b80b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aabce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1272,-165 1272,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3db80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1272,-187 1229,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2aabce0@0" ObjectIDND1="47606@x" ObjectIDZND0="47605@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307018_0" Pin0InfoVect1LinkObjId="g_2b80b60_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2aabce0_0" Pin1InfoVect1LinkObjId="SW-307019_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1272,-187 1229,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3dda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1308,-138 1307,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="47606@0" ObjectIDZND0="g_2b3ce10@0" Pin0InfoVect0LinkObjId="g_2b3ce10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307019_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1308,-138 1307,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1229,-296 1229,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47604@1" ObjectIDZND0="47603@1" Pin0InfoVect0LinkObjId="SW-307017_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307018_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1229,-296 1229,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b617a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-310 352,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47592@0" ObjectIDZND0="47581@0" Pin0InfoVect0LinkObjId="g_2b12810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="352,-310 352,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b61e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="629,-310 629,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47600@0" ObjectIDZND0="47581@0" Pin0InfoVect0LinkObjId="g_2b12810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307013_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="629,-310 629,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b625f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-314 915,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47596@0" ObjectIDZND0="47581@0" Pin0InfoVect0LinkObjId="g_2b12810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-314 915,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b62dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1229,-313 1229,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47604@0" ObjectIDZND0="47581@0" Pin0InfoVect0LinkObjId="g_2b12810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1229,-313 1229,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa4a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-397 1334,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47583@0" ObjectIDZND0="47584@1" Pin0InfoVect0LinkObjId="SW-306989_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-397 1334,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa5190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-440 1334,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47585@1" ObjectIDZND0="47583@1" Pin0InfoVect0LinkObjId="SW-306988_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-440 1334,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b28ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-351 1334,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47584@0" ObjectIDZND0="47581@0" Pin0InfoVect0LinkObjId="g_2b12810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-351 1334,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b04fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-548 1374,-558 1334,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="g_2b04460@0" ObjectIDZND0="47585@x" ObjectIDZND1="g_2b778c0@0" ObjectIDZND2="g_2b294b0@0" Pin0InfoVect0LinkObjId="SW-306989_0" Pin0InfoVect1LinkObjId="g_2b778c0_0" Pin0InfoVect2LinkObjId="g_2b294b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b04460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-548 1374,-558 1334,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b059d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-457 1334,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="47585@0" ObjectIDZND0="g_2b04460@0" ObjectIDZND1="g_2b778c0@0" ObjectIDZND2="g_2b294b0@0" Pin0InfoVect0LinkObjId="g_2b04460_0" Pin0InfoVect1LinkObjId="g_2b778c0_0" Pin0InfoVect2LinkObjId="g_2b294b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-457 1334,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5a0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-690 1334,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="47586@0" ObjectIDZND0="g_2b294b0@1" Pin0InfoVect0LinkObjId="g_2b294b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-690 1334,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5aec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1362,-752 1334,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_2b5a310@0" ObjectIDZND0="47586@x" ObjectIDZND1="47735@1" Pin0InfoVect0LinkObjId="SW-306990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5a310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1362,-752 1334,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5b950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-770 1334,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47735@1" ObjectIDZND0="g_2b5a310@0" ObjectIDZND1="47586@x" Pin0InfoVect0LinkObjId="g_2b5a310_0" Pin0InfoVect1LinkObjId="SW-306990_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-770 1334,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5bb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-752 1334,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="g_2b5a310@0" ObjectIDND1="47735@1" ObjectIDZND0="47586@1" Pin0InfoVect0LinkObjId="SW-306990_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b5a310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-752 1334,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b783a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-574 1334,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b778c0@0" ObjectIDZND0="g_2b04460@0" ObjectIDZND1="47585@x" ObjectIDZND2="g_2b294b0@0" Pin0InfoVect0LinkObjId="g_2b04460_0" Pin0InfoVect1LinkObjId="SW-306989_0" Pin0InfoVect2LinkObjId="g_2b294b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b778c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-574 1334,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b78e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-558 1334,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="g_2b04460@0" ObjectIDND1="47585@x" ObjectIDZND0="g_2b778c0@0" ObjectIDZND1="g_2b294b0@0" Pin0InfoVect0LinkObjId="g_2b778c0_0" Pin0InfoVect1LinkObjId="g_2b294b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b04460_0" Pin1InfoVect1LinkObjId="SW-306989_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-558 1334,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b79050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-574 1334,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b778c0@0" ObjectIDND1="g_2b04460@0" ObjectIDND2="47585@x" ObjectIDZND0="g_2b294b0@0" Pin0InfoVect0LinkObjId="g_2b294b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b778c0_0" Pin1InfoVect1LinkObjId="g_2b04460_0" Pin1InfoVect2LinkObjId="SW-306989_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-574 1334,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab0890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1545,-251 1545,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47587@0" ObjectIDZND0="47589@1" Pin0InfoVect0LinkObjId="SW-306998_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1545,-251 1545,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab0aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-209 1546,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="47589@0" ObjectIDZND0="48152@x" ObjectIDZND1="g_2a9af00@0" ObjectIDZND2="47590@x" Pin0InfoVect0LinkObjId="SM-CX_WSC.CX_WSC_P1_0" Pin0InfoVect1LinkObjId="g_2a9af00_0" Pin0InfoVect2LinkObjId="SW-306999_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1546,-209 1546,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab0cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-186 1546,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="47589@x" ObjectIDND1="g_2a9af00@0" ObjectIDND2="47590@x" ObjectIDZND0="48152@0" Pin0InfoVect0LinkObjId="SM-CX_WSC.CX_WSC_P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306998_0" Pin1InfoVect1LinkObjId="g_2a9af00_0" Pin1InfoVect2LinkObjId="SW-306999_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1546,-186 1546,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab1ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1588,-186 1624,-186 1624,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2a9af00@0" ObjectIDND1="48152@x" ObjectIDND2="47589@x" ObjectIDZND0="47590@1" Pin0InfoVect0LinkObjId="SW-306999_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a9af00_0" Pin1InfoVect1LinkObjId="SM-CX_WSC.CX_WSC_P1_0" Pin1InfoVect2LinkObjId="SW-306998_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1588,-186 1624,-186 1624,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab1ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1588,-164 1588,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="g_2a9af00@0" ObjectIDZND0="47590@x" ObjectIDZND1="48152@x" ObjectIDZND2="47589@x" Pin0InfoVect0LinkObjId="SW-306999_0" Pin0InfoVect1LinkObjId="SM-CX_WSC.CX_WSC_P1_0" Pin0InfoVect2LinkObjId="SW-306998_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9af00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1588,-164 1588,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab2140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1588,-186 1545,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="generator" EndDevType1="switch" ObjectIDND0="g_2a9af00@0" ObjectIDND1="47590@x" ObjectIDZND0="48152@x" ObjectIDZND1="47589@x" Pin0InfoVect0LinkObjId="SM-CX_WSC.CX_WSC_P1_0" Pin0InfoVect1LinkObjId="SW-306998_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a9af00_0" Pin1InfoVect1LinkObjId="SW-306999_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1588,-186 1545,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab23a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1624,-137 1623,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="47590@0" ObjectIDZND0="g_2ab12c0@0" Pin0InfoVect0LinkObjId="g_2ab12c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306999_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1624,-137 1623,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab3c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1545,-312 1545,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47588@0" ObjectIDZND0="47581@0" Pin0InfoVect0LinkObjId="g_2b12810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1545,-312 1545,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab5ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1545,-295 1545,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47588@1" ObjectIDZND0="47587@1" Pin0InfoVect0LinkObjId="SW-306997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1545,-295 1545,-278 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_WSC"/>
</svg>