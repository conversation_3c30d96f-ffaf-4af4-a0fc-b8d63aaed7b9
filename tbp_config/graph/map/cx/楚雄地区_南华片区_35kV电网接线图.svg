<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-199" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-246 -5241 3279 1881">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape29">
    <ellipse cx="11" cy="15" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="28" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="0" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="0" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape6">
    <polyline DF8003:Layer="0" points="16,51 0,51 9,26 16,50 16,51 "/>
    <polyline DF8003:Layer="0" points="1,3 17,3 9,27 1,4 1,3 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.03226" x1="9" x2="9" y1="54" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.03226" x1="8" x2="8" y1="52" y2="52"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape38">
    <rect height="8" stroke-width="0.75" width="18" x="21" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="1" x2="1" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="4" x2="4" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="7" x2="7" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="21" x2="7" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="46" x2="26" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="28" x2="26" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="26" x2="28" y1="7" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape159">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="9" x2="13" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="13" x2="13" y1="13" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="17" x2="13" y1="17" y2="13"/>
    <ellipse cx="12" cy="13" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="33" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="37" y2="33"/>
    <ellipse cx="12" cy="33" rx="11" ry="12" stroke-width="1.22172"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape16_0">
    <circle cx="29" cy="25" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="23" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="23" x2="15" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="15" x2="23" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape16_1">
    <ellipse cx="60" cy="25" fillStyle="0" rx="24.5" ry="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="74" x2="66" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="66" x2="58" y1="25" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="58" x2="66" y1="17" y2="25"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape43_0">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape43_1">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape71_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,55 6,55 6,26 " stroke-width="1"/>
    <circle cx="31" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="43" y2="1"/>
    <polyline DF8003:Layer="0" points="31,12 25,25 37,25 31,12 31,13 31,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="55" y2="60"/>
   </symbol>
   <symbol id="transformer2:shape71_1">
    <circle cx="31" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="79" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="79" y2="84"/>
   </symbol>
   <symbol id="voltageTransformer:shape1">
    <ellipse cx="14" cy="7" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="8" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b18c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b2260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22b2c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22b37a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22b4a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22b5730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b5f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b68f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b71c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b7bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b7bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b9380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b9380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_22ba320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22bbf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22bcad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22bd270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22bd960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22bf090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22bf860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22bff50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22c0910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22c1a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22c2410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22c2f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22c82e0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22c9030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22c4e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22c6340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22c6de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_22d48a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_22ca9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1891" width="3289" x="-251" y="-5246"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2939" x2="2957" y1="-4218" y2="-4200"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2957" x2="2939" y1="-4218" y2="-4200"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="310" x2="310" y1="-5008" y2="-4997"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="321" x2="321" y1="-5008" y2="-4996"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="0" id="SW-131651">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -3606.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24139" ObjectName="SW-NH_MJ.NH_MJ_3716SW"/>
     <cge:Meas_Ref ObjectId="131651"/>
    <cge:TPSR_Ref TObjectID="24139"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -3510.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24138" ObjectName="SW-NH_MJ.NH_MJ_3711SW"/>
     <cge:Meas_Ref ObjectId="131650"/>
    <cge:TPSR_Ref TObjectID="24138"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130598">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1948.000000 -3965.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23948" ObjectName="SW-NH_XY.NH_XY_3021SW"/>
     <cge:Meas_Ref ObjectId="130598"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130599">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1948.000000 -3877.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23949" ObjectName="SW-NH_XY.NH_XY_3022SW"/>
     <cge:Meas_Ref ObjectId="130599"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130600">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1971.000000 -3866.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23950" ObjectName="SW-NH_XY.NH_XY_3712SW"/>
     <cge:Meas_Ref ObjectId="130600"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132006">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.000000 -3712.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24195" ObjectName="SW-NH_HTP.NH_HTP_3111SW"/>
     <cge:Meas_Ref ObjectId="132006"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1554.000000 -3657.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24140" ObjectName="SW-NH_MJ.NH_MJ_37167SW"/>
     <cge:Meas_Ref ObjectId="131661"/>
    <cge:TPSR_Ref TObjectID="24140"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131256">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1555.000000 -3911.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24060" ObjectName="SW-NH_DSY.NH_DSY_36167SW"/>
     <cge:Meas_Ref ObjectId="131256"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130571">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1989.000000 -4129.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23945" ObjectName="SW-NH_XY.NH_XY_3012SW"/>
     <cge:Meas_Ref ObjectId="130571"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130570">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1989.000000 -4033.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23944" ObjectName="SW-NH_XY.NH_XY_3011SW"/>
     <cge:Meas_Ref ObjectId="130570"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9931">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 -4540.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1601" ObjectName="SW-CX_NH.CX_NH_3886SW"/>
     <cge:Meas_Ref ObjectId="9931"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9930">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 -4625.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1600" ObjectName="SW-CX_NH.CX_NH_3882SW"/>
     <cge:Meas_Ref ObjectId="9930"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9828">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -4623.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1553" ObjectName="SW-CX_NH.CX_NH_3821SW"/>
     <cge:Meas_Ref ObjectId="9828"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9829">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -4539.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1554" ObjectName="SW-CX_NH.CX_NH_3826SW"/>
     <cge:Meas_Ref ObjectId="9829"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9822">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 -4538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1550" ObjectName="SW-CX_NH.CX_NH_3836SW"/>
     <cge:Meas_Ref ObjectId="9822"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9821">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 -4622.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1549" ObjectName="SW-CX_NH.CX_NH_3831SW"/>
     <cge:Meas_Ref ObjectId="9821"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9924">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1811.000000 -4540.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1597" ObjectName="SW-CX_NH.CX_NH_3866SW"/>
     <cge:Meas_Ref ObjectId="9924"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9923">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1811.000000 -4624.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1596" ObjectName="SW-CX_NH.CX_NH_3862SW"/>
     <cge:Meas_Ref ObjectId="9923"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9814">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1989.000000 -4624.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1545" ObjectName="SW-CX_NH.CX_NH_3842SW"/>
     <cge:Meas_Ref ObjectId="9814"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9815">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1989.000000 -4540.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1546" ObjectName="SW-CX_NH.CX_NH_3846SW"/>
     <cge:Meas_Ref ObjectId="9815"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9936">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2168.000000 -4539.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1604" ObjectName="SW-CX_NH.CX_NH_3816SW"/>
     <cge:Meas_Ref ObjectId="9936"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9935">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2168.000000 -4623.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1603" ObjectName="SW-CX_NH.CX_NH_3811SW"/>
     <cge:Meas_Ref ObjectId="9935"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9932">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1236.000000 -4497.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1602" ObjectName="SW-CX_NH.CX_NH_3885SW"/>
     <cge:Meas_Ref ObjectId="9932"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9830">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1412.000000 -4496.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1555" ObjectName="SW-CX_NH.CX_NH_3825SW"/>
     <cge:Meas_Ref ObjectId="9830"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9823">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 -4496.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1551" ObjectName="SW-CX_NH.CX_NH_3835SW"/>
     <cge:Meas_Ref ObjectId="9823"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9925">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1825.000000 -4496.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1598" ObjectName="SW-CX_NH.CX_NH_3865SW"/>
     <cge:Meas_Ref ObjectId="9925"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9816">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2005.000000 -4496.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1547" ObjectName="SW-CX_NH.CX_NH_3845SW"/>
     <cge:Meas_Ref ObjectId="9816"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9937">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2184.000000 -4496.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1605" ObjectName="SW-CX_NH.CX_NH_3815SW"/>
     <cge:Meas_Ref ObjectId="9937"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130572">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -4182.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23946" ObjectName="SW-NH_XY.NH_XY_3711SW"/>
     <cge:Meas_Ref ObjectId="130572"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130136">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1410.000000 -4273.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23854" ObjectName="SW-NH_SQ.NH_SQ_3711SW"/>
     <cge:Meas_Ref ObjectId="130136"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130138">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -4221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23855" ObjectName="SW-NH_SQ.NH_SQ_3011SW"/>
     <cge:Meas_Ref ObjectId="130138"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130913">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1811.000000 -4219.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23994" ObjectName="SW-NH_YL.NH_YL_3011SW"/>
     <cge:Meas_Ref ObjectId="130913"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130914">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1834.000000 -4270.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23995" ObjectName="SW-NH_YL.NH_YL_3701SW"/>
     <cge:Meas_Ref ObjectId="130914"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9766">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1672.000000 -4689.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1533" ObjectName="SW-CX_NH.CX_NH_3121SW"/>
     <cge:Meas_Ref ObjectId="9766"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9767">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1774.000000 -4691.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1534" ObjectName="SW-CX_NH.CX_NH_3122SW"/>
     <cge:Meas_Ref ObjectId="9767"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2541.000000 -4288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-23157">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2869.000000 -4474.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3690" ObjectName="SW-CX_XJ.CX_XJ_3621SW"/>
     <cge:Meas_Ref ObjectId="23157"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-23158">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2869.000000 -4375.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3691" ObjectName="SW-CX_XJ.CX_XJ_3626SW"/>
     <cge:Meas_Ref ObjectId="23158"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129557">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 336.000000 -4654.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23732" ObjectName="SW-NH_HS.NH_HS_3711SW"/>
     <cge:Meas_Ref ObjectId="129557"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129556">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 336.000000 -4558.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23731" ObjectName="SW-NH_HS.NH_HS_3716SW"/>
     <cge:Meas_Ref ObjectId="129556"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -4657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23795" ObjectName="SW-NH_HS.NH_HS_3011SW"/>
     <cge:Meas_Ref ObjectId="129676"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129698">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 716.000000 -4658.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23801" ObjectName="SW-NH_HS.NH_HS_3021SW"/>
     <cge:Meas_Ref ObjectId="129698"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.000000 -4659.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23736" ObjectName="SW-NH_HS.NH_HS_3721SW"/>
     <cge:Meas_Ref ObjectId="129568"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129567">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.000000 -4561.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23735" ObjectName="SW-NH_HS.NH_HS_3726SW"/>
     <cge:Meas_Ref ObjectId="129567"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129569">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.000000 -4545.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23737" ObjectName="SW-NH_HS.NH_HS_37267SW"/>
     <cge:Meas_Ref ObjectId="129569"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 -4432.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129558">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 358.000000 -4537.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23733" ObjectName="SW-NH_HS.NH_HS_37167SW"/>
     <cge:Meas_Ref ObjectId="129558"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2278.000000 -5142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129589">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 -4818.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23743" ObjectName="SW-NH_HS.NH_HS_3746SW"/>
     <cge:Meas_Ref ObjectId="129589"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 -4720.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23745" ObjectName="SW-NH_HS.NH_HS_3741SW"/>
     <cge:Meas_Ref ObjectId="129591"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.000000 -4871.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23744" ObjectName="SW-NH_HS.NH_HS_37467SW"/>
     <cge:Meas_Ref ObjectId="129590"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-42342">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 -4957.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7004" ObjectName="SW-CX_LH.CX_LH_3616SW"/>
     <cge:Meas_Ref ObjectId="42342"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-42341">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 -4859.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7003" ObjectName="SW-CX_LH.CX_LH_3611SW"/>
     <cge:Meas_Ref ObjectId="42341"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-42343">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1240.000000 -5010.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7005" ObjectName="SW-CX_LH.CX_LH_36167SW"/>
     <cge:Meas_Ref ObjectId="42343"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-42344">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1233.000000 -5043.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7006" ObjectName="SW-CX_LH.CX_LH_3619SW"/>
     <cge:Meas_Ref ObjectId="42344"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-147614">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -5017.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25798" ObjectName="SW-YA_XJC.YA_XJC_3816SW"/>
     <cge:Meas_Ref ObjectId="147614"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-147612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -5115.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25796" ObjectName="SW-YA_XJC.YA_XJC_3811SW"/>
     <cge:Meas_Ref ObjectId="147612"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-145564">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 380.000000 -5117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25552" ObjectName="SW-YA_TP.YA_TP_3121SW"/>
     <cge:Meas_Ref ObjectId="145564"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-145565">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 380.000000 -5034.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25553" ObjectName="SW-YA_TP.YA_TP_3126SW"/>
     <cge:Meas_Ref ObjectId="145565"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.000000 -4376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-145567">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 329.000000 -5021.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25555" ObjectName="SW-YA_TP.YA_TP_31267SW"/>
     <cge:Meas_Ref ObjectId="145567"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-147613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 528.000000 -5005.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25797" ObjectName="SW-YA_XJC.YA_XJC_38167SW"/>
     <cge:Meas_Ref ObjectId="147613"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129580">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 601.000000 -4882.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23741" ObjectName="SW-NH_HS.NH_HS_37367SW"/>
     <cge:Meas_Ref ObjectId="129580"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -4820.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23739" ObjectName="SW-NH_HS.NH_HS_3736SW"/>
     <cge:Meas_Ref ObjectId="129578"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -4722.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23740" ObjectName="SW-NH_HS.NH_HS_3731SW"/>
     <cge:Meas_Ref ObjectId="129579"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2541.000000 -4651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2541.000000 -4750.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2278.000000 -4881.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2278.000000 -4980.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2382.000000 -4774.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2479.000000 -4949.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2479.000000 -5048.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.978261 2479.000000 -4854.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131203">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 -4082.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24046" ObjectName="SW-NH_DSY.NH_DSY_3621SW"/>
     <cge:Meas_Ref ObjectId="131203"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131204">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 -4211.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24047" ObjectName="SW-NH_DSY.NH_DSY_3626SW"/>
     <cge:Meas_Ref ObjectId="131204"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131207">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1610.000000 -4268.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24050" ObjectName="SW-NH_DSY.NH_DSY_36267SW"/>
     <cge:Meas_Ref ObjectId="131207"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193483">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2325.000000 -3477.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29417" ObjectName="SW-CX_BJ.CX_BJ_3821SW"/>
     <cge:Meas_Ref ObjectId="193483"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193484">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2325.000000 -3574.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29418" ObjectName="SW-CX_BJ.CX_BJ_3826SW"/>
     <cge:Meas_Ref ObjectId="193484"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193487">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2341.000000 -3636.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29421" ObjectName="SW-CX_BJ.CX_BJ_38267SW"/>
     <cge:Meas_Ref ObjectId="193487"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193514">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.000000 -3447.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29425" ObjectName="SW-NH_MJ.NH_MJ_3726SW"/>
     <cge:Meas_Ref ObjectId="193514"/>
    <cge:TPSR_Ref TObjectID="29425"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193512">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -3447.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29424" ObjectName="SW-NH_MJ.NH_MJ_3721SW"/>
     <cge:Meas_Ref ObjectId="193512"/>
    <cge:TPSR_Ref TObjectID="29424"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1542.000000 -3389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29426" ObjectName="SW-NH_MJ.NH_MJ_37267SW"/>
     <cge:Meas_Ref ObjectId="193516"/>
    <cge:TPSR_Ref TObjectID="29426"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2493.000000 -4637.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2495.000000 -4355.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131206">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1614.000000 -4193.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24049" ObjectName="SW-NH_DSY.NH_DSY_36260SW"/>
     <cge:Meas_Ref ObjectId="131206"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131205">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1614.000000 -4139.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24048" ObjectName="SW-NH_DSY.NH_DSY_36217SW"/>
     <cge:Meas_Ref ObjectId="131205"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131254">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -4020.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24058" ObjectName="SW-NH_DSY.NH_DSY_3611SW"/>
     <cge:Meas_Ref ObjectId="131254"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131255">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -3931.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24059" ObjectName="SW-NH_DSY.NH_DSY_3616SW"/>
     <cge:Meas_Ref ObjectId="131255"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 -3657.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1544.000000 -3684.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-185041">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1948.000000 -3630.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28098" ObjectName="SW-CX_SJ.CX_SJ_3416SW"/>
     <cge:Meas_Ref ObjectId="185041"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-185040">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1948.000000 -3534.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28099" ObjectName="SW-CX_SJ.CX_SJ_3411SW"/>
     <cge:Meas_Ref ObjectId="185040"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131236">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1330.000000 -4021.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24052" ObjectName="SW-NH_DSY.NH_DSY_3631SW"/>
     <cge:Meas_Ref ObjectId="131236"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131237">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1330.000000 -3932.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24053" ObjectName="SW-NH_DSY.NH_DSY_3636SW"/>
     <cge:Meas_Ref ObjectId="131237"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.000000 -3921.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24056" ObjectName="SW-NH_DSY.NH_DSY_36367SW"/>
     <cge:Meas_Ref ObjectId="131240"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="0" id="BS-CX_BJ.CX_BJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2262,-3467 2405,-3467 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10705" ObjectName="BS-CX_BJ.CX_BJ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2262,-3467 2405,-3467 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NH_MJ.NH_MJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1387,-3498 1590,-3498 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24135" ObjectName="BS-NH_MJ.NH_MJ_3IM"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   <polyline fill="none" opacity="0" points="1387,-3498 1590,-3498 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NH_HTP.NH_HTP_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1256,-3765 1256,-3668 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24217" ObjectName="BS-NH_HTP.NH_HTP_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1256,-3765 1256,-3668 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NH_XY.NH_XY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1924,-4023 2048,-4023 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23978" ObjectName="BS-NH_XY.NH_XY_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1924,-4023 2048,-4023 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NH_DSY.NH_DSY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1292,-4073 1626,-4073 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24042" ObjectName="BS-NH_DSY.NH_DSY_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1292,-4073 1626,-4073 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NH_YL.NH_YL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1769,-4209 1873,-4209 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23991" ObjectName="BS-NH_YL.NH_YL_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1769,-4209 1873,-4209 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NH_SQ.NH_SQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1357,-4209 1458,-4209 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23852" ObjectName="BS-NH_SQ.NH_SQ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1357,-4209 1458,-4209 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_NH.CX_NH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1168,-4679 1705,-4679 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="1512" ObjectName="BS-CX_NH.CX_NH_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1168,-4679 1705,-4679 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_NH.CX_NH_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1756,-4679 2283,-4679 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="1513" ObjectName="BS-CX_NH.CX_NH_3IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1756,-4679 2283,-4679 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_NH.CX_NH_3PM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1157,-4471 2280,-4471 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9682" ObjectName="BS-CX_NH.CX_NH_3PM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1157,-4471 2280,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_XJ.CX_XJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2802,-4527 2955,-4527 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3602" ObjectName="BS-CX_XJ.CX_XJ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2802,-4527 2955,-4527 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="864,-4355 986,-4355 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="864,-4355 986,-4355 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="2221,-4837 2618,-4837 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2221,-4837 2618,-4837 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NH_HS.NH_HS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="299,-4712 961,-4712 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23829" ObjectName="BS-NH_HS.NH_HS_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="299,-4712 961,-4712 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_LH.CX_LH_31M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1146,-4855 1312,-4855 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10915" ObjectName="BS-CX_LH.CX_LH_31M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1146,-4855 1312,-4855 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YA_TP.YA_TP_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="324,-5167 448,-5167 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25544" ObjectName="BS-YA_TP.YA_TP_3M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="324,-5167 448,-5167 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YA_XJC.YA_XJC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="521,-5167 654,-5167 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25791" ObjectName="BS-YA_XJC.YA_XJC_3M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="521,-5167 654,-5167 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="2335,-5114 2665,-5114 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2335,-5114 2665,-5114 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_SJ.CX_SJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1902,-3526 2026,-3526 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7350" ObjectName="BS-CX_SJ.CX_SJ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1902,-3526 2026,-3526 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="0" id="g_172a910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2020.000000 -3862.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_169b030" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1603.000000 -3653.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_17002c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1604.000000 -3907.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_165f860" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2062.000000 -4178.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1662a80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1457.000000 -4269.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_166cba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1883.000000 -4266.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1627b30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 988.000000 -4541.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_16294f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 989.000000 -4428.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_16315b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 407.000000 -4533.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_18e71b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 866.000000 -4867.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_18f0e30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1289.000000 -5006.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_15cbed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 650.000000 -4878.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cc82f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2382.000000 -4672.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cee150" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 500.000000 -5004.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cee990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 306.000000 -5020.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cfad90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2390.000000 -3632.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d09d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1545.000000 -3360.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d0e100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.347826 -0.000000 0.000000 -1.583333 2462.000000 -4633.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d14070" refnum="0">
    <use class="BV-0KV" transform="matrix(1.347826 -0.000000 0.000000 -1.583333 2462.000000 -4351.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d1d140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1661.000000 -4189.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d1ede0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1661.000000 -4135.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d799e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1403.000000 -3917.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_176ac50">
     <polyline DF8003:Layer="0" fill="none" points="1957,-3918 1957,-3930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23949@1" ObjectIDZND0="23947@0" Pin0InfoVect0LinkObjId="SW-130596_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130599_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1957,-3918 1957,-3930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176ae40">
     <polyline DF8003:Layer="0" fill="none" points="1957,-3957 1957,-3970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23947@1" ObjectIDZND0="23948@0" Pin0InfoVect0LinkObjId="SW-130598_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130596_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1957,-3957 1957,-3970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176b030">
     <polyline DF8003:Layer="0" fill="none" points="1957,-4006 1957,-4023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23948@1" ObjectIDZND0="23978@0" Pin0InfoVect0LinkObjId="g_168bff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1957,-4006 1957,-4023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_172b0c0">
     <polyline DF8003:Layer="0" fill="none" points="2012,-3871 2027,-3871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23950@1" ObjectIDZND0="g_172a910@0" Pin0InfoVect0LinkObjId="g_172a910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2012,-3871 2027,-3871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_172b2b0">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3592 1540,-3611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24137@1" ObjectIDZND0="24139@0" Pin0InfoVect0LinkObjId="SW-131651_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131648_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3592 1540,-3611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_172b4a0">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3498 1540,-3515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24135@0" ObjectIDZND0="24138@0" Pin0InfoVect0LinkObjId="SW-131650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d0a7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3498 1540,-3515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_172b690">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3551 1540,-3565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24138@1" ObjectIDZND0="24137@0" Pin0InfoVect0LinkObjId="SW-131648_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3551 1540,-3565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16ae190">
     <polyline DF8003:Layer="0" fill="none" points="1256,-3717 1294,-3717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24217@0" ObjectIDZND0="24195@0" Pin0InfoVect0LinkObjId="SW-132006_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1256,-3717 1294,-3717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16e95f0">
     <polyline DF8003:Layer="0" fill="none" points="1651,-3689 1666,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_16ae380@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16ae380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1651,-3689 1666,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16e9f60">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3689 1540,-3717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="24140@x" ObjectIDND1="24139@x" ObjectIDND2="0@x" ObjectIDZND0="24195@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1d35da0@0" Pin0InfoVect0LinkObjId="SW-132006_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1d35da0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131661_0" Pin1InfoVect1LinkObjId="SW-131651_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3689 1540,-3717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169b7e0">
     <polyline DF8003:Layer="0" fill="none" points="1595,-3662 1610,-3662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24140@1" ObjectIDZND0="g_169b030@0" Pin0InfoVect0LinkObjId="g_169b030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131661_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1595,-3662 1610,-3662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169b9d0">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3662 1559,-3662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24139@x" ObjectIDND1="24195@x" ObjectIDND2="0@x" ObjectIDZND0="24140@0" Pin0InfoVect0LinkObjId="SW-131661_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131651_0" Pin1InfoVect1LinkObjId="SW-132006_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3662 1559,-3662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169c340">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3647 1540,-3662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24139@1" ObjectIDZND0="24140@x" ObjectIDZND1="24195@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-131661_0" Pin0InfoVect1LinkObjId="SW-132006_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131651_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3647 1540,-3662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169c530">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3662 1540,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="24140@x" ObjectIDND1="24139@x" ObjectIDZND0="24195@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1d35da0@0" Pin0InfoVect0LinkObjId="SW-132006_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1d35da0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131661_0" Pin1InfoVect1LinkObjId="SW-131651_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3662 1540,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_168be00">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4088 1998,-4074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23943@0" ObjectIDZND0="23944@1" Pin0InfoVect0LinkObjId="SW-130570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4088 1998,-4074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_168bff0">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4038 1998,-4023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23944@0" ObjectIDZND0="23978@0" Pin0InfoVect0LinkObjId="g_176b030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4038 1998,-4023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_168c1e0">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4134 1998,-4115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23945@0" ObjectIDZND0="23943@1" Pin0InfoVect0LinkObjId="SW-130568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130571_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4134 1998,-4115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_170ba30">
     <polyline DF8003:Layer="0" fill="none" points="1405,-4591 1405,-4580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1552@0" ObjectIDZND0="1554@1" Pin0InfoVect0LinkObjId="SW-9829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9826_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-4591 1405,-4580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_170bc50">
     <polyline DF8003:Layer="0" fill="none" points="1405,-4679 1405,-4664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1512@0" ObjectIDZND0="1553@1" Pin0InfoVect0LinkObjId="SW-9828_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1733760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-4679 1405,-4664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16d3990">
     <polyline DF8003:Layer="0" fill="none" points="1405,-4628 1405,-4618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1553@0" ObjectIDZND0="1552@1" Pin0InfoVect0LinkObjId="SW-9826_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9828_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-4628 1405,-4618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1733760">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4663 1592,-4679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1549@1" ObjectIDZND0="1512@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9821_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4663 1592,-4679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17339c0">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4579 1592,-4590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1550@1" ObjectIDZND0="1548@0" Pin0InfoVect0LinkObjId="SW-9819_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9822_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4579 1592,-4590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1733c20">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4617 1592,-4627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1548@1" ObjectIDZND0="1549@0" Pin0InfoVect0LinkObjId="SW-9821_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9819_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4617 1592,-4627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16f6f10">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4679 1820,-4665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1513@0" ObjectIDZND0="1596@1" Pin0InfoVect0LinkObjId="SW-9923_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16cd110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4679 1820,-4665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16f7170">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4629 1820,-4619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1596@0" ObjectIDZND0="1595@1" Pin0InfoVect0LinkObjId="SW-9921_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9923_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4629 1820,-4619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16f73d0">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4592 1820,-4581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1595@0" ObjectIDZND0="1597@1" Pin0InfoVect0LinkObjId="SW-9924_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9921_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4592 1820,-4581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17044a0">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4592 1998,-4581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1544@0" ObjectIDZND0="1546@1" Pin0InfoVect0LinkObjId="SW-9815_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9812_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4592 1998,-4581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1704700">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4679 1998,-4665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1513@0" ObjectIDZND0="1545@1" Pin0InfoVect0LinkObjId="SW-9814_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16cd110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4679 1998,-4665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1704960">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4629 1998,-4619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1545@0" ObjectIDZND0="1544@1" Pin0InfoVect0LinkObjId="SW-9812_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4629 1998,-4619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16efd40">
     <polyline DF8003:Layer="0" fill="none" points="2177,-4679 2177,-4664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1513@0" ObjectIDZND0="1603@1" Pin0InfoVect0LinkObjId="SW-9935_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16cd110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2177,-4679 2177,-4664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16effa0">
     <polyline DF8003:Layer="0" fill="none" points="2177,-4628 2177,-4618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1603@0" ObjectIDZND0="1606@1" Pin0InfoVect0LinkObjId="SW-9940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9935_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2177,-4628 2177,-4618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16f0200">
     <polyline DF8003:Layer="0" fill="none" points="2177,-4591 2177,-4580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1606@0" ObjectIDZND0="1604@1" Pin0InfoVect0LinkObjId="SW-9936_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2177,-4591 2177,-4580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16f0460">
     <polyline DF8003:Layer="0" fill="none" points="1230,-4679 1230,-4666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1512@0" ObjectIDZND0="1600@1" Pin0InfoVect0LinkObjId="SW-9930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1733760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-4679 1230,-4666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16f06c0">
     <polyline DF8003:Layer="0" fill="none" points="1230,-4630 1230,-4620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1600@0" ObjectIDZND0="1599@1" Pin0InfoVect0LinkObjId="SW-9928_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-4630 1230,-4620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a6500">
     <polyline DF8003:Layer="0" fill="none" points="1230,-4502 1241,-4502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="1601@x" ObjectIDND1="9682@0" ObjectIDZND0="1602@0" Pin0InfoVect0LinkObjId="SW-9932_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9931_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-4502 1241,-4502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a6760">
     <polyline DF8003:Layer="0" fill="none" points="1277,-4502 1285,-4502 1285,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1602@1" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a69c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1277,-4502 1285,-4502 1285,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a69c0">
     <polyline DF8003:Layer="0" fill="none" points="1453,-4501 1461,-4501 1461,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1555@1" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1453,-4501 1461,-4501 1461,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a6c20">
     <polyline DF8003:Layer="0" fill="none" points="1406,-4501 1417,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="1554@x" ObjectIDND1="9682@0" ObjectIDZND0="1555@0" Pin0InfoVect0LinkObjId="SW-9830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9829_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,-4501 1417,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16e3dd0">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4501 1604,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="1550@x" ObjectIDND1="9682@0" ObjectIDZND0="1551@0" Pin0InfoVect0LinkObjId="SW-9823_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9822_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4501 1604,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16e6560">
     <polyline DF8003:Layer="0" fill="none" points="1640,-4501 1648,-4501 1648,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1551@1" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9823_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1640,-4501 1648,-4501 1648,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16372c0">
     <polyline DF8003:Layer="0" fill="none" points="1866,-4501 1874,-4501 1874,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1598@1" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9925_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1866,-4501 1874,-4501 1874,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1637520">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4501 1830,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="1597@x" ObjectIDND1="9682@0" ObjectIDZND0="1598@0" Pin0InfoVect0LinkObjId="SW-9925_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9924_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4501 1830,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1637780">
     <polyline DF8003:Layer="0" fill="none" points="2046,-4501 2054,-4501 2054,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1547@1" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2046,-4501 2054,-4501 2054,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16379e0">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4501 2010,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="1546@x" ObjectIDND1="9682@0" ObjectIDZND0="1547@0" Pin0InfoVect0LinkObjId="SW-9816_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9815_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4501 2010,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_165a390">
     <polyline DF8003:Layer="0" fill="none" points="2177,-4501 2189,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="1604@x" ObjectIDND1="9682@0" ObjectIDZND0="1605@0" Pin0InfoVect0LinkObjId="SW-9937_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9936_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2177,-4501 2189,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_165cb20">
     <polyline DF8003:Layer="0" fill="none" points="2225,-4501 2233,-4501 2233,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1605@1" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9937_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2225,-4501 2233,-4501 2233,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16602f0">
     <polyline DF8003:Layer="0" fill="none" points="2054,-4187 2069,-4187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23946@1" ObjectIDZND0="g_165f860@0" Pin0InfoVect0LinkObjId="g_165f860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130572_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2054,-4187 2069,-4187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1663560">
     <polyline DF8003:Layer="0" fill="none" points="1451,-4278 1464,-4278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23854@1" ObjectIDZND0="g_1662a80@0" Pin0InfoVect0LinkObjId="g_1662a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130136_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1451,-4278 1464,-4278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1665fc0">
     <polyline DF8003:Layer="0" fill="none" points="1405,-4226 1405,-4209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23855@0" ObjectIDZND0="23852@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-4226 1405,-4209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166a410">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4224 1820,-4209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23994@0" ObjectIDZND0="23991@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130913_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4224 1820,-4209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166d630">
     <polyline DF8003:Layer="0" fill="none" points="1875,-4275 1890,-4275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23995@1" ObjectIDZND0="g_166cba0@0" Pin0InfoVect0LinkObjId="g_166cba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130914_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1875,-4275 1890,-4275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166d890">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4275 1839,-4275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23994@x" ObjectIDND1="9682@0" ObjectIDND2="g_1d48a70@0" ObjectIDZND0="23995@0" Pin0InfoVect0LinkObjId="SW-130914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130913_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="g_1d48a70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4275 1839,-4275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166e380">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4275 1820,-4260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23995@x" ObjectIDND1="9682@0" ObjectIDND2="g_1d48a70@0" ObjectIDZND0="23994@1" Pin0InfoVect0LinkObjId="SW-130913_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130914_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="g_1d48a70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4275 1820,-4260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_175d130">
     <polyline DF8003:Layer="0" fill="none" points="1681,-4679 1681,-4694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1512@0" ObjectIDZND0="1533@0" Pin0InfoVect0LinkObjId="SW-9766_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1733760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1681,-4679 1681,-4694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_175d390">
     <polyline DF8003:Layer="0" fill="none" points="1681,-4730 1681,-4740 1717,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1533@1" ObjectIDZND0="1532@1" Pin0InfoVect0LinkObjId="SW-9762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1681,-4730 1681,-4740 1717,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16cceb0">
     <polyline DF8003:Layer="0" fill="none" points="1744,-4740 1783,-4740 1783,-4732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1532@0" ObjectIDZND0="1534@1" Pin0InfoVect0LinkObjId="SW-9767_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1744,-4740 1783,-4740 1783,-4732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16cd110">
     <polyline DF8003:Layer="0" fill="none" points="1783,-4696 1783,-4679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1534@0" ObjectIDZND0="1513@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1783,-4696 1783,-4679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16d09f0">
     <polyline DF8003:Layer="0" fill="none" points="2240,-4299 2224,-4299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_16d0170@0" Pin0InfoVect0LinkObjId="g_16d0170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2240,-4299 2224,-4299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16db210">
     <polyline DF8003:Layer="0" fill="none" points="2948,-4312 2925,-4312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_16da990@0" Pin0InfoVect0LinkObjId="g_16da990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2948,-4312 2925,-4312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16db470">
     <polyline DF8003:Layer="0" fill="none" points="2894,-4312 2878,-4312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="g_16da990@1" ObjectIDZND0="3691@x" ObjectIDZND1="g_16d0170@0" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="SW-23158_0" Pin0InfoVect1LinkObjId="g_16d0170_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16da990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2894,-4312 2878,-4312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16734d0">
     <polyline DF8003:Layer="0" fill="none" points="2878,-4312 2878,-4380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="busSection" EndDevType0="switch" ObjectIDND0="g_16da990@0" ObjectIDND1="g_16d0170@0" ObjectIDND2="9682@0" ObjectIDZND0="3691@0" Pin0InfoVect0LinkObjId="SW-23158_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16da990_0" Pin1InfoVect1LinkObjId="g_16d0170_0" Pin1InfoVect2LinkObjId="g_16a6760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2878,-4312 2878,-4380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1673730">
     <polyline DF8003:Layer="0" fill="none" points="2878,-4416 2878,-4433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3691@1" ObjectIDZND0="3631@0" Pin0InfoVect0LinkObjId="SW-22328_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23158_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2878,-4416 2878,-4433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1673990">
     <polyline DF8003:Layer="0" fill="none" points="2878,-4460 2878,-4479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3631@1" ObjectIDZND0="3690@0" Pin0InfoVect0LinkObjId="SW-23157_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-22328_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2878,-4460 2878,-4479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1673bf0">
     <polyline DF8003:Layer="0" fill="none" points="2878,-4515 2878,-4527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3690@1" ObjectIDZND0="3602@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23157_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2878,-4515 2878,-4527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_160c7c0">
     <polyline DF8003:Layer="0" fill="none" points="345,-4599 345,-4613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23731@1" ObjectIDZND0="23730@0" Pin0InfoVect0LinkObjId="SW-129555_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129556_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="345,-4599 345,-4613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_160ca20">
     <polyline DF8003:Layer="0" fill="none" points="345,-4640 345,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23730@1" ObjectIDZND0="23732@0" Pin0InfoVect0LinkObjId="SW-129557_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129555_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="345,-4640 345,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_160cc80">
     <polyline DF8003:Layer="0" fill="none" points="345,-4695 345,-4712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23732@1" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_15d4bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129557_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="345,-4695 345,-4712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1611bb0">
     <polyline DF8003:Layer="0" fill="none" points="520,-4616 520,-4579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23794@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129674_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="520,-4616 520,-4579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1611e10">
     <polyline DF8003:Layer="0" fill="none" points="520,-4712 520,-4698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23829@0" ObjectIDZND0="23795@1" Pin0InfoVect0LinkObjId="SW-129676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_160cc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="520,-4712 520,-4698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1612070">
     <polyline DF8003:Layer="0" fill="none" points="520,-4662 520,-4643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23795@0" ObjectIDZND0="23794@1" Pin0InfoVect0LinkObjId="SW-129674_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="520,-4662 520,-4643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_161e290">
     <polyline DF8003:Layer="0" fill="none" points="725,-4617 725,-4568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23800@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129696_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="725,-4617 725,-4568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_161e4f0">
     <polyline DF8003:Layer="0" fill="none" points="725,-4712 725,-4699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23829@0" ObjectIDZND0="23801@1" Pin0InfoVect0LinkObjId="SW-129698_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_160cc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="725,-4712 725,-4699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_161e750">
     <polyline DF8003:Layer="0" fill="none" points="725,-4663 725,-4644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23801@0" ObjectIDZND0="23800@1" Pin0InfoVect0LinkObjId="SW-129696_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="725,-4663 725,-4644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1622aa0">
     <polyline DF8003:Layer="0" fill="none" points="926,-4712 926,-4700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23829@0" ObjectIDZND0="23736@1" Pin0InfoVect0LinkObjId="SW-129568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_160cc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-4712 926,-4700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1622d00">
     <polyline DF8003:Layer="0" fill="none" points="926,-4664 926,-4645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23736@0" ObjectIDZND0="23734@1" Pin0InfoVect0LinkObjId="SW-129566_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-4664 926,-4645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1625580">
     <polyline DF8003:Layer="0" fill="none" points="926,-4618 926,-4602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23734@0" ObjectIDZND0="23735@1" Pin0InfoVect0LinkObjId="SW-129567_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129566_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-4618 926,-4602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1628560">
     <polyline DF8003:Layer="0" fill="none" points="980,-4550 995,-4550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23737@1" ObjectIDZND0="g_1627b30@0" Pin0InfoVect0LinkObjId="g_1627b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129569_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="980,-4550 995,-4550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16287c0">
     <polyline DF8003:Layer="0" fill="none" points="925,-4550 944,-4550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="23735@x" ObjectIDZND0="23737@0" Pin0InfoVect0LinkObjId="SW-129569_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-129567_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-4550 944,-4550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1629290">
     <polyline DF8003:Layer="0" fill="none" points="926,-4566 926,-4550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23735@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="23737@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-129569_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="926,-4566 926,-4550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1629ee0">
     <polyline DF8003:Layer="0" fill="none" points="981,-4437 996,-4437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_16294f0@0" Pin0InfoVect0LinkObjId="g_16294f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-4437 996,-4437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_162a140">
     <polyline DF8003:Layer="0" fill="none" points="926,-4437 945,-4437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23737@x" ObjectIDND1="23735@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129569_0" Pin1InfoVect1LinkObjId="SW-129567_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-4437 945,-4437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_162cf00">
     <polyline DF8003:Layer="0" fill="none" points="926,-4550 926,-4437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23737@x" ObjectIDND1="23735@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129569_0" Pin1InfoVect1LinkObjId="SW-129567_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="926,-4550 926,-4437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1631fe0">
     <polyline DF8003:Layer="0" fill="none" points="399,-4542 414,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23733@1" ObjectIDZND0="g_16315b0@0" Pin0InfoVect0LinkObjId="g_16315b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129558_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-4542 414,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1632240">
     <polyline DF8003:Layer="0" fill="none" points="344,-4542 363,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23731@x" ObjectIDND1="g_1d7a6d0@0" ObjectIDND2="24056@x" ObjectIDZND0="23733@0" Pin0InfoVect0LinkObjId="SW-129558_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129556_0" Pin1InfoVect1LinkObjId="g_1d7a6d0_0" Pin1InfoVect2LinkObjId="SW-131240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="344,-4542 363,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1632d10">
     <polyline DF8003:Layer="0" fill="none" points="1143,-4200 345,-4200 345,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1d7a6d0@0" ObjectIDND1="24056@x" ObjectIDND2="24053@x" ObjectIDZND0="23733@x" ObjectIDZND1="23731@x" Pin0InfoVect0LinkObjId="SW-129558_0" Pin0InfoVect1LinkObjId="SW-129556_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d7a6d0_0" Pin1InfoVect1LinkObjId="SW-131240_0" Pin1InfoVect2LinkObjId="SW-131237_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1143,-4200 345,-4200 345,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1632f70">
     <polyline DF8003:Layer="0" fill="none" points="345,-4542 345,-4563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23733@x" ObjectIDND1="g_1d7a6d0@0" ObjectIDND2="24056@x" ObjectIDZND0="23731@0" Pin0InfoVect0LinkObjId="SW-129556_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129558_0" Pin1InfoVect1LinkObjId="g_1d7a6d0_0" Pin1InfoVect2LinkObjId="SW-131240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="345,-4542 345,-4563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18e4740">
     <polyline DF8003:Layer="0" fill="none" points="802,-4712 802,-4725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23829@0" ObjectIDZND0="23745@0" Pin0InfoVect0LinkObjId="SW-129591_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_160cc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="802,-4712 802,-4725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18e49a0">
     <polyline DF8003:Layer="0" fill="none" points="802,-4761 802,-4777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23745@1" ObjectIDZND0="23742@0" Pin0InfoVect0LinkObjId="SW-129588_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="802,-4761 802,-4777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18e4c00">
     <polyline DF8003:Layer="0" fill="none" points="802,-4804 802,-4823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23742@1" ObjectIDZND0="23743@0" Pin0InfoVect0LinkObjId="SW-129589_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129588_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="802,-4804 802,-4823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18e7be0">
     <polyline DF8003:Layer="0" fill="none" points="858,-4876 873,-4876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23744@1" ObjectIDZND0="g_18e71b0@0" Pin0InfoVect0LinkObjId="g_18e71b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="858,-4876 873,-4876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f18c0">
     <polyline DF8003:Layer="0" fill="none" points="1281,-5015 1296,-5015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7005@1" ObjectIDZND0="g_18f0e30@0" Pin0InfoVect0LinkObjId="g_18f0e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42343_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1281,-5015 1296,-5015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f1b20">
     <polyline DF8003:Layer="0" fill="none" points="1226,-5015 1245,-5015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="7004@x" ObjectIDND1="g_18f3ce0@0" ObjectIDND2="7006@x" ObjectIDZND0="7005@0" Pin0InfoVect0LinkObjId="SW-42343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42342_0" Pin1InfoVect1LinkObjId="g_18f3ce0_0" Pin1InfoVect2LinkObjId="SW-42344_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-5015 1245,-5015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f1d80">
     <polyline DF8003:Layer="0" fill="none" points="1225,-4916 1225,-4900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7007@0" ObjectIDZND0="7003@1" Pin0InfoVect0LinkObjId="SW-42341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42346_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-4916 1225,-4900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f1fe0">
     <polyline DF8003:Layer="0" fill="none" points="1225,-4864 1225,-4855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7003@0" ObjectIDZND0="10915@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-4864 1225,-4855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f2ad0">
     <polyline DF8003:Layer="0" fill="none" points="1225,-5015 1225,-4998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="7005@x" ObjectIDND1="g_18f3ce0@0" ObjectIDND2="7006@x" ObjectIDZND0="7004@1" Pin0InfoVect0LinkObjId="SW-42342_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42343_0" Pin1InfoVect1LinkObjId="g_18f3ce0_0" Pin1InfoVect2LinkObjId="SW-42344_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-5015 1225,-4998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f2d30">
     <polyline DF8003:Layer="0" fill="none" points="1225,-4962 1225,-4943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7004@0" ObjectIDZND0="7007@1" Pin0InfoVect0LinkObjId="SW-42346_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-4962 1225,-4943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f2f90">
     <polyline DF8003:Layer="0" fill="none" points="1225,-5031 1214,-5031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="7005@x" ObjectIDND1="7004@x" ObjectIDND2="7006@x" ObjectIDZND0="g_18f3ce0@0" Pin0InfoVect0LinkObjId="g_18f3ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42343_0" Pin1InfoVect1LinkObjId="SW-42342_0" Pin1InfoVect2LinkObjId="SW-42344_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-5031 1214,-5031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f3a80">
     <polyline DF8003:Layer="0" fill="none" points="1225,-5031 1225,-5015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_18f3ce0@0" ObjectIDND1="7006@x" ObjectIDND2="23744@x" ObjectIDZND0="7005@x" ObjectIDZND1="7004@x" Pin0InfoVect0LinkObjId="SW-42343_0" Pin0InfoVect1LinkObjId="SW-42342_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18f3ce0_0" Pin1InfoVect1LinkObjId="SW-42344_0" Pin1InfoVect2LinkObjId="SW-129590_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-5031 1225,-5015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f7840">
     <polyline DF8003:Layer="0" fill="none" points="1274,-5048 1289,-5048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7006@1" ObjectIDZND0="g_18f6fc0@1" Pin0InfoVect0LinkObjId="g_18f6fc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1274,-5048 1289,-5048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f7aa0">
     <polyline DF8003:Layer="0" fill="none" points="1320,-5048 1336,-5048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_18f6fc0@0" ObjectIDZND0="g_18f7d00@0" Pin0InfoVect0LinkObjId="g_18f7d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18f6fc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1320,-5048 1336,-5048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1646390">
     <polyline DF8003:Layer="0" fill="none" points="587,-5167 587,-5156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25796@1" Pin0InfoVect0LinkObjId="SW-147612_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-5167 587,-5156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16465f0">
     <polyline DF8003:Layer="0" fill="none" points="587,-5120 587,-5101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25796@0" ObjectIDZND0="25795@1" Pin0InfoVect0LinkObjId="SW-147610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147612_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-5120 587,-5101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1646850">
     <polyline DF8003:Layer="0" fill="none" points="587,-5074 587,-5058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25795@0" ObjectIDZND0="25798@1" Pin0InfoVect0LinkObjId="SW-147614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-5074 587,-5058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1647340">
     <polyline DF8003:Layer="0" fill="none" points="389,-5167 389,-5158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25544@0" ObjectIDZND0="25552@1" Pin0InfoVect0LinkObjId="SW-145564_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="389,-5167 389,-5158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16475a0">
     <polyline DF8003:Layer="0" fill="none" points="389,-5122 389,-5111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25552@0" ObjectIDZND0="25551@1" Pin0InfoVect0LinkObjId="SW-145562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145564_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="389,-5122 389,-5111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1647800">
     <polyline DF8003:Layer="0" fill="none" points="389,-5084 389,-5075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25551@0" ObjectIDZND0="25553@1" Pin0InfoVect0LinkObjId="SW-145565_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="389,-5084 389,-5075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_164a200">
     <polyline DF8003:Layer="0" fill="none" points="926,-4437 926,-4417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23737@x" ObjectIDND1="23735@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129569_0" Pin1InfoVect1LinkObjId="SW-129567_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-4437 926,-4417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_164a460">
     <polyline DF8003:Layer="0" fill="none" points="926,-4381 926,-4355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-4381 926,-4355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_164cbf0">
     <polyline DF8003:Layer="0" fill="none" points="324,-5026 334,-5026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1cee990@0" ObjectIDZND0="25555@0" Pin0InfoVect0LinkObjId="SW-145567_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cee990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-5026 334,-5026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_164ce50">
     <polyline DF8003:Layer="0" fill="none" points="370,-5026 389,-5026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25555@1" ObjectIDZND0="25553@x" ObjectIDZND1="g_1cdf0b0@0" ObjectIDZND2="g_164d310@0" Pin0InfoVect0LinkObjId="SW-145565_0" Pin0InfoVect1LinkObjId="g_1cdf0b0_0" Pin0InfoVect2LinkObjId="g_164d310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145567_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="370,-5026 389,-5026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_164d0b0">
     <polyline DF8003:Layer="0" fill="none" points="389,-4946 373,-4946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_16517c0@0" ObjectIDND1="25797@x" ObjectIDND2="25798@x" ObjectIDZND0="g_164d310@0" Pin0InfoVect0LinkObjId="g_164d310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16517c0_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-147614_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="389,-4946 373,-4946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_164e0c0">
     <polyline DF8003:Layer="0" fill="none" points="518,-5010 533,-5010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1cee150@0" ObjectIDZND0="25797@0" Pin0InfoVect0LinkObjId="SW-147613_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cee150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-5010 533,-5010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1650810">
     <polyline DF8003:Layer="0" fill="none" points="569,-5010 588,-5010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25797@1" ObjectIDZND0="g_16517c0@0" ObjectIDZND1="23741@x" ObjectIDZND2="23739@x" Pin0InfoVect0LinkObjId="g_16517c0_0" Pin0InfoVect1LinkObjId="SW-129580_0" Pin0InfoVect2LinkObjId="SW-129578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147613_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="569,-5010 588,-5010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1651300">
     <polyline DF8003:Layer="0" fill="none" points="587,-5022 587,-5010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25798@0" ObjectIDZND0="g_16517c0@0" ObjectIDZND1="23741@x" ObjectIDZND2="23739@x" Pin0InfoVect0LinkObjId="g_16517c0_0" Pin0InfoVect1LinkObjId="SW-129580_0" Pin0InfoVect2LinkObjId="SW-129578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147614_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="587,-5022 587,-5010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1651560">
     <polyline DF8003:Layer="0" fill="none" points="587,-4989 576,-4989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDND2="23741@x" ObjectIDZND0="g_16517c0@0" Pin0InfoVect0LinkObjId="g_16517c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="SW-129580_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4989 576,-4989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1652e00">
     <polyline DF8003:Layer="0" fill="none" points="587,-5010 587,-4989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDZND0="g_16517c0@0" ObjectIDZND1="23741@x" ObjectIDZND2="23739@x" Pin0InfoVect0LinkObjId="g_16517c0_0" Pin0InfoVect1LinkObjId="SW-129580_0" Pin0InfoVect2LinkObjId="SW-129578_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="587,-5010 587,-4989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16548b0">
     <polyline DF8003:Layer="0" fill="none" points="587,-4989 587,-4964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_16517c0@0" ObjectIDND1="25797@x" ObjectIDND2="25798@x" ObjectIDZND0="23741@x" ObjectIDZND1="23739@x" ObjectIDZND2="g_164d310@0" Pin0InfoVect0LinkObjId="SW-129580_0" Pin0InfoVect1LinkObjId="SW-129578_0" Pin0InfoVect2LinkObjId="g_164d310_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16517c0_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-147614_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4989 587,-4964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1654b10">
     <polyline DF8003:Layer="0" fill="none" points="587,-4964 587,-4927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_16517c0@0" ObjectIDND1="25797@x" ObjectIDND2="25798@x" ObjectIDZND0="23741@x" ObjectIDZND1="23739@x" ObjectIDZND2="g_164d310@0" Pin0InfoVect0LinkObjId="SW-129580_0" Pin0InfoVect1LinkObjId="SW-129578_0" Pin0InfoVect2LinkObjId="g_164d310_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16517c0_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-147614_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4964 587,-4927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1654d70">
     <polyline DF8003:Layer="0" fill="none" points="587,-4964 573,-4964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_16517c0@0" ObjectIDND1="25797@x" ObjectIDND2="25798@x" ObjectIDZND0="g_1653060@0" Pin0InfoVect0LinkObjId="g_1653060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16517c0_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-147614_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4964 573,-4964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1654fd0">
     <polyline DF8003:Layer="0" fill="none" points="542,-4964 528,-4964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1653060@1" ObjectIDZND0="g_16538e0@0" Pin0InfoVect0LinkObjId="g_16538e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1653060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="542,-4964 528,-4964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15cc960">
     <polyline DF8003:Layer="0" fill="none" points="642,-4887 657,-4887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23741@1" ObjectIDZND0="g_15cbed0@0" Pin0InfoVect0LinkObjId="g_15cbed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="642,-4887 657,-4887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15ccbc0">
     <polyline DF8003:Layer="0" fill="none" points="587,-4887 606,-4887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_16517c0@0" ObjectIDND1="25797@x" ObjectIDND2="25798@x" ObjectIDZND0="23741@0" Pin0InfoVect0LinkObjId="SW-129580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16517c0_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-147614_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4887 606,-4887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15cd6b0">
     <polyline DF8003:Layer="0" fill="none" points="587,-4927 587,-4887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_16517c0@0" ObjectIDND1="25797@x" ObjectIDND2="25798@x" ObjectIDZND0="23741@x" ObjectIDZND1="23739@x" Pin0InfoVect0LinkObjId="SW-129580_0" Pin0InfoVect1LinkObjId="SW-129578_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16517c0_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-147614_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4927 587,-4887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d4960">
     <polyline DF8003:Layer="0" fill="none" points="587,-4779 587,-4763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23738@0" ObjectIDZND0="23740@1" Pin0InfoVect0LinkObjId="SW-129579_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4779 587,-4763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d4bc0">
     <polyline DF8003:Layer="0" fill="none" points="587,-4727 587,-4712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23740@0" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_160cc80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129579_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4727 587,-4712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d4e20">
     <polyline DF8003:Layer="0" fill="none" points="587,-4887 587,-4861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_16517c0@0" ObjectIDND1="25797@x" ObjectIDND2="25798@x" ObjectIDZND0="23739@1" Pin0InfoVect0LinkObjId="SW-129578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16517c0_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-147614_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4887 587,-4861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d5080">
     <polyline DF8003:Layer="0" fill="none" points="587,-4825 587,-4806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23739@0" ObjectIDZND0="23738@1" Pin0InfoVect0LinkObjId="SW-129577_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4825 587,-4806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d85c0">
     <polyline DF8003:Layer="0" fill="none" points="1230,-4581 1230,-4593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1601@1" ObjectIDZND0="1599@0" Pin0InfoVect0LinkObjId="SW-9928_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9931_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-4581 1230,-4593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15eb9b0">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4709 2550,-4692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4709 2550,-4692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15ec490">
     <polyline DF8003:Layer="0" fill="none" points="2549,-4520 2531,-4520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_15ebc10@1" Pin0InfoVect0LinkObjId="g_15ebc10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2549,-4520 2531,-4520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15ec6f0">
     <polyline DF8003:Layer="0" fill="none" points="2500,-4520 2484,-4520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_15ebc10@0" ObjectIDZND0="g_15ec950@0" Pin0InfoVect0LinkObjId="g_15ec950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2500,-4520 2484,-4520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cba5e0">
     <polyline DF8003:Layer="0" fill="none" points="2577,-4268 2550,-4268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="g_1cb9930@0" ObjectIDZND0="0@x" ObjectIDZND1="g_16d0170@0" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16d0170_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cb9930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-4268 2550,-4268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cbb0b0">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4293 2550,-4268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="0@0" ObjectIDZND0="g_1cb9930@0" ObjectIDZND1="g_16d0170@0" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="g_1cb9930_0" Pin0InfoVect1LinkObjId="g_16d0170_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4293 2550,-4268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cbb2f0">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4837 2550,-4791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4837 2550,-4791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cbb550">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4755 2550,-4736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4755 2550,-4736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cc3250">
     <polyline DF8003:Layer="0" fill="none" points="2287,-4985 2287,-4966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2287,-4985 2287,-4966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cc3440">
     <polyline DF8003:Layer="0" fill="none" points="2287,-4939 2287,-4922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2287,-4939 2287,-4922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cc3630">
     <polyline DF8003:Layer="0" fill="none" points="2287,-4886 2287,-4837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2287,-4886 2287,-4837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cc6e80">
     <polyline DF8003:Layer="0" fill="none" points="2391,-4837 2391,-4815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2391,-4837 2391,-4815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cc7e30">
     <polyline DF8003:Layer="0" fill="none" points="2391,-4779 2391,-4763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1cc70e0@0" Pin0InfoVect0LinkObjId="g_1cc70e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2391,-4779 2391,-4763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cc8090">
     <polyline DF8003:Layer="0" fill="none" points="2391,-4709 2391,-4698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_1cc70e0@1" ObjectIDZND0="g_1cc82f0@0" Pin0InfoVect0LinkObjId="g_1cc82f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cc70e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2391,-4709 2391,-4698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cc9940">
     <polyline DF8003:Layer="0" fill="none" points="2334,-5058 2350,-5058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1cc9370@0" ObjectIDZND0="g_1cc9ba0@0" Pin0InfoVect0LinkObjId="g_1cc9ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cc9370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2334,-5058 2350,-5058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd3b30">
     <polyline DF8003:Layer="0" fill="none" points="2488,-5114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2488,-5114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd3d20">
     <polyline DF8003:Layer="0" fill="none" points="2488,-5007 2488,-4990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2488,-5007 2488,-4990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd3f10">
     <polyline DF8003:Layer="0" fill="none" points="2488,-5114 2488,-5089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2488,-5114 2488,-5089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd4100">
     <polyline DF8003:Layer="0" fill="none" points="2488,-5053 2488,-5034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2488,-5053 2488,-5034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd6890">
     <polyline DF8003:Layer="0" fill="none" points="2488,-4859 2488,-4837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2488,-4859 2488,-4837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd78a0">
     <polyline DF8003:Layer="0" fill="none" points="2465,-4918 2487,-4918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1cd6af0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cd6af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2465,-4918 2487,-4918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd8390">
     <polyline DF8003:Layer="0" fill="none" points="2488,-4954 2488,-4918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1cd6af0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1cd6af0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2488,-4954 2488,-4918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd85f0">
     <polyline DF8003:Layer="0" fill="none" points="2488,-4918 2488,-4894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1cd6af0@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cd6af0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2488,-4918 2488,-4894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cda040">
     <polyline DF8003:Layer="0" fill="none" points="2018,-4187 1998,-4187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="23946@0" ObjectIDZND0="23945@x" ObjectIDZND1="g_1cdbe10@0" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="SW-130571_0" Pin0InfoVect1LinkObjId="g_1cdbe10_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130572_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2018,-4187 1998,-4187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cda230">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4187 1998,-4170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="busSection" EndDevType0="switch" ObjectIDND0="23946@x" ObjectIDND1="g_1cdbe10@0" ObjectIDND2="9682@0" ObjectIDZND0="23945@1" Pin0InfoVect0LinkObjId="SW-130571_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130572_0" Pin1InfoVect1LinkObjId="g_1cdbe10_0" Pin1InfoVect2LinkObjId="g_16a6760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4187 1998,-4170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cda420">
     <polyline DF8003:Layer="0" fill="none" points="1415,-4278 1405,-4278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="23854@0" ObjectIDZND0="23855@x" ObjectIDZND1="g_1cf05c0@0" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="SW-130138_0" Pin0InfoVect1LinkObjId="g_1cf05c0_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130136_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1415,-4278 1405,-4278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdc330">
     <polyline DF8003:Layer="0" fill="none" points="1956,-4394 1944,-4394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1cdbe10@0" ObjectIDZND0="g_1cdc590@0" Pin0InfoVect0LinkObjId="g_1cdc590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cdbe10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1956,-4394 1944,-4394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdcb70">
     <polyline DF8003:Layer="0" fill="none" points="1987,-4394 1998,-4394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="busSection" ObjectIDND0="g_1cdbe10@1" ObjectIDZND0="23946@x" ObjectIDZND1="23945@x" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="SW-130572_0" Pin0InfoVect1LinkObjId="SW-130571_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cdbe10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1987,-4394 1998,-4394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdcdd0">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4471 1998,-4394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="9682@0" ObjectIDZND0="g_1cdbe10@0" ObjectIDZND1="23946@x" ObjectIDZND2="23945@x" Pin0InfoVect0LinkObjId="g_1cdbe10_0" Pin0InfoVect1LinkObjId="SW-130572_0" Pin0InfoVect2LinkObjId="SW-130571_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16a6760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4471 1998,-4394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdd030">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4187 1998,-4394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="23946@x" ObjectIDND1="23945@x" ObjectIDZND0="g_1cdbe10@0" ObjectIDZND1="9682@0" Pin0InfoVect0LinkObjId="g_1cdbe10_0" Pin0InfoVect1LinkObjId="g_16a6760_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130572_0" Pin1InfoVect1LinkObjId="SW-130571_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4187 1998,-4394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdf950">
     <polyline DF8003:Layer="0" fill="none" points="330,-5002 337,-5002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1cdea90@0" ObjectIDZND0="g_1cdf0b0@0" Pin0InfoVect0LinkObjId="g_1cdf0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cdea90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="330,-5002 337,-5002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cebc20">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4301 1609,-4301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1ce9cc0@0" ObjectIDND1="24050@x" ObjectIDND2="24047@x" ObjectIDZND0="g_1ceb320@0" Pin0InfoVect0LinkObjId="g_1ceb320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ce9cc0_0" Pin1InfoVect1LinkObjId="SW-131207_0" Pin1InfoVect2LinkObjId="SW-131204_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4301 1609,-4301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cebe80">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4301 1585,-4301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1ceb320@0" ObjectIDND1="24050@x" ObjectIDND2="24047@x" ObjectIDZND0="g_1ce9cc0@0" Pin0InfoVect0LinkObjId="g_1ce9cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ceb320_0" Pin1InfoVect1LinkObjId="SW-131207_0" Pin1InfoVect2LinkObjId="SW-131204_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4301 1585,-4301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cec0e0">
     <polyline DF8003:Layer="0" fill="none" points="1540,-4301 1526,-4301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1ce9cc0@1" ObjectIDZND0="g_1cec970@0" Pin0InfoVect0LinkObjId="g_1cec970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ce9cc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-4301 1526,-4301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cef3c0">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4209 2177,-4209 2177,-4299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="g_1cb9930@0" ObjectIDND1="0@x" ObjectIDND2="g_16da990@0" ObjectIDZND0="g_16d0170@0" ObjectIDZND1="9682@0" Pin0InfoVect0LinkObjId="g_16d0170_0" Pin0InfoVect1LinkObjId="g_16a6760_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cb9930_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_16da990_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4209 2177,-4209 2177,-4299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf03d0">
     <polyline DF8003:Layer="0" fill="none" points="1405,-4278 1405,-4262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="busSection" EndDevType0="switch" ObjectIDND0="23854@x" ObjectIDND1="g_1cf05c0@0" ObjectIDND2="9682@0" ObjectIDZND0="23855@1" Pin0InfoVect0LinkObjId="SW-130138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130136_0" Pin1InfoVect1LinkObjId="g_1cf05c0_0" Pin1InfoVect2LinkObjId="g_16a6760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-4278 1405,-4262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf0f10">
     <polyline DF8003:Layer="0" fill="none" points="1325,-4247 1325,-4255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1cf05c0@0" Pin0InfoVect0LinkObjId="g_1cf05c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1325,-4247 1325,-4255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf1a00">
     <polyline DF8003:Layer="0" fill="none" points="1405,-4309 1405,-4278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1cf05c0@0" ObjectIDND1="9682@0" ObjectIDZND0="23854@x" ObjectIDZND1="23855@x" Pin0InfoVect0LinkObjId="SW-130136_0" Pin0InfoVect1LinkObjId="SW-130138_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cf05c0_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-4309 1405,-4278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf4230">
     <polyline DF8003:Layer="0" fill="none" points="2334,-3467 2334,-3482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10705@0" ObjectIDZND0="29417@0" Pin0InfoVect0LinkObjId="SW-193483_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2334,-3467 2334,-3482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf4490">
     <polyline DF8003:Layer="0" fill="none" points="2334,-3518 2334,-3534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29417@1" ObjectIDZND0="29416@0" Pin0InfoVect0LinkObjId="SW-193482_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193483_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2334,-3518 2334,-3534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf46f0">
     <polyline DF8003:Layer="0" fill="none" points="2334,-3561 2334,-3579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29416@1" ObjectIDZND0="29418@0" Pin0InfoVect0LinkObjId="SW-193484_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193482_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2334,-3561 2334,-3579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf9950">
     <polyline DF8003:Layer="0" fill="none" points="2265,-3641 2252,-3641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1cfa2b0@1" ObjectIDZND0="g_1cf9bb0@0" Pin0InfoVect0LinkObjId="g_1cf9bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cfa2b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2265,-3641 2252,-3641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfab30">
     <polyline DF8003:Layer="0" fill="none" points="2382,-3641 2397,-3641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29421@1" ObjectIDZND0="g_1cfad90@0" Pin0InfoVect0LinkObjId="g_1cfad90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193487_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2382,-3641 2397,-3641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfe800">
     <polyline DF8003:Layer="0" fill="none" points="2346,-3641 2334,-3641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29421@0" ObjectIDZND0="29418@x" ObjectIDZND1="g_1cfa2b0@0" ObjectIDZND2="g_1cfff70@0" Pin0InfoVect0LinkObjId="SW-193484_0" Pin0InfoVect1LinkObjId="g_1cfa2b0_0" Pin0InfoVect2LinkObjId="g_1cfff70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193487_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2346,-3641 2334,-3641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfea60">
     <polyline DF8003:Layer="0" fill="none" points="1607,-3506 1607,-3520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1cff3c0@1" ObjectIDZND0="g_1cfecc0@0" Pin0InfoVect0LinkObjId="g_1cfecc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cff3c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1607,-3506 1607,-3520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0a7e0">
     <polyline DF8003:Layer="0" fill="none" points="1423,-3452 1423,-3498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29424@0" ObjectIDZND0="24135@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1423,-3452 1423,-3498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0c280">
     <polyline DF8003:Layer="0" fill="none" points="1551,-3394 1551,-3378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29426@0" ObjectIDZND0="g_1d09d90@0" Pin0InfoVect0LinkObjId="g_1d09d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-3394 1551,-3378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d0df10">
     <polyline DF8003:Layer="0" fill="none" points="2500,-4642 2486,-4642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1d0e100@0" Pin0InfoVect0LinkObjId="g_1d0e100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2500,-4642 2486,-4642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0e920">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4642 2534,-4642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4642 2534,-4642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0eb50">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4642 2550,-4635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4642 2550,-4635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d11b80">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4656 2550,-4642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4656 2550,-4642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d12670">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4642 2550,-4635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4642 2550,-4635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d13140">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4550 2550,-4520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_15ebc10@0" ObjectIDZND1="g_15ed090@0" Pin0InfoVect0LinkObjId="g_15ebc10_0" Pin0InfoVect1LinkObjId="g_15ed090_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4550 2550,-4520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d16bf0">
     <polyline DF8003:Layer="0" fill="none" points="2500,-4360 2486,-4360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1d14070@0" Pin0InfoVect0LinkObjId="g_1d14070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2500,-4360 2486,-4360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d176e0">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4635 2550,-4600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4635 2550,-4600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d17940">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4600 2550,-4594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4600 2550,-4594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d18430">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4600 2550,-4594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4600 2550,-4594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d19980">
     <polyline DF8003:Layer="0" fill="none" points="1772,-4286 1754,-4286 1754,-4290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d48a70@1" ObjectIDZND0="g_1d49060@0" Pin0InfoVect0LinkObjId="g_1d49060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d48a70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1772,-4286 1754,-4286 1754,-4290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1dbd0">
     <polyline DF8003:Layer="0" fill="none" points="1655,-4198 1668,-4198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24049@1" ObjectIDZND0="g_1d1d140@0" Pin0InfoVect0LinkObjId="g_1d1d140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1655,-4198 1668,-4198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1de30">
     <polyline DF8003:Layer="0" fill="none" points="1619,-4198 1592,-4198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24049@0" ObjectIDZND0="24047@x" ObjectIDZND1="24045@x" Pin0InfoVect0LinkObjId="SW-131204_0" Pin0InfoVect1LinkObjId="SW-131202_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1619,-4198 1592,-4198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1e920">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4216 1592,-4198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24047@0" ObjectIDZND0="24049@x" ObjectIDZND1="24045@x" Pin0InfoVect0LinkObjId="SW-131206_0" Pin0InfoVect1LinkObjId="SW-131202_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4216 1592,-4198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1eb80">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4198 1592,-4185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24049@x" ObjectIDND1="24047@x" ObjectIDZND0="24045@1" Pin0InfoVect0LinkObjId="SW-131202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131206_0" Pin1InfoVect1LinkObjId="SW-131204_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4198 1592,-4185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1f870">
     <polyline DF8003:Layer="0" fill="none" points="1655,-4144 1668,-4144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24048@1" ObjectIDZND0="g_1d1ede0@0" Pin0InfoVect0LinkObjId="g_1d1ede0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131205_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1655,-4144 1668,-4144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1fad0">
     <polyline DF8003:Layer="0" fill="none" points="1619,-4144 1592,-4144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24048@0" ObjectIDZND0="24048@x" ObjectIDZND1="24046@x" ObjectIDZND2="24048@x" Pin0InfoVect0LinkObjId="SW-131205_0" Pin0InfoVect1LinkObjId="SW-131203_0" Pin0InfoVect2LinkObjId="SW-131205_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131205_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1619,-4144 1592,-4144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1fd30">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4144 1592,-4131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24048@x" ObjectIDND1="24045@x" ObjectIDND2="24048@x" ObjectIDZND0="24048@x" ObjectIDZND1="24046@x" ObjectIDZND2="24048@x" Pin0InfoVect0LinkObjId="SW-131205_0" Pin0InfoVect1LinkObjId="SW-131203_0" Pin0InfoVect2LinkObjId="SW-131205_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131205_0" Pin1InfoVect1LinkObjId="SW-131202_0" Pin1InfoVect2LinkObjId="SW-131205_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4144 1592,-4131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d22f70">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4158 1592,-4144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24045@0" ObjectIDZND0="24048@x" ObjectIDZND1="24048@x" ObjectIDZND2="24046@x" Pin0InfoVect0LinkObjId="SW-131205_0" Pin0InfoVect1LinkObjId="SW-131205_0" Pin0InfoVect2LinkObjId="SW-131203_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4158 1592,-4144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d23a60">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4144 1592,-4131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="24048@x" ObjectIDND1="24048@x" ObjectIDND2="24046@x" ObjectIDZND0="24048@x" ObjectIDZND1="24045@x" ObjectIDZND2="24046@x" Pin0InfoVect0LinkObjId="SW-131205_0" Pin0InfoVect1LinkObjId="SW-131202_0" Pin0InfoVect2LinkObjId="SW-131203_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131205_0" Pin1InfoVect1LinkObjId="SW-131205_0" Pin1InfoVect2LinkObjId="SW-131203_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4144 1592,-4131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d23cc0">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4131 1592,-4123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24048@x" ObjectIDND1="24045@x" ObjectIDND2="24048@x" ObjectIDZND0="24046@1" Pin0InfoVect0LinkObjId="SW-131203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131205_0" Pin1InfoVect1LinkObjId="SW-131202_0" Pin1InfoVect2LinkObjId="SW-131205_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4131 1592,-4123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d25140">
     <polyline DF8003:Layer="0" fill="none" points="1615,-4275 1592,-4275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="24050@0" ObjectIDZND0="g_1ceb320@0" ObjectIDZND1="g_1ce9cc0@0" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="g_1ceb320_0" Pin0InfoVect1LinkObjId="g_1ce9cc0_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1615,-4275 1592,-4275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d25c30">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4304 1592,-4275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ceb320@0" ObjectIDND1="g_1ce9cc0@0" ObjectIDND2="9682@0" ObjectIDZND0="24050@x" ObjectIDZND1="24047@x" Pin0InfoVect0LinkObjId="SW-131207_0" Pin0InfoVect1LinkObjId="SW-131204_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ceb320_0" Pin1InfoVect1LinkObjId="g_1ce9cc0_0" Pin1InfoVect2LinkObjId="g_16a6760_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4304 1592,-4275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d25e90">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4275 1592,-4252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24050@x" ObjectIDND1="g_1ceb320@0" ObjectIDND2="g_1ce9cc0@0" ObjectIDZND0="24047@1" Pin0InfoVect0LinkObjId="SW-131204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131207_0" Pin1InfoVect1LinkObjId="g_1ceb320_0" Pin1InfoVect2LinkObjId="g_1ce9cc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4275 1592,-4252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d260f0">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4087 1592,-4073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="24042@0" Pin0InfoVect0LinkObjId="g_1d7b8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4087 1592,-4073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d26350">
     <polyline DF8003:Layer="0" fill="none" points="1540,-4073 1540,-4060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24042@0" ObjectIDZND0="24058@1" Pin0InfoVect0LinkObjId="SW-131254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d260f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-4073 1540,-4060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2d5c0">
     <polyline DF8003:Layer="0" fill="none" points="1540,-4025 1540,-4011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24058@0" ObjectIDZND0="24057@1" Pin0InfoVect0LinkObjId="SW-131253_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131254_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-4025 1540,-4011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2d820">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3984 1540,-3972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24057@0" ObjectIDZND0="24059@1" Pin0InfoVect0LinkObjId="SW-131255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131253_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3984 1540,-3972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2e310">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4594 2550,-4550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_15ebc10@0" ObjectIDZND1="g_15ed090@0" Pin0InfoVect0LinkObjId="g_15ebc10_0" Pin0InfoVect1LinkObjId="g_15ed090_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4594 2550,-4550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2e570">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4550 2550,-4515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_15ebc10@0" ObjectIDND1="g_15ed090@0" ObjectIDND2="0@x" ObjectIDZND0="g_15ebc10@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_15ebc10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15ebc10_0" Pin1InfoVect1LinkObjId="g_15ed090_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4550 2550,-4515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2e7d0">
     <polyline DF8003:Layer="0" fill="none" points="2536,-4491 2550,-4491 2550,-4520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_15ed090@0" ObjectIDZND0="g_15ebc10@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_15ebc10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ed090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-4491 2550,-4491 2550,-4520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d2ea30">
     <polyline DF8003:Layer="0" fill="none" points="2303,-5058 2268,-5058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1cc9370@1" ObjectIDZND0="g_1ccaf60@0" Pin0InfoVect0LinkObjId="g_1ccaf60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cc9370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2303,-5058 2268,-5058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2ec90">
     <polyline DF8003:Layer="0" fill="none" points="1225,-5048 1225,-5147 802,-5147 802,-4876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_18f3ce0@0" ObjectIDND1="7005@x" ObjectIDND2="7004@x" ObjectIDZND0="23744@x" ObjectIDZND1="23743@x" Pin0InfoVect0LinkObjId="SW-129590_0" Pin0InfoVect1LinkObjId="SW-129589_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18f3ce0_0" Pin1InfoVect1LinkObjId="SW-42343_0" Pin1InfoVect2LinkObjId="SW-42342_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-5048 1225,-5147 802,-5147 802,-4876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2ef00">
     <polyline DF8003:Layer="0" fill="none" points="1325,-4300 1325,-4309 1405,-4309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="busSection" ObjectIDND0="g_1cf05c0@1" ObjectIDZND0="23854@x" ObjectIDZND1="23855@x" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="SW-130136_0" Pin0InfoVect1LinkObjId="SW-130138_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cf05c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1325,-4300 1325,-4309 1405,-4309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d2f160">
     <polyline DF8003:Layer="0" fill="none" points="2287,-5150 2287,-5021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2287,-5150 2287,-5021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2fc50">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3916 1540,-3936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24060@x" ObjectIDND1="g_1d35da0@0" ObjectIDND2="24140@x" ObjectIDZND0="24059@0" Pin0InfoVect0LinkObjId="SW-131255_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131256_0" Pin1InfoVect1LinkObjId="g_1d35da0_0" Pin1InfoVect2LinkObjId="SW-131661_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3916 1540,-3936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2feb0">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3916 1560,-3916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24059@x" ObjectIDND1="g_1d35da0@0" ObjectIDND2="24140@x" ObjectIDZND0="24060@0" Pin0InfoVect0LinkObjId="SW-131256_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131255_0" Pin1InfoVect1LinkObjId="g_1d35da0_0" Pin1InfoVect2LinkObjId="SW-131661_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3916 1560,-3916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d30110">
     <polyline DF8003:Layer="0" fill="none" points="1596,-3916 1611,-3916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24060@1" ObjectIDZND0="g_17002c0@0" Pin0InfoVect0LinkObjId="g_17002c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131256_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1596,-3916 1611,-3916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d30370">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4471 1820,-4287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="9682@0" ObjectIDZND0="g_1d48a70@0" ObjectIDZND1="23995@x" ObjectIDZND2="23994@x" Pin0InfoVect0LinkObjId="g_1d48a70_0" Pin0InfoVect1LinkObjId="SW-130914_0" Pin0InfoVect2LinkObjId="SW-130913_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16a6760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4471 1820,-4287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d39a40">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3908 1528,-3908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="24059@x" ObjectIDND1="24060@x" ObjectIDND2="24140@x" ObjectIDZND0="g_1d35da0@0" Pin0InfoVect0LinkObjId="g_1d35da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131255_0" Pin1InfoVect1LinkObjId="SW-131256_0" Pin1InfoVect2LinkObjId="SW-131661_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3908 1528,-3908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d39ca0">
     <polyline DF8003:Layer="0" fill="none" points="1497,-3908 1486,-3908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d35da0@1" ObjectIDZND0="g_1d39f00@0" Pin0InfoVect0LinkObjId="g_1d39f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d35da0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1497,-3908 1486,-3908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3ae50">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3908 1540,-3916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1d35da0@0" ObjectIDND1="24140@x" ObjectIDND2="24139@x" ObjectIDZND0="24059@x" ObjectIDZND1="24060@x" Pin0InfoVect0LinkObjId="SW-131255_0" Pin0InfoVect1LinkObjId="SW-131256_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d35da0_0" Pin1InfoVect1LinkObjId="SW-131661_0" Pin1InfoVect2LinkObjId="SW-131651_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3908 1540,-3916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d40750">
     <polyline DF8003:Layer="0" fill="none" points="1330,-3717 1347,-3717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24195@1" ObjectIDZND0="24140@x" ObjectIDZND1="24139@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-131661_0" Pin0InfoVect1LinkObjId="SW-131651_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-3717 1347,-3717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d409b0">
     <polyline DF8003:Layer="0" fill="none" points="1347,-3717 1540,-3717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24195@x" ObjectIDND1="0@x" ObjectIDZND0="24140@x" ObjectIDZND1="24139@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-131661_0" Pin0InfoVect1LinkObjId="SW-131651_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132006_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1347,-3717 1540,-3717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d40c10">
     <polyline DF8003:Layer="0" fill="none" points="1347,-3652 1347,-3662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1347,-3652 1347,-3662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d40e70">
     <polyline DF8003:Layer="0" fill="none" points="1347,-3707 1347,-3717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="24195@x" ObjectIDZND1="24140@x" ObjectIDZND2="24139@x" Pin0InfoVect0LinkObjId="SW-132006_0" Pin0InfoVect1LinkObjId="SW-131661_0" Pin0InfoVect2LinkObjId="SW-131651_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1347,-3707 1347,-3717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d435a0">
     <polyline DF8003:Layer="0" fill="none" points="1539,-3689 1549,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24195@x" ObjectIDND1="0@x" ObjectIDND2="g_1d35da0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132006_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1d35da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-3689 1549,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d46400">
     <polyline DF8003:Layer="0" fill="none" points="1605,-3696 1605,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1d45a30@0" ObjectIDZND0="0@x" ObjectIDZND1="g_16ae380@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16ae380_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d45a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-3696 1605,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d46ef0">
     <polyline DF8003:Layer="0" fill="none" points="1585,-3689 1605,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1d45a30@0" ObjectIDZND1="g_16ae380@0" Pin0InfoVect0LinkObjId="g_1d45a30_0" Pin0InfoVect1LinkObjId="g_16ae380_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1585,-3689 1605,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d47150">
     <polyline DF8003:Layer="0" fill="none" points="1605,-3689 1620,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d45a30@0" ObjectIDND1="0@x" ObjectIDZND0="g_16ae380@1" Pin0InfoVect0LinkObjId="g_16ae380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d45a30_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-3689 1620,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4a990">
     <polyline DF8003:Layer="0" fill="none" points="2948,-4209 2878,-4209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="g_16da990@0" ObjectIDZND1="3691@x" ObjectIDZND2="g_16d0170@0" Pin0InfoVect0LinkObjId="g_16da990_0" Pin0InfoVect1LinkObjId="SW-23158_0" Pin0InfoVect2LinkObjId="g_16d0170_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2948,-4209 2878,-4209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4abf0">
     <polyline DF8003:Layer="0" fill="none" points="2878,-4209 2878,-4312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_16d0170@0" ObjectIDND1="9682@0" ObjectIDND2="g_1cb9930@0" ObjectIDZND0="g_16da990@0" ObjectIDZND1="3691@x" Pin0InfoVect0LinkObjId="g_16da990_0" Pin0InfoVect1LinkObjId="SW-23158_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16d0170_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="g_1cb9930_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2878,-4209 2878,-4312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4ae50">
     <polyline DF8003:Layer="0" fill="none" points="2287,-5183 2287,-5233 2265,-5233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1cca1b0@0" Pin0InfoVect0LinkObjId="g_1cca1b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2287,-5183 2287,-5233 2265,-5233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4b940">
     <polyline DF8003:Layer="0" fill="none" points="389,-5039 389,-5026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25553@0" ObjectIDZND0="25555@x" ObjectIDZND1="g_1cdf0b0@0" ObjectIDZND2="g_164d310@0" Pin0InfoVect0LinkObjId="SW-145567_0" Pin0InfoVect1LinkObjId="g_1cdf0b0_0" Pin0InfoVect2LinkObjId="g_164d310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145565_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="389,-5039 389,-5026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4c430">
     <polyline DF8003:Layer="0" fill="none" points="368,-5002 389,-5002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1cdf0b0@1" ObjectIDZND0="25555@x" ObjectIDZND1="25553@x" ObjectIDZND2="g_164d310@0" Pin0InfoVect0LinkObjId="SW-145567_0" Pin0InfoVect1LinkObjId="SW-145565_0" Pin0InfoVect2LinkObjId="g_164d310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cdf0b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="368,-5002 389,-5002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4c690">
     <polyline DF8003:Layer="0" fill="none" points="389,-5002 389,-5026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1cdf0b0@0" ObjectIDND1="g_164d310@0" ObjectIDND2="g_16517c0@0" ObjectIDZND0="25555@x" ObjectIDZND1="25553@x" Pin0InfoVect0LinkObjId="SW-145567_0" Pin0InfoVect1LinkObjId="SW-145565_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cdf0b0_0" Pin1InfoVect1LinkObjId="g_164d310_0" Pin1InfoVect2LinkObjId="g_16517c0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="389,-5002 389,-5026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4d180">
     <polyline DF8003:Layer="0" fill="none" points="587,-4927 389,-4927 389,-4946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_16517c0@0" ObjectIDND1="25797@x" ObjectIDND2="25798@x" ObjectIDZND0="g_164d310@0" ObjectIDZND1="g_1cdf0b0@0" ObjectIDZND2="25555@x" Pin0InfoVect0LinkObjId="g_164d310_0" Pin0InfoVect1LinkObjId="g_1cdf0b0_0" Pin0InfoVect2LinkObjId="SW-145567_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16517c0_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-147614_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="587,-4927 389,-4927 389,-4946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4d3e0">
     <polyline DF8003:Layer="0" fill="none" points="389,-4946 389,-5002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_164d310@0" ObjectIDND1="g_16517c0@0" ObjectIDND2="25797@x" ObjectIDZND0="g_1cdf0b0@0" ObjectIDZND1="25555@x" ObjectIDZND2="25553@x" Pin0InfoVect0LinkObjId="g_1cdf0b0_0" Pin0InfoVect1LinkObjId="SW-145567_0" Pin0InfoVect2LinkObjId="SW-145565_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_164d310_0" Pin1InfoVect1LinkObjId="g_16517c0_0" Pin1InfoVect2LinkObjId="SW-147613_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="389,-4946 389,-5002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d50a90">
     <polyline DF8003:Layer="0" fill="none" points="2334,-3642 2334,-3615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29421@x" ObjectIDND1="g_1cfa2b0@0" ObjectIDND2="g_1cfff70@0" ObjectIDZND0="29418@1" Pin0InfoVect0LinkObjId="SW-193484_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193487_0" Pin1InfoVect1LinkObjId="g_1cfa2b0_0" Pin1InfoVect2LinkObjId="g_1cfff70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2334,-3642 2334,-3615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d58510">
     <polyline DF8003:Layer="0" fill="none" points="1957,-3526 1957,-3539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7350@0" ObjectIDZND0="28099@0" Pin0InfoVect0LinkObjId="SW-185040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1957,-3526 1957,-3539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d58770">
     <polyline DF8003:Layer="0" fill="none" points="1957,-3575 1957,-3589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28099@1" ObjectIDZND0="28097@0" Pin0InfoVect0LinkObjId="SW-185038_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1957,-3575 1957,-3589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d59a50">
     <polyline DF8003:Layer="0" fill="none" points="1987,-3704 1957,-3704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d59480@1" ObjectIDZND0="g_1d59f10@0" ObjectIDZND1="28098@x" ObjectIDZND2="23950@x" Pin0InfoVect0LinkObjId="g_1d59f10_0" Pin0InfoVect1LinkObjId="SW-185041_0" Pin0InfoVect2LinkObjId="SW-130600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d59480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1987,-3704 1957,-3704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d59cb0">
     <polyline DF8003:Layer="0" fill="none" points="1925,-3677 1925,-3704 1957,-3704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d59f10@0" ObjectIDZND0="g_1d59480@0" ObjectIDZND1="28098@x" ObjectIDZND2="23950@x" Pin0InfoVect0LinkObjId="g_1d59480_0" Pin0InfoVect1LinkObjId="SW-185041_0" Pin0InfoVect2LinkObjId="SW-130600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d59f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1925,-3677 1925,-3704 1957,-3704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5c8c0">
     <polyline DF8003:Layer="0" fill="none" points="1957,-3671 1957,-3704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28098@1" ObjectIDZND0="g_1d59f10@0" ObjectIDZND1="g_1d59480@0" ObjectIDZND2="23950@x" Pin0InfoVect0LinkObjId="g_1d59f10_0" Pin0InfoVect1LinkObjId="g_1d59480_0" Pin0InfoVect2LinkObjId="SW-130600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185041_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1957,-3671 1957,-3704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5cab0">
     <polyline DF8003:Layer="0" fill="none" points="2046,-3685 2046,-3704 2019,-3704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1d5ac00@0" ObjectIDZND0="g_1d59480@0" Pin0InfoVect0LinkObjId="g_1d59480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5ac00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2046,-3685 2046,-3704 2019,-3704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5cca0">
     <polyline DF8003:Layer="0" fill="none" points="1551,-3428 1551,-3452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="29425@x" ObjectIDZND1="g_1cfff70@0" ObjectIDZND2="29421@x" Pin0InfoVect0LinkObjId="SW-193514_0" Pin0InfoVect1LinkObjId="g_1cfff70_0" Pin0InfoVect2LinkObjId="SW-193487_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-3428 1551,-3452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5ced0">
     <polyline DF8003:Layer="0" fill="none" points="2296,-3641 2334,-3641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1cfa2b0@0" ObjectIDZND0="29421@x" ObjectIDZND1="29418@x" ObjectIDZND2="g_1cfff70@0" Pin0InfoVect0LinkObjId="SW-193487_0" Pin0InfoVect1LinkObjId="SW-193484_0" Pin0InfoVect2LinkObjId="g_1cfff70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cfa2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2296,-3641 2334,-3641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5d8e0">
     <polyline DF8003:Layer="0" fill="none" points="2193,-4299 2177,-4299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_16d0170@1" ObjectIDZND0="g_1cb9930@0" ObjectIDZND1="0@x" ObjectIDZND2="g_16da990@0" Pin0InfoVect0LinkObjId="g_1cb9930_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_16da990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16d0170_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-4299 2177,-4299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5db30">
     <polyline DF8003:Layer="0" fill="none" points="2177,-4299 2177,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="busSection" ObjectIDND0="g_16d0170@0" ObjectIDND1="g_1cb9930@0" ObjectIDND2="0@x" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16d0170_0" Pin1InfoVect1LinkObjId="g_1cb9930_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2177,-4299 2177,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5e600">
     <polyline DF8003:Layer="0" fill="none" points="1225,-5035 1225,-5048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_18f3ce0@0" ObjectIDND1="7005@x" ObjectIDND2="7004@x" ObjectIDZND0="7006@x" ObjectIDZND1="23744@x" ObjectIDZND2="23743@x" Pin0InfoVect0LinkObjId="SW-42344_0" Pin0InfoVect1LinkObjId="SW-129590_0" Pin0InfoVect2LinkObjId="SW-129589_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18f3ce0_0" Pin1InfoVect1LinkObjId="SW-42343_0" Pin1InfoVect2LinkObjId="SW-42342_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-5035 1225,-5048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5e860">
     <polyline DF8003:Layer="0" fill="none" points="1225,-5048 1238,-5048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_18f3ce0@0" ObjectIDND1="7005@x" ObjectIDND2="7004@x" ObjectIDZND0="7006@0" Pin0InfoVect0LinkObjId="SW-42344_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18f3ce0_0" Pin1InfoVect1LinkObjId="SW-42343_0" Pin1InfoVect2LinkObjId="SW-42342_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-5048 1238,-5048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5f330">
     <polyline DF8003:Layer="0" fill="none" points="822,-4876 802,-4876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23744@0" ObjectIDZND0="23743@x" ObjectIDZND1="g_18f3ce0@0" ObjectIDZND2="7005@x" Pin0InfoVect0LinkObjId="SW-129589_0" Pin0InfoVect1LinkObjId="g_18f3ce0_0" Pin0InfoVect2LinkObjId="SW-42343_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="822,-4876 802,-4876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5f570">
     <polyline DF8003:Layer="0" fill="none" points="802,-4876 802,-4859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23744@x" ObjectIDND1="g_18f3ce0@0" ObjectIDND2="7005@x" ObjectIDZND0="23743@1" Pin0InfoVect0LinkObjId="SW-129589_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129590_0" Pin1InfoVect1LinkObjId="g_18f3ce0_0" Pin1InfoVect2LinkObjId="SW-42343_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="802,-4876 802,-4859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d62550">
     <polyline DF8003:Layer="0" fill="none" points="1957,-3871 1957,-3704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23950@x" ObjectIDND1="23949@x" ObjectIDZND0="g_1d59f10@0" ObjectIDZND1="g_1d59480@0" ObjectIDZND2="28098@x" Pin0InfoVect0LinkObjId="g_1d59f10_0" Pin0InfoVect1LinkObjId="g_1d59480_0" Pin0InfoVect2LinkObjId="SW-185041_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130600_0" Pin1InfoVect1LinkObjId="SW-130599_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1957,-3871 1957,-3704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d689d0">
     <polyline DF8003:Layer="0" fill="none" points="1957,-3616 1957,-3635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28097@1" ObjectIDZND0="28098@0" Pin0InfoVect0LinkObjId="SW-185041_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185038_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1957,-3616 1957,-3635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d68c30">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4471 1592,-4301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9682@0" ObjectIDZND0="g_1ceb320@0" ObjectIDZND1="g_1ce9cc0@0" ObjectIDZND2="24050@x" Pin0InfoVect0LinkObjId="g_1ceb320_0" Pin0InfoVect1LinkObjId="g_1ce9cc0_0" Pin0InfoVect2LinkObjId="SW-131207_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16a6760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4471 1592,-4301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d68e90">
     <polyline DF8003:Layer="0" fill="none" points="1405,-4309 1405,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="busSection" ObjectIDND0="23854@x" ObjectIDND1="23855@x" ObjectIDND2="g_1cf05c0@0" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130136_0" Pin1InfoVect1LinkObjId="SW-130138_0" Pin1InfoVect2LinkObjId="g_1cf05c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-4309 1405,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d690f0">
     <polyline DF8003:Layer="0" fill="none" points="1653,-4318 1653,-4300 1652,-4301 1640,-4301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1ceb320@1" Pin0InfoVect0LinkObjId="g_1ceb320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1653,-4318 1653,-4300 1652,-4301 1640,-4301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d69360">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3908 1540,-3717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d35da0@0" ObjectIDND1="24059@x" ObjectIDND2="24060@x" ObjectIDZND0="24140@x" ObjectIDZND1="24139@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-131661_0" Pin0InfoVect1LinkObjId="SW-131651_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d35da0_0" Pin1InfoVect1LinkObjId="SW-131255_0" Pin1InfoVect2LinkObjId="SW-131256_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3908 1540,-3717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d69e50">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4209 2550,-4268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_16d0170@0" ObjectIDND1="9682@0" ObjectIDND2="g_16da990@0" ObjectIDZND0="g_1cb9930@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1cb9930_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16d0170_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="g_16da990_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4209 2550,-4268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6a0b0">
     <polyline DF8003:Layer="0" fill="none" points="2878,-4209 2550,-4209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="busSection" EndDevType2="lightningRod" ObjectIDND0="g_16da990@0" ObjectIDND1="3691@x" ObjectIDZND0="g_16d0170@0" ObjectIDZND1="9682@0" ObjectIDZND2="g_1cb9930@0" Pin0InfoVect0LinkObjId="g_16d0170_0" Pin0InfoVect1LinkObjId="g_16a6760_0" Pin0InfoVect2LinkObjId="g_1cb9930_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16da990_0" Pin1InfoVect1LinkObjId="SW-23158_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2878,-4209 2550,-4209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6a310">
     <polyline DF8003:Layer="0" fill="none" points="2536,-4360 2550,-4360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_15ebc10@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_15ebc10_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-4360 2550,-4360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6ae00">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4329 2550,-4360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_15ebc10@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_15ebc10_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4329 2550,-4360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6b060">
     <polyline DF8003:Layer="0" fill="none" points="2550,-4360 2550,-4520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_15ebc10@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_15ebc10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-4360 2550,-4520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6b2c0">
     <polyline DF8003:Layer="0" fill="none" points="1976,-3871 1957,-3871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23950@0" ObjectIDZND0="g_1d59f10@0" ObjectIDZND1="g_1d59480@0" ObjectIDZND2="28098@x" Pin0InfoVect0LinkObjId="g_1d59f10_0" Pin0InfoVect1LinkObjId="g_1d59480_0" Pin0InfoVect2LinkObjId="SW-185041_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1976,-3871 1957,-3871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6b520">
     <polyline DF8003:Layer="0" fill="none" points="1957,-3871 1957,-3882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23950@x" ObjectIDND1="g_1d59f10@0" ObjectIDND2="g_1d59480@0" ObjectIDZND0="23949@0" Pin0InfoVect0LinkObjId="SW-130599_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130600_0" Pin1InfoVect1LinkObjId="g_1d59f10_0" Pin1InfoVect2LinkObjId="g_1d59480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1957,-3871 1957,-3882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6c010">
     <polyline DF8003:Layer="0" fill="none" points="1143,-4200 1230,-4200 1230,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="busSection" ObjectIDND0="23733@x" ObjectIDND1="23731@x" ObjectIDND2="g_1d7a6d0@0" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129558_0" Pin1InfoVect1LinkObjId="SW-129556_0" Pin1InfoVect2LinkObjId="g_1d7a6d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1143,-4200 1230,-4200 1230,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7a470">
     <polyline DF8003:Layer="0" fill="none" points="1395,-3926 1410,-3926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24056@1" ObjectIDZND0="g_1d799e0@0" Pin0InfoVect0LinkObjId="g_1d799e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1395,-3926 1410,-3926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7af50">
     <polyline DF8003:Layer="0" fill="none" points="1296,-3909 1285,-3909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d7a6d0@1" ObjectIDZND0="g_1d7b1b0@0" Pin0InfoVect0LinkObjId="g_1d7b1b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d7a6d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-3909 1285,-3909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7b8b0">
     <polyline DF8003:Layer="0" fill="none" points="1339,-4059 1339,-4073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24052@1" ObjectIDZND0="24042@0" Pin0InfoVect0LinkObjId="g_1d260f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-4059 1339,-4073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7bb10">
     <polyline DF8003:Layer="0" fill="none" points="1339,-3970 1339,-3982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24053@1" ObjectIDZND0="24051@0" Pin0InfoVect0LinkObjId="SW-131235_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131237_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-3970 1339,-3982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7bd70">
     <polyline DF8003:Layer="0" fill="none" points="1339,-4009 1339,-4023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24051@1" ObjectIDZND0="24052@0" Pin0InfoVect0LinkObjId="SW-131236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-4009 1339,-4023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7bfd0">
     <polyline DF8003:Layer="0" fill="none" points="1339,-3926 1359,-3926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24053@x" ObjectIDND1="g_1d7a6d0@0" ObjectIDND2="23733@x" ObjectIDZND0="24056@0" Pin0InfoVect0LinkObjId="SW-131240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131237_0" Pin1InfoVect1LinkObjId="g_1d7a6d0_0" Pin1InfoVect2LinkObjId="SW-129558_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-3926 1359,-3926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7c230">
     <polyline DF8003:Layer="0" fill="none" points="1327,-3909 1339,-3909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="busSection" ObjectIDND0="g_1d7a6d0@0" ObjectIDZND0="23733@x" ObjectIDZND1="23731@x" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="SW-129558_0" Pin0InfoVect1LinkObjId="SW-129556_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d7a6d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1327,-3909 1339,-3909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7da10">
     <polyline DF8003:Layer="0" fill="none" points="1339,-3909 1339,-3837 1143,-3837 1143,-4200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="busSection" ObjectIDND0="g_1d7a6d0@0" ObjectIDND1="24056@x" ObjectIDND2="24053@x" ObjectIDZND0="23733@x" ObjectIDZND1="23731@x" ObjectIDZND2="9682@0" Pin0InfoVect0LinkObjId="SW-129558_0" Pin0InfoVect1LinkObjId="SW-129556_0" Pin0InfoVect2LinkObjId="g_16a6760_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d7a6d0_0" Pin1InfoVect1LinkObjId="SW-131240_0" Pin1InfoVect2LinkObjId="SW-131237_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-3909 1339,-3837 1143,-3837 1143,-4200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7e380">
     <polyline DF8003:Layer="0" fill="none" points="1339,-3926 1339,-3934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24056@x" ObjectIDND1="g_1d7a6d0@0" ObjectIDND2="23733@x" ObjectIDZND0="24053@0" Pin0InfoVect0LinkObjId="SW-131237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131240_0" Pin1InfoVect1LinkObjId="g_1d7a6d0_0" Pin1InfoVect2LinkObjId="SW-129558_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-3926 1339,-3934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7e570">
     <polyline DF8003:Layer="0" fill="none" points="1339,-3926 1339,-3909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24056@x" ObjectIDND1="24053@x" ObjectIDZND0="g_1d7a6d0@0" ObjectIDZND1="23733@x" ObjectIDZND2="23731@x" Pin0InfoVect0LinkObjId="g_1d7a6d0_0" Pin0InfoVect1LinkObjId="SW-129558_0" Pin0InfoVect2LinkObjId="SW-129556_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131240_0" Pin1InfoVect1LinkObjId="SW-131237_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-3926 1339,-3909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b82e80">
     <polyline DF8003:Layer="0" fill="none" points="1230,-4471 1230,-4502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9682@0" ObjectIDZND0="1602@x" ObjectIDZND1="1601@x" Pin0InfoVect0LinkObjId="SW-9932_0" Pin0InfoVect1LinkObjId="SW-9931_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16a6760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-4471 1230,-4502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b830c0">
     <polyline DF8003:Layer="0" fill="none" points="1230,-4502 1230,-4545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="1602@x" ObjectIDND1="9682@0" ObjectIDZND0="1601@0" Pin0InfoVect0LinkObjId="SW-9931_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9932_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-4502 1230,-4545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b83b60">
     <polyline DF8003:Layer="0" fill="none" points="1405,-4544 1405,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="1554@0" ObjectIDZND0="1555@x" ObjectIDZND1="9682@0" Pin0InfoVect0LinkObjId="SW-9830_0" Pin0InfoVect1LinkObjId="g_16a6760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-4544 1405,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b83dc0">
     <polyline DF8003:Layer="0" fill="none" points="1405,-4501 1405,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="1555@x" ObjectIDND1="1554@x" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9830_0" Pin1InfoVect1LinkObjId="SW-9829_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1405,-4501 1405,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b84800">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4471 1592,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9682@0" ObjectIDZND0="1551@x" ObjectIDZND1="1550@x" Pin0InfoVect0LinkObjId="SW-9823_0" Pin0InfoVect1LinkObjId="SW-9822_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16a6760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4471 1592,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b84a60">
     <polyline DF8003:Layer="0" fill="none" points="1592,-4501 1592,-4543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="1551@x" ObjectIDND1="9682@0" ObjectIDZND0="1550@0" Pin0InfoVect0LinkObjId="SW-9822_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9823_0" Pin1InfoVect1LinkObjId="g_16a6760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-4501 1592,-4543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b85530">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4545 1820,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="1597@0" ObjectIDZND0="1598@x" ObjectIDZND1="9682@0" Pin0InfoVect0LinkObjId="SW-9925_0" Pin0InfoVect1LinkObjId="g_16a6760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9924_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4545 1820,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b85790">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4501 1820,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="1598@x" ObjectIDND1="1597@x" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9925_0" Pin1InfoVect1LinkObjId="SW-9924_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4501 1820,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b86260">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4545 1998,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="1546@0" ObjectIDZND0="1547@x" ObjectIDZND1="9682@0" Pin0InfoVect0LinkObjId="SW-9816_0" Pin0InfoVect1LinkObjId="g_16a6760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4545 1998,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b864c0">
     <polyline DF8003:Layer="0" fill="none" points="1998,-4501 1998,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="1547@x" ObjectIDND1="1546@x" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9816_0" Pin1InfoVect1LinkObjId="SW-9815_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1998,-4501 1998,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b86f00">
     <polyline DF8003:Layer="0" fill="none" points="2177,-4544 2177,-4501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="1604@0" ObjectIDZND0="1605@x" ObjectIDZND1="9682@0" Pin0InfoVect0LinkObjId="SW-9937_0" Pin0InfoVect1LinkObjId="g_16a6760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9936_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2177,-4544 2177,-4501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b87160">
     <polyline DF8003:Layer="0" fill="none" points="2177,-4501 2177,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="1605@x" ObjectIDND1="1604@x" ObjectIDZND0="9682@0" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-9937_0" Pin1InfoVect1LinkObjId="SW-9936_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2177,-4501 2177,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8bc40">
     <polyline DF8003:Layer="0" fill="none" points="1804,-4287 1820,-4287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d48a70@0" ObjectIDZND0="9682@0" ObjectIDZND1="23995@x" ObjectIDZND2="23994@x" Pin0InfoVect0LinkObjId="g_16a6760_0" Pin0InfoVect1LinkObjId="SW-130914_0" Pin0InfoVect2LinkObjId="SW-130913_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d48a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1804,-4287 1820,-4287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8bea0">
     <polyline DF8003:Layer="0" fill="none" points="1820,-4287 1820,-4275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9682@0" ObjectIDND1="g_1d48a70@0" ObjectIDZND0="23995@x" ObjectIDZND1="23994@x" Pin0InfoVect0LinkObjId="SW-130914_0" Pin0InfoVect1LinkObjId="SW-130913_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16a6760_0" Pin1InfoVect1LinkObjId="g_1d48a70_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-4287 1820,-4275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8c100">
     <polyline DF8003:Layer="0" fill="none" points="1588,-3442 1588,-3452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1cfff70@0" ObjectIDZND0="29425@x" ObjectIDZND1="29421@x" ObjectIDZND2="29418@x" Pin0InfoVect0LinkObjId="SW-193514_0" Pin0InfoVect1LinkObjId="SW-193487_0" Pin0InfoVect2LinkObjId="SW-193484_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cfff70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1588,-3442 1588,-3452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8c360">
     <polyline DF8003:Layer="0" fill="none" points="1608,-3452 2136,-3452 2136,-3736 2334,-3736 2334,-3641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1cfff70@0" ObjectIDND1="29425@x" ObjectIDND2="g_1cff3c0@0" ObjectIDZND0="29421@x" ObjectIDZND1="29418@x" ObjectIDZND2="g_1cfa2b0@0" Pin0InfoVect0LinkObjId="SW-193487_0" Pin0InfoVect1LinkObjId="SW-193484_0" Pin0InfoVect2LinkObjId="g_1cfa2b0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cfff70_0" Pin1InfoVect1LinkObjId="SW-193514_0" Pin1InfoVect2LinkObjId="g_1cff3c0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-3452 2136,-3452 2136,-3736 2334,-3736 2334,-3641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8cba0">
     <polyline DF8003:Layer="0" fill="none" points="1459,-3452 1468,-3452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29424@1" ObjectIDZND0="29423@1" Pin0InfoVect0LinkObjId="SW-193510_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193512_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1459,-3452 1468,-3452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8ce00">
     <polyline DF8003:Layer="0" fill="none" points="1495,-3452 1504,-3452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29423@0" ObjectIDZND0="29425@0" Pin0InfoVect0LinkObjId="SW-193514_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1495,-3452 1504,-3452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8d8f0">
     <polyline DF8003:Layer="0" fill="none" points="1551,-3452 1540,-3452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1cfff70@0" ObjectIDND1="29421@x" ObjectIDND2="29418@x" ObjectIDZND0="29425@1" Pin0InfoVect0LinkObjId="SW-193514_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cfff70_0" Pin1InfoVect1LinkObjId="SW-193487_0" Pin1InfoVect2LinkObjId="SW-193484_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-3452 1540,-3452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8db50">
     <polyline DF8003:Layer="0" fill="none" points="1551,-3452 1588,-3452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29425@x" ObjectIDZND0="g_1cfff70@0" ObjectIDZND1="29421@x" ObjectIDZND2="29418@x" Pin0InfoVect0LinkObjId="g_1cfff70_0" Pin0InfoVect1LinkObjId="SW-193487_0" Pin0InfoVect2LinkObjId="SW-193484_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-3452 1588,-3452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8eed0">
     <polyline DF8003:Layer="0" fill="none" points="1588,-3452 1608,-3452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1cfff70@0" ObjectIDND1="29425@x" ObjectIDZND0="29421@x" ObjectIDZND1="29418@x" ObjectIDZND2="g_1cfa2b0@0" Pin0InfoVect0LinkObjId="SW-193487_0" Pin0InfoVect1LinkObjId="SW-193484_0" Pin0InfoVect2LinkObjId="g_1cfa2b0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cfff70_0" Pin1InfoVect1LinkObjId="SW-193514_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1588,-3452 1608,-3452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8f130">
     <polyline DF8003:Layer="0" fill="none" points="1608,-3452 1608,-3461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29421@x" ObjectIDND1="29418@x" ObjectIDND2="g_1cfa2b0@0" ObjectIDZND0="g_1cff3c0@0" Pin0InfoVect0LinkObjId="g_1cff3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193487_0" Pin1InfoVect1LinkObjId="SW-193484_0" Pin1InfoVect2LinkObjId="g_1cfa2b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-3452 1608,-3461 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="0" busDevId="0" cx="926" cy="-4355" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25791" cx="587" cy="-5167" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25544" cx="389" cy="-5167" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="23829" cx="345" cy="-4712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="23829" cx="520" cy="-4712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="23829" cx="725" cy="-4712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="23829" cx="926" cy="-4712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="23829" cx="802" cy="-4712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="23829" cx="587" cy="-4712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="10915" cx="1225" cy="-4855" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2488" cy="-5114" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2488" cy="-5114" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2488" cy="-5114" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="3602" cx="2878" cy="-4527" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2550" cy="-4837" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2287" cy="-4837" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2391" cy="-4837" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2488" cy="-4837" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24217" cx="1256" cy="-3717" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1512" cx="1405" cy="-4679" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1512" cx="1592" cy="-4679" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1513" cx="1820" cy="-4679" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1513" cx="2177" cy="-4679" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1512" cx="1230" cy="-4679" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1285" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1461" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1648" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1874" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="2233" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="23852" cx="1405" cy="-4209" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="23991" cx="1820" cy="-4209" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1512" cx="1681" cy="-4679" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1513" cx="1783" cy="-4679" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="10705" cx="2334" cy="-3467" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24042" cx="1339" cy="-4073" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="7350" cx="1957" cy="-3526" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="2054" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1513" cx="1998" cy="-4679" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="23978" cx="1998" cy="-4023" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24042" cx="1540" cy="-4073" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24135" cx="1540" cy="-3498" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1405" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1998" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="2177" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1592" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1230" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1230" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1405" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1592" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1820" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1998" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="2177" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9682" cx="1820" cy="-4471" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24135" cx="1423" cy="-3498" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24042" cx="1592" cy="-4073" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 -5198.000000) translate(0,17)">太平变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 545.000000 -5196.000000) translate(0,17)">西教场变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 543.000000 -5099.000000) translate(0,17)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 595.000000 -5148.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 -5051.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.000000 -5040.000000) translate(0,17)">38167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 473.000000 -4922.000000) translate(0,17)">黄西太线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 385.000000 -4745.000000) translate(0,17)">35kV黄山变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -4468.000000) translate(0,17)">1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -4467.000000) translate(0,17)">2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -4393.000000) translate(0,17)">3911</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -4424.000000) translate(0,17)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 874.000000 -4347.000000) translate(0,17)">35kV大苴变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -4845.000000) translate(0,20)">吕合变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2814.000000 -4549.000000) translate(0,17)">110kV西郊变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 -4790.000000) translate(0,17)">110kV南华变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2354.000000 -4801.000000) translate(0,17)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2354.000000 -4801.000000) translate(0,38)">复</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2354.000000 -4801.000000) translate(0,59)">烤</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2354.000000 -4801.000000) translate(0,80)">厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2354.000000 -4801.000000) translate(0,101)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1375.000000 -4201.000000) translate(0,20)">沙桥变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1598.000000 -4112.000000) translate(0,17)">3621</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1409.000000 -4101.000000) translate(0,20)">大蛇腰变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1446.000000 -3527.000000) translate(0,20)">马街变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2923.000000 -4364.000000) translate(0,17)">2号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 -4346.000000) translate(0,17)">2号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2491.000000 -4321.000000) translate(0,17)">3556</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2625.000000 -4204.000000) translate(0,17)">楚南线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.000000 -4005.000000) translate(0,13)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1524.000000 -4058.000000) translate(0,13)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -3956.000000) translate(0,13)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1914.000000 -4054.000000) translate(0,20)">徐营变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1789.000000 -4202.000000) translate(0,20)">雨露变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1557.000000 -3940.000000) translate(0,13)">36167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2233.000000 -5171.000000) translate(0,17)">3546</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -4933.000000) translate(0,17)">吕黄线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1172.000000 -4230.000000) translate(0,17)">N6塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1069.000000 -4230.000000) translate(0,17)">N7塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1683.000000 -3663.000000) translate(0,13)">站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2302.000000 -3459.000000) translate(0,20)">八角变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_171bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_171bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_171bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_171bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_171bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_171bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_171bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,59)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,101)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,143)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,164)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,185)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,206)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,227)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,248)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,269)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,290)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,311)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,332)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,353)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16b4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d52e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1181.000000 -4940.000000) translate(0,17)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d5910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -4990.000000) translate(0,17)">3616</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d5b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -4892.000000) translate(0,17)">3611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d5d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1237.000000 -5080.000000) translate(0,17)">3619</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d5fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1237.000000 -5008.000000) translate(0,17)">36167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d6210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2887.000000 -4457.000000) translate(0,17)">362</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d6450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2885.000000 -4507.000000) translate(0,17)">3621</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d6690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2885.000000 -4408.000000) translate(0,17)">3626</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d68d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2205.000000 -4705.000000) translate(0,17)">II段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d7140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1168.000000 -4705.000000) translate(0,17)">I段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d7600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1715.000000 -4770.000000) translate(0,17)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d7840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1237.000000 -4658.000000) translate(0,17)">3882</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d7a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1688.000000 -4722.000000) translate(0,17)">3121</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d7cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1790.000000 -4724.000000) translate(0,17)">3122</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d7f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -4617.000000) translate(0,17)">388</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d8140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1237.000000 -4574.000000) translate(0,17)">3886</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -4531.000000) translate(0,17)">3885</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d87b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1414.000000 -4615.000000) translate(0,17)">382</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d8a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1412.000000 -4656.000000) translate(0,17)">3821</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d8ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1412.000000 -4572.000000) translate(0,17)">3826</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d8ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1414.000000 -4530.000000) translate(0,17)">3825</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d9120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1601.000000 -4614.000000) translate(0,17)">383</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d9360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1599.000000 -4655.000000) translate(0,17)">3831</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d95a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1599.000000 -4571.000000) translate(0,17)">3836</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d97e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1601.000000 -4530.000000) translate(0,17)">3835</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d9a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1829.000000 -4616.000000) translate(0,17)">386</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d9c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 -4657.000000) translate(0,17)">3862</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15d9ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 -4573.000000) translate(0,17)">3866</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15da0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 -4530.000000) translate(0,17)">3865</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15da320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2007.000000 -4616.000000) translate(0,17)">384</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15da560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2005.000000 -4657.000000) translate(0,17)">3842</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15da7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2005.000000 -4573.000000) translate(0,17)">3846</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15da9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2007.000000 -4530.000000) translate(0,17)">3845</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15dac20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2186.000000 -4615.000000) translate(0,17)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15dae60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2184.000000 -4656.000000) translate(0,17)">3811</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15db0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2184.000000 -4572.000000) translate(0,17)">3816</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15db2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2186.000000 -4530.000000) translate(0,17)">3815</text>
   <text DF8003:Layer="0" fill="rgb(38,38,38)" font-family="SimHei" font-size="20" graphid="g_15db520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -86.000000 -5212.000000) translate(0,16)">南华片区35kV电网图</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15e43c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2937.000000 -4194.000000) translate(0,17)">N1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15e4810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2556.000000 -4586.000000) translate(0,17)">复</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15e4810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2556.000000 -4586.000000) translate(0,38)">烤</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15e4810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2556.000000 -4586.000000) translate(0,59)">厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15e4810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2556.000000 -4586.000000) translate(0,80)">II</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15e4810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2556.000000 -4586.000000) translate(0,101)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_15e4810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2556.000000 -4586.000000) translate(0,122)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cc27a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2303.000000 -4963.000000) translate(0,17)">354</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cc2dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2294.000000 -5013.000000) translate(0,17)">3543</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cc3010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2294.000000 -4914.000000) translate(0,17)">3541</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cc3820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2493.000000 -4786.000000) translate(0,17)">3551</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cc3bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2500.000000 -4735.000000) translate(0,17)">355</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cc8d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2402.000000 -4809.000000) translate(0,17)">3521</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cd2c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2433.000000 -5084.000000) translate(0,17)">3122</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cd3270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2446.000000 -5032.000000) translate(0,17)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cd34b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2438.000000 -4980.000000) translate(0,17)">3121</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cd3840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2432.000000 -4886.000000) translate(0,17)">3531</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cd8850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2589.000000 -4829.000000) translate(0,17)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cd9190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2624.000000 -5108.000000) translate(0,17)">Ⅱ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1cd9700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2518.000000 -4867.000000) translate(0,20)">楚复变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cda650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1165.000000 -4491.000000) translate(0,12)">旁路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -5004.000000) translate(0,17)">复</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -5004.000000) translate(0,38)">烤</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -5004.000000) translate(0,59)">厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -5004.000000) translate(0,80)">Ⅰ</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -5004.000000) translate(0,101)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -5004.000000) translate(0,122)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2567.000000 -4784.000000) translate(0,17)">复</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2567.000000 -4784.000000) translate(0,38)">烤</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2567.000000 -4784.000000) translate(0,59)">厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2567.000000 -4784.000000) translate(0,80)">Ⅱ</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2567.000000 -4784.000000) translate(0,101)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cdb6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2567.000000 -4784.000000) translate(0,122)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1cdd290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 287.000000 -4995.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cec340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1550.000000 -4184.000000) translate(0,17)">362</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1cece20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -4416.000000) translate(0,14)">1号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cef620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 928.000000 -4910.000000) translate(0,17)">N31</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1cf1c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1298.000000 -4305.000000) translate(0,13)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1cf1c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1298.000000 -4305.000000) translate(0,29)">号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1cf1c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1298.000000 -4305.000000) translate(0,45)">站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1cf1c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1298.000000 -4305.000000) translate(0,61)">用</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1cf1c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1298.000000 -4305.000000) translate(0,77)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0aa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 -3476.000000) translate(0,12)">372</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0b420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1428.000000 -3476.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0b800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1509.000000 -3478.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -3419.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1d0c470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1584.000000 -3572.000000) translate(0,13)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0cfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2343.000000 -3555.000000) translate(0,12)">382</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0d810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2341.000000 -3507.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2341.000000 -3604.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0dcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2348.000000 -3637.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d128d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2495.000000 -4685.000000) translate(0,17)">3553</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d12f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2485.000000 -4639.000000) translate(0,17)">35537</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d13be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2482.000000 -4353.000000) translate(0,17)">35567</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d305d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 315.000000 -5158.000000) translate(0,12)">I母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d31020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -5110.000000) translate(0,17)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d31540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 396.000000 -5147.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d31780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 397.000000 -5076.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d319c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 315.000000 -5054.000000) translate(0,17)">31267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d31c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 -5163.000000) translate(0,12)">I母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d31e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 299.000000 -4732.000000) translate(0,12)">I段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d32250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 -4752.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d326b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.000000 -4800.000000) translate(0,12)">373</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d328f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 -4850.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d32b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 610.000000 -4882.000000) translate(0,12)">37367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d32d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 809.000000 -4750.000000) translate(0,12)">3741</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d32fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.000000 -4798.000000) translate(0,12)">374</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d331f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 809.000000 -4848.000000) translate(0,12)">3746</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d33430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.000000 -4895.000000) translate(0,12)">37467</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d33670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 933.000000 -4689.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d338b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 935.000000 -4639.000000) translate(0,12)">372</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d33af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 933.000000 -4591.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d33d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 941.000000 -4576.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d33f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -4684.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d341b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 354.000000 -4634.000000) translate(0,12)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d343f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -4588.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d34630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -4568.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d34870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 -4638.000000) translate(0,12)">302</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d34ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -4688.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d34cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 529.000000 -4637.000000) translate(0,12)">301</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d34f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -4687.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d35170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -4229.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d356a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1412.000000 -4251.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d35920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1417.000000 -4304.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d35b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1769.000000 -4229.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 -4249.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1841.000000 -4301.000000) translate(0,12)">3701</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d364c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1599.000000 -4241.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1612.000000 -4269.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -3765.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1297.000000 -3743.000000) translate(0,12)">3111</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 -3490.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1547.000000 -3540.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -3586.000000) translate(0,12)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1547.000000 -3636.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d376c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1559.000000 -3656.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -3995.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1966.000000 -3951.000000) translate(0,12)">302</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -3907.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2005.000000 -4063.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2007.000000 -4109.000000) translate(0,12)">301</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2005.000000 -4159.000000) translate(0,12)">3012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1981.000000 -3862.000000) translate(0,12)">3712</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d388c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2020.000000 -4213.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1615.000000 -4164.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1616.000000 -4217.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d45400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -3710.000000) translate(0,12)">3806</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1d473b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -3649.000000) translate(0,13)">站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1d473b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -3649.000000) translate(0,29)">用</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1d473b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -3649.000000) translate(0,45)">变</text>
   <text DF8003:Layer="0" fill="rgb(0,0,0)" font-family="SimSun" font-size="25" graphid="g_1d47ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -5216.000000) translate(0,20)">配调返回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1927.000000 -3517.000000) translate(0,20)">三街变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d589d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1966.000000 -3610.000000) translate(0,17)">341</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d59000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -3564.000000) translate(0,17)">3411</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d59240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -3663.000000) translate(0,17)">3416</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1d5bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2119.000000 -3517.000000) translate(0,13)">八</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1d5bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2119.000000 -3517.000000) translate(0,29)">马</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1d5bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2119.000000 -3517.000000) translate(0,45)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7cd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -4006.000000) translate(0,12)">363</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1346.000000 -4051.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1346.000000 -3962.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -3952.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -4524.000000) translate(0,16)">大</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -4524.000000) translate(0,36)">苴</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -4524.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -4392.000000) translate(0,16)">沙</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -4392.000000) translate(0,36)">桥</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -4392.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 -4426.000000) translate(0,16)">大</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 -4426.000000) translate(0,36)">蛇</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 -4426.000000) translate(0,56)">腰</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 -4426.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2850.000000 -4461.000000) translate(0,16)">楚</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2850.000000 -4461.000000) translate(0,36)">南</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2850.000000 -4461.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1230.000000 -3766.000000) translate(0,20)">红</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1230.000000 -3766.000000) translate(0,44)">土</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1230.000000 -3766.000000) translate(0,68)">坡</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1824.000000 -4376.000000) translate(0,16)">雨</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1824.000000 -4376.000000) translate(0,36)">露</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1824.000000 -4376.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2156.000000 -4377.000000) translate(0,16)">楚</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2156.000000 -4377.000000) translate(0,36)">南</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2156.000000 -4377.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1977.000000 -4376.000000) translate(0,16)">徐</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1977.000000 -4376.000000) translate(0,36)">营</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1977.000000 -4376.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1926.000000 -3866.000000) translate(0,16)">徐</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1926.000000 -3866.000000) translate(0,36)">三</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1926.000000 -3866.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1518.000000 -3860.000000) translate(0,16)">大</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1518.000000 -3860.000000) translate(0,36)">马</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1518.000000 -3860.000000) translate(0,56)">红</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1518.000000 -3860.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 -4325.000000) translate(0,13)">站用变</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1119.000000 -4113.000000) translate(0,16)">南</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1119.000000 -4113.000000) translate(0,36)">黄</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1119.000000 -4113.000000) translate(0,56)">大</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1119.000000 -4113.000000) translate(0,76)">线</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="0" id="SW-131648">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -3557.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24137" ObjectName="SW-NH_MJ.NH_MJ_371BK"/>
     <cge:Meas_Ref ObjectId="131648"/>
    <cge:TPSR_Ref TObjectID="24137"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130596">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1948.000000 -3922.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23947" ObjectName="SW-NH_XY.NH_XY_302BK"/>
     <cge:Meas_Ref ObjectId="130596"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-130568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1989.000000 -4080.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23943" ObjectName="SW-NH_XY.NH_XY_301BK"/>
     <cge:Meas_Ref ObjectId="130568"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9928">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 -4585.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1599" ObjectName="SW-CX_NH.CX_NH_388BK"/>
     <cge:Meas_Ref ObjectId="9928"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9826">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -4583.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1552" ObjectName="SW-CX_NH.CX_NH_382BK"/>
     <cge:Meas_Ref ObjectId="9826"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9819">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 -4582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1548" ObjectName="SW-CX_NH.CX_NH_383BK"/>
     <cge:Meas_Ref ObjectId="9819"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9921">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1811.000000 -4584.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1595" ObjectName="SW-CX_NH.CX_NH_386BK"/>
     <cge:Meas_Ref ObjectId="9921"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9812">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1989.000000 -4584.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1544" ObjectName="SW-CX_NH.CX_NH_384BK"/>
     <cge:Meas_Ref ObjectId="9812"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9940">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2168.000000 -4583.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1606" ObjectName="SW-CX_NH.CX_NH_381BK"/>
     <cge:Meas_Ref ObjectId="9940"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-9762">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1708.000000 -4730.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1532" ObjectName="SW-CX_NH.CX_NH_312BK"/>
     <cge:Meas_Ref ObjectId="9762"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-22328">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2869.000000 -4425.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3631" ObjectName="SW-CX_XJ.CX_XJ_362BK"/>
     <cge:Meas_Ref ObjectId="22328"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129555">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 336.000000 -4605.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23730" ObjectName="SW-NH_HS.NH_HS_371BK"/>
     <cge:Meas_Ref ObjectId="129555"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129674">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -4608.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23794" ObjectName="SW-NH_HS.NH_HS_301BK"/>
     <cge:Meas_Ref ObjectId="129674"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129696">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 716.000000 -4609.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23800" ObjectName="SW-NH_HS.NH_HS_302BK"/>
     <cge:Meas_Ref ObjectId="129696"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129566">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.000000 -4610.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23734" ObjectName="SW-NH_HS.NH_HS_372BK"/>
     <cge:Meas_Ref ObjectId="129566"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129588">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 -4769.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23742" ObjectName="SW-NH_HS.NH_HS_374BK"/>
     <cge:Meas_Ref ObjectId="129588"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-42346">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 -4908.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7007" ObjectName="SW-CX_LH.CX_LH_361BK"/>
     <cge:Meas_Ref ObjectId="42346"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-147610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -5066.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25795" ObjectName="SW-YA_XJC.YA_XJC_381BK"/>
     <cge:Meas_Ref ObjectId="147610"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-145562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 380.000000 -5076.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25551" ObjectName="SW-YA_TP.YA_TP_312BK"/>
     <cge:Meas_Ref ObjectId="145562"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-129577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -4771.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23738" ObjectName="SW-NH_HS.NH_HS_373BK"/>
     <cge:Meas_Ref ObjectId="129577"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2560.000000 -4701.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2278.000000 -4931.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2498.114754 -4999.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131202">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 -4150.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24045" ObjectName="SW-NH_DSY.NH_DSY_362BK"/>
     <cge:Meas_Ref ObjectId="131202"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193482">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2325.000000 -3526.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29416" ObjectName="SW-CX_BJ.CX_BJ_382BK"/>
     <cge:Meas_Ref ObjectId="193482"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193510">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1459.000000 -3442.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29423" ObjectName="SW-NH_MJ.NH_MJ_372BK"/>
     <cge:Meas_Ref ObjectId="193510"/>
    <cge:TPSR_Ref TObjectID="29423"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131253">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -3976.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24057" ObjectName="SW-NH_DSY.NH_DSY_361BK"/>
     <cge:Meas_Ref ObjectId="131253"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-185038">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1948.000000 -3581.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28097" ObjectName="SW-CX_SJ.CX_SJ_341BK"/>
     <cge:Meas_Ref ObjectId="185038"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-131235">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1330.000000 -3977.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24051" ObjectName="SW-NH_DSY.NH_DSY_363BK"/>
     <cge:Meas_Ref ObjectId="131235"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.000000 -4273.000000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.000000 -4273.000000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2943.000000 -4286.000000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2943.000000 -4286.000000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 494.000000 -4495.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 494.000000 -4495.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.000000 -4484.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.000000 -4484.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1629.000000 -4313.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1629.000000 -4313.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -4200.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -4200.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 -3558.000000)" xlink:href="#transformer2:shape71_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 -3558.000000)" xlink:href="#transformer2:shape71_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1760.000000 -3657.000000)" xlink:href="#transformer2:shape71_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1760.000000 -3657.000000)" xlink:href="#transformer2:shape71_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="0" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -117.000000 -5164.013514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="40" qtmmishow="hidden" width="226" x="-117" y="-5223"/></g>
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2549" x2="2555" y1="4611" y2="4601"/>
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2543" x2="2555" y1="4601" y2="4601"/>
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2549" x2="2543" y1="4610" y2="4620"/>
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2549" x2="2555" y1="4610" y2="4620"/>
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2543" x2="2555" y1="4620" y2="4620"/>
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2549" x2="2543" y1="4611" y2="4601"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2549" x2="2555" y1="4611" y2="4601"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="0" id="g_16ae380">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1656.500000 -3680.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_16d0170">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2229.500000 -4290.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_16da990">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2930.500000 -4303.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_18f3ce0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 -5025.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_18f6fc0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1325.500000 -5039.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_18f7d00">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1331.000000 -5060.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_164d310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.000000 -4940.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_16517c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 518.000000 -4983.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1653060">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 578.500000 -4955.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_16538e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 534.000000 -4953.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_15ebc10">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2495.500000 -4511.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_15ec950">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2489.000000 -4532.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_15ed090">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2540.000000 -4485.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cb9930">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2573.000000 -4260.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cc70e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2386.000000 -4704.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cc9370">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2339.500000 -5049.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cc9ba0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2345.000000 -5070.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cca1b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2269.000000 -5227.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1ccaf60">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2272.000000 -5052.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cd6af0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2469.000000 -4912.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cdbe10">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1951.500000 -4385.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cdc590">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1949.000000 -4406.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cde390">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.629630 380.000000 -4961.000000)" xlink:href="#lightningRod:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cdf0b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.000000 -4993.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1ce9cc0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1535.000000 -4296.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1ceb320">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1604.000000 -4292.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cec970">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1490.000000 -4289.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cf05c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1320.000000 -4250.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cf9bb0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2216.000000 -3653.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cfa2b0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2301.500000 -3632.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cfecc0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1595.000000 -3556.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cff3c0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1602.000000 -3511.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1cfff70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1581.000000 -3384.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d19bc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1548.000000 -4332.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d35da0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1533.500000 -3899.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d39f00">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1450.000000 -3920.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d45a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1598.000000 -3692.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d48a70">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1808.500000 -4277.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d49060">
    <use class="BV-35KV" transform="matrix(1.125000 -0.000000 0.000000 1.040816 1741.000000 -4337.000000)" xlink:href="#lightningRod:shape159"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d59480">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2024.500000 -3695.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d59f10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1918.000000 -3619.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d7a6d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1332.500000 -3900.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d7b1b0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1249.000000 -3921.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="0" id="g_1cdea90">
    <use class="BV-35KV" transform="matrix(1.269231 -0.000000 0.000000 -1.493506 303.000000 -4991.090909)" xlink:href="#voltageTransformer:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1d5ac00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2038.000000 -3654.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="40" qtmmishow="hidden" width="226" x="-117" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="40" opacity="0" stroke="white" transform="" width="226" x="-117" y="-5223"/></g>
   <g DF8003:Layer="0" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-166" y="-5240"/></g>
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="127" x="138" y="-5223"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="$AUDIT-BAD-LAYER:0.000000 0.000000" layer11="图层2:0.000000 0.000000" layer12="GDXT:0.000000 0.000000" layer13="Defpoints:0.000000 0.000000" layer14="yc:0.000000 0.000000" layer15="PUBLIC:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer4="$AUDIT-BAD-LAYER:0.000000 0.000000" layer5="图层2:0.000000 0.000000" layer6="GDXT:0.000000 0.000000" layer7="Defpoints:0.000000 0.000000" layer8="yc:0.000000 0.000000" layer9="0:0.000000 0.000000" layerN="16" moveAndZoomFlag="1"/>
</svg>