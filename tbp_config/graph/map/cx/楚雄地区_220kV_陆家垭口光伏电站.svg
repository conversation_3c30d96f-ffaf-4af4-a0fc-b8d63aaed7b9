<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-348" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-358 -1207 2708 1675">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27195" x1="7" x2="7" y1="6" y2="15"/>
    <rect height="25" stroke-width="1" width="12" x="1" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="48" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_1ad60c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="47" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="38" x2="20" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="13" x2="13" y1="48" y2="31"/>
    <circle cx="34" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="24" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="4" x2="13" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape101">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="39" y2="44"/>
    <polyline DF8003:Layer="PUBLIC" points="16,70 10,58 22,58 16,70 16,69 16,70 "/>
    <circle cx="16" cy="61" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="8" y2="8"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,39 40,39 40,8 " stroke-width="1"/>
    <circle cx="16" cy="39" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="39" y2="34"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="load:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="reactance:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="transformer:shape3_0">
    <circle cx="57" cy="31" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="56" y1="29" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="71" x2="63" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="63" y1="20" y2="29"/>
   </symbol>
   <symbol id="transformer:shape3_1">
    <circle cx="26" cy="30" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="11" x2="26" y1="28" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="11" x2="26" y1="28" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="26" x2="26" y1="19" y2="36"/>
   </symbol>
   <symbol id="transformer:shape3-2">
    <circle cx="41" cy="60" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="33" y1="67" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="74" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="58" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape97_0">
    <circle cx="17" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="12,78 19,78 16,85 12,78 "/>
   </symbol>
   <symbol id="transformer2:shape97_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_218a470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_218b5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_218c0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_218cc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_218df00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_218ea20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_218f5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_21900d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2191940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2191940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2193140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2193140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2194ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2194ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2195dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2197a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2198690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2199500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2199c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_219b4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_219c130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_219c9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_219d1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_219e290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf2190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_219e970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_219f2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_219fef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_21a1330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_21a2530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21a31a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21a96e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21aa1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21a4780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21a5d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1685" width="2718" x="-363" y="-1212"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b65050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 -334.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17b48b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 177.000000 -349.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17bff20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 202.000000 -364.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a8a020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2040.000000 158.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a8a190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2057.000000 143.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a8a300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2048.000000 172.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4c150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2049.000000 188.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4c2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2048.000000 203.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4c430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2048.000000 218.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a7cf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 916.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a7d200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 450.000000 901.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a7d370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 886.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa2e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 237.000000 162.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa2f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 254.000000 147.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa30e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 245.000000 176.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa3250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 192.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa33c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 245.000000 207.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa3530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 245.000000 222.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad63f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 474.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad6720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 459.000000) translate(0,12)">温度:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae1530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 980.000000 915.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae16e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 900.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae1890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 994.000000 885.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c69750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1497.000000 927.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c69940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1486.000000 912.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c69b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 897.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6a450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.000000 201.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6a670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1338.000000 186.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6a8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1363.000000 171.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6abe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1044.000000 203.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6ae40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 188.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6b080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1058.000000 173.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6fd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 165.000000 805.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6ff50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.000000 790.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c70190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.000000 819.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c703d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 835.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c70610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.000000 850.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c70850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.000000 865.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="75" stroke="rgb(60,120,255)" stroke-width="1" width="122" x="1708" y="-1161"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-320424">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.741036 -851.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49751" ObjectName="SW-CX_LJYK.CX_LJYK_25217SW"/>
     <cge:Meas_Ref ObjectId="320424"/>
    <cge:TPSR_Ref TObjectID="49751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320426">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 298.741036 -920.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49753" ObjectName="SW-CX_LJYK.CX_LJYK_25260SW"/>
     <cge:Meas_Ref ObjectId="320426"/>
    <cge:TPSR_Ref TObjectID="49753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320427">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.741036 -1019.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49754" ObjectName="SW-CX_LJYK.CX_LJYK_25267SW"/>
     <cge:Meas_Ref ObjectId="320427"/>
    <cge:TPSR_Ref TObjectID="49754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320432">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.063745 -699.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49757" ObjectName="SW-CX_LJYK.CX_LJYK_29010SW"/>
     <cge:Meas_Ref ObjectId="320432"/>
    <cge:TPSR_Ref TObjectID="49757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 803.063745 271.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320416">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.741036 -845.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49745" ObjectName="SW-CX_LJYK.CX_LJYK_25117SW"/>
     <cge:Meas_Ref ObjectId="320416"/>
    <cge:TPSR_Ref TObjectID="49745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320418">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.741036 -914.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49747" ObjectName="SW-CX_LJYK.CX_LJYK_25160SW"/>
     <cge:Meas_Ref ObjectId="320418"/>
    <cge:TPSR_Ref TObjectID="49747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320419">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.741036 -1013.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49748" ObjectName="SW-CX_LJYK.CX_LJYK_25167SW"/>
     <cge:Meas_Ref ObjectId="320419"/>
    <cge:TPSR_Ref TObjectID="49748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320431">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.000000 -608.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49756" ObjectName="SW-CX_LJYK.CX_LJYK_29017SW"/>
     <cge:Meas_Ref ObjectId="320431"/>
    <cge:TPSR_Ref TObjectID="49756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320438">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1286.741036 -1063.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49763" ObjectName="SW-CX_LJYK.CX_LJYK_20167SW"/>
     <cge:Meas_Ref ObjectId="320438"/>
    <cge:TPSR_Ref TObjectID="49763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320435">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 -855.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49760" ObjectName="SW-CX_LJYK.CX_LJYK_20117SW"/>
     <cge:Meas_Ref ObjectId="320435"/>
    <cge:TPSR_Ref TObjectID="49760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320437">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1399.000000 -936.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49762" ObjectName="SW-CX_LJYK.CX_LJYK_20160SW"/>
     <cge:Meas_Ref ObjectId="320437"/>
    <cge:TPSR_Ref TObjectID="49762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320439">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1333.000000 -508.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49764" ObjectName="SW-CX_LJYK.CX_LJYK_2010SW"/>
     <cge:Meas_Ref ObjectId="320439"/>
    <cge:TPSR_Ref TObjectID="49764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320441">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 989.500000 -122.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49766" ObjectName="SW-CX_LJYK.CX_LJYK_301XC"/>
     <cge:Meas_Ref ObjectId="320441"/>
    <cge:TPSR_Ref TObjectID="49766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320441">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 989.500000 -208.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49767" ObjectName="SW-CX_LJYK.CX_LJYK_301XC1"/>
     <cge:Meas_Ref ObjectId="320441"/>
    <cge:TPSR_Ref TObjectID="49767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320443">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1291.500000 -122.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49769" ObjectName="SW-CX_LJYK.CX_LJYK_302XC"/>
     <cge:Meas_Ref ObjectId="320443"/>
    <cge:TPSR_Ref TObjectID="49769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320443">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1291.500000 -208.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49770" ObjectName="SW-CX_LJYK.CX_LJYK_302XC1"/>
     <cge:Meas_Ref ObjectId="320443"/>
    <cge:TPSR_Ref TObjectID="49770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320494">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 882.500000 37.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49813" ObjectName="SW-CX_LJYK.CX_LJYK_360XC1"/>
     <cge:Meas_Ref ObjectId="320494"/>
    <cge:TPSR_Ref TObjectID="49813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320494">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 882.500000 -48.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49812" ObjectName="SW-CX_LJYK.CX_LJYK_360XC"/>
     <cge:Meas_Ref ObjectId="320494"/>
    <cge:TPSR_Ref TObjectID="49812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320495">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 801.063745 119.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49814" ObjectName="SW-CX_LJYK.CX_LJYK_36067SW"/>
     <cge:Meas_Ref ObjectId="320495"/>
    <cge:TPSR_Ref TObjectID="49814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320449">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 731.500000 38.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49777" ObjectName="SW-CX_LJYK.CX_LJYK_351XC1"/>
     <cge:Meas_Ref ObjectId="320449"/>
    <cge:TPSR_Ref TObjectID="49777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320449">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 731.500000 -47.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49776" ObjectName="SW-CX_LJYK.CX_LJYK_351XC"/>
     <cge:Meas_Ref ObjectId="320449"/>
    <cge:TPSR_Ref TObjectID="49776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320450">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.063745 120.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49778" ObjectName="SW-CX_LJYK.CX_LJYK_35167SW"/>
     <cge:Meas_Ref ObjectId="320450"/>
    <cge:TPSR_Ref TObjectID="49778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320454">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 587.500000 40.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49781" ObjectName="SW-CX_LJYK.CX_LJYK_352XC1"/>
     <cge:Meas_Ref ObjectId="320454"/>
    <cge:TPSR_Ref TObjectID="49781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320454">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 587.500000 -45.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49780" ObjectName="SW-CX_LJYK.CX_LJYK_352XC"/>
     <cge:Meas_Ref ObjectId="320454"/>
    <cge:TPSR_Ref TObjectID="49780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320455">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 506.063745 122.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49782" ObjectName="SW-CX_LJYK.CX_LJYK_35267SW"/>
     <cge:Meas_Ref ObjectId="320455"/>
    <cge:TPSR_Ref TObjectID="49782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320459">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 441.500000 40.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49785" ObjectName="SW-CX_LJYK.CX_LJYK_353XC1"/>
     <cge:Meas_Ref ObjectId="320459"/>
    <cge:TPSR_Ref TObjectID="49785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320459">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 441.500000 -45.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49784" ObjectName="SW-CX_LJYK.CX_LJYK_353XC"/>
     <cge:Meas_Ref ObjectId="320459"/>
    <cge:TPSR_Ref TObjectID="49784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320460">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 360.063745 122.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49786" ObjectName="SW-CX_LJYK.CX_LJYK_35367SW"/>
     <cge:Meas_Ref ObjectId="320460"/>
    <cge:TPSR_Ref TObjectID="49786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320464">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 282.500000 42.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49789" ObjectName="SW-CX_LJYK.CX_LJYK_354XC1"/>
     <cge:Meas_Ref ObjectId="320464"/>
    <cge:TPSR_Ref TObjectID="49789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320464">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 282.500000 -43.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49788" ObjectName="SW-CX_LJYK.CX_LJYK_354XC"/>
     <cge:Meas_Ref ObjectId="320464"/>
    <cge:TPSR_Ref TObjectID="49788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320465">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 201.063745 124.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49790" ObjectName="SW-CX_LJYK.CX_LJYK_35467SW"/>
     <cge:Meas_Ref ObjectId="320465"/>
    <cge:TPSR_Ref TObjectID="49790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.063745 250.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320446">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1021.500000 -42.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49771" ObjectName="SW-CX_LJYK.CX_LJYK_3501XC"/>
     <cge:Meas_Ref ObjectId="320446"/>
    <cge:TPSR_Ref TObjectID="49771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320446">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1021.500000 34.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49772" ObjectName="SW-CX_LJYK.CX_LJYK_3501XC1"/>
     <cge:Meas_Ref ObjectId="320446"/>
    <cge:TPSR_Ref TObjectID="49772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1253.500000 -40.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49773" ObjectName="SW-CX_LJYK.CX_LJYK_3502XC"/>
     <cge:Meas_Ref ObjectId="320447"/>
    <cge:TPSR_Ref TObjectID="49773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1253.500000 36.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49774" ObjectName="SW-CX_LJYK.CX_LJYK_3502XC1"/>
     <cge:Meas_Ref ObjectId="320447"/>
    <cge:TPSR_Ref TObjectID="49774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320484">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2087.500000 39.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49805" ObjectName="SW-CX_LJYK.CX_LJYK_358XC1"/>
     <cge:Meas_Ref ObjectId="320484"/>
    <cge:TPSR_Ref TObjectID="49805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320484">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2087.500000 -46.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49804" ObjectName="SW-CX_LJYK.CX_LJYK_358XC"/>
     <cge:Meas_Ref ObjectId="320484"/>
    <cge:TPSR_Ref TObjectID="49804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320485">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2006.063745 121.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49806" ObjectName="SW-CX_LJYK.CX_LJYK_35867SW"/>
     <cge:Meas_Ref ObjectId="320485"/>
    <cge:TPSR_Ref TObjectID="49806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320479">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1943.500000 41.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49801" ObjectName="SW-CX_LJYK.CX_LJYK_357XC1"/>
     <cge:Meas_Ref ObjectId="320479"/>
    <cge:TPSR_Ref TObjectID="49801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320479">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1943.500000 -44.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49800" ObjectName="SW-CX_LJYK.CX_LJYK_357XC"/>
     <cge:Meas_Ref ObjectId="320479"/>
    <cge:TPSR_Ref TObjectID="49800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320480">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1862.063745 123.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49802" ObjectName="SW-CX_LJYK.CX_LJYK_35767SW"/>
     <cge:Meas_Ref ObjectId="320480"/>
    <cge:TPSR_Ref TObjectID="49802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320474">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1797.500000 41.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49797" ObjectName="SW-CX_LJYK.CX_LJYK_356XC1"/>
     <cge:Meas_Ref ObjectId="320474"/>
    <cge:TPSR_Ref TObjectID="49797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320474">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1797.500000 -44.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49796" ObjectName="SW-CX_LJYK.CX_LJYK_356XC"/>
     <cge:Meas_Ref ObjectId="320474"/>
    <cge:TPSR_Ref TObjectID="49796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320475">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1716.063745 123.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49798" ObjectName="SW-CX_LJYK.CX_LJYK_35667SW"/>
     <cge:Meas_Ref ObjectId="320475"/>
    <cge:TPSR_Ref TObjectID="49798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320469">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1638.500000 43.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49793" ObjectName="SW-CX_LJYK.CX_LJYK_355XC1"/>
     <cge:Meas_Ref ObjectId="320469"/>
    <cge:TPSR_Ref TObjectID="49793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320469">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1638.500000 -42.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49792" ObjectName="SW-CX_LJYK.CX_LJYK_355XC"/>
     <cge:Meas_Ref ObjectId="320469"/>
    <cge:TPSR_Ref TObjectID="49792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1557.063745 125.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49794" ObjectName="SW-CX_LJYK.CX_LJYK_35567SW"/>
     <cge:Meas_Ref ObjectId="320470"/>
    <cge:TPSR_Ref TObjectID="49794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1372.063745 274.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320499">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1451.500000 40.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49817" ObjectName="SW-CX_LJYK.CX_LJYK_361XC1"/>
     <cge:Meas_Ref ObjectId="320499"/>
    <cge:TPSR_Ref TObjectID="49817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320499">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1451.500000 -45.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49816" ObjectName="SW-CX_LJYK.CX_LJYK_361XC"/>
     <cge:Meas_Ref ObjectId="320499"/>
    <cge:TPSR_Ref TObjectID="49816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320500">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1370.063745 122.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49818" ObjectName="SW-CX_LJYK.CX_LJYK_36167SW"/>
     <cge:Meas_Ref ObjectId="320500"/>
    <cge:TPSR_Ref TObjectID="49818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.063745 253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320489">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2245.500000 39.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49809" ObjectName="SW-CX_LJYK.CX_LJYK_359XC1"/>
     <cge:Meas_Ref ObjectId="320489"/>
    <cge:TPSR_Ref TObjectID="49809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320489">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2245.500000 -46.500000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49808" ObjectName="SW-CX_LJYK.CX_LJYK_359XC"/>
     <cge:Meas_Ref ObjectId="320489"/>
    <cge:TPSR_Ref TObjectID="49808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320490">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2164.063745 121.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49810" ObjectName="SW-CX_LJYK.CX_LJYK_35967SW"/>
     <cge:Meas_Ref ObjectId="320490"/>
    <cge:TPSR_Ref TObjectID="49810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1966.000000 -1025.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1966.000000 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320425">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.000000 -952.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49752" ObjectName="SW-CX_LJYK.CX_LJYK_2526SW"/>
     <cge:Meas_Ref ObjectId="320425"/>
    <cge:TPSR_Ref TObjectID="49752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320423">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.000000 -790.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49750" ObjectName="SW-CX_LJYK.CX_LJYK_2521SW"/>
     <cge:Meas_Ref ObjectId="320423"/>
    <cge:TPSR_Ref TObjectID="49750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320430">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 655.000000 -646.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49755" ObjectName="SW-CX_LJYK.CX_LJYK_2901SW"/>
     <cge:Meas_Ref ObjectId="320430"/>
    <cge:TPSR_Ref TObjectID="49755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320417">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -944.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49746" ObjectName="SW-CX_LJYK.CX_LJYK_2516SW"/>
     <cge:Meas_Ref ObjectId="320417"/>
    <cge:TPSR_Ref TObjectID="49746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320415">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49744" ObjectName="SW-CX_LJYK.CX_LJYK_2511SW"/>
     <cge:Meas_Ref ObjectId="320415"/>
    <cge:TPSR_Ref TObjectID="49744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320436">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1370.000000 -976.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49761" ObjectName="SW-CX_LJYK.CX_LJYK_2016SW"/>
     <cge:Meas_Ref ObjectId="320436"/>
    <cge:TPSR_Ref TObjectID="49761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320434">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1371.000000 -790.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49759" ObjectName="SW-CX_LJYK.CX_LJYK_2011SW"/>
     <cge:Meas_Ref ObjectId="320434"/>
    <cge:TPSR_Ref TObjectID="49759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1966.000000 -803.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a4ff20">
    <use class="BV-220KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 705.000000 -518.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19dfb20">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1053.000000 110.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f0fc0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1285.000000 112.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 860.000000 424.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1429.000000 427.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1959.000000 -524.000000)" xlink:href="#transformer2:shape97_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1959.000000 -524.000000)" xlink:href="#transformer2:shape97_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b18300">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.741036 -265.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b19440">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1301.000000 -501.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa0dc0">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1373.000000 -504.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a2c050">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 337.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad5b20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 465.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad6a00">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 -1088.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad7350">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 -1030.000000)" xlink:href="#lightningRod:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a807a0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 827.000000 -1082.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a81170">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 947.000000 -1024.000000)" xlink:href="#lightningRod:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4e460">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 -561.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ac2e30">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 890.000000 -280.936255)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a71540">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 795.000000 59.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acf5b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 863.000000 190.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a62780">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 644.000000 60.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a95c10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.000000 191.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a01460">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 500.000000 62.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a05ca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 568.000000 193.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a470f0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 354.000000 62.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a08580">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 193.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1977610">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 195.000000 64.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_197be50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 263.000000 195.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a5b9f0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 951.000000 431.936255)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19deef0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 104.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e2570">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1054.063745 103.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f0390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1235.000000 106.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f3a10">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1286.063745 105.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a3eba0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2000.000000 61.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ab8e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2068.000000 192.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19b5170">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1856.000000 63.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19b99b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1924.000000 194.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19a0ee0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1710.000000 63.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19a5720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1778.000000 194.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19c9140">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1551.000000 65.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19cd980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1619.000000 196.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_198d650">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1476.000000 340.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_198ef90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 468.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1998240">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1364.000000 62.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1968100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 193.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_196eb50">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1520.000000 434.936255)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b91060">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2226.000000 194.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b95ce0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2158.000000 61.063745)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b99d40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1205.000000 -425.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19192b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.000000 -506.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1919fd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.000000 -492.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_191ad80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.000000 -477.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c72430">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2220.000000 296.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -267.000000 -1036.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -234.461538 -903.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -234.461538 -862.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320224" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 518.000000 -916.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49749"/>
     <cge:Term_Ref ObjectID="49635"/>
    <cge:TPSR_Ref TObjectID="49749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320225" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 518.000000 -916.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320225" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49749"/>
     <cge:Term_Ref ObjectID="49635"/>
    <cge:TPSR_Ref TObjectID="49749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320221" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 518.000000 -916.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320221" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49749"/>
     <cge:Term_Ref ObjectID="49635"/>
    <cge:TPSR_Ref TObjectID="49749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320212" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -913.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320212" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49743"/>
     <cge:Term_Ref ObjectID="49464"/>
    <cge:TPSR_Ref TObjectID="49743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320213" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -913.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49743"/>
     <cge:Term_Ref ObjectID="49464"/>
    <cge:TPSR_Ref TObjectID="49743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -913.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49743"/>
     <cge:Term_Ref ObjectID="49464"/>
    <cge:TPSR_Ref TObjectID="49743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1557.000000 -926.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49758"/>
     <cge:Term_Ref ObjectID="49653"/>
    <cge:TPSR_Ref TObjectID="49758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320245" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1557.000000 -926.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49758"/>
     <cge:Term_Ref ObjectID="49653"/>
    <cge:TPSR_Ref TObjectID="49758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1557.000000 -926.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49758"/>
     <cge:Term_Ref ObjectID="49653"/>
    <cge:TPSR_Ref TObjectID="49758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -200.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49768"/>
     <cge:Term_Ref ObjectID="49673"/>
    <cge:TPSR_Ref TObjectID="49768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -200.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49768"/>
     <cge:Term_Ref ObjectID="49673"/>
    <cge:TPSR_Ref TObjectID="49768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -200.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49768"/>
     <cge:Term_Ref ObjectID="49673"/>
    <cge:TPSR_Ref TObjectID="49768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1105.000000 -203.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49765"/>
     <cge:Term_Ref ObjectID="49667"/>
    <cge:TPSR_Ref TObjectID="49765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1105.000000 -203.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49765"/>
     <cge:Term_Ref ObjectID="49667"/>
    <cge:TPSR_Ref TObjectID="49765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1105.000000 -203.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49765"/>
     <cge:Term_Ref ObjectID="49667"/>
    <cge:TPSR_Ref TObjectID="49765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320323" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320323" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49787"/>
     <cge:Term_Ref ObjectID="49999"/>
    <cge:TPSR_Ref TObjectID="49787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320324" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320324" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49787"/>
     <cge:Term_Ref ObjectID="49999"/>
    <cge:TPSR_Ref TObjectID="49787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320320" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49787"/>
     <cge:Term_Ref ObjectID="49999"/>
    <cge:TPSR_Ref TObjectID="49787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320311" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 402.000000 336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320311" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49783"/>
     <cge:Term_Ref ObjectID="49854"/>
    <cge:TPSR_Ref TObjectID="49783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 402.000000 336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49783"/>
     <cge:Term_Ref ObjectID="49854"/>
    <cge:TPSR_Ref TObjectID="49783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320308" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 402.000000 336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49783"/>
     <cge:Term_Ref ObjectID="49854"/>
    <cge:TPSR_Ref TObjectID="49783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 552.000000 336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49779"/>
     <cge:Term_Ref ObjectID="49794"/>
    <cge:TPSR_Ref TObjectID="49779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320300" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 552.000000 336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49779"/>
     <cge:Term_Ref ObjectID="49794"/>
    <cge:TPSR_Ref TObjectID="49779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 552.000000 336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49779"/>
     <cge:Term_Ref ObjectID="49794"/>
    <cge:TPSR_Ref TObjectID="49779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320287" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49775"/>
     <cge:Term_Ref ObjectID="49689"/>
    <cge:TPSR_Ref TObjectID="49775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320288" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49775"/>
     <cge:Term_Ref ObjectID="49689"/>
    <cge:TPSR_Ref TObjectID="49775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49775"/>
     <cge:Term_Ref ObjectID="49689"/>
    <cge:TPSR_Ref TObjectID="49775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320395" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 326.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49811"/>
     <cge:Term_Ref ObjectID="50373"/>
    <cge:TPSR_Ref TObjectID="49811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320396" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 326.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49811"/>
     <cge:Term_Ref ObjectID="50373"/>
    <cge:TPSR_Ref TObjectID="49811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320392" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 326.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49811"/>
     <cge:Term_Ref ObjectID="50373"/>
    <cge:TPSR_Ref TObjectID="49811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 326.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49815"/>
     <cge:Term_Ref ObjectID="50381"/>
    <cge:TPSR_Ref TObjectID="49815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 326.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49815"/>
     <cge:Term_Ref ObjectID="50381"/>
    <cge:TPSR_Ref TObjectID="49815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320404" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 326.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320404" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49815"/>
     <cge:Term_Ref ObjectID="50381"/>
    <cge:TPSR_Ref TObjectID="49815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320335" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1597.000000 326.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320335" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49791"/>
     <cge:Term_Ref ObjectID="50007"/>
    <cge:TPSR_Ref TObjectID="49791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320336" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1597.000000 326.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320336" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49791"/>
     <cge:Term_Ref ObjectID="50007"/>
    <cge:TPSR_Ref TObjectID="49791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320332" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1597.000000 326.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320332" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49791"/>
     <cge:Term_Ref ObjectID="50007"/>
    <cge:TPSR_Ref TObjectID="49791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.000000 326.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320347" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49795"/>
     <cge:Term_Ref ObjectID="50222"/>
    <cge:TPSR_Ref TObjectID="49795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320348" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.000000 326.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320348" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49795"/>
     <cge:Term_Ref ObjectID="50222"/>
    <cge:TPSR_Ref TObjectID="49795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320344" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.000000 326.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320344" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49795"/>
     <cge:Term_Ref ObjectID="50222"/>
    <cge:TPSR_Ref TObjectID="49795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320359" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1905.000000 326.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320359" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49799"/>
     <cge:Term_Ref ObjectID="50230"/>
    <cge:TPSR_Ref TObjectID="49799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320360" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1905.000000 326.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320360" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49799"/>
     <cge:Term_Ref ObjectID="50230"/>
    <cge:TPSR_Ref TObjectID="49799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320356" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1905.000000 326.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320356" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49799"/>
     <cge:Term_Ref ObjectID="50230"/>
    <cge:TPSR_Ref TObjectID="49799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320371" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2051.000000 326.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320371" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49803"/>
     <cge:Term_Ref ObjectID="50238"/>
    <cge:TPSR_Ref TObjectID="49803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320372" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2051.000000 326.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320372" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49803"/>
     <cge:Term_Ref ObjectID="50238"/>
    <cge:TPSR_Ref TObjectID="49803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2051.000000 326.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49803"/>
     <cge:Term_Ref ObjectID="50238"/>
    <cge:TPSR_Ref TObjectID="49803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320383" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2221.000000 317.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320383" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49807"/>
     <cge:Term_Ref ObjectID="50347"/>
    <cge:TPSR_Ref TObjectID="49807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2221.000000 317.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49807"/>
     <cge:Term_Ref ObjectID="50347"/>
    <cge:TPSR_Ref TObjectID="49807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320380" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2221.000000 317.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320380" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49807"/>
     <cge:Term_Ref ObjectID="50347"/>
    <cge:TPSR_Ref TObjectID="49807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-320227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -864.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49739"/>
     <cge:Term_Ref ObjectID="48942"/>
    <cge:TPSR_Ref TObjectID="49739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-320228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -864.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49739"/>
     <cge:Term_Ref ObjectID="48942"/>
    <cge:TPSR_Ref TObjectID="49739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-320229" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -864.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49739"/>
     <cge:Term_Ref ObjectID="48942"/>
    <cge:TPSR_Ref TObjectID="49739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-320233" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -864.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49739"/>
     <cge:Term_Ref ObjectID="48942"/>
    <cge:TPSR_Ref TObjectID="49739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-320230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -864.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49739"/>
     <cge:Term_Ref ObjectID="48942"/>
    <cge:TPSR_Ref TObjectID="49739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-320234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -864.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49739"/>
     <cge:Term_Ref ObjectID="48942"/>
    <cge:TPSR_Ref TObjectID="49739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-320262" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -220.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49740"/>
     <cge:Term_Ref ObjectID="49455"/>
    <cge:TPSR_Ref TObjectID="49740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-320263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -220.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49740"/>
     <cge:Term_Ref ObjectID="49455"/>
    <cge:TPSR_Ref TObjectID="49740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-320264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -220.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49740"/>
     <cge:Term_Ref ObjectID="49455"/>
    <cge:TPSR_Ref TObjectID="49740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-320268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -220.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49740"/>
     <cge:Term_Ref ObjectID="49455"/>
    <cge:TPSR_Ref TObjectID="49740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-320265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -220.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49740"/>
     <cge:Term_Ref ObjectID="49455"/>
    <cge:TPSR_Ref TObjectID="49740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-320269" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -220.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320269" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49740"/>
     <cge:Term_Ref ObjectID="49455"/>
    <cge:TPSR_Ref TObjectID="49740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-320270" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -219.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320270" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49741"/>
     <cge:Term_Ref ObjectID="49456"/>
    <cge:TPSR_Ref TObjectID="49741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-320271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -219.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49741"/>
     <cge:Term_Ref ObjectID="49456"/>
    <cge:TPSR_Ref TObjectID="49741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-320272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -219.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49741"/>
     <cge:Term_Ref ObjectID="49456"/>
    <cge:TPSR_Ref TObjectID="49741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-320276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -219.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49741"/>
     <cge:Term_Ref ObjectID="49456"/>
    <cge:TPSR_Ref TObjectID="49741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-320273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -219.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49741"/>
     <cge:Term_Ref ObjectID="49456"/>
    <cge:TPSR_Ref TObjectID="49741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-320277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -219.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49741"/>
     <cge:Term_Ref ObjectID="49456"/>
    <cge:TPSR_Ref TObjectID="49741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-320259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1459.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49819"/>
     <cge:Term_Ref ObjectID="50391"/>
    <cge:TPSR_Ref TObjectID="49819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-320260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1459.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49819"/>
     <cge:Term_Ref ObjectID="50391"/>
    <cge:TPSR_Ref TObjectID="49819"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="208" x="-255" y="-1095"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="208" x="-255" y="-1095"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-304" y="-1112"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="208" x="-255" y="-1095"/></g>
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1384" x2="1399" y1="-495" y2="-495"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1393" x2="1388" y1="-499" y2="-490"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1397" x2="1392" y1="-498" y2="-489"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1384" x2="1399" y1="-481" y2="-481"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1393" x2="1388" y1="-485" y2="-476"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1397" x2="1392" y1="-484" y2="-475"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1204" x2="1204" y1="-572" y2="-584"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1201" x2="1207" y1="-583" y2="-579"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1201" x2="1207" y1="-579" y2="-575"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1218" x2="1218" y1="-571" y2="-583"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1215" x2="1221" y1="-582" y2="-578"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1215" x2="1221" y1="-578" y2="-574"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_LJYK.CX_LJYK_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="50390"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.639623 -0.000000 0.000000 -1.647727 1042.000000 -453.352273)" xlink:href="#transformer:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="50392"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.639623 -0.000000 0.000000 -1.647727 1042.000000 -453.352273)" xlink:href="#transformer:shape3_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="50394"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.639623 -0.000000 0.000000 -1.647727 1042.000000 -453.352273)" xlink:href="#transformer:shape3-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="49819" ObjectName="TF-CX_LJYK.CX_LJYK_1T"/>
    <cge:TPSR_Ref TObjectID="49819"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 393.449774 -1130.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.449774 -1124.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 268.013262 244.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 427.013262 242.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 573.013262 242.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.013262 240.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.013262 245.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1783.013262 243.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1929.013262 243.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2073.013262 241.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1970.000000 -503.000000)" xlink:href="#load:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_1aa2180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1111,-565 1342,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49819@x" ObjectIDZND0="49764@x" ObjectIDZND1="g_1aa0dc0@0" Pin0InfoVect0LinkObjId="SW-320439_0" Pin0InfoVect1LinkObjId="g_1aa0dc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_191c620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1111,-565 1342,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1aa2740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1342,-565 1342,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_1aa0dc0@0" ObjectIDND1="49819@x" ObjectIDZND0="49764@1" Pin0InfoVect0LinkObjId="SW-320439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aa0dc0_0" Pin1InfoVect1LinkObjId="g_191c620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1342,-565 1342,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1aa2930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-549 1378,-565 1342,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="g_1aa0dc0@1" ObjectIDZND0="49764@x" ObjectIDZND1="49819@x" Pin0InfoVect0LinkObjId="SW-320439_0" Pin0InfoVect1LinkObjId="g_191c620_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa0dc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-549 1378,-565 1342,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1aa2b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1308,-548 1308,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1b19440@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b19440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1308,-548 1308,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0c270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-1070 398,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_1ad7350@0" ObjectIDZND0="g_1ad6a00@0" ObjectIDZND1="0@x" ObjectIDZND2="49754@x" Pin0InfoVect0LinkObjId="g_1ad6a00_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-320427_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad7350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="434,-1070 398,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="366,-1095 398,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1ad6a00@0" ObjectIDZND0="g_1ad7350@0" ObjectIDZND1="49754@x" ObjectIDZND2="49752@x" Pin0InfoVect0LinkObjId="g_1ad7350_0" Pin0InfoVect1LinkObjId="SW-320427_0" Pin0InfoVect2LinkObjId="SW-320425_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad6a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="366,-1095 398,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0d310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-1070 398,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_1ad7350@0" ObjectIDND1="49754@x" ObjectIDND2="49752@x" ObjectIDZND0="g_1ad6a00@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1ad6a00_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ad7350_0" Pin1InfoVect1LinkObjId="SW-320427_0" Pin1InfoVect2LinkObjId="SW-320425_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="398,-1070 398,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0d500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-1095 398,-1135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1ad6a00@0" ObjectIDND1="g_1ad7350@0" ObjectIDND2="49754@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ad6a00_0" Pin1InfoVect1LinkObjId="g_1ad7350_0" Pin1InfoVect2LinkObjId="SW-320427_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="398,-1095 398,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0d6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="351,-1026 398,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49754@0" ObjectIDZND0="g_1ad7350@0" ObjectIDZND1="g_1ad6a00@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1ad7350_0" Pin0InfoVect1LinkObjId="g_1ad6a00_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="351,-1026 398,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0df40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-1026 398,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49754@x" ObjectIDND1="49752@x" ObjectIDZND0="g_1ad7350@0" ObjectIDZND1="g_1ad6a00@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1ad7350_0" Pin0InfoVect1LinkObjId="g_1ad6a00_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320427_0" Pin1InfoVect1LinkObjId="SW-320425_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="398,-1026 398,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0e130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="350,-927 398,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49753@0" ObjectIDZND0="49749@x" ObjectIDZND1="49752@x" Pin0InfoVect0LinkObjId="SW-320422_0" Pin0InfoVect1LinkObjId="SW-320425_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="350,-927 398,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0e980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-906 398,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49749@1" ObjectIDZND0="49753@x" ObjectIDZND1="49752@x" Pin0InfoVect0LinkObjId="SW-320426_0" Pin0InfoVect1LinkObjId="SW-320425_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="398,-906 398,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="351,-858 398,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49751@0" ObjectIDZND0="49749@x" ObjectIDZND1="49750@x" Pin0InfoVect0LinkObjId="SW-320422_0" Pin0InfoVect1LinkObjId="SW-320423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320424_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="351,-858 398,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1b0f4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-858 398,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49751@x" ObjectIDND1="49750@x" ObjectIDZND0="49749@0" Pin0InfoVect0LinkObjId="SW-320422_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320424_0" Pin1InfoVect1LinkObjId="SW-320423_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="398,-858 398,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a67a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-1064 916,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_1a81170@0" ObjectIDZND0="g_1a807a0@0" ObjectIDZND1="0@x" ObjectIDZND2="49748@x" Pin0InfoVect0LinkObjId="g_1a807a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-320419_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a81170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="952,-1064 916,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a67cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="884,-1089 916,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1a807a0@0" ObjectIDZND0="g_1a81170@0" ObjectIDZND1="49748@x" ObjectIDZND2="49746@x" Pin0InfoVect0LinkObjId="g_1a81170_0" Pin0InfoVect1LinkObjId="SW-320419_0" Pin0InfoVect2LinkObjId="SW-320417_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a807a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="884,-1089 916,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a67ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-1064 916,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_1a81170@0" ObjectIDND1="49748@x" ObjectIDND2="49746@x" ObjectIDZND0="g_1a807a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1a807a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a81170_0" Pin1InfoVect1LinkObjId="SW-320419_0" Pin1InfoVect2LinkObjId="SW-320417_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="916,-1064 916,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a680f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-1089 916,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1a807a0@0" ObjectIDND1="g_1a81170@0" ObjectIDND2="49748@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a807a0_0" Pin1InfoVect1LinkObjId="g_1a81170_0" Pin1InfoVect2LinkObjId="SW-320419_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-1089 916,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a68310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="869,-1020 916,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49748@0" ObjectIDZND0="g_1a81170@0" ObjectIDZND1="g_1a807a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a81170_0" Pin0InfoVect1LinkObjId="g_1a807a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="869,-1020 916,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a68530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-1020 916,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49748@x" ObjectIDND1="49746@x" ObjectIDZND0="g_1a81170@0" ObjectIDZND1="g_1a807a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a81170_0" Pin0InfoVect1LinkObjId="g_1a807a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320419_0" Pin1InfoVect1LinkObjId="SW-320417_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="916,-1020 916,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a68750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-921 916,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49747@0" ObjectIDZND0="49743@x" ObjectIDZND1="49746@x" Pin0InfoVect0LinkObjId="SW-320414_0" Pin0InfoVect1LinkObjId="SW-320417_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="868,-921 916,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a68970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-900 916,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49743@1" ObjectIDZND0="49747@x" ObjectIDZND1="49746@x" Pin0InfoVect0LinkObjId="SW-320418_0" Pin0InfoVect1LinkObjId="SW-320417_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="916,-900 916,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a68b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="869,-852 916,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49745@0" ObjectIDZND0="49743@x" ObjectIDZND1="49744@x" Pin0InfoVect0LinkObjId="SW-320414_0" Pin0InfoVect1LinkObjId="SW-320415_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="869,-852 916,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a68db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-852 916,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49745@x" ObjectIDND1="49744@x" ObjectIDZND0="49743@0" Pin0InfoVect0LinkObjId="SW-320414_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320416_0" Pin1InfoVect1LinkObjId="SW-320415_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-852 916,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1acca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,-615 664,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="49756@0" ObjectIDZND0="g_1a4e460@0" ObjectIDZND1="g_1a4ff20@0" ObjectIDZND2="49755@x" Pin0InfoVect0LinkObjId="g_1a4e460_0" Pin0InfoVect1LinkObjId="g_1a4ff20_0" Pin0InfoVect2LinkObjId="SW-320430_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="695,-615 664,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1acd4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="664,-651 664,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="49755@0" ObjectIDZND0="49756@x" ObjectIDZND1="g_1a4e460@0" ObjectIDZND2="g_1a4ff20@0" Pin0InfoVect0LinkObjId="SW-320431_0" Pin0InfoVect1LinkObjId="g_1a4e460_0" Pin0InfoVect2LinkObjId="g_1a4ff20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="664,-651 664,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1acd6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-706 664,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="49757@0" ObjectIDZND0="49739@0" ObjectIDZND1="49755@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-320430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320432_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="625,-706 664,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a4e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="664,-767 664,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49739@0" ObjectIDZND0="49757@x" ObjectIDZND1="49755@x" Pin0InfoVect0LinkObjId="SW-320432_0" Pin0InfoVect1LinkObjId="SW-320430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acd6f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="664,-767 664,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a4e240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="664,-706 664,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="49757@x" ObjectIDND1="49739@0" ObjectIDZND0="49755@1" Pin0InfoVect0LinkObjId="SW-320430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320432_0" Pin1InfoVect1LinkObjId="g_1acd6f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="664,-706 664,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a4f050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="620,-568 664,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1a4e460@0" ObjectIDZND0="49756@x" ObjectIDZND1="49755@x" ObjectIDZND2="g_1a4ff20@0" Pin0InfoVect0LinkObjId="SW-320431_0" Pin0InfoVect1LinkObjId="SW-320430_0" Pin0InfoVect2LinkObjId="g_1a4ff20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a4e460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="620,-568 664,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a4fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="664,-615 664,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="49756@x" ObjectIDND1="49755@x" ObjectIDZND0="g_1a4e460@0" ObjectIDZND1="g_1a4ff20@0" Pin0InfoVect0LinkObjId="g_1a4e460_0" Pin0InfoVect1LinkObjId="g_1a4ff20_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320431_0" Pin1InfoVect1LinkObjId="SW-320430_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="664,-615 664,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a4fd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="664,-568 664,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1a4e460@0" ObjectIDND1="49756@x" ObjectIDND2="49755@x" ObjectIDZND0="g_1a4ff20@0" Pin0InfoVect0LinkObjId="g_1a4ff20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a4e460_0" Pin1InfoVect1LinkObjId="SW-320431_0" Pin1InfoVect2LinkObjId="SW-320430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="664,-568 664,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ab4630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1412,-862 1380,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49760@0" ObjectIDZND0="49758@x" ObjectIDZND1="49759@x" Pin0InfoVect0LinkObjId="SW-320433_0" Pin0InfoVect1LinkObjId="SW-320434_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1412,-862 1380,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ab5100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1380,-862 1380,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49760@x" ObjectIDND1="49759@x" ObjectIDZND0="49758@0" Pin0InfoVect0LinkObjId="SW-320433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320435_0" Pin1InfoVect1LinkObjId="SW-320434_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1380,-862 1380,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ab5f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1308,-506 1308,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_1b19440@1" ObjectIDZND0="g_1ab5780@0" Pin0InfoVect0LinkObjId="g_1ab5780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b19440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1308,-506 1308,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ab6170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1342,-513 1342,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49764@0" ObjectIDZND0="g_1aa13b0@0" Pin0InfoVect0LinkObjId="g_1aa13b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1342,-513 1342,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ab63d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1378,-509 1378,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_1aa0dc0@0" ObjectIDZND0="g_1ab5b70@0" Pin0InfoVect0LinkObjId="g_1ab5b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa0dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-509 1378,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ac2710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-101 980,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="49740@0" ObjectIDZND0="49766@0" Pin0InfoVect0LinkObjId="SW-320441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a70e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="980,-101 980,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ac2970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-144 980,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49766@1" ObjectIDZND0="49765@0" Pin0InfoVect0LinkObjId="SW-320440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="980,-144 980,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ac2bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-193 980,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49765@1" ObjectIDZND0="49767@0" Pin0InfoVect0LinkObjId="SW-320441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="980,-193 980,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ad9b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-274 980,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_1ac2e30@0" ObjectIDZND0="49767@x" ObjectIDZND1="49819@x" ObjectIDZND2="g_1b18300@0" Pin0InfoVect0LinkObjId="SW-320441_0" Pin0InfoVect1LinkObjId="g_191c620_0" Pin0InfoVect2LinkObjId="g_1b18300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ac2e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="944,-274 980,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ada640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-230 980,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="49767@1" ObjectIDZND0="g_1ac2e30@0" ObjectIDZND1="49819@x" ObjectIDZND2="g_1b18300@0" Pin0InfoVect0LinkObjId="g_1ac2e30_0" Pin0InfoVect1LinkObjId="g_191c620_0" Pin0InfoVect2LinkObjId="g_1b18300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="980,-230 980,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e4880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-127 1282,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49769@0" ObjectIDZND0="49741@0" Pin0InfoVect0LinkObjId="g_1a3e480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-127 1282,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e4ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-213 1282,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49770@0" ObjectIDZND0="49768@1" Pin0InfoVect0LinkObjId="SW-320442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-213 1282,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e4d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-166 1282,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49768@0" ObjectIDZND0="49769@1" Pin0InfoVect0LinkObjId="SW-320443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-166 1282,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e4fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-273 1282,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_1b18300@0" ObjectIDZND0="49770@x" ObjectIDZND1="49819@x" ObjectIDZND2="g_1ac2e30@0" Pin0InfoVect0LinkObjId="SW-320443_0" Pin0InfoVect1LinkObjId="g_191c620_0" Pin0InfoVect2LinkObjId="g_1ac2e30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b18300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-273 1282,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e5a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-273 1282,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b18300@0" ObjectIDND1="49819@x" ObjectIDND2="g_1ac2e30@0" ObjectIDZND0="49770@1" Pin0InfoVect0LinkObjId="SW-320443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b18300_0" Pin1InfoVect1LinkObjId="g_191c620_0" Pin1InfoVect2LinkObjId="g_1ac2e30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-273 1282,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a70e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,-70 873,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49812@1" ObjectIDZND0="49740@0" Pin0InfoVect0LinkObjId="g_1a62060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320494_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-70 873,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a71080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,16 873,-6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49813@1" ObjectIDZND0="49811@0" Pin0InfoVect0LinkObjId="SW-320493_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320494_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,16 873,-6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a712e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,-33 873,-53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49811@1" ObjectIDZND0="49812@0" Pin0InfoVect0LinkObjId="SW-320494_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320493_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-33 873,-53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a72140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="849,66 873,66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1a71540@0" ObjectIDZND0="49813@x" ObjectIDZND1="49814@x" ObjectIDZND2="g_1acf5b0@0" Pin0InfoVect0LinkObjId="SW-320494_0" Pin0InfoVect1LinkObjId="SW-320495_0" Pin0InfoVect2LinkObjId="g_1acf5b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a71540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="849,66 873,66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a72c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,66 873,33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a71540@0" ObjectIDND1="49814@x" ObjectIDND2="g_1acf5b0@0" ObjectIDZND0="49813@0" Pin0InfoVect0LinkObjId="SW-320494_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a71540_0" Pin1InfoVect1LinkObjId="SW-320495_0" Pin1InfoVect2LinkObjId="g_1acf5b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,66 873,33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ace860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="852,112 873,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49814@0" ObjectIDZND0="g_1a71540@0" ObjectIDZND1="49813@x" ObjectIDZND2="g_1acf5b0@0" Pin0InfoVect0LinkObjId="g_1a71540_0" Pin0InfoVect1LinkObjId="SW-320494_0" Pin0InfoVect2LinkObjId="g_1acf5b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320495_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="852,112 873,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acf350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,112 873,66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="49814@x" ObjectIDND1="g_1acf5b0@0" ObjectIDZND0="g_1a71540@0" ObjectIDZND1="49813@x" Pin0InfoVect0LinkObjId="g_1a71540_0" Pin0InfoVect1LinkObjId="SW-320494_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320495_0" Pin1InfoVect1LinkObjId="g_1acf5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="873,112 873,66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ad0030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,151 873,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1acf5b0@1" ObjectIDZND0="49814@x" ObjectIDZND1="g_1a71540@0" ObjectIDZND2="49813@x" Pin0InfoVect0LinkObjId="SW-320495_0" Pin0InfoVect1LinkObjId="g_1a71540_0" Pin0InfoVect2LinkObjId="SW-320494_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acf5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="873,151 873,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a62060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="722,-69 722,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49776@1" ObjectIDZND0="49740@0" Pin0InfoVect0LinkObjId="g_1a70e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320449_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="722,-69 722,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a622c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="722,17 722,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49777@1" ObjectIDZND0="49775@0" Pin0InfoVect0LinkObjId="SW-320448_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320449_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="722,17 722,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a62520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="722,-32 722,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49775@1" ObjectIDZND0="49776@0" Pin0InfoVect0LinkObjId="SW-320449_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320448_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="722,-32 722,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a92100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="698,67 722,67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1a62780@0" ObjectIDZND0="49777@x" ObjectIDZND1="49778@x" ObjectIDZND2="g_1a95c10@0" Pin0InfoVect0LinkObjId="SW-320449_0" Pin0InfoVect1LinkObjId="SW-320450_0" Pin0InfoVect2LinkObjId="g_1a95c10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="698,67 722,67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a92360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="722,67 722,34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a62780@0" ObjectIDND1="49778@x" ObjectIDND2="g_1a95c10@0" ObjectIDZND0="49777@0" Pin0InfoVect0LinkObjId="SW-320449_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a62780_0" Pin1InfoVect1LinkObjId="SW-320450_0" Pin1InfoVect2LinkObjId="g_1a95c10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="722,67 722,34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a95750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="701,113 722,113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49778@0" ObjectIDZND0="49777@x" ObjectIDZND1="g_1a62780@0" ObjectIDZND2="g_1a95c10@0" Pin0InfoVect0LinkObjId="SW-320449_0" Pin0InfoVect1LinkObjId="g_1a62780_0" Pin0InfoVect2LinkObjId="g_1a95c10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="701,113 722,113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a959b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="722,113 722,67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49778@x" ObjectIDND1="g_1a95c10@0" ObjectIDZND0="49777@x" ObjectIDZND1="g_1a62780@0" Pin0InfoVect0LinkObjId="SW-320449_0" Pin0InfoVect1LinkObjId="g_1a62780_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320450_0" Pin1InfoVect1LinkObjId="g_1a95c10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="722,113 722,67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a96690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="722,219 722,186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1a95c10@0" Pin0InfoVect0LinkObjId="g_1a95c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="722,219 722,186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a968f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="722,152 722,113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1a95c10@1" ObjectIDZND0="49777@x" ObjectIDZND1="g_1a62780@0" ObjectIDZND2="49778@x" Pin0InfoVect0LinkObjId="SW-320449_0" Pin0InfoVect1LinkObjId="g_1a62780_0" Pin0InfoVect2LinkObjId="SW-320450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a95c10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="722,152 722,113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a00d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-67 578,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49780@1" ObjectIDZND0="49740@0" Pin0InfoVect0LinkObjId="g_1a70e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-67 578,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a00fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,19 578,-3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49781@1" ObjectIDZND0="49779@0" Pin0InfoVect0LinkObjId="SW-320453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,19 578,-3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a01200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-30 578,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49779@1" ObjectIDZND0="49780@0" Pin0InfoVect0LinkObjId="SW-320454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-30 578,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a02190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,69 578,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1a01460@0" ObjectIDZND0="49781@x" ObjectIDZND1="49782@x" ObjectIDZND2="g_1a05ca0@0" Pin0InfoVect0LinkObjId="SW-320454_0" Pin0InfoVect1LinkObjId="SW-320455_0" Pin0InfoVect2LinkObjId="g_1a05ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a01460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="554,69 578,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a023f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,69 578,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a01460@0" ObjectIDND1="49782@x" ObjectIDND2="g_1a05ca0@0" ObjectIDZND0="49781@0" Pin0InfoVect0LinkObjId="SW-320454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a01460_0" Pin1InfoVect1LinkObjId="SW-320455_0" Pin1InfoVect2LinkObjId="g_1a05ca0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,69 578,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a057e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="557,115 578,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49782@0" ObjectIDZND0="49781@x" ObjectIDZND1="g_1a01460@0" ObjectIDZND2="g_1a05ca0@0" Pin0InfoVect0LinkObjId="SW-320454_0" Pin0InfoVect1LinkObjId="g_1a01460_0" Pin0InfoVect2LinkObjId="g_1a05ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320455_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="557,115 578,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a05a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,115 578,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49782@x" ObjectIDND1="g_1a05ca0@0" ObjectIDZND0="49781@x" ObjectIDZND1="g_1a01460@0" Pin0InfoVect0LinkObjId="SW-320454_0" Pin0InfoVect1LinkObjId="g_1a01460_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320455_0" Pin1InfoVect1LinkObjId="g_1a05ca0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="578,115 578,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f65b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,221 578,188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1a05ca0@0" Pin0InfoVect0LinkObjId="g_1a05ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,221 578,188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f6810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,154 578,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1a05ca0@1" ObjectIDZND0="49781@x" ObjectIDZND1="g_1a01460@0" ObjectIDZND2="49782@x" Pin0InfoVect0LinkObjId="SW-320454_0" Pin0InfoVect1LinkObjId="g_1a01460_0" Pin0InfoVect2LinkObjId="SW-320455_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a05ca0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="578,154 578,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a469d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-67 432,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49784@1" ObjectIDZND0="49740@0" Pin0InfoVect0LinkObjId="g_1a70e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320459_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-67 432,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a46c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,19 432,-3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49785@1" ObjectIDZND0="49783@0" Pin0InfoVect0LinkObjId="SW-320458_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320459_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,19 432,-3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a46e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-30 432,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49783@1" ObjectIDZND0="49784@0" Pin0InfoVect0LinkObjId="SW-320459_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320458_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-30 432,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a47e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="408,69 432,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1a470f0@0" ObjectIDZND0="49785@x" ObjectIDZND1="49786@x" ObjectIDZND2="g_1a08580@0" Pin0InfoVect0LinkObjId="SW-320459_0" Pin0InfoVect1LinkObjId="SW-320460_0" Pin0InfoVect2LinkObjId="g_1a08580_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a470f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="408,69 432,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a48080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,69 432,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a470f0@0" ObjectIDND1="49786@x" ObjectIDND2="g_1a08580@0" ObjectIDZND0="49785@0" Pin0InfoVect0LinkObjId="SW-320459_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a470f0_0" Pin1InfoVect1LinkObjId="SW-320460_0" Pin1InfoVect2LinkObjId="g_1a08580_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,69 432,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a080c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="411,115 432,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49786@0" ObjectIDZND0="49785@x" ObjectIDZND1="g_1a470f0@0" ObjectIDZND2="g_1a08580@0" Pin0InfoVect0LinkObjId="SW-320459_0" Pin0InfoVect1LinkObjId="g_1a470f0_0" Pin0InfoVect2LinkObjId="g_1a08580_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="411,115 432,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a08320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,115 432,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49786@x" ObjectIDND1="g_1a08580@0" ObjectIDZND0="49785@x" ObjectIDZND1="g_1a470f0@0" Pin0InfoVect0LinkObjId="SW-320459_0" Pin0InfoVect1LinkObjId="g_1a470f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320460_0" Pin1InfoVect1LinkObjId="g_1a08580_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="432,115 432,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a09000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,221 432,188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1a08580@0" Pin0InfoVect0LinkObjId="g_1a08580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,221 432,188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a09260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,154 432,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1a08580@1" ObjectIDZND0="49785@x" ObjectIDZND1="g_1a470f0@0" ObjectIDZND2="49786@x" Pin0InfoVect0LinkObjId="SW-320459_0" Pin0InfoVect1LinkObjId="g_1a470f0_0" Pin0InfoVect2LinkObjId="SW-320460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a08580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="432,154 432,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1976ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,-65 273,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49788@1" ObjectIDZND0="49740@0" Pin0InfoVect0LinkObjId="g_1a70e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320464_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="273,-65 273,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1977150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,21 273,-1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49789@1" ObjectIDZND0="49787@0" Pin0InfoVect0LinkObjId="SW-320463_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320464_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="273,21 273,-1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19773b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,-28 273,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49787@1" ObjectIDZND0="49788@0" Pin0InfoVect0LinkObjId="SW-320464_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320463_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="273,-28 273,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1978340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="249,71 273,71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1977610@0" ObjectIDZND0="49789@x" ObjectIDZND1="49790@x" ObjectIDZND2="g_197be50@0" Pin0InfoVect0LinkObjId="SW-320464_0" Pin0InfoVect1LinkObjId="SW-320465_0" Pin0InfoVect2LinkObjId="g_197be50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1977610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="249,71 273,71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19785a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,71 273,38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1977610@0" ObjectIDND1="49790@x" ObjectIDND2="g_197be50@0" ObjectIDZND0="49789@0" Pin0InfoVect0LinkObjId="SW-320464_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1977610_0" Pin1InfoVect1LinkObjId="SW-320465_0" Pin1InfoVect2LinkObjId="g_197be50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="273,71 273,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_197b990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="252,117 273,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49790@0" ObjectIDZND0="49789@x" ObjectIDZND1="g_1977610@0" ObjectIDZND2="g_197be50@0" Pin0InfoVect0LinkObjId="SW-320464_0" Pin0InfoVect1LinkObjId="g_1977610_0" Pin0InfoVect2LinkObjId="g_197be50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320465_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="252,117 273,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_197bbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,117 273,71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49790@x" ObjectIDND1="g_197be50@0" ObjectIDZND0="49789@x" ObjectIDZND1="g_1977610@0" Pin0InfoVect0LinkObjId="SW-320464_0" Pin0InfoVect1LinkObjId="g_1977610_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320465_0" Pin1InfoVect1LinkObjId="g_197be50_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="273,117 273,71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_197c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,223 273,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_197be50@0" Pin0InfoVect0LinkObjId="g_197be50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="273,223 273,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_197cb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,156 273,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_197be50@1" ObjectIDZND0="49789@x" ObjectIDZND1="g_1977610@0" ObjectIDZND2="49790@x" Pin0InfoVect0LinkObjId="SW-320464_0" Pin0InfoVect1LinkObjId="g_1977610_0" Pin0InfoVect2LinkObjId="SW-320465_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_197be50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="273,156 273,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a56a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,209 873,185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1acf5b0@0" Pin0InfoVect0LinkObjId="g_1acf5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,209 873,185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a56cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="854,264 873,264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1a2c050@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1a2c050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="854,264 873,264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a577c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,264 873,245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1a2c050@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1a2c050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,264 873,245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5a320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,299 873,281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1a2c050@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1a2c050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="873,299 873,281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5a580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,281 873,264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_1a2c050@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1a2c050_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="873,281 873,264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5b070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,377 873,350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1a2c050@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1a2c050_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="873,377 873,350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5b2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,350 873,326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="reactance" EndDevType0="breaker" ObjectIDND0="g_1a2c050@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a2c050_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,350 873,326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5b530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,281 914,281 914,298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1a2c050@0" Pin0InfoVect0LinkObjId="g_1a2c050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,281 914,281 914,298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5b790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="914,332 914,350 873,350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="reactance" ObjectIDND0="g_1a2c050@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a2c050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="914,332 914,350 873,350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5c720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,425 873,425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="reactance" ObjectIDND0="g_1a5b9f0@0" ObjectIDZND0="g_1ad5b20@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1ad5b20_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a5b9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="897,425 873,425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5d210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,435 873,425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="reactance" ObjectIDND0="g_1ad5b20@0" ObjectIDZND0="g_1a5b9f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1a5b9f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad5b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="873,435 873,425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a5d470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,425 873,419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="reactance" ObjectIDND0="g_1a5b9f0@0" ObjectIDND1="g_1ad5b20@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a5b9f0_0" Pin1InfoVect1LinkObjId="g_1ad5b20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,425 873,419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19dea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-101 1012,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="49740@0" ObjectIDZND0="49771@1" Pin0InfoVect0LinkObjId="SW-320446_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a70e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-101 1012,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19dec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-47 1012,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="49771@0" ObjectIDZND0="49772@1" Pin0InfoVect0LinkObjId="SW-320446_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320446_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-47 1012,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19df8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,99 1012,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_19deef0@1" ObjectIDZND0="g_19dfb20@0" Pin0InfoVect0LinkObjId="g_19dfb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19deef0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,99 1012,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e32a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,49 1012,49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_19e2570@0" ObjectIDZND0="49772@x" ObjectIDZND1="g_19deef0@0" Pin0InfoVect0LinkObjId="SW-320446_0" Pin0InfoVect1LinkObjId="g_19deef0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19e2570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1061,49 1012,49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e3d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,30 1012,49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="49772@0" ObjectIDZND0="g_19e2570@0" ObjectIDZND1="g_19deef0@0" Pin0InfoVect0LinkObjId="g_19e2570_0" Pin0InfoVect1LinkObjId="g_19deef0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320446_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1012,30 1012,49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e3ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,49 1012,67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_19e2570@0" ObjectIDND1="49772@x" ObjectIDZND0="g_19deef0@0" Pin0InfoVect0LinkObjId="g_19deef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19e2570_0" Pin1InfoVect1LinkObjId="SW-320446_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,49 1012,67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19efed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-99 1244,-62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="49741@0" ObjectIDZND0="49773@1" Pin0InfoVect0LinkObjId="SW-320447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19e4880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-99 1244,-62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f0130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-45 1244,15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="49773@0" ObjectIDZND0="49774@1" Pin0InfoVect0LinkObjId="SW-320447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-45 1244,15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f0d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,101 1244,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_19f0390@1" ObjectIDZND0="g_19f0fc0@0" Pin0InfoVect0LinkObjId="g_19f0fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19f0390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,101 1244,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f4740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1293,51 1244,51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_19f3a10@0" ObjectIDZND0="49774@x" ObjectIDZND1="g_19f0390@0" Pin0InfoVect0LinkObjId="SW-320447_0" Pin0InfoVect1LinkObjId="g_19f0390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19f3a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1293,51 1244,51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f49a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,32 1244,51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="49774@0" ObjectIDZND0="g_19f3a10@0" ObjectIDZND1="g_19f0390@0" Pin0InfoVect0LinkObjId="g_19f3a10_0" Pin0InfoVect1LinkObjId="g_19f0390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1244,32 1244,51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f4c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,51 1244,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="49774@x" ObjectIDND1="g_19f3a10@0" ObjectIDZND0="g_19f0390@0" Pin0InfoVect0LinkObjId="g_19f0390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320447_0" Pin1InfoVect1LinkObjId="g_19f3a10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,51 1244,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3e480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2078,-68 2078,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49804@1" ObjectIDZND0="49741@0" Pin0InfoVect0LinkObjId="g_19e4880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320484_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2078,-68 2078,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3e6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2078,18 2078,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49805@1" ObjectIDZND0="49803@0" Pin0InfoVect0LinkObjId="SW-320483_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320484_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2078,18 2078,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3e940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2078,-31 2078,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49803@1" ObjectIDZND0="49804@0" Pin0InfoVect0LinkObjId="SW-320484_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320483_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2078,-31 2078,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3f8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2054,68 2078,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1a3eba0@0" ObjectIDZND0="49805@x" ObjectIDZND1="49806@x" ObjectIDZND2="g_19ab8e0@0" Pin0InfoVect0LinkObjId="SW-320484_0" Pin0InfoVect1LinkObjId="SW-320485_0" Pin0InfoVect2LinkObjId="g_19ab8e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a3eba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2054,68 2078,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3fb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2078,68 2078,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a3eba0@0" ObjectIDND1="49806@x" ObjectIDND2="g_19ab8e0@0" ObjectIDZND0="49805@0" Pin0InfoVect0LinkObjId="SW-320484_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a3eba0_0" Pin1InfoVect1LinkObjId="SW-320485_0" Pin1InfoVect2LinkObjId="g_19ab8e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2078,68 2078,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ab420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2057,114 2078,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49806@0" ObjectIDZND0="49805@x" ObjectIDZND1="g_1a3eba0@0" ObjectIDZND2="g_19ab8e0@0" Pin0InfoVect0LinkObjId="SW-320484_0" Pin0InfoVect1LinkObjId="g_1a3eba0_0" Pin0InfoVect2LinkObjId="g_19ab8e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2057,114 2078,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ab680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2078,114 2078,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49806@x" ObjectIDND1="g_19ab8e0@0" ObjectIDZND0="49805@x" ObjectIDZND1="g_1a3eba0@0" Pin0InfoVect0LinkObjId="SW-320484_0" Pin0InfoVect1LinkObjId="g_1a3eba0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320485_0" Pin1InfoVect1LinkObjId="g_19ab8e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2078,114 2078,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ac300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2078,220 2078,187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_19ab8e0@0" Pin0InfoVect0LinkObjId="g_19ab8e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2078,220 2078,187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ac560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2078,153 2078,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_19ab8e0@1" ObjectIDZND0="49805@x" ObjectIDZND1="g_1a3eba0@0" ObjectIDZND2="49806@x" Pin0InfoVect0LinkObjId="SW-320484_0" Pin0InfoVect1LinkObjId="g_1a3eba0_0" Pin0InfoVect2LinkObjId="SW-320485_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19ab8e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2078,153 2078,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b4a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1934,-66 1934,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49800@1" ObjectIDZND0="49741@0" Pin0InfoVect0LinkObjId="g_19e4880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320479_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1934,-66 1934,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b4cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1934,20 1934,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49801@1" ObjectIDZND0="49799@0" Pin0InfoVect0LinkObjId="SW-320478_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320479_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1934,20 1934,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b4f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1934,-29 1934,-49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49799@1" ObjectIDZND0="49800@0" Pin0InfoVect0LinkObjId="SW-320479_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320478_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1934,-29 1934,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b5ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1910,70 1934,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_19b5170@0" ObjectIDZND0="49801@x" ObjectIDZND1="49802@x" ObjectIDZND2="g_19b99b0@0" Pin0InfoVect0LinkObjId="SW-320479_0" Pin0InfoVect1LinkObjId="SW-320480_0" Pin0InfoVect2LinkObjId="g_19b99b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19b5170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1910,70 1934,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b6100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1934,70 1934,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_19b5170@0" ObjectIDND1="49802@x" ObjectIDND2="g_19b99b0@0" ObjectIDZND0="49801@0" Pin0InfoVect0LinkObjId="SW-320479_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_19b5170_0" Pin1InfoVect1LinkObjId="SW-320480_0" Pin1InfoVect2LinkObjId="g_19b99b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1934,70 1934,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b94f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1913,116 1934,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49802@0" ObjectIDZND0="49801@x" ObjectIDZND1="g_19b5170@0" ObjectIDZND2="g_19b99b0@0" Pin0InfoVect0LinkObjId="SW-320479_0" Pin0InfoVect1LinkObjId="g_19b5170_0" Pin0InfoVect2LinkObjId="g_19b99b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1913,116 1934,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b9750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1934,116 1934,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49802@x" ObjectIDND1="g_19b99b0@0" ObjectIDZND0="49801@x" ObjectIDZND1="g_19b5170@0" Pin0InfoVect0LinkObjId="SW-320479_0" Pin0InfoVect1LinkObjId="g_19b5170_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320480_0" Pin1InfoVect1LinkObjId="g_19b99b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1934,116 1934,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ba430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1934,222 1934,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_19b99b0@0" Pin0InfoVect0LinkObjId="g_19b99b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1934,222 1934,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19984d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1934,155 1934,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_19b99b0@1" ObjectIDZND0="49801@x" ObjectIDZND1="g_19b5170@0" ObjectIDZND2="49802@x" Pin0InfoVect0LinkObjId="SW-320479_0" Pin0InfoVect1LinkObjId="g_19b5170_0" Pin0InfoVect2LinkObjId="SW-320480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19b99b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1934,155 1934,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a07c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1788,-66 1788,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49796@1" ObjectIDZND0="49741@0" Pin0InfoVect0LinkObjId="g_19e4880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-66 1788,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a0a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1788,20 1788,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49797@1" ObjectIDZND0="49795@0" Pin0InfoVect0LinkObjId="SW-320473_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,20 1788,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a0c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1788,-29 1788,-49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49795@1" ObjectIDZND0="49796@0" Pin0InfoVect0LinkObjId="SW-320474_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320473_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-29 1788,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a1c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1764,70 1788,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_19a0ee0@0" ObjectIDZND0="49797@x" ObjectIDZND1="49798@x" ObjectIDZND2="g_19a5720@0" Pin0InfoVect0LinkObjId="SW-320474_0" Pin0InfoVect1LinkObjId="SW-320475_0" Pin0InfoVect2LinkObjId="g_19a5720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19a0ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1764,70 1788,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a1e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1788,70 1788,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_19a0ee0@0" ObjectIDND1="49798@x" ObjectIDND2="g_19a5720@0" ObjectIDZND0="49797@0" Pin0InfoVect0LinkObjId="SW-320474_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_19a0ee0_0" Pin1InfoVect1LinkObjId="SW-320475_0" Pin1InfoVect2LinkObjId="g_19a5720_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,70 1788,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a5260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1767,116 1788,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49798@0" ObjectIDZND0="49797@x" ObjectIDZND1="g_19a0ee0@0" ObjectIDZND2="g_19a5720@0" Pin0InfoVect0LinkObjId="SW-320474_0" Pin0InfoVect1LinkObjId="g_19a0ee0_0" Pin0InfoVect2LinkObjId="g_19a5720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1767,116 1788,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a54c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1788,116 1788,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49798@x" ObjectIDND1="g_19a5720@0" ObjectIDZND0="49797@x" ObjectIDZND1="g_19a0ee0@0" Pin0InfoVect0LinkObjId="SW-320474_0" Pin0InfoVect1LinkObjId="g_19a0ee0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320475_0" Pin1InfoVect1LinkObjId="g_19a5720_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1788,116 1788,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a61a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1788,222 1788,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_19a5720@0" Pin0InfoVect0LinkObjId="g_19a5720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,222 1788,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a6400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1788,155 1788,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_19a5720@1" ObjectIDZND0="49797@x" ObjectIDZND1="g_19a0ee0@0" ObjectIDZND2="49798@x" Pin0InfoVect0LinkObjId="SW-320474_0" Pin0InfoVect1LinkObjId="g_19a0ee0_0" Pin0InfoVect2LinkObjId="SW-320475_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19a5720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1788,155 1788,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c8a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1629,-64 1629,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49792@1" ObjectIDZND0="49741@0" Pin0InfoVect0LinkObjId="g_19e4880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1629,-64 1629,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c8c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1629,22 1629,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49793@1" ObjectIDZND0="49791@0" Pin0InfoVect0LinkObjId="SW-320468_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1629,22 1629,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c8ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1629,-27 1629,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49791@1" ObjectIDZND0="49792@0" Pin0InfoVect0LinkObjId="SW-320469_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320468_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1629,-27 1629,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c9e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1605,72 1629,72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_19c9140@0" ObjectIDZND0="49793@x" ObjectIDZND1="49794@x" ObjectIDZND2="g_19cd980@0" Pin0InfoVect0LinkObjId="SW-320469_0" Pin0InfoVect1LinkObjId="SW-320470_0" Pin0InfoVect2LinkObjId="g_19cd980_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19c9140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1605,72 1629,72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ca0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1629,72 1629,39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_19c9140@0" ObjectIDND1="49794@x" ObjectIDND2="g_19cd980@0" ObjectIDZND0="49793@0" Pin0InfoVect0LinkObjId="SW-320469_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_19c9140_0" Pin1InfoVect1LinkObjId="SW-320470_0" Pin1InfoVect2LinkObjId="g_19cd980_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1629,72 1629,39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19cd4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,118 1629,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49794@0" ObjectIDZND0="49793@x" ObjectIDZND1="g_19c9140@0" ObjectIDZND2="g_19cd980@0" Pin0InfoVect0LinkObjId="SW-320469_0" Pin0InfoVect1LinkObjId="g_19c9140_0" Pin0InfoVect2LinkObjId="g_19cd980_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1608,118 1629,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19cd720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1629,118 1629,72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49794@x" ObjectIDND1="g_19cd980@0" ObjectIDZND0="49793@x" ObjectIDZND1="g_19c9140@0" Pin0InfoVect0LinkObjId="SW-320469_0" Pin0InfoVect1LinkObjId="g_19c9140_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320470_0" Pin1InfoVect1LinkObjId="g_19cd980_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1629,118 1629,72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ce400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1629,224 1629,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_19cd980@0" Pin0InfoVect0LinkObjId="g_19cd980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1629,224 1629,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ce660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1629,157 1629,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_19cd980@1" ObjectIDZND0="49793@x" ObjectIDZND1="g_19c9140@0" ObjectIDZND2="49794@x" Pin0InfoVect0LinkObjId="SW-320469_0" Pin0InfoVect1LinkObjId="g_19c9140_0" Pin0InfoVect2LinkObjId="SW-320470_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19cd980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1629,157 1629,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1997b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-67 1442,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49816@1" ObjectIDZND0="49741@0" Pin0InfoVect0LinkObjId="g_19e4880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320499_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-67 1442,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1997d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,19 1442,-3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49817@1" ObjectIDZND0="49815@0" Pin0InfoVect0LinkObjId="SW-320498_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320499_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,19 1442,-3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1997fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-30 1442,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49815@1" ObjectIDZND0="49816@0" Pin0InfoVect0LinkObjId="SW-320499_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320498_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-30 1442,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19645f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,69 1442,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1998240@0" ObjectIDZND0="49817@x" ObjectIDZND1="49818@x" ObjectIDZND2="g_1968100@0" Pin0InfoVect0LinkObjId="SW-320499_0" Pin0InfoVect1LinkObjId="SW-320500_0" Pin0InfoVect2LinkObjId="g_1968100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1998240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1418,69 1442,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1964850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,69 1442,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1998240@0" ObjectIDND1="49818@x" ObjectIDND2="g_1968100@0" ObjectIDZND0="49817@0" Pin0InfoVect0LinkObjId="SW-320499_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1998240_0" Pin1InfoVect1LinkObjId="SW-320500_0" Pin1InfoVect2LinkObjId="g_1968100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,69 1442,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1967c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1421,115 1442,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49818@0" ObjectIDZND0="49817@x" ObjectIDZND1="g_1998240@0" ObjectIDZND2="g_1968100@0" Pin0InfoVect0LinkObjId="SW-320499_0" Pin0InfoVect1LinkObjId="g_1998240_0" Pin0InfoVect2LinkObjId="g_1968100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1421,115 1442,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1967ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,115 1442,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49818@x" ObjectIDND1="g_1968100@0" ObjectIDZND0="49817@x" ObjectIDZND1="g_1998240@0" Pin0InfoVect0LinkObjId="SW-320499_0" Pin0InfoVect1LinkObjId="g_1998240_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320500_0" Pin1InfoVect1LinkObjId="g_1968100_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1442,115 1442,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1968b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,154 1442,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1968100@1" ObjectIDZND0="49817@x" ObjectIDZND1="g_1998240@0" ObjectIDZND2="49818@x" Pin0InfoVect0LinkObjId="SW-320499_0" Pin0InfoVect1LinkObjId="g_1998240_0" Pin0InfoVect2LinkObjId="SW-320500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1968100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1442,154 1442,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_196b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,212 1442,188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1968100@0" Pin0InfoVect0LinkObjId="g_1968100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,212 1442,188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196b7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1423,267 1442,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_198d650@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_198d650_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1423,267 1442,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196ba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,267 1442,248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_198d650@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_198d650_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,267 1442,248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196dd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,302 1442,284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_198d650@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_198d650_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1442,302 1442,284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196df70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,284 1442,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_198d650@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_198d650_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1442,284 1442,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,380 1442,353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_198d650@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_198d650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1442,380 1442,353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,353 1442,329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_198d650@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_198d650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,353 1442,329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196e690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,284 1483,284 1483,301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_198d650@0" Pin0InfoVect0LinkObjId="g_198d650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,284 1483,284 1483,301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196e8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1483,335 1483,353 1442,353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="reactance" ObjectIDND0="g_198d650@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_198d650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1483,335 1483,353 1442,353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196f880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1466,428 1442,428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="reactance" ObjectIDND0="g_196eb50@0" ObjectIDZND0="g_198ef90@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_198ef90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_196eb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1466,428 1442,428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,438 1442,428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="reactance" ObjectIDND0="g_198ef90@0" ObjectIDZND0="g_196eb50@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_196eb50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_198ef90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1442,438 1442,428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_196fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,428 1442,422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="reactance" ObjectIDND0="g_198ef90@0" ObjectIDND1="g_196eb50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_198ef90_0" Pin1InfoVect1LinkObjId="g_196eb50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,428 1442,422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8ce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2236,-68 2236,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49808@1" ObjectIDZND0="49741@0" Pin0InfoVect0LinkObjId="g_19e4880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320489_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2236,-68 2236,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8d090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2236,18 2236,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49809@1" ObjectIDZND0="49807@0" Pin0InfoVect0LinkObjId="SW-320488_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320489_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2236,18 2236,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2236,-31 2236,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49807@1" ObjectIDZND0="49808@0" Pin0InfoVect0LinkObjId="SW-320489_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320488_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2236,-31 2236,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8d550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2212,68 2236,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1b95ce0@0" ObjectIDZND0="49809@x" ObjectIDZND1="49810@x" ObjectIDZND2="g_1b91060@0" Pin0InfoVect0LinkObjId="SW-320489_0" Pin0InfoVect1LinkObjId="SW-320490_0" Pin0InfoVect2LinkObjId="g_1b91060_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b95ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2212,68 2236,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b8d7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2236,68 2236,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="49810@x" ObjectIDND1="g_1b91060@0" ObjectIDND2="g_1b95ce0@0" ObjectIDZND0="49809@0" Pin0InfoVect0LinkObjId="SW-320489_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320490_0" Pin1InfoVect1LinkObjId="g_1b91060_0" Pin1InfoVect2LinkObjId="g_1b95ce0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2236,68 2236,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b90ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2215,114 2236,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49810@0" ObjectIDZND0="49809@x" ObjectIDZND1="g_1b95ce0@0" ObjectIDZND2="g_1b91060@0" Pin0InfoVect0LinkObjId="SW-320489_0" Pin0InfoVect1LinkObjId="g_1b95ce0_0" Pin0InfoVect2LinkObjId="g_1b91060_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2215,114 2236,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b90e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2236,114 2236,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49810@x" ObjectIDND1="g_1b91060@0" ObjectIDZND0="49809@x" ObjectIDZND1="g_1b95ce0@0" Pin0InfoVect0LinkObjId="SW-320489_0" Pin0InfoVect1LinkObjId="g_1b95ce0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320490_0" Pin1InfoVect1LinkObjId="g_1b91060_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2236,114 2236,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b95a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2236,155 2236,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1b91060@1" ObjectIDZND0="49809@x" ObjectIDZND1="g_1b95ce0@0" ObjectIDZND2="49810@x" Pin0InfoVect0LinkObjId="SW-320489_0" Pin0InfoVect1LinkObjId="g_1b95ce0_0" Pin0InfoVect2LinkObjId="SW-320490_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b91060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2236,155 2236,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b9a450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-430 1214,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_1b99d40@1" ObjectIDZND0="g_1b18cd0@0" Pin0InfoVect0LinkObjId="g_1b18cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b99d40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-430 1214,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1918c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1085,-513 999,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="49819@x" ObjectIDZND0="g_19192b0@0" Pin0InfoVect0LinkObjId="g_19192b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_191c620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1085,-513 999,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1918df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1062,-499 999,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="49819@x" ObjectIDZND0="g_1919fd0@0" Pin0InfoVect0LinkObjId="g_1919fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_191c620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1062,-499 999,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1919050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1085,-484 999,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="49819@x" ObjectIDZND0="g_191ad80@0" Pin0InfoVect0LinkObjId="g_191ad80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_191c620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1085,-484 999,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_191bb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-464 1139,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49819@0" ObjectIDZND0="g_1b18300@0" ObjectIDZND1="49770@x" ObjectIDZND2="g_1ac2e30@0" Pin0InfoVect0LinkObjId="g_1b18300_0" Pin0InfoVect1LinkObjId="SW-320443_0" Pin0InfoVect2LinkObjId="g_1ac2e30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_191c620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-464 1139,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_191c620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-273 1282,-303 1139,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1b18300@0" ObjectIDND1="49770@x" ObjectIDZND0="49819@x" ObjectIDZND1="g_1ac2e30@0" ObjectIDZND2="49767@x" Pin0InfoVect0LinkObjId="g_191d840_0" Pin0InfoVect1LinkObjId="g_1ac2e30_0" Pin0InfoVect2LinkObjId="SW-320441_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b18300_0" Pin1InfoVect1LinkObjId="SW-320443_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-273 1282,-303 1139,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_191c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-303 980,-303 980,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="49819@x" ObjectIDND1="g_1b18300@0" ObjectIDND2="49770@x" ObjectIDZND0="g_1ac2e30@0" ObjectIDZND1="49767@x" Pin0InfoVect0LinkObjId="g_1ac2e30_0" Pin0InfoVect1LinkObjId="SW-320441_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_191c620_0" Pin1InfoVect1LinkObjId="g_1b18300_0" Pin1InfoVect2LinkObjId="SW-320443_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-303 980,-303 980,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_191cae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1147,-499 1214,-499 1214,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="49819@x" ObjectIDZND0="g_1b99d40@0" Pin0InfoVect0LinkObjId="g_1b99d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_191c620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1147,-499 1214,-499 1214,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_191cd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1108,-592 1108,-1120 1379,-1120 1379,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49819@2" ObjectIDZND0="49763@x" ObjectIDZND1="49761@x" Pin0InfoVect0LinkObjId="SW-320438_0" Pin0InfoVect1LinkObjId="SW-320436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_191c620_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1108,-592 1108,-1120 1379,-1120 1379,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_191d840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,-1070 1379,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="49763@0" ObjectIDZND0="49819@x" ObjectIDZND1="49761@x" Pin0InfoVect0LinkObjId="g_191c620_0" Pin0InfoVect1LinkObjId="SW-320436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-1070 1379,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_191ec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1404,-943 1379,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49762@0" ObjectIDZND0="49758@x" ObjectIDZND1="49761@x" Pin0InfoVect0LinkObjId="SW-320433_0" Pin0InfoVect1LinkObjId="SW-320436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320437_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1404,-943 1379,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_191f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,-943 1379,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49762@x" ObjectIDND1="49761@x" ObjectIDZND0="49758@1" Pin0InfoVect0LinkObjId="SW-320433_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320437_0" Pin1InfoVect1LinkObjId="SW-320436_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1379,-943 1379,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1920ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-1120 1975,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-1120 1975,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19210b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-1120 2142,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-1120 2142,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1924a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-1120 1975,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-1120 1975,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1927c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-1030 1975,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-1030 1975,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_19396f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-927 398,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49753@x" ObjectIDND1="49749@x" ObjectIDZND0="49752@0" Pin0InfoVect0LinkObjId="SW-320425_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320426_0" Pin1InfoVect1LinkObjId="SW-320422_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="398,-927 398,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1939950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-993 398,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49752@1" ObjectIDZND0="49754@x" ObjectIDZND1="g_1ad7350@0" ObjectIDZND2="g_1ad6a00@0" Pin0InfoVect0LinkObjId="SW-320427_0" Pin0InfoVect1LinkObjId="g_1ad7350_0" Pin0InfoVect2LinkObjId="g_1ad6a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320425_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="398,-993 398,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1939bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-767 398,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="49739@0" ObjectIDZND0="49750@0" Pin0InfoVect0LinkObjId="SW-320423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acd6f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="398,-767 398,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1939e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-831 398,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49750@1" ObjectIDZND0="49751@x" ObjectIDZND1="49749@x" Pin0InfoVect0LinkObjId="SW-320424_0" Pin0InfoVect1LinkObjId="SW-320422_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="398,-831 398,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_193a070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-921 916,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49747@x" ObjectIDND1="49743@x" ObjectIDZND0="49746@0" Pin0InfoVect0LinkObjId="SW-320417_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320418_0" Pin1InfoVect1LinkObjId="SW-320414_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-921 916,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_193a2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-985 916,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49746@1" ObjectIDZND0="49748@x" ObjectIDZND1="g_1a81170@0" ObjectIDZND2="g_1a807a0@0" Pin0InfoVect0LinkObjId="SW-320419_0" Pin0InfoVect1LinkObjId="g_1a81170_0" Pin0InfoVect2LinkObjId="g_1a807a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320417_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="916,-985 916,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_193a530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-767 916,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="49739@0" ObjectIDZND0="49744@0" Pin0InfoVect0LinkObjId="SW-320415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acd6f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-767 916,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_193a790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-829 916,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49744@1" ObjectIDZND0="49745@x" ObjectIDZND1="49743@x" Pin0InfoVect0LinkObjId="SW-320416_0" Pin0InfoVect1LinkObjId="SW-320414_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="916,-829 916,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_193a9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,-943 1379,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49762@x" ObjectIDND1="49758@x" ObjectIDZND0="49761@0" Pin0InfoVect0LinkObjId="SW-320436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320437_0" Pin1InfoVect1LinkObjId="SW-320433_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1379,-943 1379,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_193ac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,-1017 1379,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="49761@1" ObjectIDZND0="49819@x" ObjectIDZND1="49763@x" Pin0InfoVect0LinkObjId="g_191c620_0" Pin0InfoVect1LinkObjId="SW-320438_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1379,-1017 1379,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_193aeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1380,-767 1380,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="49739@0" ObjectIDZND0="49759@0" Pin0InfoVect0LinkObjId="SW-320434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acd6f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1380,-767 1380,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_193b110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1380,-831 1380,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49759@1" ObjectIDZND0="49760@x" ObjectIDZND1="49758@x" Pin0InfoVect0LinkObjId="SW-320435_0" Pin0InfoVect1LinkObjId="SW-320433_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1380,-831 1380,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c55710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-947 1975,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-947 1975,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c58110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-891 1975,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-891 1975,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c5a7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-808 1975,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-808 1975,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c5aa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-723 1975,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-723 1975,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c72240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2236,220 2236,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c72430@0" ObjectIDZND0="g_1b91060@0" Pin0InfoVect0LinkObjId="g_1b91060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c72430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2236,220 2236,189 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-320180" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -64.000000 -996.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49722" ObjectName="DYN-CX_LJYK"/>
     <cge:Meas_Ref ObjectId="320180"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1213" cy="-413" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1214" cy="-385" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1213" cy="-399" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1377" cy="-495" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1377" cy="-481" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1204" cy="-565" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1218" cy="-565" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1975" cy="-1085" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1975" cy="-1003" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1975" cy="-1120" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1976" cy="-869" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1975" cy="-687" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJYK.CX_LJYK_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-99 2327,-99 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49741" ObjectName="BS-CX_LJYK.CX_LJYK_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="49741"/></metadata>
   <polyline fill="none" opacity="0" points="1206,-99 2327,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJYK.CX_LJYK_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,-101 1084,-101 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49740" ObjectName="BS-CX_LJYK.CX_LJYK_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="49740"/></metadata>
   <polyline fill="none" opacity="0" points="228,-101 1084,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJYK.CX_LJYK_2ⅠM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="199,-767 1505,-767 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49739" ObjectName="BS-CX_LJYK.CX_LJYK_2ⅠM"/>
    <cge:TPSR_Ref TObjectID="49739"/></metadata>
   <polyline fill="none" opacity="0" points="199,-767 1505,-767 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="49739" cx="664" cy="-767" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49739" cx="1380" cy="-767" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49740" cx="980" cy="-101" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49740" cx="873" cy="-101" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49740" cx="722" cy="-101" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49740" cx="578" cy="-101" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49740" cx="432" cy="-101" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49740" cx="273" cy="-101" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49740" cx="1012" cy="-101" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49741" cx="1282" cy="-99" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49741" cx="1244" cy="-99" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49741" cx="2078" cy="-99" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49741" cx="1934" cy="-99" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49741" cx="1788" cy="-99" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49741" cx="1629" cy="-99" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49741" cx="1442" cy="-99" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49741" cx="2236" cy="-99" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-320422">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.449774 -870.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49749" ObjectName="SW-CX_LJYK.CX_LJYK_252BK"/>
     <cge:Meas_Ref ObjectId="320422"/>
    <cge:TPSR_Ref TObjectID="49749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320414">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.449774 -864.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49743" ObjectName="SW-CX_LJYK.CX_LJYK_251BK"/>
     <cge:Meas_Ref ObjectId="320414"/>
    <cge:TPSR_Ref TObjectID="49743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320433">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1370.449774 -885.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49758" ObjectName="SW-CX_LJYK.CX_LJYK_201BK"/>
     <cge:Meas_Ref ObjectId="320433"/>
    <cge:TPSR_Ref TObjectID="49758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320440">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.013262 -157.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49765" ObjectName="SW-CX_LJYK.CX_LJYK_301BK"/>
     <cge:Meas_Ref ObjectId="320440"/>
    <cge:TPSR_Ref TObjectID="49765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320442">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1273.013262 -157.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49768" ObjectName="SW-CX_LJYK.CX_LJYK_302BK"/>
     <cge:Meas_Ref ObjectId="320442"/>
    <cge:TPSR_Ref TObjectID="49768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320493">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.013262 2.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49811" ObjectName="SW-CX_LJYK.CX_LJYK_360BK"/>
     <cge:Meas_Ref ObjectId="320493"/>
    <cge:TPSR_Ref TObjectID="49811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320448">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.013262 3.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49775" ObjectName="SW-CX_LJYK.CX_LJYK_351BK"/>
     <cge:Meas_Ref ObjectId="320448"/>
    <cge:TPSR_Ref TObjectID="49775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320453">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.013262 5.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49779" ObjectName="SW-CX_LJYK.CX_LJYK_352BK"/>
     <cge:Meas_Ref ObjectId="320453"/>
    <cge:TPSR_Ref TObjectID="49779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320458">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.013262 5.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49783" ObjectName="SW-CX_LJYK.CX_LJYK_353BK"/>
     <cge:Meas_Ref ObjectId="320458"/>
    <cge:TPSR_Ref TObjectID="49783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320463">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 264.013262 7.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49787" ObjectName="SW-CX_LJYK.CX_LJYK_354BK"/>
     <cge:Meas_Ref ObjectId="320463"/>
    <cge:TPSR_Ref TObjectID="49787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.013262 334.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320483">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2069.013262 4.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49803" ObjectName="SW-CX_LJYK.CX_LJYK_358BK"/>
     <cge:Meas_Ref ObjectId="320483"/>
    <cge:TPSR_Ref TObjectID="49803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320478">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1925.013262 6.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49799" ObjectName="SW-CX_LJYK.CX_LJYK_357BK"/>
     <cge:Meas_Ref ObjectId="320478"/>
    <cge:TPSR_Ref TObjectID="49799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320473">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.013262 6.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49795" ObjectName="SW-CX_LJYK.CX_LJYK_356BK"/>
     <cge:Meas_Ref ObjectId="320473"/>
    <cge:TPSR_Ref TObjectID="49795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1620.013262 8.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49791" ObjectName="SW-CX_LJYK.CX_LJYK_355BK"/>
     <cge:Meas_Ref ObjectId="320468"/>
    <cge:TPSR_Ref TObjectID="49791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320498">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.013262 5.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49815" ObjectName="SW-CX_LJYK.CX_LJYK_361BK"/>
     <cge:Meas_Ref ObjectId="320498"/>
    <cge:TPSR_Ref TObjectID="49815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.013262 337.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320488">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2227.013262 4.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49807" ObjectName="SW-CX_LJYK.CX_LJYK_359BK"/>
     <cge:Meas_Ref ObjectId="320488"/>
    <cge:TPSR_Ref TObjectID="49807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1966.000000 -883.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1966.000000 -715.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1908ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b64db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b64db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b64db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b64db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b64db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b64db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b64db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_17bdbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -1084.500000) translate(0,16)">陆家垭口光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1adee10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 -1207.000000) translate(0,17)">220kV罗垭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa2370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1449.000000 -591.000000) translate(0,12)">         1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa2370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1449.000000 -591.000000) translate(0,27)">SFZ18-190000/220（带平衡绕组）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa2370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1449.000000 -591.000000) translate(0,42)">230±8×1.25%/37/10.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa2370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1449.000000 -591.000000) translate(0,57)">YN,yn0+d11,0NAN/0NAF 70%/100%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa2370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1449.000000 -591.000000) translate(0,72)">U1，2=13.9%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1ad4050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.708738 264.000000) translate(0,17)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1a7d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.708738 365.000000) translate(0,17)">35kV1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1a7d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.708738 365.000000) translate(0,38)">号动态</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1a7d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.708738 365.000000) translate(0,59)">无功补</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1a7d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.708738 365.000000) translate(0,80)">偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1ad6890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -1179.000000) translate(0,17)">C  B  A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1a63510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 860.000000 -1201.000000) translate(0,17)">220kV鹿垭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1a80560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 881.000000 -1173.000000) translate(0,17)">C  B  A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19e4250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.708738 264.000000) translate(0,17)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19e9930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 392.708738 264.000000) translate(0,17)">3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19e9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.708738 264.000000) translate(0,17)">4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19f56f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2040.708738 265.000000) translate(0,17)">8号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19d1900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1886.708738 265.000000) translate(0,17)">7号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19d2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1748.708738 265.000000) translate(0,17)">6号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19d27d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.708738 265.000000) translate(0,17)">5号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_196ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.708738 373.000000) translate(0,17)">35kV2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_196ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.708738 373.000000) translate(0,38)">号动态</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_196ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.708738 373.000000) translate(0,59)">无功补</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_196ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.708738 373.000000) translate(0,80)">偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19736d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2196.708738 370.000000) translate(0,17)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b96a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2184.708738 396.000000) translate(0,17)">SCB13-400-37/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b96a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2184.708738 396.000000) translate(0,38)">D,yn11 Ud=6%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b96a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2184.708738 396.000000) translate(0,59)">37±2×2.5%0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9a9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -441.000000) translate(0,12)">71.2Ω 300A，10s</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_191e620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1307.000000 -1157.000000) translate(0,17)">C  B  A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_191f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1726.000000 -1125.000000) translate(0,12)">35kV大蛇腰变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19212a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1931.000000 -1178.000000) translate(0,12)">10kV一街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1921e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1958.000000 -1143.000000) translate(0,12)">#N13</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1924cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1935.000000 -1093.000000) translate(0,12)">#01</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1925300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1939.000000 -1011.000000) translate(0,12)">#02</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c58500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1994.500000 -1053.000000) translate(0,12)">A001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5ae10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1931.000000 -691.000000) translate(0,12)">#05</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5d530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1947.000000 -485.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5e220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2106.000000 -571.000000) translate(0,12)">2号站用变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5e220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2106.000000 -571.000000) translate(0,27)">S11-M-400kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5e220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2106.000000 -571.000000) translate(0,42)">D/yn，11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5e220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2106.000000 -571.000000) translate(0,57)">Uk=4%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5e220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2106.000000 -571.000000) translate(0,72)">10（10.5）±5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1937.000000 -874.000000) translate(0,12)">#04</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5eb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -973.000000) translate(0,12)">A011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5eda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1999.000000 -915.000000) translate(0,12)">A01</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5efe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -830.000000) translate(0,12)">A021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5f220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1999.000000 -742.000000) translate(0,12)">A02</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5f460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -820.000000) translate(0,12)">2521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5f6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 408.000000 -900.000000) translate(0,12)">252</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5f8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -884.000000) translate(0,12)">25217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -957.000000) translate(0,12)">25260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5fd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -1052.000000) translate(0,12)">25267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -982.000000) translate(0,12)">2526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c601e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 926.000000 -894.000000) translate(0,12)">251</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c60420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -818.000000) translate(0,12)">2511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c60660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 -878.000000) translate(0,12)">25117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c608a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -947.000000) translate(0,12)">25160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c60ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -1046.000000) translate(0,12)">25167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c60d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -974.000000) translate(0,12)">2516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c60f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1389.000000 -915.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c611a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 -820.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c613e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1415.000000 -888.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c61620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1407.000000 -969.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c61860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1006.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c61aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1295.000000 -1096.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c61ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -538.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c61f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.000000 -535.000000) translate(0,12)">220kV1#主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c62170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1291.000000 -187.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c623a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 989.000000 -187.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c625e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -676.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c62820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 582.000000 -732.000000) translate(0,12)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c62a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 698.000000 -641.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c62ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 183.000000 -756.000000) translate(0,12)">220kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c631e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 228.000000 -123.000000) translate(0,12)">35kVⅠ段母线2000A 31.5kA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c639e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 282.000000 -22.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c63c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 209.000000 91.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c63e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -24.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 368.000000 89.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c642c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -24.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 89.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 731.000000 -26.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 658.000000 87.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 882.000000 -27.000000) translate(0,12)">360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 808.000000 86.000000) translate(0,12)">36067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c65040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1025.000000 -31.000000) translate(0,12)">3501</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1c65280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 972.708738 200.000000) translate(0,17)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1adfb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1203.708738 202.000000) translate(0,17)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1adffb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1253.000000 -26.000000) translate(0,12)">3502</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae01f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1828.000000 -126.000000) translate(0,12)">35kVⅡ段母线2000A 31.5kA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae06b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 -24.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c67310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 89.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c67550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1638.000000 -21.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c67790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1565.000000 92.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c679d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1797.000000 -23.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c67c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1724.000000 90.000000) translate(0,12)">35667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c67e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1943.000000 -23.000000) translate(0,12)">357</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 90.000000) translate(0,12)">35767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c682d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2087.000000 -25.000000) translate(0,12)">358</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2014.000000 88.000000) translate(0,12)">35867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2245.000000 -25.000000) translate(0,12)">359</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c68990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2172.000000 88.000000) translate(0,12)">35967</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b18cd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1208.000000 -339.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa13b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.500000 -452.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab5780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.500000 -453.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab5b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1372.500000 -449.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LJYK"/>
</svg>