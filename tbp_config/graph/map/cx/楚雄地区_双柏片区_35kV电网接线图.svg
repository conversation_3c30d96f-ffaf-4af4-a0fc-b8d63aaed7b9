<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-27" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-246 -5241 2548 1904">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape95">
    <ellipse cx="20" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="34" x2="29" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="34" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="31" x2="29" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="21" x2="21" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="9" x2="0" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="6" x2="3" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="7" x2="2" y1="4" y2="4"/>
    <polyline points="21,19 5,19 5,6 " stroke-width="1"/>
    <ellipse cx="31" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="20" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="31" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline DF8003:Layer="0" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape1_0">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape1_1">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="0" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape38_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,57 6,57 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="0" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape38_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="0" points="30,87 26,78 36,78 30,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
   </symbol>
   <symbol id="transformer2:shape43_0">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape43_1">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape20">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="11" y2="5"/>
    <circle cx="19" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="11" y2="5"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3585e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3586890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3587480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3587ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3588dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_35899e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_358a120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_358aba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_358b470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_358bd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_358bd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_358db60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_358db60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_358e7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3590280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3590dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3591780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3592090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3593880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3594080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3594770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3595190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3596370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3596cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_35977e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_359caa0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_359d730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3599530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_359aa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_359b4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_35a9f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_359f340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1914" width="2558" x="-251" y="-5246"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1608" x2="1590" y1="-4553" y2="-4535"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1590" x2="1608" y1="-4553" y2="-4535"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1280" x2="1284" y1="-4533" y2="-4529"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1349" x2="1349" y1="-4088" y2="-4073"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1367" x2="1367" y1="-4087" y2="-4072"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1335" x2="1335" y1="-3697" y2="-3682"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1353" x2="1353" y1="-3696" y2="-3681"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1532" x2="638" y1="-4460" y2="-4460"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="637" x2="637" y1="-4459" y2="-4327"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="661" x2="661" y1="-4276" y2="-4127"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="661" x2="429" y1="-4128" y2="-4128"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="437" x2="419" y1="-4147" y2="-4129"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="419" x2="437" y1="-4147" y2="-4129"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="0" id="SW-163227">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1007.000000 -5134.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3108" ObjectName="SW-CX_BLXC.CX_BLXC_3672SW"/>
     <cge:Meas_Ref ObjectId="163227"/>
    <cge:TPSR_Ref TObjectID="3108"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-163228">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1007.000000 -5042.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3109" ObjectName="SW-CX_BLXC.CX_BLXC_3676SW"/>
     <cge:Meas_Ref ObjectId="163228"/>
    <cge:TPSR_Ref TObjectID="3109"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113956">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1333.000000 -4952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21624" ObjectName="SW-SB_TD.SB_TD_35367SW"/>
     <cge:Meas_Ref ObjectId="113956"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.111288 -4404.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14558" ObjectName="SW-CX_SBEQ.CX_SBEQ_3656SW"/>
     <cge:Meas_Ref ObjectId="68606"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68605">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.111288 -4312.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14557" ObjectName="SW-CX_SBEQ.CX_SBEQ_3651SW"/>
     <cge:Meas_Ref ObjectId="68605"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-107710">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.304000 -4858.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20971" ObjectName="SW-SB_DZ.SB_DZ_3621SW"/>
     <cge:Meas_Ref ObjectId="107710"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-107711">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.304000 -4747.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20972" ObjectName="SW-SB_DZ.SB_DZ_3626SW"/>
     <cge:Meas_Ref ObjectId="107711"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-107712">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.304000 -4847.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20973" ObjectName="SW-SB_DZ.SB_DZ_36217SW"/>
     <cge:Meas_Ref ObjectId="107712"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-107713">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.304000 -4797.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20974" ObjectName="SW-SB_DZ.SB_DZ_36260SW"/>
     <cge:Meas_Ref ObjectId="107713"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-107714">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 580.304000 -4720.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20975" ObjectName="SW-SB_DZ.SB_DZ_36267SW"/>
     <cge:Meas_Ref ObjectId="107714"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 502.304000 -4730.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 -4416.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 -4324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1748.000000 -4468.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-70724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 738.000000 -4317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15949" ObjectName="SW-CX_SBEQ.CX_SBEQ_3122SW"/>
     <cge:Meas_Ref ObjectId="70724"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-70761">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 848.000000 -4311.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15954" ObjectName="SW-CX_SBEQ.CX_SBEQ_3121SW"/>
     <cge:Meas_Ref ObjectId="70761"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68399">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.000000 -4159.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15966" ObjectName="SW-CX_SBEQ.CX_SBEQ_3616SW"/>
     <cge:Meas_Ref ObjectId="68399"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.000000 -4251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15965" ObjectName="SW-CX_SBEQ.CX_SBEQ_3611SW"/>
     <cge:Meas_Ref ObjectId="68398"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.000000 -3937.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21379" ObjectName="SW-SB_ANS.SB_ANS_3916SW"/>
     <cge:Meas_Ref ObjectId="110927"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-159530">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 -4045.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26495" ObjectName="SW-SB_YL.SB_YL_38167SW"/>
     <cge:Meas_Ref ObjectId="159530"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193388">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 -4018.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29405" ObjectName="SW-SB_ANS.SB_ANS_39167SW"/>
     <cge:Meas_Ref ObjectId="193388"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110928">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 929.000000 -4020.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21380" ObjectName="SW-SB_ANS.SB_ANS_3900SW"/>
     <cge:Meas_Ref ObjectId="110928"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 940.000000 -3671.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.000000 -3598.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-109435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 -3561.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21260" ObjectName="SW-SB_EJ.SB_EJ_33167SW"/>
     <cge:Meas_Ref ObjectId="109435"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-109434">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -3509.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21259" ObjectName="SW-SB_EJ.SB_EJ_3316SW"/>
     <cge:Meas_Ref ObjectId="109434"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-109433">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -3417.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21258" ObjectName="SW-SB_EJ.SB_EJ_3311SW"/>
     <cge:Meas_Ref ObjectId="109433"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-159574">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -3800.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26497" ObjectName="SW-SB_YL.SB_YL_3821SW"/>
     <cge:Meas_Ref ObjectId="159574"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-159581">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -3717.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26498" ObjectName="SW-SB_YL.SB_YL_3826SW"/>
     <cge:Meas_Ref ObjectId="159581"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-159589">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1465.000000 -3704.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26501" ObjectName="SW-SB_YL.SB_YL_38267SW"/>
     <cge:Meas_Ref ObjectId="159589"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113660">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -3952.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21578" ObjectName="SW-SB_ALB.SB_ALB_3711SW"/>
     <cge:Meas_Ref ObjectId="113660"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -3860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21579" ObjectName="SW-SB_ALB.SB_ALB_3716SW"/>
     <cge:Meas_Ref ObjectId="113661"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1801.000000 -3841.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21580" ObjectName="SW-SB_ALB.SB_ALB_37167SW"/>
     <cge:Meas_Ref ObjectId="113662"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1391.000000 -3493.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1938.000000 -3513.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110442">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1949.000000 -3588.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21333" ObjectName="SW-SB_PL.SB_PL_32167SW"/>
     <cge:Meas_Ref ObjectId="110442"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110440">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1969.000000 -3575.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21331" ObjectName="SW-SB_PL.SB_PL_3216SW"/>
     <cge:Meas_Ref ObjectId="110440"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110441">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2014.000000 -3587.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21332" ObjectName="SW-SB_PL.SB_PL_32160SW"/>
     <cge:Meas_Ref ObjectId="110441"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2101.000000 -3575.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21329" ObjectName="SW-SB_PL.SB_PL_3211SW"/>
     <cge:Meas_Ref ObjectId="110438"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110326">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2176.000000 -3575.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21324" ObjectName="SW-SB_PL.SB_PL_3011SW"/>
     <cge:Meas_Ref ObjectId="110326"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2079.000000 -3523.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21330" ObjectName="SW-SB_PL.SB_PL_32117SW"/>
     <cge:Meas_Ref ObjectId="110439"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110574">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2150.000000 -3523.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21348" ObjectName="SW-SB_PL.SB_PL_3121SW"/>
     <cge:Meas_Ref ObjectId="110574"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110575">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2174.000000 -3466.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21349" ObjectName="SW-SB_PL.SB_PL_31217SW"/>
     <cge:Meas_Ref ObjectId="110575"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68607">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 -4246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14559" ObjectName="SW-CX_SBEQ.CX_SBEQ_3622SW"/>
     <cge:Meas_Ref ObjectId="68607"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68608">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 -4154.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14560" ObjectName="SW-CX_SBEQ.CX_SBEQ_3626SW"/>
     <cge:Meas_Ref ObjectId="68608"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 -3903.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 -3995.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -4049.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -3836.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -3835.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -3743.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-109438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 -3507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21262" ObjectName="SW-SB_EJ.SB_EJ_3326SW"/>
     <cge:Meas_Ref ObjectId="109438"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-109437">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 -3415.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21261" ObjectName="SW-SB_EJ.SB_EJ_3321SW"/>
     <cge:Meas_Ref ObjectId="109437"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.000000 -3567.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-109439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 671.000000 -3571.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21263" ObjectName="SW-SB_EJ.SB_EJ_33267SW"/>
     <cge:Meas_Ref ObjectId="109439"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 442.000000 -3496.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-126211">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -3430.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23097" ObjectName="SW-CX_NNH.CX_NNH_30117SW"/>
     <cge:Meas_Ref ObjectId="126211"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 -3507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 -3567.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-126210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -3447.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23096" ObjectName="SW-CX_NNH.CX_NNH_3011SW"/>
     <cge:Meas_Ref ObjectId="126210"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1125.000000 -3607.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110018">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1179.000000 -3564.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21297" ObjectName="SW-SB_EJ.SB_EJ_33367SW"/>
     <cge:Meas_Ref ObjectId="110018"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110016">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 -3512.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21295" ObjectName="SW-SB_EJ.SB_EJ_3336SW"/>
     <cge:Meas_Ref ObjectId="110016"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110014">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 -3420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21293" ObjectName="SW-SB_EJ.SB_EJ_3331SW"/>
     <cge:Meas_Ref ObjectId="110014"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 -3693.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 -3678.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 -3444.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 963.000000 -4116.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113955">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 -4902.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21623" ObjectName="SW-SB_TD.SB_TD_3536SW"/>
     <cge:Meas_Ref ObjectId="113955"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1315.000000 -4903.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-108240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.385081 -4859.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21014" ObjectName="SW-SB_DZ.SB_DZ_3611SW"/>
     <cge:Meas_Ref ObjectId="108240"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-108241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.385081 -4761.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21015" ObjectName="SW-SB_DZ.SB_DZ_3616SW"/>
     <cge:Meas_Ref ObjectId="108241"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-108242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.385081 -4851.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21016" ObjectName="SW-SB_DZ.SB_DZ_36117SW"/>
     <cge:Meas_Ref ObjectId="108242"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-108243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.385081 -4805.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21017" ObjectName="SW-SB_DZ.SB_DZ_36160SW"/>
     <cge:Meas_Ref ObjectId="108243"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-108244">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.304000 -4751.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21018" ObjectName="SW-SB_DZ.SB_DZ_36167SW"/>
     <cge:Meas_Ref ObjectId="108244"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 257.304000 -4761.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-94138">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 266.385081 -4594.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19840" ObjectName="SW-CX_DZ.CX_DZ_37167SW"/>
     <cge:Meas_Ref ObjectId="94138"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-94137">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.739130 315.385081 -4548.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19839" ObjectName="SW-CX_DZ.CX_DZ_3716SW"/>
     <cge:Meas_Ref ObjectId="94137"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-94136">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.717391 315.081081 -4481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19838" ObjectName="SW-CX_DZ.CX_DZ_3711SW"/>
     <cge:Meas_Ref ObjectId="94136"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68609">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -4311.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14561" ObjectName="SW-CX_SB.CX_SB_3642SW"/>
     <cge:Meas_Ref ObjectId="68609"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -4411.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14562" ObjectName="SW-CX_SB.CX_SB_3646SW"/>
     <cge:Meas_Ref ObjectId="68610"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 -4510.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1385.000000 -4085.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1371.000000 -3694.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68603">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 -4252.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14555" ObjectName="SW-CX_SB.CX_SB_3631SW"/>
     <cge:Meas_Ref ObjectId="68603"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 -4162.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14556" ObjectName="SW-CX_SB.CX_SB_3636SW"/>
     <cge:Meas_Ref ObjectId="68604"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-159522">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 -3983.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26492" ObjectName="SW-SB_YL.SB_YL_3816SW"/>
     <cge:Meas_Ref ObjectId="159522"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-159515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 -3887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26491" ObjectName="SW-SB_YL.SB_YL_3811SW"/>
     <cge:Meas_Ref ObjectId="159515"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 493.000000 -4039.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-134601">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 -4847.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28750" ObjectName="SW-SB_TD.SB_TD_3533SW"/>
     <cge:Meas_Ref ObjectId="134601"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-134600">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 -4747.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28749" ObjectName="SW-SB_TD.SB_TD_3531SW"/>
     <cge:Meas_Ref ObjectId="134600"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1325.000000 -4793.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28751" ObjectName="SW-SB_TD.SB_TD_35317SW"/>
     <cge:Meas_Ref ObjectId="188665"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-134578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.000000 -4685.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28746" ObjectName="SW-SB_TD.SB_TD_3521SW"/>
     <cge:Meas_Ref ObjectId="134578"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113979">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.000000 -4591.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28747" ObjectName="SW-SB_TD.SB_TD_3526SW"/>
     <cge:Meas_Ref ObjectId="113979"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1630.000000 -4678.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28748" ObjectName="SW-SB_TD.SB_TD_35217SW"/>
     <cge:Meas_Ref ObjectId="188662"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-134557">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1207.000000 -4681.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28743" ObjectName="SW-SB_TD.SB_TD_3511SW"/>
     <cge:Meas_Ref ObjectId="134557"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-134558">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1207.000000 -4587.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28744" ObjectName="SW-SB_TD.SB_TD_3516SW"/>
     <cge:Meas_Ref ObjectId="134558"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1298.000000 -4677.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28745" ObjectName="SW-SB_TD.SB_TD_35117SW"/>
     <cge:Meas_Ref ObjectId="188659"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110926">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.000000 -3838.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21378" ObjectName="SW-SB_ANS.SB_ANS_3911SW"/>
     <cge:Meas_Ref ObjectId="110926"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 972.000000 -3706.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29406" ObjectName="SW-SB_ANS.SB_ANS_39267SW"/>
     <cge:Meas_Ref ObjectId="193386"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110328">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2242.000000 -3531.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21325" ObjectName="SW-SB_PL.SB_PL_30117SW"/>
     <cge:Meas_Ref ObjectId="110328"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1598.111288 -4553.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1266.000000 -5077.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 935.304000 -5043.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 502.304000 -4471.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1719.304000 -3841.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 657.359375 -5122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.359375 -5122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 -4974.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 -5065.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 810.000000 -4961.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -3768.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29403" ObjectName="SW-SB_ANS.SB_ANS_3921SW"/>
     <cge:Meas_Ref ObjectId="193384"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -3685.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29404" ObjectName="SW-SB_ANS.SB_ANS_3926SW"/>
     <cge:Meas_Ref ObjectId="193385"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="0" id="BS-CX_BLXC.CX_BLX_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="952,-5191 1070,-5191 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3464" ObjectName="BS-CX_BLXC.CX_BLX_3IIM"/>
    <cge:TPSR_Ref TObjectID="3464"/></metadata>
   <polyline fill="none" opacity="0" points="952,-5191 1070,-5191 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-SB_DZ.SB_DZ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="261,-4910 621,-4910 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20966" ObjectName="BS-SB_DZ.SB_DZ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="261,-4910 621,-4910 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-SB_TD.SB_TD_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1181,-4737 1674,-4737 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21619" ObjectName="BS-SB_TD.SB_TD_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1181,-4737 1674,-4737 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_SBEQ.CX_SBEQ_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="378,-4305 778,-4305 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12193" ObjectName="BS-CX_SBEQ.CX_SBEQ_3IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="378,-4305 778,-4305 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_SBEQ.CX_SBEQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="825,-4305 1568,-4305 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12192" ObjectName="BS-CX_SBEQ.CX_SBEQ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="825,-4305 1568,-4305 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="1681,-4317 1798,-4317 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1681,-4317 1798,-4317 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-SB_ANS.SB_ANS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="831,-3821 1001,-3821 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21375" ObjectName="BS-SB_ANS.SB_ANS_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="831,-3821 1001,-3821 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-SB_YL.SB_YL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1384,-3854 1496,-3854 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26475" ObjectName="BS-SB_YL.SB_YL_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1384,-3854 1496,-3854 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-SB_ALB.SB_ALB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1745,-4005 1836,-4005 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21556" ObjectName="BS-SB_ALB.SB_ALB_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1745,-4005 1836,-4005 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-SB_EJ.SB_EJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="604,-3405 1212,-3405 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21254" ObjectName="BS-SB_EJ.SB_EJ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="604,-3405 1212,-3405 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="368,-3371 480,-3371 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="368,-3371 480,-3371 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="245,-3894 520,-3894 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="245,-3894 520,-3894 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_DZ.CX_DZ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="278,-4469 375,-4469 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19835" ObjectName="BS-CX_DZ.CX_DZ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="278,-4469 375,-4469 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="601,-5118 686,-5118 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="601,-5118 686,-5118 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="721,-5118 821,-5118 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="721,-5118 821,-5118 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="0" id="g_2fb51b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 631.304000 -4843.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_10dc640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 630.304000 -4793.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2607fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 634.304000 -4716.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_26dc1e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1518.000000 -4041.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2bee080" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.000000 -3980.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_11325d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 992.000000 -3557.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2f72080" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1513.000000 -3700.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2f1f870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1850.000000 -3837.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_112c440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.454545 1949.000000 -3635.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30aee70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.454545 2014.000000 -3634.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_356ed00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2078.000000 -3489.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3575360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2223.000000 -3462.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_5d7c140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 343.000000 -4045.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30bd4f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.454545 671.000000 -3615.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30c0210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 491.000000 -3492.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30c5ae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 336.000000 -3426.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30cbe70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.454545 320.000000 -3611.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30df210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1228.000000 -3560.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_310f4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1338.000000 -4951.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_304edb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 384.385081 -4847.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3051f90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 383.385081 -4801.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3052ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 386.000000 -4747.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_305db20" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.476190 -0.000000 0.000000 -1.500000 264.777081 -4590.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35bd9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1330.000000 -4792.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35c80f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1635.000000 -4677.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35d2a80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 -4676.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35dc930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1021.000000 -3702.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35e7360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2241.000000 -3497.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_334e2e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 -4960.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2eeb330">
     <polyline DF8003:Layer="0" fill="none" points="1016,-5191 1016,-5175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3464@0" ObjectIDZND0="3108@1" Pin0InfoVect0LinkObjId="SW-163227_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1016,-5191 1016,-5175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1077580">
     <polyline DF8003:Layer="0" fill="none" points="1016,-5139 1016,-5123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3108@0" ObjectIDZND0="3107@1" Pin0InfoVect0LinkObjId="SW-19666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-163227_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1016,-5139 1016,-5123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_282e590">
     <polyline DF8003:Layer="0" fill="none" points="1016,-5096 1016,-5083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3107@0" ObjectIDZND0="3109@1" Pin0InfoVect0LinkObjId="SW-163228_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1016,-5096 1016,-5083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f2ea50">
     <polyline DF8003:Layer="0" fill="none" points="1328,-4957 1342,-4957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21624@0" ObjectIDZND0="g_310f4e0@0" Pin0InfoVect0LinkObjId="g_310f4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113956_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-4957 1342,-4957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f68160">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4366 1534,-4353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14529@0" ObjectIDZND0="14557@1" Pin0InfoVect0LinkObjId="SW-68605_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68445_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4366 1534,-4353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ecfaf0">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4317 1534,-4305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="14557@0" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4317 1534,-4305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f5fc20">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4409 1534,-4393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14558@0" ObjectIDZND0="14529@1" Pin0InfoVect0LinkObjId="SW-68445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4409 1534,-4393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e893c0">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4737 1534,-4726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21619@0" ObjectIDZND0="28746@1" Pin0InfoVect0LinkObjId="SW-134578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4737 1534,-4726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3594cd0">
     <polyline DF8003:Layer="0" fill="none" points="622,-4802 637,-4802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20974@1" ObjectIDZND0="g_10dc640@0" Pin0InfoVect0LinkObjId="g_10dc640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107713_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="622,-4802 637,-4802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f75200">
     <polyline DF8003:Layer="0" fill="none" points="571,-4802 586,-4802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20972@x" ObjectIDND1="21007@x" ObjectIDZND0="20974@0" Pin0InfoVect0LinkObjId="SW-107713_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107711_0" Pin1InfoVect1LinkObjId="SW-107989_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4802 586,-4802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1168b70">
     <polyline DF8003:Layer="0" fill="none" points="621,-4725 641,-4725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20975@1" ObjectIDZND0="g_2607fb0@0" Pin0InfoVect0LinkObjId="g_2607fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107714_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="621,-4725 641,-4725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f326a0">
     <polyline DF8003:Layer="0" fill="none" points="571,-4725 585,-4725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="20972@x" ObjectIDND2="g_235cfb0@0" ObjectIDZND0="20975@0" Pin0InfoVect0LinkObjId="SW-107714_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_235cfb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4725 585,-4725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f737d0">
     <polyline DF8003:Layer="0" fill="none" points="571,-4725 552,-4725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDND2="g_235cfb0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_235cfb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4725 552,-4725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2edeb20">
     <polyline DF8003:Layer="0" fill="none" points="507,-4724 493,-4724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2f22250@0" Pin0InfoVect0LinkObjId="g_2f22250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-4724 493,-4724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8cb50">
     <polyline DF8003:Layer="0" fill="none" points="571,-4899 571,-4910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20971@1" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_3059610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4899 571,-4910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ec2320">
     <polyline DF8003:Layer="0" fill="none" points="571,-4838 571,-4852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21007@1" ObjectIDZND0="20971@x" ObjectIDZND1="20973@x" Pin0InfoVect0LinkObjId="SW-107710_0" Pin0InfoVect1LinkObjId="SW-107712_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4838 571,-4852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1116790">
     <polyline DF8003:Layer="0" fill="none" points="571,-4852 571,-4863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21007@x" ObjectIDND1="20973@x" ObjectIDZND0="20971@0" Pin0InfoVect0LinkObjId="SW-107710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107989_0" Pin1InfoVect1LinkObjId="SW-107712_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4852 571,-4863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0e570">
     <polyline DF8003:Layer="0" fill="none" points="571,-4852 587,-4852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21007@x" ObjectIDND1="20971@x" ObjectIDZND0="20973@0" Pin0InfoVect0LinkObjId="SW-107712_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107989_0" Pin1InfoVect1LinkObjId="SW-107710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4852 587,-4852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fb4f70">
     <polyline DF8003:Layer="0" fill="none" points="623,-4852 638,-4852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20973@1" ObjectIDZND0="g_2fb51b0@0" Pin0InfoVect0LinkObjId="g_2fb51b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107712_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="623,-4852 638,-4852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fa130">
     <polyline DF8003:Layer="0" fill="none" points="571,-4725 571,-4752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="20975@x" ObjectIDND2="g_235cfb0@0" ObjectIDZND0="20972@0" Pin0InfoVect0LinkObjId="SW-107711_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-107714_0" Pin1InfoVect2LinkObjId="g_235cfb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4725 571,-4752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1076620">
     <polyline DF8003:Layer="0" fill="none" points="571,-4788 571,-4802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="20972@1" ObjectIDZND0="20974@x" ObjectIDZND1="21007@x" Pin0InfoVect0LinkObjId="SW-107713_0" Pin0InfoVect1LinkObjId="SW-107989_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107711_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4788 571,-4802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eea330">
     <polyline DF8003:Layer="0" fill="none" points="571,-4802 571,-4811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20972@x" ObjectIDND1="20974@x" ObjectIDZND0="21007@0" Pin0InfoVect0LinkObjId="SW-107989_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107711_0" Pin1InfoVect1LinkObjId="SW-107713_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4802 571,-4811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ee0760">
     <polyline DF8003:Layer="0" fill="none" points="1739,-4378 1739,-4365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-4378 1739,-4365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_10db770">
     <polyline DF8003:Layer="0" fill="none" points="1739,-4421 1739,-4405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-4421 1739,-4405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_282e810">
     <polyline DF8003:Layer="0" fill="none" points="1785,-4473 1802,-4473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_30cfd20@0" Pin0InfoVect0LinkObjId="g_30cfd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-4473 1802,-4473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_114fd80">
     <polyline DF8003:Layer="0" fill="none" points="1739,-4473 1753,-4473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-4473 1753,-4473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b7d770">
     <polyline DF8003:Layer="0" fill="none" points="1739,-4473 1739,-4457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-4473 1739,-4457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d3d00">
     <polyline DF8003:Layer="0" fill="none" points="747,-4305 747,-4322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12193@0" ObjectIDZND0="15949@0" Pin0InfoVect0LinkObjId="SW-70724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3077c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="747,-4305 747,-4322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef5cb0">
     <polyline DF8003:Layer="0" fill="none" points="791,-4369 747,-4369 747,-4358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15948@1" ObjectIDZND0="15949@1" Pin0InfoVect0LinkObjId="SW-70724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70722_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-4369 747,-4369 747,-4358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26abd00">
     <polyline DF8003:Layer="0" fill="none" points="857,-4352 857,-4369 818,-4369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15954@1" ObjectIDZND0="15948@0" Pin0InfoVect0LinkObjId="SW-70722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="857,-4352 857,-4369 818,-4369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eee320">
     <polyline DF8003:Layer="0" fill="none" points="857,-4305 857,-4316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12192@0" ObjectIDZND0="15954@0" Pin0InfoVect0LinkObjId="SW-70761_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ecfaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="857,-4305 857,-4316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1131a70">
     <polyline DF8003:Layer="0" fill="none" points="893,-4305 893,-4292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12192@0" ObjectIDZND0="15965@1" Pin0InfoVect0LinkObjId="SW-68398_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ecfaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="893,-4305 893,-4292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a7f5d0">
     <polyline DF8003:Layer="0" fill="none" points="893,-4256 893,-4240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15965@0" ObjectIDZND0="14533@1" Pin0InfoVect0LinkObjId="SW-68397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="893,-4256 893,-4240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bee7e0">
     <polyline DF8003:Layer="0" fill="none" points="893,-4213 893,-4200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14533@0" ObjectIDZND0="15966@1" Pin0InfoVect0LinkObjId="SW-68399_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="893,-4213 893,-4200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c37f90">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4050 1474,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26492@x" ObjectIDND1="0@x" ObjectIDND2="g_2719e70@0" ObjectIDZND0="26495@0" Pin0InfoVect0LinkObjId="SW-159530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159522_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2719e70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4050 1474,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1109d10">
     <polyline DF8003:Layer="0" fill="none" points="1510,-4050 1525,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26495@1" ObjectIDZND0="g_26dc1e0@0" Pin0InfoVect0LinkObjId="g_26dc1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1510,-4050 1525,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1134e30">
     <polyline DF8003:Layer="0" fill="none" points="869,-3944 893,-3944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1135a30@0" ObjectIDZND0="21379@0" Pin0InfoVect0LinkObjId="SW-110927_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1135a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="869,-3944 893,-3944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_258d340">
     <polyline DF8003:Layer="0" fill="none" points="855,-4023 855,-4006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29405@0" ObjectIDZND0="g_2bee080@0" Pin0InfoVect0LinkObjId="g_2bee080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="855,-4023 855,-4006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f92240">
     <polyline DF8003:Layer="0" fill="none" points="938,-4025 938,-4013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="21380@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110928_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="938,-4025 938,-4013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c3540">
     <polyline DF8003:Layer="0" fill="none" points="893,-4164 893,-4136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="15966@0" ObjectIDZND0="g_2f24e10@0" ObjectIDZND1="0@x" ObjectIDZND2="21379@x" Pin0InfoVect0LinkObjId="g_2f24e10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-110927_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68399_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="893,-4164 893,-4136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2563eb0">
     <polyline DF8003:Layer="0" fill="none" points="893,-4136 876,-4136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="15966@x" ObjectIDND1="0@x" ObjectIDND2="21379@x" ObjectIDZND0="g_2f24e10@0" Pin0InfoVect0LinkObjId="g_2f24e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-68399_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-110927_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="893,-4136 876,-4136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1073df0">
     <polyline DF8003:Layer="0" fill="none" points="845,-4136 831,-4136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2f24e10@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f24e10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="845,-4136 831,-4136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_11156e0">
     <polyline DF8003:Layer="0" fill="none" points="928,-3683 928,-3666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3683 928,-3666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_258d020">
     <polyline DF8003:Layer="0" fill="none" points="928,-3666 945,-3666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3666 945,-3666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24f2460">
     <polyline DF8003:Layer="0" fill="none" points="990,-3666 1001,-3666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_26ac310@0" Pin0InfoVect0LinkObjId="g_26ac310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="990,-3666 1001,-3666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c0d2b0">
     <polyline DF8003:Layer="0" fill="none" points="928,-3612 944,-3612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21260@x" ObjectIDND2="21259@x" ObjectIDZND0="g_1084860@0" Pin0InfoVect0LinkObjId="g_1084860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-109435_0" Pin1InfoVect2LinkObjId="SW-109434_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3612 944,-3612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f91580">
     <polyline DF8003:Layer="0" fill="none" points="928,-3612 928,-3598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1084860@0" ObjectIDND1="29406@x" ObjectIDND2="29404@x" ObjectIDZND0="0@x" ObjectIDZND1="21260@x" ObjectIDZND2="21259@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-109435_0" Pin0InfoVect2LinkObjId="SW-109434_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1084860_0" Pin1InfoVect1LinkObjId="SW-193386_0" Pin1InfoVect2LinkObjId="SW-193385_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3612 928,-3598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ef440">
     <polyline DF8003:Layer="0" fill="none" points="928,-3598 894,-3598 894,-3603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1084860@0" ObjectIDND1="29406@x" ObjectIDND2="29404@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1084860_0" Pin1InfoVect1LinkObjId="SW-193386_0" Pin1InfoVect2LinkObjId="SW-193385_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3598 894,-3598 894,-3603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1105200">
     <polyline DF8003:Layer="0" fill="none" points="894,-3648 894,-3658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2a7fd50@0" Pin0InfoVect0LinkObjId="g_2a7fd50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="894,-3648 894,-3658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272bfe0">
     <polyline DF8003:Layer="0" fill="none" points="929,-3566 948,-3566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1084860@0" ObjectIDND2="29406@x" ObjectIDZND0="21260@0" Pin0InfoVect0LinkObjId="SW-109435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1084860_0" Pin1InfoVect2LinkObjId="SW-193386_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="929,-3566 948,-3566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_107eb00">
     <polyline DF8003:Layer="0" fill="none" points="984,-3566 999,-3566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21260@1" ObjectIDZND0="g_11325d0@0" Pin0InfoVect0LinkObjId="g_11325d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,-3566 999,-3566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eed1f0">
     <polyline DF8003:Layer="0" fill="none" points="928,-3598 928,-3566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_1084860@0" ObjectIDND2="29406@x" ObjectIDZND0="21260@x" ObjectIDZND1="21259@x" Pin0InfoVect0LinkObjId="SW-109435_0" Pin0InfoVect1LinkObjId="SW-109434_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1084860_0" Pin1InfoVect2LinkObjId="SW-193386_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3598 928,-3566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29afc50">
     <polyline DF8003:Layer="0" fill="none" points="928,-3471 928,-3458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21288@0" ObjectIDZND0="21258@1" Pin0InfoVect0LinkObjId="SW-109433_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3471 928,-3458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ee6da0">
     <polyline DF8003:Layer="0" fill="none" points="928,-3422 928,-3405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21258@0" ObjectIDZND0="21254@0" Pin0InfoVect0LinkObjId="g_5d8f120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109433_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3422 928,-3405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1165d60">
     <polyline DF8003:Layer="0" fill="none" points="928,-3566 928,-3550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1084860@0" ObjectIDND2="29406@x" ObjectIDZND0="21259@1" Pin0InfoVect0LinkObjId="SW-109434_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1084860_0" Pin1InfoVect2LinkObjId="SW-193386_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3566 928,-3550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f798c0">
     <polyline DF8003:Layer="0" fill="none" points="928,-3514 928,-3498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21259@0" ObjectIDZND0="21288@1" Pin0InfoVect0LinkObjId="SW-109927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3514 928,-3498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88220">
     <polyline DF8003:Layer="0" fill="none" points="1788,-3914 1788,-3901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21577@0" ObjectIDZND0="21579@1" Pin0InfoVect0LinkObjId="SW-113661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-3914 1788,-3901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88480">
     <polyline DF8003:Layer="0" fill="none" points="1788,-4005 1788,-3993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21556@0" ObjectIDZND0="21578@1" Pin0InfoVect0LinkObjId="SW-113660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-4005 1788,-3993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f813b0">
     <polyline DF8003:Layer="0" fill="none" points="1788,-3957 1788,-3941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21578@0" ObjectIDZND0="21577@1" Pin0InfoVect0LinkObjId="SW-113659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-3957 1788,-3941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c440d0">
     <polyline DF8003:Layer="0" fill="none" points="1788,-3846 1806,-3846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21579@x" ObjectIDND1="0@x" ObjectIDND2="g_33298b0@0" ObjectIDZND0="21580@0" Pin0InfoVect0LinkObjId="SW-113662_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-113661_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_33298b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-3846 1806,-3846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f1f610">
     <polyline DF8003:Layer="0" fill="none" points="1842,-3846 1857,-3846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21580@1" ObjectIDZND0="g_2f1f870@0" Pin0InfoVect0LinkObjId="g_2f1f870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1842,-3846 1857,-3846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f67220">
     <polyline DF8003:Layer="0" fill="none" points="1788,-3865 1788,-3846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="21579@0" ObjectIDZND0="21580@x" ObjectIDZND1="0@x" ObjectIDZND2="g_33298b0@0" Pin0InfoVect0LinkObjId="SW-113662_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_33298b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-3865 1788,-3846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0e200">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3559 1397,-3559 1397,-3543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3592810@0" ObjectIDND2="g_2f91da0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3592810_0" Pin1InfoVect2LinkObjId="g_2f91da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3559 1397,-3559 1397,-3543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f227e0">
     <polyline DF8003:Layer="0" fill="none" points="1396,-3498 1396,-3485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1396,-3498 1396,-3485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f7c990">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3559 1452,-3537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3592810@0" ObjectIDND2="g_2f91da0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3592810_0" Pin1InfoVect2LinkObjId="g_2f91da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3559 1452,-3537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f91b40">
     <polyline DF8003:Layer="0" fill="none" points="1897,-3580 1897,-3536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21331@x" ObjectIDND2="21333@x" ObjectIDZND0="g_2f91da0@0" Pin0InfoVect0LinkObjId="g_2f91da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-110440_0" Pin1InfoVect2LinkObjId="SW-110442_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1897,-3580 1897,-3536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f5c760">
     <polyline DF8003:Layer="0" fill="none" points="1944,-3579 1944,-3563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21331@x" ObjectIDND1="21333@x" ObjectIDND2="g_2f91da0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110440_0" Pin1InfoVect1LinkObjId="SW-110442_0" Pin1InfoVect2LinkObjId="g_2f91da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1944,-3579 1944,-3563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f5c9c0">
     <polyline DF8003:Layer="0" fill="none" points="1943,-3518 1943,-3474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1943,-3518 1943,-3474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_112bf80">
     <polyline DF8003:Layer="0" fill="none" points="1958,-3580 1958,-3593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21331@x" ObjectIDND1="0@x" ObjectIDND2="g_2f91da0@0" ObjectIDZND0="21333@0" Pin0InfoVect0LinkObjId="SW-110442_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110440_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2f91da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1958,-3580 1958,-3593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_112c1e0">
     <polyline DF8003:Layer="0" fill="none" points="1958,-3629 1958,-3642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21333@1" ObjectIDZND0="g_112c440@0" Pin0InfoVect0LinkObjId="g_112c440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110442_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1958,-3629 1958,-3642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_112f400">
     <polyline DF8003:Layer="0" fill="none" points="1974,-3580 1958,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="21331@0" ObjectIDZND0="21333@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2f91da0@0" Pin0InfoVect0LinkObjId="SW-110442_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2f91da0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1974,-3580 1958,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ae9b0">
     <polyline DF8003:Layer="0" fill="none" points="2023,-3580 2023,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21328@x" ObjectIDND1="21331@x" ObjectIDZND0="21332@0" Pin0InfoVect0LinkObjId="SW-110441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110430_0" Pin1InfoVect1LinkObjId="SW-110440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-3580 2023,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30aec10">
     <polyline DF8003:Layer="0" fill="none" points="2023,-3628 2023,-3641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21332@1" ObjectIDZND0="g_30aee70@0" Pin0InfoVect0LinkObjId="g_30aee70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-3628 2023,-3641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30b6a00">
     <polyline DF8003:Layer="0" fill="none" points="2073,-3580 2088,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21328@0" ObjectIDZND0="21329@x" ObjectIDZND1="21330@x" Pin0InfoVect0LinkObjId="SW-110438_0" Pin0InfoVect1LinkObjId="SW-110439_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2073,-3580 2088,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30b6c60">
     <polyline DF8003:Layer="0" fill="none" points="2088,-3580 2106,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21328@x" ObjectIDND1="21330@x" ObjectIDZND0="21329@0" Pin0InfoVect0LinkObjId="SW-110438_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110430_0" Pin1InfoVect1LinkObjId="SW-110439_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2088,-3580 2106,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_356e840">
     <polyline DF8003:Layer="0" fill="none" points="2088,-3580 2088,-3564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21328@x" ObjectIDND1="21329@x" ObjectIDZND0="21330@1" Pin0InfoVect0LinkObjId="SW-110439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110430_0" Pin1InfoVect1LinkObjId="SW-110438_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2088,-3580 2088,-3564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_356eaa0">
     <polyline DF8003:Layer="0" fill="none" points="2088,-3528 2088,-3515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21330@0" ObjectIDZND0="g_356ed00@0" Pin0InfoVect0LinkObjId="g_356ed00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2088,-3528 2088,-3515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3571c00">
     <polyline DF8003:Layer="0" fill="none" points="2159,-3580 2159,-3564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21329@x" ObjectIDND1="21324@x" ObjectIDZND0="21348@1" Pin0InfoVect0LinkObjId="SW-110574_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110438_0" Pin1InfoVect1LinkObjId="SW-110326_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2159,-3580 2159,-3564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35726d0">
     <polyline DF8003:Layer="0" fill="none" points="2142,-3580 2159,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21329@1" ObjectIDZND0="21324@x" ObjectIDZND1="21348@x" Pin0InfoVect0LinkObjId="SW-110326_0" Pin0InfoVect1LinkObjId="SW-110574_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110438_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-3580 2159,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3572930">
     <polyline DF8003:Layer="0" fill="none" points="2159,-3580 2181,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21329@x" ObjectIDND1="21348@x" ObjectIDZND0="21324@0" Pin0InfoVect0LinkObjId="SW-110326_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110438_0" Pin1InfoVect1LinkObjId="SW-110574_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2159,-3580 2181,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3574ea0">
     <polyline DF8003:Layer="0" fill="none" points="2159,-3471 2179,-3471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="21348@x" ObjectIDZND0="21349@0" Pin0InfoVect0LinkObjId="SW-110575_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2159,-3471 2179,-3471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3575100">
     <polyline DF8003:Layer="0" fill="none" points="2215,-3471 2230,-3471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21349@1" ObjectIDZND0="g_3575360@0" Pin0InfoVect0LinkObjId="g_3575360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110575_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2215,-3471 2230,-3471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3576600">
     <polyline DF8003:Layer="0" fill="none" points="2159,-3528 2159,-3470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="21348@0" ObjectIDZND0="21349@x" Pin0InfoVect0LinkObjId="SW-110575_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2159,-3528 2159,-3470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3576860">
     <polyline DF8003:Layer="0" fill="none" points="2159,-3471 2159,-3452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="21349@x" ObjectIDND1="21348@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110575_0" Pin1InfoVect1LinkObjId="SW-110574_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2159,-3471 2159,-3452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357d3d0">
     <polyline DF8003:Layer="0" fill="none" points="429,-4208 429,-4195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14530@0" ObjectIDZND0="14560@1" Pin0InfoVect0LinkObjId="SW-68608_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-4208 429,-4195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357d630">
     <polyline DF8003:Layer="0" fill="none" points="429,-4305 429,-4287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12193@0" ObjectIDZND0="14559@1" Pin0InfoVect0LinkObjId="SW-68607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3077c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-4305 429,-4287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357d890">
     <polyline DF8003:Layer="0" fill="none" points="429,-4251 429,-4235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14559@0" ObjectIDZND0="14530@1" Pin0InfoVect0LinkObjId="SW-68468_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-4251 429,-4235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d78ca0">
     <polyline DF8003:Layer="0" fill="none" points="429,-3908 429,-3894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-3908 429,-3894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d78f00">
     <polyline DF8003:Layer="0" fill="none" points="429,-4000 429,-3984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-4000 429,-3984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d79160">
     <polyline DF8003:Layer="0" fill="none" points="429,-3957 429,-3944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-3957 429,-3944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5d79c30">
     <polyline DF8003:Layer="0" fill="none" points="429,-4054 429,-4036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="14560@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-68608_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-4054 429,-4036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5d7cb70">
     <polyline DF8003:Layer="0" fill="none" points="429,-4054 414,-4054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="14560@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-68608_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-4054 414,-4054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d7cdd0">
     <polyline DF8003:Layer="0" fill="none" points="378,-4054 366,-4054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_5d7c140@0" Pin0InfoVect0LinkObjId="g_5d7c140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-4054 366,-4054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d81100">
     <polyline DF8003:Layer="0" fill="none" points="308,-3798 308,-3779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="308,-3798 308,-3779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d81360">
     <polyline DF8003:Layer="0" fill="none" points="308,-3894 308,-3877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-3894 308,-3877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d815c0">
     <polyline DF8003:Layer="0" fill="none" points="308,-3841 308,-3825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-3841 308,-3825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d87eb0">
     <polyline DF8003:Layer="0" fill="none" points="428,-3797 428,-3784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3797 428,-3784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d88110">
     <polyline DF8003:Layer="0" fill="none" points="428,-3894 428,-3876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3894 428,-3876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_5d88370">
     <polyline DF8003:Layer="0" fill="none" points="428,-3840 428,-3824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3840 428,-3824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5d885d0">
     <polyline DF8003:Layer="0" fill="none" points="428,-3748 428,-3677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="21262@x" ObjectIDZND2="21263@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-109438_0" Pin0InfoVect2LinkObjId="SW-109439_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3748 428,-3677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5d8eec0">
     <polyline DF8003:Layer="0" fill="none" points="655,-3469 655,-3456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21281@0" ObjectIDZND0="21261@1" Pin0InfoVect0LinkObjId="SW-109437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-3469 655,-3456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5d8f120">
     <polyline DF8003:Layer="0" fill="none" points="655,-3420 655,-3405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21261@0" ObjectIDZND0="21254@0" Pin0InfoVect0LinkObjId="g_2ee6da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109437_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-3420 655,-3405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5d8f380">
     <polyline DF8003:Layer="0" fill="none" points="655,-3512 655,-3496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21262@0" ObjectIDZND0="21281@1" Pin0InfoVect0LinkObjId="SW-109655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-3512 655,-3496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30b9af0">
     <polyline DF8003:Layer="0" fill="none" points="621,-3617 621,-3630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_30b9d50@0" Pin0InfoVect0LinkObjId="g_30b9d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="621,-3617 621,-3630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ba370">
     <polyline DF8003:Layer="0" fill="none" points="655,-3561 621,-3561 621,-3572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="21262@x" ObjectIDND1="21263@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109438_0" Pin1InfoVect1LinkObjId="SW-109439_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-3561 621,-3561 621,-3572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ba5d0">
     <polyline DF8003:Layer="0" fill="none" points="428,-3677 655,-3677 655,-3561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="23096@x" ObjectIDZND0="0@x" ObjectIDZND1="21262@x" ObjectIDZND2="21263@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-109438_0" Pin0InfoVect2LinkObjId="SW-109439_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-126210_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3677 655,-3677 655,-3561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ba830">
     <polyline DF8003:Layer="0" fill="none" points="655,-3561 655,-3548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="21263@x" ObjectIDND2="0@x" ObjectIDZND0="21262@1" Pin0InfoVect0LinkObjId="SW-109438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-109439_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-3561 655,-3548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30bd030">
     <polyline DF8003:Layer="0" fill="none" points="655,-3561 680,-3561 680,-3576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="21262@x" ObjectIDND2="0@x" ObjectIDZND0="21263@0" Pin0InfoVect0LinkObjId="SW-109439_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-109438_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-3561 680,-3561 680,-3576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30bd290">
     <polyline DF8003:Layer="0" fill="none" points="680,-3612 680,-3622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21263@1" ObjectIDZND0="g_30bd4f0@0" Pin0InfoVect0LinkObjId="g_30bd4f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109439_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="680,-3612 680,-3622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30c0c40">
     <polyline DF8003:Layer="0" fill="none" points="428,-3501 447,-3501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23096@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-126210_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3501 447,-3501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30c0ea0">
     <polyline DF8003:Layer="0" fill="none" points="483,-3501 498,-3501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_30c0210@0" Pin0InfoVect0LinkObjId="g_30c0210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,-3501 498,-3501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30c1100">
     <polyline DF8003:Layer="0" fill="none" points="428,-3677 428,-3501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="21262@x" ObjectIDZND0="0@x" ObjectIDZND1="23096@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-126210_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-109438_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3677 428,-3501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30c2e30">
     <polyline DF8003:Layer="0" fill="none" points="428,-3391 428,-3371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23095@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126209_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3391 428,-3371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30c53c0">
     <polyline DF8003:Layer="0" fill="none" points="359,-3435 378,-3435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_30c5ae0@0" ObjectIDZND0="23097@0" Pin0InfoVect0LinkObjId="SW-126211_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30c5ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-3435 378,-3435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30c5620">
     <polyline DF8003:Layer="0" fill="none" points="414,-3435 429,-3435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23097@1" ObjectIDZND0="23095@x" ObjectIDZND1="23096@x" Pin0InfoVect0LinkObjId="SW-126209_0" Pin0InfoVect1LinkObjId="SW-126210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126211_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="414,-3435 429,-3435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30c5880">
     <polyline DF8003:Layer="0" fill="none" points="428,-3435 428,-3418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23097@x" ObjectIDND1="23096@x" ObjectIDZND0="23095@1" Pin0InfoVect0LinkObjId="SW-126209_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-126211_0" Pin1InfoVect1LinkObjId="SW-126210_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3435 428,-3418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30c9270">
     <polyline DF8003:Layer="0" fill="none" points="361,-3600 361,-3615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_30c8a90@1" ObjectIDZND0="g_30cd020@0" Pin0InfoVect0LinkObjId="g_30cd020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30c8a90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,-3600 361,-3615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30cbc10">
     <polyline DF8003:Layer="0" fill="none" points="329,-3608 329,-3618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_30cbe70@0" Pin0InfoVect0LinkObjId="g_30cbe70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-3608 329,-3618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30cc900">
     <polyline DF8003:Layer="0" fill="none" points="361,-3557 329,-3557 329,-3572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_30c8a90@0" ObjectIDND1="0@x" ObjectIDND2="g_35e1bf0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30c8a90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_35e1bf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,-3557 329,-3557 329,-3572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30ccb60">
     <polyline DF8003:Layer="0" fill="none" points="361,-3548 361,-3557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_30c8a90@0" ObjectIDZND1="0@x" ObjectIDZND2="g_35e1bf0@0" Pin0InfoVect0LinkObjId="g_30c8a90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_35e1bf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="361,-3548 361,-3557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30ccdc0">
     <polyline DF8003:Layer="0" fill="none" points="361,-3557 361,-3569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_35e1bf0@0" ObjectIDZND0="g_30c8a90@0" Pin0InfoVect0LinkObjId="g_30c8a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_35e1bf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,-3557 361,-3569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30d2870">
     <polyline DF8003:Layer="0" fill="none" points="428,-3501 428,-3488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="23096@1" Pin0InfoVect0LinkObjId="SW-126210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3501 428,-3488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30d2ad0">
     <polyline DF8003:Layer="0" fill="none" points="428,-3452 428,-3435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23096@0" ObjectIDZND0="23095@x" ObjectIDZND1="23097@x" Pin0InfoVect0LinkObjId="SW-126209_0" Pin0InfoVect1LinkObjId="SW-126211_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-126210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3452 428,-3435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30d2d30">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4095 1479,-4095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2719e70@0" ObjectIDND1="0@x" ObjectIDND2="26495@x" ObjectIDZND0="g_2719e70@0" Pin0InfoVect0LinkObjId="g_2719e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2719e70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-159530_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4095 1479,-4095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30d2f90">
     <polyline DF8003:Layer="0" fill="none" points="938,-4072 954,-4072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21380@x" ObjectIDND1="29405@x" ObjectIDZND0="g_1191fb0@0" Pin0InfoVect0LinkObjId="g_1191fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110928_0" Pin1InfoVect1LinkObjId="SW-193388_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="938,-4072 954,-4072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30d3a80">
     <polyline DF8003:Layer="0" fill="none" points="938,-4072 938,-4061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1191fb0@0" ObjectIDND1="29405@x" ObjectIDZND0="21380@1" Pin0InfoVect0LinkObjId="SW-110928_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1191fb0_0" Pin1InfoVect1LinkObjId="SW-193388_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="938,-4072 938,-4061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30dc160">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3601 1130,-3601 1130,-3612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="21297@x" ObjectIDND1="21295@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110018_0" Pin1InfoVect1LinkObjId="SW-110016_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3601 1130,-3601 1130,-3612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30dc3c0">
     <polyline DF8003:Layer="0" fill="none" points="1130,-3657 1130,-3670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_30dc620@0" Pin0InfoVect0LinkObjId="g_30dc620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1130,-3657 1130,-3670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30dfca0">
     <polyline DF8003:Layer="0" fill="none" points="1165,-3569 1184,-3569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="21297@0" Pin0InfoVect0LinkObjId="SW-110018_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-3569 1184,-3569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30dff00">
     <polyline DF8003:Layer="0" fill="none" points="1220,-3569 1235,-3569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21297@1" ObjectIDZND0="g_30df210@0" Pin0InfoVect0LinkObjId="g_30df210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110018_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1220,-3569 1235,-3569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30e0160">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3601 1164,-3569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="21297@x" ObjectIDZND1="21295@x" Pin0InfoVect0LinkObjId="SW-110018_0" Pin0InfoVect1LinkObjId="SW-110016_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3601 1164,-3569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ebf00">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3474 1164,-3461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21292@0" ObjectIDZND0="21293@1" Pin0InfoVect0LinkObjId="SW-110014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109976_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3474 1164,-3461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ec160">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3425 1164,-3405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21293@0" ObjectIDZND0="21254@0" Pin0InfoVect0LinkObjId="g_2ee6da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3425 1164,-3405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ec3c0">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3569 1164,-3553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="21295@1" Pin0InfoVect0LinkObjId="SW-110016_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3569 1164,-3553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ec620">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3517 1164,-3501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21295@0" ObjectIDZND0="21292@1" Pin0InfoVect0LinkObjId="SW-109976_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110016_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3517 1164,-3501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30f25d0">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3747 1164,-3734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3747 1164,-3734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30f3ad0">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3774 1164,-3805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3774 1164,-3805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30f66c0">
     <polyline DF8003:Layer="0" fill="none" points="1224,-3728 1224,-3741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_30f6920@0" Pin0InfoVect0LinkObjId="g_30f6920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-3728 1224,-3741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30f7020">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3683 1225,-3683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="21297@x" ObjectIDND2="21295@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-110018_0" Pin1InfoVect2LinkObjId="SW-110016_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3683 1225,-3683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30f7b10">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3698 1164,-3683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="21297@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-110018_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3698 1164,-3683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30f7d70">
     <polyline DF8003:Layer="0" fill="none" points="1164,-3683 1164,-3601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="21297@x" ObjectIDZND2="21295@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-110018_0" Pin0InfoVect2LinkObjId="SW-110016_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-3683 1164,-3601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30fb9c0">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3510 1452,-3494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3510 1452,-3494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30fbc20">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3449 1452,-3430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3449 1452,-3430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30fe760">
     <polyline DF8003:Layer="0" fill="none" points="855,-4059 855,-4072 938,-4072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="29405@1" ObjectIDZND0="g_1191fb0@0" ObjectIDZND1="21380@x" Pin0InfoVect0LinkObjId="g_1191fb0_0" Pin0InfoVect1LinkObjId="SW-110928_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="855,-4059 855,-4072 938,-4072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3100f80">
     <polyline DF8003:Layer="0" fill="none" points="958,-4110 972,-4110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_31011e0@0" Pin0InfoVect0LinkObjId="g_31011e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="958,-4110 972,-4110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3101f10">
     <polyline DF8003:Layer="0" fill="none" points="913,-4111 893,-4111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2f24e10@0" ObjectIDZND1="15966@x" ObjectIDZND2="21379@x" Pin0InfoVect0LinkObjId="g_2f24e10_0" Pin0InfoVect1LinkObjId="SW-68399_0" Pin0InfoVect2LinkObjId="SW-110927_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="913,-4111 893,-4111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3102100">
     <polyline DF8003:Layer="0" fill="none" points="944,-4130 935,-4121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="944,-4130 935,-4121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31022f0">
     <polyline DF8003:Layer="0" fill="none" points="944,-4123 944,-4130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="944,-4123 944,-4130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31024e0">
     <polyline DF8003:Layer="0" fill="none" points="944,-4130 937,-4130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="944,-4130 937,-4130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310c6a0">
     <polyline DF8003:Layer="0" fill="none" points="1390,-4898 1390,-4887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_310b8f0@0" Pin0InfoVect0LinkObjId="g_310b8f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1390,-4898 1390,-4887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310c900">
     <polyline DF8003:Layer="0" fill="none" points="1365,-4898 1390,-4898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_310b8f0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_310b8f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1365,-4898 1390,-4898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_310e820">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3559 1452,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_3592810@0" ObjectIDZND1="g_2f91da0@0" ObjectIDZND2="26501@x" Pin0InfoVect0LinkObjId="g_3592810_0" Pin0InfoVect1LinkObjId="g_2f91da0_0" Pin0InfoVect2LinkObjId="SW-159589_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3559 1452,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_310ea10">
     <polyline DF8003:Layer="0" fill="none" points="1854,-3564 1854,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_3592810@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="26501@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-159589_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3592810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1854,-3564 1854,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310ec00">
     <polyline DF8003:Layer="0" fill="none" points="1739,-4317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-4317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310ee10">
     <polyline DF8003:Layer="0" fill="none" points="1739,-4317 1739,-4328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-4317 1739,-4328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3051d30">
     <polyline DF8003:Layer="0" fill="none" points="375,-4810 390,-4810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21017@1" ObjectIDZND0="g_3051f90@0" Pin0InfoVect0LinkObjId="g_3051f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,-4810 390,-4810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3052a20">
     <polyline DF8003:Layer="0" fill="none" points="324,-4810 339,-4810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21008@x" ObjectIDND1="21015@x" ObjectIDZND0="21017@0" Pin0InfoVect0LinkObjId="SW-108243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108239_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4810 339,-4810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3052c80">
     <polyline DF8003:Layer="0" fill="none" points="372,-4756 393,-4756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21018@1" ObjectIDZND0="g_3052ee0@0" Pin0InfoVect0LinkObjId="g_3052ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108244_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="372,-4756 393,-4756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30587f0">
     <polyline DF8003:Layer="0" fill="none" points="262,-4756 248,-4756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3058a50@0" Pin0InfoVect0LinkObjId="g_3058a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="262,-4756 248,-4756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3059150">
     <polyline DF8003:Layer="0" fill="none" points="324,-4856 340,-4856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21014@x" ObjectIDND1="21008@x" ObjectIDZND0="21016@0" Pin0InfoVect0LinkObjId="SW-108242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108240_0" Pin1InfoVect1LinkObjId="SW-108239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4856 340,-4856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30593b0">
     <polyline DF8003:Layer="0" fill="none" points="376,-4856 391,-4856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21016@1" ObjectIDZND0="g_304edb0@0" Pin0InfoVect0LinkObjId="g_304edb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-4856 391,-4856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3059610">
     <polyline DF8003:Layer="0" fill="none" points="324,-4900 324,-4910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21014@1" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_2f8cb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4900 324,-4910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_305d8c0">
     <polyline DF8003:Layer="0" fill="none" points="324,-4599 305,-4599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_305a620@0" ObjectIDND1="19839@x" ObjectIDND2="g_306dfe0@0" ObjectIDZND0="19840@1" Pin0InfoVect0LinkObjId="SW-94138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_305a620_0" Pin1InfoVect1LinkObjId="SW-94137_0" Pin1InfoVect2LinkObjId="g_306dfe0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4599 305,-4599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_305e5b0">
     <polyline DF8003:Layer="0" fill="none" points="271,-4599 258,-4599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19840@0" ObjectIDZND0="g_305db20@0" Pin0InfoVect0LinkObjId="g_305db20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="271,-4599 258,-4599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3062fe0">
     <polyline DF8003:Layer="0" fill="none" points="348,-4592 324,-4592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_305a620@0" ObjectIDZND0="19840@x" ObjectIDZND1="g_306dfe0@0" ObjectIDZND2="21015@x" Pin0InfoVect0LinkObjId="SW-94138_0" Pin0InfoVect1LinkObjId="g_306dfe0_0" Pin0InfoVect2LinkObjId="SW-108241_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_305a620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="348,-4592 324,-4592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3063240">
     <polyline DF8003:Layer="0" fill="none" points="324,-4590 324,-4598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_305a620@0" ObjectIDND1="19839@x" ObjectIDZND0="19840@x" ObjectIDZND1="g_306dfe0@0" ObjectIDZND2="21015@x" Pin0InfoVect0LinkObjId="SW-94138_0" Pin0InfoVect1LinkObjId="g_306dfe0_0" Pin0InfoVect2LinkObjId="SW-108241_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_305a620_0" Pin1InfoVect1LinkObjId="SW-94137_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4590 324,-4598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3065c60">
     <polyline DF8003:Layer="0" fill="none" points="324,-4578 324,-4590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="19839@1" ObjectIDZND0="g_305a620@0" ObjectIDZND1="19840@x" ObjectIDZND2="g_306dfe0@0" Pin0InfoVect0LinkObjId="g_305a620_0" Pin0InfoVect1LinkObjId="SW-94138_0" Pin0InfoVect2LinkObjId="g_306dfe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94137_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4578 324,-4590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3065ec0">
     <polyline DF8003:Layer="0" fill="none" points="324,-4512 324,-4521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19838@1" ObjectIDZND0="19837@0" Pin0InfoVect0LinkObjId="SW-94135_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94136_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4512 324,-4521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3066120">
     <polyline DF8003:Layer="0" fill="none" points="324,-4541 324,-4552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19837@1" ObjectIDZND0="19839@0" Pin0InfoVect0LinkObjId="SW-94137_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94135_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4541 324,-4552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3066380">
     <polyline DF8003:Layer="0" fill="none" points="324,-4469 324,-4484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19835@0" ObjectIDZND0="19838@0" Pin0InfoVect0LinkObjId="SW-94136_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4469 324,-4484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_306b610">
     <polyline DF8003:Layer="0" fill="none" points="346,-4737 324,-4737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_306dfe0@0" ObjectIDZND0="21015@x" ObjectIDZND1="g_3059870@0" ObjectIDZND2="19840@x" Pin0InfoVect0LinkObjId="SW-108241_0" Pin0InfoVect1LinkObjId="g_3059870_0" Pin0InfoVect2LinkObjId="SW-94138_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_306dfe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="346,-4737 324,-4737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_306bf80">
     <polyline DF8003:Layer="0" fill="none" points="324,-4856 324,-4864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21016@x" ObjectIDND1="21008@x" ObjectIDZND0="21014@0" Pin0InfoVect0LinkObjId="SW-108240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108242_0" Pin1InfoVect1LinkObjId="SW-108239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4856 324,-4864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_306c170">
     <polyline DF8003:Layer="0" fill="none" points="324,-4847 324,-4856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21008@1" ObjectIDZND0="21016@x" ObjectIDZND1="21014@x" Pin0InfoVect0LinkObjId="SW-108242_0" Pin0InfoVect1LinkObjId="SW-108240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4847 324,-4856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_306cb90">
     <polyline DF8003:Layer="0" fill="none" points="324,-4810 324,-4820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21017@x" ObjectIDND1="21015@x" ObjectIDZND0="21008@0" Pin0InfoVect0LinkObjId="SW-108239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108243_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4810 324,-4820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_306cdf0">
     <polyline DF8003:Layer="0" fill="none" points="324,-4802 324,-4810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21015@1" ObjectIDZND0="21017@x" ObjectIDZND1="21008@x" Pin0InfoVect0LinkObjId="SW-108243_0" Pin0InfoVect1LinkObjId="SW-108239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4802 324,-4810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_306d050">
     <polyline DF8003:Layer="0" fill="none" points="340,-4756 307,-4756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="21018@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="340,-4756 307,-4756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_306db20">
     <polyline DF8003:Layer="0" fill="none" points="324,-4737 324,-4766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_306dfe0@0" ObjectIDND1="g_3059870@0" ObjectIDND2="19840@x" ObjectIDZND0="21015@0" Pin0InfoVect0LinkObjId="SW-108241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_306dfe0_0" Pin1InfoVect1LinkObjId="g_3059870_0" Pin1InfoVect2LinkObjId="SW-94138_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4737 324,-4766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_306dd80">
     <polyline DF8003:Layer="0" fill="none" points="324,-4619 324,-4737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3059870@0" ObjectIDND1="19840@x" ObjectIDND2="g_305a620@0" ObjectIDZND0="g_306dfe0@0" ObjectIDZND1="21015@x" Pin0InfoVect0LinkObjId="g_306dfe0_0" Pin0InfoVect1LinkObjId="SW-108241_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3059870_0" Pin1InfoVect1LinkObjId="SW-94138_0" Pin1InfoVect2LinkObjId="g_305a620_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4619 324,-4737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30709c0">
     <polyline DF8003:Layer="0" fill="none" points="1320,-4897 1270,-4897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="28750@x" ObjectIDZND1="21623@x" Pin0InfoVect0LinkObjId="SW-134601_0" Pin0InfoVect1LinkObjId="SW-113955_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1320,-4897 1270,-4897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3077c70">
     <polyline DF8003:Layer="0" fill="none" points="571,-4316 571,-4305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="14561@0" ObjectIDZND0="12193@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4316 571,-4305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3077ed0">
     <polyline DF8003:Layer="0" fill="none" points="571,-4365 571,-4352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14531@0" ObjectIDZND0="14561@1" Pin0InfoVect0LinkObjId="SW-68609_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4365 571,-4352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3078ee0">
     <polyline DF8003:Layer="0" fill="none" points="547,-4400 571,-4400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_3078130@0" ObjectIDZND0="14562@x" ObjectIDZND1="14531@x" Pin0InfoVect0LinkObjId="SW-68610_0" Pin0InfoVect1LinkObjId="SW-68491_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3078130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="547,-4400 571,-4400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3079140">
     <polyline DF8003:Layer="0" fill="none" points="1216,-4582 1242,-4582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_30793a0@0" Pin0InfoVect0LinkObjId="g_30793a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-4582 1242,-4582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307b3e0">
     <polyline DF8003:Layer="0" fill="none" points="571,-4416 571,-4400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="14562@0" ObjectIDZND0="14531@x" ObjectIDZND1="g_3078130@0" Pin0InfoVect0LinkObjId="SW-68491_0" Pin0InfoVect1LinkObjId="g_3078130_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4416 571,-4400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307b5d0">
     <polyline DF8003:Layer="0" fill="none" points="571,-4400 571,-4392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="14562@x" ObjectIDND1="g_3078130@0" ObjectIDZND0="14531@1" Pin0InfoVect0LinkObjId="SW-68491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68610_0" Pin1InfoVect1LinkObjId="g_3078130_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4400 571,-4392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307c050">
     <polyline DF8003:Layer="0" fill="none" points="1270,-4737 1270,-4753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="21619@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-4737 1270,-4753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307c240">
     <polyline DF8003:Layer="0" fill="none" points="1216,-4737 1216,-4724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="21619@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-4737 1216,-4724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307dc00">
     <polyline DF8003:Layer="0" fill="none" points="1236,-4556 1236,-4568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_307d030@0" ObjectIDZND0="28744@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-134558_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_307d030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1236,-4556 1236,-4568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307de60">
     <polyline DF8003:Layer="0" fill="none" points="1216,-4568 1236,-4568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="28744@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_307d030@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_307d030_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-134558_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-4568 1236,-4568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307e0c0">
     <polyline DF8003:Layer="0" fill="none" points="1236,-4568 1265,-4568 1265,-4560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_307d030@0" ObjectIDND1="28744@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_307d030_0" Pin1InfoVect1LinkObjId="SW-134558_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1236,-4568 1265,-4568 1265,-4560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3080c10">
     <polyline DF8003:Layer="0" fill="none" points="1264,-4515 1264,-4502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3080e70@0" Pin0InfoVect0LinkObjId="g_3080e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1264,-4515 1264,-4502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3083f50">
     <polyline DF8003:Layer="0" fill="none" points="1401,-4008 1401,-4021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-4008 1401,-4021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30841b0">
     <polyline DF8003:Layer="0" fill="none" points="1402,-4048 1402,-4061 1455,-4061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="26495@x" ObjectIDZND1="26492@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-159530_0" Pin0InfoVect1LinkObjId="SW-159522_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1402,-4048 1402,-4061 1455,-4061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3086da0">
     <polyline DF8003:Layer="0" fill="none" points="1390,-4079 1376,-4079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3087000@0" Pin0InfoVect0LinkObjId="g_3087000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1390,-4079 1376,-4079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3087700">
     <polyline DF8003:Layer="0" fill="none" points="1435,-4080 1455,-4080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2719e70@0" ObjectIDZND1="g_2719e70@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2719e70_0" Pin0InfoVect1LinkObjId="g_2719e70_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1435,-4080 1455,-4080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3087960">
     <polyline DF8003:Layer="0" fill="none" points="1404,-4099 1413,-4090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1404,-4099 1413,-4090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3087bc0">
     <polyline DF8003:Layer="0" fill="none" points="1404,-4092 1404,-4099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1404,-4092 1404,-4099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3087e20">
     <polyline DF8003:Layer="0" fill="none" points="1404,-4099 1411,-4099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1404,-4099 1411,-4099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_308c460">
     <polyline DF8003:Layer="0" fill="none" points="1376,-3688 1362,-3688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_308d080@0" Pin0InfoVect0LinkObjId="g_308d080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1376,-3688 1362,-3688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_308c6c0">
     <polyline DF8003:Layer="0" fill="none" points="1390,-3708 1399,-3699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1390,-3708 1399,-3699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_308c920">
     <polyline DF8003:Layer="0" fill="none" points="1390,-3701 1390,-3708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1390,-3701 1390,-3708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_308cb80">
     <polyline DF8003:Layer="0" fill="none" points="1390,-3708 1397,-3708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1390,-3708 1397,-3708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3096190">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4305 1455,-4293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12192@0" ObjectIDZND0="14555@1" Pin0InfoVect0LinkObjId="SW-68603_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ecfaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4305 1455,-4293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30963f0">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4257 1455,-4240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14555@0" ObjectIDZND0="14528@1" Pin0InfoVect0LinkObjId="SW-68422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4257 1455,-4240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3096650">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4213 1455,-4203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14528@0" ObjectIDZND0="14556@1" Pin0InfoVect0LinkObjId="SW-68604_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4213 1455,-4203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30968b0">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2719e70@0" ObjectIDND1="0@x" ObjectIDND2="26495@x" ObjectIDZND0="g_2719e70@0" ObjectIDZND1="0@x" ObjectIDZND2="26495@x" Pin0InfoVect0LinkObjId="g_2719e70_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-159530_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2719e70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-159530_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309db20">
     <polyline DF8003:Layer="0" fill="none" points="1455,-3890 1455,-3854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26491@0" ObjectIDZND0="26475@0" Pin0InfoVect0LinkObjId="g_309e240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159515_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-3890 1455,-3854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309dd80">
     <polyline DF8003:Layer="0" fill="none" points="1455,-3988 1455,-3969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26492@0" ObjectIDZND0="26490@1" Pin0InfoVect0LinkObjId="SW-159509_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159522_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-3988 1455,-3969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309dfe0">
     <polyline DF8003:Layer="0" fill="none" points="1455,-3942 1455,-3928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26490@0" ObjectIDZND0="26491@1" Pin0InfoVect0LinkObjId="SW-159515_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-3942 1455,-3928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309e240">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3842 1452,-3854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26497@1" ObjectIDZND0="26475@0" Pin0InfoVect0LinkObjId="g_309db20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159574_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3842 1452,-3854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309e4a0">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3805 1452,-3794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26497@0" ObjectIDZND0="26496@1" Pin0InfoVect0LinkObjId="SW-159568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3805 1452,-3794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309e700">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3767 1452,-3758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26496@0" ObjectIDZND0="26498@1" Pin0InfoVect0LinkObjId="SW-159581_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3767 1452,-3758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309e960">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3709 1470,-3709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26498@x" ObjectIDND1="0@x" ObjectIDND2="g_2f7f240@0" ObjectIDZND0="26501@0" Pin0InfoVect0LinkObjId="SW-159589_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159581_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2f7f240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3709 1470,-3709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309ebc0">
     <polyline DF8003:Layer="0" fill="none" points="1506,-3709 1520,-3709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26501@1" ObjectIDZND0="g_2f72080@0" Pin0InfoVect0LinkObjId="g_2f72080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159589_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1506,-3709 1520,-3709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309fbd0">
     <polyline DF8003:Layer="0" fill="none" points="429,-4159 429,-4098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="14560@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68608_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="429,-4159 429,-4098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309fdc0">
     <polyline DF8003:Layer="0" fill="none" points="429,-4098 429,-4054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="14560@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68608_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="429,-4098 429,-4054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30a2880">
     <polyline DF8003:Layer="0" fill="none" points="427,-4096 499,-4096 499,-4089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="14560@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-68608_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="427,-4096 499,-4096 499,-4089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30a2ae0">
     <polyline DF8003:Layer="0" fill="none" points="498,-4044 498,-4034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="498,-4044 498,-4034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30a5a80">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4050 1455,-4039 1455,-4025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26495@x" ObjectIDND1="0@x" ObjectIDND2="g_2719e70@0" ObjectIDZND0="26492@1" Pin0InfoVect0LinkObjId="SW-159522_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159530_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2719e70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4050 1455,-4039 1455,-4025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b2040">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4061 1455,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_2719e70@0" ObjectIDND2="g_2719e70@0" ObjectIDZND0="26495@x" ObjectIDZND1="26492@x" Pin0InfoVect0LinkObjId="SW-159530_0" Pin0InfoVect1LinkObjId="SW-159522_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2719e70_0" Pin1InfoVect2LinkObjId="g_2719e70_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4061 1455,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b2d30">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4166 1455,-4095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="14556@0" ObjectIDZND0="g_2719e70@0" ObjectIDZND1="g_2719e70@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2719e70_0" Pin0InfoVect1LinkObjId="g_2719e70_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4166 1455,-4095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b37b0">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4095 1455,-4080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2719e70@0" ObjectIDND1="g_2719e70@0" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="26495@x" ObjectIDZND2="26492@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-159530_0" Pin0InfoVect2LinkObjId="SW-159522_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2719e70_0" Pin1InfoVect1LinkObjId="g_2719e70_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4095 1455,-4080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b3a10">
     <polyline DF8003:Layer="0" fill="none" points="1455,-4080 1455,-4061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_2719e70@0" ObjectIDND2="g_2719e70@0" ObjectIDZND0="26495@x" ObjectIDZND1="26492@x" Pin0InfoVect0LinkObjId="SW-159530_0" Pin0InfoVect1LinkObjId="SW-159522_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2719e70_0" Pin1InfoVect2LinkObjId="g_2719e70_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-4080 1455,-4061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bab10">
     <polyline DF8003:Layer="0" fill="none" points="1270,-4941 1270,-4957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="21624@x" ObjectIDZND1="g_35ec130@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-113956_0" Pin0InfoVect1LinkObjId="g_35ec130_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-4941 1270,-4957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35bad70">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4898 1411,-4898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="g_310b8f0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_310b8f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4898 1411,-4898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bafd0">
     <polyline DF8003:Layer="0" fill="none" points="1270,-4838 1270,-4851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21622@1" ObjectIDZND0="28750@0" Pin0InfoVect0LinkObjId="SW-134601_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113954_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-4838 1270,-4851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bd760">
     <polyline DF8003:Layer="0" fill="none" points="1320,-4798 1334,-4798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28751@0" ObjectIDZND0="g_35bd9c0@0" Pin0InfoVect0LinkObjId="g_35bd9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1320,-4798 1334,-4798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35be450">
     <polyline DF8003:Layer="0" fill="none" points="1270,-4798 1284,-4798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28749@x" ObjectIDND1="21622@x" ObjectIDZND0="28751@1" Pin0InfoVect0LinkObjId="SW-188665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134600_0" Pin1InfoVect1LinkObjId="SW-113954_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-4798 1284,-4798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c5700">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4632 1534,-4646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28747@1" ObjectIDZND0="21625@0" Pin0InfoVect0LinkObjId="SW-113969_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113979_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4632 1534,-4646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c7e90">
     <polyline DF8003:Layer="0" fill="none" points="1625,-4683 1639,-4683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28748@0" ObjectIDZND0="g_35c80f0@0" Pin0InfoVect0LinkObjId="g_35c80f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-4683 1639,-4683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c8b80">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4683 1589,-4683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21625@x" ObjectIDND1="28746@x" ObjectIDZND0="28748@1" Pin0InfoVect0LinkObjId="SW-188662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-113969_0" Pin1InfoVect1LinkObjId="SW-134578_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4683 1589,-4683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35cb5e0">
     <polyline DF8003:Layer="0" fill="none" points="1216,-4686 1216,-4673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28743@0" ObjectIDZND0="21626@1" Pin0InfoVect0LinkObjId="SW-113984_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-4686 1216,-4673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d0090">
     <polyline DF8003:Layer="0" fill="none" points="1216,-4646 1216,-4628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21626@0" ObjectIDZND0="28744@1" Pin0InfoVect0LinkObjId="SW-134558_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113984_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-4646 1216,-4628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d2820">
     <polyline DF8003:Layer="0" fill="none" points="1293,-4682 1307,-4682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28745@0" ObjectIDZND0="g_35d2a80@0" Pin0InfoVect0LinkObjId="g_35d2a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1293,-4682 1307,-4682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d3510">
     <polyline DF8003:Layer="0" fill="none" points="1216,-4682 1257,-4682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="28745@1" Pin0InfoVect0LinkObjId="SW-188659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-4682 1257,-4682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d7e20">
     <polyline DF8003:Layer="0" fill="none" points="928,-3773 928,-3761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29403@0" ObjectIDZND0="21382@1" Pin0InfoVect0LinkObjId="SW-110956_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3773 928,-3761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d8080">
     <polyline DF8003:Layer="0" fill="none" points="928,-3734 928,-3722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21382@0" ObjectIDZND0="29404@1" Pin0InfoVect0LinkObjId="SW-193385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110956_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3734 928,-3722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35db4c0">
     <polyline DF8003:Layer="0" fill="none" points="893,-3903 893,-3879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21381@0" ObjectIDZND0="21378@1" Pin0InfoVect0LinkObjId="SW-110926_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110929_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="893,-3903 893,-3879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35db720">
     <polyline DF8003:Layer="0" fill="none" points="893,-3843 893,-3821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21378@0" ObjectIDZND0="21375@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110926_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="893,-3843 893,-3821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dd190">
     <polyline DF8003:Layer="0" fill="none" points="1013,-3711 1028,-3711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29406@1" ObjectIDZND0="g_35dc930@0" Pin0InfoVect0LinkObjId="g_35dc930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-3711 1028,-3711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dffd0">
     <polyline DF8003:Layer="0" fill="none" points="928,-3690 928,-3683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29404@0" ObjectIDZND0="0@x" ObjectIDZND1="21260@x" ObjectIDZND2="21259@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-109435_0" Pin0InfoVect2LinkObjId="SW-109434_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3690 928,-3683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e0230">
     <polyline DF8003:Layer="0" fill="none" points="928,-3683 928,-3612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29406@x" ObjectIDND1="29404@x" ObjectIDZND0="0@x" ObjectIDZND1="21260@x" ObjectIDZND2="21259@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-109435_0" Pin0InfoVect2LinkObjId="SW-109434_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193386_0" Pin1InfoVect1LinkObjId="SW-193385_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3683 928,-3612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e1990">
     <polyline DF8003:Layer="0" fill="none" points="428,-3501 361,-3501 361,-3512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="23096@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-126210_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-3501 361,-3501 361,-3512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35e2910">
     <polyline DF8003:Layer="0" fill="none" points="361,-3557 400,-3557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_30c8a90@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_35e1bf0@0" Pin0InfoVect0LinkObjId="g_35e1bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30c8a90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,-3557 400,-3557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e46a0">
     <polyline DF8003:Layer="0" fill="none" points="2217,-3580 2251,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="21324@1" ObjectIDZND0="21325@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-110328_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110326_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2217,-3580 2251,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e7100">
     <polyline DF8003:Layer="0" fill="none" points="2251,-3536 2251,-3523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21325@0" ObjectIDZND0="g_35e7360@0" Pin0InfoVect0LinkObjId="g_35e7360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110328_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2251,-3536 2251,-3523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e7db0">
     <polyline DF8003:Layer="0" fill="none" points="2251,-3578 2251,-3572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="21324@x" ObjectIDND1="0@x" ObjectIDZND0="21325@1" Pin0InfoVect0LinkObjId="SW-110328_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110326_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2251,-3578 2251,-3572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ef8f0">
     <polyline DF8003:Layer="0" fill="none" points="1282,-5009 1261,-5009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_35ec130@0" ObjectIDZND0="0@x" ObjectIDZND1="21624@x" ObjectIDZND2="3109@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-113956_0" Pin0InfoVect2LinkObjId="SW-163228_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35ec130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-5009 1261,-5009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35efb50">
     <polyline DF8003:Layer="0" fill="none" points="1261,-5027 1261,-5009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_35ec130@0" ObjectIDZND1="21624@x" ObjectIDZND2="3109@x" Pin0InfoVect0LinkObjId="g_35ec130_0" Pin0InfoVect1LinkObjId="SW-113956_0" Pin0InfoVect2LinkObjId="SW-163228_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1261,-5027 1261,-5009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35efdb0">
     <polyline DF8003:Layer="0" fill="none" points="1261,-5009 1261,-4957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_35ec130@0" ObjectIDND1="0@x" ObjectIDZND0="21624@x" ObjectIDZND1="3109@x" ObjectIDZND2="g_3600990@0" Pin0InfoVect0LinkObjId="SW-113956_0" Pin0InfoVect1LinkObjId="SW-163228_0" Pin0InfoVect2LinkObjId="g_3600990_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35ec130_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1261,-5009 1261,-4957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35f0a40">
     <polyline DF8003:Layer="0" fill="none" points="1260,-5072 1260,-5087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_35f0010@0" Pin0InfoVect0LinkObjId="g_35f0010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-5072 1260,-5087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f1f70">
     <polyline DF8003:Layer="0" fill="none" points="893,-4111 893,-4136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="21379@x" ObjectIDZND0="g_2f24e10@0" ObjectIDZND1="15966@x" Pin0InfoVect0LinkObjId="g_2f24e10_0" Pin0InfoVect1LinkObjId="SW-68399_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-110927_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="893,-4111 893,-4136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f29a0">
     <polyline DF8003:Layer="0" fill="none" points="1216,-4592 1216,-4568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="28744@0" ObjectIDZND0="g_307d030@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_307d030_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134558_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-4592 1216,-4568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f3450">
     <polyline DF8003:Layer="0" fill="none" points="1292,-4957 1270,-4957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="21624@1" ObjectIDZND0="g_35ec130@0" ObjectIDZND1="0@x" ObjectIDZND2="3109@x" Pin0InfoVect0LinkObjId="g_35ec130_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-163228_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-4957 1270,-4957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f3f20">
     <polyline DF8003:Layer="0" fill="none" points="1270,-4788 1270,-4798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28749@1" ObjectIDZND0="21622@x" ObjectIDZND1="28751@x" Pin0InfoVect0LinkObjId="SW-113954_0" Pin0InfoVect1LinkObjId="SW-188665_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-4788 1270,-4798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f4160">
     <polyline DF8003:Layer="0" fill="none" points="1270,-4798 1270,-4811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28749@x" ObjectIDND1="28751@x" ObjectIDZND0="21622@0" Pin0InfoVect0LinkObjId="SW-113954_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134600_0" Pin1InfoVect1LinkObjId="SW-188665_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-4798 1270,-4811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f4c30">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4673 1534,-4683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21625@1" ObjectIDZND0="28746@x" ObjectIDZND1="28748@x" Pin0InfoVect0LinkObjId="SW-134578_0" Pin0InfoVect1LinkObjId="SW-188662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113969_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4673 1534,-4683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f4e90">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4683 1534,-4689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21625@x" ObjectIDND1="28748@x" ObjectIDZND0="28746@0" Pin0InfoVect0LinkObjId="SW-134578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-113969_0" Pin1InfoVect1LinkObjId="SW-188662_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4683 1534,-4689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f50f0">
     <polyline DF8003:Layer="0" fill="none" points="928,-3683 958,-3683 958,-3711 977,-3711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="21260@x" ObjectIDND2="21259@x" ObjectIDZND0="29406@0" Pin0InfoVect0LinkObjId="SW-193386_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-109435_0" Pin1InfoVect2LinkObjId="SW-109434_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3683 958,-3683 958,-3711 977,-3711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f5340">
     <polyline DF8003:Layer="0" fill="none" points="2277,-3627 2277,-3580 2251,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="21324@x" ObjectIDZND1="21325@x" Pin0InfoVect0LinkObjId="SW-110326_0" Pin0InfoVect1LinkObjId="SW-110328_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2277,-3627 2277,-3580 2251,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f5580">
     <polyline DF8003:Layer="0" fill="none" points="893,-3978 893,-4111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21379@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2f24e10@0" ObjectIDZND2="15966@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2f24e10_0" Pin0InfoVect2LinkObjId="SW-68399_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110927_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="893,-3978 893,-4111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35f57e0">
     <polyline DF8003:Layer="0" fill="none" points="1599,-4544 1739,-4544 1739,-4473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1599,-4544 1739,-4544 1739,-4473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35fc570">
     <polyline DF8003:Layer="0" fill="none" points="1270,-4888 1270,-4897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28750@1" ObjectIDZND0="0@x" ObjectIDZND1="21623@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-113955_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134601_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-4888 1270,-4897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35fc790">
     <polyline DF8003:Layer="0" fill="none" points="1270,-4897 1270,-4907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="28750@x" ObjectIDZND0="21623@0" Pin0InfoVect0LinkObjId="SW-113955_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-134601_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-4897 1270,-4907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35fc9b0">
     <polyline DF8003:Layer="0" fill="none" points="940,-5037 931,-5037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_35fd0c0@0" Pin0InfoVect0LinkObjId="g_35fd0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="940,-5037 931,-5037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3600730">
     <polyline DF8003:Layer="0" fill="none" points="1016,-5038 1016,-5047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3600990@0" ObjectIDND1="0@x" ObjectIDND2="g_35ec130@0" ObjectIDZND0="3109@0" Pin0InfoVect0LinkObjId="SW-163228_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3600990_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_35ec130_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1016,-5038 1016,-5047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3601740">
     <polyline DF8003:Layer="0" fill="none" points="996,-5026 996,-5038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3600990@0" ObjectIDZND0="3109@x" ObjectIDZND1="g_35ec130@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-163228_0" Pin0InfoVect1LinkObjId="g_35ec130_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3600990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="996,-5026 996,-5038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3602230">
     <polyline DF8003:Layer="0" fill="none" points="1016,-5038 996,-5038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3109@x" ObjectIDND1="g_35ec130@0" ObjectIDND2="0@x" ObjectIDZND0="g_3600990@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3600990_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-163228_0" Pin1InfoVect1LinkObjId="g_35ec130_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1016,-5038 996,-5038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3602490">
     <polyline DF8003:Layer="0" fill="none" points="996,-5038 985,-5038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3600990@0" ObjectIDND1="3109@x" ObjectIDND2="g_35ec130@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3600990_0" Pin1InfoVect1LinkObjId="SW-163228_0" Pin1InfoVect2LinkObjId="g_35ec130_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="996,-5038 985,-5038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3602f80">
     <polyline DF8003:Layer="0" fill="none" points="1270,-4957 1261,-4957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="21624@x" ObjectIDZND0="g_35ec130@0" ObjectIDZND1="0@x" ObjectIDZND2="3109@x" Pin0InfoVect0LinkObjId="g_35ec130_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-163228_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113956_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-4957 1261,-4957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36031e0">
     <polyline DF8003:Layer="0" fill="none" points="1261,-4957 1016,-4957 1016,-5039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_35ec130@0" ObjectIDND1="0@x" ObjectIDND2="21624@x" ObjectIDZND0="3109@x" ObjectIDZND1="g_3600990@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-163228_0" Pin0InfoVect1LinkObjId="g_3600990_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35ec130_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-113956_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1261,-4957 1016,-4957 1016,-5039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36064d0">
     <polyline DF8003:Layer="0" fill="none" points="507,-4465 493,-4465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3603440@0" Pin0InfoVect0LinkObjId="g_3603440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-4465 493,-4465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3607b50">
     <polyline DF8003:Layer="0" fill="none" points="893,-3951 893,-3930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="21381@1" Pin0InfoVect0LinkObjId="SW-110929_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="893,-3951 893,-3930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3608db0">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4595 1534,-4544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28747@0" ObjectIDZND0="0@x" ObjectIDZND1="g_35e8010@0" ObjectIDZND2="14558@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_35e8010_0" Pin0InfoVect2LinkObjId="SW-68606_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113979_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4595 1534,-4544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3609010">
     <polyline DF8003:Layer="0" fill="none" points="1534,-4544 1534,-4445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28747@x" ObjectIDND1="0@x" ObjectIDND2="g_35e8010@0" ObjectIDZND0="14558@1" Pin0InfoVect0LinkObjId="SW-68606_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-113979_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_35e8010_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1534,-4544 1534,-4445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3609270">
     <polyline DF8003:Layer="0" fill="none" points="1581,-4581 1567,-4581 1567,-4544 1534,-4544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="28747@x" ObjectIDZND1="g_35e8010@0" ObjectIDZND2="14558@x" Pin0InfoVect0LinkObjId="SW-113979_0" Pin0InfoVect1LinkObjId="g_35e8010_0" Pin0InfoVect2LinkObjId="SW-68606_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1581,-4581 1567,-4581 1567,-4544 1534,-4544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36094e0">
     <polyline DF8003:Layer="0" fill="none" points="1520,-4544 1534,-4544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_35e8010@0" ObjectIDZND0="28747@x" ObjectIDZND1="0@x" ObjectIDZND2="14558@x" Pin0InfoVect0LinkObjId="SW-113979_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-68606_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e8010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1520,-4544 1534,-4544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3609fd0">
     <polyline DF8003:Layer="0" fill="none" points="2023,-3580 2046,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21332@x" ObjectIDND1="21331@x" ObjectIDZND0="21328@1" Pin0InfoVect0LinkObjId="SW-110430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110441_0" Pin1InfoVect1LinkObjId="SW-110440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-3580 2046,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360a230">
     <polyline DF8003:Layer="0" fill="none" points="2010,-3580 2023,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21331@1" ObjectIDZND0="21332@x" ObjectIDZND1="21328@x" Pin0InfoVect0LinkObjId="SW-110441_0" Pin0InfoVect1LinkObjId="SW-110430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2010,-3580 2023,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33249e0">
     <polyline DF8003:Layer="0" fill="none" points="1959,-3580 1944,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21331@x" ObjectIDND1="21333@x" ObjectIDZND0="0@x" ObjectIDZND1="g_2f91da0@0" ObjectIDZND2="g_3592810@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2f91da0_0" Pin0InfoVect2LinkObjId="g_3592810_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110440_0" Pin1InfoVect1LinkObjId="SW-110442_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1959,-3580 1944,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33254b0">
     <polyline DF8003:Layer="0" fill="none" points="1897,-3580 1944,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f91da0@0" ObjectIDND1="g_3592810@0" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="21331@x" ObjectIDZND2="21333@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-110440_0" Pin0InfoVect2LinkObjId="SW-110442_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f91da0_0" Pin1InfoVect1LinkObjId="g_3592810_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1897,-3580 1944,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3325f30">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3580 1854,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="26501@x" ObjectIDZND0="g_3592810@0" ObjectIDZND1="g_2f91da0@0" Pin0InfoVect0LinkObjId="g_3592810_0" Pin0InfoVect1LinkObjId="g_2f91da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-159589_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3580 1854,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3326190">
     <polyline DF8003:Layer="0" fill="none" points="1854,-3580 1897,-3580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_3592810@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2f91da0@0" Pin0InfoVect0LinkObjId="g_2f91da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3592810_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1854,-3580 1897,-3580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3327790">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3722 1452,-3709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26498@0" ObjectIDZND0="26501@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2f7f240@0" Pin0InfoVect0LinkObjId="SW-159589_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2f7f240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159581_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3722 1452,-3709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33279d0">
     <polyline DF8003:Layer="0" fill="none" points="1421,-3689 1452,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="26501@x" ObjectIDZND1="26498@x" ObjectIDZND2="g_2f7f240@0" Pin0InfoVect0LinkObjId="SW-159589_0" Pin0InfoVect1LinkObjId="SW-159581_0" Pin0InfoVect2LinkObjId="g_2f7f240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1421,-3689 1452,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33286c0">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3709 1452,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26501@x" ObjectIDND1="26498@x" ObjectIDZND0="0@x" ObjectIDZND1="g_2f7f240@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2f7f240_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159589_0" Pin1InfoVect1LinkObjId="SW-159581_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3709 1452,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3328920">
     <polyline DF8003:Layer="0" fill="none" points="1474,-3689 1452,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f7f240@0" ObjectIDZND0="26501@x" ObjectIDZND1="26498@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-159589_0" Pin0InfoVect1LinkObjId="SW-159581_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f7f240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-3689 1452,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33293f0">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3689 1452,-3654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="26501@x" ObjectIDND1="26498@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3592810@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3592810_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159589_0" Pin1InfoVect1LinkObjId="SW-159581_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3689 1452,-3654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3329650">
     <polyline DF8003:Layer="0" fill="none" points="1452,-3580 1452,-3654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_3592810@0" ObjectIDZND0="26501@x" ObjectIDZND1="26498@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-159589_0" Pin0InfoVect1LinkObjId="SW-159581_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3592810_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-3580 1452,-3654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332a600">
     <polyline DF8003:Layer="0" fill="none" points="1764,-3811 1788,-3811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_33298b0@0" ObjectIDZND0="0@x" ObjectIDZND1="21580@x" ObjectIDZND2="21579@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-113662_0" Pin0InfoVect2LinkObjId="SW-113661_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33298b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1764,-3811 1788,-3811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_332d1f0">
     <polyline DF8003:Layer="0" fill="none" points="1724,-3835 1710,-3835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_332dc70@0" Pin0InfoVect0LinkObjId="g_332dc70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1724,-3835 1710,-3835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332da80">
     <polyline DF8003:Layer="0" fill="none" points="1788,-3836 1769,-3836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21580@x" ObjectIDND1="21579@x" ObjectIDND2="g_33298b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-113662_0" Pin1InfoVect1LinkObjId="SW-113661_0" Pin1InfoVect2LinkObjId="g_33298b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-3836 1769,-3836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332e880">
     <polyline DF8003:Layer="0" fill="none" points="1788,-3846 1788,-3836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21580@x" ObjectIDND1="21579@x" ObjectIDZND0="0@x" ObjectIDZND1="g_33298b0@0" ObjectIDZND2="26501@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_33298b0_0" Pin0InfoVect2LinkObjId="SW-159589_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-113662_0" Pin1InfoVect1LinkObjId="SW-113661_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-3846 1788,-3836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332f370">
     <polyline DF8003:Layer="0" fill="none" points="1788,-3836 1788,-3811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="21580@x" ObjectIDND2="21579@x" ObjectIDZND0="g_33298b0@0" ObjectIDZND1="26501@x" ObjectIDZND2="26498@x" Pin0InfoVect0LinkObjId="g_33298b0_0" Pin0InfoVect1LinkObjId="SW-159589_0" Pin0InfoVect2LinkObjId="SW-159581_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-113662_0" Pin1InfoVect2LinkObjId="SW-113661_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-3836 1788,-3811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332f5d0">
     <polyline DF8003:Layer="0" fill="none" points="1788,-3811 1788,-3654 1452,-3654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_33298b0@0" ObjectIDND1="0@x" ObjectIDND2="21580@x" ObjectIDZND0="26501@x" ObjectIDZND1="26498@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-159589_0" Pin0InfoVect1LinkObjId="SW-159581_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33298b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-113662_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1788,-3811 1788,-3654 1452,-3654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3333860">
     <polyline DF8003:Layer="0" fill="none" points="552,-4466 571,-4466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="14562@x" ObjectIDZND1="28744@x" ObjectIDZND2="g_307d030@0" Pin0InfoVect0LinkObjId="SW-68610_0" Pin0InfoVect1LinkObjId="SW-134558_0" Pin0InfoVect2LinkObjId="g_307d030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="552,-4466 571,-4466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3333ac0">
     <polyline DF8003:Layer="0" fill="none" points="571,-4466 571,-4452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="28744@x" ObjectIDND2="g_307d030@0" ObjectIDZND0="14562@1" Pin0InfoVect0LinkObjId="SW-68610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-134558_0" Pin1InfoVect2LinkObjId="g_307d030_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4466 571,-4452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33345f0">
     <polyline DF8003:Layer="0" fill="none" points="346,-4619 324,-4619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3059870@0" ObjectIDZND0="g_306dfe0@0" ObjectIDZND1="21015@x" ObjectIDZND2="19840@x" Pin0InfoVect0LinkObjId="g_306dfe0_0" Pin0InfoVect1LinkObjId="SW-108241_0" Pin0InfoVect2LinkObjId="SW-94138_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3059870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="346,-4619 324,-4619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3334850">
     <polyline DF8003:Layer="0" fill="none" points="324,-4619 324,-4598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_306dfe0@0" ObjectIDND1="21015@x" ObjectIDND2="g_3059870@0" ObjectIDZND0="19840@x" ObjectIDZND1="g_305a620@0" ObjectIDZND2="19839@x" Pin0InfoVect0LinkObjId="SW-94138_0" Pin0InfoVect1LinkObjId="g_305a620_0" Pin0InfoVect2LinkObjId="SW-94137_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_306dfe0_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="g_3059870_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="324,-4619 324,-4598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333a2c0">
     <polyline DF8003:Layer="0" fill="none" points="597,-4703 571,-4703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_235cfb0@0" ObjectIDZND0="0@x" ObjectIDZND1="20975@x" ObjectIDZND2="20972@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-107714_0" Pin0InfoVect2LinkObjId="SW-107711_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_235cfb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="597,-4703 571,-4703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333a520">
     <polyline DF8003:Layer="0" fill="none" points="571,-4703 571,-4725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_235cfb0@0" ObjectIDND1="28744@x" ObjectIDND2="g_307d030@0" ObjectIDZND0="0@x" ObjectIDZND1="20975@x" ObjectIDZND2="20972@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-107714_0" Pin0InfoVect2LinkObjId="SW-107711_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_235cfb0_0" Pin1InfoVect1LinkObjId="SW-134558_0" Pin1InfoVect2LinkObjId="g_307d030_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4703 571,-4725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3348a70">
     <polyline DF8003:Layer="0" fill="none" points="665,-5127 665,-5118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-5127 665,-5118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3348cd0">
     <polyline DF8003:Layer="0" fill="none" points="752,-5129 752,-5118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-5129 752,-5118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3348f30">
     <polyline DF8003:Layer="0" fill="none" points="721,-5170 752,-5170 752,-5163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-5170 752,-5170 752,-5163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_334ba40">
     <polyline DF8003:Layer="0" fill="none" points="666,-5163 666,-5170 694,-5170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="666,-5163 666,-5170 694,-5170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334f3a0">
     <polyline DF8003:Layer="0" fill="none" points="815,-4966 787,-4966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="28744@x" ObjectIDZND1="g_307d030@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-134558_0" Pin0InfoVect1LinkObjId="g_307d030_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="815,-4966 787,-4966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334fd10">
     <polyline DF8003:Layer="0" fill="none" points="787,-4539 571,-4539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="28744@x" ObjectIDND1="g_307d030@0" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="14562@x" ObjectIDZND2="g_235cfb0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-68610_0" Pin0InfoVect2LinkObjId="g_235cfb0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-134558_0" Pin1InfoVect1LinkObjId="g_307d030_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="787,-4539 571,-4539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334ff00">
     <polyline DF8003:Layer="0" fill="none" points="1216,-4568 1216,-4539 787,-4539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="28744@x" ObjectIDND1="g_307d030@0" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-134558_0" Pin1InfoVect1LinkObjId="g_307d030_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-4568 1216,-4539 787,-4539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3350950">
     <polyline DF8003:Layer="0" fill="none" points="787,-4539 787,-4966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28744@x" ObjectIDND1="g_307d030@0" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-134558_0" Pin1InfoVect1LinkObjId="g_307d030_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="787,-4539 787,-4966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3350b90">
     <polyline DF8003:Layer="0" fill="none" points="787,-4966 787,-4979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="28744@x" ObjectIDND2="g_307d030@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-134558_0" Pin1InfoVect2LinkObjId="g_307d030_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="787,-4966 787,-4979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3350df0">
     <polyline DF8003:Layer="0" fill="none" points="787,-5106 787,-5118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="787,-5106 787,-5118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33515d0">
     <polyline DF8003:Layer="0" fill="none" points="787,-5015 787,-5030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="787,-5015 787,-5030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3351830">
     <polyline DF8003:Layer="0" fill="none" points="787,-5070 787,-5057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="787,-5070 787,-5057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33528b0">
     <polyline DF8003:Layer="0" fill="none" points="571,-4466 571,-4539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="14562@x" ObjectIDZND0="28744@x" ObjectIDZND1="g_307d030@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-134558_0" Pin0InfoVect1LinkObjId="g_307d030_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-68610_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4466 571,-4539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3352b10">
     <polyline DF8003:Layer="0" fill="none" points="571,-4539 571,-4703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="28744@x" ObjectIDND1="g_307d030@0" ObjectIDND2="0@x" ObjectIDZND0="g_235cfb0@0" ObjectIDZND1="0@x" ObjectIDZND2="20975@x" Pin0InfoVect0LinkObjId="g_235cfb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-107714_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-134558_0" Pin1InfoVect1LinkObjId="g_307d030_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="571,-4539 571,-4703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3352d70">
     <polyline DF8003:Layer="0" fill="none" points="851,-4966 860,-4966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_334e2e0@0" Pin0InfoVect0LinkObjId="g_334e2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-4966 860,-4966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3352fd0">
     <polyline DF8003:Layer="0" fill="none" points="928,-3821 928,-3808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21375@0" ObjectIDZND0="29403@1" Pin0InfoVect0LinkObjId="SW-193384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35db720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-3821 928,-3808 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="0" busDevId="0" cx="428" cy="-3371" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="429" cy="-3894" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="308" cy="-3894" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="428" cy="-3894" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1739" cy="-4317" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1739" cy="-4317" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21619" cx="1270" cy="-4737" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21619" cx="1216" cy="-4737" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="3464" cx="1016" cy="-5191" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21619" cx="1534" cy="-4737" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="26475" cx="1452" cy="-3854" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="26475" cx="1455" cy="-3854" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21556" cx="1788" cy="-4005" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="19835" cx="324" cy="-4469" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21254" cx="928" cy="-3405" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21254" cx="655" cy="-3405" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21254" cx="1164" cy="-3405" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="12192" cx="1534" cy="-4305" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="12192" cx="857" cy="-4305" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="12192" cx="893" cy="-4305" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="12192" cx="1455" cy="-4305" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="12193" cx="747" cy="-4305" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="12193" cx="429" cy="-4305" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="12193" cx="571" cy="-4305" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="20966" cx="324" cy="-4910" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="20966" cx="571" cy="-4910" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="752" cy="-5118" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="787" cy="-5118" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="665" cy="-5118" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21375" cx="928" cy="-3821" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 386.000000 -3983.000000) translate(0,17)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 377.000000 -4019.000000) translate(0,17)">3712</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -3936.000000) translate(0,17)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -4084.000000) translate(0,17)">3712D</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -3923.000000) translate(0,17)">鱼庄河电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 384.000000 -3821.000000) translate(0,17)">372</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 376.000000 -3864.000000) translate(0,17)">3721</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 372.000000 -3784.000000) translate(0,17)">3722</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 372.000000 -3358.000000) translate(0,17)">纳嫩河电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 -3526.000000) translate(0,17)">39010</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 306.000000 -3541.000000) translate(0,17)">3901</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 270.000000 -3589.000000) translate(0,17)">39017</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -3492.000000) translate(0,17)">332</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 636.000000 -3534.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 637.000000 -3443.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 914.000000 -3395.000000) translate(0,20)">鄂嘉变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 685.000000 -3604.000000) translate(0,17)">33267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -3924.000000) translate(0,17)">391</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 -3814.000000) translate(0,20)">爱尼山变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 949.000000 -4061.000000) translate(0,17)">3900</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 886.000000 -3493.000000) translate(0,17)">331</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 911.000000 -3534.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -3443.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -3824.000000) translate(0,17)">303</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 288.000000 -3863.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -3700.000000) translate(0,17)">纳鱼鄂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 -3964.000000) translate(0,17)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1431.000000 -4013.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -3916.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1467.000000 -4042.000000) translate(0,17)">38167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -3881.000000) translate(0,20)">雨龙变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1403.000000 -3795.000000) translate(0,17)">382</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1432.000000 -3835.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1430.000000 -3748.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1481.000000 -3742.000000) translate(0,17)">38267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -3401.000000) translate(0,17)">35kV大麦地变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 748.000000 -4425.000000) translate(0,17)">110kV双柏变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1616.000000 -4540.000000) translate(0,17)">双小妥线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1687.000000 -4413.000000) translate(0,17)">341</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1711.000000 -4458.000000) translate(0,17)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1711.000000 -4364.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1766.000000 -4501.000000) translate(0,17)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1677.000000 -4311.000000) translate(0,17)">35kV小庙河变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,15)">35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,51)">双</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,69)">妥</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,87)">大</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,105)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,123)">T</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,141)">凹</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,159)">侧</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,177)">力</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,195)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -4892.000000) translate(0,213)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 -5224.000000) translate(0,20)">110kV白龙新村变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 346.000000 -4296.000000) translate(0,17)">Ⅱ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1522.000000 -4300.000000) translate(0,17)">Ⅰ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 228.000000 -3740.000000) translate(0,12)">大湾电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 228.000000 -3725.000000) translate(0,12)">施工电源</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 745.000000 -4110.000000) translate(0,17)">2号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1291.000000 -4984.000000) translate(0,17)">35367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1748.500000 -4031.000000) translate(0,17)">安龙堡变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -4936.000000) translate(0,17)">35kV大庄变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.000000 -4979.000000) translate(0,17)">白妥线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1796.000000 -4500.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 231.500000 -3754.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -3678.000000) translate(0,17)">大麦T安线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1567.000000 -3602.000000) translate(0,17)">大麦T普线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -4834.000000) translate(0,17)">362</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 -4893.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -4802.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -4882.000000) translate(0,17)">36217</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -4829.000000) translate(0,17)">36260</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -4753.000000) translate(0,17)">36267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 488.000000 -4766.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1633.000000 -4605.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 975.000000 -3653.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 942.000000 -3591.000000) translate(0,17)">33167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 840.000000 -3677.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -3647.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1473.000000 -3537.000000) translate(0,17)">311</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2149.000000 -3682.000000) translate(0,17)">35kV普龙变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1865.000000 -3505.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e92180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e92180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e92180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e92180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e92180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e92180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e92180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,59)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,101)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,143)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,164)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,185)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,206)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,227)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,248)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,269)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,290)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,311)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,332)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,353)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_235d300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1957.000000 -3478.000000) translate(0,11)">站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30cd720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 788.000000 -4399.000000) translate(0,17)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30cdd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -4349.000000) translate(0,17)">3122</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30cdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 860.000000 -4344.000000) translate(0,17)">3121</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30ce1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 438.000000 -4232.000000) translate(0,17)">362</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30ce410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 436.000000 -4187.000000) translate(0,17)">3626</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30ce650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 436.000000 -4279.000000) translate(0,17)">3622</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30ce890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 -4237.000000) translate(0,17)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30cead0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.000000 -4192.000000) translate(0,17)">3616</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30ced10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.000000 -4284.000000) translate(0,17)">3611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30cef50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 -5184.000000) translate(0,17)">Ⅱ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1025.000000 -5120.000000) translate(0,17)">367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30cf8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1023.000000 -5075.000000) translate(0,17)">3676</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30cfae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1023.000000 -5167.000000) translate(0,17)">3672</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30d8d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -3499.000000) translate(0,17)">333</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30d93c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1147.000000 -3537.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30d9600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1148.000000 -3446.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30d9840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1181.000000 -3562.000000) translate(0,17)">33367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30ed9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -3785.000000) translate(0,17)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30edfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1147.000000 -3721.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30f7fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1100.000000 -3906.000000) translate(0,17)">空龙河一级电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30f9020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1212.000000 -3800.000000) translate(0,17)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30f9230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -3726.000000) translate(0,17)">TV</text>
   <text DF8003:Layer="0" fill="rgb(38,38,38)" font-family="SimHei" font-size="20" graphid="g_30fbe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -86.000000 -5212.000000) translate(0,16)">双柏片区35kV电网图</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31018e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 956.000000 -4143.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3102ef0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1355.500000 -4001.500000) translate(0,17)">#1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3102ef0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1355.500000 -4001.500000) translate(0,38)">站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3102ef0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1355.500000 -4001.500000) translate(0,59)">用</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3102ef0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1355.500000 -4001.500000) translate(0,80)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3103cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1463.000000 -4236.000000) translate(0,17)">363</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3104190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1460.000000 -4190.000000) translate(0,17)">3636</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31043d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 -4283.000000) translate(0,17)">3631</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3104610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.111288 -4387.000000) translate(0,17)">365</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3104850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.111288 -4347.000000) translate(0,17)">3651</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3104a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1539.111288 -4429.000000) translate(0,17)">3656</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3104cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -4386.000000) translate(0,17)">364</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3104f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -4336.000000) translate(0,12)">3642</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31055f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 -4440.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_310cb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1399.000000 -4942.000000) translate(0,12)">35kV 2号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_310f040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -3724.000000) translate(0,17)">空</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_310f040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -3724.000000) translate(0,38)">龙</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_310f040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -3724.000000) translate(0,59)">河</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_310f040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -3724.000000) translate(0,80)">一</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_310f040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -3724.000000) translate(0,101)">级</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_310f040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -3724.000000) translate(0,122)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_310fd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 277.000000 -4842.000000) translate(0,17)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31101d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 305.000000 -4894.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3110410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -4794.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3110650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 348.000000 -4886.000000) translate(0,17)">36117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3110890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 346.000000 -4840.000000) translate(0,17)">36160</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3110ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 -4784.000000) translate(0,17)">36167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3110d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -4788.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30679a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 242.000000 -4625.000000) translate(0,17)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3067fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 284.000000 -4542.000000) translate(0,17)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3068210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 305.000000 -4507.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3068450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 304.000000 -4577.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3068690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 282.000000 -4461.000000) translate(0,12)">大庄光伏电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3068690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 282.000000 -4461.000000) translate(0,27)">(桐果开关站)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_306ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 300.000000 -4719.000000) translate(0,17)">桐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_306ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 300.000000 -4719.000000) translate(0,38)">大</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_306ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 300.000000 -4719.000000) translate(0,59)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_307a150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 889.000000 -4536.000000) translate(0,16)">双妥大线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_307b7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1198.000000 -4765.000000) translate(0,12)">I段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_307c430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 -4567.000000) translate(0,15)">N4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30816c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1286.000000 -4541.000000) translate(0,17)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30816c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1286.000000 -4541.000000) translate(0,38)">路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30816c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1286.000000 -4541.000000) translate(0,59)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3081cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1402.000000 -3657.000000) translate(0,17)">N001</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30822b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 389.000000 -4148.000000) translate(0,17)">N1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3082500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 389.000000 -3710.000000) translate(0,17)">N55</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3088080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 -4082.000000) translate(0,12)">A,B相</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3089220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -4113.000000) translate(0,12)">35kV雨龙线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3089220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -4113.000000) translate(0,26)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_308d780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1337.000000 -3717.000000) translate(0,12)">A,B相</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_308e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1297.000000 -3684.000000) translate(0,17)">35kV大麦地线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_308e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1297.000000 -3684.000000) translate(0,38)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_309ee20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 -3668.000000) translate(0,17)">N02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_30a3f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -3978.000000) translate(0,12)">42B厂用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 -4667.000000) translate(0,12)">351</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d4150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -4618.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -4713.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d4610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 -4677.000000) translate(0,12)">35117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d4930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -4666.000000) translate(0,12)">352</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d4db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1534.000000 -4621.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d4ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1541.000000 -4715.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d5230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1597.000000 -4680.000000) translate(0,12)">35217</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d5470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.000000 -4831.000000) translate(0,12)">353</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d56b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1275.000000 -4776.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d58f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1277.000000 -4877.000000) translate(0,12)">3533</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d5b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1319.000000 -4794.000000) translate(0,12)">35317</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_35d5d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1275.000000 -4934.000000) translate(0,17)">3536</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_35d5fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1591.000000 -4568.000000) translate(0,17)">N2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d82e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -3755.000000) translate(0,12)">392</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d8cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 887.000000 -3798.000000) translate(0,12)">3921</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d8f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -3715.000000) translate(0,12)">3926</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_35db980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 847.000000 -3873.000000) translate(0,14)">3911</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e0490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 980.000000 -3733.000000) translate(0,12)">39267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_35f0ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1244.000000 -5140.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_35f1a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 846.000000 -3974.000000) translate(0,14)">3916</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_35f5a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 780.000000 -4049.000000) translate(0,17)">39167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f6070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -3999.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f6510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1795.000000 -3982.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f67a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1797.000000 -3935.000000) translate(0,12)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f69e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1795.000000 -3890.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f6c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1804.000000 -3872.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f6e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2183.000000 -3606.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f73f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2166.000000 -3553.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2177.000000 -3497.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f78b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2108.000000 -3606.000000) translate(0,12)">3211</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2095.000000 -3553.000000) translate(0,12)">32117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2049.000000 -3570.000000) translate(0,12)">321</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2030.000000 -3617.000000) translate(0,12)">32160</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f81b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1976.000000 -3606.000000) translate(0,12)">3216</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f83f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 -3612.000000) translate(0,12)">32167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f8630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2258.000000 -3561.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f8870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -3477.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f8ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 376.000000 -3461.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f8cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 -3412.000000) translate(0,12)">301</text>
   <text DF8003:Layer="0" fill="rgb(0,0,0)" font-family="SimSun" font-size="25" graphid="g_35f92f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -5216.000000) translate(0,20)">配调返回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_35fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1407.000000 -4776.000000) translate(0,20)">妥甸变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_35fcc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -5063.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3606730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -4448.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_332d450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1674.000000 -3861.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_333a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.000000 -4654.000000) translate(0,16)">双</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_333a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.000000 -4654.000000) translate(0,36)">妥</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_333a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.000000 -4654.000000) translate(0,56)">大</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_333a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.000000 -4654.000000) translate(0,76)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3349190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 659.000000 -5222.000000) translate(0,17)">凹侧力变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_334a170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.500000 -5199.000000) translate(0,17)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_334a3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 668.000000 -5153.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_334a600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 725.000000 -5153.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_334a840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 787.000000 -5139.000000) translate(0,12)">Ⅰ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_334aa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -5112.000000) translate(0,12)">Ⅱ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_334b320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.000000 -5055.000000) translate(0,17)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_334b5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 795.000000 -5096.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_334b800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -5009.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_334ed70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 809.000000 -4991.000000) translate(0,17)">38167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33acd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1444.000000 -4511.000000) translate(0,17)">双小妥线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33362f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -4452.000000) translate(0,16)">转供电电缆</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 393.000000 -4229.000000) translate(0,16)">鱼</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 393.000000 -4229.000000) translate(0,36)">庄</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 393.000000 -4229.000000) translate(0,56)">河</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 393.000000 -4229.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 956.000000 -3977.000000) translate(0,16)">1号站用变</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -4256.000000) translate(0,16)">爱</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -4256.000000) translate(0,36)">尼</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -4256.000000) translate(0,56)">山</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -4256.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.000000 -3794.000000) translate(0,16)">爱</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.000000 -3794.000000) translate(0,36)">鄂</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.000000 -3794.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -4266.000000) translate(0,16)">雨</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -4266.000000) translate(0,36)">龙</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -4266.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 -3830.000000) translate(0,16)">大</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 -3830.000000) translate(0,36)">麦</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 -3830.000000) translate(0,56)">地</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 -3830.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -3436.000000) translate(0,11)">站用变</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="0" id="SW-19666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1007.000000 -5088.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3107" ObjectName="SW-CX_BLXC.CX_BLX_367BK"/>
     <cge:Meas_Ref ObjectId="19666"/>
    <cge:TPSR_Ref TObjectID="3107"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.111288 -4358.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14529" ObjectName="SW-CX_SBEQ.CX_SBEQ_365BK"/>
     <cge:Meas_Ref ObjectId="68445"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-107989">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.304000 -4803.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21007" ObjectName="SW-SB_DZ.SB_DZ_362BK"/>
     <cge:Meas_Ref ObjectId="107989"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 -4370.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-70722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.000000 -4359.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15948" ObjectName="SW-CX_SBEQ.CX_SBEQ_312BK"/>
     <cge:Meas_Ref ObjectId="70722"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68397">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.000000 -4205.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14533" ObjectName="SW-CX_SBEQ.CX_SBEQ_361BK"/>
     <cge:Meas_Ref ObjectId="68397"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.000000 -3895.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21381" ObjectName="SW-SB_ANS.SB_ANS_391BK"/>
     <cge:Meas_Ref ObjectId="110929"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-109927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -3463.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21288" ObjectName="SW-SB_EJ.SB_EJ_331BK"/>
     <cge:Meas_Ref ObjectId="109927"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-159568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -3759.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26496" ObjectName="SW-SB_YL.SB_YL_382BK"/>
     <cge:Meas_Ref ObjectId="159568"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -3906.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21577" ObjectName="SW-SB_ALB.SB_ALB_371BK"/>
     <cge:Meas_Ref ObjectId="113659"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -3502.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110430">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2037.000000 -3570.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21328" ObjectName="SW-SB_PL.SB_PL_321BK"/>
     <cge:Meas_Ref ObjectId="110430"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 -4200.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14530" ObjectName="SW-CX_SBEQ.CX_SBEQ_362BK"/>
     <cge:Meas_Ref ObjectId="68468"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 -3949.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -3790.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -3789.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-109655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 -3461.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21281" ObjectName="SW-SB_EJ.SB_EJ_332BK"/>
     <cge:Meas_Ref ObjectId="109655"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-126209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -3383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23095" ObjectName="SW-CX_NNH.CX_NNH_301BK"/>
     <cge:Meas_Ref ObjectId="126209"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-109976">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 -3466.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21292" ObjectName="SW-SB_EJ.SB_EJ_333BK"/>
     <cge:Meas_Ref ObjectId="109976"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 -3739.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-108239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.385081 -4812.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21008" ObjectName="SW-SB_DZ.SB_DZ_361BK"/>
     <cge:Meas_Ref ObjectId="108239"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-94135">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.866667 -0.000000 0.000000 -0.763158 315.611214 -4514.710526)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19837" ObjectName="SW-CX_DZ.CX_DZ_371BK"/>
     <cge:Meas_Ref ObjectId="94135"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68491">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -4357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14531" ObjectName="SW-CX_SB.CX_SB_364BK"/>
     <cge:Meas_Ref ObjectId="68491"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-68422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 -4205.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14528" ObjectName="SW-CX_SB.CX_SB_363BK"/>
     <cge:Meas_Ref ObjectId="68422"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-159509">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 -3934.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26490" ObjectName="SW-SB_YL.SB_YL_381BK"/>
     <cge:Meas_Ref ObjectId="159509"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113954">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.304000 -4802.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21622" ObjectName="SW-SB_TD.SB_TD_353BK"/>
     <cge:Meas_Ref ObjectId="113954"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113969">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.304000 -4637.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21625" ObjectName="SW-SB_TD.SB_TD_352BK"/>
     <cge:Meas_Ref ObjectId="113969"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-113984">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1207.304000 -4638.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21626" ObjectName="SW-SB_TD.SB_TD_351BK"/>
     <cge:Meas_Ref ObjectId="113984"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-110956">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -3726.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21382" ObjectName="SW-SB_ANS.SB_ANS_392BK"/>
     <cge:Meas_Ref ObjectId="110956"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 -5022.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 685.359375 -5160.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.820000 -0.000000 0.000000 -0.811111 917.000000 -3944.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.820000 -0.000000 0.000000 -0.811111 917.000000 -3944.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 835.500000 -4111.500000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 835.500000 -4111.500000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1383.000000 -3438.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1383.000000 -3438.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1426.000000 -3346.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1426.000000 -3346.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1928.000000 -3381.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1928.000000 -3381.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.000000 -3800.000000)" xlink:href="#transformer2:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.000000 -3800.000000)" xlink:href="#transformer2:shape1_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.000000 -4883.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.000000 -4883.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1370.000000 -3915.000000)" xlink:href="#transformer2:shape38_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1370.000000 -3915.000000)" xlink:href="#transformer2:shape38_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 -3978.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 -3978.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.000000 -3694.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.000000 -3694.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2252.000000 -3622.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2252.000000 -3622.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="0" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -117.000000 -5164.013514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="40" qtmmishow="hidden" width="226" x="-117" y="-5223"/></g>
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/></g>
  </g><g id="ArcTwoPoints_Layer">
   <polyline DF8003:Layer="0" fill="none" points="637,-4328 639,-4327 642,-4327 644,-4325 646,-4324 648,-4321 650,-4319 652,-4316 654,-4312 656,-4308 657,-4304 658,-4300 659,-4295 660,-4290 661,-4285 661,-4280 661,-4275 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="0" id="g_2f2f530">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1674.000000 -4568.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_235cfb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.304000 -4697.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2f22250">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 457.304000 -4736.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2719e70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1475.000000 -4089.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1135a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 810.000000 -3938.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1191fb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 950.000000 -4066.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_26ac310">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1037.000000 -3654.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1084860">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 -3606.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2a7fd50">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 906.000000 -3694.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2f24e10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 881.500000 -4127.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2f7f240">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1470.000000 -3683.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3592810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1847.000000 -3506.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2f91da0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1877.000000 -3512.000000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30b9d50">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 633.000000 -3666.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30c8a90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 -3564.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30cd020">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 373.000000 -3651.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30dc620">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1142.000000 -3706.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30f6920">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1236.000000 -3777.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_31011e0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1008.000000 -4122.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_31036c0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1410.000000 -4054.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_310b8f0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1398.000000 -4893.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3058a50">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 212.304000 -4768.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3059870">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 341.385081 -4613.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_305a620">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 343.385081 -4586.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_306dfe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 341.385081 -4731.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3078130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 488.000000 -4394.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_30793a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.304000 -4575.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_307d030">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1242.500000 -4497.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3080e70">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1276.000000 -4466.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3087000">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1340.000000 -4091.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_308d080">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1326.000000 -3700.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35e1bf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 393.000000 -3552.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35e8010">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1461.111288 -4538.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35ec130">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1276.500000 -5016.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35fd0c0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 895.304000 -5049.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3600990">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1002.500000 -4967.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3603440">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 457.304000 -4477.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_33298b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1705.000000 -3805.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_332dc70">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1674.304000 -3847.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="0" id="g_30cfd20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1797.000000 -4463.000000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35f0010">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1252.500000 -5117.500000)" xlink:href="#voltageTransformer:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="40" qtmmishow="hidden" width="226" x="-117" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="40" opacity="0" stroke="white" transform="" width="226" x="-117" y="-5223"/></g>
   <g DF8003:Layer="0" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-166" y="-5240"/></g>
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="127" x="138" y="-5223"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="$AUDIT-BAD-LAYER:0.000000 0.000000" layer11="图层2:0.000000 0.000000" layer12="GDXT:0.000000 0.000000" layer13="Defpoints:0.000000 0.000000" layer14="yc:0.000000 0.000000" layer15="PUBLIC:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer4="$AUDIT-BAD-LAYER:0.000000 0.000000" layer5="图层2:0.000000 0.000000" layer6="GDXT:0.000000 0.000000" layer7="Defpoints:0.000000 0.000000" layer8="yc:0.000000 0.000000" layer9="0:0.000000 0.000000" layerN="16" moveAndZoomFlag="1"/>
</svg>