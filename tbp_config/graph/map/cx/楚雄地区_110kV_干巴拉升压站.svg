<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-111" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3085 -1200 2033 1174">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="17" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.337605" x1="7" x2="7" y1="47" y2="36"/>
    <polyline arcFlag="1" points="7,24 7,24 7,24 7,24 8,24 8,24 8,25 8,25 9,25 9,25 9,26 9,26 9,26 9,27 9,27 9,28 9,28 9,28 9,29 8,29 8,29 8,29 8,30 7,30 7,30 7,30 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="6,18 7,18 7,18 7,18 7,18 8,18 8,18 8,19 8,19 8,19 8,20 9,20 9,20 9,21 9,21 9,21 8,22 8,22 8,22 8,23 8,23 8,23 7,23 7,24 7,24 7,24 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="7,30 7,30 7,30 7,30 8,30 8,31 8,31 8,31 9,31 9,32 9,32 9,32 9,33 9,33 9,33 9,34 9,34 9,35 9,35 8,35 8,35 8,36 8,36 7,36 7,36 7,36 " stroke-width="0.0170053"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.266312" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="4" x2="9" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="0" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape177">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_264bf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape151">
    <ellipse cx="28" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="21,7 5,7 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.240771" x1="3" x2="7" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.240771" x1="0" x2="10" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.206229" x1="4" x2="6" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="39" x2="39" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="36" x2="39" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="28" x2="28" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="25" x2="28" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="11" y2="9"/>
    <ellipse cx="38" cy="16" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="18" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="27" x2="27" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="24" x2="27" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="30" x2="27" y1="24" y2="22"/>
    <ellipse cx="27" cy="21" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="17" x2="20" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="14" y1="13" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="14" x2="20" y1="16" y2="16"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.40306"/>
    <polyline points="58,100 64,100 " stroke-width="1.40306"/>
    <polyline points="64,100 64,93 " stroke-width="1.40306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="voltageTransformer:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="64" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="57" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="9" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="45" y1="9" y2="9"/>
    <circle cx="31" cy="46" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="40" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="31" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="23" x2="23" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="39" x2="22" y1="10" y2="10"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1184" width="2043" x="3080" y="-1205"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="4166" x2="4183" y1="-664" y2="-664"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="4183" x2="4183" y1="-664" y2="-686"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.222222" x1="4183" x2="4183" y1="-701" y2="-705"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="4183" x2="4183" y1="-743" y2="-722"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4183,-711 4178,-722 4189,-722 4183,-711 4183,-712 4183,-711 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4183,-697 4178,-686 4189,-686 4183,-697 4183,-696 4183,-697 " stroke="rgb(170,85,127)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-70506">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -824.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15801" ObjectName="SW-CX_GBL.CX_GBL_151BK"/>
     <cge:Meas_Ref ObjectId="70506"/>
    <cge:TPSR_Ref TObjectID="15801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -546.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15806" ObjectName="SW-CX_GBL.CX_GBL_001BK"/>
     <cge:Meas_Ref ObjectId="70511"/>
    <cge:TPSR_Ref TObjectID="15806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70555">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -541.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15849" ObjectName="SW-CX_GBL.CX_GBL_062BK"/>
     <cge:Meas_Ref ObjectId="70555"/>
    <cge:TPSR_Ref TObjectID="15849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70556">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 -388.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15850" ObjectName="SW-CX_GBL.CX_GBL_061BK"/>
     <cge:Meas_Ref ObjectId="70556"/>
    <cge:TPSR_Ref TObjectID="15850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70549">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -376.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15841" ObjectName="SW-CX_GBL.CX_GBL_059BK"/>
     <cge:Meas_Ref ObjectId="70549"/>
    <cge:TPSR_Ref TObjectID="15841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70546">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -374.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15837" ObjectName="SW-CX_GBL.CX_GBL_058BK"/>
     <cge:Meas_Ref ObjectId="70546"/>
    <cge:TPSR_Ref TObjectID="15837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70543">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -374.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15833" ObjectName="SW-CX_GBL.CX_GBL_057BK"/>
     <cge:Meas_Ref ObjectId="70543"/>
    <cge:TPSR_Ref TObjectID="15833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70540">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.000000 -375.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15829" ObjectName="SW-CX_GBL.CX_GBL_056BK"/>
     <cge:Meas_Ref ObjectId="70540"/>
    <cge:TPSR_Ref TObjectID="15829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70537">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4396.000000 -376.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15825" ObjectName="SW-CX_GBL.CX_GBL_055BK"/>
     <cge:Meas_Ref ObjectId="70537"/>
    <cge:TPSR_Ref TObjectID="15825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70534">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -374.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15821" ObjectName="SW-CX_GBL.CX_GBL_054BK"/>
     <cge:Meas_Ref ObjectId="70534"/>
    <cge:TPSR_Ref TObjectID="15821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70531">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 -371.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15817" ObjectName="SW-CX_GBL.CX_GBL_053BK"/>
     <cge:Meas_Ref ObjectId="70531"/>
    <cge:TPSR_Ref TObjectID="15817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70528">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -374.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15813" ObjectName="SW-CX_GBL.CX_GBL_052BK"/>
     <cge:Meas_Ref ObjectId="70528"/>
    <cge:TPSR_Ref TObjectID="15813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70525">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -375.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15809" ObjectName="SW-CX_GBL.CX_GBL_051BK"/>
     <cge:Meas_Ref ObjectId="70525"/>
    <cge:TPSR_Ref TObjectID="15809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.142857 -0.000000 0.000000 -0.297214 4262.714286 -779.819745)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47739" ObjectName="SW-CX_GBL.XB"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="47739"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a8d160">
    <use class="BV-110KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 4235.000000 -1112.000000)" xlink:href="#voltageTransformer:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_GBL.CX_GBL_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3527,-482 5118,-482 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="15859" ObjectName="BS-CX_GBL.CX_GBL_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="15859"/></metadata>
   <polyline fill="none" opacity="0" points="3527,-482 5118,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_GBL.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4247,-813 4280,-813 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47738" ObjectName="BS-CX_GBL.XM"/>
    <cge:TPSR_Ref TObjectID="47738"/></metadata>
   <polyline fill="none" opacity="0" points="4247,-813 4280,-813 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3775.000000 -713.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3775.000000 -713.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4741.000000 -643.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4741.000000 -643.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_GBL.CX_GBL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="23358"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(0.712727 -0.000000 0.000000 -0.735294 4237.000000 -693.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.712727 -0.000000 0.000000 -0.735294 4237.000000 -693.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="15857" ObjectName="TF-CX_GBL.CX_GBL_1T"/>
    <cge:TPSR_Ref TObjectID="15857"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3455000">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4761.000000 -730.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d8e650">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -671.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a78980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 -576.018519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e6440">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4005.000000 -646.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f20a60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3957.000000 -610.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_46b4f70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -1028.018519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261f9a0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3804.000000 -792.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41e3b00">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3723.000000 -715.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c18e0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3765.000000 -711.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b46500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 -254.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b7dc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 -247.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cf1580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3580.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e59e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3622.000000 -226.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41b4590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acc1d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3654e00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43acb20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d92f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4207.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4139a40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c0f40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4357.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_235b020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.000000 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ae2080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43a8bd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2abcc20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4657.000000 -254.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_358e240">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f7eba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29cc6d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.000000 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_411b3e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4957.000000 -254.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e47b20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5000.000000 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a66330">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4761.000000 -717.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_410fb00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -727.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_264bcc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3610.000000 -121.000000)" xlink:href="#lightningRod:shape177"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41fb980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -615.000000)" xlink:href="#lightningRod:shape151"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78588" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3257.538462 -1016.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78588" ObjectName="CX_GBL:CX_GBL_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79722" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3255.538462 -975.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79722" ObjectName="CX_GBL:CX_GBL_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-70574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3602.000000 -522.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15859"/>
     <cge:Term_Ref ObjectID="23360"/>
    <cge:TPSR_Ref TObjectID="15859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -865.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15801"/>
     <cge:Term_Ref ObjectID="23244"/>
    <cge:TPSR_Ref TObjectID="15801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70432" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -865.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70432" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15801"/>
     <cge:Term_Ref ObjectID="23244"/>
    <cge:TPSR_Ref TObjectID="15801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -865.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15801"/>
     <cge:Term_Ref ObjectID="23244"/>
    <cge:TPSR_Ref TObjectID="15801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4388.000000 -588.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15806"/>
     <cge:Term_Ref ObjectID="23254"/>
    <cge:TPSR_Ref TObjectID="15806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4388.000000 -588.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15806"/>
     <cge:Term_Ref ObjectID="23254"/>
    <cge:TPSR_Ref TObjectID="15806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4388.000000 -588.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15806"/>
     <cge:Term_Ref ObjectID="23254"/>
    <cge:TPSR_Ref TObjectID="15806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15850"/>
     <cge:Term_Ref ObjectID="23342"/>
    <cge:TPSR_Ref TObjectID="15850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15850"/>
     <cge:Term_Ref ObjectID="23342"/>
    <cge:TPSR_Ref TObjectID="15850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15850"/>
     <cge:Term_Ref ObjectID="23342"/>
    <cge:TPSR_Ref TObjectID="15850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3788.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15841"/>
     <cge:Term_Ref ObjectID="23324"/>
    <cge:TPSR_Ref TObjectID="15841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3788.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15841"/>
     <cge:Term_Ref ObjectID="23324"/>
    <cge:TPSR_Ref TObjectID="15841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3788.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15841"/>
     <cge:Term_Ref ObjectID="23324"/>
    <cge:TPSR_Ref TObjectID="15841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15837"/>
     <cge:Term_Ref ObjectID="23316"/>
    <cge:TPSR_Ref TObjectID="15837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15837"/>
     <cge:Term_Ref ObjectID="23316"/>
    <cge:TPSR_Ref TObjectID="15837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15837"/>
     <cge:Term_Ref ObjectID="23316"/>
    <cge:TPSR_Ref TObjectID="15837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15833"/>
     <cge:Term_Ref ObjectID="23308"/>
    <cge:TPSR_Ref TObjectID="15833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15833"/>
     <cge:Term_Ref ObjectID="23308"/>
    <cge:TPSR_Ref TObjectID="15833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15833"/>
     <cge:Term_Ref ObjectID="23308"/>
    <cge:TPSR_Ref TObjectID="15833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15829"/>
     <cge:Term_Ref ObjectID="23300"/>
    <cge:TPSR_Ref TObjectID="15829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15829"/>
     <cge:Term_Ref ObjectID="23300"/>
    <cge:TPSR_Ref TObjectID="15829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15829"/>
     <cge:Term_Ref ObjectID="23300"/>
    <cge:TPSR_Ref TObjectID="15829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4413.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15825"/>
     <cge:Term_Ref ObjectID="23292"/>
    <cge:TPSR_Ref TObjectID="15825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4413.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15825"/>
     <cge:Term_Ref ObjectID="23292"/>
    <cge:TPSR_Ref TObjectID="15825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4413.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15825"/>
     <cge:Term_Ref ObjectID="23292"/>
    <cge:TPSR_Ref TObjectID="15825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70454" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15821"/>
     <cge:Term_Ref ObjectID="23284"/>
    <cge:TPSR_Ref TObjectID="15821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70455" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15821"/>
     <cge:Term_Ref ObjectID="23284"/>
    <cge:TPSR_Ref TObjectID="15821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70453" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15821"/>
     <cge:Term_Ref ObjectID="23284"/>
    <cge:TPSR_Ref TObjectID="15821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15817"/>
     <cge:Term_Ref ObjectID="23276"/>
    <cge:TPSR_Ref TObjectID="15817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15817"/>
     <cge:Term_Ref ObjectID="23276"/>
    <cge:TPSR_Ref TObjectID="15817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15817"/>
     <cge:Term_Ref ObjectID="23276"/>
    <cge:TPSR_Ref TObjectID="15817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15813"/>
     <cge:Term_Ref ObjectID="23268"/>
    <cge:TPSR_Ref TObjectID="15813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15813"/>
     <cge:Term_Ref ObjectID="23268"/>
    <cge:TPSR_Ref TObjectID="15813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15813"/>
     <cge:Term_Ref ObjectID="23268"/>
    <cge:TPSR_Ref TObjectID="15813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15809"/>
     <cge:Term_Ref ObjectID="23260"/>
    <cge:TPSR_Ref TObjectID="15809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15809"/>
     <cge:Term_Ref ObjectID="23260"/>
    <cge:TPSR_Ref TObjectID="15809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15809"/>
     <cge:Term_Ref ObjectID="23260"/>
    <cge:TPSR_Ref TObjectID="15809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-70440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -662.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70440" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15857"/>
     <cge:Term_Ref ObjectID="23359"/>
    <cge:TPSR_Ref TObjectID="15857"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="159" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="159" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3385,-1061 3382,-1064 3382,-1010 3385,-1013 3385,-1061" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3385,-1061 3382,-1064 3531,-1064 3528,-1061 3385,-1061" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="3385,-1013 3382,-1010 3531,-1010 3528,-1013 3385,-1013" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="3528,-1061 3531,-1064 3531,-1010 3528,-1013 3528,-1061" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="3385" y="-1061"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3385" y="-1061"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="159" x="3246" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
   <g href="AVC干巴拉1.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3385" y="-1061"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3632,-201 3675,-201 3675,-166 " stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-70507">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4249.000000 -936.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15802" ObjectName="SW-CX_GBL.CX_GBL_1516SW"/>
     <cge:Meas_Ref ObjectId="70507"/>
    <cge:TPSR_Ref TObjectID="15802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70509">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 -984.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15804" ObjectName="SW-CX_GBL.CX_GBL_15167SW"/>
     <cge:Meas_Ref ObjectId="70509"/>
    <cge:TPSR_Ref TObjectID="15804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70508">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -880.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15803" ObjectName="SW-CX_GBL.CX_GBL_15160SW"/>
     <cge:Meas_Ref ObjectId="70508"/>
    <cge:TPSR_Ref TObjectID="15803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70551">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3807.000000 -299.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15844" ObjectName="SW-CX_GBL.CX_GBL_05917SW"/>
     <cge:Meas_Ref ObjectId="70551"/>
    <cge:TPSR_Ref TObjectID="15844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70560">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3655.000000 -298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15855" ObjectName="SW-CX_GBL.CX_GBL_06117SW"/>
     <cge:Meas_Ref ObjectId="70560"/>
    <cge:TPSR_Ref TObjectID="15855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70548">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3970.000000 -298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15840" ObjectName="SW-CX_GBL.CX_GBL_05817SW"/>
     <cge:Meas_Ref ObjectId="70548"/>
    <cge:TPSR_Ref TObjectID="15840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70545">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4132.000000 -298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15836" ObjectName="SW-CX_GBL.CX_GBL_05717SW"/>
     <cge:Meas_Ref ObjectId="70545"/>
    <cge:TPSR_Ref TObjectID="15836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70542">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4282.000000 -298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15832" ObjectName="SW-CX_GBL.CX_GBL_05617SW"/>
     <cge:Meas_Ref ObjectId="70542"/>
    <cge:TPSR_Ref TObjectID="15832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70539">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4432.000000 -298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15828" ObjectName="SW-CX_GBL.CX_GBL_05517SW"/>
     <cge:Meas_Ref ObjectId="70539"/>
    <cge:TPSR_Ref TObjectID="15828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70536">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4582.000000 -298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15824" ObjectName="SW-CX_GBL.CX_GBL_05417SW"/>
     <cge:Meas_Ref ObjectId="70536"/>
    <cge:TPSR_Ref TObjectID="15824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70533">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4732.000000 -299.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15820" ObjectName="SW-CX_GBL.CX_GBL_05317SW"/>
     <cge:Meas_Ref ObjectId="70533"/>
    <cge:TPSR_Ref TObjectID="15820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70530">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4882.000000 -298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15816" ObjectName="SW-CX_GBL.CX_GBL_05217SW"/>
     <cge:Meas_Ref ObjectId="70530"/>
    <cge:TPSR_Ref TObjectID="15816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70527">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5032.000000 -299.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15812" ObjectName="SW-CX_GBL.CX_GBL_05117SW"/>
     <cge:Meas_Ref ObjectId="70527"/>
    <cge:TPSR_Ref TObjectID="15812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70553">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3805.000000 -612.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15846" ObjectName="SW-CX_GBL.CX_GBL_06217SW"/>
     <cge:Meas_Ref ObjectId="70553"/>
    <cge:TPSR_Ref TObjectID="15846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70512">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -594.018519)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15807" ObjectName="SW-CX_GBL.CX_GBL_001XC"/>
     <cge:Meas_Ref ObjectId="70512"/>
    <cge:TPSR_Ref TObjectID="15807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70512">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -510.018519)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15808" ObjectName="SW-CX_GBL.CX_GBL_001XC1"/>
     <cge:Meas_Ref ObjectId="70512"/>
    <cge:TPSR_Ref TObjectID="15808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70554">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 -586.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15847" ObjectName="SW-CX_GBL.CX_GBL_062XC"/>
     <cge:Meas_Ref ObjectId="70554"/>
    <cge:TPSR_Ref TObjectID="15847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70554">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 -504.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15848" ObjectName="SW-CX_GBL.CX_GBL_062XC1"/>
     <cge:Meas_Ref ObjectId="70554"/>
    <cge:TPSR_Ref TObjectID="15848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70559">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3612.000000 -161.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15854" ObjectName="SW-CX_GBL.CX_GBL_0616SW"/>
     <cge:Meas_Ref ObjectId="70559"/>
    <cge:TPSR_Ref TObjectID="15854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70558">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3651.000000 -154.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15853" ObjectName="SW-CX_GBL.CX_GBL_06167SW"/>
     <cge:Meas_Ref ObjectId="70558"/>
    <cge:TPSR_Ref TObjectID="15853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70557">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3617.000000 -435.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15851" ObjectName="SW-CX_GBL.CX_GBL_061XC"/>
     <cge:Meas_Ref ObjectId="70557"/>
    <cge:TPSR_Ref TObjectID="15851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70557">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3617.000000 -357.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15852" ObjectName="SW-CX_GBL.CX_GBL_061XC1"/>
     <cge:Meas_Ref ObjectId="70557"/>
    <cge:TPSR_Ref TObjectID="15852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70550">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -345.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15843" ObjectName="SW-CX_GBL.CX_GBL_059XC1"/>
     <cge:Meas_Ref ObjectId="70550"/>
    <cge:TPSR_Ref TObjectID="15843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70550">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -423.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15842" ObjectName="SW-CX_GBL.CX_GBL_059XC"/>
     <cge:Meas_Ref ObjectId="70550"/>
    <cge:TPSR_Ref TObjectID="15842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70547">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -344.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15839" ObjectName="SW-CX_GBL.CX_GBL_058XC1"/>
     <cge:Meas_Ref ObjectId="70547"/>
    <cge:TPSR_Ref TObjectID="15839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70547">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -421.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15838" ObjectName="SW-CX_GBL.CX_GBL_058XC"/>
     <cge:Meas_Ref ObjectId="70547"/>
    <cge:TPSR_Ref TObjectID="15838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70544">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -344.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15835" ObjectName="SW-CX_GBL.CX_GBL_057XC1"/>
     <cge:Meas_Ref ObjectId="70544"/>
    <cge:TPSR_Ref TObjectID="15835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70544">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -421.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15834" ObjectName="SW-CX_GBL.CX_GBL_057XC"/>
     <cge:Meas_Ref ObjectId="70544"/>
    <cge:TPSR_Ref TObjectID="15834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70541">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -344.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15831" ObjectName="SW-CX_GBL.CX_GBL_056XC1"/>
     <cge:Meas_Ref ObjectId="70541"/>
    <cge:TPSR_Ref TObjectID="15831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70541">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -422.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15830" ObjectName="SW-CX_GBL.CX_GBL_056XC"/>
     <cge:Meas_Ref ObjectId="70541"/>
    <cge:TPSR_Ref TObjectID="15830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70538">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4395.000000 -344.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15827" ObjectName="SW-CX_GBL.CX_GBL_055XC1"/>
     <cge:Meas_Ref ObjectId="70538"/>
    <cge:TPSR_Ref TObjectID="15827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70538">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4395.000000 -423.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15826" ObjectName="SW-CX_GBL.CX_GBL_055XC"/>
     <cge:Meas_Ref ObjectId="70538"/>
    <cge:TPSR_Ref TObjectID="15826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70535">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4545.000000 -344.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15823" ObjectName="SW-CX_GBL.CX_GBL_054XC1"/>
     <cge:Meas_Ref ObjectId="70535"/>
    <cge:TPSR_Ref TObjectID="15823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70535">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4545.000000 -421.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15822" ObjectName="SW-CX_GBL.CX_GBL_054XC"/>
     <cge:Meas_Ref ObjectId="70535"/>
    <cge:TPSR_Ref TObjectID="15822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70532">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4695.000000 -345.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15819" ObjectName="SW-CX_GBL.CX_GBL_053XC1"/>
     <cge:Meas_Ref ObjectId="70532"/>
    <cge:TPSR_Ref TObjectID="15819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70532">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4695.000000 -418.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15818" ObjectName="SW-CX_GBL.CX_GBL_053XC"/>
     <cge:Meas_Ref ObjectId="70532"/>
    <cge:TPSR_Ref TObjectID="15818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70529">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -344.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15815" ObjectName="SW-CX_GBL.CX_GBL_052XC1"/>
     <cge:Meas_Ref ObjectId="70529"/>
    <cge:TPSR_Ref TObjectID="15815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70529">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -421.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15814" ObjectName="SW-CX_GBL.CX_GBL_052XC"/>
     <cge:Meas_Ref ObjectId="70529"/>
    <cge:TPSR_Ref TObjectID="15814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4995.000000 -345.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15811" ObjectName="SW-CX_GBL.CX_GBL_051XC1"/>
     <cge:Meas_Ref ObjectId="70526"/>
    <cge:TPSR_Ref TObjectID="15811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4995.000000 -422.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15810" ObjectName="SW-CX_GBL.CX_GBL_051XC"/>
     <cge:Meas_Ref ObjectId="70526"/>
    <cge:TPSR_Ref TObjectID="15810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70510">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4135.000000 -659.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15805" ObjectName="SW-CX_GBL.CX_GBL_1010SW"/>
     <cge:Meas_Ref ObjectId="70510"/>
    <cge:TPSR_Ref TObjectID="15805"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_GBL" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongganwanTgbl_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4264,-1121 4264,-1150 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18020" ObjectName="AC-110kV.yongganwanTgbl_line"/>
    <cge:TPSR_Ref TObjectID="18020_SS-111"/></metadata>
   <polyline fill="none" opacity="0" points="4264,-1121 4264,-1150 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_3df1140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-1010 4198,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15804@0" ObjectIDZND0="g_3cec890@0" Pin0InfoVect0LinkObjId="g_3cec890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-1010 4198,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_413a930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-743 4208,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="15857@x" ObjectIDND1="15805@x" ObjectIDZND0="g_3d8e650@0" Pin0InfoVect0LinkObjId="g_3d8e650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_418d6e0_0" Pin1InfoVect1LinkObjId="SW-70510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-743 4208,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_418d6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-743 4263,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_3d8e650@0" ObjectIDND1="15805@x" ObjectIDZND0="15857@x" Pin0InfoVect0LinkObjId="g_2365620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d8e650_0" Pin1InfoVect1LinkObjId="SW-70510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-743 4263,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d3870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-906 4199,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15803@0" ObjectIDZND0="g_3cec080@0" Pin0InfoVect0LinkObjId="g_3cec080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70508_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-906 4199,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3430750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-552 4012,-552 4012,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="lightningRod" ObjectIDND0="g_3f20a60@0" ObjectIDND1="15859@0" ObjectIDZND0="g_34e6440@0" Pin0InfoVect0LinkObjId="g_34e6440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f20a60_0" Pin1InfoVect1LinkObjId="g_35f07b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-552 4012,-552 4012,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43808f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-552 3966,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="lightningRod" ObjectIDND0="g_34e6440@0" ObjectIDND1="15859@0" ObjectIDZND0="g_3f20a60@1" Pin0InfoVect0LinkObjId="g_3f20a60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34e6440_0" Pin1InfoVect1LinkObjId="g_35f07b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-552 3966,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26825d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-605 3966,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3f20a60@0" ObjectIDZND0="g_41fb980@0" Pin0InfoVect0LinkObjId="g_41fb980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f20a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-605 3966,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-638 3824,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15846@0" ObjectIDZND0="g_3f7f910@0" Pin0InfoVect0LinkObjId="g_3f7f910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70553_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-638 3824,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f1de30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-638 3760,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_41e3b00@0" ObjectIDND1="15846@x" ObjectIDND2="15847@x" ObjectIDZND0="g_29c18e0@0" Pin0InfoVect0LinkObjId="g_29c18e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_41e3b00_0" Pin1InfoVect1LinkObjId="SW-70553_0" Pin1InfoVect2LinkObjId="SW-70554_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-638 3760,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aafcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3715,-656 3715,-638 3760,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_41e3b00@0" ObjectIDZND0="g_29c18e0@0" ObjectIDZND1="15846@x" ObjectIDZND2="15847@x" Pin0InfoVect0LinkObjId="g_29c18e0_0" Pin0InfoVect1LinkObjId="SW-70553_0" Pin0InfoVect2LinkObjId="SW-70554_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41e3b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3715,-656 3715,-638 3760,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_356c2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-718 3760,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_29c18e0@1" Pin0InfoVect0LinkObjId="g_29c18e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a8d160_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-718 3760,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23060d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-745 3797,-731 3760,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_261f9a0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="g_2a8d160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_261f9a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-745 3797,-731 3760,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36040b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3834,-325 3844,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15844@1" ObjectIDZND0="g_2ab0b60@0" Pin0InfoVect0LinkObjId="g_2ab0b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70551_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3834,-325 3844,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c8890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-252 3780,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_34b7dc0@1" ObjectIDZND0="43323@0" Pin0InfoVect0LinkObjId="SM-CX_GBL.P9_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b7dc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-252 3780,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ce7d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3682,-324 3692,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15855@1" ObjectIDZND0="g_435b0c0@0" Pin0InfoVect0LinkObjId="g_435b0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3682,-324 3692,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a806d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15855@x" ObjectIDND1="g_40e59e0@0" ObjectIDND2="15855@x" ObjectIDZND0="15855@x" ObjectIDZND1="g_40e59e0@0" ObjectIDZND2="15855@x" Pin0InfoVect0LinkObjId="SW-70560_0" Pin0InfoVect1LinkObjId="g_40e59e0_0" Pin0InfoVect2LinkObjId="SW-70560_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70560_0" Pin1InfoVect1LinkObjId="g_40e59e0_0" Pin1InfoVect2LinkObjId="SW-70560_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33d1230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-324 3627,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15855@x" ObjectIDND1="15855@x" ObjectIDND2="g_40e59e0@0" ObjectIDZND0="g_40e59e0@0" Pin0InfoVect0LinkObjId="g_40e59e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70560_0" Pin1InfoVect1LinkObjId="SW-70560_0" Pin1InfoVect2LinkObjId="g_40e59e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-324 3627,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32595a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-324 4007,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15840@1" ObjectIDZND0="g_33d0d50@0" Pin0InfoVect0LinkObjId="g_33d0d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70548_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-324 4007,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2381db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-324 3961,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2acc1d0@0" ObjectIDND1="15839@x" ObjectIDND2="g_41b4590@0" ObjectIDZND0="15840@0" Pin0InfoVect0LinkObjId="SW-70548_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2acc1d0_0" Pin1InfoVect1LinkObjId="SW-70547_0" Pin1InfoVect2LinkObjId="g_41b4590_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-324 3961,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34fc770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15840@x" ObjectIDND1="g_2acc1d0@0" ObjectIDND2="15839@x" ObjectIDZND0="15840@x" ObjectIDZND1="g_2acc1d0@0" ObjectIDZND2="15839@x" Pin0InfoVect0LinkObjId="SW-70548_0" Pin0InfoVect1LinkObjId="g_2acc1d0_0" Pin0InfoVect2LinkObjId="SW-70547_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70548_0" Pin1InfoVect1LinkObjId="g_2acc1d0_0" Pin1InfoVect2LinkObjId="SW-70547_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cc5c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-330 3902,-330 3902,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15839@x" ObjectIDND1="15840@x" ObjectIDND2="g_2acc1d0@0" ObjectIDZND0="g_41b4590@0" Pin0InfoVect0LinkObjId="g_41b4590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70547_0" Pin1InfoVect1LinkObjId="SW-70548_0" Pin1InfoVect2LinkObjId="g_2acc1d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-330 3902,-330 3902,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4463740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-324 3943,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15840@x" ObjectIDND1="15839@x" ObjectIDND2="g_41b4590@0" ObjectIDZND0="g_2acc1d0@0" Pin0InfoVect0LinkObjId="g_2acc1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70548_0" Pin1InfoVect1LinkObjId="SW-70547_0" Pin1InfoVect2LinkObjId="g_41b4590_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-324 3943,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_436dc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-324 4169,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15836@1" ObjectIDZND0="g_3feaa80@0" Pin0InfoVect0LinkObjId="g_3feaa80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70545_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-324 4169,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3272ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-324 4123,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_43acb20@0" ObjectIDND1="15835@x" ObjectIDND2="g_3654e00@0" ObjectIDZND0="15836@0" Pin0InfoVect0LinkObjId="SW-70545_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_43acb20_0" Pin1InfoVect1LinkObjId="SW-70544_0" Pin1InfoVect2LinkObjId="g_3654e00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-324 4123,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e14f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15836@x" ObjectIDND1="g_43acb20@0" ObjectIDND2="15835@x" ObjectIDZND0="15836@x" ObjectIDZND1="g_43acb20@0" ObjectIDZND2="15835@x" Pin0InfoVect0LinkObjId="SW-70545_0" Pin0InfoVect1LinkObjId="g_43acb20_0" Pin0InfoVect2LinkObjId="SW-70544_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70545_0" Pin1InfoVect1LinkObjId="g_43acb20_0" Pin1InfoVect2LinkObjId="SW-70544_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353b1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-330 4064,-330 4064,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15835@x" ObjectIDND1="15836@x" ObjectIDND2="g_43acb20@0" ObjectIDZND0="g_3654e00@0" Pin0InfoVect0LinkObjId="g_3654e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70544_0" Pin1InfoVect1LinkObjId="SW-70545_0" Pin1InfoVect2LinkObjId="g_43acb20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-330 4064,-330 4064,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34fc250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-324 4105,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15836@x" ObjectIDND1="15835@x" ObjectIDND2="g_3654e00@0" ObjectIDZND0="g_43acb20@0" Pin0InfoVect0LinkObjId="g_43acb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70545_0" Pin1InfoVect1LinkObjId="SW-70544_0" Pin1InfoVect2LinkObjId="g_3654e00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-324 4105,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3611170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-324 4319,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15832@1" ObjectIDZND0="g_3e3b460@0" Pin0InfoVect0LinkObjId="g_3e3b460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70542_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-324 4319,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e7870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-324 4273,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_4139a40@0" ObjectIDND1="g_3d92f20@0" ObjectIDND2="15831@x" ObjectIDZND0="15832@0" Pin0InfoVect0LinkObjId="SW-70542_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4139a40_0" Pin1InfoVect1LinkObjId="g_3d92f20_0" Pin1InfoVect2LinkObjId="SW-70541_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-324 4273,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4106860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="15832@x" ObjectIDND1="g_4139a40@0" ObjectIDND2="g_3d92f20@0" ObjectIDZND0="15832@x" ObjectIDZND1="g_4139a40@0" ObjectIDZND2="g_3d92f20@0" Pin0InfoVect0LinkObjId="SW-70542_0" Pin0InfoVect1LinkObjId="g_4139a40_0" Pin0InfoVect2LinkObjId="g_3d92f20_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70542_0" Pin1InfoVect1LinkObjId="g_4139a40_0" Pin1InfoVect2LinkObjId="g_3d92f20_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d92290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-324 4255,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="15832@x" ObjectIDND1="g_3d92f20@0" ObjectIDND2="15831@x" ObjectIDZND0="g_4139a40@0" Pin0InfoVect0LinkObjId="g_4139a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70542_0" Pin1InfoVect1LinkObjId="g_3d92f20_0" Pin1InfoVect2LinkObjId="SW-70541_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-324 4255,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_238cd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-324 4423,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_235b020@0" ObjectIDND1="15827@x" ObjectIDND2="g_29c0f40@0" ObjectIDZND0="15828@0" Pin0InfoVect0LinkObjId="SW-70539_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_235b020_0" Pin1InfoVect1LinkObjId="SW-70538_0" Pin1InfoVect2LinkObjId="g_29c0f40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-324 4423,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42863d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15828@x" ObjectIDND1="g_235b020@0" ObjectIDND2="15827@x" ObjectIDZND0="15828@x" ObjectIDZND1="g_235b020@0" ObjectIDZND2="15827@x" Pin0InfoVect0LinkObjId="SW-70539_0" Pin0InfoVect1LinkObjId="g_235b020_0" Pin0InfoVect2LinkObjId="SW-70538_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70539_0" Pin1InfoVect1LinkObjId="g_235b020_0" Pin1InfoVect2LinkObjId="SW-70538_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4101fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-330 4364,-330 4364,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15827@x" ObjectIDND1="15828@x" ObjectIDND2="g_235b020@0" ObjectIDZND0="g_29c0f40@0" Pin0InfoVect0LinkObjId="g_29c0f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70538_0" Pin1InfoVect1LinkObjId="SW-70539_0" Pin1InfoVect2LinkObjId="g_235b020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-330 4364,-330 4364,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29cbe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-324 4405,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15828@x" ObjectIDND1="15827@x" ObjectIDND2="g_29c0f40@0" ObjectIDZND0="g_235b020@0" Pin0InfoVect0LinkObjId="g_235b020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70539_0" Pin1InfoVect1LinkObjId="SW-70538_0" Pin1InfoVect2LinkObjId="g_29c0f40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-324 4405,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a786a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4609,-324 4619,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15824@1" ObjectIDZND0="g_2aba8d0@0" Pin0InfoVect0LinkObjId="g_2aba8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70536_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4609,-324 4619,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3476010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-324 4573,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_43a8bd0@0" ObjectIDND1="15823@x" ObjectIDND2="g_2ae2080@0" ObjectIDZND0="15824@0" Pin0InfoVect0LinkObjId="SW-70536_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_43a8bd0_0" Pin1InfoVect1LinkObjId="SW-70535_0" Pin1InfoVect2LinkObjId="g_2ae2080_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-324 4573,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2309be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15824@x" ObjectIDND1="g_43a8bd0@0" ObjectIDND2="15823@x" ObjectIDZND0="15824@x" ObjectIDZND1="g_43a8bd0@0" ObjectIDZND2="15823@x" Pin0InfoVect0LinkObjId="SW-70536_0" Pin0InfoVect1LinkObjId="g_43a8bd0_0" Pin0InfoVect2LinkObjId="SW-70535_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70536_0" Pin1InfoVect1LinkObjId="g_43a8bd0_0" Pin1InfoVect2LinkObjId="SW-70535_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3455410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-330 4514,-330 4514,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15823@x" ObjectIDND1="15824@x" ObjectIDND2="g_43a8bd0@0" ObjectIDZND0="g_2ae2080@0" Pin0InfoVect0LinkObjId="g_2ae2080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70535_0" Pin1InfoVect1LinkObjId="SW-70536_0" Pin1InfoVect2LinkObjId="g_43a8bd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-330 4514,-330 4514,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35dff30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-324 4555,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15824@x" ObjectIDND1="15823@x" ObjectIDND2="g_2ae2080@0" ObjectIDZND0="g_43a8bd0@0" Pin0InfoVect0LinkObjId="g_43a8bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70536_0" Pin1InfoVect1LinkObjId="SW-70535_0" Pin1InfoVect2LinkObjId="g_2ae2080_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-324 4555,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4068080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-325 4769,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15820@1" ObjectIDZND0="g_3d6eaa0@0" Pin0InfoVect0LinkObjId="g_3d6eaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70533_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-325 4769,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ebe8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-324 4919,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15816@1" ObjectIDZND0="g_34fcf60@0" Pin0InfoVect0LinkObjId="g_34fcf60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-324 4919,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a259f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-324 4873,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_29cc6d0@0" ObjectIDND1="g_3f7eba0@0" ObjectIDND2="15815@x" ObjectIDZND0="15816@0" Pin0InfoVect0LinkObjId="SW-70530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29cc6d0_0" Pin1InfoVect1LinkObjId="g_3f7eba0_0" Pin1InfoVect2LinkObjId="SW-70529_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-324 4873,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d739f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_29cc6d0@0" ObjectIDND1="15816@x" ObjectIDND2="g_3f7eba0@0" ObjectIDZND0="g_29cc6d0@0" ObjectIDZND1="15816@x" ObjectIDZND2="g_3f7eba0@0" Pin0InfoVect0LinkObjId="g_29cc6d0_0" Pin0InfoVect1LinkObjId="SW-70530_0" Pin0InfoVect2LinkObjId="g_3f7eba0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29cc6d0_0" Pin1InfoVect1LinkObjId="SW-70530_0" Pin1InfoVect2LinkObjId="g_3f7eba0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41abb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-330 4814,-330 4814,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="15815@x" ObjectIDND1="g_29cc6d0@0" ObjectIDND2="15816@x" ObjectIDZND0="g_3f7eba0@0" Pin0InfoVect0LinkObjId="g_3f7eba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70529_0" Pin1InfoVect1LinkObjId="g_29cc6d0_0" Pin1InfoVect2LinkObjId="SW-70530_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-330 4814,-330 4814,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_327b7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-324 4855,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="15816@x" ObjectIDND1="g_3f7eba0@0" ObjectIDND2="15815@x" ObjectIDZND0="g_29cc6d0@0" Pin0InfoVect0LinkObjId="g_29cc6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70530_0" Pin1InfoVect1LinkObjId="g_3f7eba0_0" Pin1InfoVect2LinkObjId="SW-70529_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-324 4855,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2657400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5059,-325 5069,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15812@1" ObjectIDZND0="g_3e71d90@0" Pin0InfoVect0LinkObjId="g_3e71d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70527_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5059,-325 5069,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3625130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-325 5023,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="15812@x" ObjectIDND1="g_3e47b20@0" ObjectIDND2="g_411b3e0@0" ObjectIDZND0="15812@0" Pin0InfoVect0LinkObjId="SW-70527_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70527_0" Pin1InfoVect1LinkObjId="g_3e47b20_0" Pin1InfoVect2LinkObjId="g_411b3e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-325 5023,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb9ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-325 5005,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="15812@x" ObjectIDND1="g_3e47b20@0" ObjectIDND2="g_411b3e0@0" ObjectIDZND0="15812@x" ObjectIDZND1="g_3e47b20@0" ObjectIDZND2="g_411b3e0@0" Pin0InfoVect0LinkObjId="SW-70527_0" Pin0InfoVect1LinkObjId="g_3e47b20_0" Pin0InfoVect2LinkObjId="g_411b3e0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70527_0" Pin1InfoVect1LinkObjId="g_3e47b20_0" Pin1InfoVect2LinkObjId="g_411b3e0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-325 5005,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c49e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-331 4964,-331 4964,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15812@x" ObjectIDND1="15812@x" ObjectIDND2="g_3e47b20@0" ObjectIDZND0="g_411b3e0@0" Pin0InfoVect0LinkObjId="g_411b3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70527_0" Pin1InfoVect1LinkObjId="SW-70527_0" Pin1InfoVect2LinkObjId="g_3e47b20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-331 4964,-331 4964,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41bdab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-325 5005,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15812@x" ObjectIDND1="15812@x" ObjectIDND2="g_3e47b20@0" ObjectIDZND0="g_3e47b20@0" Pin0InfoVect0LinkObjId="g_3e47b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70527_0" Pin1InfoVect1LinkObjId="SW-70527_0" Pin1InfoVect2LinkObjId="g_3e47b20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-325 5005,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42a83b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-638 3778,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_29c18e0@0" ObjectIDND1="g_41e3b00@0" ObjectIDND2="15847@x" ObjectIDZND0="15846@1" Pin0InfoVect0LinkObjId="SW-70553_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29c18e0_0" Pin1InfoVect1LinkObjId="g_41e3b00_0" Pin1InfoVect2LinkObjId="SW-70554_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-638 3778,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dc7730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-735 4756,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3455000@1" ObjectIDZND0="g_2a66330@1" Pin0InfoVect0LinkObjId="g_2a66330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3455000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-735 4756,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36d7540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-659 4756,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2a66330@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a8d160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a66330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-659 4756,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2acd340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4658,-832 4755,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDZND0="g_410fb00@0" ObjectIDZND1="g_3455000@0" Pin0InfoVect0LinkObjId="g_410fb00_0" Pin0InfoVect1LinkObjId="g_3455000_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4658,-832 4755,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e733d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-832 4855,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_410fb00@0" ObjectIDND1="g_3455000@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_410fb00_0" Pin1InfoVect1LinkObjId="g_3455000_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-832 4855,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dce500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-785 4806,-804 4755,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_410fb00@0" ObjectIDZND0="g_3455000@0" Pin0InfoVect0LinkObjId="g_3455000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_410fb00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-785 4806,-804 4755,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_263c390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-780 4755,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3455000@0" ObjectIDZND0="g_410fb00@0" Pin0InfoVect0LinkObjId="g_410fb00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3455000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-780 4755,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f7f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-804 4755,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_410fb00@0" ObjectIDND1="g_3455000@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_410fb00_0" Pin1InfoVect1LinkObjId="g_3455000_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-804 4755,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_358d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-534 4264,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15808@0" ObjectIDZND0="15806@0" Pin0InfoVect0LinkObjId="SW-70511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-534 4264,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_358d520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-581 4264,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15806@1" ObjectIDZND0="15807@1" Pin0InfoVect0LinkObjId="SW-70512_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-581 4264,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35f07b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-516 4264,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15808@1" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_264e590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70512_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-516 4264,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_264e590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-552 3966,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="busSection" ObjectIDND0="g_3f20a60@0" ObjectIDND1="g_34e6440@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f20a60_0" Pin1InfoVect1LinkObjId="g_34e6440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-552 3966,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3392e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4459,-324 4472,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15828@1" ObjectIDZND0="g_2654950@0" Pin0InfoVect0LinkObjId="g_2654950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70539_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4459,-324 4472,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fb8960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-859 4264,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="15801@1" ObjectIDZND0="15803@x" ObjectIDZND1="15802@x" Pin0InfoVect0LinkObjId="SW-70508_0" Pin0InfoVect1LinkObjId="SW-70507_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70506_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-859 4264,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35c65e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-906 4264,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="15803@1" ObjectIDZND0="15801@x" ObjectIDZND1="15802@x" Pin0InfoVect0LinkObjId="SW-70506_0" Pin0InfoVect1LinkObjId="SW-70507_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70508_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-906 4264,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35c67d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-906 4264,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="15803@x" ObjectIDND1="15801@x" ObjectIDZND0="15802@0" Pin0InfoVect0LinkObjId="SW-70507_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-70508_0" Pin1InfoVect1LinkObjId="SW-70506_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-906 4264,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2359a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-697 4264,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="15857@1" ObjectIDZND0="g_2a78980@0" ObjectIDZND1="15807@x" Pin0InfoVect0LinkObjId="g_2a78980_0" Pin0InfoVect1LinkObjId="SW-70512_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_418d6e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-697 4264,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2365620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4313,-635 4313,-651 4264,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_2a78980@0" ObjectIDZND0="15857@x" ObjectIDZND1="15807@x" Pin0InfoVect0LinkObjId="g_418d6e0_0" Pin0InfoVect1LinkObjId="SW-70512_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a78980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4313,-635 4313,-651 4264,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2365880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-651 4264,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_2a78980@0" ObjectIDND1="15857@x" ObjectIDZND0="15807@0" Pin0InfoVect0LinkObjId="SW-70512_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a78980_0" Pin1InfoVect1LinkObjId="g_418d6e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-651 4264,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d72770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-324 3646,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_40e59e0@0" ObjectIDND1="15855@x" ObjectIDND2="g_40e59e0@0" ObjectIDZND0="15855@0" Pin0InfoVect0LinkObjId="SW-70560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40e59e0_0" Pin1InfoVect1LinkObjId="SW-70560_0" Pin1InfoVect2LinkObjId="g_40e59e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-324 3646,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d729a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-330 3587,-330 3587,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15852@x" ObjectIDND1="15855@x" ObjectIDND2="g_40e59e0@0" ObjectIDZND0="g_3cf1580@0" Pin0InfoVect0LinkObjId="g_3cf1580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70557_0" Pin1InfoVect1LinkObjId="SW-70560_0" Pin1InfoVect2LinkObjId="g_40e59e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-330 3587,-330 3587,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4079230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-576 3760,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15849@1" ObjectIDZND0="15847@1" Pin0InfoVect0LinkObjId="SW-70554_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70555_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-576 3760,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4079490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-610 3760,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15847@0" ObjectIDZND0="g_29c18e0@0" ObjectIDZND1="g_41e3b00@0" ObjectIDZND2="15846@x" Pin0InfoVect0LinkObjId="g_29c18e0_0" Pin0InfoVect1LinkObjId="g_41e3b00_0" Pin0InfoVect2LinkObjId="SW-70553_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70554_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-610 3760,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ec13c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-482 3760,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="15859@0" ObjectIDZND0="15848@0" Pin0InfoVect0LinkObjId="SW-70554_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f07b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-482 3760,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ec1620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-528 3760,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15848@1" ObjectIDZND0="15849@0" Pin0InfoVect0LinkObjId="SW-70555_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70554_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-528 3760,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41110d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-231 3627,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_40e59e0@1" ObjectIDZND0="15854@1" Pin0InfoVect0LinkObjId="SW-70559_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40e59e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-231 3627,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43800b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-161 3627,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="15853@0" ObjectIDZND0="g_264bcc0@0" ObjectIDZND1="15854@x" Pin0InfoVect0LinkObjId="g_264bcc0_0" Pin0InfoVect1LinkObjId="SW-70559_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70558_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-161 3627,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f131d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-184 3627,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="15854@0" ObjectIDZND0="15853@x" ObjectIDZND1="g_264bcc0@0" Pin0InfoVect0LinkObjId="SW-70558_0" Pin0InfoVect1LinkObjId="g_264bcc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70559_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-184 3627,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f13430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-161 3627,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="15853@x" ObjectIDND1="15854@x" ObjectIDZND0="g_264bcc0@0" Pin0InfoVect0LinkObjId="g_264bcc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-70558_0" Pin1InfoVect1LinkObjId="SW-70559_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-161 3627,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ea10c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-381 3627,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15852@1" ObjectIDZND0="15850@0" Pin0InfoVect0LinkObjId="SW-70556_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70557_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-381 3627,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ea1320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-423 3627,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15850@1" ObjectIDZND0="15851@1" Pin0InfoVect0LinkObjId="SW-70557_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70556_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-423 3627,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_260da30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-459 3627,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15851@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-459 3627,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_447b820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-369 5005,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15811@1" ObjectIDZND0="15809@0" Pin0InfoVect0LinkObjId="SW-70525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70526_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-369 5005,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_447ba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-410 5005,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15809@1" ObjectIDZND0="15810@1" Pin0InfoVect0LinkObjId="SW-70526_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-410 5005,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb54c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-446 5005,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15810@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-446 5005,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb5720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-368 4855,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15815@1" ObjectIDZND0="15813@0" Pin0InfoVect0LinkObjId="SW-70528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70529_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-368 4855,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb5980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-409 4855,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15813@1" ObjectIDZND0="15814@1" Pin0InfoVect0LinkObjId="SW-70529_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70528_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-409 4855,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb5be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-445 4855,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15814@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70529_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-445 4855,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb5e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-369 4705,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15819@1" ObjectIDZND0="15817@0" Pin0InfoVect0LinkObjId="SW-70531_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70532_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-369 4705,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb60a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-406 4705,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15817@1" ObjectIDZND0="15818@1" Pin0InfoVect0LinkObjId="SW-70532_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70531_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-406 4705,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb6300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-442 4705,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15818@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70532_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-442 4705,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb6560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-368 4555,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15823@1" ObjectIDZND0="15821@0" Pin0InfoVect0LinkObjId="SW-70534_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70535_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-368 4555,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb67c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-409 4555,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15821@1" ObjectIDZND0="15822@1" Pin0InfoVect0LinkObjId="SW-70535_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70534_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-409 4555,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da28a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-445 4555,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15822@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70535_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-445 4555,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da2ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-368 4405,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15827@1" ObjectIDZND0="15825@0" Pin0InfoVect0LinkObjId="SW-70537_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70538_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-368 4405,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da2d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-411 4405,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15825@1" ObjectIDZND0="15826@1" Pin0InfoVect0LinkObjId="SW-70538_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70537_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-411 4405,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da2f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-447 4405,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15826@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70538_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-447 4405,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da31f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-410 4255,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15829@1" ObjectIDZND0="15830@1" Pin0InfoVect0LinkObjId="SW-70541_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-410 4255,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da3450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-446 4255,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15830@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70541_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-446 4255,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da36b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-368 4105,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15835@1" ObjectIDZND0="15833@0" Pin0InfoVect0LinkObjId="SW-70543_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70544_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-368 4105,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da3910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-409 4105,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15833@1" ObjectIDZND0="15834@1" Pin0InfoVect0LinkObjId="SW-70544_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70543_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-409 4105,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da3b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-445 4105,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15834@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70544_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-445 4105,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_368b5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-368 3943,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15839@1" ObjectIDZND0="15837@0" Pin0InfoVect0LinkObjId="SW-70546_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70547_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-368 3943,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_368b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-409 3943,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15837@1" ObjectIDZND0="15838@1" Pin0InfoVect0LinkObjId="SW-70547_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70546_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-409 3943,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_368ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-445 3943,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15838@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70547_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-445 3943,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e08860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-369 3780,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15843@1" ObjectIDZND0="15841@0" Pin0InfoVect0LinkObjId="SW-70549_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70550_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-369 3780,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e08ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-411 3780,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15841@1" ObjectIDZND0="15842@1" Pin0InfoVect0LinkObjId="SW-70550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70549_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-411 3780,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35e2e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-447 3780,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15842@0" ObjectIDZND0="15859@0" Pin0InfoVect0LinkObjId="g_35f07b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-447 3780,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f66b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3739,-312 3739,-331 3780,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2b46500@0" ObjectIDZND0="15843@x" ObjectIDZND1="g_34b7dc0@0" ObjectIDZND2="15844@x" Pin0InfoVect0LinkObjId="SW-70550_0" Pin0InfoVect1LinkObjId="g_34b7dc0_0" Pin0InfoVect2LinkObjId="SW-70551_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b46500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3739,-312 3739,-331 3780,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_421ae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-325 3780,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15844@x" ObjectIDND1="15843@x" ObjectIDND2="g_2b46500@0" ObjectIDZND0="g_34b7dc0@0" Pin0InfoVect0LinkObjId="g_34b7dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70551_0" Pin1InfoVect1LinkObjId="SW-70550_0" Pin1InfoVect2LinkObjId="g_2b46500_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-325 3780,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_421b000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-325 3780,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="15844@0" ObjectIDZND0="g_34b7dc0@0" ObjectIDZND1="15843@x" ObjectIDZND2="g_2b46500@0" Pin0InfoVect0LinkObjId="g_34b7dc0_0" Pin0InfoVect1LinkObjId="SW-70550_0" Pin0InfoVect2LinkObjId="g_2b46500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70551_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-325 3780,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_421b210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-324 3627,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15855@x" ObjectIDND1="g_40e59e0@0" ObjectIDND2="15855@x" ObjectIDZND0="15855@x" ObjectIDZND1="g_40e59e0@0" ObjectIDZND2="15855@x" Pin0InfoVect0LinkObjId="SW-70560_0" Pin0InfoVect1LinkObjId="g_40e59e0_0" Pin0InfoVect2LinkObjId="SW-70560_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70560_0" Pin1InfoVect1LinkObjId="g_40e59e0_0" Pin1InfoVect2LinkObjId="SW-70560_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-324 3627,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4337540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-324 3943,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="15840@x" ObjectIDND1="g_2acc1d0@0" ObjectIDND2="15840@x" ObjectIDZND0="15839@x" ObjectIDZND1="g_41b4590@0" Pin0InfoVect0LinkObjId="SW-70547_0" Pin0InfoVect1LinkObjId="g_41b4590_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70548_0" Pin1InfoVect1LinkObjId="g_2acc1d0_0" Pin1InfoVect2LinkObjId="SW-70548_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-324 3943,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43377a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-330 3943,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_41b4590@0" ObjectIDND1="15840@x" ObjectIDND2="g_2acc1d0@0" ObjectIDZND0="15839@0" Pin0InfoVect0LinkObjId="SW-70547_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_41b4590_0" Pin1InfoVect1LinkObjId="SW-70548_0" Pin1InfoVect2LinkObjId="g_2acc1d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-330 3943,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4338150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-324 4105,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="15836@x" ObjectIDND1="g_43acb20@0" ObjectIDND2="15836@x" ObjectIDZND0="15835@x" ObjectIDZND1="g_3654e00@0" Pin0InfoVect0LinkObjId="SW-70544_0" Pin0InfoVect1LinkObjId="g_3654e00_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70545_0" Pin1InfoVect1LinkObjId="g_43acb20_0" Pin1InfoVect2LinkObjId="SW-70545_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-324 4105,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43383b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-330 4105,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3654e00@0" ObjectIDND1="15836@x" ObjectIDND2="g_43acb20@0" ObjectIDZND0="15835@0" Pin0InfoVect0LinkObjId="SW-70544_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3654e00_0" Pin1InfoVect1LinkObjId="SW-70545_0" Pin1InfoVect2LinkObjId="g_43acb20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-330 4105,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f968b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-324 4405,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="15828@x" ObjectIDND1="g_235b020@0" ObjectIDND2="15828@x" ObjectIDZND0="15827@x" ObjectIDZND1="g_29c0f40@0" Pin0InfoVect0LinkObjId="SW-70538_0" Pin0InfoVect1LinkObjId="g_29c0f40_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70539_0" Pin1InfoVect1LinkObjId="g_235b020_0" Pin1InfoVect2LinkObjId="SW-70539_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-324 4405,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f96b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-330 4405,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_29c0f40@0" ObjectIDND1="15828@x" ObjectIDND2="g_235b020@0" ObjectIDZND0="15827@0" Pin0InfoVect0LinkObjId="SW-70538_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29c0f40_0" Pin1InfoVect1LinkObjId="SW-70539_0" Pin1InfoVect2LinkObjId="g_235b020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-330 4405,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f974c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-324 4555,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="15824@x" ObjectIDND1="g_43a8bd0@0" ObjectIDND2="15824@x" ObjectIDZND0="15823@x" ObjectIDZND1="g_2ae2080@0" Pin0InfoVect0LinkObjId="SW-70535_0" Pin0InfoVect1LinkObjId="g_2ae2080_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70536_0" Pin1InfoVect1LinkObjId="g_43a8bd0_0" Pin1InfoVect2LinkObjId="SW-70536_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-324 4555,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f97720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-330 4555,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ae2080@0" ObjectIDND1="15824@x" ObjectIDND2="g_43a8bd0@0" ObjectIDZND0="15823@0" Pin0InfoVect0LinkObjId="SW-70535_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ae2080_0" Pin1InfoVect1LinkObjId="SW-70536_0" Pin1InfoVect2LinkObjId="g_43a8bd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-330 4555,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ace550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-323 3627,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="15855@x" ObjectIDND1="g_40e59e0@0" ObjectIDND2="15855@x" ObjectIDZND0="15852@x" ObjectIDZND1="g_3cf1580@0" Pin0InfoVect0LinkObjId="SW-70557_0" Pin0InfoVect1LinkObjId="g_3cf1580_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70560_0" Pin1InfoVect1LinkObjId="g_40e59e0_0" Pin1InfoVect2LinkObjId="SW-70560_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-323 3627,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ace7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-330 3627,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3cf1580@0" ObjectIDND1="15855@x" ObjectIDND2="g_40e59e0@0" ObjectIDZND0="15852@0" Pin0InfoVect0LinkObjId="SW-70557_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3cf1580_0" Pin1InfoVect1LinkObjId="SW-70560_0" Pin1InfoVect2LinkObjId="g_40e59e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-330 3627,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2acf2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-352 3780,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="15843@0" ObjectIDZND0="g_34b7dc0@0" ObjectIDZND1="15844@x" ObjectIDZND2="g_2b46500@0" Pin0InfoVect0LinkObjId="g_34b7dc0_0" Pin0InfoVect1LinkObjId="SW-70551_0" Pin0InfoVect2LinkObjId="g_2b46500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-352 3780,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36319b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-331 3780,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="15843@x" ObjectIDND1="g_2b46500@0" ObjectIDZND0="g_34b7dc0@0" ObjectIDZND1="15844@x" Pin0InfoVect0LinkObjId="g_34b7dc0_0" Pin0InfoVect1LinkObjId="SW-70551_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-70550_0" Pin1InfoVect1LinkObjId="g_2b46500_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-331 3780,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36324a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-325 5005,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="15812@x" ObjectIDND1="15812@x" ObjectIDND2="g_3e47b20@0" ObjectIDZND0="g_411b3e0@0" ObjectIDZND1="15811@x" Pin0InfoVect0LinkObjId="g_411b3e0_0" Pin0InfoVect1LinkObjId="SW-70526_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70527_0" Pin1InfoVect1LinkObjId="SW-70527_0" Pin1InfoVect2LinkObjId="g_3e47b20_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-325 5005,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3632700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-331 5005,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_411b3e0@0" ObjectIDND1="15812@x" ObjectIDND2="15812@x" ObjectIDZND0="15811@0" Pin0InfoVect0LinkObjId="SW-70526_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_411b3e0_0" Pin1InfoVect1LinkObjId="SW-70527_0" Pin1InfoVect2LinkObjId="SW-70527_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-331 5005,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e66870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-324 4855,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_29cc6d0@0" ObjectIDND1="15816@x" ObjectIDND2="g_29cc6d0@0" ObjectIDZND0="g_3f7eba0@0" ObjectIDZND1="15815@x" Pin0InfoVect0LinkObjId="g_3f7eba0_0" Pin0InfoVect1LinkObjId="SW-70529_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29cc6d0_0" Pin1InfoVect1LinkObjId="SW-70530_0" Pin1InfoVect2LinkObjId="g_29cc6d0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-324 4855,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e66ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-330 4855,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3f7eba0@0" ObjectIDND1="g_29cc6d0@0" ObjectIDND2="15816@x" ObjectIDZND0="15815@0" Pin0InfoVect0LinkObjId="SW-70529_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f7eba0_0" Pin1InfoVect1LinkObjId="g_29cc6d0_0" Pin1InfoVect2LinkObjId="SW-70530_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-330 4855,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e66d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-324 4705,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="15820@x" ObjectIDND1="g_2abcc20@0" ObjectIDND2="15819@x" ObjectIDZND0="g_358e240@0" Pin0InfoVect0LinkObjId="g_358e240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70533_0" Pin1InfoVect1LinkObjId="g_2abcc20_0" Pin1InfoVect2LinkObjId="SW-70532_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-324 4705,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e66f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-331 4705,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2abcc20@0" ObjectIDND1="15819@x" ObjectIDZND0="g_358e240@0" ObjectIDZND1="15820@x" Pin0InfoVect0LinkObjId="g_358e240_0" Pin0InfoVect1LinkObjId="SW-70533_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2abcc20_0" Pin1InfoVect1LinkObjId="SW-70532_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-331 4705,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e671f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-325 4723,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_358e240@0" ObjectIDND1="g_2abcc20@0" ObjectIDND2="15819@x" ObjectIDZND0="15820@0" Pin0InfoVect0LinkObjId="SW-70533_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_358e240_0" Pin1InfoVect1LinkObjId="g_2abcc20_0" Pin1InfoVect2LinkObjId="SW-70532_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-325 4723,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e67450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-251 3943,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2acc1d0@1" ObjectIDZND0="43322@0" Pin0InfoVect0LinkObjId="SM-CX_GBL.P8_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2acc1d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-251 3943,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e676b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-251 4105,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_43acb20@1" ObjectIDZND0="43321@0" Pin0InfoVect0LinkObjId="SM-CX_GBL.P7_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43acb20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-251 4105,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e67910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-252 4255,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_4139a40@1" ObjectIDZND0="43320@0" Pin0InfoVect0LinkObjId="SM-CX_GBL.P6_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4139a40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-252 4255,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fa530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-252 4405,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_235b020@1" ObjectIDZND0="43319@0" Pin0InfoVect0LinkObjId="SM-CX_GBL.P5_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_235b020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-252 4405,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fa790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-251 4555,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_43a8bd0@1" ObjectIDZND0="43318@0" Pin0InfoVect0LinkObjId="SM-CX_GBL.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43a8bd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-251 4555,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fa9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-251 4705,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_358e240@1" ObjectIDZND0="43317@0" Pin0InfoVect0LinkObjId="SM-CX_GBL.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_358e240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-251 4705,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-251 4855,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_29cc6d0@1" ObjectIDZND0="43316@0" Pin0InfoVect0LinkObjId="SM-CX_GBL.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29cc6d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-251 4855,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41faeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-252 5005,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_3e47b20@1" ObjectIDZND0="43315@0" Pin0InfoVect0LinkObjId="SM-CX_GBL.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e47b20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-252 5005,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36655b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4664,-311 4664,-330 4705,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2abcc20@0" ObjectIDZND0="g_358e240@0" ObjectIDZND1="15820@x" ObjectIDZND2="15819@x" Pin0InfoVect0LinkObjId="g_358e240_0" Pin0InfoVect1LinkObjId="SW-70533_0" Pin0InfoVect2LinkObjId="SW-70532_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2abcc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4664,-311 4664,-330 4705,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3665810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-330 4705,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2abcc20@0" ObjectIDND1="g_358e240@0" ObjectIDND2="15820@x" ObjectIDZND0="15819@0" Pin0InfoVect0LinkObjId="SW-70532_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2abcc20_0" Pin1InfoVect1LinkObjId="g_358e240_0" Pin1InfoVect2LinkObjId="SW-70533_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-330 4705,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4260140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4214,-311 4214,-330 4255,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3d92f20@0" ObjectIDZND0="15831@x" ObjectIDZND1="15832@x" ObjectIDZND2="g_4139a40@0" Pin0InfoVect0LinkObjId="SW-70541_0" Pin0InfoVect1LinkObjId="SW-70542_0" Pin0InfoVect2LinkObjId="g_4139a40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d92f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4214,-311 4214,-330 4255,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42603a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-330 4255,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3d92f20@0" ObjectIDND1="15831@x" ObjectIDZND0="15832@x" ObjectIDZND1="g_4139a40@0" ObjectIDZND2="15832@x" Pin0InfoVect0LinkObjId="SW-70542_0" Pin0InfoVect1LinkObjId="g_4139a40_0" Pin0InfoVect2LinkObjId="SW-70542_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d92f20_0" Pin1InfoVect1LinkObjId="SW-70541_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-330 4255,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4260600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-330 4255,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3d92f20@0" ObjectIDND1="15832@x" ObjectIDND2="g_4139a40@0" ObjectIDZND0="15831@0" Pin0InfoVect0LinkObjId="SW-70541_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d92f20_0" Pin1InfoVect1LinkObjId="SW-70542_0" Pin1InfoVect2LinkObjId="g_4139a40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-330 4255,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4260860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-368 4255,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15831@1" ObjectIDZND0="15829@0" Pin0InfoVect0LinkObjId="SW-70540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70541_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-368 4255,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_448bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4166,-650 4166,-664 4150,-664 4150,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3623230@0" ObjectIDZND0="15805@0" Pin0InfoVect0LinkObjId="SW-70510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3623230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4166,-650 4166,-664 4150,-664 4150,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_448c020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4150,-716 4150,-743 4208,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="15805@1" ObjectIDZND0="g_3d8e650@0" ObjectIDZND1="15857@x" Pin0InfoVect0LinkObjId="g_3d8e650_0" Pin0InfoVect1LinkObjId="g_418d6e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4150,-716 4150,-743 4208,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34022d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4244,-1010 4264,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="15804@1" ObjectIDZND0="15802@x" ObjectIDZND1="g_2a8d160@0" ObjectIDZND2="g_46b4f70@0" Pin0InfoVect0LinkObjId="SW-70507_0" Pin0InfoVect1LinkObjId="g_2a8d160_0" Pin0InfoVect2LinkObjId="g_46b4f70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4244,-1010 4264,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3402c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-994 4264,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="15802@1" ObjectIDZND0="15804@x" ObjectIDZND1="g_2a8d160@0" ObjectIDZND2="g_46b4f70@0" Pin0InfoVect0LinkObjId="SW-70509_0" Pin0InfoVect1LinkObjId="g_2a8d160_0" Pin0InfoVect2LinkObjId="g_46b4f70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70507_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-994 4264,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3402e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4230,-1102 4264,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2a8d160@0" ObjectIDZND0="15804@x" ObjectIDZND1="15802@x" ObjectIDZND2="g_46b4f70@0" Pin0InfoVect0LinkObjId="SW-70509_0" Pin0InfoVect1LinkObjId="SW-70507_0" Pin0InfoVect2LinkObjId="g_46b4f70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a8d160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4230,-1102 4264,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3373d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-1010 4264,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="15804@x" ObjectIDND1="15802@x" ObjectIDZND0="g_2a8d160@0" ObjectIDZND1="g_46b4f70@0" ObjectIDZND2="18020@1" Pin0InfoVect0LinkObjId="g_2a8d160_0" Pin0InfoVect1LinkObjId="g_46b4f70_0" Pin0InfoVect2LinkObjId="g_3373f90_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-70509_0" Pin1InfoVect1LinkObjId="SW-70507_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-1010 4264,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3373f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-1102 4264,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2a8d160@0" ObjectIDND1="15804@x" ObjectIDND2="15802@x" ObjectIDZND0="18020@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a8d160_0" Pin1InfoVect1LinkObjId="SW-70509_0" Pin1InfoVect2LinkObjId="SW-70507_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-1102 4264,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33741f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-1102 4301,-1102 4301,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a8d160@0" ObjectIDND1="15804@x" ObjectIDND2="15802@x" ObjectIDZND0="g_46b4f70@0" Pin0InfoVect0LinkObjId="g_46b4f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a8d160_0" Pin1InfoVect1LinkObjId="SW-70509_0" Pin1InfoVect2LinkObjId="SW-70507_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-1102 4301,-1102 4301,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db4a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-781 4264,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="47739@0" ObjectIDZND0="15857@0" Pin0InfoVect0LinkObjId="g_418d6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-781 4264,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db4ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-790 4264,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="47739@1" ObjectIDZND0="47738@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-790 4264,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db5710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-813 4264,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="47738@0" ObjectIDZND0="15801@0" Pin0InfoVect0LinkObjId="SW-70506_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3db4ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-813 4264,-832 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-58567" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3418.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11142" ObjectName="DYN-CX_GBL"/>
     <cge:Meas_Ref ObjectId="58567"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3530e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 866.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_466f080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 851.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45770e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4337.000000 836.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3551a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4335.000000 588.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3551cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 573.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2605050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4349.000000 558.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2605350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3580.000000 74.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b7740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3569.000000 59.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b7950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 44.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -11.222222 -9.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e40b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e4240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 50.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b57080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 35.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 151.555556 -9.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_328ab30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_328ada0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 50.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_328afe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 35.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 313.333333 -9.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_328b310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_328b5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 50.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_328b7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 35.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 463.111111 -9.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3692c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3692e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 50.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36930c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 35.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.888889 -9.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36933f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3693690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 50.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36938d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 35.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.666667 -9.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3693c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3693ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 50.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e7120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 35.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 913.444444 -9.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e7450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e76f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 50.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e7930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 35.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.222222 -9.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e7c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e7f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 50.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e8140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 35.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e8470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4957.000000 74.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cebc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 59.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cebe40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4971.000000 44.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_GBL.P1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5000.000000 -196.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43315" ObjectName="SM-CX_GBL.P1"/>
    <cge:TPSR_Ref TObjectID="43315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_GBL.P2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.000000 -194.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43316" ObjectName="SM-CX_GBL.P2"/>
    <cge:TPSR_Ref TObjectID="43316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_GBL.P3">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -194.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43317" ObjectName="SM-CX_GBL.P3"/>
    <cge:TPSR_Ref TObjectID="43317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_GBL.P4">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 -194.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43318" ObjectName="SM-CX_GBL.P4"/>
    <cge:TPSR_Ref TObjectID="43318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_GBL.P5">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.000000 -195.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43319" ObjectName="SM-CX_GBL.P5"/>
    <cge:TPSR_Ref TObjectID="43319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_GBL.P6">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -195.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43320" ObjectName="SM-CX_GBL.P6"/>
    <cge:TPSR_Ref TObjectID="43320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_GBL.P7">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -194.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43321" ObjectName="SM-CX_GBL.P7"/>
    <cge:TPSR_Ref TObjectID="43321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_GBL.P8">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -195.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43322" ObjectName="SM-CX_GBL.P8"/>
    <cge:TPSR_Ref TObjectID="43322"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_GBL.P9">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 -197.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43323" ObjectName="SM-CX_GBL.P9"/>
    <cge:TPSR_Ref TObjectID="43323"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="4264" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="3966" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="3760" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="3627" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="5005" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="4855" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="4705" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="4555" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="4405" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="4255" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="4105" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="3943" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="15859" cx="3780" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47738" cx="4264" cy="-813" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47738" cx="4263" cy="-813" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3086" y="-626"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32fad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f5ab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3ea1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3272.000000 -1165.500000) translate(0,16)">干巴拉升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_264e7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -1176.000000) translate(0,15)">永干万线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d76d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4665.000000 -872.000000) translate(0,15)">35kV维的变10kV的鲁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29dd210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -852.000000) translate(0,15)">N60</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29dd440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4619.000000 -614.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d76330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,15)">2号站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d76330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,33)">S11-M-200/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d76330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,51)">200kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d76330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,69)">10±5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d76330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,87)">D,YN11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d76330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,105)">U%=4.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d765b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -844.000000) translate(0,15)">1号接地站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aa84a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -775.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3811.000000 -761.000000) translate(0,15)">10kV消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af8ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.000000 -700.000000) translate(0,15)">10kV汇流段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af8ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.000000 -700.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e199b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -788.000000) translate(0,15)">1号接地站用变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e199b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -788.000000) translate(0,33)">DKSC-630/10.5-250/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e199b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -788.000000) translate(0,51)">10.5±5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e199b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -788.000000) translate(0,69)">ZN,YN11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e199b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -788.000000) translate(0,87)">U%=4.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e199b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -788.000000) translate(0,105)">1号消弧线圈参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e199b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -788.000000) translate(0,123)">XHDCZ-315/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e199b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -788.000000) translate(0,141)">10.5/1.732kV,315kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26820b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -765.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26820b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -765.000000) translate(0,33)">SFZ11-50000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26820b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -765.000000) translate(0,51)">121±8х1.25%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26820b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -765.000000) translate(0,69)">50000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26820b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -765.000000) translate(0,87)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26820b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -765.000000) translate(0,105)">U%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_367ae60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3707.000000 -182.000000) translate(0,15)">    9号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_367ae60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3707.000000 -182.000000) translate(0,33)">（41-45号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_367ae60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3707.000000 -182.000000) translate(0,51)">    5х1.11MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e2e6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -181.000000) translate(0,15)">    8号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e2e6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -181.000000) translate(0,33)">（36-40号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e2e6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -181.000000) translate(0,51)">    5х1.11MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e2e900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4032.000000 -181.000000) translate(0,15)">    7号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e2e900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4032.000000 -181.000000) translate(0,33)">（31-35号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e2e900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4032.000000 -181.000000) translate(0,51)">    5х1.11MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26817a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4182.000000 -181.000000) translate(0,15)">    6号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26817a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4182.000000 -181.000000) translate(0,33)">（26-30号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26817a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4182.000000 -181.000000) translate(0,51)">    5х1.11MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2681a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4333.000000 -181.000000) translate(0,15)">    5号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2681a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4333.000000 -181.000000) translate(0,33)">（21-25号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2681a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4333.000000 -181.000000) translate(0,51)">    5х1.11MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ab4390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -181.000000) translate(0,15)">    4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ab4390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -181.000000) translate(0,33)">（16-20号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ab4390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -181.000000) translate(0,51)">    5х1.11MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ab45c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4635.000000 -181.000000) translate(0,15)">    3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ab45c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4635.000000 -181.000000) translate(0,33)">（11-15号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ab45c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4635.000000 -181.000000) translate(0,51)">    5х1.1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4785.000000 -181.000000) translate(0,15)">   2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4785.000000 -181.000000) translate(0,33)">（6-10号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4785.000000 -181.000000) translate(0,51)">   5х1.11MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4937.000000 -181.000000) translate(0,15)">   1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4937.000000 -181.000000) translate(0,33)">（1-5号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4937.000000 -181.000000) translate(0,51)">   5х1.11MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_367a660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3544.000000 -118.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_367a660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3544.000000 -118.000000) translate(0,33)">      11MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f5fe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -852.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f60060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -931.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34df120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -575.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34df360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5017.000000 -405.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad3b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5021.000000 -351.000000) translate(0,12)">05117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad3dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4867.000000 -404.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4466250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.000000 -350.000000) translate(0,12)">05217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4466490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4717.000000 -405.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a46370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -351.000000) translate(0,12)">05317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a46580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4567.000000 -404.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2654740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4571.000000 -350.000000) translate(0,12)">05417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42542d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4417.000000 -404.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29cb000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.000000 -350.000000) translate(0,12)">05517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29cb1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -404.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2382050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -350.000000) translate(0,12)">05617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2382290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -404.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425e510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4121.000000 -350.000000) translate(0,12)">05717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3955.000000 -404.000000) translate(0,12)">058</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4247d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -350.000000) translate(0,12)">05817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4247fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -404.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d1f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3646.000000 -351.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d2130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3792.000000 -405.000000) translate(0,12)">059</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb7290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -351.000000) translate(0,12)">05917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb74d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -569.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abc7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3776.000000 -664.000000) translate(0,12)">06217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abc9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3447.000000 -490.000000) translate(0,12)">10kV汇流段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb8720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -709.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2359c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3642.000000 -219.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2657cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3660.000000 -151.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e45180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -983.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43738d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -1036.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e30b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3540.000000 -523.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f668f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3242.000000 -219.000000) translate(0,15)">4728</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_448c960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3242.000000 -198.000000) translate(0,15)">0878-6044082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3374b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4405.000000 -663.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a41cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3412.000000 -1046.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3623230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 4157.000000 -626.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f7f910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.055556 3819.000000 -629.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ab0b60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -316.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_435b0c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d0d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3feaa80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e3b460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4314.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aba8d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d6eaa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4764.000000 -316.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34fcf60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e71d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5064.000000 -316.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2654950" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4467.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cec080" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -897.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cec890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 -1001.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_GBL"/>
</svg>